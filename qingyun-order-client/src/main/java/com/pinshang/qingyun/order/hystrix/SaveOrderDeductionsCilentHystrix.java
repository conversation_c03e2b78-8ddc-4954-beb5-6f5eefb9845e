package com.pinshang.qingyun.order.hystrix;

import com.github.pagehelper.PageInfo;
import com.pinshang.qingyun.order.dto.SaveOrderDeductionsIDTO;
import com.pinshang.qingyun.order.dto.order.*;
import com.pinshang.qingyun.order.service.SaleReturnOrderClient;
import com.pinshang.qingyun.order.service.SaveOrderDeductionsClient;
import feign.hystrix.FallbackFactory;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * Created by crell on 2017/11/6.
 */
@Component
public class SaveOrderDeductionsCilentHystrix implements FallbackFactory<SaveOrderDeductionsClient> {


    @Override
    public SaveOrderDeductionsClient create(Throwable throwable) {
        return  new SaveOrderDeductionsClient() {

            @Override
            public Boolean saveOrderDeductions(SaveOrderDeductionsIDTO idto) {
                return null;
            }
        };
    }
}
 
package com.pinshang.qingyun.order.hystrix;

import com.github.pagehelper.PageInfo;
import com.pinshang.qingyun.order.dto.orderStatistics.OrderMirrorSyncODTO;
import com.pinshang.qingyun.order.dto.orderStatistics.OrderStatisticsMonitorIDTO;
import com.pinshang.qingyun.order.dto.orderStatistics.OrderStatisticsODTO;
import com.pinshang.qingyun.order.dto.orderStatistics.OrderSyncODTO;
import com.pinshang.qingyun.order.service.OrderStatisticsMonitorClient;
import feign.hystrix.FallbackFactory;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public class OrderStatisticsMonitorClientHystrix implements FallbackFactory<OrderStatisticsMonitorClient> {
    @Override
    public OrderStatisticsMonitorClient create(Throwable throwable) {
        return new OrderStatisticsMonitorClient() {


            @Override
            public OrderStatisticsODTO queryOrderStatisticsInfo(OrderStatisticsMonitorIDTO idto) {
                return null;
            }

            @Override
            public List<String> queryOrderDiffList(OrderStatisticsMonitorIDTO idto) {
                return null;
            }

            @Override
            public PageInfo<OrderSyncODTO> queryOrderSyncList(OrderStatisticsMonitorIDTO idto) {
                return null;
            }

            @Override
            public PageInfo<OrderMirrorSyncODTO> queryOrderMirrorSyncList(OrderStatisticsMonitorIDTO idto) {
                return null;
            }
        };
    }

}

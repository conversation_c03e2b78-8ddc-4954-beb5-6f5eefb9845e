package com.pinshang.qingyun.order.dto.order;

import com.pinshang.qingyun.order.dto.Pagination;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
public class SaleReturnReportIDTO extends Pagination {

    @ApiModelProperty("门店ID")
    private Long shopId;

    @ApiModelProperty("用户权限范围内的门店ID列表")
    private List<Long> shopIdList;

    @ApiModelProperty("申请退货日期--开始")
    private String startTime;

    @ApiModelProperty("申请退货日期--结束")
    private String endTime;

    @ApiModelProperty("退货确认日期--开始")
    private String updateStartTime;

    @ApiModelProperty("退货确认日期--结束")
    private String updateEndTime;

    @ApiModelProperty("退货单号")
    private String orderCode;

    @ApiModelProperty("商品ID")
    private Long commodityId;

    @ApiModelProperty("商品")
    private String commodityCode;

    @ApiModelProperty("条码")
    private String barCode;

    @ApiModelProperty("代销商id")
    private Long consignmentId;

}
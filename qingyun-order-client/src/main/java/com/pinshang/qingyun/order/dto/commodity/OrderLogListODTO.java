package com.pinshang.qingyun.order.dto.commodity;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import lombok.ToString;

@Data
@ToString
@AllArgsConstructor
@RequiredArgsConstructor
public class OrderLogListODTO {
	private String createTime;
	private String createName;
	private String operationType;
	private String entityType;
	private String code;
	private String name;
	private String newValue;
	private String oldValue;
}

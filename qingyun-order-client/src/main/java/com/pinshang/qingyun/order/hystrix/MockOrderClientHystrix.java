package com.pinshang.qingyun.order.hystrix;

import com.pinshang.qingyun.order.dto.mock.MockOrderIDTO;
import com.pinshang.qingyun.order.service.MockOrderClient;
import feign.hystrix.FallbackFactory;
import org.springframework.stereotype.Component;

@Component
public class MockOrderClientHystrix implements FallbackFactory<MockOrderClient> {
	@Override
	public MockOrderClient create(Throwable throwable) {
		return new MockOrderClient() {

			@Override
			public Long mockCreateOrder(MockOrderIDTO mockOrderIDTO) {
				return null;
			}
		};
	}
}

package com.pinshang.qingyun.order.dto.shop;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2017/6/16.
 */
@Data
public class ShopReceiveOrderItemODTO {
	private String itemId;

    private String commodityId;

    private String commodityCode;

    private String commodityName;

    private String commoditySpec;

    private String unit;

    private BigDecimal price;

    private Integer status;

    private BigDecimal recommandPrice;

    private BigDecimal quantity;

    private BigDecimal realDeliveryQuantity;

    private BigDecimal realReceiveQuantity;

    @ApiModelProperty("实收份数")
    private Integer realReceiveNumber;

    private BigDecimal totalPrice;

    private String rejectReason;

    private String remark;

    @ApiModelProperty("包装规格")
    private BigDecimal commodityPackageSpec;
}

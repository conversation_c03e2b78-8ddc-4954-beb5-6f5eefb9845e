package com.pinshang.qingyun.order.hystrix;

import com.pinshang.qingyun.order.service.XDShoppingCartClient;
import feign.hystrix.FallbackFactory;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

@Component
public class XDShoppingCartClientHystrix implements FallbackFactory<XDShoppingCartClient> {
	private Logger logger = LoggerFactory.getLogger(getClass());
	
	@Override
	public XDShoppingCartClient create(Throwable throwable) {
		return new XDShoppingCartClient() {

			@Override
			public void batchAddXdShoppingCartTemp() {

			}

			@Override
			public void batchAddXdShoppingCart() {

			}

		};
	}
}

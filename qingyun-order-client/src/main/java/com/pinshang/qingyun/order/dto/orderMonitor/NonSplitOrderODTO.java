package com.pinshang.qingyun.order.dto.orderMonitor;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * Created by weican on 2017-10-16.
 */
@Data
public class NonSplitOrderODTO {
    @ApiModelProperty("订单ID")
    private Long orderId;
    @ApiModelProperty("订单编码")
    private String orderCode;
    @ApiModelProperty("客户编码")
    private String storeCode;
    @ApiModelProperty("客户名称")
    private String storeName;
    @ApiModelProperty("送货日期")
    private Date orderTime;
    @ApiModelProperty("订单状态：0正常,1删除,2 取消")
    private Integer orderStatus;
    @ApiModelProperty("下单时间")
    private Date createTime;
    @ApiModelProperty("更新时间")
    private Date updateTime;
}

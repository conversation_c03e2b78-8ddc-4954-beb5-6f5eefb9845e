package com.pinshang.qingyun.order.dto.order;

import com.pinshang.qingyun.order.dto.Pagination;
import lombok.Data;

import java.util.List;

/**
 * Created by crell on 2017/11/6.
 */
@Data
public class SaleReturnIDTO extends Pagination{

    private String beginDate;

    private String endDate;

    private String orderCode;

    private List<Integer> status;

    private Integer logisticsModel;

    private Long enterpriseId;

    private Long storeId;

    private Long supplierId;

    private Long shopId;

    // 退货原因
    private Boolean ifReturnShort = false;

    /** 档口id */
    private Long stallId;
}

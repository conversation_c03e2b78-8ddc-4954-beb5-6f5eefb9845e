package com.pinshang.qingyun.order.dto.tob;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2024/04/15
 * @Version 1.0
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class EsXdaCommoditySoldOutIDTO {
    /**
     * 商品id
     */
    private List<Long> commodityIdList;

    /**
     * 1-查当日, 2-查0-7日
     */
    private Integer type;

    /**
     * 查询开始日期
     */
    private Date date;

    /**
     * 0=B端销售   10=通达销售
     */
    private Integer businessType;
}

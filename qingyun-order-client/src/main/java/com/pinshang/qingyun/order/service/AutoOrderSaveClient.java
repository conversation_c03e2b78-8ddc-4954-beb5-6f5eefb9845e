package com.pinshang.qingyun.order.service;

import com.pinshang.qingyun.base.configure.FeignClientConfiguration;
import com.pinshang.qingyun.base.constant.ApplicationNameConstant;
import com.pinshang.qingyun.order.dto.auto.AutoSaveOrderIDTO;
import com.pinshang.qingyun.order.hystrix.AutoOrderSaveClientHystrix;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

@FeignClient(value = ApplicationNameConstant.QINGYUN_ORDER_SERVICE, fallbackFactory = AutoOrderSaveClientHystrix.class, configuration = FeignClientConfiguration.class)
public interface AutoOrderSaveClient {

    /**
     * 自动订货保存订单
     * @param saveOrderIDTO
     * @return
     */
    @PostMapping("/auto/saveAutoOrder")
    Boolean saveAutoOrder(@RequestBody AutoSaveOrderIDTO saveOrderIDTO);
}

package com.pinshang.qingyun.order.dto.order;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import lombok.ToString;

import java.util.List;

/**
 * summary.
 * <p>
 * detailed description
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2019-02-15
 */
@Data
@ToString
@RequiredArgsConstructor
@AllArgsConstructor
public class CreateOrderODTO {
    // 创建订单是否成功
    private Boolean createOrderResult;
    // 门店订货-过滤提示
    private List<FilterTipODTO> filterTips;

    private List<BStockShortResponseODTO> shortStockList;
}

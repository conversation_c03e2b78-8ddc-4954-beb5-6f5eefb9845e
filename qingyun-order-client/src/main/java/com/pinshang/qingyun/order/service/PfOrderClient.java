package com.pinshang.qingyun.order.service;

import org.springframework.cloud.openfeign.FeignClient;

import com.pinshang.qingyun.base.configure.FeignClientConfiguration;
import com.pinshang.qingyun.base.constant.ApplicationNameConstant;
import com.pinshang.qingyun.order.hystrix.PfOrderClientHystrix;

@FeignClient(value = ApplicationNameConstant.QINGYUN_ORDER_SERVICE, fallbackFactory = PfOrderClientHystrix.class, configuration = FeignClientConfiguration.class)
public interface PfOrderClient {

	
	
}

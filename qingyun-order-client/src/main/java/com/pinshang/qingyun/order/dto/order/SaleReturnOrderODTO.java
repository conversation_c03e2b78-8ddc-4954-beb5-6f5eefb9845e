package com.pinshang.qingyun.order.dto.order;

import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
public class SaleReturnOrderODTO {
    /**客户ID*/
    private Long storeId;

    /**
     * 档口ID
     */
    private Long stallId;

    /**退货单号*/
    private String orderCode;

    /**0＝取消，1＝待入库(待确认)，2＝已入库（已确认）*/
    private Integer status;

    /**物流配送模式0=直送，1＝配送，2＝直通*/
    private Integer logisticsModel;

    /**供应商ID*/
    private Long supplierId;

    /**仓库ID*/
    private Long warehouseId;

    /**备注*/
    private String remark;

    private List<SaleReturnOrderItemODTO> itemList;
    
    
    
    
    protected Long id;
    private Long enterpriseId;
    private Date createTime;
    private Long createId;
    private Date updateTime;
    private Long updateId;
    private Long consignmentId; // 代销商户id
}
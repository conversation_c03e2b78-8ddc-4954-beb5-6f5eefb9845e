package com.pinshang.qingyun.order.dto.shop;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 *
 * <AUTHOR>
 * @Date 2018/4/8 13:58
 */
@Data
public class OutstandingShopODTO implements Serializable {

    /**
     * 时间段
     */
    @ApiModelProperty("时间段")
    private String endTime;
    /**
     * 门店code
     */
    @ApiModelProperty("门店code")
    private String storeCode;
    /**
     * 门店名称
     */
    @ApiModelProperty("门店名称")
    private String storeName;
    /**
     * 联系人
     */
    @ApiModelProperty("联系人")
    private String storeLinkman;
    /**
     * 联系人电话
     */
    @ApiModelProperty("联系人电话")
    private String linkmanTel;
    /**
     * 未订货备注
     */
    @ApiModelProperty("未订货备注")
    private String notOrderRemark;
}
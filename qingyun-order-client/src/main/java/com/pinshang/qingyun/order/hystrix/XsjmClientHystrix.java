package com.pinshang.qingyun.order.hystrix;

import com.pinshang.qingyun.order.service.XsjmClient;
import feign.hystrix.FallbackFactory;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

@Component
public class XsjmClientHystrix implements FallbackFactory<XsjmClient> {
	private Logger logger = LoggerFactory.getLogger(getClass());
	
	@Override
	public XsjmClient create(Throwable throwable) {
		return new XsjmClient() {
			@Override
			public void autoSettleDaily() {

			}
		};
	}
}

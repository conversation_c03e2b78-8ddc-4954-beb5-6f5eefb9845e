package com.pinshang.qingyun.order.dto.store;

/**
 * <AUTHOR>
 * @Date 2018/8/8 11:28
 */

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
@Data
@ApiModel
public class DistributionSortStoreODTO {
    @ApiModelProperty("门店id,新增时回传用")
    private String storeId;
    @ApiModelProperty("客户编号")
    private String storeCode;
    @ApiModelProperty("客户名称")
    private String storeName;
    @ApiModelProperty("客户类型")
    private String customerType;
}

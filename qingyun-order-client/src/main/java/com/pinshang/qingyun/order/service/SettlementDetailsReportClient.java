package com.pinshang.qingyun.order.service;

import com.github.pagehelper.PageInfo;
import com.pinshang.qingyun.base.configure.FeignClientConfiguration;
import com.pinshang.qingyun.base.constant.ApplicationNameConstant;
import com.pinshang.qingyun.order.dto.shop.SettlementDetailsReportIDTO;
import com.pinshang.qingyun.order.dto.shop.SettlementDetailsReportODTO;
import com.pinshang.qingyun.order.hystrix.SettlementDetailsReportClientHystrix;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.util.List;

/**
 * Created by hhf on 2019/1/16.
 * 结算明细报表
 */
@FeignClient(value = ApplicationNameConstant.QINGYUN_ORDER_SERVICE, fallbackFactory = SettlementDetailsReportClientHystrix.class, configuration = FeignClientConfiguration.class)
public interface SettlementDetailsReportClient {

    /**
     * 结算明细报表
     * @param dto
     * @return
     */
    @RequestMapping(value = "/settlementDetailsReport/settlementDetailsReport", method = RequestMethod.POST)
    PageInfo<SettlementDetailsReportODTO> settlementDetailsReport(@RequestBody SettlementDetailsReportIDTO dto);

    /**
     * 查询结算总金额
     * @param dto
     * @return
     */
    @RequestMapping(value = "/settlementDetailsReport/findTotalSettlePrice", method = RequestMethod.POST)
    BigDecimal findTotalSettlePrice(@RequestBody SettlementDetailsReportIDTO dto);

    /**
     * 查询符合条件的客户类型
     * @return
     */
    @GetMapping("/settlementDetailsReport/getStoreTypeIdList")
    List<String> getStoreTypeIdList();
}

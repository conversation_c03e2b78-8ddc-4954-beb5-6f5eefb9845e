package com.pinshang.qingyun.order.hystrix;

import com.pinshang.qingyun.order.dto.DeliveryTimeLineIDTO;
import com.pinshang.qingyun.order.dto.DeliveryTimeODTO;
import com.pinshang.qingyun.order.service.DeliveryTimeClient;
import feign.hystrix.FallbackFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
@Slf4j
public class DeliveryTimeClientHystrix implements FallbackFactory<DeliveryTimeClient> {
    @Override
    public DeliveryTimeClient create(Throwable throwable) {
        return new DeliveryTimeClient() {

            @Override
            public List<DeliveryTimeODTO> deliveryTimeList() {
                return null;
            }

            @Override
            public String selectEndTimeById(Long deliveryTimeId) {
                return null;
            }

            @Override
            public String selectDeliveryTimeEndTimeByLineIds(DeliveryTimeLineIDTO idto) {
                return null;
            }
        };
    }
}

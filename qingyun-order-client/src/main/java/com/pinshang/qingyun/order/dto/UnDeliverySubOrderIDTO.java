package com.pinshang.qingyun.order.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class UnDeliverySubOrderIDTO implements Serializable {
	private static final long serialVersionUID = 444440950253758358L;
	//仓库
	private Long warehouseId;
	//送货日期
	private String orderTime;
	//鲜达客户类型
	private Long xdaStoreTypeId;
	private List<Long> directStoreTypeList;
	private List<Long> tobStoreTypeList;
	private List<Long> tobWarehouseList;

}

package com.pinshang.qingyun.order.dto.shop;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2017/6/7.
 */
@Data
public class ShopReceiveODTO {

    private String id;

    private Integer logisticsModel;

    private String subOrderId;

    private String subOrderCode;

    private BigDecimal totalPrice;

    private BigDecimal subOrderTotalPrice;

    private String supplierId;

    private String supplierName;

    private String status;

    private Date orderTime;

    private Date receiveTime;

    private BigDecimal orderAmount;

    private String receiveId;

    private String realName;
    
    private Date createTime;

    //门店类型
    private Integer shopType;

    //下单人
    private String createName;

    private String stallName;
}

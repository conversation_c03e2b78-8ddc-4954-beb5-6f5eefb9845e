package com.pinshang.qingyun.order.service;

import com.pinshang.qingyun.base.configure.FeignClientConfiguration;
import com.pinshang.qingyun.base.constant.ApplicationNameConstant;
import com.pinshang.qingyun.order.dto.AutoShopCommodityODTO;
import com.pinshang.qingyun.order.hystrix.AppUserClientHystrix;
import com.pinshang.qingyun.order.hystrix.ShopAutoOrderClientHystrix;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import java.util.List;

@FeignClient(value = ApplicationNameConstant.QINGYUN_ORDER_SERVICE, fallbackFactory = ShopAutoOrderClientHystrix.class, configuration = FeignClientConfiguration.class)
public interface ShopAutoOrderClient {

    @RequestMapping(value = "/orderJob/queryAutoShopList",method = RequestMethod.POST)
    List<AutoShopCommodityODTO> queryAutoShopList();

}

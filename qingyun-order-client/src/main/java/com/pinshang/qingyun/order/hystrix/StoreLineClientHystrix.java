package com.pinshang.qingyun.order.hystrix;

import com.pinshang.qingyun.order.dto.DistributionLineODTO;
import com.pinshang.qingyun.order.service.StoreLineClient;
import feign.hystrix.FallbackFactory;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2018/10/12 9:53.
 * @blog http://linuxsogood.org
 */
@Service
public class StoreLineClientHystrix implements FallbackFactory<StoreLineClient> {
    @Override
    public StoreLineClient create(Throwable cause) {
        return new StoreLineClient() {
            @Override
            public Map<Long, DistributionLineODTO> queryStoreLineByStoreIds(List<Long> storeIds) {
                return null;
            }
        };
    }
}

package com.pinshang.qingyun.order.dto.order;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * Created by crell on 2017/11/6.
 */
@Data
public class SaleReturnItemAddIDTO {
    private Long id;

    @ApiModelProperty("商品id")
    private String commodityId;

    private String commodityCode;
    private String commodityName;

    @ApiModelProperty("退货少货数量")
    private BigDecimal returnQuantity;

    @ApiModelProperty("单价")
    private BigDecimal price;

    @ApiModelProperty("金额")
    private BigDecimal totalPrice;

    private BigDecimal realReturnQuantity;

    @ApiModelProperty("退货原因")
    private Integer returnReason;

    @ApiModelProperty("备注")
    private String remark;

    //商品所属物流模式、供应商、仓库
    private Integer logisticsModel;

    private Long supplierId;

    private Long warehouseId;

    private BigDecimal commodityPackageSpec;

    @ApiModelProperty("图片list")
    private List<SaleReturnItemAddPicIDTO> picList;
    @ApiModelProperty("视频list")
    private List<SaleReturnItemAddPicIDTO> videoList;
}

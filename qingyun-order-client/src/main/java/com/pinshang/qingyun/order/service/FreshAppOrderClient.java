package com.pinshang.qingyun.order.service;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import com.pinshang.qingyun.base.configure.FeignClientConfiguration;
import com.pinshang.qingyun.base.constant.ApplicationNameConstant;
import com.pinshang.qingyun.order.hystrix.FreshAppOrderClientHystrix;

/**
 * 清美生鲜订单
 */
@FeignClient(value = ApplicationNameConstant.QINGYUN_ORDER_SERVICE, fallbackFactory = FreshAppOrderClientHystrix.class, configuration = FeignClientConfiguration.class)
public interface FreshAppOrderClient {

	/**
	 * 补偿生鲜订单消息Job
	 */
	@RequestMapping(value = "/app/order/client/compensateAppOrderMsgJob", method = RequestMethod.POST)
	public String compensateAppOrderMsgJob();

}

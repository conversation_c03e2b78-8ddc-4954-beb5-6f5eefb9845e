package com.pinshang.qingyun.order.dto.shop;

import com.pinshang.qingyun.order.dto.Pagination;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 *
 * <AUTHOR>
 * @Date 2018/4/9 17:59
 */
@Data
public class StoreAccountIDTO extends Pagination {

    private static final long serialVersionUID = -2001027524564547744L;
    /**
     * 模糊查询code
     */
    @ApiModelProperty("模糊查询code 店铺编号/店铺名称/店铺助记码")
    private String storeCode;

    /**  雇员表ID**/
    @ApiModelProperty("雇员表ID,接口不用赋值")
    private String employeeId;
}

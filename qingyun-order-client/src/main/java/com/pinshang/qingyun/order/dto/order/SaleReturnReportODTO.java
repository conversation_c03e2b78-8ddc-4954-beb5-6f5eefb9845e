package com.pinshang.qingyun.order.dto.order;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Date;

@Data
public class SaleReturnReportODTO {
    private Long id; //退货明细表自增ID
    private Long enterpriseId;
    private String storeCode;
    private String storeName;
    private String orderCode;
    private Date createTime;
    private Date updateTime;
    private Long commodityId;
    private String commodityCode;
    private String barCode;
    private String commodityName;
    private String commoditySpec;
    private BigDecimal returnQuantity;
    private BigDecimal realReturnQuantity;
    private BigDecimal price;
    private BigDecimal totalPrice;
    private String barCodes;	// 子码列表

    private String shopName;
    private String returnReasonName;
    @ApiModelProperty("责任方类型(Responsible party) 1=配送，2=大仓，3=门店")
    private Integer rpType;

    @ApiModelProperty("实退金额")
    private BigDecimal realTotalPrice;

    public BigDecimal getRealTotalPrice() {
        if(this.realReturnQuantity==null || this.price==null){
            return null;
        }
        return this.realReturnQuantity.multiply(this.price).setScale(2, RoundingMode.HALF_UP).stripTrailingZeros();
    }

}

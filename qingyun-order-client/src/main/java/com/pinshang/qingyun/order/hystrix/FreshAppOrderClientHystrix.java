package com.pinshang.qingyun.order.hystrix;

import org.springframework.stereotype.Component;

import com.pinshang.qingyun.order.service.FreshAppOrderClient;

import feign.hystrix.FallbackFactory;

/**
 * 清美生鲜订单
 */
@Component
public class FreshAppOrderClientHystrix implements FallbackFactory<FreshAppOrderClient> {

	@Override
	public FreshAppOrderClient create(Throwable throwable) {
		return new FreshAppOrderClient() {

			@Override
			public String compensateAppOrderMsgJob() {
				return null;
			}

		};

	}

}

package com.pinshang.qingyun.order.dto.order;

import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

@Data
public class OrderReplenishmentODTO {

    private String deliveryDate;//送货日期

    private String shopId;

    private String shopCode;//门店编码

    private String shopName;//门店名称

    private String storeId;

    private BigDecimal totalAmount;//金额合计

    private List<OrderReplenishmentCommodityODTO> commodityList;//补货明细

}

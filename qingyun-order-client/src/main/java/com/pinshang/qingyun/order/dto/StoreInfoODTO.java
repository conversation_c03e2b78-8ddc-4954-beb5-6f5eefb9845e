package com.pinshang.qingyun.order.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 *  查询待收货/收货中的客户
 */
@Data
@ApiModel
public class StoreInfoODTO {
    @ApiModelProperty("客户ID / 车间ID")
    private String storeId;
    @ApiModelProperty("客户名称 / 车间名称")
    private String storeName;
    @ApiModelProperty("业务类型: 1.门店退货 2.领料退货")
    private Integer bizType;
}

package com.pinshang.qingyun.order.service;

import com.github.pagehelper.PageInfo;
import com.pinshang.qingyun.base.configure.FeignClientConfiguration;
import com.pinshang.qingyun.base.constant.ApplicationNameConstant;
import com.pinshang.qingyun.order.dto.shop.ReturnOrderDetailsReportIDto;
import com.pinshang.qingyun.order.dto.shop.ReturnOrderDetailsReportODto;
import com.pinshang.qingyun.order.hystrix.OrderReportClientHystrix;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

@FeignClient(value = ApplicationNameConstant.QINGYUN_ORDER_SERVICE, fallbackFactory = OrderReportClientHystrix.class, configuration = FeignClientConfiguration.class)
public interface OrderReportClient {


    @RequestMapping(value = "/report/findReturnOrderDetailsReport", method = RequestMethod.POST)
    PageInfo<ReturnOrderDetailsReportODto> findReturnOrderDetailsReport(ReturnOrderDetailsReportIDto returnOrderDetailsReportIDto);


}

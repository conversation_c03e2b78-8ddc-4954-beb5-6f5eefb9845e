package com.pinshang.qingyun.order.dto.order;

import com.pinshang.qingyun.order.dto.Pagination;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Author: sk
 * @Date: 2020/3/17
 */
@Data
public class XdReceiveDocIDTO extends Pagination {

    @ApiModelProperty("单据id")
    private Long docId;

    @ApiModelProperty("商品编码、条形码、名称")
    private String commodityKey;

    @ApiModelProperty("货位id")
    private Long shelfId;



    private String deliveryTime;
    private Long commodityId;
    private Long shopId;
}

package com.pinshang.qingyun.order.dto.purchase;


public class ConnectionPurchaseODto {
	private Long supplierId;
	private Integer preOrderNum;
	private Integer purchaseOrderNum;
	private String supplierName;
	private Long purchaserId;
	private String purchaserName;
	private String supplyStartTime;
	private String supplyEndTime;
	private String orderDate;
	
	public Long getSupplierId() {
		return supplierId;
	}
	public void setSupplierId(Long supplierId) {
		this.supplierId = supplierId;
	}
	public Integer getPreOrderNum() {
		return preOrderNum;
	}
	public void setPreOrderNum(Integer preOrderNum) {
		this.preOrderNum = preOrderNum;
	}
	public Integer getPurchaseOrderNum() {
		return purchaseOrderNum;
	}
	public void setPurchaseOrderNum(Integer purchaseOrderNum) {
		this.purchaseOrderNum = purchaseOrderNum;
	}
	public String getSupplierName() {
		return supplierName;
	}
	public void setSupplierName(String supplierName) {
		this.supplierName = supplierName;
	}
	public Long getPurchaserId() {
		return purchaserId;
	}
	public void setPurchaserId(Long purchaserId) {
		this.purchaserId = purchaserId;
	}
	public String getPurchaserName() {
		return purchaserName;
	}
	public void setPurchaserName(String purchaserName) {
		this.purchaserName = purchaserName;
	}
	public String getSupplyStartTime() {
		return supplyStartTime;
	}
	public void setSupplyStartTime(String supplyStartTime) {
		this.supplyStartTime = supplyStartTime;
	}
	public String getSupplyEndTime() {
		return supplyEndTime;
	}
	public void setSupplyEndTime(String supplyEndTime) {
		this.supplyEndTime = supplyEndTime;
	}
	public String getOrderDate() {
		return orderDate;
	}
	public void setOrderDate(String orderDate) {
		this.orderDate = orderDate;
	}
}

package com.pinshang.qingyun.order.service;

import com.github.pagehelper.PageInfo;
import com.pinshang.qingyun.base.configure.FeignClientConfiguration;
import com.pinshang.qingyun.base.constant.ApplicationNameConstant;
import com.pinshang.qingyun.order.dto.deliveryBill.DeliveryBillODTO;
import com.pinshang.qingyun.order.dto.deliveryBill.DeliveryBillSearchIDTO;
import com.pinshang.qingyun.order.hystrix.DeliveryBillClientHystrix;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * @description:
 * @author: hhf
 * @time: 2021/8/4 15:14
 */
@FeignClient(value = ApplicationNameConstant.QINGYUN_ORDER_SERVICE, fallbackFactory = DeliveryBillClientHystrix.class, configuration = FeignClientConfiguration.class)
public interface DeliveryBillClient {

    /**
     * 送货单列表查询
     * @param idto
     * @return
     */
    @RequestMapping(value = "/deliveryBill/findDeliveryBillPageInfoByParams",method = RequestMethod.POST)
    PageInfo<DeliveryBillODTO> findDeliveryBillPageInfoByParams(@RequestBody DeliveryBillSearchIDTO idto);

    @ApiOperation(value = "导出", notes = "导出")
    @RequestMapping(value = "/deliveryBill/exportBill", method = RequestMethod.POST)
    String exportBill(@RequestParam("id") Long id);



    @ApiOperation(value = "打印", notes = "打印")
    @RequestMapping(value = "/deliveryBill/printerBill", method = RequestMethod.POST)
    String printerBill(@RequestParam("id") Long id,@RequestParam("userId") Long userId);

}

package com.pinshang.qingyun.order.hystrix;

import com.pinshang.qingyun.order.dto.DictionaryQueryDCTpyeODTO;
import com.pinshang.qingyun.order.service.OrderDictionaryClient;
import feign.hystrix.FallbackFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * @auther dy
 * @date 2023/12/21  9:55
 **/
@Component
@Slf4j
public class OrderDictionaryClientHystrix implements FallbackFactory<OrderDictionaryClient> {
    @Override
    public OrderDictionaryClient create(Throwable throwable) {
        return new OrderDictionaryClient() {

            @Override
            public DictionaryQueryDCTpyeODTO queryDirectDcConfig() {
                return null;
            }
        };
    }
}

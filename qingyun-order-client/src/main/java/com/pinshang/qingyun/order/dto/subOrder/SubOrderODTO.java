package com.pinshang.qingyun.order.dto.subOrder;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * @auther dy
 * @date 2023/12/19  14:34
 **/
@Data
public class SubOrderODTO {
    private Long id;
    //订单id
    private Long orderId;
    /*物流模式 0-直送, 1-配送, 2-直通*/
    private Integer logisticsModel;
    //品种合计
    private Integer varietyTotal;
    //供应商id
    private Long supplierId;
    //仓库id
    private Long warehouseId;
    //0:未生成do单, 1:已生成do单， 2已取消;
    private Integer status;
    //下单时间
    private Date orderTime;
    //采购单id(直送自动生成采购单)
    private Long purchaseOrderId;
    //子单编号
    private String subOrderCode;
    //总价
    private BigDecimal totalPrice;
    // 已回填实发数量标识
    private Boolean writebackRealQtyFlag;
    private Long enterpriseId;
    private Date createTime;
    private Long createId;
    private Date updateTime;
    private Long updateId;

    /**
     * 是否预售:0=否，1=是
     */
    private Integer presaleStatus;

    /**
     * 库存依据  1=依据大仓, 2=不限量订货, 3=限量供应
     */
    private Integer stockType;

}

package com.pinshang.qingyun.order.service;

import com.pinshang.qingyun.base.configure.FeignClientConfiguration;
import com.pinshang.qingyun.base.constant.ApplicationNameConstant;
import com.pinshang.qingyun.order.dto.DeliveryTimeLineIDTO;
import com.pinshang.qingyun.order.dto.DeliveryTimeODTO;
import com.pinshang.qingyun.order.hystrix.DeliveryTimeClientHystrix;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 发货时间
 */
@FeignClient(value = ApplicationNameConstant.QINGYUN_ORDER_SERVICE, fallbackFactory = DeliveryTimeClientHystrix.class, configuration = FeignClientConfiguration.class)
public interface DeliveryTimeClient {
    @RequestMapping(value = "/deliveryTime/list",method = RequestMethod.GET)
    List<DeliveryTimeODTO> deliveryTimeList();


    /***
     * 根据线路组id 查询线路
     * @param deliveryTimeId
     * @return
     */
    @GetMapping(value = "/deliveryTime/selectEndTimeById")
    String selectEndTimeById(@RequestParam("deliveryTimeId") Long deliveryTimeId);

    /***
     * 根据线路id集合获取线路截单时间
     * @param idto
     * @return
     */
    @PostMapping(value = "/deliveryTime/selectEndTimeByLineIds")
    String selectDeliveryTimeEndTimeByLineIds(@RequestBody DeliveryTimeLineIDTO idto);

}

package com.pinshang.qingyun.order.dto.commodity;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/*
 * subOrder及item新增vo
 */
public class SubOrderAndItemIDTO {
	//订单id
	private Long orderId;
	//订单code
	private String orderCode;
	//物流模式
	private Integer logisticsModel;
	//商品品类数
	private BigDecimal varietyTotal;
	//供应商id
	private Long supplierId;
	//仓库id
	private Long warehouseId;
	//createId
	private Long createId;
	//enterpriseId
	private Long enterpriseId;
	//total_price
	private BigDecimal totalPrice;
	//orderTime
	private Date orderTime;
	//采购单id(直通自动生成采购单)
	private Long purchaseOrderId;
	// 店铺id
	private Long storeId;
	
	private List<SubOrderItemVo> items;
	public Long getOrderId() {
		return orderId;
	}
	public void setOrderId(Long orderId) {
		this.orderId = orderId;
	}
	public Integer getLogisticsModel() {
		return logisticsModel;
	}
	public void setLogisticsModel(Integer logisticsModel) {
		this.logisticsModel = logisticsModel;
	}
	public BigDecimal getVarietyTotal() {
		return varietyTotal;
	}
	public void setVarietyTotal(BigDecimal varietyTotal) {
		this.varietyTotal = varietyTotal;
	}
	public Long getSupplierId() {
		return supplierId;
	}
	public void setSupplierId(Long supplierId) {
		this.supplierId = supplierId;
	}
	public Long getWarehouseId() {
		return warehouseId;
	}
	public void setWarehouseId(Long warehouseId) {
		this.warehouseId = warehouseId;
	}
	public Long getCreateId() {
		return createId;
	}
	public void setCreateId(Long createId) {
		this.createId = createId;
	}
	public Long getEnterpriseId() {
		return enterpriseId;
	}
	public void setEnterpriseId(Long enterpriseId) {
		this.enterpriseId = enterpriseId;
	}
	public BigDecimal getTotalPrice() {
		return totalPrice;
	}
	public void setTotalPrice(BigDecimal totalPrice) {
		this.totalPrice = totalPrice;
	}
	public Date getOrderTime() {
		return orderTime;
	}
	public void setOrderTime(Date orderTime) {
		this.orderTime = orderTime;
	}
	public Long getPurchaseOrderId() {
		return purchaseOrderId;
	}
	public void setPurchaseOrderId(Long purchaseOrderId) {
		this.purchaseOrderId = purchaseOrderId;
	}

	public Long getStoreId() {
		return storeId;
	}

	public void setStoreId(Long storeId) {
		this.storeId = storeId;
	}

	public static class SubOrderItemVo{
		private Long subOrderId;
		private Long commodityId;
		private BigDecimal quantity;
		private BigDecimal price;
		private BigDecimal totalPrice;
		private BigDecimal realReceiveQuantity;
		private BigDecimal realDeliveryQuantity;
		private String rejectReason;
		//是否可变价——特价不可变价，其余从 订货设置 取
		private Integer changePriceStatus;
		public Long getSubOrderId() {
			return subOrderId;
		}
		public void setSubOrderId(Long subOrderId) {
			this.subOrderId = subOrderId;
		}
		public Long getCommodityId() {
			return commodityId;
		}
		public void setCommodityId(Long commodityId) {
			this.commodityId = commodityId;
		}
		public BigDecimal getQuantity() {
			return quantity;
		}
		public void setQuantity(BigDecimal quantity) {
			this.quantity = quantity;
		}
		public BigDecimal getPrice() {
			return price;
		}
		public void setPrice(BigDecimal price) {
			this.price = price;
		}
		public BigDecimal getTotalPrice() {
			return totalPrice;
		}
		public void setTotalPrice(BigDecimal totalPrice) {
			this.totalPrice = totalPrice;
		}
		public BigDecimal getRealReceiveQuantity() {
			return realReceiveQuantity;
		}
		public void setRealReceiveQuantity(BigDecimal realReceiveQuantity) {
			this.realReceiveQuantity = realReceiveQuantity;
		}
		public BigDecimal getRealDeliveryQuantity() {
			return realDeliveryQuantity;
		}
		public void setRealDeliveryQuantity(BigDecimal realDeliveryQuantity) {
			this.realDeliveryQuantity = realDeliveryQuantity;
		}
		public String getRejectReason() {
			return rejectReason;
		}
		public void setRejectReason(String rejectReason) {
			this.rejectReason = rejectReason;
		}

		public Integer getChangePriceStatus() {
			return changePriceStatus;
		}

		public void setChangePriceStatus(Integer changePriceStatus) {
			this.changePriceStatus = changePriceStatus;
		}
	}

	public List<SubOrderItemVo> getItems() {
		return items;
	}
	public void setItems(List<SubOrderItemVo> items) {
		this.items = items;
	}
	public String getOrderCode() {
		return orderCode;
	}
	public void setOrderCode(String orderCode) {
		this.orderCode = orderCode;
	}
}

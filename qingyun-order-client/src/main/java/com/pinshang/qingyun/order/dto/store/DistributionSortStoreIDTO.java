package com.pinshang.qingyun.order.dto.store;

import com.pinshang.qingyun.order.dto.Pagination;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2018/8/8 11:24
 */
@Data
@ApiModel
public class DistributionSortStoreIDTO extends Pagination {
    @ApiModelProperty("模糊查询参数:客户编号,客户名称,助记码")
    private String vagueStr;
    @ApiModelProperty("客户类型(下拉框option)")
    private Long storeTypeId;
    @ApiModelProperty(hidden = true)
    /**
     * 查询时排除已存在配送排序表中的storeIds
     */
    private List<Long> storeIds;
}

package com.pinshang.qingyun.order.service;

import com.github.pagehelper.PageInfo;
import com.pinshang.qingyun.base.configure.FeignClientConfiguration;
import com.pinshang.qingyun.base.constant.ApplicationNameConstant;
import com.pinshang.qingyun.order.dto.order.*;
import com.pinshang.qingyun.order.hystrix.SaleReturnOrderCilentHystrix;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * Created by crell on 2017/11/6.
 */
@FeignClient(name = ApplicationNameConstant.QINGYUN_ORDER_SERVICE, fallbackFactory = SaleReturnOrderCilentHystrix.class, configuration = FeignClientConfiguration.class)
@Service
public interface SaleReturnOrderClient {

    @RequestMapping(value = "/order/saleReturn/list", method = RequestMethod.POST)
    PageInfo<SaleReturnODTO> getSaleReturnListByCondition(@RequestBody SaleReturnIDTO saleReturnIDTO);

    @RequestMapping(value = "/order/saleReturn/show/{orderCode}",method = RequestMethod.GET)
    SaleReturnInfoODTO showSaleReturn(@PathVariable("orderCode") String orderCode);

    @RequestMapping(value =  "/order/saleReturn/add",method = RequestMethod.POST )
    String addSaleReturn(@RequestBody SaleReturnAddIDTO saleReturnAddIDTO);

    @RequestMapping(value = "/order/saleReturn/cancel/{orderCode}/{userId}",method = RequestMethod.POST)
    public Integer cancelSaleReturn(@PathVariable("orderCode") String orderCode,@PathVariable("userId") Long userId);

    @RequestMapping(value = "/order/saleReturn/cancelWithReason",method = RequestMethod.POST)
    Integer cancelSaleReturnWithReason(@RequestBody SaleReturnCancelIDTO idto);

    @RequestMapping(value = "/order/saleReturn/copy/{orderCode}",method = RequestMethod.GET)
    List<SaleReturnItemODTO> copySaleReturn(@PathVariable("orderCode") String orderCode);

    @RequestMapping(value =  "/order/saleReturn/findCommodityByCode",method = RequestMethod.POST )
    List<SaleReturnItemODTO> findCommodityByCode(@RequestBody SaleReturnCommodityIDTO saleReturnCommodityIDTO);

    @RequestMapping(value =  "/order/saleReturn/findCommodityByCommodityCode",method = RequestMethod.POST )
    List<SaleReturnItemODTO> findCommodityByCommodityCode(@RequestBody SaleReturnCommodityIDTO saleReturnCommodityIDTO);

    @RequestMapping(value = "/order/saleReturn/sending/list", method = RequestMethod.POST)
    PageInfo<SaleReturnODTO> getSaleReturnSendList(@RequestBody SaleReturnIDTO saleReturnIDTO);

    @RequestMapping(value =  "/order/saleReturn/confirm",method = RequestMethod.POST)
    String confirmSaleReturn(@RequestBody SaleReturnAddIDTO saleReturnAddIDTO);

    @RequestMapping(value =  "/order/saleReturn/pdaReturnByBarcode",method = RequestMethod.GET )
    PdaReturnItemODTO pdaReturnByBarcode(@RequestParam(value = "shopId")Long shopId, @RequestParam(value = "barCode")String barCode, @RequestParam(value = "stallId",required = false) Long stallId);
    @RequestMapping(value =  "/order/saleReturn/pdaShortByBarcode",method = RequestMethod.GET )
    PdaReturnItemODTO pdaShortByBarcode(@RequestParam(value = "shopId")Long shopId, @RequestParam(value = "barCode")String barCode, @RequestParam(value = "stallId",required = false) Long stallId);

    @RequestMapping(value =  "/order/saleReturn/pdaReturnAdd",method = RequestMethod.POST )
    Boolean pdaReturnAdd(@RequestBody SaleReturnAddIDTO saleReturnAddIDTO);
    @RequestMapping(value =  "/order/saleReturn/pdaShortAdd",method = RequestMethod.POST )
    Boolean pdaShortAdd(@RequestBody SaleReturnAddIDTO saleReturnAddIDTO);
}

package com.pinshang.qingyun.order.dto.shop;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@Data
public class ReturnOrderDetailsReportODto {
	
	private Date createTime;
	private String orderCode;
	private Long commodityFirstId;
	private String cateName;
	private String barCode;
	private String barCodes;
	private String shopName;
	private String commodityCode;
	private String commodityName;
	private String commoditySpec;
	private BigDecimal price;
	private BigDecimal returnQuantity;
	private BigDecimal totalPrice;
	private BigDecimal realReturnQuantity;

	//退货原因名称
	private String returnReasonName;
	//备注
	private String remark;
	//单位
	private String commodityUnit;
	private String stallName;
}

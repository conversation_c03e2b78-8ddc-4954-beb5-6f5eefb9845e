package com.pinshang.qingyun.order.dto.shop;

import com.pinshang.qingyun.order.dto.Pagination;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2017/6/7.
 */
@Data
public class ShopReceiveHQIDTO extends Pagination{

    @ApiModelProperty(position = 1,value = "门店id")
    private String shopId;

    @ApiModelProperty(position = 2,value = "开始时间")
    private String beginDate;

    @ApiModelProperty(position = 3,value = "结束时间")
    private String endDate;
    @ApiModelProperty(position = 4,value = "预订单号")
    private String subOrderCode;

    private String supplierStr;

    private Integer logisticsModel;

    @ApiModelProperty(position = 5,value = "收货状态")
    private List<Integer> status;

    private String enterpriseId;

    private String storeId;

    @ApiModelProperty(position = 6,value = "供应商id")
    private String supplierId;

    @ApiModelProperty(position = 7,value = "是否补货1:补货")
    private Integer isReplenishment;//是否补货

    @ApiModelProperty(position = 8,value = "创建人编码/名称")
    private String createUser;

    @ApiModelProperty(position = 9,value = "订单号")
    private String orderCode;
}

package com.pinshang.qingyun.order.hystrix;

import com.pinshang.qingyun.order.dto.store.AppUserInfoIDTO;
import com.pinshang.qingyun.order.service.AppUserClient;
import feign.hystrix.FallbackFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @Date 2018/10/10 10:54
 */
@Component
@Slf4j
public class AppUserClientHystrix implements FallbackFactory<AppUserClient> {
    @Override
    public AppUserClient create(Throwable throwable) {
        return new AppUserClient() {
            @Override
            public Boolean openAccount(AppUserInfoIDTO info) {
                return null;
            }

            @Override
            public Boolean editPassword(AppUserInfoIDTO info) {
                return null;
            }
        };
    }
}

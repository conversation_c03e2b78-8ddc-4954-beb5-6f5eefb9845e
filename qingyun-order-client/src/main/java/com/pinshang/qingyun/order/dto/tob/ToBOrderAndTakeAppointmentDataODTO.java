package com.pinshang.qingyun.order.dto.tob;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;


@Data
@ApiModel(value = "ToBOrderAndTakeAppointmentDataODTO", description = "查询B端订单数据和提货卡数据返回参数")
public class ToBOrderAndTakeAppointmentDataODTO {

    @ApiModelProperty("B端订单数量和份数数据")
    private List<ToBOrderDataODTO> toBOrderDataODTOList;

    @ApiModelProperty("B端提货卡数量和份数数据")
    private List<TobOrderTakeQuantityAndNumberODTO> tobOrderTakeQuantityAndNumberList;

}

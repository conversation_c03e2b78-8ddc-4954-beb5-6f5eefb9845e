package com.pinshang.qingyun.order.service;

import com.github.pagehelper.PageInfo;
import com.pinshang.qingyun.base.configure.FeignClientConfiguration;
import com.pinshang.qingyun.base.constant.ApplicationNameConstant;
import com.pinshang.qingyun.order.dto.shop.*;
import com.pinshang.qingyun.order.hystrix.ShopReceiveCilentHystrix;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2017/6/7.
 */
@FeignClient(name = ApplicationNameConstant.QINGYUN_ORDER_SERVICE, fallbackFactory = ShopReceiveCilentHystrix.class, configuration = FeignClientConfiguration.class)
@Service
public interface ShopReceiveClient {

    @RequestMapping(value = "/shopReceive/list", method = RequestMethod.POST)
    PageInfo<ShopReceiveODTO> getListByCondition(@RequestBody ShopReceiveIDTO shopReceiveIDTO);

    @RequestMapping(value = "/shopReceive/showReceive",method = RequestMethod.POST)
    ShopReceiveOrderInfoODTO showReceive(@RequestBody QuerySubOrderIDTO querySubOrderIDTO);

    @RequestMapping(value = "/shopReceive/aduitList",method = RequestMethod.POST)
    PageInfo<AuditInfoODTO> getAduitList(@RequestBody AuditQueryIDTO auditQueryIDTO);

    @RequestMapping(value =  "/shopReceive/saveAduit",method = RequestMethod.POST )
    void saveAduit(@RequestBody AuditSaveIDTO auditSaveIDTO);

    @RequestMapping(value =  "/shopReceive/addReceive",method = RequestMethod.POST )
    String addReceive(@RequestBody ReceiveOrderIDTO receiveOrderIDTO);

    @RequestMapping(value = "/shopReceive/aduitDetail",method = RequestMethod.POST)
    ReceiveAuditODTO getAuditDetail(@RequestBody AuditDetailIDTO auditDetailIDTO);

    /*
     * 新增门店收货单
     */
    @RequestMapping(value = "/shopReceive/newReceiveOrder",method = RequestMethod.POST)
    void newReceiveOrder(@RequestBody ShopReceiveOrderIDTO shopReceiveOrderIDTO);

    @RequestMapping(value = "/shopReceive/getShopName",method = RequestMethod.GET)
    String getShopName(@RequestParam("storeId") String storeId);

    @RequestMapping(value = "/shopReceive/cancelReceiveOrder",method = RequestMethod.POST)
    public void cancelReceiveOrder(@RequestBody ShopReceiveOrderIDTO shopReceiveOrderVo) throws Throwable;

    @RequestMapping(value = "/shopReceive/preOrder/list", method = RequestMethod.POST)
    PageInfo<ShopReceiveODTO> getPreOrderListByCondition(@RequestBody ShopReceiveIDTO shopReceiveIDTO);

    @RequestMapping(value = "/shopReceive/preOrder/showReceive",method = RequestMethod.POST)
    ShopReceiveOrderInfoODTO showPreOrderReceive(@RequestBody QuerySubOrderIDTO querySubOrderIDTO);

    @RequestMapping(value =  "/shopReceive/addPreReceive",method = RequestMethod.POST )
    String addPreReceive(@RequestBody ReceiveOrderIDTO receiveOrderIDTO);

    @RequestMapping(value = "/shopReceive/distribution/ok/{distributionCode}/{userId}",method = RequestMethod.POST)
    void autoReceiveByDirstributionOk(@PathVariable("distributionCode") String distributionCode
            , @PathVariable(value = "userId", required = false) Long userId
            , @RequestBody List<Long> subOrderIds);

    @RequestMapping(value = "/shopReceive/preOrderHQ/list", method = RequestMethod.POST)
    PageInfo<ShopReceiveHQODTO> getPreOrderHQListByCondition(@RequestBody ShopReceiveHQIDTO shopReceiveHQIDTO);

    /**
     * 鲜食自动收货
     * @param orderTime
     * @return
     */
    @RequestMapping(value = "/shopReceive/autoReceive",method = RequestMethod.POST)
    Boolean autoReceive(@RequestParam("orderTime") String orderTime);

    /**
     * 鲜食加盟自动收货(钱大妈)
     * @param orderTime
     * @return
     */
    @RequestMapping(value = "/shopReceive/autoReceiveXsJm",method = RequestMethod.POST)
    Boolean autoReceiveXsJm(@RequestParam("orderTime") String orderTime);

    /**
     * 鲜食自动收货(新),根据队列收货
     * @return
     */
    @RequestMapping(value = "/shopReceive/autoReceiveByQueue",method = RequestMethod.POST)
    Boolean autoReceiveByQueue();

    /**
     * 大店自动收货
     * @param orderTime
     * @return
     */
    @RequestMapping(value = "/bigShopReceive/autoReceiveBigShop",method = RequestMethod.POST)
    Boolean autoReceiveBigShop(@RequestParam("orderTime") String orderTime);


    @RequestMapping(value = "/shopReceive/handleAutoReceive",method = RequestMethod.POST)
    Boolean handleAutoReceive(@RequestParam("orderTime") String orderTime);

    @RequestMapping(value = "/shopReceive/autoReceiveRemind",method = RequestMethod.POST)
    Boolean autoReceiveRemind();
    @RequestMapping(value = "/shopReceive/handleReceiveRemind",method = RequestMethod.POST)
    Boolean handleReceiveRemind();

    /**
     * 商品组配送统计已生成采购单的预订单
     * @param preOrderIDTO
     * @return
     */
    @RequestMapping(value = "/shopReceive/queryTjPreOrderList",method = RequestMethod.POST)
    List<TjPreOrderODTO> queryTjPreOrderList(@RequestBody TjPreOrderIDTO preOrderIDTO);
}

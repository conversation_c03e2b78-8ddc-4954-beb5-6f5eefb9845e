package com.pinshang.qingyun.order.dto.order;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2018/10/16 10:50.
 * @blog http://linuxsogood.org
 */
@Data
public class SaleReturnOrderItemQuantityWrapperIDTO {

    private Long commodityId;

    private BigDecimal breakageQuantity;

    private BigDecimal realReturnQuantity;

    @ApiModelProperty("责任方类型(Responsible party) 1=配送，2=大仓，3=门店")
    private Integer rpType;

    /**退货金额*/
    private BigDecimal compensatePrice;

    /**审核备注*/
    private String auditRemark;
}

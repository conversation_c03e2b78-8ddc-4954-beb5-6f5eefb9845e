package com.pinshang.qingyun.order.hystrix;

import org.springframework.stereotype.Component;

import com.pinshang.qingyun.order.service.PfRechargeClient;

import feign.hystrix.FallbackFactory;

@Component
public class PfRechargeClientHystrix implements FallbackFactory<PfRechargeClient> {
	
	@Override
	public PfRechargeClient create(Throwable throwable) {
		return new PfRechargeClient() {
            @Override
            public void pfAppRechargeJob() {

            }
        };
	}
}

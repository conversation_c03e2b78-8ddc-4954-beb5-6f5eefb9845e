package com.pinshang.qingyun.order.dto.commodity;

import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/*
 * 购物车详情vo
 */
public class ShoppingCartDetailHandleODto {
	TableData[] sites;

	public TableData[] getSites() {
		return sites;
	}
	public void setSites(TableData[] sites) {
		this.sites = sites;
	}

	
	public static class TableData implements Serializable{
		@ApiModelProperty("购物车信息")
		private TitleRow[] title;

		@ApiModelProperty("商品信息")
		private CommodityRow[] commoditys;

		@ApiModelProperty("送货日期")
		private List<DeliveryBatchDay> deliveryBatchDays;

		@ApiModelProperty("配送批次")
		private List<DeliveryBatchEntry> entrys;

		public TitleRow[] getTitle() {
			return title;
		}
		public void setTitle(TitleRow[] title) {
			this.title = title;
		}
		public CommodityRow[] getCommoditys() {
			return commoditys;
		}
		public void setCommoditys(CommodityRow[] commoditys) {
			this.commoditys = commoditys;
		}
		public List<DeliveryBatchDay> getDeliveryBatchDays() {
			return deliveryBatchDays;
		}
		public void setDeliveryBatchDays(List<DeliveryBatchDay> deliveryBatchDays) {
			this.deliveryBatchDays = deliveryBatchDays;
		}
		public List<DeliveryBatchEntry> getEntrys() {
			return entrys;
		}
		public void setEntrys(List<DeliveryBatchEntry> entrys) {
			this.entrys = entrys;
		}
	}
	
	//标题, 如清美-配送-仓库等等
	public static class TitleRow implements Serializable{
		/**
		 * 
		 */
		private static final long serialVersionUID = -6088739460323149234L;
		//公司
		@ApiModelProperty("公司")
		private String companyName;
		//配送模式
		private Integer logisticsModel;
		//配送模式
		@ApiModelProperty("配送模式")
		private String logisticsModelName;
		//仓库
		@ApiModelProperty("仓库")
		private String warehouseName;
		//供货时间
		@ApiModelProperty("订货时间")
		private String supplyTime;
		//商品金额
		@ApiModelProperty("金额合计")
		private BigDecimal totalPrice;
		//品类总数
		private BigDecimal varietyTotal;
		//商品数量总数
		@ApiModelProperty("数量合计")
		private BigDecimal quantityTotal;
		//shoppingCartId
		@ApiModelProperty("购物车主ID")
		private String shoppingCartId;
		//是否在供应商时间+客户时间内
		@ApiModelProperty("是否可以提交订单：true不能 false:可以")
		private Boolean disable;
		
		//是否显示批次信息
		private Boolean showDeliveryBatch;
		private String orderTime;
		private String deliveryBatch;

		private String xdDeliveryBatch;
		@ApiModelProperty("开业日期")
		private Date openDate;

		/** 状态：2-开业前、0-暂停营业、1-营业中、3-永久停业，（原0-停用,1-启用） */
		@ApiModelProperty("状态：2-开业前、0-暂停营业、1-营业中、3-永久停业，（原0-停用,1-启用）")
		private Integer shopStatus;

		@ApiModelProperty("鲜道订货固定送货日期")
		private String xdOrderTime;

		@ApiModelProperty("店铺名字")
		private String shopName;

		private String adminDeliveryBatch;

		@ApiModelProperty("是否自动订货")
		private Boolean autoCommodity; // 是否自动订货
		private String stallName;// 档口名称

		public String getStallName() {
			return stallName;
		}

		public void setStallName(String stallName) {
			this.stallName = stallName;
		}

		public Boolean getAutoCommodity() {
			return autoCommodity;
		}

		public void setAutoCommodity(Boolean autoCommodity) {
			this.autoCommodity = autoCommodity;
		}

		public String getAdminDeliveryBatch() {
			return adminDeliveryBatch;
		}

		public void setAdminDeliveryBatch(String adminDeliveryBatch) {
			this.adminDeliveryBatch = adminDeliveryBatch;
		}

		public String getShopName() {
			return shopName;
		}

		public void setShopName(String shopName) {
			this.shopName = shopName;
		}

		public String getXdDeliveryBatch() {
			return xdDeliveryBatch;
		}

		public void setXdDeliveryBatch(String xdDeliveryBatch) {
			this.xdDeliveryBatch = xdDeliveryBatch;
		}

		public Integer getShopStatus() {
			return shopStatus;
		}

		public void setShopStatus(Integer shopStatus) {
			this.shopStatus = shopStatus;
		}

		public String getXdOrderTime() {
			return xdOrderTime;
		}

		public void setXdOrderTime(String xdOrderTime) {
			this.xdOrderTime = xdOrderTime;
		}

		public Date getOpenDate() {
			return openDate;
		}
		public void setOpenDate(Date openDate) {
			this.openDate = openDate;
		}
		public String getCompanyName() {
			return companyName;
		}
		public void setCompanyName(String companyName) {
			this.companyName = companyName;
		}
		public Integer getLogisticsModel() {
			return logisticsModel;
		}
		public void setLogisticsModel(Integer logisticsModel) {
			this.logisticsModel = logisticsModel;
		}
		public String getWarehouseName() {
			return warehouseName;
		}
		public void setWarehouseName(String warehouseName) {
			this.warehouseName = warehouseName;
		}
		public String getSupplyTime() {
			return supplyTime;
		}
		public void setSupplyTime(String supplyTime) {
			this.supplyTime = supplyTime;
		}
		public BigDecimal getTotalPrice() {
			return totalPrice;
		}
		public void setTotalPrice(BigDecimal totalPrice) {
			this.totalPrice = totalPrice;
		}
		public BigDecimal getVarietyTotal() {
			return varietyTotal;
		}
		public void setVarietyTotal(BigDecimal varietyTotal) {
			this.varietyTotal = varietyTotal;
		}
		public BigDecimal getQuantityTotal() {
			return quantityTotal;
		}
		public void setQuantityTotal(BigDecimal quantityTotal) {
			this.quantityTotal = quantityTotal;
		}
		public String getLogisticsModelName() {
			return logisticsModelName;
		}
		public void setLogisticsModelName(String logisticsModelName) {
			this.logisticsModelName = logisticsModelName;
		}
		public String getShoppingCartId() {
			return shoppingCartId;
		}
		public void setShoppingCartId(String shoppingCartId) {
			this.shoppingCartId = shoppingCartId;
		}
		public Boolean getDisable() {
			return disable;
		}
		public void setDisable(Boolean disable) {
			this.disable = disable;
		}
		public Boolean getShowDeliveryBatch() {
			return showDeliveryBatch;
		}
		public void setShowDeliveryBatch(Boolean showDeliveryBatch) {
			this.showDeliveryBatch = showDeliveryBatch;
		}
		public String getOrderTime() {
			return orderTime;
		}
		public void setOrderTime(String orderTime) {
			this.orderTime = orderTime;
		}
		public String getDeliveryBatch() {
			return deliveryBatch;
		}
		public void setDeliveryBatch(String deliveryBatch) {
			this.deliveryBatch = deliveryBatch;
		}
	}
	
	public static class CommodityRow implements Serializable{
		/**
		 * 
		 */
		private static final long serialVersionUID = -7457474595424903318L;
		//商品id
		@ApiModelProperty("商品id")
		private String commodityId;

		//商品名称
		@ApiModelProperty("商品名称")
		private String commodityName;

		//规格型号
		@ApiModelProperty("规格型号")
		private String commoditySpec;

		// 条形码
		@ApiModelProperty("条形码")
		private String barCode;
		//单价
		@ApiModelProperty("单价")
		private BigDecimal price;

		//数量
		@ApiModelProperty("数量")
		private BigDecimal quantity;
		//商品限量
		@ApiModelProperty("商品限量")
		private BigDecimal commodityNumberLimit;

		@ApiModelProperty("是否促销")
		private boolean isPromotionProduct;//是否促销

		@ApiModelProperty("是否新品")
		private boolean isNewProduct;//是否新品

		@ApiModelProperty("速冻 1:是 0：否")
		private Integer frozen;//速冻 1:是 0：否
	    //包装类型
	    private String  packedType;//包类型
	    //小计
		@ApiModelProperty("小计")
	    private BigDecimal totalPrice;
	    //
	    private Boolean isShow;
	    
	  //shoppingCartItemId
	    @ApiModelProperty("购物车明细ID")
	    private String shoppingCartItemId;

		@ApiModelProperty("箱规")
		private BigDecimal salesBoxCapacity;
		
		//单位
		@ApiModelProperty("单位")
		private String commodityUnit;

		@ApiModelProperty("包装规格")
		private BigDecimal commodityPackageSpec;

		@ApiModelProperty("份数")
		private Long shares;

		@ApiModelProperty("01 散装  02 整包")
		private String commodityPackageKind;

		@ApiModelProperty("是否称重0-不称量,1-称重")
		private Integer isWeight;

		@ApiModelProperty("商品编码")
		private String commodityCode;

		@ApiModelProperty("是否系统推荐商品")
		private Boolean shelvesRecommend;
		@ApiModelProperty("系统推荐订货数")
		private BigDecimal shelvesRecommendQuantity;
		@ApiModelProperty("系统推荐时间")
		private String shelvesRecommendTime;

		@ApiModelProperty("已定未收数量")
		private BigDecimal orderedQuantity;// 已定未收数量
		@ApiModelProperty("库存数量")
		private BigDecimal stockQuantity; //现有库存
		@ApiModelProperty("库存份数")
		private Integer stockNumber;
		private BigDecimal boxCapacity;

		@ApiModelProperty("是否有库存: ture:有；false：没有")
		private Boolean haveInventory;
		@ApiModelProperty("可用库存数量")
		private BigDecimal inventoryQuantity;

		/** 近15天日均销量 */
		private BigDecimal avgDailySales15Days;
		/** 当日销售数量 */
		private BigDecimal dailySales;
		/** 近15天毛利率 */
		private BigDecimal grossProfitMargin15Days;

		public BigDecimal getAvgDailySales15Days() {
			return avgDailySales15Days;
		}

		public void setAvgDailySales15Days(BigDecimal avgDailySales15Days) {
			this.avgDailySales15Days = avgDailySales15Days;
		}

		public BigDecimal getDailySales() {
			return dailySales;
		}

		public void setDailySales(BigDecimal dailySales) {
			this.dailySales = dailySales;
		}

		public BigDecimal getGrossProfitMargin15Days() {
			return grossProfitMargin15Days;
		}

		public void setGrossProfitMargin15Days(BigDecimal grossProfitMargin15Days) {
			this.grossProfitMargin15Days = grossProfitMargin15Days;
		}

		public Boolean getHaveInventory() {
			return haveInventory;
		}

		public void setHaveInventory(Boolean haveInventory) {
			this.haveInventory = haveInventory;
		}

		public BigDecimal getInventoryQuantity() {
			return inventoryQuantity;
		}

		public void setInventoryQuantity(BigDecimal inventoryQuantity) {
			this.inventoryQuantity = inventoryQuantity;
		}

		public BigDecimal getBoxCapacity() {
			return boxCapacity;
		}

		public void setBoxCapacity(BigDecimal boxCapacity) {
			this.boxCapacity = boxCapacity;
		}

		public BigDecimal getOrderedQuantity() {
			return orderedQuantity;
		}

		public void setOrderedQuantity(BigDecimal orderedQuantity) {
			this.orderedQuantity = orderedQuantity;
		}

		public BigDecimal getStockQuantity() {
			return stockQuantity;
		}

		public void setStockQuantity(BigDecimal stockQuantity) {
			this.stockQuantity = stockQuantity;
		}

		public Integer getStockNumber() {
			return stockNumber;
		}

		public void setStockNumber(Integer stockNumber) {
			this.stockNumber = stockNumber;
		}

		public Boolean getShelvesRecommend() {
			return shelvesRecommend;
		}
		public void setShelvesRecommend(Boolean shelvesRecommend) {
			this.shelvesRecommend = shelvesRecommend;
		}
		public BigDecimal getShelvesRecommendQuantity() {
			return shelvesRecommendQuantity;
		}
		public void setShelvesRecommendQuantity(BigDecimal shelvesRecommendQuantity) {
			this.shelvesRecommendQuantity = shelvesRecommendQuantity;
		}
		public String getShelvesRecommendTime() {
			return shelvesRecommendTime;
		}
		public void setShelvesRecommendTime(String shelvesRecommendTime) {
			this.shelvesRecommendTime = shelvesRecommendTime;
		}
		public String getBarCode() {
			return barCode;
		}

		public void setBarCode(String barCode) {
			this.barCode = barCode;
		}

		public String getCommodityCode() {
			return commodityCode;
		}

		public void setCommodityCode(String commodityCode) {
			this.commodityCode = commodityCode;
		}

		public String getCommodityPackageKind() {
			return commodityPackageKind;
		}

		public void setCommodityPackageKind(String commodityPackageKind) {
			this.commodityPackageKind = commodityPackageKind;
		}

		public Integer getIsWeight() {
			return isWeight;
		}

		public void setIsWeight(Integer isWeight) {
			this.isWeight = isWeight;
		}

		public BigDecimal getCommodityPackageSpec() {
			return commodityPackageSpec;
		}

		public void setCommodityPackageSpec(BigDecimal commodityPackageSpec) {
			this.commodityPackageSpec = commodityPackageSpec;
		}

		public Long getShares() {
			return shares;
		}

		public void setShares(Long shares) {
			this.shares = shares;
		}

		public boolean isPromotionProduct() {
			return isPromotionProduct;
		}
		public void setPromotionProduct(boolean promotionProduct) {
			isPromotionProduct = promotionProduct;
		}
		public boolean isNewProduct() {
			return isNewProduct;
		}
		public void setNewProduct(boolean newProduct) {
			isNewProduct = newProduct;
		}
		public String getCommodityUnit() {
			return commodityUnit;
		}
		public void setCommodityUnit(String commodityUnit) {
			this.commodityUnit = commodityUnit;
		}
		public String getCommodityId() {
			return commodityId;
		}
		public void setCommodityId(String commodityId) {
			this.commodityId = commodityId;
		}
		public String getCommoditySpec() {
			return commoditySpec;
		}
		public void setCommoditySpec(String commoditySpec) {
			this.commoditySpec = commoditySpec;
		}
		public BigDecimal getPrice() {
			return price;
		}
		public void setPrice(BigDecimal price) {
			this.price = price;
		}
		public BigDecimal getQuantity() {
			return quantity;
		}
		public void setQuantity(BigDecimal quantity) {
			this.quantity = quantity;
		}
		public BigDecimal getCommodityNumberLimit() {
			return commodityNumberLimit;
		}
		public void setCommodityNumberLimit(BigDecimal commodityNumberLimit) {
			this.commodityNumberLimit = commodityNumberLimit;
		}
		public Integer getFrozen() {
			return frozen;
		}
		public void setFrozen(Integer frozen) {
			this.frozen = frozen;
		}
		public String getPackedType() {
			return packedType;
		}
		public void setPackedType(String packedType) {
			this.packedType = packedType;
		}
		public String getCommodityName() {
			return commodityName;
		}
		public void setCommodityName(String commodityName) {
			this.commodityName = commodityName;
		}
		public BigDecimal getTotalPrice() {
			return totalPrice;
		}
		public void setTotalPrice(BigDecimal totalPrice) {
			this.totalPrice = totalPrice;
		}
		public Boolean getIsShow() {
			return isShow;
		}
		public void setIsShow(Boolean isShow) {
			this.isShow = isShow;
		}
		public String getShoppingCartItemId() {
			return shoppingCartItemId;
		}
		public void setShoppingCartItemId(String shoppingCartItemId) {
			this.shoppingCartItemId = shoppingCartItemId;
		}
		public BigDecimal getSalesBoxCapacity() {
			return salesBoxCapacity;
		}
		public void setSalesBoxCapacity(BigDecimal salesBoxCapacity) {
			this.salesBoxCapacity = salesBoxCapacity;
		}
	}

	public static class DeliveryBatchEntry  implements Serializable{
		private static final long serialVersionUID = 3864327178292821808L;
		private String optionName;
		private String optionCode;
		private String optionValue;
		private String memo;
		public String getOptionCode() {
			return optionCode;
		}
		public void setOptionCode(String optionCode) {
			this.optionCode = optionCode;
		}
		public String getOptionValue() {
			return optionValue;
		}
		public void setOptionValue(String optionValue) {
			this.optionValue = optionValue;
		}
		public String getMemo() {
			return memo;
		}
		public void setMemo(String memo) {
			this.memo = memo;
		}
		public String getOptionName() {
			return optionName;
		}
		public void setOptionName(String optionName) {
			this.optionName = optionName;
		}
	}


	public static class DeliveryBatchDay implements Serializable{
		private static final long serialVersionUID = 980745494135229662L;
		private String value;
		private String day;
		public String getValue() {
			return value;
		}
		public void setValue(String value) {
			this.value = value;
		}
		public String getDay() {
			return day;
		}
		public void setDay(String day) {
			this.day = day;
		}
	}
}

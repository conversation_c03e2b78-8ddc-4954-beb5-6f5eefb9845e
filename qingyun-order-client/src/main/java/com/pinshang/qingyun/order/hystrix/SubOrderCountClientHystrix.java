package com.pinshang.qingyun.order.hystrix;

import com.pinshang.qingyun.order.dto.order.CountCommodityInfoVo;
import com.pinshang.qingyun.order.dto.order.CountCommoditySubmitVo;
import com.pinshang.qingyun.order.dto.order.ShopOrderNumVo;
import com.pinshang.qingyun.order.service.SubOrderCountClient;
import feign.hystrix.FallbackFactory;
import org.springframework.stereotype.Component;

import java.util.List;


@Component
public class SubOrderCountClientHystrix implements FallbackFactory<SubOrderCountClient> {
    @Override
    public SubOrderCountClient create(Throwable throwable) {
        return new SubOrderCountClient() {


            @Override
            public ShopOrderNumVo shopOrderNum() {
                return null;
            }

            @Override
            public CountCommodityInfoVo countCommodityInfo(String barCode) {
                return null;
            }

            @Override
            public Boolean submitCountCommodity(CountCommoditySubmitVo vo) {
                return null;
            }

            @Override
            public Boolean submitCountMultiCommodity(List<CountCommoditySubmitVo> list) {
                return null;
            }

            @Override
            public List<CountCommodityInfoVo> countCommodityList() {
                return null;
            }

            @Override
            public CountCommodityInfoVo commodityInfo(String barCode) {
                return null;
            }

            @Override
            public Boolean redisDeleteCommodity(List<Long> commodityIds) {
                return null;
            }


        };
    }

}

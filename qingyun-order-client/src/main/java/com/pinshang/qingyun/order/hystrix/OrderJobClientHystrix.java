package com.pinshang.qingyun.order.hystrix;

import com.pinshang.qingyun.order.dto.order.CompensateOrderQuantityReqODTO;
import com.pinshang.qingyun.order.service.OrderJobClient;
import feign.hystrix.FallbackFactory;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/07/01 17:47
 */
@Component
public class OrderJobClientHystrix implements FallbackFactory<OrderJobClient> {

    @Override
    public OrderJobClient create(Throwable cause) {
        return new OrderJobClient(){

            @Override
            public List<Long> compensateOrderQuantity(CompensateOrderQuantityReqODTO odto) {
                return null;
            }

            @Override
            public Boolean countShopOrderedQuantity() {
                return null;
            }

            @Override
            public Boolean commodityFreshOrderSummary(String orderTime) {
                return null;
            }

            @Override
            public Boolean shelvesRecommendAddShoppingCart(String hhmm) {
                return null;
            }

            @Override
            public Boolean amountFlowComp() {
                return null;
            }

            @Override
            public Boolean cancelOrderAmountCompare(String timeStamp) {
                return null;
            }
        };
    }
}

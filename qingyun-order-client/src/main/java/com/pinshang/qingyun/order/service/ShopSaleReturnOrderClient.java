package com.pinshang.qingyun.order.service;

import com.github.pagehelper.PageInfo;
import com.pinshang.qingyun.base.configure.FeignClientConfiguration;
import com.pinshang.qingyun.base.constant.ApplicationNameConstant;
import com.pinshang.qingyun.order.dto.*;
import com.pinshang.qingyun.order.dto.order.*;
import com.pinshang.qingyun.order.hystrix.ShopSaleReturnOrderHystrix;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2018/10/15 10:15.
 * @blog http://linuxsogood.org
 */
@FeignClient(value = ApplicationNameConstant.QINGYUN_ORDER_SERVICE, fallbackFactory = ShopSaleReturnOrderHystrix.class, configuration = FeignClientConfiguration.class)
public interface ShopSaleReturnOrderClient {

    @RequestMapping(value = "/saleReturnOrder/querySaleReturnDetail/{code}/{enterpriseId}", method = RequestMethod.GET)
    SaleReturnOrderDetailODTO querySaleReturnDetail(@PathVariable(value = "code") String code, @PathVariable(value = "enterpriseId") Long enterpriseId);

    @RequestMapping(value = "/saleReturnOrder/returnDifferentReport", method = RequestMethod.POST)
    PageInfo<SaleReturnReportODTO> listReturnReport(@RequestBody SaleReturnReportIDTO idto);

    @RequestMapping(value = "/saleReturnOrder/list", method = RequestMethod.POST)
    PageInfo<SaleReturnOrderListODTO> list(@RequestBody SaleReturnOrderListIDTO idto);

    @RequestMapping(value = "/saleReturnOrder/querySaleReturnOrderList", method = RequestMethod.POST)
    public List<SaleReturnOrderPDAODTO> querySaleReturnOrderList(@RequestBody SaleReturnOrderPDAIDTO idto);

    @RequestMapping(value = "/saleReturnOrder/updateOrderStatus", method = RequestMethod.POST)
    boolean updateOrderStatus(@RequestBody SaleReturnOrderIDTO idto);

    @RequestMapping(value = "/saleReturnOrder/selectByOrderCodeAndLogisticsModel/{returnOrderCode}", method = RequestMethod.POST)
    SaleReturnOrderODTO selectByOrderCodeAndLogisticsModel(@PathVariable("returnOrderCode") String returnOrderCode, @RequestBody List<Integer> asList);

    @RequestMapping(value = "/saleReturnOrder/processReturnOrderItemQuantity", method = RequestMethod.POST)
    boolean processReturnOrderItemQuantity(@RequestBody SaleReturnOrderUpdateQuantityWrapperIDTO updateQuantityWrapper);

    @RequestMapping(value = "/saleReturnOrder/queryReturnReportSumEntry", method = RequestMethod.POST)
    SaleReturnReportSumODTO queryReturnReportSumEntry(@RequestBody SaleReturnReportIDTO idto);

    @RequestMapping(value = "/saleReturnOrder/queryStoreInfo", method = RequestMethod.POST)
    List<StoreInfoODTO> queryStoreInfo(@RequestBody StoreInfoIDTO idto);

    @RequestMapping(value = "/saleReturnOrder/checkReceiver", method = RequestMethod.POST)
    public CheckReceiverODTO checkReceiver(CheckReceiverIDTO idto);
}

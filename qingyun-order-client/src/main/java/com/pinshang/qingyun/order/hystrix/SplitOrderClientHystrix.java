package com.pinshang.qingyun.order.hystrix;

import com.github.pagehelper.PageInfo;
import com.pinshang.qingyun.order.dto.orderMonitor.NonSplitCommodityDetailODTO;
import com.pinshang.qingyun.order.dto.orderMonitor.NonSplitOrderODTO;
import com.pinshang.qingyun.order.dto.orderMonitor.NonSplitOrderQueryIDTO;
import com.pinshang.qingyun.order.service.SplitOrderClient;
import feign.hystrix.FallbackFactory;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public class SplitOrderClientHystrix implements FallbackFactory<SplitOrderClient> {
    @Override
    public SplitOrderClient create(Throwable throwable) {
        return new SplitOrderClient() {
            @Override
            public PageInfo<NonSplitOrderODTO> manualSplitOrderByPage(NonSplitOrderQueryIDTO nonSplitOrderQueryVo) {
                return null;
            }

            @Override
            public List<NonSplitCommodityDetailODTO> findNonSplitByOrderId(Long orderId) {
                return null;
            }

//            @Override
//            public Integer removeSplitOrderInfo() {
//                return null;
//            }

            @Override
            public void manualSplitOrder(NonSplitOrderQueryIDTO nonSplitOrderQueryIDTO) {

            }

            @Override
            public Boolean splitOrderByJob(String createTime) {
                return null;
            }
        };
    }

}

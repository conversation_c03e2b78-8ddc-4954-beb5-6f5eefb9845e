//package com.pinshang.qingyun.order.hystrix;
//
//import com.pinshang.qingyun.order.dto.commodity.ShipmentsIDTO;
//import com.pinshang.qingyun.order.dto.commodity.ShipmentsODTO;
//import com.pinshang.qingyun.order.service.ShipmentsClient;
//import feign.hystrix.FallbackFactory;
//import lombok.extern.slf4j.Slf4j;
//import org.springframework.stereotype.Component;
//
//import java.util.List;
//
///**
// * Create by JXL on 2018/4/9
// */
//@Component
//@Slf4j
//public class ShipmentsClientHystrix implements FallbackFactory<ShipmentsClient> {
//    @Override
//    public ShipmentsClient create(Throwable throwable) {
//        return new ShipmentsClient() {
//            @Override
//            public List<ShipmentsODTO> list(ShipmentsIDTO vo) {
//                log.error(throwable.getMessage());
//                return null;
//            }
//        };
//    }
//}

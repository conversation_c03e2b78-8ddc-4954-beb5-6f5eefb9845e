package com.pinshang.qingyun.order.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 *  查询待收货/收货中的客户
 */
@Data
public class  CheckReceiverIDTO {
    @ApiModelProperty("退货单ID / 退领单ID")
    private Long id;
    @ApiModelProperty("退货单号")
    private String code;
    private Long enterpriseId;
    private Long receiverId;
    @ApiModelProperty("业务类型: 1.门店退货 2.领料退货")
    private Integer bizType;
}

package com.pinshang.qingyun.order.hystrix;

import java.util.List;

import org.springframework.stereotype.Component;

import com.pinshang.qingyun.order.dto.tms.RefOrderForWaybillODTO;
import com.pinshang.qingyun.order.dto.tms.SelectRefOrderForWaybillIDTO;
import com.pinshang.qingyun.order.dto.tms.WarehouseConfirmInfoODTO;
import com.pinshang.qingyun.order.service.OrderTmsClient;

import feign.hystrix.FallbackFactory;

/**
 * 【订单系统 <-> 物流系统】
 */
@Component
public class OrderTmsClientHystrix implements FallbackFactory<OrderTmsClient> {
	@Override
	public OrderTmsClient create(Throwable throwable) {
		return new OrderTmsClient() {

			@Override
			public RefOrderForWaybillODTO selectRefOrder(SelectRefOrderForWaybillIDTO idto) {
				return null;
			}

			@Override
			public List<WarehouseConfirmInfoODTO> selectWarehouseConfirmInfoList(Long returnOrderId) {
				return null;
			}

		};
	}

}

package com.pinshang.qingyun.order.hystrix;

import com.pinshang.qingyun.order.service.SettlementConsumerOrderClient;
import com.pinshang.qingyun.order.service.XDShoppingCartClient;
import feign.hystrix.FallbackFactory;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

@Component
public class SettlementConsumerOrderClientHystrix implements FallbackFactory<SettlementConsumerOrderClient> {
	private Logger logger = LoggerFactory.getLogger(getClass());
	
	@Override
	public SettlementConsumerOrderClient create(Throwable throwable) {
		return new SettlementConsumerOrderClient() {
			@Override
			public Long orderSave(String str) {
				return null;
			}
		};
	}
}

package com.pinshang.qingyun.order.hystrix;


import com.github.pagehelper.PageInfo;
import com.pinshang.qingyun.order.dto.shop.ReturnOrderDetailsReportIDto;
import com.pinshang.qingyun.order.dto.shop.ReturnOrderDetailsReportODto;
import com.pinshang.qingyun.order.service.OrderReportClient;
import feign.hystrix.FallbackFactory;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;


@Component
public class OrderReportClientHystrix implements FallbackFactory<OrderReportClient> {
	private Logger logger = LoggerFactory.getLogger(getClass());

	@Override
	public OrderReportClient create(Throwable throwable) {
		return new OrderReportClient() {

			@Override
			public PageInfo<ReturnOrderDetailsReportODto> findReturnOrderDetailsReport(ReturnOrderDetailsReportIDto returnOrderDetailsReportIDto) {
				return null;
			}
		};
	}
}

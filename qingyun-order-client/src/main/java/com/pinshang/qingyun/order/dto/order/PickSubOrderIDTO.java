package com.pinshang.qingyun.order.dto.order;

import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2018/11/1 17:48.
 * @blog http://linuxsogood.org
 */
@Data
public class PickSubOrderIDTO {

    private Long storeId;
    private Long subOrderId;
    //出库单号
    private String stockOutOrderCode;
    private Date stockOutTime;
    /**
     * MessageOperationType INSERT OR  UPDATE
     */
    private String optionType;
    /**
     * 是否需要把消息发送结算
     * B端的大仓库不需要发送结算，非B端的需要发送结算(以前批次+非批次出库)
     */
    private Boolean isSendSettle;
    private List<PickSubOrderItemODTO> itemList;

//    @Transient
    private Long stockOutOrderId;
}

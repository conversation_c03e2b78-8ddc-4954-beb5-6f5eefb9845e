package com.pinshang.qingyun.order.dto.order;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class CountCommoditySubmitVo {

    @ApiModelProperty("商品Id")
    private Long commodityId;

    @ApiModelProperty("调整数量")
    private BigDecimal adjustNum;

    @ApiModelProperty("1=增加  2=减去 3=更新")
    private Integer type;
}

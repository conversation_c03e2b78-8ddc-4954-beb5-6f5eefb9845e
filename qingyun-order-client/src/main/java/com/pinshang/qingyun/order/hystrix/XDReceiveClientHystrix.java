package com.pinshang.qingyun.order.hystrix;

import com.pinshang.qingyun.order.dto.order.XdReceiveDocCommodityIDTO;
import com.pinshang.qingyun.order.dto.order.XdReceiveDocDetailODTO;
import com.pinshang.qingyun.order.dto.order.XdReceiveDocIDTO;
import com.pinshang.qingyun.order.dto.order.XdReceiveDocODTO;
import com.pinshang.qingyun.order.service.XDReceiveClient;
import feign.hystrix.FallbackFactory;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public class XDReceiveClientHystrix implements FallbackFactory<XDReceiveClient> {
	private Logger logger = LoggerFactory.getLogger(getClass());
	
	@Override
	public XDReceiveClient create(Throwable throwable) {
		return new XDReceiveClient() {


			@Override
			public Boolean createXdReceiveDoc(String timeStr) {
				return null;
			}

			@Override
			public List<XdReceiveDocODTO> getXdReceiveDocList() {
				return null;
			}

			@Override
			public XdReceiveDocDetailODTO getXdReceiveDocCommodityInfoList(XdReceiveDocIDTO xdReceiveDocIDTO) {
				return null;
			}

			@Override
			public Boolean addXdReceive(List<XdReceiveDocCommodityIDTO> xdReceiveDocCommodityIDTOList) {
				return null;
			}
		};
	}
}

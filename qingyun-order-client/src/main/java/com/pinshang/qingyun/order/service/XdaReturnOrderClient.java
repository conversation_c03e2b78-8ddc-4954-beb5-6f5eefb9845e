package com.pinshang.qingyun.order.service;

import com.pinshang.qingyun.base.configure.FeignClientConfiguration;
import com.pinshang.qingyun.base.constant.ApplicationNameConstant;
import com.pinshang.qingyun.order.dto.tda.CancelReturnOrderIDTO;
import com.pinshang.qingyun.order.dto.tda.ReturnOrderODTO;
import com.pinshang.qingyun.order.dto.tda.SaveReturnOrderODTO;
import com.pinshang.qingyun.order.hystrix.XdaReturnOrderClientHystrix;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

@FeignClient(value = ApplicationNameConstant.QINGYUN_ORDER_SERVICE, fallbackFactory = XdaReturnOrderClientHystrix.class, configuration = FeignClientConfiguration.class)
public interface XdaReturnOrderClient {

    /***
     * 生成退货/少货单
     * 来源：app发起，后台新增投诉，通达销售的订单配送失败，通达销售的取消物流拦截成功的订单
     */
    @PostMapping("/xda/return/generateReturnOrder")
    Boolean generateReturnOrder(@RequestBody SaveReturnOrderODTO dto);

    /***
     * 取消退货/少货单
     */
    @PostMapping("/xda/return/cancelReturnOrder")
    Boolean cancelReturnOrder(@RequestBody CancelReturnOrderIDTO dto);

    /***
     * 查询单个退货单
     */
    @PostMapping("/xda/return/queryReturnOrderByReturnOrderSeq")
    ReturnOrderODTO queryReturnOrderByReturnOrderSeq(@RequestParam("returnOrderSeq") String returnOrderSeq);
}

package com.pinshang.qingyun.order.service;

import com.pinshang.qingyun.base.configure.FeignClientConfiguration;
import com.pinshang.qingyun.base.constant.ApplicationNameConstant;
import com.pinshang.qingyun.order.dto.settlement.OrderReqIDTO;
import com.pinshang.qingyun.order.dto.settlement.SettleOrderItemODTO;
import com.pinshang.qingyun.order.dto.settlement.SettleOrderODTO;
import com.pinshang.qingyun.order.hystrix.SettleOrderClientHystrix;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

@FeignClient(value = ApplicationNameConstant.QINGYUN_ORDER_SERVICE, fallbackFactory = SettleOrderClientHystrix.class, configuration = FeignClientConfiguration.class)
public interface SettleOrderClient {


    @RequestMapping(value = "/settleOrder/findOrderByParams", method = RequestMethod.POST)
    List<SettleOrderODTO> findOrderByParams(@RequestBody OrderReqIDTO orderReqIDTO);

    @RequestMapping(value = "/settleOrder/findXsZsOrderByParams", method = RequestMethod.POST)
    List<SettleOrderODTO> findXsZsOrderByParams(@RequestBody OrderReqIDTO orderReqIDTO);

//    @RequestMapping(value = "/settleOrder/findXsZtPsOrderByParams", method = RequestMethod.POST)
//    List<SettleOrderODTO> findXsZtPsOrderByParams(@RequestBody OrderReqIDTO orderReqIDTO);

    @RequestMapping(value = "/settleOrder/findXsOrderItemByOrderIds", method = RequestMethod.POST)
    List<SettleOrderItemODTO> findXsOrderItemByOrderIds(@RequestParam("orderIds") List<Long> orderIds, @RequestParam("logisticsModel") int logisticsModel);


    @RequestMapping(value = "/settleOrder/findTOrderByParams", method = RequestMethod.POST)
    List<SettleOrderODTO> findTOrderByParams(@RequestBody OrderReqIDTO orderReqIDTO);

    @RequestMapping(value = "/settleOrder/findTOrderItemByOrderIds", method = RequestMethod.POST)
    List<SettleOrderItemODTO> findTOrderItemByOrderIds(@RequestParam("orderIds") List<Long> orderIds);


}

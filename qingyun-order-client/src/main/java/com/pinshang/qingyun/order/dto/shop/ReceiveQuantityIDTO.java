package com.pinshang.qingyun.order.dto.shop;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2017/6/19.
 */
@Data
public class ReceiveQuantityIDTO {
	private String itemId;
	
    private String commodityCode;

    private String commodityId;

    private BigDecimal realReceiveQuantity;
    
    private BigDecimal realDeliveryQuantity;

    @ApiModelProperty("实收份数")
    private Integer realReceiveNumber;
    
    private Boolean isAdd; 
    
    private BigDecimal price;
    
    private BigDecimal requireQuantity;
}

package com.pinshang.qingyun.order.service;

import com.pinshang.qingyun.base.configure.FeignClientConfiguration;
import com.pinshang.qingyun.base.constant.ApplicationNameConstant;
import com.pinshang.qingyun.order.dto.order.XdReceiveDocCommodityIDTO;
import com.pinshang.qingyun.order.dto.order.XdReceiveDocDetailODTO;
import com.pinshang.qingyun.order.dto.order.XdReceiveDocIDTO;
import com.pinshang.qingyun.order.dto.order.XdReceiveDocODTO;
import com.pinshang.qingyun.order.hystrix.XDReceiveClientHystrix;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

@FeignClient(value = ApplicationNameConstant.QINGYUN_ORDER_SERVICE, fallbackFactory = XDReceiveClientHystrix.class, configuration = FeignClientConfiguration.class)
public interface XDReceiveClient {

    @RequestMapping(value = "/xdReceive/createXdReceiveDoc", method = RequestMethod.POST)
    Boolean createXdReceiveDoc(@RequestParam("timeStr") String timeStr);

    @RequestMapping(value = "/xdReceive/getXdReceiveDocList", method = RequestMethod.GET)
    List<XdReceiveDocODTO> getXdReceiveDocList();

    @RequestMapping(value = "/xdReceive/getXdReceiveDocCommodityInfoList", method = RequestMethod.POST)
    XdReceiveDocDetailODTO getXdReceiveDocCommodityInfoList(@RequestBody XdReceiveDocIDTO xdReceiveDocIDTO);

    @RequestMapping(value = "/xdReceive/addXdReceive",method = RequestMethod.POST)
    Boolean addXdReceive(@RequestBody List<XdReceiveDocCommodityIDTO> xdReceiveDocCommodityIDTOList);
}

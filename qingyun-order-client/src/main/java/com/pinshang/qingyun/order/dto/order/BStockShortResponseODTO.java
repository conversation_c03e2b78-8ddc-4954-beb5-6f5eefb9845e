package com.pinshang.qingyun.order.dto.order;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * @Author: sk
 * @Date: 2024/3/5
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class BStockShortResponseODTO {

    private String commodityId;

    private String commodityCode;

    private String commodityName;

    @ApiModelProperty("当前商品数量")
    private BigDecimal quantity;

    @ApiModelProperty("可用库存数量")
    private BigDecimal inventoryQuantity;


}

package com.pinshang.qingyun.order.service;

import com.github.pagehelper.PageInfo;
import com.pinshang.qingyun.base.configure.FeignClientConfiguration;
import com.pinshang.qingyun.base.constant.ApplicationNameConstant;
import com.pinshang.qingyun.order.dto.purchase.*;
import com.pinshang.qingyun.order.hystrix.PurchaseOrderListClientHystrix;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import java.util.List;

/**
 * Created by crell on 2017/6/14
 * 采购列表client
 */

@FeignClient(name = ApplicationNameConstant.QINGYUN_ORDER_SERVICE, fallbackFactory = PurchaseOrderListClientHystrix.class, configuration = FeignClientConfiguration.class)
@Service
public interface PurchaseOrderListClient {

//    @RequestMapping(value = "/order/purchase/connectionListByPage", method = RequestMethod.POST)
//    PageInfo<ConnectionPurchaseODto> findConnectionPurchaseByPage(PurchaseListIDto purchaseListIDto);

    @RequestMapping(value = "/order/purchase/connectionPurchaseDetail", method = RequestMethod.POST)
    PageInfo<ConnectionPurchaseDetailODto> findConnectionPurchaseDetail(PurchaseListIDto purchaseListIDto);
    
    @RequestMapping(value ="/order/purchase/findDirectSendingPurchaseItems", method = RequestMethod.POST)
	public List<DirectSendingPurchaseItemODto> findDirectSendingPurchaseItems(ShopPurchaseSearchIDto shopPurchaseSearchIDto);

    @RequestMapping(value ="/order/purchase/findDirectSendingPurchaseItemList", method = RequestMethod.POST)
    public List<DirectSendingPurchaseItemODto> findDirectSendingPurchaseItemList(ShopPurchaseSearchIDto shopPurchaseSearchIDto);

    @RequestMapping(value = "/order/purchase/sendListByPage", method = RequestMethod.POST)
    PageInfo<ConnectionPurchaseODto> findSendPurchaseByPage(PurchaseListIDto purchaseListIDto);

    @RequestMapping(value = "/order/purchase/sendPurchaseDetail", method = RequestMethod.POST)
    List<SendPurchaseDetailODto> findSendPurchaseDetail(PurchaseListIDto purchaseListIDto);
}

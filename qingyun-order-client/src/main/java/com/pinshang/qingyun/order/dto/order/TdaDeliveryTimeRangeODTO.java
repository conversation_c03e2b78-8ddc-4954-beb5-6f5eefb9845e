package com.pinshang.qingyun.order.dto.order;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @Author: sk
 * @Date: 2024/5/11
 */
@Data
public class TdaDeliveryTimeRangeODTO {

    @ApiModelProperty(value = "送货时间段是否可选  1 是  0 否")
    private Integer disabled;

    @ApiModelProperty(value = "送货时间段")
    private String deliveryTimeRange;

    @ApiModelProperty(value = "物流中心ID")
    @JsonSerialize(using= ToStringSerializer.class)
    private Long logisticsCenterId;
    @ApiModelProperty(value = "物流中心名称")
    private String logisticsCenterName;

    @ApiModelProperty(value = "配送批次：1-一配、2-二配		—— 参见枚举： DeliveryBatchEnums")
    private Integer deliveryBatch;

    @ApiModelProperty(value = "业务类型：10-通达销售	—— 参见枚举： BusinessTypeEnums")
    private Integer businessType;

    @ApiModelProperty(value = "客户可下单时间-开始：HH:mm")
    private String storeBeginTime;
    @ApiModelProperty(value = "客户可下单时间-截止：HH:mm")
    private String storeEndTime;


    @ApiModelProperty(value = "客服截单时间：HH:mm")
    private String kfEndTime;


    @ApiModelProperty(value = "客户可下单时间-开始：--------- 结束")
    private String storeTime;

    private List<String> pickupTimeList;
    private Long id;
    public String getStoreTime() {
        return storeBeginTime + " - " + storeEndTime;
    }

}

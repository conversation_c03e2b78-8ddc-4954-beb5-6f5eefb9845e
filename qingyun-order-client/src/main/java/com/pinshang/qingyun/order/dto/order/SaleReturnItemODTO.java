package com.pinshang.qingyun.order.dto.order;

import com.pinshang.qingyun.box.utils.SpringUtil;
import com.pinshang.qingyun.box.utils.StringUtil;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * Created by crell on 2017/11/6.
 */
@Data
public class SaleReturnItemODTO {
    private Long id;

    private String commodityId;

    private String commodityCode;

    private String commodityName;

    private String commoditySpec;

    private String barCode;

    private String unit;

    private BigDecimal price;

    private BigDecimal returnQuantity;

    private BigDecimal totalPrice;

    private Integer returnReason;

    private BigDecimal realReturnQuantity;

    private String remark;

    //商品所属物流模式、供应商、仓库
    private Integer logisticsModel;

    private Long supplierId;

    private Long warehouseId;

    private BigDecimal commodityPackageSpec;

    // 份数
    private Long shares;

    @ApiModelProperty("责任方类型(Responsible party) 1=配送，2=大仓，3=门店")
    private Integer rpType;

    private Integer ifWeight;

    private List<SaleReturnOrderPicODTO> picList;
    private String videoUrl;
    private String visitVideoUrl;

    /** 档口id */
    private String stallId;
    public String getPicStatusName() {
        return SpringUtil.isEmpty(this.picList)? "无": "有";
    }
    public String getVideoStatusName() {
        return StringUtil.isNullOrEmpty(this.videoUrl)? "无": "有";
    }

    private BigDecimal deliveryQuantity;

}

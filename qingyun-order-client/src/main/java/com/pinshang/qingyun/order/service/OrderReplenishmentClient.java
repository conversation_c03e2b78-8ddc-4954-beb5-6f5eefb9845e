package com.pinshang.qingyun.order.service;

import com.github.pagehelper.PageInfo;
import com.pinshang.qingyun.base.configure.FeignClientConfiguration;
import com.pinshang.qingyun.base.constant.ApplicationNameConstant;
import com.pinshang.qingyun.order.dto.order.OrderReplenishmentIDTO;
import com.pinshang.qingyun.order.dto.order.OrderReplenishmentListIDTO;
import com.pinshang.qingyun.order.dto.order.OrderReplenishmentListODTO;
import com.pinshang.qingyun.order.hystrix.OrderReplenishmentClientHystrix;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import java.util.List;

@FeignClient(value = ApplicationNameConstant.QINGYUN_ORDER_SERVICE, fallbackFactory = OrderReplenishmentClientHystrix.class, configuration = FeignClientConfiguration.class)
public interface OrderReplenishmentClient {

    /**
     * 批量导入新增门店补货信息
     * @param list
     * @return
     */
    @RequestMapping(value = {"/orderReplenishment/importOrderReplenishment"}, method = {RequestMethod.POST})
    Object importOrderReplenishment(@RequestBody List<OrderReplenishmentIDTO> list);

    @RequestMapping(value = "/orderReplenishment/list", method = RequestMethod.POST)
    PageInfo<OrderReplenishmentListODTO> orderReplenishmentList(@RequestBody OrderReplenishmentListIDTO orderReplenishmentListIDTO);

}

package com.pinshang.qingyun.order.dto.orderMonitor;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * Created by weican on 2017-10-21.
 */
@Data
public class NonSplitCommodityDetailODTO {
    @ApiModelProperty("订单ID")
    private Long orderId;
    @ApiModelProperty("商品ID")
    private String commodityId;
    @ApiModelProperty("商品编码")
    private String commodityCode;
    @ApiModelProperty("商品名称")
    private String commodityName;
    @ApiModelProperty("物流模式：物流配送模式0=直送，1＝配送，2＝直通")
    private Integer logisticsModel;
    @ApiModelProperty("供应商ID")
    private Long supplierId;
    @ApiModelProperty("供应商名称")
    private String supplierName;
    @ApiModelProperty("仓库ID")
    private Long warehouseId;
    @ApiModelProperty("仓库名称")
    private String warehouseName;
}

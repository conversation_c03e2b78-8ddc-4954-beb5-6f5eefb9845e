package com.pinshang.qingyun.order.service;

import com.github.pagehelper.PageInfo;
import com.pinshang.qingyun.base.configure.FeignClientConfiguration;
import com.pinshang.qingyun.base.constant.ApplicationNameConstant;
import com.pinshang.qingyun.order.dto.shop.StoreAccountIDTO;
import com.pinshang.qingyun.order.dto.shop.StoreAccountODTO;
import com.pinshang.qingyun.order.dto.store.DistributionSortStoreIDTO;
import com.pinshang.qingyun.order.dto.store.DistributionSortStoreODTO;
import com.pinshang.qingyun.order.dto.store.StoreInviceODTO;
import com.pinshang.qingyun.order.dto.store.StoreODTO;
import com.pinshang.qingyun.order.hystrix.StoreClient2Hystrix;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import java.util.List;


/**
 *
 * <AUTHOR>
 * @Date 2018/4/9 18:18
 */
@FeignClient(value = ApplicationNameConstant.QINGYUN_ORDER_SERVICE, fallbackFactory = StoreClient2Hystrix.class, configuration = FeignClientConfiguration.class)
public interface StoreClient2 {

    @RequestMapping(value = "/storeAccount/list" ,method = RequestMethod.POST)
    PageInfo<StoreAccountODTO> list(@RequestBody StoreAccountIDTO vo);

    @RequestMapping(value = "/storeAccount/find/{storeCode}",method = RequestMethod.GET)
    StoreODTO getByCode(@PathVariable("storeCode") String storeCode);

    @RequestMapping(value = "/storeAccount/distribution/list", method = RequestMethod.POST)
    PageInfo<DistributionSortStoreODTO> distributionList(@RequestBody DistributionSortStoreIDTO idto);

    @RequestMapping(value = "/storeAccount/distribution/excel", method = RequestMethod.POST)
    List<Long> handleExcelImportInfo(@RequestBody List<String> storeCodes);

    /**
     * 根据客户id集合查询客户详细信息
     * @param storeIdList
     * @return
     */
    @RequestMapping(value = "/storeAccount/findStoreListByStoreIdList", method = RequestMethod.POST)
    List<StoreODTO> findStoreListByStoreIdList(@RequestBody List<Long> storeIdList);

    /**
     * 条件查询客户列表
     * @param storeName
     * @return
     */
    @RequestMapping(value = "/storeAccount/findStoreListByStoreName",method = RequestMethod.GET)
    List<StoreODTO> findStoreListByStoreName(@RequestParam("storeName")String storeName);


    /**
     * 根据客户 id 查询客户信息
     * @param storeId
     * @return
     */
    @RequestMapping(value = "/storeAccount/findStoreByStoreId",method = RequestMethod.GET)
    StoreODTO findStoreByStoreId(@RequestParam("storeId") Long storeId);



    /**
     * 查询所有绑定门店的客户
     * @return
     */
    @RequestMapping(value = "/storeAccount/findAllShopStoreList",method = RequestMethod.GET)
    List<StoreODTO> findAllShopStoreList();


    /**
     * 根据客户ID 获取 客户发票信息
     * @param storeIds
     * @return
     */
    @RequestMapping(value = "/storeAccount/findListByStoreIds", method = RequestMethod.POST)
    List<StoreInviceODTO> findListByStoreIds(@RequestBody List<Long> storeIds);
    /**
     * 根据客户发票ID 获取 客户发票信息
     * @return
     */
    @RequestMapping(value = "/storeAccount/findInvoiceById", method = RequestMethod.POST)
    StoreInviceODTO findInvoiceById(@RequestParam("uid")  String uid);




}

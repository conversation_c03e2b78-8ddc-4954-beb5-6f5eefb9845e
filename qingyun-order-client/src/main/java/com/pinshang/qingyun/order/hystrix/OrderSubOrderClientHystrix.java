package com.pinshang.qingyun.order.hystrix;

import com.pinshang.qingyun.order.dto.commodity.SubOrderAndItemIDTO;
import com.pinshang.qingyun.order.service.OrderSubOrderClient;
import feign.hystrix.FallbackFactory;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

@Component
public class OrderSubOrderClientHystrix implements FallbackFactory<OrderSubOrderClient> {
    private Logger logger = LoggerFactory.getLogger(getClass());
    @Override
    public OrderSubOrderClient create(Throwable cause) {
        return new OrderSubOrderClient() {
            @Override
            public Long createSubOrderAndItem(SubOrderAndItemIDTO subOrderAndItemIDTO) throws Throwable {
                return null;
            }
        };
    }

}

package com.pinshang.qingyun.order.hystrix;

import com.pinshang.qingyun.order.service.SubOrderConfirmClient;
import feign.hystrix.FallbackFactory;
import org.springframework.stereotype.Component;

@Component
public class SubOrderConfirmClientHystrix implements FallbackFactory<SubOrderConfirmClient> {
	@Override
	public SubOrderConfirmClient create(Throwable throwable) {
		return new SubOrderConfirmClient() {

			@Override
			public void confirmSO2DO() {

			}
		};
	}
}

package com.pinshang.qingyun.order.dto.orderMonitor;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2018/10/12 17:44
 */
@Data
@ApiModel
public class SubOrderMonitorODTO {

    @ApiModelProperty("主键id")
    private Long subOrderId;

    @ApiModelProperty("子单编号")
    private String subOrderCode;

    @ApiModelProperty("创建时间")
    private Date createTime;

    @ApiModelProperty("状态：0-未生成do单, 1-已生成do单， 2-取消")
    private Integer status;

    @ApiModelProperty("物流模式：0=直送，1＝配送，2＝直通")
    private Integer logisticsModel;


    @ApiModelProperty("仓库id")
    private Long warehouseId;

    @ApiModelProperty("仓库")
    private String warehouseName;


    @ApiModelProperty("供应商id")
    private Long supplierId;

    @ApiModelProperty("供应商")
    private String supplierName;

    @ApiModelProperty("品类数")
    private Integer varietyTotal;

    @ApiModelProperty("修改时间")
    private Date updateTime;

    @ApiModelProperty("")
    private List<SubOrderItemMonitorODTO> subOrderItem;

    @ApiModelProperty("1=依据大仓, 2=不限量订货,3=限量供应")
    private Integer stockType;

    @ApiModelProperty("库存依据描述")
    private String stockTypeDesc;
}

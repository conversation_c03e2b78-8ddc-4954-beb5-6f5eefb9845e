package com.pinshang.qingyun.order.dto.order;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 */
@NoArgsConstructor
@AllArgsConstructor
@Data
public class CountCommodityInfoVo {

    @ApiModelProperty("001=今日计划订货商品中没有该商品 " +
            "002=该商品有门店其他人员正在点数中" +
            "003=该称重商品条码中没有称重数量，请通过扫码录入进行点数")
    private String errorCode;

    @ApiModelProperty("商品ID")
    private Long commodityId;

    @ApiModelProperty("商品编码")
    private String commodityCode;

    @ApiModelProperty("商品条码")
    private String barCode;

    @ApiModelProperty("商品名称")
    private String commodityName;

    @ApiModelProperty("规格")
    private String commoditySpec;

    @ApiModelProperty("计量单位")
    private String commodityUnit;

    @ApiModelProperty("包装规格")
    private BigDecimal commodityPackageSpec;

    @ApiModelProperty("是否称重 0=否 1=是")
    private Integer isWeight;

    @ApiModelProperty("订货数量")
    private BigDecimal orderNum;

    @ApiModelProperty("已点数量")
    private BigDecimal countNum;

    @ApiModelProperty("已点数量,用于解析本次扫码的称重商品重量，非称重商品默认是1")
    private BigDecimal nowNum;

    @ApiModelProperty("差异数量=已点数量-实发数量")
    private BigDecimal diffQuantity;
}

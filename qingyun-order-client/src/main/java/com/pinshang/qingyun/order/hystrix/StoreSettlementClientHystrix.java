package com.pinshang.qingyun.order.hystrix;

import com.pinshang.qingyun.order.dto.store.StoreSettlementODTO;
import com.pinshang.qingyun.order.service.StoreSettlementClient;
import feign.hystrix.FallbackFactory;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2019/3/11 16:26
 */
@Component
public class StoreSettlementClientHystrix implements FallbackFactory<StoreSettlementClient> {
    @Override
    public StoreSettlementClient create(Throwable throwable) {

        return new StoreSettlementClient() {

            @Override
            public StoreSettlementODTO queryBalance(String storeCode) {
                return null;
            }

            @Override
            public List<StoreSettlementODTO> findStoreSettleByStoreIds(List<Long> storeIds) {
                return null;
            }
        };
    }
}

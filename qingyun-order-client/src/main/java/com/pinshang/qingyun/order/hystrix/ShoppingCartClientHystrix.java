package com.pinshang.qingyun.order.hystrix;

import com.pinshang.qingyun.base.page.TablePageInfo;
import com.pinshang.qingyun.order.dto.commodity.*;
import com.pinshang.qingyun.order.dto.order.BStockShortResponseODTO;
import com.pinshang.qingyun.order.dto.order.DeliveryBatchODTO;
import com.pinshang.qingyun.order.service.ShoppingCartClient;
import feign.hystrix.FallbackFactory;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PathVariable;

import java.util.List;
import java.util.Map;

@Component
public class ShoppingCartClientHystrix  implements FallbackFactory<ShoppingCartClient> {
	private Logger logger = LoggerFactory.getLogger(getClass());
	
	@Override
	public ShoppingCartClient create(Throwable throwable) {
		return new ShoppingCartClient() {

			@Override
			public List<BStockShortResponseODTO> addShoppingCart(ShoppingCartIDTO shoppingCartIDTO) {
				logger.error(throwable.getMessage());
				return null;
			}

			@Override
			public List<BStockShortResponseODTO> batchAddShoppingCart(ShoppingCartBatchIDTO shoppingCartIDTO) {
				return null;
			}

			@Override
			public Boolean removeShoppingCart(ShoppingCartIDTO shoppingCartIDTO) {
				logger.error(throwable.getMessage());
				return null;
			}

			@Override
			public ShoppingCartDetailODto shoppingCartDetail(@PathVariable("storeId") Long storeId, @PathVariable("isInternal") boolean isInternal, @PathVariable("userId") Long userId) {
				return null;
			}

			@Override
			public TablePageInfo<ShoppingCartDetailODto> shoppingCartDetailAdmin(ShoppingCartAdminPageIDTO shoppingCartAdminPageIDTO) {
				return null;
			}


			@Override
			public ShoppingCartDetailHandleODto cartdetail(Long storeId, Long shoppingCartId, Boolean isInternal, String orderTime) {
				return null;
			}

			@Override
			public ShoppingCartDetailHandleODto cartdetailAdmin(Long shoppingCartId) {
				return null;
			}

			@Override
			public Boolean updateItemQuantity(ShoppingCartIDTO vo) {
				logger.error(throwable.getMessage());
				return null;
			}

			@Override
			public Map<Integer, Map<String, List<String>>> getSplitOrderInfo(
					SplitOrderIDto vo) {
				logger.error(throwable.getMessage());
				return null;
			}

			@Override
			public List<DeliveryBatchODTO> findDeliveryBatch(Long storeId, String date) {
				return null;
			}

			@Override
			public List<DeliveryBatchODTO> findDeliveryBatchByCartId(Long cartId, String date) {
				return null;
			}

			@Override
			public HandCartRspODTO handleRemoveShoppingCart(ShoppingCartIDTO vo) {
				return null;
			}

			@Override
			public HandCartRspODTO handleUpdateItemQuantity(ShoppingCartIDTO vo) {
				return null;
			}

			@Override
			public void batchUpdateItemQuantity(ShoppingCartUpdateIDTO vo) {
			}

			@Override
			public Boolean dealAutoShoppingCart() {
				return null;
			}
		};
	}
}

package com.pinshang.qingyun.order.dto.commodity;

import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;

public class CommodityResultODTO {
    private String commodityName;

    private String commoditySpec;
    private String commodityId;
    private String commodityCode;
    private String categoryName;
    private boolean edit = true;

    private String commodityRemark;//商品备注

    private Integer commodityType;

    private Integer commodityState;
    //商品价格(实际用的->新价格)
    private BigDecimal commodityPrice;

    private BigDecimal commodityNumber;

    private BigDecimal commodityNumberLimit;

    private Integer frozen;//速冻

    private String  packedType;//包类型(01散装,02正包)

    private String newProductFlag;//新品标识
    @SuppressWarnings("unused")
    private boolean isNewProduct;//是否新品

    private boolean isPromotionProduct;//是否促销

    private String storageCondition;//存储条件
    private String commodityDesc;//描述(说明)

    private String supplyStartTime; //供货开始时间
    private String supplyEndTime;//供货截止时间

    private BigDecimal referencePrice;//参考零售价

    private BigDecimal saleRation;

    private Integer logisticsModel;//物流模式

    private BigDecimal salesBoxCapacity;

    private BigDecimal stockQuantity; //现有库存

    private BigDecimal suggestedQuantity; //建议订货量

    private String commodityUnit;

    private BigDecimal retailPrice;//门店零售价

    private  Integer isWeight;
    private BigDecimal onlineQuantity;//在途数量
    private String inStorageDate;//第一次入库日期
    private BigDecimal cartNum;//购物车数量
    private Long shopId;

    private String barCode;

    public Long getShopId() {
        return shopId;
    }

    public void setShopId(Long shopId) {
        this.shopId = shopId;
    }

    public Integer getIsWeight() {
        return isWeight;
    }

    public void setIsWeight(Integer isWeight) {
        this.isWeight = isWeight;
    }

    public BigDecimal getCartNum() {
        return cartNum;
    }

    public void setCartNum(BigDecimal cartNum) {
        this.cartNum = cartNum;
    }

    public BigDecimal getOnlineQuantity() {
        return onlineQuantity;
    }

    public void setOnlineQuantity(BigDecimal onlineQuantity) {
        this.onlineQuantity = onlineQuantity;
    }

    public String getInStorageDate() {
        return inStorageDate;
    }

    public void setInStorageDate(String inStorageDate) {
        this.inStorageDate = inStorageDate;
    }

    public String getCommodityUnit() {
        return commodityUnit;
    }

    public void setCommodityUnit(String commodityUnit) {
        this.commodityUnit = commodityUnit;
    }

    public String getCommodityName() {
        return commodityName;
    }

    public void setCommodityName(String commodityName) {
        this.commodityName = commodityName;
    }

    public String getCommoditySpec() {
        return commoditySpec;
    }

    public void setCommoditySpec(String commoditySpec) {
        this.commoditySpec = commoditySpec;
    }

    public BigDecimal getCommodityNumber() {
        return commodityNumber==null? BigDecimal.ZERO :commodityNumber;
    }

    public void setCommodityNumber(BigDecimal commodityNumber) {
        this.commodityNumber = commodityNumber;
    }

    public void setCommodityNumber(String commodityNumber){
        this.commodityNumber = new BigDecimal(commodityNumber);
    }

    public BigDecimal getCommodityNumberLimit() {
        return commodityNumberLimit == null ? BigDecimal.ZERO: commodityNumberLimit ;
    }

    public void setCommodityNumberLimit(BigDecimal commodityNumberLimit) {
        this.commodityNumberLimit = commodityNumberLimit;
    }

    public BigDecimal getCommodityPrice() {
        return commodityPrice;
    }

    public void setCommodityPrice(BigDecimal commodityPrice) {
        this.commodityPrice = commodityPrice;
    }

    public String getCommodityId() {
        return commodityId;
    }

    public void setCommodityId(String commodityId) {
        this.commodityId = commodityId;
    }

    public String getCommodityCode() {
        return commodityCode;
    }

    public void setCommodityCode(String commodityCode) {
        this.commodityCode = commodityCode;
    }

    public Integer getCommodityType() {
        return commodityType;
    }

    public void setCommodityType(Integer commodityType) {
        this.commodityType = commodityType;
    }


    public String getCommodityRemark() {
        return commodityRemark;
    }

    public void setCommodityRemark(String commodityRemark) {
        this.commodityRemark = commodityRemark;
    }

    public String getCategoryName() {
        return categoryName;
    }

    public void setCategoryName(String categoryName) {
        this.categoryName = categoryName;
    }

    public Integer getCommodityState() {
        return commodityState;
    }

    public void setCommodityState(Integer commodityState) {
        this.commodityState = commodityState;
    }

    public boolean isEdit() {
        return edit;
    }

    public void setEdit(boolean edit) {
        this.edit = edit;
    }

    public Integer getFrozen() {
        if( null == this.frozen){
            return  0 ;
        }
        return frozen;
    }

    public void setFrozen(Integer frozen) {
        this.frozen = frozen;
    }

    public String getPackedType() {
        if(StringUtils.isEmpty(this.packedType)){
            return "01";
        }
        return packedType;
    }

    public void setPackedType(String packedType) {
        this.packedType = packedType;
    }

    public String getStorageCondition() {
        return storageCondition;
    }

    public void setStorageCondition(String storageCondition) {
        this.storageCondition = storageCondition;
    }

    public String getCommodityDesc() {
        if(StringUtils.isEmpty(this.commodityDesc)){
            return "";
        }
        return commodityDesc;
    }

    public void setCommodityDesc(String commodityDesc) {
        this.commodityDesc = commodityDesc;
    }

    public String getSupplyStartTime() {
        return supplyStartTime;
    }

    public void setSupplyStartTime(String supplyStartTime) {
        this.supplyStartTime = supplyStartTime;
    }

    public String getSupplyEndTime() {
        return supplyEndTime;
    }

    public void setSupplyEndTime(String supplyEndTime) {
        this.supplyEndTime = supplyEndTime;
    }

    public BigDecimal getReferencePrice() {
        return referencePrice;
    }

    public void setReferencePrice(BigDecimal referencePrice) {
        this.referencePrice = referencePrice;
    }

    public String getNewProductFlag() {
        return newProductFlag;
    }

    public void setNewProductFlag(String newProductFlag) {
        this.newProductFlag = newProductFlag;
    }

    public boolean isNewProduct() {
        if(StringUtils.isEmpty(this.newProductFlag)){
            return false;
        }
        if("01".equals(this.newProductFlag)){
            return true;
        }
        return false;
    }

    public boolean isPromotionProduct() {
        return isPromotionProduct;
    }

    public void setPromotionProduct(boolean isPromotionProduct) {
        this.isPromotionProduct = isPromotionProduct;
    }

    public BigDecimal getSaleRation() {
        return saleRation;
    }

    public void setSaleRation(BigDecimal saleRation) {
        this.saleRation = saleRation;
    }

    public Integer getLogisticsModel() {
        return logisticsModel;
    }

    public void setLogisticsModel(Integer logisticsModel) {
        this.logisticsModel = logisticsModel;
    }

    public BigDecimal getSalesBoxCapacity() {
        return salesBoxCapacity;
    }

    public void setSalesBoxCapacity(BigDecimal salesBoxCapacity) {
        this.salesBoxCapacity = salesBoxCapacity;
    }

    public BigDecimal getStockQuantity() {
        return stockQuantity;
    }

    public void setStockQuantity(BigDecimal stockQuantity) {
        this.stockQuantity = stockQuantity;
    }

    public BigDecimal getSuggestedQuantity() {
        return suggestedQuantity;
    }

    public void setSuggestedQuantity(BigDecimal suggestedQuantity) {
        this.suggestedQuantity = suggestedQuantity;
    }

    public BigDecimal getRetailPrice() {
        return retailPrice;
    }

    public void setRetailPrice(BigDecimal retailPrice) {
        this.retailPrice = retailPrice;
    }

    public String getBarCode() {
        return barCode;
    }

    public void setBarCode(String barCode) {
        this.barCode = barCode;
    }
}

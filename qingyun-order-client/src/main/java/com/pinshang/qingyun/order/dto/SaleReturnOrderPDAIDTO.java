package com.pinshang.qingyun.order.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel
public class SaleReturnOrderPDAIDTO {
    @ApiModelProperty("退货日期 开始日期 yyyy-MM-dd")
    private String startDate;
    @ApiModelProperty("退货日期 结束日期 yyyy-MM-dd")
    private String endDate;
    @ApiModelProperty("客户ID / 车间ID")
    private Long storeId;
    @ApiModelProperty("仓库ID")
    private Long warehouseId;
    @ApiModelProperty(hidden = true)
    private Long receiverId;
    @ApiModelProperty("业务类型: 1.门店退货 2.领料退货")
    private Integer bizType;
}

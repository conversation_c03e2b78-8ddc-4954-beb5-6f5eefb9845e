package com.pinshang.qingyun.order.dto.order;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * Created by crell on 2017/11/6.
 */
@Data
public class SaleReturnODTO {

    private String id;

    private Integer logisticsModel;

    private String orderCode;

    private String supplierName;

    private Integer status;

    private BigDecimal returnAmount;

    private String createName;
    
    private Date createTime;

    private String ShopName;

    private String updateName;

    private Date updateTime;

    private Integer categoryNum;

    private String stallName;
}

package com.pinshang.qingyun.order.service;

import com.pinshang.qingyun.base.configure.FeignClientConfiguration;
import com.pinshang.qingyun.base.constant.ApplicationNameConstant;
import com.pinshang.qingyun.order.dto.order.CompensateOrderQuantityReqODTO;
import com.pinshang.qingyun.order.hystrix.OrderJobClientHystrix;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/07/01 17:46
 */
@FeignClient(value = ApplicationNameConstant.QINGYUN_ORDER_SERVICE, fallbackFactory = OrderJobClientHystrix.class, configuration = FeignClientConfiguration.class)
public interface OrderJobClient {

    @RequestMapping(value = "/orderJob/compensateOrderQuantity",method = RequestMethod.POST)
    List<Long> compensateOrderQuantity(@RequestBody CompensateOrderQuantityReqODTO odto);

    /**
     * 统计门店在途数量
     * @return
     */
    @RequestMapping(value = "/orderShelves/countShopOrderedQuantity",method = RequestMethod.POST)
    Boolean countShopOrderedQuantity();

    /**
     * 汇总日日鲜商品昨日下单数量(job调用)
     * @return
     */
    @RequestMapping(value = "/orderShelves/commodityFreshOrderSummary",method = RequestMethod.POST)
    Boolean commodityFreshOrderSummary(@RequestParam("orderTime") String orderTime);


    /**
     * 计算排面推荐数量并且加入购物车
     * @param hhmm
     * @return
     */
    @RequestMapping(value = "/orderShelves/shelvesRecommendAddShoppingCart",method = RequestMethod.POST)
    Boolean shelvesRecommendAddShoppingCart(@RequestParam("hhmm") String hhmm);

    /**
     * chon
     * @return
     */
    @RequestMapping(value = "/orderJob/amountFlowComp",method = RequestMethod.POST)
    Boolean amountFlowComp();

    /**
     * 鲜达取消订单金额对比
     * @param dateTime
     * @return
     */
    @RequestMapping(value = "/orderJob/cancelOrderAmountCompare",method = RequestMethod.POST)
    Boolean cancelOrderAmountCompare(@RequestParam("timeStamp") String timeStamp);

}

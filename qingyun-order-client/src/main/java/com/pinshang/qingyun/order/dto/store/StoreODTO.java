package com.pinshang.qingyun.order.dto.store;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@Data
public class StoreODTO {
    @ApiModelProperty(value = "店铺编号")
    private String storeCode;
    @ApiModelProperty(value = "店铺名称 ")
    private String storeName;
    @ApiModelProperty(value = "店铺助记码 ")
    private String storeAid;
    @ApiModelProperty(value = "旧代码")
    private String storeOldCode;
    @ApiModelProperty(value = "描述")
    private String storeDestribe;
    @ApiModelProperty(value = "店铺类型_id ")
    private Long storeTypeId;
    @ApiModelProperty(value = "店铺等级_id")
    private Long storeLevelId;
    @ApiModelProperty(value = "商铺类别_id")
    private Long storeCategoryId;
    @ApiModelProperty(value = "状态(0启用,1停用) ")
    private Integer storeStatus;
    @ApiModelProperty(value = "开店时间 ")
    private Date storeOpenTime;
    @ApiModelProperty(value = "关店时间")
    private Date storeCloseTime;
    @ApiModelProperty(value = "所属公司ID")
    private Long storeCompanyId;
    @ApiModelProperty(value = "所属区域ID")
    private Long storeDistrictId;
    @ApiModelProperty(value = "结账客户ID")
    private Long settlementCustomerId;
    @ApiModelProperty(value = "渠道ID")
    private Long storeChannelId;
    @ApiModelProperty(value = "线路ID")
    private Long storeLineId;
    @ApiModelProperty(value = "下浮率（折扣率）")
    private BigDecimal lowerRate;
    @ApiModelProperty(value = "是否预收 ")
    private Integer storeIsPrepay;
    @ApiModelProperty(value = "是否开票")
    private Integer storeIsBilling;
    @ApiModelProperty(value = "送货员 ")
    private Long deliverymanId;
    @ApiModelProperty(value = "销售员")
    private Long salesmanId;
    @ApiModelProperty(value = "督导ID ")
    private Long supervisorId;
    @ApiModelProperty(value = "办事处主任ID")
    private Long officeDirectorId;
    @ApiModelProperty(value = "押金")
    private BigDecimal deposit;
    @ApiModelProperty(value = "大区经理ID")
    private Long regionManagerId;
    @ApiModelProperty(value = "是否提醒 ")
    private Integer storeIsAlert;
    @ApiModelProperty(value = "订货结束是否短信提示")
    private Integer orderCloseIsAlert;
    @ApiModelProperty(value = "订货方式")
    private String orderMethod;
    @ApiModelProperty(value = "第一次提醒司机")
    private String firstAlertTime;
    @ApiModelProperty(value = "提醒时间间隔")
    private String alertInterval;
    @ApiModelProperty(value = "提醒次数")
    private Integer alertTimes;
    @ApiModelProperty(value = "停止手机订货时间 (控制手机app的停止订货时间)")
    private String stopMonileTime;
    @ApiModelProperty(value = "网上订货时间")
    private String netOrderTime;
    @ApiModelProperty(value = "电话停止订货")
    private String telStopOrder;
    @ApiModelProperty(value = "未订货备注")
    private String notOrderRemark;
    @ApiModelProperty(value = "打印份数 (客户要求的小单子份数) ")
    private BigDecimal printCopies;
    @ApiModelProperty(value = "打印备注 ")
    private String printBackup;
    @ApiModelProperty(value = "打印送货单标题")
    private String printOrderTitle;
    @ApiModelProperty(value = "是否显示单价 ")
    private Integer showPriceRetail;
    @ApiModelProperty(value = "是否显示金额")
    private Integer moneyIsShow;
    @ApiModelProperty(value = "是否送货单加上签字")
    private Integer orderSignIn;
    @ApiModelProperty(value = "当日手机最大订单量")
    private BigDecimal maxMobileOrder;
    @ApiModelProperty(value = "当日订单最大订单量")
    private BigDecimal maxOrder;
    @ApiModelProperty(value = "国家ID ")
    private Long countryId;
    @ApiModelProperty(value = "省ID ")
    private Long provinceId;
    @ApiModelProperty(value = "市ID")
    private Long cityId;
    @ApiModelProperty(value = "区ID")
    private Long areaId;
    @ApiModelProperty(value = "详细地址*")
    private String detailAddress;
    @ApiModelProperty(value = "商圈ID ")
    private Long businessCirclesId;
    @ApiModelProperty(value = "门店地址")
    private String storeAddress;
    @ApiModelProperty(value = "送货地址 ")
    private String deliveryAddress;
    @ApiModelProperty(value = "联系人")
    private String storeLinkman;
    @ApiModelProperty(value = "电话")
    private String linkmanTel;
    @ApiModelProperty(value = "手机")
    private String linkmanMobile;
    @ApiModelProperty(value = "传真 ")
    private String linkmanFax;
    @ApiModelProperty(value = "邮箱")
    private String linkmanEmail;
    @ApiModelProperty(value = "收货时间")
    private String receiveTime;
    @ApiModelProperty(value = "检验报告")
    private Integer isTestReport;
    @ApiModelProperty(value = "检验报告备注")
    private String testReportRemark;
    @ApiModelProperty(value = "是否领用耗材 ")
    private Integer isReceiveConsumables;
    @ApiModelProperty(value = "金蝶接口")
    private String kingdeeInterface;
    @ApiModelProperty(value = "是否速冻")
    private Integer isQuickFreeze;
    @ApiModelProperty(value = "速冻数量 ")
    private BigDecimal quickFreezeAmount;
    @ApiModelProperty(value = "打印批次_id")
    private Long printDeliveryBatch;
    @ApiModelProperty(value = "结账备注")
    private String settlementRemark;
    @ApiModelProperty(value = "顺序")
    private Integer printDeliveryQueue;
    @ApiModelProperty(value = "创建该客户的用户ID")
    private Long createUserId;
    @ApiModelProperty(value = "是否已开启结账方式 默认为启用-1 其余0")
    private Long settlementStatus;

    private Date createTime;
    private Date updateTime;
    private Long id;

    private String idStr;
    private String storeCodeAndName;

    /**客户关联门店信息**/
    private Long shopId;
    private String shopCode;
}

package com.pinshang.qingyun.order.service;

import com.github.pagehelper.PageInfo;
import com.pinshang.qingyun.base.configure.FeignClientConfiguration;
import com.pinshang.qingyun.base.constant.ApplicationNameConstant;
import com.pinshang.qingyun.order.dto.CloudOrderAllocationResultOrderODTO;
import com.pinshang.qingyun.order.dto.CloudOrderAllocationSaveIDTO;
import com.pinshang.qingyun.order.dto.ShopOrderedQuantityODTO;
import com.pinshang.qingyun.order.dto.ShopOrderedQuantityQueryIDTO;
import com.pinshang.qingyun.order.dto.commodity.*;
import com.pinshang.qingyun.order.dto.order.*;
import com.pinshang.qingyun.order.dto.order.OrderListInfoODTO;
import com.pinshang.qingyun.order.dto.shop.OutstandingShopIDTO;
import com.pinshang.qingyun.order.dto.shop.OutstandingShopODTO;
import com.pinshang.qingyun.order.dto.shop.ShopOrderIDTO;
import com.pinshang.qingyun.order.dto.sync.SyncOrderListIDTO;
import com.pinshang.qingyun.order.dto.sync.SyncOrderListODTO;
import com.pinshang.qingyun.order.dto.sync.SyncOrderODTO;
import com.pinshang.qingyun.order.hystrix.OrderClientHystrix;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import java.util.Date;
import java.util.List;

@FeignClient(value = ApplicationNameConstant.QINGYUN_ORDER_SERVICE, fallbackFactory = OrderClientHystrix.class, configuration = FeignClientConfiguration.class)
public interface OrderClient{

	//获取订单列表列表->带条件查询
	@RequestMapping(value = "/order/findOrderListByPage", method = RequestMethod.POST)
	PageInfo<OrderListODTO>  findOrderListByPage(@RequestBody OrderListIDto orderListIDto);

	//获取预置订单列表列表->带条件查询
	@RequestMapping(value = "/order/findPreOrderListByPage", method = RequestMethod.POST)
	PageInfo<OrderListODTO>  findPreOrderListByPage(@RequestBody OrderListIDto orderListIDto);

	@RequestMapping(value = "/order/orderCancelById/{orderId}/{isInternal}", method = RequestMethod.GET)
	boolean orderCancelById(@PathVariable("orderId") Long orderId, @PathVariable("isInternal") Boolean isInternal, @RequestParam("userId") Long userId, @RequestParam("realName") String realName);

	@RequestMapping(value = "/order/preOrderCancelByOrderId/{orderId}/{isInternal}", method = RequestMethod.GET)
	boolean preOrderCancelByOrderId(@PathVariable("orderId") Long orderId, @PathVariable("isInternal") Boolean isInternal, @RequestParam("userId") Long userId);

	@RequestMapping(value = "/order/copyOrderById/{orderId}/{isInternal}", method = RequestMethod.GET)
	boolean copyOrderById(@PathVariable("orderId") Long orderId, @PathVariable("isInternal") Boolean isInternal);

	@RequestMapping(value = "/order/copyPreOrderById/{orderId}/{isInternal}", method = RequestMethod.GET)
	boolean copyPreOrderById(@PathVariable("orderId") Long orderId, @PathVariable("isInternal") Boolean isInternal);

	@RequestMapping(value = "/order/createOrder", method = RequestMethod.POST)
    public CreateOrderODTO createOrder(@RequestBody List<CreateOrderIDTO> vos);

	@RequestMapping(value = "/order/findStoreOrderTimeByStoreId/{storeId}", method = RequestMethod.GET)
	public String findStoreOrderTimeByStoreId(@PathVariable("storeId") String storeId);

	@RequestMapping(value = "/order/createShopOrder", method = RequestMethod.POST)
	boolean createShopOrder(@RequestBody ShopOrderIDTO shopOrderIDTO);

	@RequestMapping(value = "/order/preOrderCheckSuucess", method = RequestMethod.POST)
	boolean preOrderCheckSuucess(@RequestBody PreSaveOrderIDTO vo);

	@RequestMapping(value = "/order/generatePurchaseOrder", method = RequestMethod.POST)
	boolean generatePurchaseOrder();

	@RequestMapping(value = "/order/createPurchaseOrderByTime", method = RequestMethod.GET)
	boolean createPurchaseOrderByTime(@RequestParam("times")String times);

	@RequestMapping(value = "/order/outstanding" ,method = RequestMethod.POST)
	PageInfo<OutstandingShopODTO> outstandingStore(@RequestBody OutstandingShopIDTO vo);

	@RequestMapping(value ="/order/queryModifyDeliverDateList", method = RequestMethod.POST)
    PageInfo<ModifyDeliverDateODTO> queryModifyDeliverDateList(ModifyDeliverDateIDTO dto);

	@RequestMapping(value ="/order/modifyDeliverDate", method = RequestMethod.PUT)
	void modifyDeliverDate(@RequestParam("orderCode") String orderCode, @RequestParam("date") String date);

	@RequestMapping(value = "/order/createDirectSendingBackOrder", method = RequestMethod.POST)
	boolean createDirectSendingBackOrder(DirectSendingBackOrderIDTO idto);

	@RequestMapping(value ="/order/fixedSettleOrder", method = RequestMethod.GET)
	void fixedSettleOrder(@RequestParam("sourceCode") String sourceCode, @RequestParam("sourceType") String sourceType,@RequestParam("topic")String topic);

	//根据购物车ID获取购物车详细信息
	@RequestMapping(value ="/order/findShoppingCartItems", method = RequestMethod.GET)
    List<CreateOrderIDTO.CreateOrderItemIDTO> findShoppingCartItems(@RequestParam("storeId")Long storeId,@RequestParam("shoppingCartId") Long shoppingCartId);
	//(手持)端提交订单
	@RequestMapping(value = "/order/createHandleOrder", method = RequestMethod.POST)
	CreateOrderODTO createHandleOrder(CreateHandleOrderIDTO createHandleOrderIDTO);

	@RequestMapping(value ="/order/orderNotCommitTips", method = RequestMethod.GET)
	void orderNotCommitTips();

	/**
	 * 查询订单日志
	 * @param orderId
	 * @return
	 */
	@RequestMapping(value = "/order/logs/{orderId}", method = RequestMethod.GET)
    List<OrderLogListODTO> getOrderLogList(@PathVariable("orderId")Long orderId);

	@RequestMapping(value = "/order/createOrderYear", method = RequestMethod.POST)
	void createOrderYear();

	@RequestMapping(value = "/order/createGrouponOrder", method = RequestMethod.POST)
	Boolean createGrouponOrder(@RequestParam("timeStamp") String timeStamp);

	/**
	 *  B 端团购订单日汇总
	 * @param timeStamp
	 * @return
	 */
	@RequestMapping(value = "/grouponOrder/grouponOrderDay", method = RequestMethod.POST)
	Boolean grouponOrderDay(@RequestParam("timeStamp") String timeStamp);

	@RequestMapping(value = "/order/stockOutSettleMsg", method = RequestMethod.POST)
	Boolean stockOutSettleMsg(@RequestParam("beginTime") String beginTime, @RequestParam("endTime") String endTime);
	@PostMapping(value = "/order/getSumRealQuantity")
	Double getSumRealQuantity(@RequestParam("subOrderId")Long subOrderId);

	/****
	 * 场景：
	 * 修改客户的所属公司后，需将改该客户下的订单修改成对应所属公司
	 * 修改订单的条件为：客户下的订单、订单送货时间>当前时间、订单所属公司不等于客户所属公司
	 * @return
	 */
	@RequestMapping(value = "/order/modifyOrderCompany", method = RequestMethod.POST)
	Integer modifyOrderCompanyByStore(@RequestParam("storeId") Long storeId,@RequestParam("companyId") Long companyId);

	/**
	 * 云超配货单分配客户提交订单
	 * @param list
	 * @return
	 */
	@RequestMapping(value = "/order/cloudOrderAllocationCreateOrder", method = RequestMethod.POST)
	List<CloudOrderAllocationResultOrderODTO> cloudOrderAllocationCreateOrder(@RequestBody List<CloudOrderAllocationSaveIDTO> list);

	/**
	 * 根据订货日期、日日鲜商品idList查询在途数量
	 *
	 * @param idto
	 * @return
	 */
	@RequestMapping(value = "/orderShelves/queryShopOrderedQuantity", method = RequestMethod.POST)
	List<ShopOrderedQuantityODTO> queryShopOrderedQuantity(@RequestBody ShopOrderedQuantityQueryIDTO idto);

	/**
	 * 同步B端订单到大仓
	 *
	 * @param orderId
	 * @return
	 */
	@RequestMapping(value = "/order/api/getOrderById", method = RequestMethod.POST)
	OrderODTO getOrderById(@RequestParam("orderId") Long orderId);


    /***
     * 根据订单查询订单明细
     * @param vo
     */
    @PostMapping(value = "/order/selectOrderListGiftByOrderIdAndCommodityIdList")
    List<OrderListInfoODTO> selectOrderListGiftByOrderIdAndCommodityIdList(@RequestBody OrderListOrderIdAndCommIDTO vo);

	/**
	 * 代销香烟
	 * 是否存在历史数据待收货的代销订单或者待确认的代销退货单(pos用)
	 * @param shopId
	 * @return  true 存在  false 不存在
	 */
	@RequestMapping(value ="/apiExToken/existConsignmentOrder", method = RequestMethod.POST)
	Boolean existConsignmentOrder(@RequestParam("shopId") Long shopId);

	@RequestMapping(value = "/order/api/selectOrderListGiftJob", method = RequestMethod.POST)
	Long selectOrderListGiftJob(@RequestBody OrderReportJobIDTO vo);

	@RequestMapping(value = "/orderJob/syncOrderList", method = RequestMethod.POST)
	List<SyncOrderODTO> syncOrderList(@RequestBody SyncOrderListIDTO syncOrderListIDTO);

	@RequestMapping(value = "/order/api/selectTdaKfDeliveryTimeRangeList", method = RequestMethod.POST)
	List<TdaDeliveryTimeRangeODTO> selectTdaDeliveryTimeRangeList(@RequestParam("orderTime") String orderTime,
																  @RequestParam("storeId") Long storeId);

	/**
	 * 优惠券日统计-查询已核销数量、已核销订单总金额、折扣总金额
	 * @param beginTime
	 * @param endTime
	 * @return
	 */
	@RequestMapping(value = "/order/queryOrderCouponList", method = RequestMethod.GET)
	List<MtCouponDayStatisticsODTO> queryOrderCouponList(@RequestParam("beginTime") String beginTime, @RequestParam("endTime") String endTime);

	/**
	 * 客服订单导入保存
	 * @param orderImportSaveIDTO
	 * @return
	 */
	@RequestMapping(value = "/orderCupImport/importSave", method = RequestMethod.POST)
	OrderImportSaveResponse importSave(@RequestBody OrderImportSaveIDTO orderImportSaveIDTO);


	/**
	 * 小鹅通推送订单，创建B端订单
	 */
	@RequestMapping(value = "/order/createXiaoeTongOrder", method = RequestMethod.POST)
	List<XiaoeTongPushOrderIDTO> createXiaoeTongOrder(@RequestBody List<XiaoeTongPushOrderIDTO> xiaoeTongPushOrderList);

	@RequestMapping(value = "/orderJob/selectOrderListByBusinessTypeIsNullOrLogisticsCenterIsNull", method = RequestMethod.GET)
	List<SyncOrderODTO> selectOrderListByBusinessTypeIsNullOrLogisticsCenterIsNull(@RequestParam("beginTime") String beginTime, @RequestParam("endTime") String endTime);

	/**
	 * 1. 计划销售订单，顺丰通知清美入库后，大仓修改订单状态为已完成
	 * 2. B端全国订单，订单进大仓后，大仓修改订单状态为出库中
	 * 3. B端全国订单，顺丰审核失败，大仓发起取消订单后，修改订单状态为已取消
	 * @param updateOrderIDTO
	 * @return
	 */
	@RequestMapping(value = "/order/updateOrderStatus", method = RequestMethod.POST)
	Boolean updateOrderStatus(@RequestBody UpdateOrderIDTO updateOrderIDTO);
}

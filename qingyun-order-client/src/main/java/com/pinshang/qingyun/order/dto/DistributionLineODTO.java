package com.pinshang.qingyun.order.dto;

import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2018/10/12 9:57.
 * @blog http://linuxsogood.org
 */
@Data
public class DistributionLineODTO {

    private Long id;

    /** 线路编码 **/
    private String lineCode;
    /**线路名称 **/
    private String lineName;
    /**送货员id **/
    private Long deliveryManId;

    /**送货员 **/
    private String deliveryMan;
    /**发货时间id **/
    private Long deliveryTimeId;
    /**发货时间 **/
    private String deliveryTime;
    /**发货仓库Id**/
    private Long deliveryWarehouseId;
    /**发货仓库 **/
    private String deliveryWarehouse;
    /**车位id **/
    private Long carportId;
    /**车位名称**/
    private String carportName;
    /**创建人 **/
    private Long createId;
    /**线路状态：0停用，1启用**/
    private Integer lineStatus;

    /** 新立成中线路和送货员一对多,新清美中一对一.同步的时候就出现问题,为了解决这种同步问题 **/
    private String lineOldCode;

    /**客户ID**/
    private Long storeId;

    private Date createTime;

    private Date updateTime;

}

package com.pinshang.qingyun.order.dto.commodity;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class CommodityListHandleODto {
	@ApiModelProperty("商品ID")
	private String commodityId;

	@ApiModelProperty("商品编码")
	private String commodityCode;

	@ApiModelProperty("商品名称")
	private String commodityName;

	@ApiModelProperty("规格")
    private String commoditySpec;

	@ApiModelProperty("速冻 1:是 0：否")
    private Integer frozen;//速冻

	@ApiModelProperty("是否新品")
	private boolean isNewProduct;//是否新品

	@ApiModelProperty("是否促销")
    private boolean isPromotionProduct;//是否促销

	@ApiModelProperty("条形码")
	private String barCode;

	@ApiModelProperty("是否自动订货")
	private Boolean autoCommodity;

	private BigDecimal boxCapacity;

	/**
	 * 1 当前时间可订货的商品——在订货时间段内，且有库存（商品库存依据且有库存的商品、不限量供应的商品、限量供应且有余量的商品）
	 * 2 已抢光的商品——商品库存依据且库存≤0或限量供应且已满限量的商品，标记“已抢光”效果。
	 * 3 当前时间不在订货时间段的商品
	 */
	private Integer orderedStatus;
}

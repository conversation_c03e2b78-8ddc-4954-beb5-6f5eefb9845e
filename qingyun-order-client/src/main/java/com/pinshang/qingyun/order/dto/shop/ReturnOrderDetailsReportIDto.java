package com.pinshang.qingyun.order.dto.shop;

import com.pinshang.qingyun.order.dto.Pagination;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper=false)
public class ReturnOrderDetailsReportIDto extends Pagination{
	/**
	 * 
	 */
	private static final long serialVersionUID = 260094569057060684L;
	//private Date createTime;
	@ApiModelProperty("退单编号")
	private String orderCode;
	
	@ApiModelProperty("一级分类id")
	private Long commodityFirstId;
	
	//private String cateName;
	
	@ApiModelProperty("条码")
	private String barCode;
	//private String commodityCode;
	//private String commodityName;
	//private String commoditySpec;
	//private BigDecimal price;
	//private BigDecimal returnQuantity;
	//private BigDecimal realReturnQuantity;
	
	@ApiModelProperty("门店（只用总部查询接口生效）")
	private Long shopId;
	
	//private Long storeId;
	
	@ApiModelProperty("退货日期开始时间 格式  yyyy-MM-dd")
	private String orderTimeStartStr;
	@ApiModelProperty("退货日期结束时间 格式  yyyy-MM-dd")
	private String orderTimeEndStr;
	//private Date orderTimeStart;
	//private Date orderTimeEnd;
	@ApiModelProperty("编码/名称/助记码")
	private String searchWord;

	@ApiModelProperty("退货原因下拉框:1=质量问题、2=已过保质期、3=包装破损、4=条码不符、5=其他")
	private Integer returnReason;

	//代销商id
	private Long consignmentId;
	private Long stallId;
}

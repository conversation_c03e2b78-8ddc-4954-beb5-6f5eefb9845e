package com.pinshang.qingyun.order.hystrix;

import com.pinshang.qingyun.order.service.XDAShoppingCartClient;
import feign.hystrix.FallbackFactory;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;

@Component
public class XDAShoppingCartClientHystrix implements FallbackFactory<XDAShoppingCartClient> {
	private Logger logger = LoggerFactory.getLogger(getClass());
	
	@Override
	public XDAShoppingCartClient create(Throwable throwable) {
		return new XDAShoppingCartClient() {


			@Override
			public Integer getCategoryNum(String orderDate, Long storeId) {
				return null;
			}

			@Override
			public Integer getCategoryNumV2(String orderDate, Long storeId) {
				return null;
			}

			@Override
			public Integer getCategoryNumV4(String orderDate, Long storeId) {
				return 0;
			}

			@Override
			public BigDecimal getNormalGroupAmountV2(String orderDate, Long storeId) {
				return null;
			}

			@Override
			public BigDecimal getNormalGroupAmountV3(String orderDate, Long storeId) {
				return null;
			}

			@Override
			public BigDecimal getNormalGroupAmountV4(String orderDate, Long storeId, Long logisticsCenterId) {
				return null;
			}
		};
	}
}

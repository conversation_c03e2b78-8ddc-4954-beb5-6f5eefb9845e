package com.pinshang.qingyun.order.dto.commodity;

import io.swagger.annotations.ApiModelProperty;

import java.math.BigDecimal;

/*
 * 添加购物车
 */
public class ShoppingCartIDTO {

	@ApiModelProperty("商品ID")
	private Long commodityId;
	private Long storeId;
	private Long enterpriseId;
	//删除shoppingCart整个
	@ApiModelProperty("购物车主ID")
	private Long shoppingCartId;
	//删除shoppingCartItem
	@ApiModelProperty("购物车明细ID")
	private Long shoppingCartItemId;
	
	//数量
	@ApiModelProperty("数量")
	private BigDecimal quantity = BigDecimal.ZERO;
	
	private Long createId;

	/**
	 * 是否内置用户
	 */
	private Boolean isInternal;
	private Integer orderType;
	/** 档口id */
	private Long stallId;

	public Long getStallId() {
		return stallId;
	}

	public void setStallId(Long stallId) {
		this.stallId = stallId;
	}

	public Integer getOrderType() {
		return orderType;
	}

	public void setOrderType(Integer orderType) {
		this.orderType = orderType;
	}

	public Long getCommodityId() {
		return commodityId;
	}
	public void setCommodityId(Long commodityId) {
		this.commodityId = commodityId;
	}
	public Long getStoreId() {
		return storeId;
	}
	public void setStoreId(Long storeId) {
		this.storeId = storeId;
	}
	public Long getEnterpriseId() {
		return enterpriseId;
	}
	public void setEnterpriseId(Long enterpriseId) {
		this.enterpriseId = enterpriseId;
	}
	public Long getCreateId() {
		return createId;
	}
	public void setCreateId(Long createId) {
		this.createId = createId;
	}
	public BigDecimal getQuantity() {
		return quantity;
	}
	public void setQuantity(BigDecimal quantity) {
		this.quantity = quantity;
	}
	public Long getShoppingCartId() {
		return shoppingCartId;
	}
	public void setShoppingCartId(Long shoppingCartId) {
		this.shoppingCartId = shoppingCartId;
	}
	public Long getShoppingCartItemId() {
		return shoppingCartItemId;
	}
	public void setShoppingCartItemId(Long shoppingCartItemId) {
		this.shoppingCartItemId = shoppingCartItemId;
	}

	public Boolean getInternal() {
		return isInternal;
	}

	public void setInternal(Boolean internal) {
		isInternal = internal;
	}
}

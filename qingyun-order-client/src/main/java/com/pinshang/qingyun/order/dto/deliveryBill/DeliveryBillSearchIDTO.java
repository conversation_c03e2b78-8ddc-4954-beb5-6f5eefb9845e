package com.pinshang.qingyun.order.dto.deliveryBill;

import com.pinshang.qingyun.order.dto.Pagination;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @description:
 * @author: hhf
 * @time: 2021/8/4 14:34
 */
@Data
public class DeliveryBillSearchIDTO extends Pagination {

    /**开始时间**/
    @ApiModelProperty("开始时间")
    private String orderStartTime;
    /**结束时间**/
    @ApiModelProperty("结束时间")
    private String orderEndTime;
    /**客户类型**/
    @ApiModelProperty("客户类型")
    private Long storeTypeId;
    /**线路**/
    @ApiModelProperty("线路")
    private Long lineId;
    /**客户**/
    @ApiModelProperty("客户")
    private Long storeId;
    /**订单编码**/
    @ApiModelProperty("订单编码")
    private String orderCode;
    /**送货员**/
    @ApiModelProperty("送货员")
    private Long deliverymanId;
}

package com.pinshang.qingyun.order.hystrix;

import java.util.List;
import java.util.Map;

import com.pinshang.qingyun.order.dto.commodity.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.RequestBody;

import com.github.pagehelper.PageInfo;
import com.pinshang.qingyun.order.service.ProductClient;

import feign.hystrix.FallbackFactory;

@Component
public class ProductClientHystrix  implements FallbackFactory<ProductClient> {
	private Logger logger = LoggerFactory.getLogger(getClass());

	@Override
	public ProductClient create(Throwable throwable) {
		return new ProductClient() {
			@Override
			public PageInfo<CommodityListODto> findCommodityListByPage(@RequestBody CommodityListIDto ommodityListIDto) {
				logger.error(throwable.getMessage());
				return null;
			}

			@Override
			public PageInfo<CommodityListHandleODto> findCommodityHandListPage(CommodityListHandleIDto commodityListHandleIDto) {
				return null;
			}

			@Override
			public CommodityHandleODTO findCommodityByBarcodeOrId(CommodityListHandleIDto commodityListHandleIDto) {
				return null;
			}

			@Override
			public List<PreCommodityODTO> findPreCommodityByParam(@RequestBody PreCommodityIDTO preCommodityIDTO) {
				logger.error(throwable.getMessage());
				return null;
			}

			@Override
			public Map<String, CommodityResultODTO> getToBPromotionPrice(String storeId) {
				return null;
			}

			@Override
			public CouponCodeQueryODTO couponQuery(String barCode, Long storeId) {
				return null;
			}

		};
	}
}

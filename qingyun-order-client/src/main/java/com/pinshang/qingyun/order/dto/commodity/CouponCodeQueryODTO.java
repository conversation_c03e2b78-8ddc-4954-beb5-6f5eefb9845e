package com.pinshang.qingyun.order.dto.commodity;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class CouponCodeQueryODTO {

    @ApiModelProperty("条形码")
    private  String barcode;

    private String commodityId;
    private String commodityCode;
    @ApiModelProperty("商品名称")
    private String commodityName;//商品名称

    @ApiModelProperty("规格")
    private String commoditySpec;//规格

    @ApiModelProperty("单位")
    private String commodityUnit;//单位

    @ApiModelProperty("门店零售价")
    private BigDecimal retailPrice;//门店零售价

    @ApiModelProperty("特价")
    private BigDecimal promotionPrice;//特价

    @ApiModelProperty("进货价")
    private BigDecimal commodityPrice;//进货价

    @ApiModelProperty("是否称重0-不称量,1-称重")
    private  Integer isWeight;//是否称重0-不称量,1-称重

    @ApiModelProperty("允许POS手动打折:1-是,0-否")
    private Integer posDiscount;//允许POS手动打折:1-是,0-否

    @ApiModelProperty("允许POS手动议价:1-是,0-否")
    private Integer posNegotiatedPrice;//允许POS手动议价:1-是,0-否

    @ApiModelProperty("折扣在yy折以下将低于进货价")
    private BigDecimal yy;

    @ApiModelProperty("是否可售:1-是,0-否")
    private Integer commoditySaleStatus;
}

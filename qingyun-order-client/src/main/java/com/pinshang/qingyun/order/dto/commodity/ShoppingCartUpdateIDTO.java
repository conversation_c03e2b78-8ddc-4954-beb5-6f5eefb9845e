package com.pinshang.qingyun.order.dto.commodity;

import io.swagger.annotations.ApiModelProperty;

import java.math.BigDecimal;
import java.util.List;

/**
 * @Author: sk
 * @Date: 2019/11/28
 */
public class ShoppingCartUpdateIDTO {

    private Long storeId;

    @ApiModelProperty("购物车主ID")
    private Long shoppingCartId;

    @ApiModelProperty("商品明细")
    private List<shoppingCartItem> itemList;

    public Long getStoreId() {
        return storeId;
    }

    public void setStoreId(Long storeId) {
        this.storeId = storeId;
    }

    public Long getShoppingCartId() {
        return shoppingCartId;
    }

    public void setShoppingCartId(Long shoppingCartId) {
        this.shoppingCartId = shoppingCartId;
    }

    public List<shoppingCartItem> getItemList() {
        return itemList;
    }

    public void setItemList(List<shoppingCartItem> itemList) {
        this.itemList = itemList;
    }

    public static class shoppingCartItem{

        @ApiModelProperty("商品ID")
        private Long commodityId;

        @ApiModelProperty("购物车明细ID")
        private Long shoppingCartItemId;

        @ApiModelProperty("数量")
        private BigDecimal quantity = BigDecimal.ZERO;

        public Long getCommodityId() {
            return commodityId;
        }

        public void setCommodityId(Long commodityId) {
            this.commodityId = commodityId;
        }

        public Long getShoppingCartItemId() {
            return shoppingCartItemId;
        }

        public void setShoppingCartItemId(Long shoppingCartItemId) {
            this.shoppingCartItemId = shoppingCartItemId;
        }

        public BigDecimal getQuantity() {
            return quantity;
        }

        public void setQuantity(BigDecimal quantity) {
            this.quantity = quantity;
        }
    }

}

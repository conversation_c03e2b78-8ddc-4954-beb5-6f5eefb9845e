package com.pinshang.qingyun.order.service;

import com.pinshang.qingyun.base.configure.FeignClientConfiguration;
import com.pinshang.qingyun.base.constant.ApplicationNameConstant;
import com.pinshang.qingyun.order.dto.commodity.SubOrderAndItemIDTO;
import com.pinshang.qingyun.order.dto.mock.MockOrderIDTO;
import com.pinshang.qingyun.order.hystrix.MockOrderClientHystrix;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

@FeignClient(value = ApplicationNameConstant.QINGYUN_ORDER_SERVICE, fallbackFactory = MockOrderClientHystrix.class,configuration = FeignClientConfiguration.class)
public interface MockOrderClient {

    @RequestMapping(value = "/mockOrder/mockCreateOrder", method = RequestMethod.POST)
    Long mockCreateOrder(@RequestBody MockOrderIDTO mockOrderIDTO);

}

package com.pinshang.qingyun.order.dto.commodity;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import lombok.ToString;

import java.util.List;

/**
 * summary.
 * <p>
 * detailed description
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2018/8/10
 */
@Data
@ToString
@AllArgsConstructor
@RequiredArgsConstructor
public class DirectSendingBackOrderIDTO {
    private Long storeId;
    private Long shopId;
    private Long enterpriseId;
    private Long userId;
    private Long supplierId;
    private Boolean isInternal;

    private List<DirectSendingBackOrderItemIDTO> items;
}

package com.pinshang.qingyun.order.service;

import com.pinshang.qingyun.base.configure.FeignClientConfiguration;
import com.pinshang.qingyun.base.constant.ApplicationNameConstant;
import com.pinshang.qingyun.order.dto.DictionaryQueryDCTpyeODTO;
import com.pinshang.qingyun.order.hystrix.OrderDictionaryClientHystrix;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

@FeignClient(value = ApplicationNameConstant.QINGYUN_ORDER_SERVICE, fallbackFactory = OrderDictionaryClientHystrix.class, configuration = FeignClientConfiguration.class)
public interface OrderDictionaryClient {

    @RequestMapping(value = "/dictionary/client/queryDirectDcConfig", method = RequestMethod.POST)
    DictionaryQueryDCTpyeODTO queryDirectDcConfig();

}

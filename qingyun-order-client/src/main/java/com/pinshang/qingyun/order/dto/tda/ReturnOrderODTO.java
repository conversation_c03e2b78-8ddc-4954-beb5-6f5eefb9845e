/*
 * tramy.cn Inc.
 * Copyright (c) 2021-2024 All Rights Reserved.
 */
package com.pinshang.qingyun.order.dto.tda;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>
 * 退货单
 * </p>
 *
 * <AUTHOR> shenyang
 * @version : 1.0.0
 * @history : modify history
 * <author>              <time>              <version>              <desc>
 * @since : 2024/05/31 14:50
 */
@Data
public class ReturnOrderODTO {
    /**
     * id
     */
    @ApiModelProperty(value = "id")
    private Long id;
    /**
     * 退货类型：1-客户退货，2-订单取消，3-配送失败
     */
    @ApiModelProperty(value = "退货类型：1-客户退货，2-订单取消，3-配送失败")
    private Integer returnType;
    /**
     * 退货类型：1-客户退货，2-订单取消，3-配送失败
     */
    @ApiModelProperty(value = "退货类型：1-客户退货，2-订单取消，3-配送失败")
    private String returnTypeName;
    /**
     * 退货来源：1-app客户申请，2-品鲜后台投诉，3-订单取消，4-订单配送失败
     */
    @ApiModelProperty(value = "退货来源：1-app客户申请，2-品鲜后台投诉，3-订单取消，4-订单配送失败")
    private Integer returnSource;
    /**
     * 客户id
     */
    @ApiModelProperty(value = "客户id")
    private Long storeId;
    /**
     * 客户编码
     */
    @ApiModelProperty(value = "客户编码")
    private String storeCode;
    /**
     * 客户名称
     */
    @ApiModelProperty(value = "客户名称")
    private String storeName;
    /**
     * 客户类型名称
     */
    @ApiModelProperty(value = "客户类型名称")
    private String storeTypeName;

    /**
     * 退货单编号，唯一标识每个退货单
     */
    @ApiModelProperty(value = "退货单号")
    private String returnOrderCode;

    /**
     * 退货单序列号，前端展示用
     */
    @ApiModelProperty(value = "退货单序列号，前端展示用")
    private String returnOrderSeq;

    /**
     * 来源订单id
     */
    @ApiModelProperty(value = "来源订单id")
    private Long sourceOrderId;

    /**
     * 来源单号，即相关的订单编号
     */
    @ApiModelProperty(value = "来源单号")
    private String sourceOrderCode;

    /**
     * 退货单状态：1-待审核、2-待取货、3-取货失败、4-待大仓确认、5-已取消、6-已完成、7-审核不通过
     */
    @ApiModelProperty(value = "状态:1-待审核、2-待取货、3-取货失败、4-待大仓确认、5-已取消、6-已完成、7-审核不通过")
    private Integer status;

    /**
     * 退货单状态描述：1-待审核、2-待取货、3-取货失败、4-待大仓确认、5-已取消、6-已完成、7-审核不通过
     */
    @ApiModelProperty(value = "状态描述:1-待审核、2-待取货、3-取货失败、4-待大仓确认、5-已取消、6-已完成、7-审核不通过")
    private String statusName;

    /**
     * 取货时间段
     */
    @ApiModelProperty(value = "取货时间段")
    private String pickUpTimeRange;

    /**
     * 取货日期
     */
    @ApiModelProperty(value = "取货日期")
    @JsonFormat(pattern="yyyy-MM-dd",timezone = "GMT+8")
    private Date pickUpTime;

    /**
     * 配送批次
     */
    @ApiModelProperty(value = "配送批次")
    private String deliveryBatch;

    /**
     * 配送批次名称
     */
    @ApiModelProperty(value = "配送批次名称")
    private String deliveryBatchName;

    /**
     * 手机号
     */
    @ApiModelProperty(value = "手机号")
    private String phone;

    /**
     * 物流中心id,pinshang_tms.t_tms_logistics_center.id
     */
    @ApiModelProperty(value = "物流中心id")
    private Long logisticsCenterId;

    /**
     * 退货订单类型：1-退货，2-差异（少货/多货）
     */
    private Integer returnOrderType;

    /**
     * 送货地址
     */
    @ApiModelProperty(value = "收货地址")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private String deliveryAddress;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date createTime;

    /**
     * 送货日期
     */
    @ApiModelProperty(value = "送货日期")
    @JsonFormat(pattern="yyyy-MM-dd",timezone = "GMT+8")
    private Date deliveryTime;

    /**
     * 送货/取货  完成时间
     */
    @ApiModelProperty(value = "取货完成时间")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date deliveryEndTime;

    /**
     * 业务类型：10-通达销售
     */
    private Integer businessType;

    @ApiModelProperty(value = "运单号")
    private String waybillCode;

    @ApiModelProperty(value = "司机ID")
    private Long driverId;

    @ApiModelProperty(value = "司机")
    private String driverName;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "确认时间")
    @JsonFormat(pattern="yyyy-MM-dd",timezone = "GMT+8")
    private Date confirmTime;

    @ApiModelProperty(value = "确认人ID")
    private Long confirmUserId;

    @ApiModelProperty(value = "审核时间")
    @JsonFormat(pattern="yyyy-MM-dd",timezone = "GMT+8")
    private Date checkTime;

    @ApiModelProperty(value = "审核人ID")
    private Long checkUserId;

    /**
     * 申请商品金额
     */
    private BigDecimal totalApplyMoney;

    /**
     * 审核商品金额
     */
    private BigDecimal totalCheckMoney;

}

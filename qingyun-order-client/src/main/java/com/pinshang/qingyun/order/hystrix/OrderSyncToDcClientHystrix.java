package com.pinshang.qingyun.order.hystrix;

import com.github.pagehelper.PageInfo;
import com.pinshang.qingyun.order.dto.order.*;
import com.pinshang.qingyun.order.service.OrderSyncToDcClient;
import feign.hystrix.FallbackFactory;
import org.springframework.stereotype.Component;
import java.util.List;

@Component
public class OrderSyncToDcClientHystrix implements FallbackFactory<OrderSyncToDcClient> {
	@Override
	public OrderSyncToDcClient create(Throwable throwable) {
		return new OrderSyncToDcClient() {

			@Override
			public List<OrderSyncToDcODTO> queryOrderSyncToDcList(OrderSyncToDcIDTO idto) {
				return null;
			}

			@Override
			public List<OrderODTO> getOrderListByIds(List<Long> orderIds) {
				return null;
			}

			@Override
			public List<OrderODTO> queryOrderListBySubOrderIds(List<Long> subOrderIds) {
				return null;
			}

			@Override
			public PageInfo<OrderSyncToDcODTO> queryOrderPage(OrderSyncToDcIDTO idto) {
				return null;
			}
		};
	}
}

package com.pinshang.qingyun.order.dto.shop;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * 商品组配送统计已生成采购单的预订单
 */
@Data
public class TjPreOrderIDTO {

    @ApiModelProperty("送货日期")
    private Date orderTime;

    @ApiModelProperty("客户ID")
    private List<Long> storeIds;

    @ApiModelProperty("商品ID")
    private List<Long> commodityIds;

    public TjPreOrderIDTO(Date orderTime, List<Long> storeIds, List<Long> commodityIds) {
        this.orderTime = orderTime;
        this.storeIds = storeIds;
        this.commodityIds = commodityIds;
    }
}

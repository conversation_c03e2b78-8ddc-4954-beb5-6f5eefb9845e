package com.pinshang.qingyun.order.service;

import com.pinshang.qingyun.base.configure.FeignClientConfiguration;
import com.pinshang.qingyun.base.constant.ApplicationNameConstant;
import com.pinshang.qingyun.order.dto.EmployeePrinterODTO;
import com.pinshang.qingyun.order.dto.StorePrinterODTO;
import com.pinshang.qingyun.order.dto.SystemPrinterODTO;
import com.pinshang.qingyun.order.hystrix.SystemPrinterClientHystrix;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import java.util.List;

@FeignClient(value = ApplicationNameConstant.QINGYUN_ORDER_SERVICE, fallbackFactory = SystemPrinterClientHystrix.class, configuration = FeignClientConfiguration.class)
public interface SystemPrinterClient {

	@RequestMapping(value ="/systemPrinter/querySystemPrinterByUserId/{userId}", method = RequestMethod.GET)
	List<SystemPrinterODTO> querySystemPrinterByUserId(@PathVariable("userId") Long userId);


	@RequestMapping(value ="/systemPrinter/queryEmployeePrinterByDeliveryManCode/{employeeCode}", method = RequestMethod.GET)
	EmployeePrinterODTO queryEmployeePrinterByDeliveryManCode(@PathVariable("employeeCode") String employeeCode);

	@RequestMapping(value ="/systemPrinter/queryStorePrinterByStoreCode/{storeCode}", method = RequestMethod.GET)
	List<StorePrinterODTO> queryStorePrinterByStoreCode(@PathVariable("storeCode") String storeCode);

}

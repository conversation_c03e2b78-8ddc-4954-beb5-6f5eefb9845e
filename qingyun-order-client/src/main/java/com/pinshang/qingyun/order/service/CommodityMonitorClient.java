package com.pinshang.qingyun.order.service;

import com.pinshang.qingyun.base.configure.FeignClientConfiguration;
import com.pinshang.qingyun.base.constant.ApplicationNameConstant;
import com.pinshang.qingyun.order.dto.commodity.CommodityMonitorIDTO;
import com.pinshang.qingyun.order.dto.commodity.CommodityMonitorODTO;
import com.pinshang.qingyun.order.hystrix.CommodityMonitorClientHystrix;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

/**
 * 商品监控->订单信息
 * <AUTHOR>
 * @Date 2018/10/11 13:42
 */
@FeignClient(value = ApplicationNameConstant.QINGYUN_ORDER_SERVICE, fallbackFactory = CommodityMonitorClientHystrix.class, configuration = FeignClientConfiguration.class)
public interface CommodityMonitorClient {
    @RequestMapping(value = "/commodityMonitor/orderInfo",method = RequestMethod.POST)
    CommodityMonitorODTO orderInfo(CommodityMonitorIDTO idto);
}

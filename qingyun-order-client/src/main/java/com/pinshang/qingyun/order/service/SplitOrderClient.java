package com.pinshang.qingyun.order.service;

import com.github.pagehelper.PageInfo;
import com.pinshang.qingyun.base.configure.FeignClientConfiguration;
import com.pinshang.qingyun.base.constant.ApplicationNameConstant;
import com.pinshang.qingyun.order.dto.orderMonitor.NonSplitCommodityDetailODTO;
import com.pinshang.qingyun.order.dto.orderMonitor.NonSplitOrderODTO;
import com.pinshang.qingyun.order.dto.orderMonitor.NonSplitOrderQueryIDTO;
import com.pinshang.qingyun.order.hystrix.SplitOrderClientHystrix;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@FeignClient(value = ApplicationNameConstant.QINGYUN_ORDER_SERVICE, fallbackFactory = SplitOrderClientHystrix.class, configuration = FeignClientConfiguration.class)
public interface SplitOrderClient {

    @RequestMapping(value="/splitOrder/manualSplitOrderByPage",method = RequestMethod.POST)
    PageInfo<NonSplitOrderODTO> manualSplitOrderByPage(@RequestBody NonSplitOrderQueryIDTO nonSplitOrderQueryVo);

    /** 根据订单ID，查询此订单的商品拆单相关信息(物流模式，仓库，供应商)**/
    @RequestMapping(value = "/splitOrder/findByOrderId/{orderId}", method = RequestMethod.GET)
    List<NonSplitCommodityDetailODTO> findNonSplitByOrderId(@PathVariable("orderId") Long orderId);

    @RequestMapping(value="/splitOrder/manualSplitOrder",method = RequestMethod.POST)
    void manualSplitOrder(@RequestBody NonSplitOrderQueryIDTO nonSplitOrderQueryIDTO);


    @RequestMapping(value="/splitOrder/splitOrderByJob",method = RequestMethod.GET)
    Boolean splitOrderByJob(@RequestParam(value = "createTime") String createTime);

//    @RequestMapping(value = "/splitOrder/removeSplitOrderInfo", method = RequestMethod.GET)
//    Integer removeSplitOrderInfo();
}

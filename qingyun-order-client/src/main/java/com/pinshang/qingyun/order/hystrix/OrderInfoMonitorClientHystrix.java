package com.pinshang.qingyun.order.hystrix;

import com.pinshang.qingyun.order.dto.orderMonitor.OrderMonitorODTO;
import com.pinshang.qingyun.order.dto.orderMonitor.SubOrderMonitorODTO;
import com.pinshang.qingyun.order.service.OrderInfoMonitorClient;
import feign.hystrix.FallbackFactory;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2018/10/12 14:56
 */
@Component
public class OrderInfoMonitorClientHystrix implements FallbackFactory<OrderInfoMonitorClient> {

    @Override
    public OrderInfoMonitorClient create(Throwable throwable) {
        return new OrderInfoMonitorClient() {
            @Override
            public OrderMonitorODTO getMonitorOrderInfo(String orderCode) {
                return null;
            }

            @Override
            public List<SubOrderMonitorODTO> getMonitorSubOrderInfo(String orderCode) {
                return null;
            }
        };
    }
}

package com.pinshang.qingyun.order.dto.shop;

import com.pinshang.qingyun.base.enums.IogisticsModelEnums;
import com.pinshang.qingyun.base.enums.shop.ShopTypeEnums;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.util.Date;

@Data
public class MdShopOrderSettingODTO {

    private Long id;

    private Long enterpriseId;//企业ID

    private String commodityId;

    private String commodityCode;

    private String commodityName;//商品名称

    private String commoditySpec;//商品规格

    private Long shopId;

    private Integer shopType;

    private String storeId;

    private String shopCode;

    private String shopName;//门店名称

    private String storeCode;

    private String storeName;

    private Long supplierId;

    private String supplierCode;

    private String supplierName;//供应商名称

    private Integer logisticsModel;//物流模式

    private String deleveryTimeRange;//配送范围

    private Integer changePriceStatus;//是否可变价

    private Date createTime;

    private Date updateTime;

    private Long createId;

    private Long updateId;

    private String deleveryTimeRangeName;//配送范围

    private String defaultSupplierBeginTime;
    private String defaultSupplierEndTime;

    private String warehouseId;
    private String warehouseName;
    private String defaultWarehouseBeginTime;
    private String defaultWarehouseEndTime;

    private  Integer isNew;

    /** 商品截单时间 */
    private String expirationTime;
    public String getExpirationTime(){
        if(logisticsModel != null) {
            if(logisticsModel == IogisticsModelEnums.DIRECT_SENDING.getCode()
                    || logisticsModel == IogisticsModelEnums.DIRECT_CONNECTION.getCode()){
                return defaultSupplierEndTime;
            }
            if(logisticsModel == IogisticsModelEnums.DISPATCHING.getCode()){
                return defaultWarehouseEndTime;
            }
        }
        return "";
    }

    public String getShopTypeName(){
        if(shopType == null){
            return "";
        }else if(shopType == -1){
            return "全部";
        }
        return ShopTypeEnums.getName(shopType);
    }

    public Long getCommodityIdLong(){
        if(StringUtils.isNotBlank(commodityId)){
            return Long.valueOf(commodityId);
        }
        return 0L;
    }
}

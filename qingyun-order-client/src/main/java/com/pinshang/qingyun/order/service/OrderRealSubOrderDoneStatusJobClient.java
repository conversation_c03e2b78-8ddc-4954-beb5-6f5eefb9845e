package com.pinshang.qingyun.order.service;

import com.pinshang.qingyun.base.configure.FeignClientConfiguration;
import com.pinshang.qingyun.base.constant.ApplicationNameConstant;
import com.pinshang.qingyun.order.dto.OrderRealSubOrderDoneStatusIDTO;
import com.pinshang.qingyun.order.hystrix.OrderRealSubOrderDoneStatusJobClientHystrix;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;


/**
 * <AUTHOR>
 * @date 2024/7/2 11:25
 * <p>
 * 回填订单 real_sub_order_done_status 子订单是否出库完标识：只有1代表所有子单(不包括直送)已出库
 * 直接调用 dc 接口获取出库完成的订单 做标志 job跑批
 */
@FeignClient(value = ApplicationNameConstant.QINGYUN_ORDER_SERVICE, fallbackFactory = OrderRealSubOrderDoneStatusJobClientHystrix.class, configuration = FeignClientConfiguration.class)
public interface OrderRealSubOrderDoneStatusJobClient {

    @PostMapping(value = "/realSubOrderDoneStatus/exec/job")
    void processOrderRealSubOrderDoneStatus(@RequestBody OrderRealSubOrderDoneStatusIDTO vo);
}

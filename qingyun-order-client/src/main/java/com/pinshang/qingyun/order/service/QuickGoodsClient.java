package com.pinshang.qingyun.order.service;

import com.pinshang.qingyun.base.configure.FeignClientConfiguration;
import com.pinshang.qingyun.base.constant.ApplicationNameConstant;
import com.pinshang.qingyun.order.dto.order.QuickGoodsIDTO;
import com.pinshang.qingyun.order.dto.order.QuickGoodsODTO;
import com.pinshang.qingyun.order.hystrix.QuickGoodsHystrix;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import java.util.List;

@FeignClient(value = ApplicationNameConstant.QINGYUN_ORDER_SERVICE, fallbackFactory = QuickGoodsHystrix.class, configuration = FeignClientConfiguration.class)
public interface QuickGoodsClient {
	
    @RequestMapping(value = "/order/findQuickGoodsList", method = RequestMethod.POST)
    List<QuickGoodsODTO> findQuickGoodsList(QuickGoodsIDTO idto);
    
    @RequestMapping(value = "/order/deleteQuickGoodsByStoreId", method = RequestMethod.POST)
    Integer deleteQuickGoodsByStoreId(QuickGoodsIDTO idto);
    
    @RequestMapping(value="/order/quickGoods/addShoppingCart", method = RequestMethod.POST)
    Integer addShoppingCart(QuickGoodsIDTO idto);
    
    @RequestMapping(value = "/order/saveQuickGoods", method = RequestMethod.POST)
    Boolean saveQuickGoods(QuickGoodsIDTO idto) throws Throwable;

    @RequestMapping(value = "/order/saveQuickGoods4admin", method = RequestMethod.POST)
    Boolean saveQuickGoods4admin(QuickGoodsIDTO idto) throws Throwable;
}

package com.pinshang.qingyun.order.dto.shop;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 *
 * <AUTHOR>
 * @Date 2018/4/9 18:01
 */
@Data
public class StoreAccountODTO implements Serializable {

    private static final long serialVersionUID = 6409360255806535064L;
    /**
     * 客户ID
     */
    @ApiModelProperty("客户ID")
    private Long storeId;
    /**
     * 客户Code
     */
    @ApiModelProperty("客户Code")
    private String storeCode;
    /**
     * 客户名称
     */
    @ApiModelProperty("客户名称")
    private String storeName;
    /**
     * 客户状态
     */
    @ApiModelProperty("客户状态")
    private Integer storeStatus;
    /**
     * 订货时间段
     */
    @ApiModelProperty("订货时间段")
    private String beginTime;
    /**
     * 时间段
     */
    @ApiModelProperty("时间段")
    private String endTime;
    /**
     * 订单限制数
     */
    @ApiModelProperty("订单限制数")
    private Integer maxOrderNum;
    /**
     * 送货员
     */
    @ApiModelProperty("送货员")
    private String deliverymanName;
    /**
     * 账号状态
     */
    @ApiModelProperty("账号状态")
    private Integer openStatus;

}

package com.pinshang.qingyun.order.hystrix;

import com.github.pagehelper.PageInfo;
import com.pinshang.qingyun.order.dto.CloudOrderAllocationResultOrderODTO;
import com.pinshang.qingyun.order.dto.CloudOrderAllocationSaveIDTO;
import com.pinshang.qingyun.order.dto.ShopOrderedQuantityODTO;
import com.pinshang.qingyun.order.dto.ShopOrderedQuantityQueryIDTO;
import com.pinshang.qingyun.order.dto.commodity.*;
import com.pinshang.qingyun.order.dto.order.*;
import com.pinshang.qingyun.order.dto.shop.OutstandingShopIDTO;
import com.pinshang.qingyun.order.dto.shop.OutstandingShopODTO;
import com.pinshang.qingyun.order.dto.shop.ShopOrderIDTO;
import com.pinshang.qingyun.order.dto.sync.SyncOrderListIDTO;
import com.pinshang.qingyun.order.dto.sync.SyncOrderListODTO;
import com.pinshang.qingyun.order.dto.sync.SyncOrderODTO;
import com.pinshang.qingyun.order.service.OrderClient;
import feign.hystrix.FallbackFactory;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.Collections;
import java.util.List;

@Component
public class OrderClientHystrix  implements FallbackFactory<OrderClient> {
	private Logger logger = LoggerFactory.getLogger(getClass());
	@Override
	public OrderClient create(Throwable throwable) {
		return new OrderClient() {
			@Override
			public PageInfo<OrderListODTO> findOrderListByPage(@RequestBody OrderListIDto orderListIDto) {
				logger.error(throwable.getMessage());
				return null;
			}
			@Override
			public PageInfo<OrderListODTO> findPreOrderListByPage(@RequestBody OrderListIDto orderListIDto) {
				logger.error(throwable.getMessage());
				return null;
			}
			@Override
			public boolean orderCancelById(@PathVariable("orderId") Long orderId, @PathVariable("isInternal") Boolean isInternal, @RequestParam("userId") Long userId, @RequestParam("realName") String realName) {
				logger.error(throwable.getMessage());
				return false;
			}
			@Override
			public boolean copyOrderById(@PathVariable("orderId") Long orderId, @PathVariable("isInternal") Boolean isInternal) {
				logger.error(throwable.getMessage());
				return false;
			}

			@Override
			public CreateOrderODTO createOrder(List<CreateOrderIDTO> vos) {
				logger.error(throwable.getMessage());
				return new CreateOrderODTO(false, null, null);
			}

			@Override
			public String findStoreOrderTimeByStoreId(@PathVariable("storeId") String storeId) {
				logger.error(throwable.getMessage());
				return null;
			}

			@Override
			public boolean createShopOrder(@RequestBody ShopOrderIDTO shopOrderIDTO) {
				logger.error(throwable.getMessage());
				return false;
			}
			@Override
			public boolean preOrderCancelByOrderId(@PathVariable("orderId") Long orderId, @PathVariable("isInternal") Boolean isInternal, @RequestParam("userId") Long userId) {
				return false;
			}
			@Override
			public boolean copyPreOrderById(@PathVariable("orderId") Long orderId, @PathVariable("isInternal") Boolean isInternal) {
				return false;
			}
			@Override
			public boolean preOrderCheckSuucess(@RequestBody PreSaveOrderIDTO vo) {
				return false;
			}

			@Override
			public boolean generatePurchaseOrder() {
				return false;
			}
			@Override
			public boolean createPurchaseOrderByTime(@RequestParam("times")String times) {
				return false;
			}

			@Override
			public PageInfo<OutstandingShopODTO> outstandingStore(OutstandingShopIDTO vo) {
				return null;
			}

			@Override
			public PageInfo<ModifyDeliverDateODTO> queryModifyDeliverDateList(ModifyDeliverDateIDTO dto) {
				return null;
			}

			@Override
			public void modifyDeliverDate(String orderCode, String date) {

			}

			@Override
			public boolean createDirectSendingBackOrder(DirectSendingBackOrderIDTO idto) {
				return false;
			}

			@Override
			public void fixedSettleOrder(String sourceCode, String sourceType,String topic) {

			}
			@Override
			public List<CreateOrderIDTO.CreateOrderItemIDTO> findShoppingCartItems(Long storeId, Long shoppingCartId) {
				return null;
			}

			@Override
			public CreateOrderODTO createHandleOrder(CreateHandleOrderIDTO createHandleOrderIDTO) {
				return null;
			}

			@Override
			public void orderNotCommitTips() {

			}

            @Override
            public List<OrderLogListODTO> getOrderLogList(@PathVariable("orderId") Long orderId) {
                return null;
            }

			@Override
			public void createOrderYear() {

			}

			@Override
			public Boolean createGrouponOrder( String timeStamp) {
				return null;
			}

			@Override
			public Boolean grouponOrderDay(String timeStamp) {
				return null;
			}

			@Override
			public Boolean stockOutSettleMsg(String beginTime, String endTime) {
				return null;
			}

			@Override
			public Double getSumRealQuantity(Long orderId) {
				return null;
			}

			@Override
			public Integer modifyOrderCompanyByStore(Long storeId, Long companyId) {
				return null;
			}

			@Override
			public List<CloudOrderAllocationResultOrderODTO> cloudOrderAllocationCreateOrder(List<CloudOrderAllocationSaveIDTO> list) {
				return null;
			}

			@Override
			public List<ShopOrderedQuantityODTO> queryShopOrderedQuantity(ShopOrderedQuantityQueryIDTO idto) {
				return null;
			}

			@Override
			public OrderODTO getOrderById(Long orderId) {
				return null;
			}

            @Override
            public List<OrderListInfoODTO> selectOrderListGiftByOrderIdAndCommodityIdList(OrderListOrderIdAndCommIDTO vo) {
                return null;
            }

			@Override
			public Boolean existConsignmentOrder(Long shopId) {
				return null;
			}

			@Override
			public Long selectOrderListGiftJob(OrderReportJobIDTO vo) {
				return null;
			}

			@Override
			public List<SyncOrderODTO> syncOrderList(SyncOrderListIDTO syncOrderListIDTO) {
				return null;
			}

			@Override
			public List<TdaDeliveryTimeRangeODTO> selectTdaDeliveryTimeRangeList(String orderTime, Long storeId) {
				return null;
			}

			@Override
			public List<MtCouponDayStatisticsODTO> queryOrderCouponList(String beginTime, String endTime) {
				return Collections.emptyList();
			}

			@Override
			public OrderImportSaveResponse importSave(OrderImportSaveIDTO orderImportSaveIDTO) {
				return null;
			}

			@Override
			public List<XiaoeTongPushOrderIDTO> createXiaoeTongOrder(List<XiaoeTongPushOrderIDTO> xiaoeTongPushOrderList) {
				return Collections.emptyList();
			}

			@Override
			public List<SyncOrderODTO> selectOrderListByBusinessTypeIsNullOrLogisticsCenterIsNull(String beginTime, String endTime) {
				return null;
			}

			@Override
			public Boolean updateOrderStatus(UpdateOrderIDTO updateOrderIDTO) {
				return null;
			}
		};
	}
}

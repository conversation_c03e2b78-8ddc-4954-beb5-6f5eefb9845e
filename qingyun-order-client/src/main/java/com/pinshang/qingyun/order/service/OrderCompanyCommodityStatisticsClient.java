package com.pinshang.qingyun.order.service;

import com.pinshang.qingyun.base.configure.FeignClientConfiguration;
import com.pinshang.qingyun.base.constant.ApplicationNameConstant;
import com.pinshang.qingyun.order.hystrix.OrderCompanyCommodityStatisticsClientHystrix;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;


/**
 * 公司商品订货统计
 */
@FeignClient(value = ApplicationNameConstant.QINGYUN_ORDER_SERVICE, fallbackFactory = OrderCompanyCommodityStatisticsClientHystrix.class, configuration = FeignClientConfiguration.class)
public interface OrderCompanyCommodityStatisticsClient {

    @RequestMapping(value = "/orderCompanyCommodityStatistics/createOrderCompanyCommodityStatisticsJob", method = RequestMethod.POST)
    void createOrderCompanyCommodityStatisticsJob(@RequestBody(required = false) String[] orderTimes);

}

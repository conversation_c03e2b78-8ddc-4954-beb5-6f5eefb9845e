package com.pinshang.qingyun.order.dto.shop;

import com.pinshang.qingyun.order.dto.Pagination;
import lombok.Data;

import java.util.List;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2017/6/15.
 */
@Data
public class AuditQueryIDTO extends Pagination{

    private String beginDate;

    private String endDate;

    private Integer logisticsModel;

    private Integer status;

    private String supplierId;

    private String subOrderCode;

    private String shopStr;

    private String enterpriseId;

    private List<Integer> orderStatus;
    
    private String supplierStr;

    /** 档口id */
    private Long stallId;
}

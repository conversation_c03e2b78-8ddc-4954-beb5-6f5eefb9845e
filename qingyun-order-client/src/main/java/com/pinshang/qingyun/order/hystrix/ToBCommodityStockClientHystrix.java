package com.pinshang.qingyun.order.hystrix;

import com.github.pagehelper.PageInfo;
import com.pinshang.qingyun.order.dto.tob.*;
import com.pinshang.qingyun.order.service.ToBCommodityStockClient;
import feign.hystrix.FallbackFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;


@Component
@Slf4j
public class ToBCommodityStockClientHystrix implements FallbackFactory<ToBCommodityStockClient> {


    @Override
    public ToBCommodityStockClient create(Throwable throwable) {
        return new ToBCommodityStockClient() {

            @Override
            public void stockRepair(List<ToBStockRepairIDTO> repairIDTOList) {

            }

            @Override
            public Integer queryStockCount() {
                return null;
            }

            @Override
            public PageInfo<ToBStockRepairODTO> queryStockList(Integer pageNo, Integer pageSize) {
                return null;
            }

            @Override
            public List<Long> selectCommodityIdByStockType(Integer stockType) {
                return null;
            }

            @Override
            public List<EsXdaCommoditySoldOutODTO> queryCommodityInventory(EsXdaCommoditySoldOutIDTO idto) {
                return null;
            }

            @Override
            public Boolean updateXdaCommodityStock(TobCommodityStockIDTO idto) {
                return null;
            }
        };
    }
}

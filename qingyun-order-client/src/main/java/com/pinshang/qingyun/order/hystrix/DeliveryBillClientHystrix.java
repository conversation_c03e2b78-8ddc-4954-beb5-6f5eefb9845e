package com.pinshang.qingyun.order.hystrix;

import com.github.pagehelper.PageInfo;
import com.pinshang.qingyun.order.dto.deliveryBill.DeliveryBillODTO;
import com.pinshang.qingyun.order.dto.deliveryBill.DeliveryBillSearchIDTO;
import com.pinshang.qingyun.order.service.DeliveryBillClient;
import feign.hystrix.FallbackFactory;
import org.springframework.stereotype.Component;

/**
 * @description:
 * @author: hhf
 * @time: 2021/8/4 15:15
 */
@Component
public class DeliveryBillClientHystrix implements FallbackFactory<DeliveryBillClient> {
    @Override
    public DeliveryBillClient create(Throwable throwable) {
        return new DeliveryBillClient() {
            @Override
            public PageInfo<DeliveryBillODTO> findDeliveryBillPageInfoByParams(DeliveryBillSearchIDTO idto) {
                return null;
            }

            @Override
            public String exportBill(Long id) {
                return null;
            }

            @Override
            public String printerBill(Long id, Long userId) {
                return null;
            }
        };
    }
}

package com.pinshang.qingyun.order.dto.finance;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2025/7/2 10:30
 */
@Data
@NoArgsConstructor
public class DirectDeliveryOrderIDTO {

    @ApiModelProperty(value = "业务日期：yyyy-MM-dd")
    private String businessDate;

    @ApiModelProperty(value = "业务单明细ID 查询起点比如100表示就从第100条开始查询到limitQuantity")
    private Long businessOrderItemId;

    @ApiModelProperty(value = "查询条数")
    private Integer limitQuantity;

    @ApiModelProperty(value = "业务类型:1-直送订单 2-直送退单")
    private Integer businessType;
    
    public DirectDeliveryOrderIDTO(String businessDate, Integer businessType, Integer limitQuantity, Long businessOrderItemId) {
    	this.businessDate = businessDate;
    	this.businessType = businessType;
    	this.limitQuantity = limitQuantity;
    	this.businessOrderItemId = businessOrderItemId;
    }
    
}

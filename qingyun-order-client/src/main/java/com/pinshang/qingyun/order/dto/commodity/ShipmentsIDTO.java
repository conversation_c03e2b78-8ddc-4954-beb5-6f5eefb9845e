//package com.pinshang.qingyun.order.dto.commodity;
//
//import io.swagger.annotations.ApiModelProperty;
//import lombok.Data;
//
//import java.io.Serializable;
//
///**
// *
// * <AUTHOR>
// * @Date 2018/4/9 18:07
// */
//@Data
//public class ShipmentsIDTO implements Serializable {
//
//    private static final long serialVersionUID = -7611471340229568127L;
//    /**
//     * 订单日期
//     * "yyyy-MM-dd"
//     */
//    @ApiModelProperty("订单日期 yyyy-MM-dd")
//    private String orderDate;
//    /**
//     * 线路组ID
//     */
//    @ApiModelProperty("线路组ID")
//    private Long lineGroupId;
//    /**
//     * 发货仓库ID
//     */
//    @ApiModelProperty("发货仓库ID")
//    private Long warehouseId;
//    /**
//     * 生产组主任code
//     */
//    @ApiModelProperty(value = "生产组主任code",required = true)
//    private String directorCode;
//    /**
//     * 发货时间
//     * "HH:mm"
//     */
//    @ApiModelProperty("发货时间 HH:mm")
//    private String deliveryTime;
//
//    /**
//     * 配送批次
//     */
//    @ApiModelProperty("字典表ID")
//    private Long deliveryBatch;
//}

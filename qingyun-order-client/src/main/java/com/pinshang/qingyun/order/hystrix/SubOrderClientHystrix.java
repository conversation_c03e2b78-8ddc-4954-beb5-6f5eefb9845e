package com.pinshang.qingyun.order.hystrix;

import com.github.pagehelper.PageInfo;
import com.pinshang.qingyun.order.dto.UnDeliverySubOrderIDTO;
import com.pinshang.qingyun.order.dto.order.*;
import com.pinshang.qingyun.order.service.SubOrderClient;
import feign.hystrix.FallbackFactory;
import org.springframework.stereotype.Component;
import java.util.List;

@Component
public class SubOrderClientHystrix implements FallbackFactory<SubOrderClient> {
    @Override
    public SubOrderClient create(Throwable throwable) {
        return new SubOrderClient() {

            @Override
            public PageInfo<SubOrderListEntryODTO> queryUnDeliverySubOrderList(SubOrderIDTO vo) {
                return null;
            }

            @Override
            public List<SubOrderItemListEntryODTO> querySubOrderItemList(SubOrderSearchIDTO vo) {
                return null;
            }

            @Override
            public boolean createDeliveryOrder(SubOrderSearchIDTO dto) {
                return false;
            }

            @Override
            public boolean batchUpdateDeliveryQuantityV2(List<PickSubOrderIDTO> subOrderList) {
                return false;
            }

            @Override
            public void unDeliverySubOrderJob(UnDeliverySubOrderIDTO idto) {

            }

            @Override
            public int batchUpdateDeliveryQuantity(List<PickSubOrderItemODTO> deliveryItemList) {
                return 0;
            }

            @Override
            public List<PickSubOrderItemODTO> findItemsBySubOrderId(Long subOrderId) {
                return null;
            }

            @Override
            public Integer updateSubOrderStatus(List<Long> subOrderIdList, Integer subOrderStatus) {
                return null;
            }

        };
    }

}

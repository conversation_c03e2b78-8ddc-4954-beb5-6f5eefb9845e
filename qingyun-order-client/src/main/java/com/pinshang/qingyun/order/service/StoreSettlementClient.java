package com.pinshang.qingyun.order.service;

import com.pinshang.qingyun.base.configure.FeignClientConfiguration;
import com.pinshang.qingyun.base.constant.ApplicationNameConstant;
import com.pinshang.qingyun.order.dto.store.StoreSettlementODTO;
import com.pinshang.qingyun.order.hystrix.StoreSettlementClientHystrix;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2019/3/11 16:25
 */
@FeignClient(value = ApplicationNameConstant.QINGYUN_ORDER_SERVICE, fallbackFactory = StoreSettlementClientHystrix.class, configuration = FeignClientConfiguration.class)
public interface StoreSettlementClient {

    @RequestMapping(value = "/store/settlement/balance/{storeCode}", method = RequestMethod.GET)
    StoreSettlementODTO queryBalance(@PathVariable("storeCode") String storeCode);

    @RequestMapping(value = "/store/settlement/findStoreSettleByStoreIds", method = RequestMethod.GET)
    List<StoreSettlementODTO> findStoreSettleByStoreIds(@RequestBody List<Long> storeIds);

}

package com.pinshang.qingyun.order.service.finance;

import com.pinshang.qingyun.base.configure.FeignClientConfiguration;
import com.pinshang.qingyun.base.constant.ApplicationNameConstant;
import com.pinshang.qingyun.order.dto.finance.DirectDeliveryOrderIDTO;
import com.pinshang.qingyun.order.dto.finance.DirectDeliveryOrderODTO;
import com.pinshang.qingyun.order.dto.finance.ShopLessGoodsOrderIDTO;
import com.pinshang.qingyun.order.dto.finance.ShopLessGoodsOrderODTO;
import com.pinshang.qingyun.order.hystrix.finance.OrderFinanceClientHystrix;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/7/2 14:56
 */
@FeignClient(value = ApplicationNameConstant.QINGYUN_ORDER_SERVICE, fallbackFactory = OrderFinanceClientHystrix.class, configuration = FeignClientConfiguration.class)
public interface OrderFinanceClient {

    @PostMapping("/orderFinance/client/directDeliveryOrder/list")
    List<DirectDeliveryOrderODTO> selectDirectDeliveryOrderList(@RequestBody DirectDeliveryOrderIDTO idto);

    @PostMapping("/orderFinance/client/shopLessGoods/list")
    List<ShopLessGoodsOrderODTO> selectShopLessGoodsOrderList(@RequestBody ShopLessGoodsOrderIDTO idto);
}

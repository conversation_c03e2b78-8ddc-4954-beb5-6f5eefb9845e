package com.pinshang.qingyun.order.dto.order;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
public class SubOrderItemListEntryODTO {
	@ApiModelProperty("商品ID")
	private String commodityId;
	@ApiModelProperty("商品编码")
	private String commodityCode;
	@ApiModelProperty("商品名称")
	private String commodityName;
	@ApiModelProperty("商品规格")
	private String commoditySpec;
	@ApiModelProperty("计量单位名称")
	private String commodityUnitName;
	@ApiModelProperty("商品的条形码列表")
	private List<String> barCodeList;
	@ApiModelProperty("商品的条形码")
	private String barCode;
	@ApiModelProperty("数量")
	private String quantity;
	@ApiModelProperty("份数")
	private Integer number;
	@ApiModelProperty("组合商品转换状态：0=无转换，1=有转换")
	private Integer convertStatus;
	@ApiModelProperty("组合商品转换的最小单位商品ID")
	@JsonSerialize(using = ToStringSerializer.class)
	private Long targetCommodityId;
	@ApiModelProperty("组合商品转换的商品编码")
	private String targetCommodityCode;
	@ApiModelProperty("组合商品转换的商品名称")
	private String targetCommodityName;
	@ApiModelProperty("组合商品转换的商品规格")
	private String targetCommoditySpec;
	@ApiModelProperty("组合商品转换的商品计量单位名称")
	private String targetCommodityUnitName;
	@ApiModelProperty("组合商品转换的数量")
	private String targetQuantity;
	@ApiModelProperty("组合商品转换的份数")
	private Integer targetNumber;
}

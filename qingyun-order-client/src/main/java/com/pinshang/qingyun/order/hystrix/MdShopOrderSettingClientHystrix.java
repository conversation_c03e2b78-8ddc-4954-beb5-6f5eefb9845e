package com.pinshang.qingyun.order.hystrix;

import com.github.pagehelper.PageInfo;
import com.pinshang.qingyun.order.dto.shop.*;
import com.pinshang.qingyun.order.service.MdShopOrderSettingClient;
import feign.hystrix.FallbackFactory;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.Collections;
import java.util.List;

@Component
public class MdShopOrderSettingClientHystrix implements FallbackFactory<MdShopOrderSettingClient>{
    private Logger logger = LoggerFactory.getLogger(getClass());

    @Override
    public MdShopOrderSettingClient create(Throwable cause) {
        return new MdShopOrderSettingClient() {

            @Override
            public List<MdShopOrderSettingODTO> queryMdShopOrderSettingListByIds(List<String> commodityIds) {
                return null;
            }

            @Override
            public List<MdShopOrderSettingODTO> queryMdShopOrderSettingListByShopType(MdShopOrderSettingQueryIDTO queryIDTO) {
                return Collections.emptyList();
            }

            @Override
            public PageInfo<MdShopOrderSettingODTO> queryMdShopOrderSettingListByParams(MdShopOrderSettingIDTO mdShopOrderSettingIDTO) {
                return null;
            }

            @Override
            public MdShopOrderSettingODTO findMdShopOrderSettingById(Long id) {
                return null;
            }

            @Override
            public Integer updateMdShopOrderSettingById(MdShopOrderSettingIDTO mdShopOrderSettingIDTO) {
                return null;
            }

            @Override
            public Integer deleteMdShopOrderSettingById(Long id,Long createId) {
                return null;
            }

            @Override
            public Integer saveMdShopOrderSettingList(List<MdShopOrderSettingIDTO> list) {
                return null;
            }

            @Override
            public PageInfo<MdShopOrderSettingLogODTO> queryMdShopOrderSettingLogListByParams(MdShopOrderSettingLogIDTO mdShopOrderSettingLogIDTO) {
                return null;
            }

            @Override
            public Integer dealMdShopOrderSetting(@RequestBody ShopOrderSettingDealIDTO dto) {
                return null;
            }

            @Override
            public Integer batchDealMdShopOrderSettingDetails(List<ShopOrderSettingDetailsDealIDTO> list) {
                return null;
            }

            @Override
            public List<MdShopOrderSettingODTO> queryMdShopOrderSettingListByCommodityIds(List<String> commodityIds, Long storeId) { return null;}

            @Override
            public boolean lastMonthSale() {
                return false;
            }

            @Override
            public boolean deleteDirectSendingCommodity() {
                return false;
            }


        };
    }
}

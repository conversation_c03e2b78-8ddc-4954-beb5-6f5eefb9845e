package com.pinshang.qingyun.order.hystrix;

import com.pinshang.qingyun.order.dto.tda.CancelReturnOrderIDTO;
import com.pinshang.qingyun.order.dto.tda.ReturnOrderODTO;
import com.pinshang.qingyun.order.dto.tda.SaveReturnOrderODTO;
import com.pinshang.qingyun.order.service.XdaReturnOrderClient;
import feign.hystrix.FallbackFactory;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

@Component
public class XdaReturnOrderClientHystrix implements FallbackFactory<XdaReturnOrderClient> {
	private Logger logger = LoggerFactory.getLogger(getClass());
	
	@Override
	public XdaReturnOrderClient create(Throwable throwable) {
		return new XdaReturnOrderClient() {
			@Override
			public Boolean generateReturnOrder(SaveReturnOrderODTO dto) {
				return null;
			}

			@Override
			public Boolean cancelReturnOrder(CancelReturnOrderIDTO dto) {
				return null;
			}

			@Override
			public ReturnOrderODTO queryReturnOrderByReturnOrderSeq(String returnOrderSeq) {
				return null;
			}
		};
	}
}

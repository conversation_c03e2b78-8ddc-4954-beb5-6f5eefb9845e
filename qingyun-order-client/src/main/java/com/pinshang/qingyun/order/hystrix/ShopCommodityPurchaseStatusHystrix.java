package com.pinshang.qingyun.order.hystrix;

import com.github.pagehelper.PageInfo;
import com.pinshang.qingyun.order.dto.commodity.CommodityPurchaseStatusIDTO;
import com.pinshang.qingyun.order.dto.purchaseStatus.ShopCommodityModifyPurchaseStatusIDTO;
import com.pinshang.qingyun.order.dto.purchaseStatus.ShopCommodityPurchaseStatusIDTO;
import com.pinshang.qingyun.order.dto.purchaseStatus.ShopCommodityPurchaseStatusODTO;
import com.pinshang.qingyun.order.dto.shop.*;
import com.pinshang.qingyun.order.service.ShopCommodityPurchaseStatusClient;
import feign.hystrix.FallbackFactory;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public class ShopCommodityPurchaseStatusHystrix implements FallbackFactory<ShopCommodityPurchaseStatusClient> {
    @Override
    public ShopCommodityPurchaseStatusClient create(Throwable throwable) {
        return  new ShopCommodityPurchaseStatusClient() {
            @Override
            public int updateShopCommodityPurchaseStatus(List<CommodityPurchaseStatusIDTO> commodityPurchaseStatusIDTOList) {
              return 0;
            }
            @Override
            public int saveShopCommodityPurchaseStatus(List<CommodityPurchaseStatusIDTO> commodityPurchaseStatusIDTOList) {
                return 0;
            }
            @Override
            public List<ShopQtOrderProductODTO> selectShopQtOrderProduct(ShopQtOrderProductIDTO shopQtOrderProductIDTO){
                return null;
            }
//            @Override
//            public List<PriceModelLogODTO> selectProductPriceModel(PriceModelLogSearchIDTO idto){
//                return null;
//            }
            @Override
            public List<CommodityPriceODTO> selectProductPriceModelListByPriceModeClode(String priceModeClode){
                return  null;
            }
            @Override
            public List<CommodityListODTO> selectCommodityByProductPriceModelId(Long productPriceModelId){
                return null;
            }
            @Override
            public PageInfo<PriceModelLogODTO> list(PriceModelLogSearchIDTO idto){
                return null;
            }
            @Override
            public List<XsShopCommodityPurchaseStatusODTO> selectXsShopCommodityPurchaseStatus(XsShopCommodityPurchaseStatusIDTO idto){
                return null;
            }

            @Override
            public PageInfo<ShopCommodityPurchaseStatusODTO> selectShopCommodityPurchaseStatusList(ShopCommodityPurchaseStatusIDTO shopCommodityPurchaseStatusIDTO) {
                return null;
            }

            @Override
            public Integer modifyShopCommodityPurchaseStatusList(ShopCommodityModifyPurchaseStatusIDTO shopCommodityModifyPurchaseStatusIDTO) {
                return null;
            }

        };
    }

}

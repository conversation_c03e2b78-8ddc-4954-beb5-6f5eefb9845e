package com.pinshang.qingyun.order.service;

import com.pinshang.qingyun.base.configure.FeignClientConfiguration;
import com.pinshang.qingyun.base.constant.ApplicationNameConstant;
import com.pinshang.qingyun.base.page.TablePageInfo;
import com.pinshang.qingyun.order.dto.commodity.*;
import com.pinshang.qingyun.order.dto.order.BStockShortResponseODTO;
import com.pinshang.qingyun.order.dto.order.DeliveryBatchODTO;
import com.pinshang.qingyun.order.hystrix.ShoppingCartClientHystrix;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

@FeignClient(value = ApplicationNameConstant.QINGYUN_ORDER_SERVICE, fallbackFactory = ShoppingCartClientHystrix.class, configuration = FeignClientConfiguration.class)
public interface ShoppingCartClient{
	
    @RequestMapping(value = "/shoppingcart/add", method = RequestMethod.POST)
    public List<BStockShortResponseODTO> addShoppingCart(@RequestBody ShoppingCartIDTO shoppingCartIDTO);

    @RequestMapping(value = "/shoppingcart/batchAdd", method = RequestMethod.POST)
    public List<BStockShortResponseODTO> batchAddShoppingCart(ShoppingCartBatchIDTO shoppingCartIDTO);
    
    @RequestMapping(value = "/shoppingcart/remove", method = RequestMethod.POST)
    public Boolean removeShoppingCart(@RequestBody ShoppingCartIDTO shoppingCartIDTO);

    /**
     * 门店购物车列表
     */
    @RequestMapping(value="/shoppingcart/detail/{storeId}/{isInternal}/{userId}", method =RequestMethod.GET)
    public ShoppingCartDetailODto shoppingCartDetail(@PathVariable("storeId") Long storeId, @PathVariable("isInternal") boolean isInternal, @PathVariable("userId") Long userId);

    /**
     * 总部登录、代理购物车列表
     */
    @RequestMapping(value="/shoppingcart/detailAdmin", method =RequestMethod.POST)
    TablePageInfo<ShoppingCartDetailODto>  shoppingCartDetailAdmin(@RequestBody ShoppingCartAdminPageIDTO shoppingCartAdminPageIDTO);

    /**
     * 门店购物车单个详情
     */
    @RequestMapping(value="/shoppingcart/cartdetail", method =RequestMethod.GET)
    public ShoppingCartDetailHandleODto cartdetail(@RequestParam("storeId") Long storeId, @RequestParam("shoppingCartId") Long shoppingCartId, @RequestParam("isInternal") Boolean isInternal, @RequestParam(value = "orderTime", required = false) String orderTime);

    /**
     * 总部代理购物车单个
     */
    @RequestMapping(value="/shoppingcart/cartdetailAdmin", method =RequestMethod.GET)
    public ShoppingCartDetailHandleODto cartdetailAdmin(@RequestParam("shoppingCartId") Long shoppingCartId);

    @RequestMapping(value = "/shoppingcart/updateItemQuantity", method = RequestMethod.POST)
    public Boolean updateItemQuantity(@RequestBody ShoppingCartIDTO vo);
    
    @RequestMapping(value = "/shoppingcart/getSplitOrderInfo", method = RequestMethod.POST)
    public Map<Integer, Map<String, List<String>>> getSplitOrderInfo(@RequestBody SplitOrderIDto vo);
    
    @RequestMapping(value = "/shoppingcart/findDeliveryBatch",method=RequestMethod.GET)
   	public List<DeliveryBatchODTO> findDeliveryBatch(@RequestParam("storeId")Long storeId, @RequestParam("date") String date);

    @RequestMapping(value = "/shoppingcart/findDeliveryBatchByCartId",method=RequestMethod.GET)
    public List<DeliveryBatchODTO> findDeliveryBatchByCartId(@RequestParam("cartId")Long cartId, @RequestParam("date") String date);

    //(手持)购物车删除
    @RequestMapping(value = "/shoppingcart/handleRemoveShoppingCart", method = RequestMethod.POST)
    HandCartRspODTO handleRemoveShoppingCart(ShoppingCartIDTO vo);

    //(手持)购物车修改数量
    @RequestMapping(value = "/shoppingcart/handleUpdateItemQuantity", method = RequestMethod.POST)
    HandCartRspODTO handleUpdateItemQuantity(ShoppingCartIDTO vo);

    //批量修改购物车商品数量(以单个购物车为维度)
    @RequestMapping(value = "/shoppingcart/batchUpdateItemQuantity", method = RequestMethod.POST)
    void batchUpdateItemQuantity(ShoppingCartUpdateIDTO vo);

    //处理自动订货购物车
    @RequestMapping(value = "/shoppingcart/dealAutoShoppingCart", method = RequestMethod.POST)
    Boolean dealAutoShoppingCart();
}

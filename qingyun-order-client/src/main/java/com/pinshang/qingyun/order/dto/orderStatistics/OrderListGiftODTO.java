package com.pinshang.qingyun.order.dto.orderStatistics;

import lombok.Data;

import java.math.BigDecimal;

/**
 * 订单明细
 **/
@Data
public class OrderListGiftODTO{
	private Long id;
    private Long orderId;
    private Long commodityId;
    private BigDecimal commodityNum;
    private BigDecimal totalPrice;
    private BigDecimal commodityPrice;
    private Integer type;
    private String remark;
    /**
     * 商品类型-1-非组合 2-组合  3-组合子品
     */
    private Integer combType;
    /**
     * 属于组合商品id
     */
    private Long combCommodityId;

}
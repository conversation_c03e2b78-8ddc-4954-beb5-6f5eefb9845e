package com.pinshang.qingyun.order.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 *  退货收货人查寻
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CheckReceiverODTO {
    @ApiModelProperty("是否可以收货，true-可以，false-不可以")
    private Boolean flag;
    @ApiModelProperty("收货人名称")
    private String name;
    @ApiModelProperty("业务类型: 1.门店退货 2.领料退货")
    private Integer bizType;
}

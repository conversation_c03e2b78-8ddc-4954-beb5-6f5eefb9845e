package com.pinshang.qingyun.order.hystrix;

import com.github.pagehelper.PageInfo;
import com.pinshang.qingyun.order.dto.order.*;
import com.pinshang.qingyun.order.service.SaleReturnOrderClient;
import feign.hystrix.FallbackFactory;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * Created by crell on 2017/11/6.
 */
@Component
public class SaleReturnOrderCilentHystrix implements FallbackFactory<SaleReturnOrderClient> {


    @Override
    public SaleReturnOrderClient create(Throwable throwable) {
        return  new SaleReturnOrderClient() {

            @Override
            public PageInfo<SaleReturnODTO> getSaleReturnListByCondition(@RequestBody SaleReturnIDTO saleReturnIDTO) {
                return null;
            }

            @Override
            public SaleReturnInfoODTO showSaleReturn(@PathVariable String orderCode) {
                return null;
            }

            @Override
            public String addSaleReturn(@RequestBody SaleReturnAddIDTO saleReturnAddIDTO) {
                return null;
            }

            @Override
            public Integer cancelSaleReturn(String orderCode, Long userId) {
                return null;
            }

            @Override
            public Integer cancelSaleReturnWithReason(SaleReturnCancelIDTO idto) {
                return null;
            }

            @Override
            public List<SaleReturnItemODTO> copySaleReturn(@PathVariable("orderCode") String orderCode) {
                return null;
            }

            @Override
            public List<SaleReturnItemODTO> findCommodityByCode(@RequestBody SaleReturnCommodityIDTO saleReturnCommodityIDTO) {
                return null;
            }

            @Override
            public List<SaleReturnItemODTO> findCommodityByCommodityCode(SaleReturnCommodityIDTO saleReturnCommodityIDTO) {
                return null;
            }

            @Override
            public PageInfo<SaleReturnODTO> getSaleReturnSendList(@RequestBody SaleReturnIDTO saleReturnIDTO) {
                return null;
            }

            @Override
            public String confirmSaleReturn(@RequestBody SaleReturnAddIDTO saleReturnAddIDTO) {
                return null;
            }

            @Override
            public PdaReturnItemODTO pdaReturnByBarcode(Long shopId, String barCode, Long stallId) {
                return null;
            }

            @Override
            public PdaReturnItemODTO pdaShortByBarcode(Long shopId, String barCode, Long stallId) {
                return null;
            }

            @Override
            public Boolean pdaReturnAdd(SaleReturnAddIDTO saleReturnAddIDTO) {
                return null;
            }

            @Override
            public Boolean pdaShortAdd(SaleReturnAddIDTO saleReturnAddIDTO) {
                return null;
            }
        };
    }
}
 
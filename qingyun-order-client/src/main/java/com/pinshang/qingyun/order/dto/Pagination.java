package com.pinshang.qingyun.order.dto;

import lombok.Data;

import java.io.Serializable;

/**
 * 分页
 */
@Data
public class Pagination implements Serializable{
	 
	private static final long serialVersionUID = 1L;
	/**
	 * 当前页号, 默认第 1 页
	 */
	private Integer pageNo = 1;
	/**
	 * 每页显示记录数, 默认 10 条
	 */
	private Integer pageSize = 10;

	/**
	 * 导出时设置的分页参数
	 */
	public  void initExportPage(){
		this.pageNo = 1 ;
		this.pageSize = Integer.MAX_VALUE ;
	}

	public Integer getPageNo(){
		return  pageNo==null || pageNo<1 ? 1 : pageNo ;
	}
	
	
}

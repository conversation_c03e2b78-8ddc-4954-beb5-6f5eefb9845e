package com.pinshang.qingyun.order.hystrix;

import com.pinshang.qingyun.order.service.XDAOrderClient;
import feign.hystrix.FallbackFactory;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

@Component
public class XDAOrderClientHystrix implements FallbackFactory<XDAOrderClient> {
	private Logger logger = LoggerFactory.getLogger(getClass());
	
	@Override
	public XDAOrderClient create(Throwable throwable) {
		return new XDAOrderClient() {


			@Override
			public Integer autoCompleteXdaOrder(String orderTime) {
				return null;
			}

			@Override
			public Boolean updateSaleStatistics() {
				return null;
			}

			@Override
			public Boolean xdaCommoditySaleStatisticsDayReport(String orderTime) {
				return null;
			}


		};
	}
}

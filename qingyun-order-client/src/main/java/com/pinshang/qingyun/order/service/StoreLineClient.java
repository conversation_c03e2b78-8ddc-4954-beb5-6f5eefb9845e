package com.pinshang.qingyun.order.service;

import com.pinshang.qingyun.base.configure.FeignClientConfiguration;
import com.pinshang.qingyun.base.constant.ApplicationNameConstant;
import com.pinshang.qingyun.order.dto.DistributionLineODTO;
import com.pinshang.qingyun.order.hystrix.StoreLineClientHystrix;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2018/10/12 9:51.
 * @blog http://linuxsogood.org
 */
@FeignClient(value = ApplicationNameConstant.QINGYUN_ORDER_SERVICE, fallbackFactory = StoreLineClientHystrix.class, configuration = FeignClientConfiguration.class)
public interface StoreLineClient {

    @RequestMapping(value = "/storeLine/queryStoreLineByStoreIds",method = RequestMethod.POST)
    Map<Long, DistributionLineODTO> queryStoreLineByStoreIds(@RequestBody List<Long> storeIds);
}

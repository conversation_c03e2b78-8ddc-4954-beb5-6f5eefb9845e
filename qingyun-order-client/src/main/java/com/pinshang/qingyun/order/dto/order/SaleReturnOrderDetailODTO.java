package com.pinshang.qingyun.order.dto.order;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
public class SaleReturnOrderDetailODTO {

    @ApiModelProperty("客户名称 / 退领车间名称")
    private String storeName;
    @ApiModelProperty("退货单号")
    private String returnOrderCode;
    @ApiModelProperty("退货单日期 / 退领单创建日期")
    private Date returnOrderDate;
    @ApiModelProperty("仓库id")
    private Long warehouseId;
    @ApiModelProperty("仓库名称")
    private String warehouseName;
    @ApiModelProperty("备注")
    private String remark;
    @ApiModelProperty("明细信息")
    private List<SaleReturnOrderItemODTO> itemList;
    @ApiModelProperty("业务类型: 1.门店退货 2.领料退货")
    private Integer bizType;
}
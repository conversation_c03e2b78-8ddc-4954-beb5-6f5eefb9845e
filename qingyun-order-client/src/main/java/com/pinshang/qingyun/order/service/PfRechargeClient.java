package com.pinshang.qingyun.order.service;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import com.pinshang.qingyun.base.configure.FeignClientConfiguration;
import com.pinshang.qingyun.base.constant.ApplicationNameConstant;
import com.pinshang.qingyun.order.hystrix.PfRechargeClientHystrix;

@FeignClient(value = ApplicationNameConstant.QINGYUN_ORDER_SERVICE, fallbackFactory = PfRechargeClientHystrix.class, configuration = FeignClientConfiguration.class)
public interface PfRechargeClient {

    @RequestMapping(value = "/pf/recharge/pfAppRechargeJob", method = RequestMethod.GET)
    void pfAppRechargeJob();

}

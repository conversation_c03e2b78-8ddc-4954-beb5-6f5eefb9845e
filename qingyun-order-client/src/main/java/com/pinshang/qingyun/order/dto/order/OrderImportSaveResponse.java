package com.pinshang.qingyun.order.dto.order;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Collection;

/**
 * @Author: sk
 * @Date: 2024/12/25
 */
@Data
public class OrderImportSaveResponse {

    private static final long serialVersionUID = 1L;
    private Boolean success;
    private String code;
    private String message;

    @ApiModelProperty(value = "错误信息List集合对象String")
    private Collection<String> errMsgList;
    //返回数据对象
    private Object data;
}

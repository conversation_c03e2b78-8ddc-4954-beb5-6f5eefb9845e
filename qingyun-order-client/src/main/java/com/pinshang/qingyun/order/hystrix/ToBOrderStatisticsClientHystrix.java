package com.pinshang.qingyun.order.hystrix;

import com.github.pagehelper.PageInfo;
import com.pinshang.qingyun.order.dto.tob.*;
import com.pinshang.qingyun.order.service.ToBOrderStatisticsClient;
import feign.hystrix.FallbackFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;


@Component
@Slf4j
public class ToBOrderStatisticsClientHystrix implements FallbackFactory<ToBOrderStatisticsClient> {


    @Override
    public ToBOrderStatisticsClient create(Throwable throwable) {
        return new ToBOrderStatisticsClient() {


            @Override
            public void repairData(List<ToBOrderStatisticsRepairIDTO> repairIDTOList) {

            }

            @Override
            public Integer queryOrderStatisticsCount(ToBQueryOrderStatisticsIDTO idto) {
                return null;
            }

            @Override
            public PageInfo<ToBQueryOrderStatisticsODTO> queryOrderStatisticsList(ToBQueryOrderStatisticsIDTO idto) {
                return null;
            }

            @Override
            public ToBOrderAndTakeAppointmentDataODTO queryToBOrderAndTakeAppointmentData(ToBOrderAndTakeAppointmentDataIDTO dataIDTO) {
                return null;
            }
        };
    }
}

//package com.pinshang.qingyun.order.dto.commodity;
//
//import io.swagger.annotations.ApiModelProperty;
//import lombok.Data;
//
//import java.io.Serializable;
//import java.math.BigDecimal;
//import java.time.LocalTime;
//import java.time.format.DateTimeFormatter;
//
///**
// *
// * <AUTHOR>
// * @Date 2018/4/9 18:08
// */
//@Data
//public class ShipmentsODTO implements Serializable {
//
//    private static final long serialVersionUID = -5780942498188848254L;
//    /**
//     * 产品名称
//     */
//    @ApiModelProperty("产品名称")
//    private String commodityName;
//    /**
//     * 产品编码
//     */
//    @ApiModelProperty("产品编码")
//    private String commodityCode;
//    /**
//     * 车间名
//     */
//    @ApiModelProperty("车间名")
//    private String workshopName;
//    /**
//     * 产品总计
//     */
//    @ApiModelProperty("产品总计")
//    private BigDecimal quantity;
//
//    /**
//     * 更新时间
//     */
//    @ApiModelProperty("更新时间 HH:mm:ss")
//    private String updateTime = LocalTime.now().format(DateTimeFormatter.ofPattern("HH:mm:ss"));
//
//    /**
//     * 送货日期
//     */
//    @ApiModelProperty("送货时间 yyyy-MM-dd")
//    private String deliveryDate;
//
//    /**
//     * 线路组
//     */
//    @ApiModelProperty("线路组")
//    private String lineName;
//
//    /**
//     * 仓库名
//     */
//    @ApiModelProperty("仓库名")
//    private String warehouse;
//
//    /**
//     * 配送批次
//     */
//    @ApiModelProperty("配送批次")
//    private Integer deliveryBatch;
//
//}

package com.pinshang.qingyun.order.dto.mock;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class MockOrderItemIDTO {

    private Long commodityId;

    /**
     * 单价
     */
    @ApiModelProperty(value = "单价，自动生成两位小数，无须传参",hidden = true)
    private BigDecimal price;

    /**
     * 数量
     */
    @ApiModelProperty(value = "下单数量")
    private BigDecimal quantity;

}

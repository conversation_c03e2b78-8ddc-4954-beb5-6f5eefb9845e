package com.pinshang.qingyun.order.hystrix;

import com.pinshang.qingyun.order.dto.subOrder.StockOutJobSubOrderIDTO;
import com.pinshang.qingyun.order.dto.subOrder.StockOutJobSubOrderODTO;
import com.pinshang.qingyun.order.service.StockOutSubOrderClient;
import feign.hystrix.FallbackFactory;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public class StockOutSubOrderHystrix implements FallbackFactory<StockOutSubOrderClient> {
    @Override
    public StockOutSubOrderClient create(Throwable throwable) {
        return new StockOutSubOrderClient() {
            @Override
            public List<StockOutJobSubOrderODTO> getStockOutSubOrderList(StockOutJobSubOrderIDTO reqVo) {
                return null;
            }
        };
    }
}

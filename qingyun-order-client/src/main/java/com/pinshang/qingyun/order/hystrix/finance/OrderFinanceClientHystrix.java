package com.pinshang.qingyun.order.hystrix.finance;

import com.pinshang.qingyun.order.dto.finance.DirectDeliveryOrderIDTO;
import com.pinshang.qingyun.order.dto.finance.DirectDeliveryOrderODTO;
import com.pinshang.qingyun.order.dto.finance.ShopLessGoodsOrderIDTO;
import com.pinshang.qingyun.order.dto.finance.ShopLessGoodsOrderODTO;
import com.pinshang.qingyun.order.service.finance.OrderFinanceClient;
import feign.hystrix.FallbackFactory;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/7/2 14:57
 */
@Component
public class OrderFinanceClientHystrix implements FallbackFactory<OrderFinanceClient> {
    @Override
    public OrderFinanceClient create(Throwable cause) {
        return new OrderFinanceClient() {
            @Override
            public List<DirectDeliveryOrderODTO> selectDirectDeliveryOrderList(DirectDeliveryOrderIDTO idto) {
                return null;
            }

            @Override
            public List<ShopLessGoodsOrderODTO> selectShopLessGoodsOrderList(ShopLessGoodsOrderIDTO idto) {
                return null;
            }
        };
    }
}

package com.pinshang.qingyun.order.service;

import com.pinshang.qingyun.base.configure.FeignClientConfiguration;
import com.pinshang.qingyun.base.constant.ApplicationNameConstant;
import com.pinshang.qingyun.order.hystrix.SyncOrderJobClientHystrix;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;

@FeignClient(value = ApplicationNameConstant.QINGYUN_ORDER_SERVICE, fallbackFactory = SyncOrderJobClientHystrix.class, configuration = FeignClientConfiguration.class)
public interface SyncOrderJobClient {
	
	@RequestMapping(value = "/order/syncOrderAndSO2DO", method = RequestMethod.POST)
	void syncOrderAndSO2DO(@RequestParam("coverTime") String coverTime);
	
}

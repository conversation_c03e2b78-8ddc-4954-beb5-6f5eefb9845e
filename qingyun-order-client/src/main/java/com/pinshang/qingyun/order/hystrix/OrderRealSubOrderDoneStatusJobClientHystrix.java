package com.pinshang.qingyun.order.hystrix;

import com.pinshang.qingyun.order.dto.OrderRealSubOrderDoneStatusIDTO;
import com.pinshang.qingyun.order.service.OrderRealSubOrderDoneStatusJobClient;
import feign.hystrix.FallbackFactory;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2024/7/2 14:30
 */
@Component
public class OrderRealSubOrderDoneStatusJobClientHystrix implements FallbackFactory<OrderRealSubOrderDoneStatusJobClient> {
    @Override
    public OrderRealSubOrderDoneStatusJobClient create(Throwable throwable) {
        return new OrderRealSubOrderDoneStatusJobClient() {
            @Override
            public void processOrderRealSubOrderDoneStatus(OrderRealSubOrderDoneStatusIDTO vo) {
                return;
            }
        };
    }
}

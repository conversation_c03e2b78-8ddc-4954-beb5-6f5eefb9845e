package com.pinshang.qingyun.order.dto.order;/**
 * @Author: sk
 * @Date: 2025/7/9
 */

import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2025年07月09日 下午3:31
 */
@Data
public class XiaoeTongPushOrderIDTO {

    // 门店id
    private Long shopId;
    // 订单编码
    private Long orderId;
    // 订单时间 (yyyy-MM-dd)
    private String orderTime;
    // 商品
    private Long commodityId;
    // 订单数量
    private BigDecimal orderQuantity;
    // 门店向总部订货价
    private BigDecimal price;
    // 小鹅通订单下单时间(yyyy-MM-dd HH:mm:ss)
    private String orderCreateTime;
    // 是否下单成功 1=是  0=否
    private Integer orderStauts;
}

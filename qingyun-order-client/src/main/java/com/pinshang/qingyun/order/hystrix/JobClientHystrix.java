package com.pinshang.qingyun.order.hystrix;

import com.pinshang.qingyun.order.dto.ShopAutoOrderJobIDTO;
import com.pinshang.qingyun.order.service.JobClient;
import feign.hystrix.FallbackFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @Date 2018/10/10 10:54
 */
@Component
@Slf4j
public class JobClientHystrix implements FallbackFactory<JobClient> {
    @Override
    public JobClient create(Throwable throwable) {
        return new JobClient() {

            @Override
            public Boolean createTakeCardOrderJob() {
                return null;
            }

            @Override
            public Boolean executeTakeCardOrderJob(String orderTime) {
                return null;
            }

            @Override
            public Boolean createPurchaseOrderJob() {
                return null;
            }

            @Override
            public Boolean executePurchaseOrderJob(String orderTime) {
                return null;
            }

            @Override
            public Boolean createShopAutoOrderJob() {
                return null;
            }

            @Override
            public Boolean executeShopAutoOrderJob(ShopAutoOrderJobIDTO idto) {
                return null;
            }

            @Override
            public void subOrderItemCommodityPriceJob(String orderTime) {

            }
        };
    }
}

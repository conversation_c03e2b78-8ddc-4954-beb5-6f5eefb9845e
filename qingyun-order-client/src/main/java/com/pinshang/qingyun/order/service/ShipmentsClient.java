//package com.pinshang.qingyun.order.service;
//
//import com.pinshang.qingyun.base.configure.FeignClientConfiguration;
//import com.pinshang.qingyun.base.constant.ApplicationNameConstant;
//import com.pinshang.qingyun.order.dto.commodity.ShipmentsIDTO;
//import com.pinshang.qingyun.order.dto.commodity.ShipmentsODTO;
//import com.pinshang.qingyun.order.hystrix.ShipmentsClientHystrix;
//import org.springframework.cloud.openfeign.FeignClient;
//import org.springframework.web.bind.annotation.RequestBody;
//import org.springframework.web.bind.annotation.RequestMapping;
//import org.springframework.web.bind.annotation.RequestMethod;
//
//import java.util.List;
//
///**
// *
// * <AUTHOR>
// * @Date 2018/4/9 18:17
// */
//@FeignClient(value = ApplicationNameConstant.QINGYUN_ORDER_SERVICE, fallbackFactory = ShipmentsClientHystrix.class, configuration = FeignClientConfiguration.class)
//public interface ShipmentsClient {
//    @RequestMapping(value = "shipments/list",method = RequestMethod.POST)
//    List<ShipmentsODTO> list(@RequestBody ShipmentsIDTO vo);
//
//}

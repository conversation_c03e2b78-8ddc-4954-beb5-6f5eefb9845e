package com.pinshang.qingyun.order.dto.settlement;

import com.pinshang.qingyun.base.enums.settlement.SettleCompaintSourceTypeEnum;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.List;

@Data
@NoArgsConstructor
public class OrderReqIDTO {

    private List<Long> ids;

    private List<Integer> notOrderTypes;

    private List<Integer> orderTypes;

//    /**订单编号集合**/
//    private List<Long> subOrderIdList;

    private List<Long> storeIds;

    private List<String> orderSourceTypes;

    private List<String> compaintSourceTypes;


    /**送货日期-开始**/
    private String deliveryTimeStart;

    /**送货日期-结束**/
    private String deliveryTimeEnd;

    private int dataType;


    public OrderReqIDTO (List<Long> storeIds, String deliveryTimeStart, String deliveryTimeEnd,int dataType) {
        this.storeIds = storeIds;
        this.deliveryTimeStart = deliveryTimeStart;
        this.deliveryTimeEnd = deliveryTimeEnd;
        this.dataType=dataType;
        compaintSourceTypes = new ArrayList();
        compaintSourceTypes.add(SettleCompaintSourceTypeEnum.RT_ADJUST.name());
    }
}

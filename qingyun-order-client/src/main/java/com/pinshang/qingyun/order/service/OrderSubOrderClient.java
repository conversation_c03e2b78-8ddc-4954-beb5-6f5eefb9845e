package com.pinshang.qingyun.order.service;

import com.pinshang.qingyun.base.configure.FeignClientConfiguration;
import com.pinshang.qingyun.base.constant.ApplicationNameConstant;
import com.pinshang.qingyun.order.dto.commodity.SubOrderAndItemIDTO;
import com.pinshang.qingyun.order.hystrix.OrderSubOrderClientHystrix;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

@FeignClient(value = ApplicationNameConstant.QINGYUN_STORAGE_SERVICE, fallbackFactory = OrderSubOrderClientHystrix.class,configuration = FeignClientConfiguration.class)
public interface OrderSubOrderClient {

    @RequestMapping(value = "/subOrder/createSubOrderAndItem", method = RequestMethod.POST)
    public Long createSubOrderAndItem(@RequestBody SubOrderAndItemIDTO subOrderAndItemIDTO) throws Throwable;

}

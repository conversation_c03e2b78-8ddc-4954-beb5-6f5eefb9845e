package com.pinshang.qingyun.order.service;

import com.pinshang.qingyun.base.configure.FeignClientConfiguration;
import com.pinshang.qingyun.base.constant.ApplicationNameConstant;
import com.pinshang.qingyun.order.dto.settlement.SettlementODTO;
import com.pinshang.qingyun.order.hystrix.SettlementClientHystrix;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

@FeignClient(value = ApplicationNameConstant.QINGYUN_ORDER_SERVICE, fallbackFactory = SettlementClientHystrix.class, configuration = FeignClientConfiguration.class)
public interface SettlementClient {


    /**
     * 更新鲜食结算起始日期
     * @param id
     * @param date
     * @return
     */
    @RequestMapping(value = "/settlement/upXsStartBillDateBySettleId", method = RequestMethod.POST)
    String upXsStartBillDateBySettleId(@RequestParam("id")Long id, @RequestParam("date")String date);


    /**
     * 获取鲜食账单起始日期
     * @param id
     * @return
     */
    @RequestMapping(value = "/settlement/findXsStartBillDateById", method = RequestMethod.POST)
    String findXsStartBillDateById(@RequestParam("id")Long id);

    /**
     * 获取结账客户相关的客户IDs
     * @param settleId
     * @return
     */
    @RequestMapping(value ="/settlement/findStoreIdsBySettleId",method = RequestMethod.POST)
    public List<Long> findStoreIdsBySettleId(@RequestParam("settleId")Long settleId);

    /**
     * 根据code或者编码搜索 结账客户
     * @param str
     * @return
     */
    @RequestMapping(value ="/settlement/findByCodeOrName",method = RequestMethod.POST)
    public List<SettlementODTO> findByCodeOrName(@RequestParam("str")String str);

}

package com.pinshang.qingyun.order.dto.purchaseStatus;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
public class ShopCommodityModifyPurchaseStatusIDTO {

    @ApiModelProperty(position = 1,value = "门店id")
    private List<Long> shopId;

    @ApiModelProperty(position = 2,value = "商品编码")
    private String commodityCodes;

    @ApiModelProperty(position = 3,value = "原因")
    private String reason;

    @ApiModelProperty(position = 4,value = "是否可采状态")
    private Integer purchaseStatus;

    private Long userId;


}

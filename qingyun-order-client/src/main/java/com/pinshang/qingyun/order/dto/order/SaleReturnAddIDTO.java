package com.pinshang.qingyun.order.dto.order;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * Created by crell on 2017/11/6.
 */
@Data
public class SaleReturnAddIDTO {

    private Long storeId;

    private Long enterpriseId;

    @ApiModelProperty("退货少货商品列表")
    private List<SaleReturnItemAddIDTO> saleReturnItems;
    
    private Long createId;

    //退货单id
    private Long saleReturnOrderId;

    private Long shopId;

    /** 前置仓质检： true不扣库存    fasle扣库存 */
    private Boolean xdQuality = false;

    /** 档口id */
    private Long stallId;
}

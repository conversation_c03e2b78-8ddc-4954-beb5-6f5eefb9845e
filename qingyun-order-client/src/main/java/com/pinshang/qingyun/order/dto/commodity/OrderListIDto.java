package com.pinshang.qingyun.order.dto.commodity;

import com.pinshang.qingyun.order.dto.Pagination;

public class OrderListIDto extends Pagination{
	
	private static final long serialVersionUID = 1L;
	/** 门店id **/
	private Long storeId;
	/** 企业id **/
	private Long enterpriseId;

	private String startTime;
	
	private String endTime;
	
	private String orderCode;

	private Long createId;
	private Integer deliveryBatch;
	private Integer orderStatus;
	/** 档口id */
	private Long stallId;

	public Long getStallId() {
		return stallId;
	}

	public void setStallId(Long stallId) {
		this.stallId = stallId;
	}

	public Long getStoreId() {
		return storeId;
	}

	public void setStoreId(Long storeId) {
		this.storeId = storeId;
	}

	public Long getEnterpriseId() {
		return enterpriseId;
	}

	public void setEnterpriseId(Long enterpriseId) {
		this.enterpriseId = enterpriseId;
	}

	public String getStartTime() {
		return startTime;
	}

	public void setStartTime(String startTime) {
		this.startTime = startTime;
	}

	public String getEndTime() {
		return endTime;
	}

	public void setEndTime(String endTime) {
		this.endTime = endTime;
	}

	public String getOrderCode() {
		return orderCode;
	}

	public void setOrderCode(String orderCode) {
		this.orderCode = orderCode;
	}

	public Long getCreateId() {
		return createId;
	}

	public void setCreateId(Long createId) {
		this.createId = createId;
	}

	public Integer getDeliveryBatch() {
		return deliveryBatch;
	}

	public void setDeliveryBatch(Integer deliveryBatch) {
		this.deliveryBatch = deliveryBatch;
	}

	public Integer getOrderStatus() {
		return orderStatus;
	}

	public void setOrderStatus(Integer orderStatus) {
		this.orderStatus = orderStatus;
	}
}
package com.pinshang.qingyun.order.service;

import com.pinshang.qingyun.base.configure.FeignClientConfiguration;
import com.pinshang.qingyun.base.constant.ApplicationNameConstant;
import com.pinshang.qingyun.order.dto.subOrder.StockOutJobSubOrderIDTO;
import com.pinshang.qingyun.order.dto.subOrder.StockOutJobSubOrderODTO;
import com.pinshang.qingyun.order.hystrix.StockOutSubOrderHystrix;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

@FeignClient(value = ApplicationNameConstant.QINGYUN_ORDER_SERVICE, fallbackFactory = StockOutSubOrderHystrix.class, configuration = FeignClientConfiguration.class)
public interface StockOutSubOrderClient {
    @PostMapping("/stockOutJob/client/getStockOutSubOrderList")
    List<StockOutJobSubOrderODTO> getStockOutSubOrderList(@RequestBody StockOutJobSubOrderIDTO reqVo);
}

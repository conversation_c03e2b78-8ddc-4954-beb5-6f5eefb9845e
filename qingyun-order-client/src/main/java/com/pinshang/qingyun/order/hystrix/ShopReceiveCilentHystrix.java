package com.pinshang.qingyun.order.hystrix;

import com.github.pagehelper.PageInfo;
import com.pinshang.qingyun.order.dto.shop.*;
import com.pinshang.qingyun.order.service.ShopReceiveClient;
import feign.hystrix.FallbackFactory;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2017/6/7.
 */
@Component
public class ShopReceiveCilentHystrix implements FallbackFactory<ShopReceiveClient> {


    @Override
    public ShopReceiveClient create(Throwable throwable) {
        return  new ShopReceiveClient() {
            @Override
            public PageInfo<ShopReceiveODTO> getListByCondition(ShopReceiveIDTO shopReceiveIDTO) {
                return null;
            }

            @Override
            public ShopReceiveOrderInfoODTO showReceive(QuerySubOrderIDTO querySubOrderIDTO) {
                return null;
            }

            @Override
            public PageInfo <AuditInfoODTO> getAduitList(AuditQueryIDTO auditQueryIDTO) {
                return null;
            }

            @Override
            public void saveAduit(AuditSaveIDTO auditSaveIDTO) {

            }

            @Override
            public String addReceive(ReceiveOrderIDTO receiveOrderIDTO) {
                return null;
            }

            @Override
            public ReceiveAuditODTO getAuditDetail(AuditDetailIDTO auditDetailIDTO) {
                return null;
            }

			@Override
			public void newReceiveOrder(
					ShopReceiveOrderIDTO shopReceiveOrderIDTO) {
			}

            @Override
            public String getShopName(String userId) {
                return null;
            }

			@Override
			public void cancelReceiveOrder(
					ShopReceiveOrderIDTO shopReceiveOrderVo) throws Throwable {
				throw throwable.getCause();
			}

            @Override
            public PageInfo<ShopReceiveODTO> getPreOrderListByCondition(@RequestBody ShopReceiveIDTO shopReceiveIDTO) {
                return null;
            }

            @Override
            public ShopReceiveOrderInfoODTO showPreOrderReceive(@RequestBody QuerySubOrderIDTO querySubOrderIDTO) {
                return null;
            }

			@Override
			public String addPreReceive(ReceiveOrderIDTO receiveOrderIDTO) {
				return null;
			}

            @Override
            public void autoReceiveByDirstributionOk(String distributionCode, Long userId, List<Long> subOrderIds) {

            }
            @Override
            public PageInfo<ShopReceiveHQODTO> getPreOrderHQListByCondition(@RequestBody ShopReceiveHQIDTO shopReceiveHQIDTO) {
                return null;
            }

            @Override
            public Boolean autoReceive(String orderTime) {
                return null;
            }

            @Override
            public Boolean autoReceiveXsJm(String orderTime) {
                return null;
            }

            @Override
            public Boolean autoReceiveByQueue() {
                return null;
            }

            @Override
            public Boolean autoReceiveBigShop(String orderTime) {
                return null;
            }

            @Override
            public Boolean handleAutoReceive(String orderTime) {
                return null;
            }

            @Override
            public Boolean autoReceiveRemind() {
                return null;
            }

            @Override
            public Boolean handleReceiveRemind() {
                return null;
            }

            @Override
            public List<TjPreOrderODTO> queryTjPreOrderList(TjPreOrderIDTO preOrderIDTO) {
                return null;
            }


        };
    }
}
 
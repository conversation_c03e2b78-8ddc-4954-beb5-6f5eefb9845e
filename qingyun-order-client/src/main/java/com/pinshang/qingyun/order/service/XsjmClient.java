package com.pinshang.qingyun.order.service;

import com.pinshang.qingyun.base.configure.FeignClientConfiguration;
import com.pinshang.qingyun.base.constant.ApplicationNameConstant;
import com.pinshang.qingyun.order.hystrix.XsjmClientHystrix;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;

@FeignClient(value = ApplicationNameConstant.QINGYUN_ORDER_SERVICE, fallbackFactory = XsjmClientHystrix.class, configuration = FeignClientConfiguration.class)
public interface XsjmClient {

    /**
     * 自动日结
     */
    @PostMapping("/xsjm/autoSettleDaily")
    void autoSettleDaily();
}

package com.pinshang.qingyun.order.service;

import com.github.pagehelper.PageInfo;
import com.pinshang.qingyun.base.configure.FeignClientConfiguration;
import com.pinshang.qingyun.base.constant.ApplicationNameConstant;
import com.pinshang.qingyun.order.dto.SettlementStoreIDTO;
import com.pinshang.qingyun.order.dto.SettlementStoreODTO;
import com.pinshang.qingyun.order.dto.StoreIDTO;
import com.pinshang.qingyun.order.dto.StoreODTO;
import com.pinshang.qingyun.order.hystrix.SettleStoreClientHystrix;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;
import java.util.Map;

@FeignClient(value = ApplicationNameConstant.QINGYUN_ORDER_SERVICE, fallbackFactory = SettleStoreClientHystrix.class, configuration = FeignClientConfiguration.class)
public interface SettleStoreClient {
    /**
     * 根据客户id查询客户信息
     * @param storeIDTO
     * @return
     */
//    @RequestMapping(value = "/settleStore/selectStoreList", method = RequestMethod.POST)
//    Map<Long,StoreODTO> selectStoreList(@RequestBody StoreIDTO storeIDTO);
//
//    @RequestMapping(value = "/settleStore/selectSettlementStoreList")
//    Map<Long,SettlementStoreODTO> selectSettlementStoreList(@RequestBody StoreIDTO storeIDTO);

    /**
     * 条件查询客户列表
     * @param storeIDTO
     * @return
     */
    @RequestMapping(value = "/settleStore/selectStore", method = RequestMethod.POST)
    PageInfo<StoreODTO> selectStore(@RequestBody StoreIDTO storeIDTO);

    /**
     * 条件查询结账客户
     */
    @RequestMapping(value = "/settleStore/selectSettleStore", method = RequestMethod.POST)
    PageInfo<SettlementStoreODTO> selectSettleStore(@RequestBody SettlementStoreIDTO settlementStoreIDTO);

    /**
     * 批量添加客户
     * @return
     */
    @RequestMapping(value = "/settleStore/selectStoreByStores", method = RequestMethod.GET)
    List<StoreODTO> selectStoreByStores(@RequestParam("storeCodes") String storeCodes);

}

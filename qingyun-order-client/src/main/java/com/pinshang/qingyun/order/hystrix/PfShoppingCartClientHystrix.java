package com.pinshang.qingyun.order.hystrix;

import com.pinshang.qingyun.order.service.PfShoppingCartClient;
import feign.hystrix.FallbackFactory;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

@Component
public class PfShoppingCartClientHystrix implements FallbackFactory<PfShoppingCartClient> {
	private Logger logger = LoggerFactory.getLogger(getClass());
	
	@Override
	public PfShoppingCartClient create(Throwable throwable) {
		return new PfShoppingCartClient() {


			@Override
			public Integer getCategoryNum(String orderDate, Long storeId) {
				return null;
			}
		};
	}
}

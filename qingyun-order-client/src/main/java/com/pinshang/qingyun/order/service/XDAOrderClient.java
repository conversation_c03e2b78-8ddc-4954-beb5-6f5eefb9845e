package com.pinshang.qingyun.order.service;

import com.pinshang.qingyun.base.configure.FeignClientConfiguration;
import com.pinshang.qingyun.base.constant.ApplicationNameConstant;
import com.pinshang.qingyun.order.hystrix.XDAOrderClientHystrix;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;

@FeignClient(value = ApplicationNameConstant.QINGYUN_ORDER_SERVICE, fallbackFactory = XDAOrderClientHystrix.class, configuration = FeignClientConfiguration.class)
public interface XDAOrderClient {

    @RequestMapping(value = "/xda/order/job/complete", method = RequestMethod.GET)
    Integer autoCompleteXdaOrder(@RequestParam(value = "date",required = false) String date);

    @RequestMapping(value = "/xda/order/job/updateSaleStatistics", method = RequestMethod.GET)
    Boolean updateSaleStatistics();

    /**
     * job每天汇总鲜达商品销量日统计
     * t_xda_commodity_sale_day_statistics
     * @param orderTime
     * @return
     */
    @RequestMapping(value = "/xda/orderV4/xdaCommoditySaleStatisticsDayReport", method = RequestMethod.GET)
    Boolean xdaCommoditySaleStatisticsDayReport(@RequestParam(value = "orderTime",required = false) String orderTime);
}

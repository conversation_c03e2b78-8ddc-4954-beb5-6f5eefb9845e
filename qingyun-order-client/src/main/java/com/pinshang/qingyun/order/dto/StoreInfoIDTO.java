package com.pinshang.qingyun.order.dto;

import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 *  查询待收货/收货中的客户
 */
@Data
@ApiModel
public class StoreInfoIDTO {
    @ApiModelProperty("仓库ID")
    private Long warehouseId;
    @ApiModelProperty("开始时间 yyyy-MM-dd")
    private String beginTime;
    @ApiModelProperty("结束时间 yyyy-MM-dd")
    private String endTime;
    @ApiModelProperty("客户名称/ 车间名称")
    private String storeName;
    @ApiModelProperty(hidden = true)
    /**
     * 当前操作的用户id
     */
    private Long receiverId;
}

package com.pinshang.qingyun.order.service;

import com.pinshang.qingyun.base.configure.FeignClientConfiguration;
import com.pinshang.qingyun.base.constant.ApplicationNameConstant;
import com.pinshang.qingyun.order.hystrix.TakeCardOrderBillClientHystrix;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;

@FeignClient(value = ApplicationNameConstant.QINGYUN_ORDER_SERVICE, fallbackFactory = TakeCardOrderBillClientHystrix.class, configuration = FeignClientConfiguration.class)
public interface TakeCardOrderBillClient {
    /**
     * 拉取陈奇提货卡流水
     * @param appointDate
     * @return
     */
    @RequestMapping(value = "/takeCardOrderBill/pullTakeBill", method = RequestMethod.POST)
    String pullTakeBill(@RequestParam(value = "appointDate", required = false) String appointDate);

    /**
     * 基于提货卡流水 生成订单
     * @return
     */
    @RequestMapping(value = "/takeCardOrderBill/generateCardOrder", method = RequestMethod.POST)
     String generateCardOrder();


}

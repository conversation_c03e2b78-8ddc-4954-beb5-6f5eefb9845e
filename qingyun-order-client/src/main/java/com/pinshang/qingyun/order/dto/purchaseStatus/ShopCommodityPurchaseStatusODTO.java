package com.pinshang.qingyun.order.dto.purchaseStatus;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class ShopCommodityPurchaseStatusODTO {
    @ApiModelProperty(position = 1,value = "门店编码")
    private String shopCode;//门店编码
    @ApiModelProperty(position = 2,value = "门店名称")
    private String shopName;//门店名称
    @ApiModelProperty(position = 3,value = "商品编码")
    private String commodityCode;//商品编码
    @ApiModelProperty(position = 4,value = "条码")
    private String barCode;//商品条码
    @ApiModelProperty(position = 5,value = "商品名称")
    private String commodityName;//商品名称
    @ApiModelProperty(position = 6,value = "规格")
    private String commoditySpec;//包装规格
    private Long commodityUnitId;
    @ApiModelProperty(position = 7,value = "计量单位")
    private String commodityUnitName;//计量单位
    private Integer commodityPurchaseStatus;
    @ApiModelProperty(position = 8,value = "是否可采")
    private String commodityPurchaseStatusName;//可采状态
    private Integer commodityState;
    @ApiModelProperty(position = 9,value = "总部是否可售")
    private String commodityStateName;//状态：0-不可售,1-可售
    @ApiModelProperty(position = 10,value = "包装规格")
    private BigDecimal commodityPackageSpec;//包装规格
    @ApiModelProperty(position = 11,value = "销售箱规")
    private BigDecimal salesBoxCapacity;//销售箱规
    @ApiModelProperty(position = 12,value = "销售箱规(大店)")
    private BigDecimal xdSalesBoxCapacity;//前置仓销售箱规
    private Integer isWeight;
    @ApiModelProperty(position = 13,value = "是否称重")
    private String isWeightName;//是否称重
    private Long commodityPackageId;
    @ApiModelProperty(position = 14,value = "包装类型")
    private String commodityPackageKind;//包装类型ID
    @ApiModelProperty(position = 15,value = "品类")
    private String categoryName;
    @ApiModelProperty(position = 16,value = "APP上架")
    private String appStatusName;
    @ApiModelProperty(position = 17,value = "前台品名")
    private String commodityAppName;
}

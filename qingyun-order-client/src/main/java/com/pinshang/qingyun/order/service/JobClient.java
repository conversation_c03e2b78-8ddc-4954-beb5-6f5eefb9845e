package com.pinshang.qingyun.order.service;

import com.pinshang.qingyun.base.configure.FeignClientConfiguration;
import com.pinshang.qingyun.base.constant.ApplicationNameConstant;
import com.pinshang.qingyun.order.dto.ShopAutoOrderJobIDTO;
import com.pinshang.qingyun.order.hystrix.JobClientHystrix;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * <AUTHOR>
 * @Date 2018/10/10 10:52
 */
@FeignClient(value = ApplicationNameConstant.QINGYUN_ORDER_SERVICE, fallbackFactory = JobClientHystrix.class, configuration = FeignClientConfiguration.class)
public interface JobClient {

    /**
     * 创建提货卡job
     * @return
     */
    @RequestMapping(value = "/orderJob/createTakeCardOrderJob",method = RequestMethod.POST)
    Boolean createTakeCardOrderJob();
    /**
     * 执行提货卡job
     * @return
     */
    @RequestMapping(value = "/orderJob/executeTakeCardOrderJob",method = RequestMethod.POST)
    Boolean executeTakeCardOrderJob(@RequestParam("orderTime") String orderTime);


    /**
     * 创建生成采购单job
     * @return
     */
    @RequestMapping(value = "/orderJob/createPurchaseOrderJob",method = RequestMethod.POST)
    Boolean createPurchaseOrderJob();
    /**
     * 执行生成采购单job
     * @return
     */
    @RequestMapping(value = "/orderJob/executePurchaseOrderJob",method = RequestMethod.POST)
    Boolean executePurchaseOrderJob(@RequestParam("orderTime") String orderTime);


    /**
     * 创建门店自动订货job
     * @return
     */
    @RequestMapping(value = "/orderJob/createShopAutoOrderJob",method = RequestMethod.POST)
    Boolean createShopAutoOrderJob();
    /**
     * 执行门店自动订货job
     * @return
     */
    @RequestMapping(value = "/orderJob/executeShopAutoOrderJob",method = RequestMethod.POST)
    Boolean executeShopAutoOrderJob(@RequestBody ShopAutoOrderJobIDTO idto);

    @RequestMapping(value = "/orderJob/subOrderItemCommodityPriceJob",method = RequestMethod.POST)
    void subOrderItemCommodityPriceJob(@RequestParam("orderTime") String orderTime);
}

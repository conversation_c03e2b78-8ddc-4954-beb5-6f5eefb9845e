package com.pinshang.qingyun.order.dto.order;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@Data
public class OrderReplenishmentListODTO {

    private String id;

    @ApiModelProperty(position = 1,value = "门店编码")
    private String shopCode;

    @ApiModelProperty(position = 2,value = "门店名称")
    private String shopName;

    private Integer logisticsModel;

    @ApiModelProperty(position = 3,value = "订单编号")
    private String orderCode;

    @ApiModelProperty(position = 4,value = "预订单编码")
    private String subOrderCode;

    @ApiModelProperty(position = 5,value = "订单金额")
    private BigDecimal totalPrice;

    private String status;

    @ApiModelProperty(position = 6,value = "送货日期")
    private Date orderTime;

    @ApiModelProperty(position = 7,value = "创建时间")
    private Date createTime;

    //下单人
    @ApiModelProperty(position = 1,value = "创建人")
    private String createName;

}

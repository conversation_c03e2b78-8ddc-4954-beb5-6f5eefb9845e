package com.pinshang.qingyun.order.hystrix;

import com.pinshang.qingyun.order.dto.auto.AutoCommodityVO;
import com.pinshang.qingyun.order.dto.auto.AutoSaveOrderIDTO;
import com.pinshang.qingyun.order.service.AutoOrderSaveClient;
import com.pinshang.qingyun.order.service.AutoShopCommodityClient;
import feign.hystrix.FallbackFactory;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;

@Component
public class AutoOrderSaveClientHystrix implements FallbackFactory<AutoOrderSaveClient> {
	
	@Override
	public AutoOrderSaveClient create(Throwable throwable) {
		return new AutoOrderSaveClient() {

			@Override
			public Boolean saveAutoOrder(AutoSaveOrderIDTO saveOrderIDTO) {
				return null;
			}
		};
	}
}

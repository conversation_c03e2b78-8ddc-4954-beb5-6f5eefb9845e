package com.pinshang.qingyun.order.hystrix;

import com.pinshang.qingyun.order.dto.EmployeePrinterODTO;
import com.pinshang.qingyun.order.dto.StorePrinterODTO;
import com.pinshang.qingyun.order.dto.SystemPrinterODTO;
import com.pinshang.qingyun.order.service.SystemPrinterClient;
import feign.hystrix.FallbackFactory;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public class SystemPrinterClientHystrix implements FallbackFactory<SystemPrinterClient> {
    @Override
    public SystemPrinterClient create(Throwable throwable) {
        return new SystemPrinterClient() {


            @Override
            public List<SystemPrinterODTO> querySystemPrinterByUserId(Long userId) {
                return null;
            }

            @Override
            public EmployeePrinterODTO queryEmployeePrinterByDeliveryManCode(String employeeCode) {
                return null;
            }

            @Override
            public List<StorePrinterODTO> queryStorePrinterByStoreCode(String storeCode) {
                return null;
            }
        };
    }

}

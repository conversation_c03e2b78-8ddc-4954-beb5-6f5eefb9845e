package com.pinshang.qingyun.order.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class StoreODTO {
    @ApiModelProperty(position = 1,value = "客户id")
    private String scopeId;

    @ApiModelProperty(position = 2,value = "客户编码")
    private String storeCode;

    @ApiModelProperty(position = 3,value = "客户名称")
    private String storeName;

    @ApiModelProperty(position = 4,value = "客户状态")
    private String storeStatus;

    @ApiModelProperty(position = 5,value = "门店名称")
    private String shopName;

}

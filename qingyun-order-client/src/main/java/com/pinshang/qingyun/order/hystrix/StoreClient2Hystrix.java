package com.pinshang.qingyun.order.hystrix;

import com.github.pagehelper.PageInfo;
import com.pinshang.qingyun.order.dto.shop.StoreAccountIDTO;
import com.pinshang.qingyun.order.dto.shop.StoreAccountODTO;
import com.pinshang.qingyun.order.dto.store.DistributionSortStoreIDTO;
import com.pinshang.qingyun.order.dto.store.DistributionSortStoreODTO;
import com.pinshang.qingyun.order.dto.store.StoreInviceODTO;
import com.pinshang.qingyun.order.dto.store.StoreODTO;
import com.pinshang.qingyun.order.service.StoreClient2;
import feign.hystrix.FallbackFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 *
 * <AUTHOR>
 * @Date 2018/4/9 17:55
 */
@Component
@Slf4j
public class StoreClient2Hystrix implements FallbackFactory<StoreClient2> {

    @Override
    public StoreClient2 create(Throwable throwable) {
        return new StoreClient2() {
            @Override
            public PageInfo<StoreAccountODTO> list(StoreAccountIDTO vo) {
                return null;
            }

            @Override
            public StoreODTO getByCode(String storeCode) {
                return null;
            }

            @Override
            public PageInfo<DistributionSortStoreODTO> distributionList(DistributionSortStoreIDTO idto) {
                return null;
            }

            @Override
            public List<Long> handleExcelImportInfo(List<String> storeCodes) {
                return null;
            }

            @Override
            public List<StoreODTO> findStoreListByStoreIdList(List<Long> storeIdList) {
                return null;
            }

            @Override
            public List<StoreODTO> findStoreListByStoreName(String storeName) {
                return null;
            }

            @Override
            public StoreODTO findStoreByStoreId(Long storeId) {
                return null;
            }

            @Override
            public List<StoreODTO> findAllShopStoreList() {
                return null;
            }

            @Override
            public List<StoreInviceODTO> findListByStoreIds(List<Long> storeIds) {
                return null;
            }

            @Override
            public StoreInviceODTO findInvoiceById(String uid) {
                return null;
            }

        };
    }
}

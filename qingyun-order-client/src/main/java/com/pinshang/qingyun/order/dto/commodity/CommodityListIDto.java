package com.pinshang.qingyun.order.dto.commodity;

import com.pinshang.qingyun.order.dto.Pagination;

public class CommodityListIDto extends Pagination{

	private static final long serialVersionUID = 1L;

	private Long enterpriseId;
	
	private String storeId;
	
	private String commodityKey;
	
	private Long categoryId;
	
	private Long supplerId;
	
	private boolean isPromotionProduct;//是否促销

	private boolean isNew;//新品
	
	private String orderTime;

	private Integer logisticsModel;//物流模式

	private String barCode;//条形码

	private Long warehouseId;//仓库
	private Boolean showZero;//是否展示数量为0的商品

	private Long checkGroupId; //考核组id

	private Boolean autoCommodity; // 是否自定订货

	/** app状态：0-上架，1-下架 */
	private Integer appStatus;
	private Long shopId;
	private boolean freshCommodity;

	/** 档口id */
	private Long stallId;

	public Long getStallId() {
		return stallId;
	}

	public void setStallId(Long stallId) {
		this.stallId = stallId;
	}

	public boolean isFreshCommodity() {
		return freshCommodity;
	}

	public void setFreshCommodity(boolean freshCommodity) {
		this.freshCommodity = freshCommodity;
	}

	public Long getShopId() {
		return shopId;
	}
	public void setShopId(Long shopId) {
		this.shopId = shopId;
	}
	public Integer getAppStatus() {
		return appStatus;
	}

	public void setAppStatus(Integer appStatus) {
		this.appStatus = appStatus;
	}

	public Boolean getAutoCommodity() {
		return autoCommodity;
	}

	public void setAutoCommodity(Boolean autoCommodity) {
		this.autoCommodity = autoCommodity;
	}

	public Long getCheckGroupId() {
		return checkGroupId;
	}

	public void setCheckGroupId(Long checkGroupId) {
		this.checkGroupId = checkGroupId;
	}

	public Boolean isShowZero() {
		return showZero;
	}

	public void setShowZero(boolean showZero) {
		this.showZero = showZero;
	}

	public Integer getLogisticsModel() {
		return logisticsModel;
	}

	public void setLogisticsModel(Integer logisticsModel) {
		this.logisticsModel = logisticsModel;
	}

	public String getBarCode() {
		return barCode;
	}

	public void setBarCode(String barCode) {
		this.barCode = barCode;
	}

	public Long getWarehouseId() {
		return warehouseId;
	}

	public void setWarehouseId(Long warehouseId) {
		this.warehouseId = warehouseId;
	}

	public String getStoreId() {
		return storeId;
	}

	public void setStoreId(String storeId) {
		this.storeId = storeId;
	}

	public Long getCategoryId() {
		return categoryId;
	}

	public void setCategoryId(Long categoryId) {
		this.categoryId = categoryId;
	}

	public Long getSupplerId() {
		return supplerId;
	}

	public void setSupplerId(Long supplerId) {
		this.supplerId = supplerId;
	}

	public boolean isPromotionProduct() {
		return isPromotionProduct;
	}

	public void setPromotionProduct(boolean isPromotionProduct) {
		this.isPromotionProduct = isPromotionProduct;
	}

	public String getCommodityKey() {
		return commodityKey;
	}

	public void setCommodityKey(String commodityKey) {
		this.commodityKey = commodityKey;
	}

	public Long getEnterpriseId() {
		return enterpriseId;
	}

	public void setEnterpriseId(Long enterpriseId) {
		this.enterpriseId = enterpriseId;
	}

	public String getOrderTime() {
		return orderTime;
	}

	public void setOrderTime(String orderTime) {
		this.orderTime = orderTime;
	}

	public boolean isNew() {
		return isNew;
	}

	public void setNew(boolean aNew) {
		isNew = aNew;
	}
}

package com.pinshang.qingyun.order.hystrix;

import com.pinshang.qingyun.order.dto.commodity.CommodityMonitorIDTO;
import com.pinshang.qingyun.order.dto.commodity.CommodityMonitorODTO;
import com.pinshang.qingyun.order.service.CommodityMonitorClient;
import feign.hystrix.FallbackFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @Date 2018/10/11 13:42
 */
@Component
@Slf4j
public class CommodityMonitorClientHystrix implements FallbackFactory<CommodityMonitorClient> {
    @Override
    public CommodityMonitorClient create(Throwable throwable) {
        return new CommodityMonitorClient() {
            @Override
            public CommodityMonitorODTO orderInfo(CommodityMonitorIDTO idto) {
                return null;
            }
        };
    }
}

package com.pinshang.qingyun.order.hystrix;

import com.github.pagehelper.PageInfo;
import com.pinshang.qingyun.order.dto.shop.SettlementDetailsReportIDTO;
import com.pinshang.qingyun.order.dto.shop.SettlementDetailsReportODTO;
import com.pinshang.qingyun.order.service.SettlementDetailsReportClient;
import feign.hystrix.FallbackFactory;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.List;

/**
 * Created by hhf on 2019/1/16.
 * 结算明细报表
 */
@Component
public class SettlementDetailsReportClientHystrix implements FallbackFactory<SettlementDetailsReportClient> {
    @Override
    public SettlementDetailsReportClient create(Throwable cause) {
        return new SettlementDetailsReportClient() {
            @Override
            public PageInfo<SettlementDetailsReportODTO> settlementDetailsReport(SettlementDetailsReportIDTO dto) {
                return null;
            }

            @Override
            public BigDecimal findTotalSettlePrice(SettlementDetailsReportIDTO dto) {
                return null;
            }

            @Override
            public List<String> getStoreTypeIdList() {
                return null;
            }
        };
    }
}

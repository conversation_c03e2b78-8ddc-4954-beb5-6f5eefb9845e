package com.pinshang.qingyun.order.dto.shop;

import com.pinshang.qingyun.order.dto.Pagination;

import java.util.Date;

public class PriceModelLogODTO extends Pagination {
    private String shopName;
    //三级分类名称
    private String categoryName;
    //条形码
    private String barCode;
    //商品代码
    private String commodityCode;
    //商品名称
    private String commodityName;
    //商品规格型号
    private String commoditySpec;
    //计量单位
    private String unit;
    //调整通过日期
    private Date adjustTime;
    //原订货价
    private String oldPrice;
    //订货价
    private String price;
    //条形码集合
    private String barCodes;

    private Long productPriceModelId;

    private String refListCode;

    public Long getProductPriceModelId() {
        return productPriceModelId;
    }

    public void setProductPriceModelId(Long productPriceModelId) {
        this.productPriceModelId = productPriceModelId;
    }

    public Date getAdjustTime() {
        return adjustTime;
    }

    public void setAdjustTime(Date adjustTime) {
        this.adjustTime = adjustTime;
    }

    public String getOldPrice() {
        return oldPrice;
    }

    public void setOldPrice(String oldPrice) {
        this.oldPrice = oldPrice;
    }

    public String getPrice() {
        return price;
    }

    public void setPrice(String price) {
        this.price = price;
    }

    public String getRefListCode() {
        return refListCode;
    }

    public void setRefListCode(String refListCode) {
        this.refListCode = refListCode;
    }

    public String getShopName() {
        return shopName;
    }

    public void setShopName(String shopName) {
        this.shopName = shopName;
    }

    public String getCategoryName() {
        return categoryName;
    }

    public void setCategoryName(String categoryName) {
        this.categoryName = categoryName;
    }

    public String getBarCode() {
        return barCode;
    }

    public void setBarCode(String barCode) {
        this.barCode = barCode;
    }

    public String getCommodityCode() {
        return commodityCode;
    }

    public void setCommodityCode(String commodityCode) {
        this.commodityCode = commodityCode;
    }

    public String getCommodityName() {
        return commodityName;
    }

    public void setCommodityName(String commodityName) {
        this.commodityName = commodityName;
    }

    public String getCommoditySpec() {
        return commoditySpec;
    }

    public void setCommoditySpec(String commoditySpec) {
        this.commoditySpec = commoditySpec;
    }

    public String getUnit() {
        return unit;
    }

    public void setUnit(String unit) {
        this.unit = unit;
    }

    public String getBarCodes() {
        return barCodes;
    }

    public void setBarCodes(String barCodes) {
        this.barCodes = barCodes;
    }
}

package com.pinshang.qingyun.order.dto.commodity;

import com.pinshang.qingyun.order.dto.Pagination;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class CommodityListHandleIDto extends Pagination{

	private Long enterpriseId;

	private String storeId;

	@ApiModelProperty("商品关键字")
	private String commodityKey;

	@ApiModelProperty("商分类ID")
	private Long categoryId;

	@ApiModelProperty("供应商ID")
	private Long supplerId;

	@ApiModelProperty("是否促销")
	private boolean isPromotionProduct=false;//是否促销\

	@ApiModelProperty("称重商品")
	private boolean isWeight=false;//称重商品

	@ApiModelProperty("标品")
	private boolean isNormal=false;//标品

	@ApiModelProperty("新品")
	private boolean isNew=false;//新品

	private String barCode;//条形码
	private Long commodityId;//商品ID

	private Long shopId;

	/** 档口id */
	private Long stallId;
}

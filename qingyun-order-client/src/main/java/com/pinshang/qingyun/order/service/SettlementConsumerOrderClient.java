package com.pinshang.qingyun.order.service;

import com.pinshang.qingyun.base.configure.FeignClientConfiguration;
import com.pinshang.qingyun.base.constant.ApplicationNameConstant;
import com.pinshang.qingyun.order.hystrix.SettlementConsumerOrderClientHystrix;
import com.pinshang.qingyun.order.hystrix.XDShoppingCartClientHystrix;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;

@FeignClient(value = ApplicationNameConstant.QINGYUN_ORDER_SERVICE, fallbackFactory = SettlementConsumerOrderClientHystrix.class, configuration = FeignClientConfiguration.class)
public interface SettlementConsumerOrderClient {

    @RequestMapping(value = "/settlementConsumerOrder/orderSave", method = RequestMethod.POST)
    Long orderSave(@RequestParam("str") String str);

}

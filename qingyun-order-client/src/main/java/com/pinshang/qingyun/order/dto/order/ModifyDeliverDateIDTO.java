package com.pinshang.qingyun.order.dto.order;

import com.pinshang.qingyun.order.dto.Pagination;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * summary.
 * <p>
 * detailed description
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2018/7/25
 */
@Data
public class ModifyDeliverDateIDTO extends Pagination {

    @ApiModelProperty(position = 1, required = true, value = "门店id")
    private Long shopId;

    @ApiModelProperty(position = 2, value = "送货日期", example = "yyyy-MM-dd")
    private String orderTime;

    @ApiModelProperty(position = 3, value = "订单号")
    private String orderCode;

    @ApiModelProperty(position = 4, value = "配送批次(0:无需批次配送, 1:1配, 2:2配, 3:3配, 9:临时批次)")
    private Integer deliveryBatch;

    @ApiModelProperty(position = 5, value = "创建人姓名或编号")
    private String createId;
}

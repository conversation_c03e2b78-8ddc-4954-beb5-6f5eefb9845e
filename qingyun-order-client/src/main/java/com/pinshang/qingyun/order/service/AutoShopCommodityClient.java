package com.pinshang.qingyun.order.service;

import com.pinshang.qingyun.base.configure.FeignClientConfiguration;
import com.pinshang.qingyun.base.constant.ApplicationNameConstant;
import com.pinshang.qingyun.order.dto.auto.AutoCommodityVO;
import com.pinshang.qingyun.order.hystrix.AutoShopCommodityClientHystrix;
import com.pinshang.qingyun.order.hystrix.PfOrderClientHystrix;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.math.BigDecimal;

@FeignClient(value = ApplicationNameConstant.QINGYUN_ORDER_SERVICE, fallbackFactory = AutoShopCommodityClientHystrix.class, configuration = FeignClientConfiguration.class)
public interface AutoShopCommodityClient {

    @GetMapping("/autoShopCommodity/commodityInfo")
    AutoCommodityVO commodityInfo(@RequestParam(value = "barCode", required = false) String barCode);

    @GetMapping("/autoShopCommodity/insertAutoOrderCommodity")
    Boolean insertAutoOrderCommodity(@RequestParam(value = "commodityId", required = false) Long commodityId, @RequestParam(value = "stockQuantity", required = false) BigDecimal stockQuantity);
}

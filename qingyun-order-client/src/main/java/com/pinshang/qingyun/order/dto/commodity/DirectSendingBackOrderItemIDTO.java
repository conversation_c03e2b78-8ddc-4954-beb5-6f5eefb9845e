package com.pinshang.qingyun.order.dto.commodity;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import lombok.ToString;

import java.math.BigDecimal;

/**
 * summary.
 * <p>
 * detailed description
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2018/8/10
 */
@Data
@ToString
@AllArgsConstructor
@RequiredArgsConstructor
public class DirectSendingBackOrderItemIDTO {

    @ApiModelProperty(position = 1, value = "门店id")
    private Long shopId;

    @ApiModelProperty(position = 2, value = "商品id")
    private Long productId;

    @ApiModelProperty(position = 3, value = "商品价格")
    private BigDecimal price;

    @ApiModelProperty(position = 4, value = "商品数量")
    private BigDecimal productNum;

    /** 辅助字段 */
    @ApiModelProperty(hidden = true)
    private Integer type = 1;
    @ApiModelProperty(hidden = true)
    private Integer status = 1;
}

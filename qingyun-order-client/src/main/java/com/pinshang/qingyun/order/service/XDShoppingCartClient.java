package com.pinshang.qingyun.order.service;

import com.pinshang.qingyun.base.configure.FeignClientConfiguration;
import com.pinshang.qingyun.base.constant.ApplicationNameConstant;
import com.pinshang.qingyun.order.hystrix.XDShoppingCartClientHystrix;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

@FeignClient(value = ApplicationNameConstant.QINGYUN_ORDER_SERVICE, fallbackFactory = XDShoppingCartClientHystrix.class, configuration = FeignClientConfiguration.class)
public interface XDShoppingCartClient {

    @RequestMapping(value = "/xdShoppingcart/batchAddXdShoppingCartTemp", method = RequestMethod.POST)
    void batchAddXdShoppingCartTemp();

    @RequestMapping(value = "/xdShoppingcart/batchAddXdShoppingCart", method = RequestMethod.POST)
    void batchAddXdShoppingCart();
}

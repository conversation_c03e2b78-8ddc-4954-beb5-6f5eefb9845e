package com.pinshang.qingyun.order.dto.commodity;


import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

@Data
@NoArgsConstructor
public class CommodityPurchaseStatusIDTO {
    //门店id
    private Long shopId;
    //商品id
    private Long commodityId;
    //商品是否可采
    private Integer commodityPurchaseStatus;
    //创建人
    private Long createId;
    //创建时间
    private Date createTime;
    //修改人
    private Long updateId;
    //修改时间
    private Date updateTime;

    public CommodityPurchaseStatusIDTO (Long shopId,Long commodityId,Integer commodityPurchaseStatus,Long createId,
                                        Date createTime,Long updateId,Date updateTime){
        this.shopId = shopId;
        this.commodityId =commodityId;
        this.commodityPurchaseStatus = commodityPurchaseStatus;
        this.createId = createId;
        this.createTime = createTime;
        this.updateId = updateId;
        this.updateTime = updateTime;
    }
}

package com.pinshang.qingyun.order.dto.tob;


import com.pinshang.qingyun.order.dto.Pagination;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Description：
 * @Author：ZhangHui
 * @Package：com.pinshang.qingyun.order.dto.tob
 * @Date: 2024/4/12
 */
@Data
@ApiModel(value = "ToBQueryOrderStatisticsIDTO", description = "订单商品统计入参查询")
public class ToBQueryOrderStatisticsIDTO extends Pagination {

    @ApiModelProperty("送货开始日期")
    private String startOrderTime;

    @ApiModelProperty("送货结束日期")
    private String endOrderTime;

    @ApiModelProperty(value = "商品ID")
    private String commodityId;
}

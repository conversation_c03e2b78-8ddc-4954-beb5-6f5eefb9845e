package com.pinshang.qingyun.order.dto.shop;

import com.pinshang.qingyun.order.dto.Pagination;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.Map;

@Data
public class MdShopOrderSettingIDTO  extends Pagination {
    private Long id;

    private Long enterpriseId;//企业ID

    private String commodityId;

    private String commodityCode;

    private String commodityName;//商品名称

    private String commoditySpec;//商品规格

    private String shopTypeName;

    private Integer shopType;

    private Long supplierId;

    private String supplierCode;

    private String supplierName;//供应商名称

    private Integer logisticsModel;//物流模式

    private String deleveryTimeRange;//配送范围

    private Integer changePriceStatus;//是否可变价

    private Date createTime;

    private Date updateTime;

    private Long createId;

    private Long updateId;

    private String commodityCodeOrName;
    private String logisticsModelName;//物流模式
    private String changePriceStatusName;//是否可变价
    private Map<String, String> map;

    @ApiModelProperty("大类ID")
    private Long cateId1;

    @ApiModelProperty("中类ID")
    private Long cateId2;

    @ApiModelProperty("小类ID")
    private Long cateId3;
}

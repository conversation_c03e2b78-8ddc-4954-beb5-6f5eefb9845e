package com.pinshang.qingyun.order.dto.finance;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2025/7/16 13:42
 */
@Data
public class ShopLessGoodsOrderIDTO {
    @ApiModelProperty(value = "业务日期：yyyy-MM-dd")
    private String businessDate;

    @ApiModelProperty(value = "业务单明细ID 查询起点比如100表示就从第100条开始查询到limitQuantity")
    private Long businessOrderItemId;

    @ApiModelProperty(value = "查询条数")
    private Integer limitQuantity;
    
    public ShopLessGoodsOrderIDTO(String businessDate, Integer limitQuantity, Long businessOrderItemId) {
    	this.businessDate = businessDate;
    	this.limitQuantity = limitQuantity;
    	this.businessOrderItemId = businessOrderItemId;
    }
    
}

package com.pinshang.qingyun.order.dto.order;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * summary.
 * <p>
 * detailed description
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2018/7/25
 */
@Data
public class ModifyDeliverDateODTO {

    @ApiModelProperty(position = 1, value = "送货日期")
    private String orderTime;

    @ApiModelProperty(position = 2, value = "门店编码")
    private String shopCode;

    @ApiModelProperty(position = 3, value = "门店名称")
    private String shopName;

    @ApiModelProperty(position = 4, value = "订单金额")
    private BigDecimal orderAmount;

    @ApiModelProperty(position = 5, value = "配送批次")
    private String deliveryBatch;

    @ApiModelProperty(position = 6, value = "创建人")
    private String realName;

    @ApiModelProperty(position = 7, value = "发货状态")
    private String deliveryStatus;

    @ApiModelProperty(position = 8, value = "订单号")
    private String orderCode;

}

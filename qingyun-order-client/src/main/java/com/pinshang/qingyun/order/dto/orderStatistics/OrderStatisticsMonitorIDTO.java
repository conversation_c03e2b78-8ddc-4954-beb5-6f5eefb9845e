package com.pinshang.qingyun.order.dto.orderStatistics;

import com.pinshang.qingyun.order.dto.Pagination;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * 订单统计查询--订单监控查询参数
 */
@Data
public class OrderStatisticsMonitorIDTO extends Pagination {
    private List<String> orderCodeList;

    private List<Integer> filterOrderTypeList;

    private Date orderTime;
    private Date startTime;
    private Date endTime;
    private Date sysTime;//当前系统时间

    private Date startCreateTime;
    private Date endCreateTime;

}

package com.pinshang.qingyun.order.dto.order;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;


@Data
public class PdaReturnItemODTO {

    @ApiModelProperty("商品id")
    private String commodityId;

    @ApiModelProperty("商品编码")
    private String commodityCode;

    @ApiModelProperty("商品名称")
    private String commodityName;

    @ApiModelProperty("规格")
    private String commoditySpec;

    @ApiModelProperty("包装规格")
    private BigDecimal commodityPackageSpec;

    @ApiModelProperty("单位")
    private String unit;
    @ApiModelProperty("条形码")
    private String barCode;

    @ApiModelProperty("库存数量")
    private BigDecimal stockQuantity;

    @ApiModelProperty("单价")
    private BigDecimal price;

    @ApiModelProperty("实发数量")
    private BigDecimal deliveryQuantity;

    @ApiModelProperty("退货原因")
    private List<ReturnReason> returnReasonList;

    @Data
    public static class ReturnReason{
        private String optionCode;
        private String optionName;
    }

}

package com.pinshang.qingyun.order.dto.order;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import lombok.ToString;

import java.math.BigDecimal;
import java.util.Objects;

/**
 * summary.
 * <p>
 * detailed description
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2019-02-15
 */
@Data
@ToString
@AllArgsConstructor
@RequiredArgsConstructor
public class FilterTipODTO {
    private Long commodityId;
    private String commodityCode;
    private String commodityName;
    private String commoditySpec;
    /** 商品数量 */
    private BigDecimal quantity;
    private String msg;
    /** 类型 1: 不能订货，已被剔除 2: 配置信息变更，已被重新添加到购物车 */
    private Integer type;

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        FilterTipODTO that = (FilterTipODTO) o;
        return commodityId.equals(that.commodityId) &&
                commodityName.equals(that.commodityName) &&
                type.equals(that.type);
    }

    @Override
    public int hashCode() {
        return Objects.hash(commodityId, commodityName, type);
    }
}

package com.pinshang.qingyun.order.dto.shop;

import com.pinshang.qingyun.base.enums.IogisticsModelEnums;
import com.pinshang.qingyun.box.utils.DateUtil;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 导出已审核的直送订单列表
 * 注意：请勿调整字段的顺序，如需要增加字段请在最后面添加
 */
@Data
public class AuditInfoExportODTO {

	@ApiModelProperty(position = 1, value ="门店")
	private String shopName;

	@ApiModelProperty(position = 2, value ="送货日期")
	private String orderTime;

	@ApiModelProperty(position = 3, value ="物流模式")
	private String logisticsModel;

	@ApiModelProperty(position = 4, value ="预订单号")
	private String subOrderCode;

	@ApiModelProperty(position = 5, value ="预订单金额")
	private BigDecimal totalPrice;

	@ApiModelProperty(position = 6, value ="审核人")
	private String approvingName;

	@ApiModelProperty(position = 7, value ="审核时间")
	private String approvingTime;

	@ApiModelProperty(position = 8, value ="订单编号")
	private String realOrderCode;

	@ApiModelProperty(position = 9, value ="供应商")
	private String supplierName;

	@ApiModelProperty(position = 10, value ="收货状态")
	private String status;

	@ApiModelProperty(position = 11, value ="收货日期")
	private String receiveTime;

	public void setOrderTime(Date orderTime) {
		if (orderTime != null) {
			this.orderTime = DateUtil.getDateFormate(orderTime, "yyyy-MM-dd");
		}
	}

	public void setLogisticsModel(Integer logisticsModel) {
		this.logisticsModel = IogisticsModelEnums.getName(logisticsModel);
	}

	public void setApprovingTime(Date approvingTime) {
		if (approvingTime != null) {
			this.approvingTime = DateUtil.getDateFormate(approvingTime, DateUtil.DEFAULT_DATE_FORMAT);
		}
	}

	public void setStatus(Integer status) {
		// 0-待收货, 1-待审核, 2-审核不通过, 3-审核通过(已完成), 4-已完成
		switch (status) {
			case 0:
				this.status = "待收货";
				break;
			case 1:
				this.status = "待审核";
				break;
			case 2:
				this.status = "审核不通过";
				break;
			case 3:
				this.status = "已完成";
				break;
			case 4:
				this.status = "已完成";
				break;
			default:
				break;
		}
	}

	public void setReceiveTime(Date receiveTime) {
		if (receiveTime != null) {
			this.receiveTime = DateUtil.getDateFormate(receiveTime, "yyyy-MM-dd");
		}
	}
}

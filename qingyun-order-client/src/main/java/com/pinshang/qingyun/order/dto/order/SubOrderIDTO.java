package com.pinshang.qingyun.order.dto.order;

import com.pinshang.qingyun.order.dto.Pagination;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
public class SubOrderIDTO extends Pagination {
	/**
	 * 
	 */
	private static final long serialVersionUID = -5920644605352450524L;
	private Long id;
	//订单编号
	private String orderCode;
	//仓库
	private Long warehouseId;
	
	private Long enterpriseId;

	private List<Long> orderIds;
	// 客户类型
	private Long storeTypeId;
	private Long userId;
	// 批次时间段start
	private String startTime;
	// 批次时间段end
	private String endTime;
	/** 批次查询字段  **/
	private Integer deliveryBatch;
	//送货日期
	private Date orderTime;

	private Long lineGroupId;

    @ApiModelProperty("发货时间")
    private String deliveryTime;

	@ApiModelProperty("业务类型：0=B端销售，1=调拨，4=线上销售，10=通达销售")
	private Integer businessType;

	@ApiModelProperty("区域（物流中心id，通达时有效）")
	private Long logisticsCenterId;

}

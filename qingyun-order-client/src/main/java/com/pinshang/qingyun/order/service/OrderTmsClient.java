package com.pinshang.qingyun.order.service;

import java.util.List;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;

import com.pinshang.qingyun.base.configure.FeignClientConfiguration;
import com.pinshang.qingyun.base.constant.ApplicationNameConstant;
import com.pinshang.qingyun.order.dto.tms.RefOrderForWaybillODTO;
import com.pinshang.qingyun.order.dto.tms.SelectRefOrderForWaybillIDTO;
import com.pinshang.qingyun.order.dto.tms.WarehouseConfirmInfoODTO;
import com.pinshang.qingyun.order.hystrix.OrderTmsClientHystrix;

/**
 * 【订单系统 <-> 物流系统】
 */
@FeignClient(value = ApplicationNameConstant.QINGYUN_ORDER_SERVICE, fallbackFactory = OrderTmsClientHystrix.class, configuration = FeignClientConfiguration.class)
public interface OrderTmsClient {

	/**
	 * 查询【源单-用于运单】
	 * 
	 * @param idto
	 * @return
	 */
	@RequestMapping(value = "/ordertms/client/selectRefOrder", method = RequestMethod.POST)
	public RefOrderForWaybillODTO selectRefOrder(@RequestBody SelectRefOrderForWaybillIDTO idto);

	/**
	 * 查询【大仓确认信息】列表
	 * 
	 * @param returnOrderId
	 * @return
	 */
	@RequestMapping(value = "/ordertms/client/selectWarehouseConfirmInfoList", method = RequestMethod.POST)
	public List<WarehouseConfirmInfoODTO> selectWarehouseConfirmInfoList(@RequestParam(value = "returnOrderId", required = false) Long returnOrderId);

}

package com.pinshang.qingyun.order.service;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;

import com.pinshang.qingyun.base.configure.FeignClientConfiguration;
import com.pinshang.qingyun.base.constant.ApplicationNameConstant;
import com.pinshang.qingyun.order.hystrix.AppUserClientHystrix;

/**
 * 预付账户
 * <AUTHOR>
 *
 */
@FeignClient(value = ApplicationNameConstant.QINGYUN_ORDER_SERVICE, fallbackFactory = AppUserClientHystrix.class, configuration = FeignClientConfiguration.class)
public interface PrePaidAccountClient {

	@PostMapping("/prePaidAccount/asyncRecord")
	void asyncRecord();
	
}

package com.pinshang.qingyun.order.dto.commodity;

import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.util.Date;

public class CommodityListODto {

	private String commodityName;

    private String commoditySpec;
    private String commodityId;
    private String commodityCode;
    private String categoryName;
    private boolean edit = true;
    
    private String commodityRemark;//商品备注
    
    private Integer commodityType;
    
    private Integer commodityState;
    //商品价格(实际用的->新价格)
    private BigDecimal commodityPrice;
    
    private BigDecimal commodityNumber;
    
    private BigDecimal commodityNumberLimit;
    
    private Integer frozen;//速冻
    
    private String  packedType;//包类型
    
    private String newProductFlag;//新品标识
    @SuppressWarnings("unused")
	private boolean isNewProduct;//是否新品
    
    private boolean isPromotionProduct;//是否促销
    
    private String storageCondition;//存储条件
    private String commodityDesc;//描述(说明)
    
    private String supplyStartTime; //供货开始时间
    private String supplyEndTime;//供货截止时间
    
    private BigDecimal referencePrice;//参考零售价
    
    private Boolean isShow =false;//
    
    private BigDecimal salesBoxCapacity;
	private BigDecimal boxCapacity;
	private BigDecimal stockQuantity; //现有库存

	private Integer stockNumber;

	private BigDecimal suggestedQuantity; //建议订货量
	
	private String commodityUnit;

	private BigDecimal retailPrice;//门店零售价

	private String barCode;

	private String barCodeList;	// 子码列表

	// 包装规格
	private BigDecimal commodityPackageSpec;
	private Integer logisticsModel;//物流模式
	private BigDecimal commodityShares;//份数

	/** app状态：0-上架，1-下架 */
	private Integer appStatus;
	private String categoryFirstName;
	private  Integer isWeight;
	private Boolean autoCommodity; // 是否自定订货
	private BigDecimal orderedQuantity;//已定未收数量

	/** 订货属性  1 必售  2 一级可选  3 二级可选**/
	private Integer orderPropertyType;
	private String OrderPropertyTypeName;

	/**
	 * 1 当前时间可订货的商品——在订货时间段内，且有库存（商品库存依据且有库存的商品、不限量供应的商品、限量供应且有余量的商品）
	 * 2 已抢光的商品——商品库存依据且库存≤0或限量供应且已满限量的商品，标记“已抢光”效果。
	 * 3 当前时间不在订货时间段的商品
	 */
	private Integer orderedStatus;

	/**
	 * 促销开始时间
	 */
	private Date startTime;
	/**
	 * 促销结束时间
	 */
	private Date endTime;

	public Date getStartTime() {
		return startTime;
	}

	public void setStartTime(Date startTime) {
		this.startTime = startTime;
	}

	public Date getEndTime() {
		return endTime;
	}

	public void setEndTime(Date endTime) {
		this.endTime = endTime;
	}

	public Integer getOrderedStatus() {
		return orderedStatus;
	}

	public void setOrderedStatus(Integer orderedStatus) {
		this.orderedStatus = orderedStatus;
	}

	public String getOrderPropertyTypeName() {
		return OrderPropertyTypeName;
	}

	public void setOrderPropertyTypeName(String orderPropertyTypeName) {
		OrderPropertyTypeName = orderPropertyTypeName;
	}

	public Integer getOrderPropertyType() {
		return orderPropertyType;
	}

	public void setOrderPropertyType(Integer orderPropertyType) {
		this.orderPropertyType = orderPropertyType;
	}

	public BigDecimal getBoxCapacity() {
		return boxCapacity;
	}

	public void setBoxCapacity(BigDecimal boxCapacity) {
		this.boxCapacity = boxCapacity;
	}

	public BigDecimal getOrderedQuantity() {
		return orderedQuantity;
	}

	public void setOrderedQuantity(BigDecimal orderedQuantity) {
		this.orderedQuantity = orderedQuantity;
	}
	public Boolean getAutoCommodity() {
		return autoCommodity;
	}

	public void setAutoCommodity(Boolean autoCommodity) {
		this.autoCommodity = autoCommodity;
	}

	public Integer getStockNumber() {
		return stockNumber;
	}

	public void setStockNumber(Integer stockNumber) {
		this.stockNumber = stockNumber;
	}

	public Integer getIsWeight() {
		return isWeight;
	}

	public void setIsWeight(Integer isWeight) {
		this.isWeight = isWeight;
	}

	public String getCategoryFirstName() {
		return categoryFirstName;
	}

	public void setCategoryFirstName(String categoryFirstName) {
		this.categoryFirstName = categoryFirstName;
	}

	public Integer getAppStatus() {
		return appStatus;
	}

	public void setAppStatus(Integer appStatus) {
		this.appStatus = appStatus;
	}

	public BigDecimal getCommodityShares() {
		return commodityShares;
	}

	public void setCommodityShares(BigDecimal commodityShares) {
		this.commodityShares = commodityShares;
	}

	public Integer getLogisticsModel() {
		return logisticsModel;
	}

	public void setLogisticsModel(Integer logisticsModel) {
		this.logisticsModel = logisticsModel;
	}

	public String getBarCode() {
		return barCode;
	}

	public void setBarCode(String barCode) {
		this.barCode = barCode;
	}

	public String getBarCodeList() {
		return barCodeList;
	}

	public void setBarCodeList(String barCodeList) {
		this.barCodeList = barCodeList;
	}

	public BigDecimal getCommodityPackageSpec() {
		return commodityPackageSpec;
	}

	public void setCommodityPackageSpec(BigDecimal commodityPackageSpec) {
		this.commodityPackageSpec = commodityPackageSpec;
	}

	public String getCommodityUnit() {
		return commodityUnit;
	}

	public void setCommodityUnit(String commodityUnit) {
		this.commodityUnit = commodityUnit;
	}

	public String getCommodityName() {
        return commodityName;
    }

    public void setCommodityName(String commodityName) {
        this.commodityName = commodityName;
    }

    public String getCommoditySpec() {
        return commoditySpec;
    }

    public void setCommoditySpec(String commoditySpec) {
        this.commoditySpec = commoditySpec;
    }

	public BigDecimal getCommodityNumber() {
        return commodityNumber==null?new BigDecimal(-1):commodityNumber;
    }

    public void setCommodityNumber(BigDecimal commodityNumber) {
        this.commodityNumber = commodityNumber;
    }
    
    public void setCommodityNumber(String commodityNumber){
    	this.commodityNumber = new BigDecimal(commodityNumber);
    }
    
    public BigDecimal getCommodityNumberLimit() {
		return commodityNumberLimit == null ? BigDecimal.valueOf(-1): commodityNumberLimit ;
	}

	public void setCommodityNumberLimit(BigDecimal commodityNumberLimit) {
		this.commodityNumberLimit = commodityNumberLimit;
	}

    public BigDecimal getCommodityPrice() {
        return commodityPrice;
    }

    public void setCommodityPrice(BigDecimal commodityPrice) {
        this.commodityPrice = commodityPrice;
    }

    public String getCommodityId() {
        return commodityId;
    }

    public void setCommodityId(String commodityId) {
        this.commodityId = commodityId;
    }

    public String getCommodityCode() {
        return commodityCode;
    }

    public void setCommodityCode(String commodityCode) {
        this.commodityCode = commodityCode;
    }

	public Integer getCommodityType() {
		return commodityType;
	}

	public void setCommodityType(Integer commodityType) {
		this.commodityType = commodityType;
	}
	
	
    public String getCommodityRemark() {
		return commodityRemark;
	}

	public void setCommodityRemark(String commodityRemark) {
		this.commodityRemark = commodityRemark;
	}

	public String getCategoryName() {
		return categoryName;
	}

	public void setCategoryName(String categoryName) {
		this.categoryName = categoryName;
	}

	public Integer getCommodityState() {
		return commodityState;
	}

	public void setCommodityState(Integer commodityState) {
		this.commodityState = commodityState;
	}

	public boolean isEdit() {
		return edit;
	}

	public void setEdit(boolean edit) {
		this.edit = edit;
	}

	public Integer getFrozen() {
		if( null == this.frozen){
			return  0 ;
		}
		return frozen;
	}

	public void setFrozen(Integer frozen) {
		this.frozen = frozen;
	}

	public String getPackedType() {
		if(StringUtils.isEmpty(this.packedType)){
			return "01";
		}
		return packedType;
	}

	public void setPackedType(String packedType) {
		this.packedType = packedType;
	}

	public String getStorageCondition() {
		return storageCondition;
	}

	public void setStorageCondition(String storageCondition) {
		this.storageCondition = storageCondition;
	}

	public String getCommodityDesc() {
		if(StringUtils.isEmpty(this.commodityDesc)){
			return "";
		}
		return commodityDesc;
	}

	public void setCommodityDesc(String commodityDesc) {
		this.commodityDesc = commodityDesc;
	}
	 
	public String getSupplyStartTime() {
		return supplyStartTime;
	}

	public void setSupplyStartTime(String supplyStartTime) {
		this.supplyStartTime = supplyStartTime;
	}

	public String getSupplyEndTime() {
		return supplyEndTime;
	}

	public void setSupplyEndTime(String supplyEndTime) {
		this.supplyEndTime = supplyEndTime;
	}

	public BigDecimal getReferencePrice() {
		return referencePrice;
	}

	public void setReferencePrice(BigDecimal referencePrice) {
		this.referencePrice = referencePrice;
	}

	public String getNewProductFlag() {
		return newProductFlag;
	}

	public void setNewProductFlag(String newProductFlag) {
		this.newProductFlag = newProductFlag;
	}
	
	public boolean isNewProduct() {
		if(StringUtils.isEmpty(this.newProductFlag)){
			return false;
		}
		if("01".equals(this.newProductFlag)){
			return true;
		}
		return false;
	}

	public boolean isPromotionProduct() {
		return isPromotionProduct;
	}

	public void setPromotionProduct(boolean isPromotionProduct) {
		this.isPromotionProduct = isPromotionProduct;
	}

	public Boolean getIsShow() {
		return isShow;
	}

	public void setIsShow(Boolean isShow) {
		this.isShow = isShow;
	}

	public BigDecimal getSalesBoxCapacity() {
		return salesBoxCapacity;
	}

	public void setSalesBoxCapacity(BigDecimal salesBoxCapacity) {
		this.salesBoxCapacity = salesBoxCapacity;
	}

	public BigDecimal getStockQuantity() {
		return stockQuantity;
	}

	public void setStockQuantity(BigDecimal stockQuantity) {
		this.stockQuantity = stockQuantity;
	}

	public BigDecimal getSuggestedQuantity() {
		return suggestedQuantity;
	}

	public void setSuggestedQuantity(BigDecimal suggestedQuantity) {
		this.suggestedQuantity = suggestedQuantity;
	}

	public BigDecimal getRetailPrice() {
		return retailPrice;
	}

	public void setRetailPrice(BigDecimal retailPrice) {
		this.retailPrice = retailPrice;
	}
}

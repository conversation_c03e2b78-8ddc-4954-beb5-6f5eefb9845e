package com.pinshang.qingyun.order.service;

import com.github.pagehelper.PageInfo;
import com.pinshang.qingyun.base.configure.FeignClientConfiguration;
import com.pinshang.qingyun.base.constant.ApplicationNameConstant;
import com.pinshang.qingyun.order.dto.UnDeliverySubOrderIDTO;
import com.pinshang.qingyun.order.dto.order.*;
import com.pinshang.qingyun.order.hystrix.SubOrderClientHystrix;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@FeignClient(value = ApplicationNameConstant.QINGYUN_ORDER_SERVICE, fallbackFactory = SubOrderClientHystrix.class, configuration = FeignClientConfiguration.class)
public interface SubOrderClient {

	@RequestMapping(value ="/subOrder/queryUnDeliverySubOrderList", method = RequestMethod.POST)
	PageInfo<SubOrderListEntryODTO> queryUnDeliverySubOrderList(@RequestBody SubOrderIDTO vo);

	@RequestMapping(value ="/subOrder/querySubOrderItemList", method = RequestMethod.POST)
	public List<SubOrderItemListEntryODTO> querySubOrderItemList(@RequestBody SubOrderSearchIDTO vo);

	@RequestMapping(value ="/subOrder/createDeliveryOrder", method = RequestMethod.POST)
	boolean createDeliveryOrder(@RequestBody SubOrderSearchIDTO dto);

	@RequestMapping(value = "/subOrder/updateSubOrderStatus", method = RequestMethod.POST)
	Integer updateSubOrderStatus(@RequestBody List<Long> subOrderIdList , @RequestParam("subOrderStatus") Integer subOrderStatus);

//	@RequestMapping(value = "/subOrder/querySubOrderCode", method = RequestMethod.POST)
//	Map<Long,String> querySubOrderCode (@RequestBody List<Long> subOrderIds);

	@RequestMapping(value = "/subOrder/findItemsBySubOrderId/{subOrderId}", method = RequestMethod.GET)
    List<PickSubOrderItemODTO> findItemsBySubOrderId(@PathVariable("subOrderId") Long subOrderId);


    /**
     * 批量更新subOrder的实发数量
     * @param deliveryItemList
     * @return
     */
	@RequestMapping(value = "/subOrder/batchUpdateDeliveryQuantity", method = RequestMethod.POST)
	@Deprecated
	int batchUpdateDeliveryQuantity(@RequestBody List<PickSubOrderItemODTO> deliveryItemList);

	/**
	 * 批量更新多个subOrder的实发数量
	 * @param subOrderList
	 * @return
	 */
	@RequestMapping(value = "/subOrder/batchUpdateSubOrderDeliveryQuantity", method = RequestMethod.POST)
	boolean batchUpdateDeliveryQuantityV2(@RequestBody List<PickSubOrderIDTO> subOrderList);

	@RequestMapping(value = "/subOrder/unDeliverySubOrderJob", method = RequestMethod.POST)
	public void unDeliverySubOrderJob(@RequestBody UnDeliverySubOrderIDTO idto);
}

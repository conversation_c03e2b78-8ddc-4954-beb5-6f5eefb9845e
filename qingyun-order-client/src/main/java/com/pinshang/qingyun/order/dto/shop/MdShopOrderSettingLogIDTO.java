package com.pinshang.qingyun.order.dto.shop;

import com.pinshang.qingyun.order.dto.Pagination;
import lombok.Data;

import java.util.Date;

@Data
public class MdShopOrderSettingLogIDTO extends Pagination {
    private Long id;

    private Integer opreateType;//0:新增1:删除  2：修改

    private String commodityCode;

    private String commodityName;//商品名称

    private String commoditySpec;//商品规格

    private Integer shopType;

    private String storeId;

    private String storeCode;

    private String storeName;

    private String shopCode;

    private String shopName;//门店名称

    private Long supplierId;

    private String supplierCode;

    private String supplierName;//供应商名称

    private Integer fromLogisticsModel;//物流模式

    private Integer toLogisticsModel;

    private String fromDeleveryTimeRange;//配送范围

    private String toDeleveryTimeRange;

    private Integer fromChangePriceStatus;//是否可变价

    private Integer toChangePriceStatus;

    private Date createTime;

    private Long createId;

    private String commodityCodeOrName;

    private String beginDate;

    private String endDate;

}

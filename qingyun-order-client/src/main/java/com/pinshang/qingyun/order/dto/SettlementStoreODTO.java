package com.pinshang.qingyun.order.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class SettlementStoreODTO {
    @ApiModelProperty(position = 1,value = "结账客户id")
    private String storeId;

    @ApiModelProperty(position = 2,value = "结账客户编码")
    private String customerCode;

    @ApiModelProperty(position = 3,value = "结账客户名称")
    private String customerName;

    @ApiModelProperty(position = 4,value = "结账客户状态")
    private String customerStatus;

    @ApiModelProperty(position = 5,value = "鲜食结账起始日期")
    private String xsStartBillDate;

}

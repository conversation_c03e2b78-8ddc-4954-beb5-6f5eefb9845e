package com.pinshang.qingyun.order.dto.purchase;

import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.math.BigDecimal;

public class ConnectionPurchaseDetailODto implements Serializable{

	/**
	 * 
	 */
	private static final long serialVersionUID = 4325777380760397009L;
	@ApiModelProperty(value = "商品id")
	private Long commodityId;
	@ApiModelProperty(value = "商品编码")
	private String commodityCode;
	@ApiModelProperty(value = "商品名称")
	private String commodityName;
	@ApiModelProperty(value = "商品分类")
	private String categoryName;
	@ApiModelProperty(value = "规格")
	private String commoditySpec;
	@ApiModelProperty(value = "单位")
	private String commodityUnit;
	@ApiModelProperty(value = "是否是加价供应商")
	private BigDecimal commodityNum;
	@ApiModelProperty(value = "供应商")
	private String supplierName;
	@ApiModelProperty(value = "箱规")
	private BigDecimal boxCapacity;
	@ApiModelProperty(value = "箱数")
	private BigDecimal boxNums;
	@ApiModelProperty(value = "门店类型")
	private Integer shopType;
	@ApiModelProperty(value = "订货数量")
	private BigDecimal commodityOrderNum;


	public Integer getShopType() {
		return shopType;
	}

	public void setShopType(Integer shopType) {
		this.shopType = shopType;
	}

	public String getShopTypeName() {
		if(shopType != null){
			if(shopType  == 1){
				return "门店";
			}else if(shopType  == 2){
				return "鲜食店";
			}else if(shopType  == 3){
				return "乐食店";
			}else if(shopType  == 4){
				return "包子铺";
			}else if(shopType  == 5){
				return "前置仓";
			}else {
				return "";
			}
		}else {
			return "";
		}
	}
	private String storeTypeName;

	public String getStoreTypeName() {
		return storeTypeName;
	}

	public void setStoreTypeName(String storeTypeName) {
		this.storeTypeName = storeTypeName;
	}

	public BigDecimal getCommodityOrderNum() {
		return commodityOrderNum;
	}

	public void setCommodityOrderNum(BigDecimal commodityOrderNum) {
		this.commodityOrderNum = commodityOrderNum;
	}

	public BigDecimal getBoxCapacity() {
		return boxCapacity;
	}

	public void setBoxCapacity(BigDecimal boxCapacity) {
		this.boxCapacity = boxCapacity;
	}

	public BigDecimal getBoxNums() {
		return boxNums;
	}

	public void setBoxNums(BigDecimal boxNums) {
		this.boxNums = boxNums;
	}

	public Long getCommodityId() {
		return commodityId;
	}

	public void setCommodityId(Long commodityId) {
		this.commodityId = commodityId;
	}

	public String getCommodityCode() {
		return commodityCode;
	}

	public void setCommodityCode(String commodityCode) {
		this.commodityCode = commodityCode;
	}

	public String getCommodityName() {
		return commodityName;
	}

	public void setCommodityName(String commodityName) {
		this.commodityName = commodityName;
	}

	public String getCategoryName() {
		return categoryName;
	}

	public void setCategoryName(String categoryName) {
		this.categoryName = categoryName;
	}

	public String getCommoditySpec() {
		return commoditySpec;
	}

	public void setCommoditySpec(String commoditySpec) {
		this.commoditySpec = commoditySpec;
	}

	public String getCommodityUnit() {
		return commodityUnit;
	}

	public void setCommodityUnit(String commodityUnit) {
		this.commodityUnit = commodityUnit;
	}

	public BigDecimal getCommodityNum() {
		if(null !=commodityNum){
			return commodityNum.setScale(2,BigDecimal.ROUND_HALF_UP);
		}
		return commodityNum;
	}

	public void setCommodityNum(BigDecimal commodityNum) {
		this.commodityNum = commodityNum;
	}

	public String getSupplierName() {
		return supplierName;
	}

	public void setSupplierName(String supplierName) {
		this.supplierName = supplierName;
	}
}

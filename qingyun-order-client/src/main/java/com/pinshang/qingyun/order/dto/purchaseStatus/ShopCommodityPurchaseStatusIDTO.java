package com.pinshang.qingyun.order.dto.purchaseStatus;

import com.pinshang.qingyun.order.dto.Pagination;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class ShopCommodityPurchaseStatusIDTO extends Pagination {


    @ApiModelProperty(position = 0,value = "门店类型 2- 鲜食店 5- 前置仓")
    private Integer shopType;

    @ApiModelProperty(position = 1,value = "门店id")
    private Long shopId;

    @ApiModelProperty(position = 2,value = "品类id")
    private Long categoryId;

    @ApiModelProperty(position = 3,value = "是否称重")
    private Integer isWeight;

    @ApiModelProperty(position = 4,value = "商品Id")
    private String commodityId;

    @ApiModelProperty(position = 5,value = "商品条码")
    private String barCode;

    @ApiModelProperty(position = 6,value = "是否可采 1-可采,0-否,不可采")
    private Integer commodityPurchaseStatus;

    @ApiModelProperty(position = 7,value = "总部是否可售 0-不可售,1-可售")
    private Integer commodityState;

    @ApiModelProperty(position = 8,value = "app状态：0-上架，1-下架")
    private Integer appStatus;

    private Long userId;
}

package com.pinshang.qingyun.order.service;

import com.pinshang.qingyun.base.configure.FeignClientConfiguration;
import com.pinshang.qingyun.base.constant.ApplicationNameConstant;
import com.pinshang.qingyun.order.dto.store.AppUserInfoIDTO;
import com.pinshang.qingyun.order.hystrix.AppUserClientHystrix;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

/**
 * <AUTHOR>
 * @Date 2018/10/10 10:52
 */
@FeignClient(value = ApplicationNameConstant.QINGYUN_ORDER_SERVICE, fallbackFactory = AppUserClientHystrix.class, configuration = FeignClientConfiguration.class)
public interface AppUserClient {
    /**
     * 开户
     * @param info
     * @return
     */
    @RequestMapping(value = "/storeAccount/openAccount",method = RequestMethod.POST)
    Boolean openAccount(@RequestBody AppUserInfoIDTO info);

    /**
     * 编辑密码
     * @param info
     * @return
     */
    @RequestMapping(value = "/storeAccount/editPassword",method = RequestMethod.POST)
    Boolean editPassword(@RequestBody AppUserInfoIDTO info);
}

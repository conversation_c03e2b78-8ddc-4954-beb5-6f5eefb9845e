package com.pinshang.qingyun.order.dto.order;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * @Author: sk
 * @Date: 2020/3/17
 */
@Data
public class XdReceiveDocCommodityODTO {

    @ApiModelProperty("单据id")
    private String docId;

    @ApiModelProperty("单据号")
    private String docCode;

    @ApiModelProperty("前置仓id")
    private Long shopId;

    private String shopCode;

    @ApiModelProperty("前置仓名称")
    private String shopName;



    @ApiModelProperty("送货日期")
    private Date deliveryTime;

    @ApiModelProperty("单据状态：0 待收货 1 已关闭")
    private Integer docStatus;


    private String commodityId;
    private String commodityCode;
    private String commodityName;

    @ApiModelProperty("规格")
    private String commoditySpec;

    @ApiModelProperty("单位")
    private String unit;

    @ApiModelProperty("主条形码")
    private String barCode;

    private String barCodes;

    @ApiModelProperty("条形码list")
    private List<String> barCodeList;

    @ApiModelProperty("01 散装  02 整包")
    private String commodityPackageKind;

    @ApiModelProperty("是否称重0-不称量,1-称重")
    private Integer isWeight;

    @ApiModelProperty("商品类型")
    private String commodityType;

    @ApiModelProperty("包装规格")
    private BigDecimal commodityPackageSpec;

    @ApiModelProperty("货位")
    private String shelfNo;

    private List<WarehouseShelfResultODTO> shelfList;

    @ApiModelProperty("订货份数")
    private BigDecimal orderShares;

    @ApiModelProperty("发货份数")
    private BigDecimal realDeliveryShares;

    @ApiModelProperty("已收份数")
    private BigDecimal receiveShares;

    @ApiModelProperty("待收份数")
    private BigDecimal unReceiveShares;


    @ApiModelProperty("订货数量")
    private BigDecimal quantity;
    @ApiModelProperty("已收数量")
    private BigDecimal realReceiveQuantity;
    @ApiModelProperty("发货数量")
    private BigDecimal realDeliveryQuantity;
    private BigDecimal price;
    /**
     * commodityPackageKind 01 散装  02 整包
     * isWeight 是否称重 0-不称量,1-称重
     * @return
     */
    public Integer getCommodityType() {
        if(isWeight == 0){ //标品
            return 1;
        }
        if(isWeight == 1 && "01".equals(commodityPackageKind)){//散称商品
            return 2;
        }
        if(isWeight == 1 && "02".equals(commodityPackageKind)){//称重包装品
            return 3;
        }
        return null;
    }

}

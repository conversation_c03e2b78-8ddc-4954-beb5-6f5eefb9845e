package com.pinshang.qingyun.order.service;

import com.pinshang.qingyun.base.configure.FeignClientConfiguration;
import com.pinshang.qingyun.base.constant.ApplicationNameConstant;
import com.pinshang.qingyun.order.hystrix.XDAShoppingCartClientHystrix;
import com.pinshang.qingyun.order.hystrix.XDShoppingCartClientHystrix;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;

import java.math.BigDecimal;
import java.util.Date;

@FeignClient(value = ApplicationNameConstant.QINGYUN_ORDER_SERVICE, fallbackFactory = XDAShoppingCartClientHystrix.class, configuration = FeignClientConfiguration.class)
public interface XDAShoppingCartClient {

    @Deprecated
    @RequestMapping(value = "/xda/shoppingCart/category", method = RequestMethod.GET)
    Integer getCategoryNum(@RequestParam("orderDate") String orderDate, @RequestParam("storeId") Long storeId);

    @Deprecated
    @RequestMapping(value = "/xda/shoppingCartV2/getCategoryV2", method = RequestMethod.GET)
    Integer getCategoryNumV2(@RequestParam("orderDate") String orderDate, @RequestParam("storeId") Long storeId);

    @RequestMapping(value = "/xda/shoppingCartV4/getCategoryV4", method = RequestMethod.GET)
    Integer getCategoryNumV4(@RequestParam("orderDate") String orderDate, @RequestParam("storeId") Long storeId);

    @Deprecated
    @RequestMapping(value = "/xda/shoppingCartV2/getNormalGroupAmountV2", method = RequestMethod.GET)
    BigDecimal getNormalGroupAmountV2(@RequestParam("orderDate") String orderDate, @RequestParam("storeId") Long storeId);

    @Deprecated
    @RequestMapping(value = "/xda/shoppingCartV3/getNormalGroupAmountV3", method = RequestMethod.GET)
    BigDecimal getNormalGroupAmountV3(@RequestParam("orderDate") String orderDate, @RequestParam("storeId") Long storeId);

    @RequestMapping(value = "/xda/shoppingCartV4/getNormalGroupAmountV4", method = RequestMethod.GET)
    BigDecimal getNormalGroupAmountV4(@RequestParam("orderDate") String orderDate, @RequestParam("storeId") Long storeId);


}

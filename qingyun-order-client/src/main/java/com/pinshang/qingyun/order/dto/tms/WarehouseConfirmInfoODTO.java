package com.pinshang.qingyun.order.dto.tms;

import io.swagger.annotations.ApiModelProperty;

import java.math.BigDecimal;

import lombok.Data;
import lombok.NoArgsConstructor;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;

/**
 * 【大仓确认信息】
 */
@Data
@NoArgsConstructor
public class WarehouseConfirmInfoODTO {
	@JsonSerialize(using = ToStringSerializer.class)
	@ApiModelProperty(position = 11, required = true, value = "商品ID")
	private Long commodityId;
	@ApiModelProperty(position = 12, required = true, value = "是否称重 0-否,1-是")
    private Integer isWeight;
	
	@ApiModelProperty(position = 31, required = true, value = "确认数量")
	private BigDecimal confirmQuantity;
	@ApiModelProperty(position = 32, required = true, value = "确认份数")
	private Integer confirmNumber;
	@ApiModelProperty(position = 33, required = true, value = "确认金额")
	private BigDecimal confirmAmount;
}

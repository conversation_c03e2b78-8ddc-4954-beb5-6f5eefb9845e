package com.pinshang.qingyun.order.hystrix;

import com.pinshang.qingyun.order.dto.settlement.SettlementODTO;
import com.pinshang.qingyun.order.dto.store.StoreSettlementODTO;
import com.pinshang.qingyun.order.service.SettlementClient;
import com.pinshang.qingyun.order.service.StoreSettlementClient;
import feign.hystrix.FallbackFactory;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2019/3/11 16:26
 */
@Component
public class SettlementClientHystrix implements FallbackFactory<SettlementClient> {
    @Override
    public SettlementClient create(Throwable throwable) {

        return new SettlementClient() {
            @Override
            public String upXsStartBillDateBySettleId(Long id, String date) {
                return null;
            }

            @Override
            public String findXsStartBillDateById(Long id) {
                return null;
            }

            @Override
            public List<Long> findStoreIdsBySettleId(Long settleId) {
                return null;
            }

            @Override
            public List<SettlementODTO> findByCodeOrName(String str) {
                return null;
            }
        };
    }
}

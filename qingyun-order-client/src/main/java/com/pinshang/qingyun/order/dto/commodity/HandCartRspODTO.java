package com.pinshang.qingyun.order.dto.commodity;

import io.swagger.annotations.ApiModelProperty;

import java.math.BigDecimal;

public class HandCartRspODTO {

     @ApiModelProperty("当前修改或者删除数量")
     private BigDecimal quantity=BigDecimal.ZERO;

     @ApiModelProperty("份数")
     private BigDecimal shares=BigDecimal.ZERO;

     //品类总数
     @ApiModelProperty("品类总数")
     private BigDecimal varietyTotal;

     @ApiModelProperty("总数量")
     private BigDecimal totalQuantity=BigDecimal.ZERO;

     @ApiModelProperty("总价")
     private BigDecimal totalAmount=BigDecimal.ZERO;

     @ApiModelProperty("commodityId 不为空说明当前商品库存余量不足")
     private String commodityId;
     @ApiModelProperty("可用库存数量")
     private BigDecimal inventoryQuantity;

     public String getCommodityId() {
          return commodityId;
     }

     public void setCommodityId(String commodityId) {
          this.commodityId = commodityId;
     }

     public BigDecimal getInventoryQuantity() {
          return inventoryQuantity;
     }

     public void setInventoryQuantity(BigDecimal inventoryQuantity) {
          this.inventoryQuantity = inventoryQuantity;
     }

     public BigDecimal getQuantity() {
          if(null !=quantity){
               return quantity.setScale(2,BigDecimal.ROUND_HALF_UP);
          }
          return quantity;
     }

     public void setQuantity(BigDecimal quantity) {
          this.quantity = quantity;
     }

     public BigDecimal getVarietyTotal() {
          return varietyTotal;
     }

     public void setVarietyTotal(BigDecimal varietyTotal) {
          this.varietyTotal = varietyTotal;
     }

     public BigDecimal getTotalQuantity() {
          if(null !=totalQuantity){
               return totalQuantity.setScale(2,BigDecimal.ROUND_HALF_UP);
          }
          return totalQuantity;
     }

     public void setTotalQuantity(BigDecimal totalQuantity) {
          this.totalQuantity = totalQuantity;
     }

     public BigDecimal getTotalAmount() {
          if(null !=totalAmount){
               return totalAmount.setScale(2,BigDecimal.ROUND_HALF_UP);
          }
          return totalAmount;
     }

     public void setTotalAmount(BigDecimal totalAmount) {
          this.totalAmount = totalAmount;
     }

     public BigDecimal getShares() {
          return shares;
     }

     public void setShares(BigDecimal shares) {
          this.shares = shares;
     }
}

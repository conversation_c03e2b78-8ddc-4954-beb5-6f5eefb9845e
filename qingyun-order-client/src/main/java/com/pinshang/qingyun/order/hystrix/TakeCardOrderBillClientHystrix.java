package com.pinshang.qingyun.order.hystrix;

import com.pinshang.qingyun.order.service.TakeCardOrderBillClient;
import com.pinshang.qingyun.order.service.XDShoppingCartClient;
import feign.hystrix.FallbackFactory;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

@Component
public class TakeCardOrderBillClientHystrix implements FallbackFactory<TakeCardOrderBillClient> {
	private Logger logger = LoggerFactory.getLogger(getClass());
	
	@Override
	public TakeCardOrderBillClient create(Throwable throwable) {
		return new TakeCardOrderBillClient() {

			@Override
			public String pullTakeBill(String appointDate) {
				return null;
			}

			@Override
			public String generateCardOrder() {
				return null;
			}
		};
	}
}

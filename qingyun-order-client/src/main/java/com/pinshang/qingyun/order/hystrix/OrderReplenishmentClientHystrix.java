package com.pinshang.qingyun.order.hystrix;

import com.github.pagehelper.PageInfo;
import com.pinshang.qingyun.order.dto.order.OrderReplenishmentIDTO;
import com.pinshang.qingyun.order.dto.order.OrderReplenishmentListIDTO;
import com.pinshang.qingyun.order.dto.order.OrderReplenishmentListODTO;
import com.pinshang.qingyun.order.service.OrderReplenishmentClient;
import feign.hystrix.FallbackFactory;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public class OrderReplenishmentClientHystrix implements FallbackFactory<OrderReplenishmentClient>{

    @Override
    public OrderReplenishmentClient create(Throwable cause) {
        return new OrderReplenishmentClient() {

            @Override
            public Object importOrderReplenishment(List<OrderReplenishmentIDTO> list) {
                return null;
            }

            @Override
            public PageInfo<OrderReplenishmentListODTO> orderReplenishmentList(OrderReplenishmentListIDTO orderReplenishmentListIDTO) {
                return null;
            }
        };
    }
}

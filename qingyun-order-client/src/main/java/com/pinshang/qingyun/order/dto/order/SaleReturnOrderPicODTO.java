package com.pinshang.qingyun.order.dto.order;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;


@Data
@NoArgsConstructor
public class SaleReturnOrderPicODTO {
	@ApiModelProperty(position = 11, required = true, value = "图片URL")
	private String picUrl;

	@ApiModelProperty(position = 21, required = true, value = "访问图片URL")
	private String visitPicUrl;

	public SaleReturnOrderPicODTO(String picUrl, String visitPicUrl) {
		this.picUrl = picUrl;
		this.visitPicUrl = visitPicUrl;
	}
}

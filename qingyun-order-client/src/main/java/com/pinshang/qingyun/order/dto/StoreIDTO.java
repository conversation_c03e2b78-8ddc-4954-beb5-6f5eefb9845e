package com.pinshang.qingyun.order.dto;

import com.pinshang.qingyun.order.dto.Pagination;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
public class StoreIDTO extends Pagination {
    @ApiModelProperty(position = 1,value = "客户编码")
    private String storeCode;

    @ApiModelProperty(position = 2,value = "门店名称")
    private String shopName;

    @ApiModelProperty(position = 3,value = "门店id")
    private Long shopId;
}

package com.pinshang.qingyun.order.dto.auto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * @Author: sk
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class AutoSaveOrderItemIDTO extends MsShopOrderSettingBaseDTO{


    private Long shopId;
    private Long storeId;

    //private String productId;
   // private BigDecimal productNum;

    private String orderTime;
    private Long enterpriseId;
    //物流模式
    private Integer logisticsModel;
    //供应商id
    private Long supplierId;
    private Long warehouseId;
    private String deliveryBatch;
    private String deleveryTimeRange;
    private Long userId;
    private String createName;


    /** 安全库存 */
    private BigDecimal safeQuantity;
    private Integer commodityIsQuickFreeze;
    private BigDecimal salesBoxCapacity; // 销售箱规
    private BigDecimal commodityPackageSpec; // 包装规格

    private BigDecimal stockQuantity;// 库存数量
    private BigDecimal price;
    private String commodityCode;
    private String commodityName;

    private String defaultWarehouseEndTime;
    private String defaultSupplierEndTime;

}

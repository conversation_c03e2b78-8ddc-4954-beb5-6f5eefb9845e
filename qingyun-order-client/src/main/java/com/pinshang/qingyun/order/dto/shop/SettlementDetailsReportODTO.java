package com.pinshang.qingyun.order.dto.shop;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * Created by hhf on 2018/6/12.
 * 结算明细报表
 */
@Data
public class SettlementDetailsReportODTO {

    /**门店id**/
    @ApiModelProperty(position = 1, value = "门店id")
    private Long shopId;

    /**门店名称**/
    @ApiModelProperty(position = 2, value = "门店名称")
    private String shopName;

    /**送货日期**/
    @ApiModelProperty(position = 3, value = "送货日期")
    private Date orderTime;

    /**结算日期**/
    @ApiModelProperty(position = 4, value = "结算日期")
    private Date settleTime;

    /**订单来源-名称**/
    private String orderTypeName;

    /**订单编号**/
    @ApiModelProperty(position = 5, value = "订单编号")
    private String subOrderCode;

    /**预订单编号**/
    @ApiModelProperty(position = 6, value = "预订单编号")
    private String preOrderCode;

    /**分类**/
    @ApiModelProperty(position = 7, value = "分类")
    private String categoryName;

    /**条形码**/
    @ApiModelProperty(position = 8, value = "条形码")
    private String barCode;
    //private String barCodes;
    @ApiModelProperty(position = 9, value = "主副条码集合")
    private List<String> barCodeList;

    /**商品编码**/
    @ApiModelProperty(position = 10, value = "商品编码")
    private String commodityCode;

    /**商品名称**/
    @ApiModelProperty(position = 11, value = "商品名称")
    private String commodityName;

    /**规格**/
    @ApiModelProperty(position = 12, value = "规格")
    private String commoditySpec;

    /**计量单位**/
    @ApiModelProperty(position = 13, value = "计量单位")
    private String commodityUnitName;


    /**订货价**/
    @ApiModelProperty(position = 14, value = "订货价")
    private BigDecimal price;

    /**订货数量**/
    @ApiModelProperty(position = 15, value = "订货数量")
    private BigDecimal quantity;

    /**实发数量**/
    @ApiModelProperty(position = 16, value = "实发数量")
    private BigDecimal realDeliveryQuantity;

    /**结算金额**/
    @ApiModelProperty(position = 17, value = "结算金额")
    private BigDecimal settlePrice;

    @ApiModelProperty(position = 18, value = "档口")
    private String stallName;

}

package com.pinshang.qingyun.order.service;

import com.pinshang.qingyun.base.configure.FeignClientConfiguration;
import com.pinshang.qingyun.base.constant.ApplicationNameConstant;
import com.pinshang.qingyun.order.hystrix.SubOrderConfirmClientHystrix;
import com.pinshang.qingyun.order.hystrix.SyncOrderJobClientHystrix;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

@FeignClient(value = ApplicationNameConstant.QINGYUN_ORDER_SERVICE, fallbackFactory = SubOrderConfirmClientHystrix.class, configuration = FeignClientConfiguration.class)
public interface SubOrderConfirmClient {
	
	@RequestMapping(value = "/subOrder/confirmSO2DO", method = RequestMethod.POST)
	void confirmSO2DO();
	
}

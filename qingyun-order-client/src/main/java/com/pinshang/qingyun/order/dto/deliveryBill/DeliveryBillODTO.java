package com.pinshang.qingyun.order.dto.deliveryBill;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * @description: 送货单查询对象
 * @author: hhf
 * @time: 2021/8/4 14:29
 */
@Data
public class DeliveryBillODTO {

    /**订单id**/
    @ApiModelProperty("订单id")
    private String orderId;
    /**送货日期**/
    @ApiModelProperty("送货日期")
    private Date orderTime;
    /**配送单号**/
    @ApiModelProperty("配送单号")
    private String DeliveryBillCode;
    /**订单编号**/
    @ApiModelProperty("订单编号")
    private String orderCode;

    /**客户编码**/
    @ApiModelProperty("客户编码")
    private String storeCode;
    /**客户名称**/
    @ApiModelProperty("客户名称")
    private String storeName;
    /**配送线路**/
    @ApiModelProperty("配送线路")
    private String LineName;
    /**送货员**/
    @ApiModelProperty("送货员")
    private String deliveryManName;
}

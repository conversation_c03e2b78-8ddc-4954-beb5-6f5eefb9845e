package com.pinshang.qingyun.order.service;

import com.pinshang.qingyun.base.configure.FeignClientConfiguration;
import com.pinshang.qingyun.base.constant.ApplicationNameConstant;
import com.pinshang.qingyun.order.hystrix.XDAOrderClientHystrix;
import com.pinshang.qingyun.order.hystrix.XdaRechargeClientHystrix;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

@FeignClient(value = ApplicationNameConstant.QINGYUN_ORDER_SERVICE, fallbackFactory = XdaRechargeClientHystrix.class, configuration = FeignClientConfiguration.class)
public interface XdaRechargeClient {

    @RequestMapping(value = "/recharge/xdaAppRechargeJob", method = RequestMethod.GET)
    void xdaAppRechargeJob();

}

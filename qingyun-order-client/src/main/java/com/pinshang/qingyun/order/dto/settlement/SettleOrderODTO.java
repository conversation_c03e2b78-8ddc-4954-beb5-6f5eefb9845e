package com.pinshang.qingyun.order.dto.settlement;

import com.pinshang.qingyun.base.enums.settlement.SettleOrderSourceTypeEnum;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@Data
public class SettleOrderODTO {
    private String kid;
    private String uid;
    private String sourceType;
    private String orderCode;
    private Long sourceId;
    private BigDecimal deliveryTotalAmount;
    private String storeCode;
    private Long supervisorId;
    private Long storeTypeId;
    private String stockOutOrderCode;
    private Long officeDirectorId;
    private Long salesmanId;
    private Long deliverymanId;
    private Long storeLineId;
    private BigDecimal totalAmount;
    private Long storeId;
    private String storeName;
    private Long settlmentId;
    private String settlementName;
    private Date deliveryTime;
    private Date orderTime;
    private int logisticsModel;
    private Long regionManagerId;
    private String deliveryAddress;

    public String getSourceType() {
        SettleOrderSourceTypeEnum settleOrderSourceTypeEnum = SettleOrderSourceTypeEnum.fromCode(logisticsModel);
        return settleOrderSourceTypeEnum == null ? "未知类型：" + logisticsModel : settleOrderSourceTypeEnum.name();
    }


    public BigDecimal getDeliveryTotalAmount() {
        return deliveryTotalAmount == null?BigDecimal.ZERO:deliveryTotalAmount;
    }
}

package com.pinshang.qingyun.order.dto;

import com.pinshang.qingyun.base.enums.ObtainReturnOrderStatusEnum;
import com.pinshang.qingyun.base.enums.SaleReturnOrderStatusEnums;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
public class SaleReturnOrderPDAODTO {
    @ApiModelProperty("退货单日期 / 退领单创建日期 yyyy-MM-dd")
    private Date returnOrderDate;
    @ApiModelProperty("退货单号")
    private String returnOrderCode;
    @ApiModelProperty("退货ID")
    private String returnOrderId;
    /**
     * 资产退货兼容老版本，状态与退领单一致：2=待入库、4=入库中; 同时新增commonStatus，以统一返回各业务单据状态
     */
    @ApiModelProperty("退货单状态 / 退领单状态:状态:0=已取消，1=待审核，2=待入库，3=驳回，4=入库中，5=已完成 ")
    private Integer status;
    @ApiModelProperty("业务类型: 1.门店退货 2.领料退货")
    private Integer bizType;

    @ApiModelProperty("各退货业务返回给PDA的公共状态：1=待入库，2=入库中；PDA查看详情根据此值判断是否需要先验证操作人")
    private Integer commonStatus;

    public Integer getBizType() {
        return bizType==null?1:bizType;
    }

    public Integer getCommonStatus() {
        if(commonStatus!=null){
            return commonStatus;
        }
        if(this.getBizType()==null || this.getBizType()==1){
            if(status.equals(SaleReturnOrderStatusEnums.WAITING_STOCK_IN.getCode())){
                commonStatus = 1;
            }
            if(status.equals(SaleReturnOrderStatusEnums.PROCESS_STOCK_IN.getCode())){
                commonStatus = 2;
            }
        }else{
            // 基于PDA端代码验证，后续新增的退货业务，status都应与退领单状态一致，同时新增commonStatus
            if(status.equals(ObtainReturnOrderStatusEnum.TO_STOCK.getCode())){
                commonStatus = 1;
            }
            if(status.equals(ObtainReturnOrderStatusEnum.STOCK.getCode())){
                commonStatus = 2;
            }
        }
        return commonStatus;
    }
}
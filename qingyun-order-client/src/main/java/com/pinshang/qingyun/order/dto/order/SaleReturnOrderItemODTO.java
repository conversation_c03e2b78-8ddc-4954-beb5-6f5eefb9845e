package com.pinshang.qingyun.order.dto.order;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2018/10/15 17:18.
 * @blog http://linuxsogood.org
 */
@Data
public class SaleReturnOrderItemODTO {

    @ApiModelProperty("商品ID")
    private String commodityId;
    @ApiModelProperty("商品名称")
    private String commodityName;
    @ApiModelProperty("商品编码")
    private String commodityCode;
    @ApiModelProperty("规格")
    private String commoditySpec;
    @ApiModelProperty("单位")
    private String unitName;
    @ApiModelProperty("退货数量")
    private BigDecimal returnQuantity;
    @ApiModelProperty("退货原因")
    private Integer returnReason;
    @ApiModelProperty("备注 / 退领原因")
    private String remark;
    @ApiModelProperty("批次管理")
    private Integer batchStatus;
    @ApiModelProperty("存储位")
    private String storageShelfNo;
    @ApiModelProperty("拣货位id")
    private Long pickShelfId;
    @ApiModelProperty("拣货位")
    private String pickShelfNo;
    @ApiModelProperty("锁库状态0为未锁 1位锁库")
    private Integer lockStatus;
    @ApiModelProperty("入库数量")
    private BigDecimal stockInQuantity;
    @ApiModelProperty("报损数量")
    private BigDecimal breakage;
    @ApiModelProperty("主键ID")
    private Long id;
    @ApiModelProperty("退货单表主键ID")
    private String saleReturnOrderId;
    @ApiModelProperty("退货价格")
    private BigDecimal price;
    @ApiModelProperty("实退数量（合格数量+报损数量）")
    private BigDecimal realReturnQuantity;
    @ApiModelProperty("报损数量")
    private BigDecimal breakageQuantity;
    @ApiModelProperty("条形码")
    private String barCode;
    @ApiModelProperty("业务类型: 1.门店退货 2.领料退货")
    private Integer bizType;
}

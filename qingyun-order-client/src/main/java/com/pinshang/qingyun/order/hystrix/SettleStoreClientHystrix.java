package com.pinshang.qingyun.order.hystrix;

import com.github.pagehelper.PageInfo;
import com.pinshang.qingyun.order.dto.SettlementStoreIDTO;
import com.pinshang.qingyun.order.dto.SettlementStoreODTO;
import com.pinshang.qingyun.order.dto.StoreIDTO;
import com.pinshang.qingyun.order.dto.StoreODTO;
import com.pinshang.qingyun.order.dto.shop.SettlementDetailsReportIDTO;
import com.pinshang.qingyun.order.dto.shop.SettlementDetailsReportODTO;
import com.pinshang.qingyun.order.service.SettleStoreClient;
import feign.hystrix.FallbackFactory;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

@Component
public class SettleStoreClientHystrix implements FallbackFactory<SettleStoreClient> {
    @Override
    public SettleStoreClient create(Throwable cause) {
        return new SettleStoreClient() {
//            @Override
//            public Map<Long,StoreODTO> selectStoreList(StoreIDTO storeIDTO) {
//                return null;
//            }
//
//            @Override
//            public Map<Long, SettlementStoreODTO> selectSettlementStoreList(StoreIDTO storeIDTO) {
//                return null;
//            }

            @Override
            public PageInfo<SettlementStoreODTO> selectSettleStore(SettlementStoreIDTO settlementStoreIDTO) {
                return null;
            }

            @Override
            public PageInfo<StoreODTO> selectStore(StoreIDTO storeIDTO) {
                return null;
            }

            @Override
            public List<StoreODTO> selectStoreByStores(String storeCodes) {
                return null;
            }
        };
    }
}

package com.pinshang.qingyun.order.dto.subOrder;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * @auther dy
 * @date 2023/12/19  14:34
 **/
@Data
public class SubOrderItemODTO {
    private Long id;
    //子订单id
    private Long subOrderId;
    //商品id
    private Long commodityId;
    //数量
    private BigDecimal quantity;
    //单价
    private BigDecimal price;
    private BigDecimal totalPrice;

    //实收数量(门店收货)
    private BigDecimal realReceiveQuantity;
    //状态: 0-审核未通过,1-审核通过
    private Integer status;
    //real_delivery_quantity实发数量(暂时没用)
    private BigDecimal realDeliveryQuantity;
    //驳回原因
    private String rejectReason;
    // 商品价格是否可变
    private Integer changePriceStatus;

    //组合商品转换的最小单位商品ID
    private Long targetCommodityId;
    //组合商品转换状态：0=无转换，1=有转换
    private Integer convertStatus;

    //组合商品源转换比率
    private Integer sourceRatio;

    //组合商品目标转换比率
    private Integer targetRatio;

    //组合商品目标数量 : 向上取整 ( quantity / source_ratio * target_ratio)
    private BigDecimal targetQuantity;
    private Date createTime;
    private Long createId;
    private Date updateTime;
    private Long updateId;
}

package com.pinshang.qingyun.order.service;

import com.pinshang.qingyun.base.configure.FeignClientConfiguration;
import com.pinshang.qingyun.base.constant.ApplicationNameConstant;
import com.pinshang.qingyun.order.dto.orderMonitor.OrderMonitorODTO;
import com.pinshang.qingyun.order.dto.orderMonitor.SubOrderMonitorODTO;
import com.pinshang.qingyun.order.hystrix.OrderInfoMonitorClientHystrix;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2018/10/12 14:46
 */
@FeignClient(value = ApplicationNameConstant.QINGYUN_ORDER_SERVICE, fallbackFactory = OrderInfoMonitorClientHystrix.class, configuration = FeignClientConfiguration.class)
public interface OrderInfoMonitorClient {
    @RequestMapping(value = "/orderMonitor/queryOrderInfo/{orderCode}", method = RequestMethod.GET)
    OrderMonitorODTO getMonitorOrderInfo(@PathVariable("orderCode") String orderCode);

    @RequestMapping(value = "/orderMonitor/querySubOrderInfo/{orderCode}", method = RequestMethod.GET)
    List<SubOrderMonitorODTO> getMonitorSubOrderInfo(@PathVariable("orderCode") String orderCode);
}

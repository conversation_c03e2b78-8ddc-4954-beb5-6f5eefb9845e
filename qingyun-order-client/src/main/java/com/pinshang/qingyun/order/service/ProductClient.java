package com.pinshang.qingyun.order.service;

import java.util.List;
import java.util.Map;

import com.pinshang.qingyun.order.dto.commodity.*;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import com.github.pagehelper.PageInfo;
import com.pinshang.qingyun.base.configure.FeignClientConfiguration;
import com.pinshang.qingyun.base.constant.ApplicationNameConstant;
import com.pinshang.qingyun.order.hystrix.ProductClientHystrix;
import org.springframework.web.bind.annotation.RequestParam;

@FeignClient(value = ApplicationNameConstant.QINGYUN_ORDER_SERVICE, fallbackFactory = ProductClientHystrix.class, configuration = FeignClientConfiguration.class)
public interface ProductClient{

	/**
	 * 获取门店订货列表->带条件查询
	 */
	@RequestMapping(value = "/product/findCommodityListByPage", method = RequestMethod.POST)
	PageInfo<CommodityListODto>  findCommodityListByPage(@RequestBody CommodityListIDto commodityListIDto);

	/**
	 * 获取门店手持无码订货列表->带条件查询
	 */
	@RequestMapping(value = "/product/findCommodityHandListPage", method = RequestMethod.POST)
	PageInfo<CommodityListHandleODto>  findCommodityHandListPage(@RequestBody CommodityListHandleIDto commodityListHandleIDto);

	/**
	 * 手持》扫码订货->
	 */
	@RequestMapping(value = "/product/findCommodityByBarcodeOrId", method = RequestMethod.POST)
	CommodityHandleODTO  findCommodityByBarcodeOrId(@RequestBody CommodityListHandleIDto commodityListHandleIDto);

	@RequestMapping(value = "/product/findPreCommodityByParam", method = RequestMethod.POST)
	List<PreCommodityODTO> findPreCommodityByParam(@RequestBody PreCommodityIDTO preCommodityIDTO);

	@RequestMapping(value = "/product/getToBPromotionPrice", method = RequestMethod.GET)
	Map<String,CommodityResultODTO> getToBPromotionPrice(@RequestParam("storeId")String storeId);

	/**
	 * pos折扣码查询使用
	 * @param barCode
	 * @param storeId
	 * @return
	 */
	@RequestMapping(value = "/product/couponQuery", method = RequestMethod.GET)
	CouponCodeQueryODTO couponQuery(@RequestParam("barCode")String barCode,@RequestParam("storeId") Long storeId);
}

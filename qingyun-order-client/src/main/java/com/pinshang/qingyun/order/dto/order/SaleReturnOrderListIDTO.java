package com.pinshang.qingyun.order.dto.order;

import com.pinshang.qingyun.order.dto.Pagination;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

@Data
public class SaleReturnOrderListIDTO extends Pagination {

    /**退货日期 开始日期*/
    @ApiModelProperty("退货日期 开始日期")
    private Date returnOrderStartDate;

    /**退货日期 结束日期*/
    @ApiModelProperty("退货日期 结束日期")
    private Date returnOrderEndDate;

    /**退货单号*/
    @ApiModelProperty("退货单号")
    private String returnOrderCode;

    /**退货单状态*/
    @ApiModelProperty("退货单状态")
    private Integer status;

    /**客户 名称/编码/助记码 模糊搜索条件*/
    @ApiModelProperty("客户 名称/编码/助记码 模糊搜索条件")
    private String storeFuzzy;

    /**仓库ID*/
    @ApiModelProperty("仓库ID")
    private Long warehouseId;

    // 退货原因
    private Boolean ifReturnShort = false;
}

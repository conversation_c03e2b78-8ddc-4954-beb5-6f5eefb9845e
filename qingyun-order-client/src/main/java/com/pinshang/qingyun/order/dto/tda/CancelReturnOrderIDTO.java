/*
 * tramy.cn Inc.
 * Copyright (c) 2021-2024 All Rights Reserved.
 */
package com.pinshang.qingyun.order.dto.tda;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <p>
 * 退货单取消
 * </p>
 *
 * <AUTHOR> shenyang
 * @version : 1.0.0
 * @history : modify history
 * <author>              <time>              <version>              <desc>
 * @since : 2024/05/11 10:09
 */
@Data
@NoArgsConstructor
public class CancelReturnOrderIDTO {
    /**
     * 退货单id
     */
    @ApiModelProperty(value = "退货单id")
    private Long id;

    /**
     * 取消来源，1-退货单上取消 ，2-投诉单取消
     */
    private Integer cancelSource;
    /**
     * 退货单序号
     */
    private String returnOrderSeq;

    public CancelReturnOrderIDTO(Integer cancelSource, String returnOrderSeq) {
        this.cancelSource = cancelSource;
        this.returnOrderSeq = returnOrderSeq;
    }
}

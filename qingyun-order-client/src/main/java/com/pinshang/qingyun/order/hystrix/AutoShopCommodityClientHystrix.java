package com.pinshang.qingyun.order.hystrix;

import com.pinshang.qingyun.order.dto.auto.AutoCommodityVO;
import com.pinshang.qingyun.order.service.AutoShopCommodityClient;
import com.pinshang.qingyun.order.service.PfOrderClient;
import feign.hystrix.FallbackFactory;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;

@Component
public class AutoShopCommodityClientHystrix implements FallbackFactory<AutoShopCommodityClient> {
	
	@Override
	public AutoShopCommodityClient create(Throwable throwable) {
		return new AutoShopCommodityClient() {


			@Override
			public AutoCommodityVO commodityInfo(String barCode) {
				return null;
			}

			@Override
			public Boolean insertAutoOrderCommodity(Long commodityId, BigDecimal stockQuantity) {
				return null;
			}
		};
	}
}

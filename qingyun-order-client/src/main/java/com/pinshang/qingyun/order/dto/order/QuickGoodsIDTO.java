package com.pinshang.qingyun.order.dto.order;

import java.util.List;

/*
 * 快速订货IDTO
 */
public class QuickGoodsIDTO {
	private Long enterpriseId;
	private Long storeId;
	private Long createId;
	private Boolean isInternal;
	private List<QuickGoodsItemIDTO> list;
	//是否 管理员操作
	private Boolean ifAdmin = false;
	private Long stallId;
	/** true 大店  false 非大店*/
	private Boolean bigShop = false;

	public Boolean getBigShop() {
		return bigShop;
	}

	public void setBigShop(Boolean bigShop) {
		this.bigShop = bigShop;
	}

	public Long getStallId() {
		return stallId;
	}

	public void setStallId(Long stallId) {
		this.stallId = stallId;
	}

	public Long getEnterpriseId() {
		return enterpriseId;
	}
	public void setEnterpriseId(Long enterpriseId) {
		this.enterpriseId = enterpriseId;
	}
	public Long getStoreId() {
		return storeId;
	}
	public void setStoreId(Long storeId) {
		this.storeId = storeId;
	}
	public Long getCreateId() {
		return createId;
	}
	public void setCreateId(Long createId) {
		this.createId = createId;
	}
	public List<QuickGoodsItemIDTO> getList() {
		return list;
	}
	public void setList(List<QuickGoodsItemIDTO> list) {
		this.list = list;
	}
	public Boolean getIsInternal() {
		return isInternal;
	}
	public void setIsInternal(Boolean isInternal) {
		this.isInternal = isInternal;
	}
	public Boolean getIfAdmin() {
		return ifAdmin;
	}
	public void setIfAdmin(Boolean ifAdmin) {
		this.ifAdmin = ifAdmin;
	}
}

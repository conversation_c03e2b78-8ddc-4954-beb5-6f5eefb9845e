package com.pinshang.qingyun.order.dto.shop;

import com.pinshang.qingyun.order.dto.Pagination;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * Created by hhf on 2018/6/12.
 * 结算明细报表
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class SettlementDetailsReportIDTO extends Pagination {

    /**门店**/
    @ApiModelProperty(position = 1, value = "门店id")
    private Long shopId;

    /**送货日期-开始**/
    @ApiModelProperty(position = 2, value = "送货日期-开始")
    private String deliveryTimeStart;

    /**送货日期-结束**/
    @ApiModelProperty(position = 3, value = "送货日期-结束")
    private String deliveryTimeEnd;

    /**结算日期-开始**/
    @ApiModelProperty(position = 4, value = "结算日期-开始")
    private String settleTimeStart;

    /**结算日期-结束**/
    @ApiModelProperty(position = 5, value = "结算日期-结束")
    private String settleTimeEnd;

    /**物流模式 0-直送, 1-配送, 2-直通**/
    @ApiModelProperty(position = 6, value = "物流模式 0-直送, 1-配送, 2-直通")
    private Integer logisticsModel;

    /**分类**/
    @ApiModelProperty(position = 7, value = "分类")
    private Long categoryId;

    /**条码**/
    @ApiModelProperty(position = 8, value = "条码")
    private String barCode;

    /**商品**/
    @ApiModelProperty(position = 9, value = "商品")
    private String commodityCode;

    /**订单编号**/
    @ApiModelProperty(position = 10, value = "订单编号")
    private String subOrderCode;

    /**预订单编号**/
    @ApiModelProperty(position = 11, value = "预订单编号")
    private String preOrderCode;

    /**客户类型**/
    @ApiModelProperty(position = 12, value = "客户类型")
    private Long storeTypeId;

    //门店id集合:当前登录用户所关联门店列表
    private List<Long> shopIdList;

    /**代销商id**/
    @ApiModelProperty(position = 14, value = "代销商id")
    private Long consignmentId;

    /**订单来源: 参考: OrderType  枚举**/
    @ApiModelProperty(position = 13, value = "1=PC下单,2=APP下单, 8=鲜达APP,9-清美批发App 10-门店订货下单 21-团购订单 22-门店订货PC版 23-门店订货PDA版 24-门店订货代理补货 25-大仓配货自动下单 26-门店自动订货 27-代理订货 28-直送补单 29-直送审核通过 30-提货卡自动下单 31-门店加货申请单")
    private Integer orderType;

    /**档口id**/
    @ApiModelProperty(position = 15, value = "档口id")
    private Long stallId;

}

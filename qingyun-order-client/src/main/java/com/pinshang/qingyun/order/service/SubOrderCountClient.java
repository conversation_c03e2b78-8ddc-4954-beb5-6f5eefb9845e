package com.pinshang.qingyun.order.service;

import com.pinshang.qingyun.base.configure.FeignClientConfiguration;
import com.pinshang.qingyun.base.constant.ApplicationNameConstant;
import com.pinshang.qingyun.order.dto.order.*;
import com.pinshang.qingyun.order.hystrix.SubOrderClientHystrix;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 */
@FeignClient(value = ApplicationNameConstant.QINGYUN_ORDER_SERVICE, fallbackFactory = SubOrderClientHystrix.class, configuration = FeignClientConfiguration.class)
public interface SubOrderCountClient {

	/**
	 * 该门店送货点数
	 * @return
	 */
	@GetMapping("/subOrderCount/shopOrderNum")
	ShopOrderNumVo shopOrderNum();

	/**
	 * 清点商品的信息
	 * @param barCode
	 * @return
	 */
	@GetMapping("/subOrderCount/countCommodityInfo")
	CountCommodityInfoVo countCommodityInfo(@RequestParam(value = "barCode", required = false) String barCode);

	/**
	 * 提交清点的商品,扫码录入，单个
	 * @param vo
	 * @return
	 */
	@PostMapping("/subOrderCount/submitCountCommodity")
	Boolean submitCountCommodity(@RequestBody CountCommoditySubmitVo vo);

	/**
	 * 提交清点的商品,逐个扫码，多个
	 * @param list
	 * @return
	 */
	@PostMapping("/subOrderCount/submitCountMultiCommodity")
	Boolean submitCountMultiCommodity(@RequestBody List<CountCommoditySubmitVo> list);

	/**
	 * 查看已点商品
	 * @return
	 */
	@GetMapping("/subOrderCount/countCommodityList")
	List<CountCommodityInfoVo> countCommodityList();

	@GetMapping("/subOrderCount/commodityInfo")
	CountCommodityInfoVo commodityInfo(@RequestParam(value = "barCode", required = false) String barCode);

	@PostMapping("/subOrderCount/redisDeleteCommodity")
	Boolean redisDeleteCommodity(@RequestBody List<Long> commodityIds);
}

package com.pinshang.qingyun.order.hystrix;

import com.pinshang.qingyun.order.service.SyncOrderJobClient;
import feign.hystrix.FallbackFactory;
import org.springframework.stereotype.Component;

@Component
public class SyncOrderJobClientHystrix implements FallbackFactory<SyncOrderJobClient> {
	@Override
	public SyncOrderJobClient create(Throwable throwable) {
		return new SyncOrderJobClient() {
			@Override
			public void syncOrderAndSO2DO(String coverTime) {

			}
		};
	}
}

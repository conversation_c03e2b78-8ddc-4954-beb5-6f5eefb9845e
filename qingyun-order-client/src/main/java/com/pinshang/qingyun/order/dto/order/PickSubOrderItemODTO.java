package com.pinshang.qingyun.order.dto.order;

import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2018/10/17 10:16.
 * @blog http://linuxsogood.org
 */
@Data
public class PickSubOrderItemODTO {

    private Long id;
    //
    private Long storeId;
    //商品id
    private Long commodityId;

    //要货数量
    private BigDecimal quantity;

    //实收数量(门店收货)
    private BigDecimal realReceiveQuantity;

    //real_delivery_quantity实发数量(暂时没用)
    private BigDecimal realDeliveryQuantity;

    //实发份数
    private Long realDeliveryShares;

    //是否处理过发货数据,用于发货单明细->子单明细,解决do单明细中普通商品与赠品一样,造成数据不对;
    private Boolean isDone =false;
}

package com.pinshang.qingyun.order.dto.shop;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;


@Data
public class TjPreOrderODTO {
	@ApiModelProperty("预订单ID")
	private Long orderId;

	@ApiModelProperty("预订单编号")
	private String orderCode;

	@ApiModelProperty("送货日期")
	private Date orderTime;

	@ApiModelProperty("客户ID")
	private Long storeId;

	@ApiModelProperty("客户编码")
	private String storeCode;

	@ApiModelProperty("客户名称")
	private String storeName;

	@ApiModelProperty("商品ID")
	private Long commodityId;

	@ApiModelProperty("商品编码")
	private String commodityCode;

	@ApiModelProperty("商品名称")
	private String commodityName;

	@ApiModelProperty("规格")
	private String commoditySpec;

	@ApiModelProperty("预定数量")
	private BigDecimal requireQuantity;

}

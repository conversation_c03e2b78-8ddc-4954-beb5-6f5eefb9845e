package com.pinshang.qingyun.order.hystrix;

import com.pinshang.qingyun.order.dto.settlement.OrderReqIDTO;
import com.pinshang.qingyun.order.dto.settlement.SettleOrderItemODTO;
import com.pinshang.qingyun.order.dto.settlement.SettleOrderODTO;
import com.pinshang.qingyun.order.service.SettleOrderClient;
import feign.hystrix.FallbackFactory;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public class SettleOrderClientHystrix implements FallbackFactory<SettleOrderClient> {

    @Override
    public SettleOrderClient create(Throwable cause) {
        return new SettleOrderClient() {

            @Override
            public List<SettleOrderODTO> findOrderByParams(OrderReqIDTO orderReqIDTO) {
                return null;
            }

            @Override
            public List<SettleOrderODTO> findXsZsOrderByParams(OrderReqIDTO orderReqIDTO) {
                return null;
            }

//            @Override
//            public List<SettleOrderODTO> findXsZtPsOrderByParams(OrderReqIDTO orderReqIDTO) {
//                return null;
//            }

            @Override
            public List<SettleOrderItemODTO> findXsOrderItemByOrderIds(List<Long> orderIds, int logisticsModel) {
                return null;
            }

            @Override
            public List<SettleOrderODTO> findTOrderByParams(OrderReqIDTO orderReqIDTO) {
                return null;
            }

            @Override
            public List<SettleOrderItemODTO> findTOrderItemByOrderIds(List<Long> orderIds) {
                return null;
            }
        };
    }
}

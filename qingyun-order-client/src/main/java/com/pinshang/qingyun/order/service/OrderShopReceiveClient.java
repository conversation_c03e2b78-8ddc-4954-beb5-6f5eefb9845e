package com.pinshang.qingyun.order.service;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import com.pinshang.qingyun.base.configure.FeignClientConfiguration;
import com.pinshang.qingyun.base.constant.ApplicationNameConstant;
import com.pinshang.qingyun.order.dto.commodity.ShopReceiveOrderIDTO;
import com.pinshang.qingyun.order.hystrix.OrderShopReceiveCilentHystrix;

@FeignClient(name = ApplicationNameConstant.QINGYUN_ORDER_SERVICE, fallbackFactory = OrderShopReceiveCilentHystrix.class, configuration = FeignClientConfiguration.class)
@Service
public interface OrderShopReceiveClient {
    /*
     * 新增门店收货单
     */
    @RequestMapping(value = "/shopReceive/newReceiveOrder",method = RequestMethod.POST)
    public void newReceiveOrder(@RequestBody ShopReceiveOrderIDTO shopReceiveOrderIDTO) throws Throwable;
}

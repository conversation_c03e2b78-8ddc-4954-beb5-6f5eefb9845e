package com.pinshang.qingyun.order.service;

import com.github.pagehelper.PageInfo;
import com.pinshang.qingyun.base.configure.FeignClientConfiguration;
import com.pinshang.qingyun.base.constant.ApplicationNameConstant;
import com.pinshang.qingyun.order.dto.order.*;
import com.pinshang.qingyun.order.hystrix.OrderSyncToDcClientHystrix;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@FeignClient(value = ApplicationNameConstant.QINGYUN_ORDER_SERVICE, fallbackFactory = OrderSyncToDcClientHystrix.class, configuration = FeignClientConfiguration.class)
public interface OrderSyncToDcClient {
	/**
     * B端订单补偿到大仓--查询
	 * @param idto
     * @return
	 */
	@RequestMapping(value = "/orderSync/client/queryOrderSyncToDcList", method = RequestMethod.POST)
	List<OrderSyncToDcODTO> queryOrderSyncToDcList(@RequestBody OrderSyncToDcIDTO idto);

	/**
	 * 批量同步B端订单到大仓
	 * @param orderIds
	 * @return
	 */
	@RequestMapping(value = "/orderSync/client/getOrderListByIds", method = RequestMethod.POST)
	List<OrderODTO> getOrderListByIds(@RequestParam("orderIds") List<Long> orderIds);

	/**
	 * 批量同步B端订单到大仓--子单维度
	 * @param subOrderIds
	 * @return
	 */
	@RequestMapping(value = "/orderSync/client/queryOrderListBySubOrderIds", method = RequestMethod.POST)
	List<OrderODTO> queryOrderListBySubOrderIds(@RequestParam("subOrderIds") List<Long> subOrderIds);

	/**
	 *
	 * @param idto
	 * @return
	 */
	@RequestMapping(value = "/orderSync/client/queryOrderPage", method = RequestMethod.POST)
	PageInfo<OrderSyncToDcODTO> queryOrderPage(@RequestBody OrderSyncToDcIDTO idto);

}

package com.pinshang.qingyun.order.hystrix;

import com.pinshang.qingyun.order.service.XdaRechargeClient;
import feign.hystrix.FallbackFactory;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

@Component
public class XdaRechargeClientHystrix implements FallbackFactory<XdaRechargeClient> {
	private Logger logger = LoggerFactory.getLogger(getClass());
	
	@Override
	public XdaRechargeClient create(Throwable throwable) {
		return new XdaRechargeClient() {
            @Override
            public void xdaAppRechargeJob() {

            }
        };
	}
}

package com.pinshang.qingyun.order.hystrix;

import org.springframework.stereotype.Component;

import com.pinshang.qingyun.order.dto.commodity.ShopReceiveOrderIDTO;
import com.pinshang.qingyun.order.service.OrderShopReceiveClient;

import feign.hystrix.FallbackFactory;

@Component
public class OrderShopReceiveCilentHystrix  implements FallbackFactory<OrderShopReceiveClient> {


    @Override
    public OrderShopReceiveClient create(Throwable throwable) {
        return  new OrderShopReceiveClient() {
			@Override
			public void newReceiveOrder(
					ShopReceiveOrderIDTO shopReceiveOrderIDTO) throws Throwable {
				throw throwable.getCause();
			}
        };
    }
}

package com.pinshang.qingyun.order.service;

import com.pinshang.qingyun.base.configure.FeignClientConfiguration;
import com.pinshang.qingyun.base.constant.ApplicationNameConstant;
import com.pinshang.qingyun.order.dto.SaveOrderDeductionsIDTO;
import com.pinshang.qingyun.order.hystrix.SaleReturnOrderCilentHystrix;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * Created by crell on 2017/11/6.
 */
@FeignClient(name = ApplicationNameConstant.QINGYUN_ORDER_SERVICE, fallbackFactory = SaleReturnOrderCilentHystrix.class, configuration = FeignClientConfiguration.class)
@Service
public interface SaveOrderDeductionsClient {

    /**
     * 预付款扣款、回款
     * @param saveOrderDeductionsIDTO
     * @return
     */
    @RequestMapping(value = "/order/saveOrderDeductions", method = RequestMethod.POST)
    Boolean saveOrderDeductions(@RequestBody SaveOrderDeductionsIDTO saveOrderDeductionsIDTO);


}

package com.pinshang.qingyun.order.dto.orderStatistics;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 订单统计查询--订单同步返回结果
 */
@Data
public class OrderSyncODTO {
    private Long id;
    /**公司id**/
    private Long companyId;
    /**订单号 **/
    private String orderCode;
    /**商铺id **/
    private Long storeId;
    /**订单时间 **/
    private Date orderTime;
    /**订单类型：（0：普通订单，1：补货单） **/
    private Integer modeType;
    /**订单类型 **/
    private Integer orderType;
    /**打印份数 **/
    private Integer printNum;
    /**打印类型(1：本地,2：送货员,3：不打印) **/
    private Integer printType;
    /**应付金额（参与促销活动之前的源始金额） **/
    private BigDecimal totalAmount;
    /**订单金额 **/
    private BigDecimal orderAmount;
    /**订单最终金额 **/
    private BigDecimal finalAmount;
    /**订单运费金额（免运费为0）**/
    private BigDecimal freightAmount;
    /**备注 **/
    private String orderRemark;
    /**更新者 **/
    private Long updateId;
    private Date updateTime;
    /**订单状态(0正常,1删除) **/
    private Integer orderStatus;
    /**创建者 **/
    private Long createId;
    private Date createTime;

    private Integer    settleStatus;
    private Integer deliveryBatch;
    /** 订单是否允许修改价格 */
    private Integer changePriceStatus;

    //订单明细
    private List<OrderListGiftODTO> itemList;

    private String createName;
    private String updateName;

}

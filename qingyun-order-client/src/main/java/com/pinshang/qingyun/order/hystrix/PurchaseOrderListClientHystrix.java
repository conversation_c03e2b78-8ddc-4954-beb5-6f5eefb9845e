package com.pinshang.qingyun.order.hystrix;

import com.github.pagehelper.PageInfo;
import com.pinshang.qingyun.order.dto.purchase.*;
import com.pinshang.qingyun.order.service.PurchaseOrderListClient;
import feign.hystrix.FallbackFactory;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public class PurchaseOrderListClientHystrix implements FallbackFactory<PurchaseOrderListClient> {
	private Logger logger = LoggerFactory.getLogger(getClass());
	@Override
	public PurchaseOrderListClient create(Throwable throwable) {
		return new PurchaseOrderListClient() {

//			@Override
//			public PageInfo<ConnectionPurchaseODto> findConnectionPurchaseByPage(PurchaseListIDto purchaseListIDto) {
//				logger.error(throwable.getMessage());
//				return null;
//			}

			@Override
			public PageInfo<ConnectionPurchaseDetailODto> findConnectionPurchaseDetail(PurchaseListIDto purchaseListIDto) {
				logger.error(throwable.getMessage());
				return null;
			}

			@Override
			public List<DirectSendingPurchaseItemODto> findDirectSendingPurchaseItems(
					ShopPurchaseSearchIDto shopPurchaseSearchIDto) {
				logger.error(throwable.getMessage());
				return null;
			}
			@Override
			public List<DirectSendingPurchaseItemODto> findDirectSendingPurchaseItemList(
					ShopPurchaseSearchIDto shopPurchaseSearchIDto) {
				logger.error(throwable.getMessage());
				return null;
			}

			@Override
			public PageInfo<ConnectionPurchaseODto> findSendPurchaseByPage(PurchaseListIDto purchaseListIDto) {
				return null;
			}

			@Override
			public List<SendPurchaseDetailODto> findSendPurchaseDetail(PurchaseListIDto purchaseListIDto) {
				return null;
			}
		};
	}
}

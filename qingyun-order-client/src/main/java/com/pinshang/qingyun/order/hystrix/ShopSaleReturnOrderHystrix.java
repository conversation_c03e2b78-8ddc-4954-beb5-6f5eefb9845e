package com.pinshang.qingyun.order.hystrix;

import com.github.pagehelper.PageInfo;
import com.pinshang.qingyun.order.dto.*;
import com.pinshang.qingyun.order.dto.order.*;
import com.pinshang.qingyun.order.service.ShopSaleReturnOrderClient;
import feign.hystrix.FallbackFactory;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2018/10/15 10:16.
 * @blog http://linuxsogood.org
 */
@Component
public class ShopSaleReturnOrderHystrix implements FallbackFactory<ShopSaleReturnOrderClient> {
    @Override
    public ShopSaleReturnOrderClient create(Throwable cause) {
        return new ShopSaleReturnOrderClient() {
            @Override
            public SaleReturnOrderDetailODTO querySaleReturnDetail(String code, Long enterpriseId) {
                return null;
            }

            @Override
            public boolean processReturnOrderItemQuantity(SaleReturnOrderUpdateQuantityWrapperIDTO updateQuantityWrapper) {
                return false;
            }

            @Override
            public SaleReturnReportSumODTO queryReturnReportSumEntry(SaleReturnReportIDTO idto) {
                return null;
            }

            @Override
            public List<StoreInfoODTO> queryStoreInfo(StoreInfoIDTO idto) {
                return null;
            }

            @Override
            public CheckReceiverODTO checkReceiver(CheckReceiverIDTO idto) {
                return null;
            }

            @Override
            public SaleReturnOrderODTO selectByOrderCodeAndLogisticsModel(String returnOrderCode, List<Integer> asList) {
                return null;
            }

            @Override
            public PageInfo<SaleReturnReportODTO> listReturnReport(SaleReturnReportIDTO idto) {
                return null;
            }

            @Override
            public PageInfo<SaleReturnOrderListODTO> list(SaleReturnOrderListIDTO idto) {
                return null;
            }

            @Override
            public List<SaleReturnOrderPDAODTO> querySaleReturnOrderList(SaleReturnOrderPDAIDTO idto) {
                return null;
            }

            @Override
            public boolean updateOrderStatus(SaleReturnOrderIDTO idto) {
                return false;
            }

        };
    }
}

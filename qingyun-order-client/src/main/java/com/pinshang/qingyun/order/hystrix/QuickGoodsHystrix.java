package com.pinshang.qingyun.order.hystrix;

import com.pinshang.qingyun.order.dto.order.QuickGoodsIDTO;
import com.pinshang.qingyun.order.dto.order.QuickGoodsODTO;
import com.pinshang.qingyun.order.service.QuickGoodsClient;
import feign.hystrix.FallbackFactory;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

@Component
public class QuickGoodsHystrix  implements FallbackFactory<QuickGoodsClient> {
	private Logger logger = LoggerFactory.getLogger(getClass());
	@Override
	public QuickGoodsClient create(Throwable throwable) {
		return new QuickGoodsClient() {

			@Override
			public List<QuickGoodsODTO> findQuickGoodsList(QuickGoodsIDTO idto) {
				QuickGoodsODTO o =new QuickGoodsODTO();
				o.setCommodityName("系统出错,走断路器......");
				List list =new ArrayList<QuickGoodsODTO>();
				list.add(o);
				return list;
			}

			@Override
			public Integer deleteQuickGoodsByStoreId(QuickGoodsIDTO idto) {
				return 0;
			}

			@Override
			public Integer addShoppingCart(QuickGoodsIDTO idto) {
				return 0;
			}

			@Override
			public Boolean saveQuickGoods(QuickGoodsIDTO idto) throws Throwable {
				logger.error("QuickGoodsHystrix saveQuickGoods" +throwable.getMessage());
				throw throwable.getCause();
			}

			@Override
			public Boolean saveQuickGoods4admin(QuickGoodsIDTO idto) throws Throwable {
				return null;
			}

		};
	}
}

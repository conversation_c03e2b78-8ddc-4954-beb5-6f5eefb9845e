package com.pinshang.qingyun.order.dto.sync;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2024/7/26 9:32
 */
@Data
public class SyncOrderODTO {

    private Long orderId;

    private String orderCode;

    /**订单时间*/
    private Date orderTime;

    /**订单类型*/
    private Integer orderType;

    /**应付金额（参与促销活动之前的源始金额）*/
    private BigDecimal totalAmount;

    /**最终金额*/
    private BigDecimal finalAmount;

    /**订单金额*/
    private BigDecimal orderAmount;

    /**订单运费金额（免运费为0）*/
    private BigDecimal freightAmount;

    private Long logisticsCenterId;

    private Integer businessType;


    private List<SyncOrderListODTO> syncOrderListODTOList;

    public String toSyncString() {
        if(null != totalAmount){
            totalAmount = totalAmount.stripTrailingZeros();
        } else {
        	totalAmount = new BigDecimal("0");
        }
        if(null != finalAmount){
            finalAmount = finalAmount.stripTrailingZeros();
        }
        if(null != orderAmount){
            orderAmount = orderAmount.stripTrailingZeros();
        }
        if(null != freightAmount){
            freightAmount = freightAmount.stripTrailingZeros();
        }else {
            freightAmount = new BigDecimal("0");
        }
        return
                "orderId=" + orderId +
                        ", orderTime=" + orderTime +
                        ", orderType=" + orderType +
                        ", totalAmount=" + totalAmount +
                        ", finalAmount=" + finalAmount +
                        ", orderAmount=" + orderAmount +
                        ", freightAmount=" + freightAmount+
                        ", logisticsCenterId=" + logisticsCenterId +
                        ", businessType=" + businessType
                ;
    }
}

package com.pinshang.qingyun.order.dto.order;

import com.pinshang.qingyun.order.dto.Pagination;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

@Data
public class SaleReturnOrderListODTO extends Pagination {

    /** 客户名称 */
    @ApiModelProperty("客户名称")
    private String storeName;

    /** 退货单日期 */
    @ApiModelProperty("退货单日期")
    private Date returnOrderDate;

    /** 退货单号 */
    @ApiModelProperty("退货单号")
    private String returnOrderCode;

    /** 仓库 */
    @ApiModelProperty("仓库")
    private String warehouseName;

    /** 退货单状态 */
    @ApiModelProperty("退货单状态")
    private Integer status;

    /** 操作人 */
    @ApiModelProperty("收货人")
    private String updateName;

    /** 操作时间 */
    @ApiModelProperty("操作时间")
    private Date updateTime;

    //退货人
    @ApiModelProperty("退货人")
    private String createName;

    @ApiModelProperty("品类数")
    private Integer categoryNum;
}

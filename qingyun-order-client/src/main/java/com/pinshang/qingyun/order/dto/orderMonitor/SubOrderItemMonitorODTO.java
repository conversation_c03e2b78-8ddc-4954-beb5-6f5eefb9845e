package com.pinshang.qingyun.order.dto.orderMonitor;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @Date 2018/10/12 17:44
 */
@Data
@ApiModel
public class SubOrderItemMonitorODTO {
    /**
     * 订单明细ID
     */
    @ApiModelProperty("订单明细ID")
    private Long itemId;
    /**
     * 商品id
     */
    @ApiModelProperty("商品id")
    private Long commodityId;
    /**
     * 商品编码
     */
    @ApiModelProperty("商品编码")
    private String commodityCode;
    /**
     * 商品名称
     */
    @ApiModelProperty("商品名称")
    private String commodityName;
    /**
     * 规格
     */
    @ApiModelProperty("规格")
    private String commoditySpec;
    /**
     * 商品数量
     */
    @ApiModelProperty("商品数量")
    private BigDecimal quantity;
    /**
     * 价格
     */
    @ApiModelProperty("价格")
    private BigDecimal price;
    /**
     * 实收数量(门店收货)
     */
    private BigDecimal realReceiveQuantity;
    /**
     * real_delivery_quantity实发数量(大仓)
     */
    private BigDecimal realDeliveryQuantity;
    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    private Date createTime;

    /**
     * 商品类型-1-非组合 2-组合  3-组合子品
     */
    @ApiModelProperty("商品类型-1-非组合 2-组合  3-组合子品")
    private Integer combType;


    /**
     * 商品类型名称
     */
    @ApiModelProperty("商品类型名称")
    private String combTypeName;



    /**
     * 组合商品id
     */
    @ApiModelProperty("组合商品id")
    private Long groupCommodityId;

    /**
     * 组合商品编码
     */
    @ApiModelProperty("组合商品编码")
    private String groupCommodityCode;

    /**
     * 组合商品名称
     */
    @ApiModelProperty("组合商品名称")
    private String groupSubCommodityName;
}

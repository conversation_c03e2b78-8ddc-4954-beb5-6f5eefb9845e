package com.pinshang.qingyun.order.hystrix;

import com.pinshang.qingyun.order.dto.AutoShopCommodityODTO;
import com.pinshang.qingyun.order.dto.store.AppUserInfoIDTO;
import com.pinshang.qingyun.order.service.AppUserClient;
import com.pinshang.qingyun.order.service.ShopAutoOrderClient;
import feign.hystrix.FallbackFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2018/10/10 10:54
 */
@Component
@Slf4j
public class ShopAutoOrderClientHystrix implements FallbackFactory<ShopAutoOrderClient> {
    @Override
    public ShopAutoOrderClient create(Throwable throwable) {
       return new ShopAutoOrderClient() {
           @Override
           public List<AutoShopCommodityODTO> queryAutoShopList() {
               return null;
           }
       };
    }
}

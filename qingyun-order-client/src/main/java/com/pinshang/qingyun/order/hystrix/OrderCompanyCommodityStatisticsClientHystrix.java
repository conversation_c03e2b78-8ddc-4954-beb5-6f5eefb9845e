package com.pinshang.qingyun.order.hystrix;


import com.pinshang.qingyun.order.service.OrderCompanyCommodityStatisticsClient;
import feign.hystrix.FallbackFactory;
import org.springframework.stereotype.Component;


@Component
public class OrderCompanyCommodityStatisticsClientHystrix implements FallbackFactory<OrderCompanyCommodityStatisticsClient> {
    @Override
    public OrderCompanyCommodityStatisticsClient create(Throwable throwable) {
        return new OrderCompanyCommodityStatisticsClient() {


            @Override
            public void createOrderCompanyCommodityStatisticsJob(String[] orderTimes) {

            }
        };
    }

}

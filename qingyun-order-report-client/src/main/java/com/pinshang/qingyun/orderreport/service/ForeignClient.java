package com.pinshang.qingyun.orderreport.service;

import com.pinshang.qingyun.base.configure.FeignClientConfiguration;
import com.pinshang.qingyun.base.constant.ApplicationNameConstant;
import com.pinshang.qingyun.orderreport.dto.CommodityQuantityIDTO;
import com.pinshang.qingyun.orderreport.dto.CommodityQuantityIDTO2;
import com.pinshang.qingyun.orderreport.dto.CommodityQuantityODTO;
import com.pinshang.qingyun.orderreport.hystrix.DeliveryNoteClientHystrix;
import com.pinshang.qingyun.orderreport.hystrix.ForeignClientHystrix;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import java.util.List;

/**
 * @author: chen<PERSON>ang
 * @time: 22/7/20/020 14:41
 */
@FeignClient(value = ApplicationNameConstant.QINGYUN_ORDER_REPORT_SERVICE, fallbackFactory = ForeignClientHystrix.class, configuration = FeignClientConfiguration.class)
public interface ForeignClient {

    @RequestMapping(value = "/foreign/selectOrderCountCommodityQuantityByOrderTime",method = RequestMethod.POST)
    List<CommodityQuantityODTO> selectOrderCountCommodityQuantityByOrderTime(@RequestBody CommodityQuantityIDTO commodityQuantityIDTO);

    @RequestMapping(value = "/foreign/selectOrderCountCommodityQuantityByOrderTimeV2",method = RequestMethod.POST)
    List<CommodityQuantityODTO> selectOrderCountCommodityQuantityByOrderTimeV2(@RequestBody CommodityQuantityIDTO2 commodityQuantityIDTO);
}

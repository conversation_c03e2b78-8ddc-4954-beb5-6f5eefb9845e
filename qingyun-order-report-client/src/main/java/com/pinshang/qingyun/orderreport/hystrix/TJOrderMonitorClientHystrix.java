package com.pinshang.qingyun.orderreport.hystrix;


import com.pinshang.qingyun.orderreport.dto.TJOrderMonitorIDTO;
import com.pinshang.qingyun.orderreport.dto.TJOrderMonitorODTO;
import com.pinshang.qingyun.orderreport.service.TJOrderMonitorClient;
import feign.hystrix.FallbackFactory;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public class TJOrderMonitorClientHystrix implements FallbackFactory<TJOrderMonitorClient> {
    @Override
    public TJOrderMonitorClient create(Throwable throwable) {
        return new TJOrderMonitorClient() {

            @Override
            public TJOrderMonitorODTO queryOrderInfo(TJOrderMonitorIDTO idto) {
                return null;
            }

            @Override
            public List<String> queryOrderDiffList(TJOrderMonitorIDTO idto) {
                return null;
            }

            @Override
            public Integer syncTJOrder(TJOrderMonitorIDTO idto) {
                return null;
            }

            @Override
            public Integer removeTJOrderLatest() {
                return null;
            }

            @Override
            public Integer compensateTjOrderSync(String[] orderTimes) {
                return null;
            }

            @Override
            public Integer removeTJOrderSync() {
                return null;
            }
        };
    }

}

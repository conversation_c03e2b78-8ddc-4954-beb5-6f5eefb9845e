package com.pinshang.qingyun.orderreport.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2019/2/28 17:43.
 * 生产组(或外地)发货单 返回实体类
 */
@Data
public class WorkshopShipmentsPreviewODTO {

    @ApiModelProperty("生产组名称")
    private String workshopName;

    @ApiModelProperty("线路组名称")
    private String lineGroupName;

    @ApiModelProperty("发货仓库")
    private String deliveryHouse;

    @ApiModelProperty("订单开始时间")
    private Date endOrderDate;

    @ApiModelProperty("订单结束时间")
    private Date startOrderDate;

    @ApiModelProperty("发货时间")
    private String deliveryTime;

    @ApiModelProperty("客户类型")
    private String storeType;

    @ApiModelProperty("送货批次")
    private String deliveryBatch;


    @ApiModelProperty("补货数据选项")
    private String orderModeTypeName;

    @ApiModelProperty("表头")
    private List<String> tableHeader;

    @ApiModelProperty("表体")
    private List<List<String>> tableDate;

    @ApiModelProperty("表格底部合计")
    private List<String> tableBottom;
}

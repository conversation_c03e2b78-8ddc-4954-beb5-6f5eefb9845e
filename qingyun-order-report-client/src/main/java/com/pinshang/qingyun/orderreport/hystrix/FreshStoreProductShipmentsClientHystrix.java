package com.pinshang.qingyun.orderreport.hystrix;

import com.pinshang.qingyun.orderreport.dto.*;
import com.pinshang.qingyun.orderreport.service.FreshStoreProductShipmentsClient;
import feign.hystrix.FallbackFactory;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2019/3/11 15:14
 */
@Component
public class FreshStoreProductShipmentsClientHystrix implements FallbackFactory<FreshStoreProductShipmentsClient> {
    @Override
    public FreshStoreProductShipmentsClient create(Throwable throwable) {
        return new FreshStoreProductShipmentsClient() {

            @Override
            public FreshProductShipmentsODTO queryList(FreshProductShipmentsIDTO reqVo) {
                return null;
            }

            @Override
            public List<FreshProductShipmentsTempODTO> queryListV2(FreshProductShipmentsIDTO reqVo) {
                return null;
            }

            @Override
            public List<StoreODTO> queryFreshStoreList(List<Long> storeIdList) {
                return null;
            }
        };
    }
}

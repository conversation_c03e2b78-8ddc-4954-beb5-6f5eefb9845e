package com.pinshang.qingyun.orderreport.hystrix;

import com.pinshang.qingyun.orderreport.dto.ProductSaleStatisticsIDTO;
import com.pinshang.qingyun.orderreport.dto.ProductSaleStatisticsODTO;
import com.pinshang.qingyun.orderreport.dto.ProductSaleStatisticsPurchaseQueryDto;
import com.pinshang.qingyun.orderreport.dto.ProductSaleStatisticsSumODTO;
import com.pinshang.qingyun.orderreport.dto.ProductSaleStatisticsTempODTO;
import com.pinshang.qingyun.orderreport.service.ProductSaleStatisticsClient;
import feign.hystrix.FallbackFactory;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 产品销售汇总
 */
@Component
public class ProductSaleStatisticsClientHystrix implements FallbackFactory<ProductSaleStatisticsClient> {
    @Override
    public ProductSaleStatisticsClient create(Throwable cause) {
        return new ProductSaleStatisticsClient() {


            @Override
            public ProductSaleStatisticsODTO queryList(ProductSaleStatisticsIDTO idto) {
                return null;
            }

            @Override
            public List<ProductSaleStatisticsTempODTO> queryListV2(ProductSaleStatisticsIDTO idto) {
                return null;
            }

            @Override
            public ProductSaleStatisticsSumODTO querySum(ProductSaleStatisticsIDTO idto) {
                return null;
            }

            @Override
            public String print(ProductSaleStatisticsIDTO idto) {
                return null;
            }

            @Override
            public List<ProductSaleStatisticsTempODTO> queryListForPurchase(ProductSaleStatisticsPurchaseQueryDto dto) {
                return null;
            }

            @Override
            public List<ProductSaleStatisticsTempODTO> queryListForPurchaseByCreateTime(ProductSaleStatisticsPurchaseQueryDto queryVo) {
                return null;
            }
        };
    }
}

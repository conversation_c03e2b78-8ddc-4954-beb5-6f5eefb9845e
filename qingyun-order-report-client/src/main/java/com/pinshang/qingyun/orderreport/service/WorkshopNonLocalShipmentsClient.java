package com.pinshang.qingyun.orderreport.service;

import com.pinshang.qingyun.base.configure.FeignClientConfiguration;
import com.pinshang.qingyun.base.constant.ApplicationNameConstant;
import com.pinshang.qingyun.orderreport.dto.NonlocalShipmentsIDTO;
import com.pinshang.qingyun.orderreport.dto.NonlocalShipmentsODTO;
import com.pinshang.qingyun.orderreport.dto.ProductShipmentsRequestIDTO;
import com.pinshang.qingyun.orderreport.dto.WorkshopShipmentsPreviewODTO;
import com.pinshang.qingyun.orderreport.hystrix.WorkshopNonLocalShipmentsClientHystrix;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2019/3/4 13:58.
 */
@FeignClient(value = ApplicationNameConstant.QINGYUN_ORDER_REPORT_SERVICE, fallbackFactory = WorkshopNonLocalShipmentsClientHystrix.class, configuration = FeignClientConfiguration.class)
public interface WorkshopNonLocalShipmentsClient {

    @RequestMapping(value = "/statistical/workshopNonLocalShipments/preview",method = RequestMethod.POST)
    WorkshopShipmentsPreviewODTO preview(@RequestBody ProductShipmentsRequestIDTO requestIDTO);

    @RequestMapping(value = "/statistical/workshopNonLocalShipments/print",method = RequestMethod.POST)
    String print(@RequestBody ProductShipmentsRequestIDTO requestIDTO);

    @RequestMapping(value = "/statistical/workshopNonLocalShipments/printAll",method = RequestMethod.POST)
    String printAll(@RequestBody List<ProductShipmentsRequestIDTO> requestIDTO);

    @RequestMapping(value = "/statistical/workshopNonLocalShipments/queryListForApp",method = RequestMethod.POST)
    List<NonlocalShipmentsODTO> queryListForApp(@RequestBody NonlocalShipmentsIDTO vo);
}

package com.pinshang.qingyun.orderreport.service;

import com.github.pagehelper.PageInfo;
import com.pinshang.qingyun.base.configure.FeignClientConfiguration;
import com.pinshang.qingyun.base.constant.ApplicationNameConstant;
import com.pinshang.qingyun.orderreport.dto.*;
import com.pinshang.qingyun.orderreport.hystrix.StatisticsDeliveryOrderClientHystrix;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2019/3/8 14:24
 */
@FeignClient(value = ApplicationNameConstant.QINGYUN_ORDER_REPORT_SERVICE, fallbackFactory = StatisticsDeliveryOrderClientHystrix.class, configuration = FeignClientConfiguration.class)
public interface StatisticsDeliveryOrderClient {
    /**
     *
     * 送货单列表数据
     * @param deliveryOrderIDTO
     * @return
     */
    @RequestMapping(value = "/statistical/deliveryOrder/queryList", method = RequestMethod.POST)
    PageInfo<DeliveryOrderODTO> queryList(@RequestBody DeliveryOrderIDTO deliveryOrderIDTO);

    /**
     * 送货单详情
     * @param orderId
     * @return
     */
    @RequestMapping(value = "/statistical/deliveryOrder/orderDetail/{id}", method = RequestMethod.GET)
    DeliveryOrderDetailODTO queryDetail(@PathVariable("id")Long orderId);

    /**
     * 送货单打印
     * @param idto
     * @return
     */
    @RequestMapping(value = "/statistical/deliveryOrder/print", method = RequestMethod.POST)
    List<DeliveryOrderPrintODTO> print(@RequestBody DeliveryOrderPrintIDTO idto);

}

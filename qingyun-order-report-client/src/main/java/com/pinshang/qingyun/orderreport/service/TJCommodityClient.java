package com.pinshang.qingyun.orderreport.service;

import com.github.pagehelper.PageInfo;
import com.pinshang.qingyun.base.configure.FeignClientConfiguration;
import com.pinshang.qingyun.base.constant.ApplicationNameConstant;
import com.pinshang.qingyun.orderreport.dto.CommodityODTO;
import com.pinshang.qingyun.orderreport.dto.CommoditySaleStatisticsIDTO;
import com.pinshang.qingyun.orderreport.dto.CommoditySaleStatisticsODTO;
import com.pinshang.qingyun.orderreport.hystrix.TJCommodityClientHystrix;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import java.util.List;
import java.util.Map;

/**
 * 商品
 */
@FeignClient(value = ApplicationNameConstant.QINGYUN_ORDER_REPORT_SERVICE, fallbackFactory = TJCommodityClientHystrix.class, configuration = FeignClientConfiguration.class)
public interface TJCommodityClient {

    /**
     * 查询商品的大类、中类、小类名称
     * @param commodityCodes
     * @return
     */
    @RequestMapping(value = "/statistical/tjCommodity/queryCommodityCateName", method = RequestMethod.POST)
    Map<String, String> queryCommodityCateName(@RequestBody List<String> commodityCodes);

    /**
     * 查询商品基础信息
     * @param commodityCodes
     * @return
     */
    @RequestMapping(value = "/statistical/tjCommodity/queryCommodityMap", method = RequestMethod.POST)
    Map<String, CommodityODTO> queryCommodityMap(@RequestBody List<String> commodityCodes);
}

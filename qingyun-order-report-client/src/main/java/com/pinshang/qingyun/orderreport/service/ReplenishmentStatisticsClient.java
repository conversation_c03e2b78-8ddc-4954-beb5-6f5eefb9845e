package com.pinshang.qingyun.orderreport.service;

import com.github.pagehelper.PageInfo;
import com.pinshang.qingyun.base.configure.FeignClientConfiguration;
import com.pinshang.qingyun.base.constant.ApplicationNameConstant;
import com.pinshang.qingyun.orderreport.dto.ReplenishmentStatisticsIDTO;
import com.pinshang.qingyun.orderreport.dto.ReplenishmentStatisticsODTO;
import com.pinshang.qingyun.orderreport.hystrix.ReplenishmentStatisticsClientHystrix;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

/**
 * 补货数据汇总
 */
@FeignClient(value = ApplicationNameConstant.QINGYUN_ORDER_REPORT_SERVICE, fallbackFactory = ReplenishmentStatisticsClientHystrix.class, configuration = FeignClientConfiguration.class)
public interface ReplenishmentStatisticsClient {

    /**
     * 补货数据汇总查询列表
     * @param idto
     * @return
     */
    @RequestMapping(value = "/statistical/replenishmentStatistics/list", method = RequestMethod.POST)
    PageInfo<ReplenishmentStatisticsODTO> queryList(@RequestBody ReplenishmentStatisticsIDTO idto);

    /**
     * 补货数据汇总打印
     * @param idto
     * @return
     */
    @RequestMapping(value = "/statistical/replenishmentStatistics/print", method = RequestMethod.POST)
    String print(@RequestBody ReplenishmentStatisticsIDTO idto);

}

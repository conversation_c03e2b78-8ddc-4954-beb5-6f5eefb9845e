package com.pinshang.qingyun.orderreport.service;

import com.pinshang.qingyun.base.configure.FeignClientConfiguration;
import com.pinshang.qingyun.base.constant.ApplicationNameConstant;
import com.pinshang.qingyun.orderreport.dto.FreshProductShipmentsIDTO;
import com.pinshang.qingyun.orderreport.dto.FreshProductShipmentsODTO;
import com.pinshang.qingyun.orderreport.dto.FreshProductShipmentsTempODTO;
import com.pinshang.qingyun.orderreport.dto.StoreODTO;
import com.pinshang.qingyun.orderreport.hystrix.FreshStoreProductShipmentsClientHystrix;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2019/3/11 15:14
 */
@FeignClient(value = ApplicationNameConstant.QINGYUN_ORDER_REPORT_SERVICE, fallbackFactory = FreshStoreProductShipmentsClientHystrix.class, configuration = FeignClientConfiguration.class)
public interface FreshStoreProductShipmentsClient {
    @RequestMapping(value = "/statistical/fresh/productShipments/queryList", method = RequestMethod.POST)
    FreshProductShipmentsODTO queryList(@RequestBody FreshProductShipmentsIDTO reqVo);

    @RequestMapping(value = "/statistical/fresh/productShipments/queryListV2", method = RequestMethod.POST)
    List<FreshProductShipmentsTempODTO> queryListV2(@RequestBody FreshProductShipmentsIDTO reqVo);

    @RequestMapping(value = "/statistical/fresh/productShipments/queryFreshStoreList", method = RequestMethod.POST)
    List<StoreODTO> queryFreshStoreList(@RequestBody List<Long> storeIdList);

}

package com.pinshang.qingyun.orderreport.hystrix;

import com.pinshang.qingyun.orderreport.dto.ProductShipmentsODTO;
import com.pinshang.qingyun.orderreport.dto.ProductShipmentsRequestIDTO;
import com.pinshang.qingyun.orderreport.dto.ProductShipmentsTempODTO;
import com.pinshang.qingyun.orderreport.service.ProductShipmentsClient;
import feign.hystrix.FallbackFactory;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2019/3/5 9:50.
 */
@Component
public class ProductShipmentsClientHystrix implements FallbackFactory<ProductShipmentsClient> {
    @Override
    public ProductShipmentsClient create(Throwable cause) {
        return new ProductShipmentsClient(){

            @Override
            public ProductShipmentsODTO doSearch(ProductShipmentsRequestIDTO requestVo) {
                return null;
            }

            @Override
            public String print(ProductShipmentsRequestIDTO vo) {
                return null;
            }

            @Override
            public List<ProductShipmentsTempODTO> doSearchV2(ProductShipmentsRequestIDTO requestVo) {
                return null;
            }
        };
    }
}

package com.pinshang.qingyun.orderreport.hystrix;

import com.github.pagehelper.PageInfo;
import com.pinshang.qingyun.orderreport.dto.LateOrderIDTO;
import com.pinshang.qingyun.orderreport.dto.LateOrderODTO;
import com.pinshang.qingyun.orderreport.dto.OrderDetailIDTO;
import com.pinshang.qingyun.orderreport.dto.OrderDetailODTO;
import com.pinshang.qingyun.orderreport.service.OrderDetailClient;
import feign.hystrix.FallbackFactory;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @Date 2019/3/11 11:26
 */
@Component
public class OrderDetailClientHystrix implements FallbackFactory<OrderDetailClient> {
    @Override
    public OrderDetailClient create(Throwable throwable) {
        return new OrderDetailClient() {
            @Override
            public PageInfo<LateOrderODTO> queryLateOrder(LateOrderIDTO lateOrderReqVo) {
                return null;
            }

            @Override
            public PageInfo<OrderDetailODTO> queryList(OrderDetailIDTO orderDetailIDTO) {
                return null;
            }
        };
    }
}

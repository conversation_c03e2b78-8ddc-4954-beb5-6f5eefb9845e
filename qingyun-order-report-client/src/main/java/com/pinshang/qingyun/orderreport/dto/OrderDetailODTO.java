package com.pinshang.qingyun.orderreport.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @Date 2019/3/11 10:34
 */
@Data
@ApiModel
public class OrderDetailODTO {
    @ApiModelProperty("订单日期")
    private Date orderTime;
    @ApiModelProperty("订单编码")
    private String orderCode;
    @ApiModelProperty("客户编码")
    private String storeCode;
    @ApiModelProperty("客户名称")
    private String storeName;
    @ApiModelProperty("商品编码")
    private String commodityCode;
    @ApiModelProperty("商品名称")
    private String commodityName;
    @ApiModelProperty("品类")
    private String cateName;
    @ApiModelProperty("规格")
    private String commoditySpec;
    @ApiModelProperty("单价")
    private BigDecimal commodityPrice;
    @ApiModelProperty("订货数量")
    private BigDecimal orderGoodsNum;
    @ApiModelProperty("商品金额")
    private BigDecimal commodityAmount;
    @ApiModelProperty("商品备注")
    private String commodityRemark;
    @ApiModelProperty("操作人")
    private String operator;
    @ApiModelProperty("订单创建时间")
    private Date orderCreateTime;
    @ApiModelProperty("订单修改时间")
    private Date orderUpdateTime;
    @ApiModelProperty("公司id")
    private Long companyId;
    @ApiModelProperty("公司名称")
    private String companyName;
}

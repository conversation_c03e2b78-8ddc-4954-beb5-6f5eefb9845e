package com.pinshang.qingyun.orderreport.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @Date 2019/3/8 14:05
 */
@Data
@ApiModel
public class DeliveryOrderODTO {
    /**
     * 订单id
     */
    @ApiModelProperty("订单id")
    private String id;
    /**
     * 送货日期
     */
    @ApiModelProperty("送货日期")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date deliveryDate;
    /**
     * 客户编号
     */
    @ApiModelProperty("客户编号")
    private String storeCode;
    /**
     * 客户名称
     */
    @ApiModelProperty("客户名称")
    private String storeName;
    /**
     * 送货地址
     */
    @ApiModelProperty(value = "送货地址 ")
    private String deliveryAddress;
    /**
     * 订单金额
     */
    @ApiModelProperty("订单金额")
    private BigDecimal finalAmount;
    /**
     * 送货员
     */
    @ApiModelProperty("送货员")
    private String deliveryManName;
    /**
     * 客户类型
     */
    @ApiModelProperty("客户类型")
    private String storeTypeName;
    /**
     * 督导
     */
    @ApiModelProperty("督导")
    private String supervisorName;
    /**
     * 结账客户
     */
    @ApiModelProperty("结账客户")
    private String settlementCustomer;
    /**
     * 操作员
     */
    @ApiModelProperty("操作员")
    private String operatorName;
    /**
     * 订单编码
     */
    @ApiModelProperty("订单编码")
    private String orderCode;
    /**
     * 班组长(导出字段)
     */
    @ApiModelProperty(value = "班组长", hidden = true)
    private String teamLeaderName;

    @ApiModelProperty("送货时间要求")
    private String receiveTime;
}

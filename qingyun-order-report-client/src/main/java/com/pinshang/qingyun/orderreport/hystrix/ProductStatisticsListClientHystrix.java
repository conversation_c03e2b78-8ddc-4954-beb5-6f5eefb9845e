package com.pinshang.qingyun.orderreport.hystrix;

import com.github.pagehelper.PageInfo;
import com.pinshang.qingyun.orderreport.dto.ProductStatisticsListIDTO;
import com.pinshang.qingyun.orderreport.dto.ProductStatisticsListODTO;
import com.pinshang.qingyun.orderreport.dto.ProductStatisticsListSumODTO;
import com.pinshang.qingyun.orderreport.service.ProductStatisticsListClient;
import feign.hystrix.FallbackFactory;
import org.springframework.stereotype.Component;

/**
 * 产品统计清单
 */
@Component
public class ProductStatisticsListClientHystrix implements FallbackFactory<ProductStatisticsListClient> {
    @Override
    public ProductStatisticsListClient create(Throwable cause) {
        return new ProductStatisticsListClient() {


            @Override
            public PageInfo<ProductStatisticsListODTO> queryList(ProductStatisticsListIDTO idto) {
                return null;
            }

            @Override
            public ProductStatisticsListSumODTO querySum(ProductStatisticsListIDTO idto) {
                return null;
            }

            @Override
            public String print(ProductStatisticsListIDTO idto) {
                return null;
            }
        };
    }
}

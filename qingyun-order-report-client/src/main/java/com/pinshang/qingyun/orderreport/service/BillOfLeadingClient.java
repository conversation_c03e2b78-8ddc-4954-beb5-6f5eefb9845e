package com.pinshang.qingyun.orderreport.service;

import com.pinshang.qingyun.base.configure.FeignClientConfiguration;
import com.pinshang.qingyun.base.constant.ApplicationNameConstant;
import com.pinshang.qingyun.orderreport.dto.BillOfLadingPreviewODTO;
import com.pinshang.qingyun.orderreport.dto.DeliveryListPreviewIDTO;
import com.pinshang.qingyun.orderreport.hystrix.BillOfLeadingClientHystrix;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2019/1/28 11:39.
 */
@FeignClient(value = ApplicationNameConstant.QINGYUN_ORDER_REPORT_SERVICE, fallbackFactory = BillOfLeadingClientHystrix.class, configuration = FeignClientConfiguration.class)
public interface BillOfLeadingClient {

    /**
     * 提货单 预览
     * @param lineId 线路编码
     * @param orderDate 订单日期
     * @return 返回提货单预览页面
     */
    @RequestMapping("/statistical/billOfLading/preview")
    BillOfLadingPreviewODTO preview(@RequestParam("lineId") Long lineId, @RequestParam("orderDate") String orderDate);

    @RequestMapping("/statistical/billOfLading/print")
    String print(DeliveryListPreviewIDTO param);

    @RequestMapping("/statistical/billOfLading/printAll/{userId}")
    String printAll(@RequestBody List<DeliveryListPreviewIDTO> params, @PathVariable("userId") Long userId);
}

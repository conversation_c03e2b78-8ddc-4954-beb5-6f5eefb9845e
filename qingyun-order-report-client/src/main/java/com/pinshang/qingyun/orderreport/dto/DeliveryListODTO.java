package com.pinshang.qingyun.orderreport.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2019/1/23 9:55.
 */
@Data
public class DeliveryListODTO {


    @ApiModelProperty(value = "线路ID")
    private Long lineId;

    @ApiModelProperty(value = "线路编码")
    private String lineCode;

    @ApiModelProperty(value = "线路名称")
    private String lineName;

    @ApiModelProperty(value = "送货员ID")
    private String deliveryManId;

    @ApiModelProperty(value = "送货员姓名")
    private String deliveryManName;

    @ApiModelProperty(value = "班组长姓名")
    private String teamLeaderName;

    @ApiModelProperty(value = "打印状态")
    private Integer printStatus;

    @ApiModelProperty(value = "补货数据下拉选项值")
    private Integer orderModeType;
}

package com.pinshang.qingyun.orderreport.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/12/28 15:15
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class LatestCommoditySaleStatisticsIDTO {
    private List<Long> factoryIds;
    //只能为今天或今天以后的日期
    private String orderTime;
    //按商品销售额排序取的条数
    private Integer size;
}
package com.pinshang.qingyun.orderreport.hystrix.sync;

import com.pinshang.qingyun.orderreport.dto.sync.DifferenceInfoODTO;
import com.pinshang.qingyun.orderreport.dto.sync.SyncInfoByCodesIDTO;
import com.pinshang.qingyun.orderreport.service.sync.SyncCommodityInfoToOrderReportClient;

import feign.hystrix.FallbackFactory;

import org.springframework.stereotype.Component;

import java.util.List;

import lombok.extern.slf4j.Slf4j;

/**
 * 同步商品相关信息
 *
 * <AUTHOR>
 *
 * @date 2019年01月21日
 */
@Slf4j
@Component
public class SyncCommodityInfoToOrderReportClientHystrix implements FallbackFactory<SyncCommodityInfoToOrderReportClient> {
    @Override
    public SyncCommodityInfoToOrderReportClient create(Throwable throwable) {
        return new SyncCommodityInfoToOrderReportClient() {

        	@Override
			public Integer syncCommodityInfo(SyncInfoByCodesIDTO idto) {
        		log.error("\n syncCommodityInfo:" + throwable.toString() + "\nidto=" + idto);
				return null;
			}
        	
        	@Override
			public DifferenceInfoODTO selectCommodityDifferenceInfo() {
        		log.error("\n selectCommodityDifferenceInfo:" + throwable.toString());
				return null;
			}

			@Override
			public List<String> selectMissingCommodityCodeList() {
				log.error("\n selectMissingCommodityCodeList:" + throwable.toString());
				return null;
			}

        };
    }

}

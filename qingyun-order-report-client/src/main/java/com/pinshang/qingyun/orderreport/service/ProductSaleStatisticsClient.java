package com.pinshang.qingyun.orderreport.service;

import com.pinshang.qingyun.base.configure.FeignClientConfiguration;
import com.pinshang.qingyun.base.constant.ApplicationNameConstant;
import com.pinshang.qingyun.orderreport.dto.ProductSaleStatisticsIDTO;
import com.pinshang.qingyun.orderreport.dto.ProductSaleStatisticsODTO;
import com.pinshang.qingyun.orderreport.dto.ProductSaleStatisticsPurchaseQueryDto;
import com.pinshang.qingyun.orderreport.dto.ProductSaleStatisticsSumODTO;
import com.pinshang.qingyun.orderreport.dto.ProductSaleStatisticsTempODTO;
import com.pinshang.qingyun.orderreport.hystrix.ProductSaleStatisticsClientHystrix;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import java.util.List;

/**
 * 产品销售汇总
 */
@FeignClient(value = ApplicationNameConstant.QINGYUN_ORDER_REPORT_SERVICE, fallbackFactory = ProductSaleStatisticsClientHystrix.class, configuration = FeignClientConfiguration.class)
public interface ProductSaleStatisticsClient {

    /**
     * 产品销售汇总
     * @param idto
     * @return
     */
    @RequestMapping(value = "/statistical/productSaleStatistics/list", method = RequestMethod.POST)
    ProductSaleStatisticsODTO queryList(@RequestBody ProductSaleStatisticsIDTO idto);

    /**
     * 产品销售汇总
     * @param idto
     * @return
     */
    @RequestMapping(value = "/statistical/productSaleStatistics/listV2", method = RequestMethod.POST)
    List<ProductSaleStatisticsTempODTO> queryListV2(@RequestBody ProductSaleStatisticsIDTO idto);

    /**
     * 产品销售汇总
     * @param idto
     * @return
     */
    @RequestMapping(value = "/statistical/productSaleStatistics/querySum", method = RequestMethod.POST)
    ProductSaleStatisticsSumODTO querySum(@RequestBody ProductSaleStatisticsIDTO idto);

    /**
     * 打印
     * @param idto
     * @return
     */
    @RequestMapping(value = "/statistical/productSaleStatistics/print", method = RequestMethod.POST)
    String print(@RequestBody ProductSaleStatisticsIDTO idto);


    /**
     * 采购预估-产品销售汇总查询
     */
    @PostMapping("/statistical/productSaleStatistics/queryListForPurchase")
    List<ProductSaleStatisticsTempODTO> queryListForPurchase(@RequestBody ProductSaleStatisticsPurchaseQueryDto dto);

    /**
     * 查询 送货日期 ≥ T，订货日期为T-2 和 T-1 的订单数据
     * @param queryVo
     * @return
     */
    @PostMapping("/statistical/productSaleStatistics/queryListForPurchaseByCreateTime")
    List<ProductSaleStatisticsTempODTO> queryListForPurchaseByCreateTime(@RequestBody ProductSaleStatisticsPurchaseQueryDto queryVo);

}
package com.pinshang.qingyun.orderreport.dto;

import io.swagger.annotations.ApiModelProperty;

import java.math.BigDecimal;

import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @author: ch<PERSON><PERSON><PERSON>
 * @time: 2021/9/16 17:19
 */
@Data
@NoArgsConstructor
public class DeliveryNoteIDTO {

    private String orderTime;

    /**
     * 线路id
     */
    private Long storeLineId;

    private String storeName;

    /**
     * 送货地址
     */
    private String deliveryAddress;
    
    @ApiModelProperty(position = 21, value = "百度经度")
    private BigDecimal baiduLongitude;
	@ApiModelProperty(position = 22, value = "百度维度")
    private BigDecimal baiduLatitude;

	@ApiModelProperty(position = 50, required = true, value = "职员ID", hidden = true)
    private Long userId;

    public static DeliveryNoteIDTO copy(String orderTime,Long storeLineId,String storeName,String deliveryAddress,Long userId){
        DeliveryNoteIDTO deliveryNoteIDTO = new DeliveryNoteIDTO();
        deliveryNoteIDTO.setDeliveryAddress(deliveryAddress);
        deliveryNoteIDTO.setOrderTime(orderTime);
        deliveryNoteIDTO.setStoreLineId(storeLineId);
        deliveryNoteIDTO.setStoreName(storeName);
        deliveryNoteIDTO.setUserId(userId);
        return deliveryNoteIDTO;
    }
}

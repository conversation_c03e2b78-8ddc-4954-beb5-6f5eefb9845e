package com.pinshang.qingyun.orderreport.dto;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.pinshang.qingyun.base.configure.codec.DecimalSerializerKeep2;
import com.pinshang.qingyun.orderreport.dto.Pagination;
import io.swagger.annotations.ApiModelProperty;
import jdk.nashorn.internal.ir.annotations.Ignore;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * 清美人客户订货统计查询
 */
@Data
@NoArgsConstructor
public class StoreOrderStatisticsIDTO extends Pagination {

    @ApiModelProperty(value = "督导ID",hidden = true)
    @Ignore
    private Long supervisorId;

    @ApiModelProperty("送货日期:yyyy-MM-dd")
    private String orderTime;

}

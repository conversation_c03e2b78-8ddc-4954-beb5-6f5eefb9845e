package com.pinshang.qingyun.orderreport.service;

import com.pinshang.qingyun.base.configure.FeignClientConfiguration;
import com.pinshang.qingyun.base.constant.ApplicationNameConstant;
import com.pinshang.qingyun.orderreport.dto.DeliveryNoteIDTO;
import com.pinshang.qingyun.orderreport.dto.DeliveryNoteODTO;
import com.pinshang.qingyun.orderreport.hystrix.DeliveryNoteClientHystrix;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

/**
 * @author: chenqiang
 * @time: 2021/9/16 17:16
 */
@FeignClient(value = ApplicationNameConstant.QINGYUN_ORDER_REPORT_SERVICE, fallbackFactory = DeliveryNoteClientHystrix.class, configuration = FeignClientConfiguration.class)
public interface DeliveryNoteClient {

    /**
     * 查看当前送货员的送货单
     * @param deliveryNoteIDTO
     * @return
     */
    @RequestMapping(value = "/deliveryNote/selectDeliveryNote", method = RequestMethod.POST)
    DeliveryNoteODTO selectDeliveryNote(@RequestBody DeliveryNoteIDTO deliveryNoteIDTO);
}

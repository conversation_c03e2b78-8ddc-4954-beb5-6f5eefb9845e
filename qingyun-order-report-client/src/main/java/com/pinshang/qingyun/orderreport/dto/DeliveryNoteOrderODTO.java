package com.pinshang.qingyun.orderreport.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @author: chen<PERSON>ang
 * @time: 2021/9/16 17:21
 */
@Data
@NoArgsConstructor
public class DeliveryNoteOrderODTO {

    private String storeName;

    private String storeCode;

    /**
     * 送货地址
     */
    private String deliveryAddress;

    /**
     * 联系人
     */
    private String storeLinkman;


    private String linkmanMobile;
    /**
     * 线路组名称
     */
    private String lineName;

    /**
     * 发货时间
     */
    private String deliveryTime;


    /**
     * 是否有图片
     */
    private Boolean isTruePic;

    /**
     * 距离
     */
    @ApiModelProperty(value = "距离")
    private String distanceStr;
}

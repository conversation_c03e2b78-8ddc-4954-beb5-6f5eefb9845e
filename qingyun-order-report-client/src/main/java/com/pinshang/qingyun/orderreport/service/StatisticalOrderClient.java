package com.pinshang.qingyun.orderreport.service;

import com.pinshang.qingyun.base.configure.FeignClientConfiguration;
import com.pinshang.qingyun.base.constant.ApplicationNameConstant;
import com.pinshang.qingyun.orderreport.hystrix.StatisticalOrderClientHystrix;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

/**
 * <AUTHOR>
 * @date 2019/3/6 17:29.
 */
@FeignClient(value = ApplicationNameConstant.QINGYUN_ORDER_REPORT_SERVICE, fallbackFactory = StatisticalOrderClientHystrix.class, configuration = FeignClientConfiguration.class)
public interface StatisticalOrderClient {
    @RequestMapping(value = "/statistical/order/discardTestUserOrder", method = RequestMethod.GET)
    boolean discardTestUserOrder();
}

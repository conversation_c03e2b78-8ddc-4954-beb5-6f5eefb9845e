package com.pinshang.qingyun.orderreport.hystrix;

import com.pinshang.qingyun.orderreport.dto.BillOfLadingPreviewODTO;
import com.pinshang.qingyun.orderreport.dto.DeliveryListPreviewIDTO;
import com.pinshang.qingyun.orderreport.service.BillOfLeadingClient;
import feign.hystrix.FallbackFactory;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2019/1/28 11:39.
 */
@Component
public class BillOfLeadingClientHystrix implements FallbackFactory<BillOfLeadingClient> {
    @Override
    public BillOfLeadingClient create(Throwable cause) {
        return new BillOfLeadingClient(){

            @Override
            public String print(DeliveryListPreviewIDTO param) {
                return null;
            }

            @Override
            public String printAll(List<DeliveryListPreviewIDTO> params, Long userId) {
                return null;
            }

            @Override
            public BillOfLadingPreviewODTO preview(Long lineId, String orderDate) {
                return null;
            }
        };
    }
}

package com.pinshang.qingyun.orderreport.hystrix;

import com.github.pagehelper.PageInfo;
import com.pinshang.qingyun.orderreport.dto.ReplenishmentStatisticsIDTO;
import com.pinshang.qingyun.orderreport.dto.ReplenishmentStatisticsODTO;
import com.pinshang.qingyun.orderreport.service.ReplenishmentStatisticsClient;
import feign.hystrix.FallbackFactory;
import org.springframework.stereotype.Component;

/**
 * 补货数据汇总
 */
@Component
public class ReplenishmentStatisticsClientHystrix implements FallbackFactory<ReplenishmentStatisticsClient> {
    @Override
    public ReplenishmentStatisticsClient create(Throwable cause) {
        return new ReplenishmentStatisticsClient() {

            @Override
            public PageInfo<ReplenishmentStatisticsODTO> queryList(ReplenishmentStatisticsIDTO idto) {
                return null;
            }

            @Override
            public String print(ReplenishmentStatisticsIDTO idto) {
                return null;
            }
        };
    }
}

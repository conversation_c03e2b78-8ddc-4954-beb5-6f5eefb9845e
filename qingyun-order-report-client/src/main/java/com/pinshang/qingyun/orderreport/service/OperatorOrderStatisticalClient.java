package com.pinshang.qingyun.orderreport.service;

import com.pinshang.qingyun.base.configure.FeignClientConfiguration;
import com.pinshang.qingyun.base.constant.ApplicationNameConstant;
import com.pinshang.qingyun.orderreport.dto.OperatorOrderMonitorIDTO;
import com.pinshang.qingyun.orderreport.dto.OperatorOrderReqIDTO;
import com.pinshang.qingyun.orderreport.dto.OperatorOrderRespODTO;
import com.pinshang.qingyun.orderreport.hystrix.OperatorOrderStatisticalClientHystrix;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2019/3/13 14:54.
 */
@FeignClient(value = ApplicationNameConstant.QINGYUN_ORDER_REPORT_SERVICE, fallbackFactory = OperatorOrderStatisticalClientHystrix.class, configuration = FeignClientConfiguration.class)
public interface OperatorOrderStatisticalClient {


    @RequestMapping(value = "/statistical/operatorOrder/list", method = RequestMethod.POST)
    List<OperatorOrderRespODTO> queryList(@RequestBody OperatorOrderReqIDTO reqVo);

    @RequestMapping(value = "/statistical/operatorOrder/print", method = RequestMethod.POST)
    String print(@RequestBody OperatorOrderReqIDTO reqVo);

    @RequestMapping(value = "/statistical/operatorOrder/resetOperatorOrderStatistics", method = RequestMethod.POST)
    Integer resetOperatorOrderStatistics(@RequestBody OperatorOrderMonitorIDTO reqVo);
}

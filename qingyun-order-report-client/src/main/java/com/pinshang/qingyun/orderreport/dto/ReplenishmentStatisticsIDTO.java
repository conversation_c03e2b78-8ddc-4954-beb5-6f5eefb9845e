package com.pinshang.qingyun.orderreport.dto;

import com.pinshang.qingyun.orderreport.dto.Pagination;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;


/**
 * 补货数据汇总查询参数
 */
@Data
public class ReplenishmentStatisticsIDTO extends Pagination {

    @ApiModelProperty(value = "送货员ID")
    private Long deliveryManId;

    @ApiModelProperty(value = "送货员")
    private String deliveryManName;

    @ApiModelProperty(value = "发货仓库ID")
    private Long deliveryWarehouseId;

    @ApiModelProperty(value = "发货仓库")
    private String deliveryWarehouseName;

    @ApiModelProperty(value = "送货日期开始")
    private String startDate;

    @ApiModelProperty(value = "送货日期结束")
    private String endDate;

    @ApiModelProperty(value = "操作人")
    private Long userId;

    @ApiModelProperty(value = "生成组id")
    private Long commodityWorkshopId;
    @ApiModelProperty(value = "车间id")
    private Long commodityFlowshopId;
    @ApiModelProperty(value = "工厂id")
    private Long commodityFactoryId;

}

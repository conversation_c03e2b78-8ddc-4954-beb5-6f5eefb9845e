package com.pinshang.qingyun.orderreport.service;

import com.pinshang.qingyun.base.configure.FeignClientConfiguration;
import com.pinshang.qingyun.base.constant.ApplicationNameConstant;
import com.pinshang.qingyun.orderreport.dto.XdaStoreOrderAmountIDTO;
import com.pinshang.qingyun.orderreport.dto.XdaStoreOrderAmountODTO;
import com.pinshang.qingyun.orderreport.hystrix.XdaStoreOrderAmountClientHystrix;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import java.util.List;

/**
 * @author: chenqiang
 * @time: 2021/9/28 10:34
 */
@FeignClient(value = ApplicationNameConstant.QINGYUN_ORDER_REPORT_SERVICE, fallbackFactory = XdaStoreOrderAmountClientHystrix.class, configuration = FeignClientConfiguration.class)
public interface XdaStoreOrderAmountClient {

    @RequestMapping(value = "/statistical/xdaStoreOrderAmount/exportXdaStoreOrderAmount",method = RequestMethod.POST)
    public List<XdaStoreOrderAmountODTO> exportXdaStoreOrderAmount(@RequestBody XdaStoreOrderAmountIDTO idto);
}

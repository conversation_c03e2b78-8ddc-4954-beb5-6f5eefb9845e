package com.pinshang.qingyun.orderreport.dto;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.github.pagehelper.PageInfo;
import com.pinshang.qingyun.base.configure.codec.DecimalSerializerKeep2;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.List;

/**
 * 清美人客户订货统计查询
 */
@Data
@NoArgsConstructor
public class StoreOrderStatisticsODTO {

    @ApiModelProperty("订货客户总数")
    private Integer totalStoreQuantity;

    @ApiModelProperty("订货总金额")
    @JsonSerialize(using = DecimalSerializerKeep2.class)
    private BigDecimal totalOrderAmount;

    @ApiModelProperty("客户订货统计列表")
    private PageInfo<StoreOrderListODTO> pageList;

}

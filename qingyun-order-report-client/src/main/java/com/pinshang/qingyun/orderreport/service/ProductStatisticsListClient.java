package com.pinshang.qingyun.orderreport.service;

import com.github.pagehelper.PageInfo;
import com.pinshang.qingyun.base.configure.FeignClientConfiguration;
import com.pinshang.qingyun.base.constant.ApplicationNameConstant;
import com.pinshang.qingyun.orderreport.dto.ProductStatisticsListIDTO;
import com.pinshang.qingyun.orderreport.dto.ProductStatisticsListODTO;
import com.pinshang.qingyun.orderreport.dto.ProductStatisticsListSumODTO;
import com.pinshang.qingyun.orderreport.hystrix.ProductStatisticsListClientHystrix;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

/**
 * 产品统计清单
 */
@FeignClient(value = ApplicationNameConstant.QINGYUN_ORDER_REPORT_SERVICE, fallbackFactory = ProductStatisticsListClientHystrix.class, configuration = FeignClientConfiguration.class)
public interface ProductStatisticsListClient {

    /**
     * 产品统计清单列表
     * @param idto
     * @return
     */
    @RequestMapping(value = "/statistical/productStatistics/list", method = RequestMethod.POST)
    PageInfo<ProductStatisticsListODTO> queryList(@RequestBody ProductStatisticsListIDTO idto);

    /**
     * 产品统计清单合计
     * @param idto
     * @return
     */
    @RequestMapping(value = "/statistical/productStatistics/querySum", method = RequestMethod.POST)
    ProductStatisticsListSumODTO querySum(@RequestBody ProductStatisticsListIDTO idto);

    @RequestMapping(value = "/statistical/productStatistics/print", method = RequestMethod.POST)
    String print(@RequestBody ProductStatisticsListIDTO idto);

}

package com.pinshang.qingyun.orderreport.dto;

import com.pinshang.qingyun.orderreport.dto.Pagination;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @Date 2019/3/11 10:13
 */
@Data
@ApiModel
public class OrderDetailIDTO extends Pagination {
    @ApiModelProperty("结账客户Id")
    private Long settlementId;
    @ApiModelProperty("客户编码")
    private String storeCode;
    @ApiModelProperty("起始时间")
    private String startOrderDate;
    @ApiModelProperty("截止日期")
    private String endOrderDate;
    @ApiModelProperty("商品品类")
    private Long categoryId;
    @ApiModelProperty("客户类型")
    private Long storeTypeId;
    @ApiModelProperty("商品id")
    private Long commodityId;
    @ApiModelProperty("操作人id")
    private Long operatorId;
    /***
     * 公司id
     */
    @ApiModelProperty("公司id")
    private Long companyId;

    /***
     * 订单编码
     */
    @ApiModelProperty("订单编码")
    private String orderCode;
}

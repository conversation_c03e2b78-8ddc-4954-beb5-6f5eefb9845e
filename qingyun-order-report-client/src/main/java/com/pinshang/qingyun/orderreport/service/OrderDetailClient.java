package com.pinshang.qingyun.orderreport.service;

import com.github.pagehelper.PageInfo;
import com.pinshang.qingyun.base.configure.FeignClientConfiguration;
import com.pinshang.qingyun.base.constant.ApplicationNameConstant;
import com.pinshang.qingyun.orderreport.dto.LateOrderIDTO;
import com.pinshang.qingyun.orderreport.dto.LateOrderODTO;
import com.pinshang.qingyun.orderreport.dto.OrderDetailIDTO;
import com.pinshang.qingyun.orderreport.dto.OrderDetailODTO;
import com.pinshang.qingyun.orderreport.hystrix.OrderDetailClientHystrix;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

/**
 * <AUTHOR>
 * @Date 2019/3/11 11:27
 */
@FeignClient(value = ApplicationNameConstant.QINGYUN_ORDER_REPORT_SERVICE, fallbackFactory = OrderDetailClientHystrix.class, configuration = FeignClientConfiguration.class)
public interface OrderDetailClient {
    /**
     * 查询订单明细列表数据
     * @param orderDetailIDTO
     * @return
     */
    @Deprecated
    @RequestMapping(value = "/statistical/order/queryList", method = RequestMethod.POST)
    PageInfo<OrderDetailODTO> queryList(@RequestBody OrderDetailIDTO orderDetailIDTO);


    /**
     * 晚订货查询
     * @param lateOrderReqVo
     */
    @RequestMapping(value = "/statistical/order/queryLateOrder", method = RequestMethod.POST)
    PageInfo<LateOrderODTO> queryLateOrder(@RequestBody LateOrderIDTO lateOrderReqVo);
}

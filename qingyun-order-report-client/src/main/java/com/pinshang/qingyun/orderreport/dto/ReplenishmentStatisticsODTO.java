package com.pinshang.qingyun.orderreport.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 补货数据汇总返回对象
 */
@Data
public class ReplenishmentStatisticsODTO {

    @ApiModelProperty(value = "商品ID")
    private Long commodityId;

    @ApiModelProperty(value = "商品名称")
    private String commodityName;

    @ApiModelProperty(value = "商品规格")
    private String commoditySpec;

    @ApiModelProperty(value = "商品合计")
    private BigDecimal totalCommodityNum;

    @ApiModelProperty(value = "商品编码")
    private String commodityCode;

    private Long commodityFactoryId;

    @ApiModelProperty(value = "工厂")
    private String commodityFactoryName;

    private Long commodityWorkshopId;

    @ApiModelProperty(value = "生产组")
    private String commodityWorkshopName;

    private Long commodityFlowshopId;

    @ApiModelProperty(value = "车间")
    private String commodityFlowshopName;

}

package com.pinshang.qingyun.orderreport.service;

import com.pinshang.qingyun.base.configure.FeignClientConfiguration;
import com.pinshang.qingyun.base.constant.ApplicationNameConstant;
import com.pinshang.qingyun.orderreport.dto.*;
import com.pinshang.qingyun.orderreport.hystrix.WorkshopShipmentsClientHystrix;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2019/2/28 13:47.
 */
@FeignClient(value = ApplicationNameConstant.QINGYUN_ORDER_REPORT_SERVICE, fallbackFactory = WorkshopShipmentsClientHystrix.class, configuration = FeignClientConfiguration.class)
public interface WorkshopShipmentsClient {

    @RequestMapping(value = "/statistical/workshopShipments/doSearch",method = RequestMethod.POST)
    List<WorkshopODTO> doSearch(@RequestBody ProductShipmentsRequestIDTO requestIDTO);

    @RequestMapping(value = "/statistical/workshopShipments/preview",method = RequestMethod.POST)
    WorkshopShipmentsPreviewODTO preview(@RequestBody ProductShipmentsRequestIDTO requestIDTO);

    @RequestMapping(value = "/statistical/workshopShipments/print",method = RequestMethod.POST)
    String print(@RequestBody ProductShipmentsRequestIDTO requestIDTO);

    @RequestMapping(value = "/statistical/workshopShipments/printAll",method = RequestMethod.POST)
    String printAll(@RequestBody List<ProductShipmentsRequestIDTO> requestIDTO);

    /**
     * 清美人APP查询 生产组发货单列表
     * @param vo
     * @return
     */
    @RequestMapping(value = "/statistical/workshopShipments/list",method = RequestMethod.POST)
    List<ShipmentsODTO> list(@RequestBody ShipmentsIDTO vo);
}

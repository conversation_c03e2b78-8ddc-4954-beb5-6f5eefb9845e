package com.pinshang.qingyun.orderreport.service;

import com.pinshang.qingyun.base.configure.FeignClientConfiguration;
import com.pinshang.qingyun.base.constant.ApplicationNameConstant;
import com.pinshang.qingyun.orderreport.dto.ProductShipmentsODTO;
import com.pinshang.qingyun.orderreport.dto.ProductShipmentsRequestIDTO;
import com.pinshang.qingyun.orderreport.dto.ProductShipmentsTempODTO;
import com.pinshang.qingyun.orderreport.hystrix.ProductShipmentsClientHystrix;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2019/3/5 9:48.
 */
@FeignClient(value = ApplicationNameConstant.QINGYUN_ORDER_REPORT_SERVICE, fallbackFactory = ProductShipmentsClientHystrix.class, configuration = FeignClientConfiguration.class)
public interface ProductShipmentsClient {

    /**
     * 查数据库搜索,按条件搜索
     * @param requestVo 请求参数封装
     * @return 返回JSON数据给前台
     */
    @RequestMapping(value = "/statistical/productShipments/doSearch",method = RequestMethod.POST)
    ProductShipmentsODTO doSearch(@RequestBody ProductShipmentsRequestIDTO requestVo);


    /**
     * 打印
     * @param vo 当前搜索条件
     * @return 返回JSON
     */
    @RequestMapping(value = "/statistical/productShipments/print", method = RequestMethod.POST)
    String print(@RequestBody ProductShipmentsRequestIDTO vo);

    @RequestMapping(value = "/statistical/productShipments/doSearchV2",method = RequestMethod.POST)
    List<ProductShipmentsTempODTO> doSearchV2(@RequestBody ProductShipmentsRequestIDTO requestVo);

}

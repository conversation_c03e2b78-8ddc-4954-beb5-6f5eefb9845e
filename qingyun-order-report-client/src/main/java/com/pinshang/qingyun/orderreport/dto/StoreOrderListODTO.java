package com.pinshang.qingyun.orderreport.dto;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.pinshang.qingyun.base.configure.codec.DecimalSerializerKeep2;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * 清美人客户订货统计查询
 */
@Data
@NoArgsConstructor
public class StoreOrderListODTO {

    @ApiModelProperty(value = "客户ID",hidden = true)
    private Long storeId;

    @ApiModelProperty("客户名称")
    private String storeName;

    @ApiModelProperty("客户编码")
    private String storeCode;

    @ApiModelProperty("客户订货金额")
    @JsonSerialize(using = DecimalSerializerKeep2.class)
    private BigDecimal orderAmount;

}

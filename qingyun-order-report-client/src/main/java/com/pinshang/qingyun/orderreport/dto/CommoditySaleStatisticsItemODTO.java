package com.pinshang.qingyun.orderreport.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @description 产品销售汇总数据（详细）
 * @date 2019/09/03
 */
@Data
public class CommoditySaleStatisticsItemODTO {

    @ApiModelProperty(value = "商品ID")
    private Long commodityId;

    @ApiModelProperty(value = "客户类型ID")
    private Long storeTypeId;

    @ApiModelProperty(value = "客户类型ID,String")
    private String storeTypeIdStr;

    @ApiModelProperty(value = "商品数量")
    private BigDecimal totalQuantity;

    @ApiModelProperty(value = "商品金额")
    private BigDecimal totalAmount;

    public void setStoreTypeId(Long storeTypeId) {
        this.storeTypeId = storeTypeId;
        if(storeTypeId!=null){
            this.storeTypeIdStr = String.valueOf(storeTypeId);
        }
    }
}

package com.pinshang.qingyun.orderreport.hystrix.sync;

import java.util.List;

import lombok.extern.slf4j.Slf4j;

import org.springframework.stereotype.Component;

import com.pinshang.qingyun.orderreport.dto.sync.DifferenceInfoODTO;
import com.pinshang.qingyun.orderreport.dto.sync.SyncInfoByCodesIDTO;
import com.pinshang.qingyun.orderreport.service.sync.SyncEmployeeUserInfoToOrderReportClient;

import feign.hystrix.FallbackFactory;

/**
 * 同步职员用户相关信息
 *
 * <AUTHOR>
 *
 * @date 2019年3月5日
 */
@Slf4j
@Component
public class SyncEmployeeUserInfoToOrderReportClientHystrix implements FallbackFactory<SyncEmployeeUserInfoToOrderReportClient> {
    @Override
    public SyncEmployeeUserInfoToOrderReportClient create(Throwable throwable) {
        return new SyncEmployeeUserInfoToOrderReportClient() {

        	@Override
			public Integer syncEmployeeUserInfo(SyncInfoByCodesIDTO idto) {
        		log.error("\n syncEmployeeUserInfo:" + throwable.toString() + "\nidto=" + idto);
				return null;
			}
        	
        	@Override
			public DifferenceInfoODTO selectEmployeeUserDifferenceInfo() {
        		log.error("\n selectEmployeeUserDifferenceInfo:" + throwable.toString());
				return null;
			}

			@Override
			public List<String> selectMissingEmployeeUserCodeList() {
				log.error("\n selectMissingEmployeeUserCodeList:" + throwable.toString());
				return null;
			}

        };
    }

}

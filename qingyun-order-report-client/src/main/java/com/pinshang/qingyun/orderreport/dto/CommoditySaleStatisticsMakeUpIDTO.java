package com.pinshang.qingyun.orderreport.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @description 产品销售汇总查询条件
 * @date 2019/09/03
 */
@Data
public class CommoditySaleStatisticsMakeUpIDTO {
    @ApiModelProperty(value = "商品Id")
    private Long commodityId;

    @ApiModelProperty(value = "补偿某个日期的时间")
    private String orderTime;

    @ApiModelProperty(value = "范围补偿开始时间")
    private String startDate;

    @ApiModelProperty(value = "范围补偿结束时间")
    private String endDate;

    @ApiModelProperty(value = "产品销售汇总(商品) 和 产品销售汇总(月报) 接口共用，类型区分")
    private Integer paramType;

    /**
     * 补偿业务类型：1=产品销售汇总(日报/月报)；2=产品销售汇总--督导明细；3=产品销售汇总--工厂送货汇总；为空时表示补偿所有业务
     */
    private List<Integer> businessType;
}

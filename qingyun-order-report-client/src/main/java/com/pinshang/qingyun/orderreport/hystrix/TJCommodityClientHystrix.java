package com.pinshang.qingyun.orderreport.hystrix;

import com.github.pagehelper.PageInfo;
import com.pinshang.qingyun.orderreport.dto.CommodityODTO;
import com.pinshang.qingyun.orderreport.dto.CommoditySaleStatisticsIDTO;
import com.pinshang.qingyun.orderreport.dto.CommoditySaleStatisticsODTO;
import com.pinshang.qingyun.orderreport.service.TJCommodityClient;
import feign.hystrix.FallbackFactory;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

@Component
public class TJCommodityClientHystrix implements FallbackFactory<TJCommodityClient> {
    @Override
    public TJCommodityClient create(Throwable cause) {
        return new TJCommodityClient() {

            @Override
            public Map<String, String> queryCommodityCateName(List<String> commodityCodeList) {
                return null;
            }

            @Override
            public Map<String, CommodityODTO> queryCommodityMap(List<String> commodityCodes) {
                return null;
            }
        };
    }
}

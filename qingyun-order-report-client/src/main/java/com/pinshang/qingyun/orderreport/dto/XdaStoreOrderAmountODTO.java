package com.pinshang.qingyun.orderreport.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @author: chenqiang
 * @time: 2021/9/28 10:36
 */
@Data
public class XdaStoreOrderAmountODTO {
    @ApiModelProperty(value = "客户编码")
    private String storeCode;

    @ApiModelProperty(value = "客户名称")
    private String storeName;

    @ApiModelProperty(value = "客户类型")
    private String storeType;
    /**
     * 督导
     */
    @ApiModelProperty(value = "督导")
    private String supervisorName;

    /**
     * 主任
     */
    @ApiModelProperty(value = "主任")
    private String officeDirectorName;

    /**
     * 大区经理
     */
    @ApiModelProperty(value = "大区经理")
    private String regionManagerName;

    /**
     * 部门
     */
    @ApiModelProperty(value = "部门")
    private String storeCompanyName;

    /**
     * 订单总金额
     */
    @ApiModelProperty(value = "订单总金额")
    private String orderAmount;

    /**
     * 豆面制品订单金额
     */
    @ApiModelProperty(value = "豆制品订单金额")
    private String orderFilterAmount;
}

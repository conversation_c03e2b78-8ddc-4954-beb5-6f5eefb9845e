package com.pinshang.qingyun.orderreport.service;

import com.pinshang.qingyun.base.configure.FeignClientConfiguration;
import com.pinshang.qingyun.base.constant.ApplicationNameConstant;
import com.pinshang.qingyun.orderreport.dto.TJOrderMonitorIDTO;
import com.pinshang.qingyun.orderreport.dto.TJOrderMonitorODTO;
import com.pinshang.qingyun.orderreport.hystrix.TJOrderMonitorClientHystrix;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import java.util.List;

/**
 * 统计查询--订单监控
 *
 */
@FeignClient(value = ApplicationNameConstant.QINGYUN_ORDER_REPORT_SERVICE, fallbackFactory = TJOrderMonitorClientHystrix.class, configuration = FeignClientConfiguration.class)
public interface TJOrderMonitorClient {
    //订单比较
    @RequestMapping(value = "/tjOrderMonitor/queryTJOrderStatisticsInfo", method = RequestMethod.POST)
    TJOrderMonitorODTO queryOrderInfo(@RequestBody TJOrderMonitorIDTO idto);

    //差异订单
    @RequestMapping(value = "/tjOrderMonitor/queryTJOrderDiffList", method = RequestMethod.POST)
    List<String> queryOrderDiffList(@RequestBody TJOrderMonitorIDTO idto);

    //订单同步
    @RequestMapping(value = "/tjOrderMonitor/syncTJOrder", method = RequestMethod.POST)
    Integer syncTJOrder(@RequestBody TJOrderMonitorIDTO idto);

    //定时清理 最新订单统计表t_tj_order_latest及明细表的数据
    @RequestMapping(value = "/tjOrderMonitor/removeTJOrderLatest", method = RequestMethod.GET)
    Integer removeTJOrderLatest();

    @RequestMapping(value = "/tjOrderMonitor/compensateTjOrderSync", method = RequestMethod.POST)
    Integer compensateTjOrderSync(@RequestBody(required = false) String[] orderTimes);

    //定时清理 最新订单统计表t_tj_order_sync及明细表的数据
    @RequestMapping(value = "/tjOrderMonitor/removeTJOrderSync", method = RequestMethod.GET)
    Integer removeTJOrderSync();
}

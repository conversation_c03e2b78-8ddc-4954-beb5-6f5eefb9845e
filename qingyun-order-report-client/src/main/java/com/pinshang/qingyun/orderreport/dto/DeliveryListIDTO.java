package com.pinshang.qingyun.orderreport.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2019/1/23 9:57.
 */
@Data
public class DeliveryListIDTO {

    @ApiModelProperty(value = "线路组ID")
    private Long lineGroupId;

    @ApiModelProperty(value = "线路组名称")
    private String lineGroupName;

    @ApiModelProperty(value = "送货员ID")
    private Long deliveryManId;

    @ApiModelProperty(value = "送货员姓名")
    private String deliveryManName;

    @ApiModelProperty(value = "发货时间")
    private String deliveryTime;

    @ApiModelProperty(value = "订单日期")
    @JsonFormat(pattern="yyyy-MM-dd",timezone = "GMT+8")
    private Date orderDate;

    @ApiModelProperty(value = "用户ID", hidden = true)
    private Long userId;

    @ApiModelProperty(value = "班组长ID")
    private Long teamLeaderId;

    @ApiModelProperty(value = "配送批次")
    private Integer deliveryBatch;

    @ApiModelProperty(value = "0=送货清单  1= 提货单")
    private Integer type;

    @ApiModelProperty(value = "补货数据 0-订单、 1-补货订单、  2-查所有0和1  （此字段没值也是查所有0和1 兼容清美组手低版本）")
    private Integer orderModeType;
}

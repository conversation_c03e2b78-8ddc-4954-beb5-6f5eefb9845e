package com.pinshang.qingyun.orderreport.hystrix;

import com.pinshang.qingyun.orderreport.service.StatisticalOrderClient;
import feign.hystrix.FallbackFactory;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2019/3/6 17:30.
 */
@Component
public class StatisticalOrderClientHystrix implements FallbackFactory<StatisticalOrderClient> {
    @Override
    public StatisticalOrderClient create(Throwable cause) {
        return new StatisticalOrderClient(){

            @Override
            public boolean discardTestUserOrder() {
                return false;
            }
        };
    }
}

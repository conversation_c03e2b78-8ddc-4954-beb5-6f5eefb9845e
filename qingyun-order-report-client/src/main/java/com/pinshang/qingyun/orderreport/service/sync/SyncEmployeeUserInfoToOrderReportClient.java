package com.pinshang.qingyun.orderreport.service.sync;

import java.util.List;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import com.pinshang.qingyun.base.configure.FeignClientConfiguration;
import com.pinshang.qingyun.base.constant.ApplicationNameConstant;
import com.pinshang.qingyun.orderreport.dto.sync.DifferenceInfoODTO;
import com.pinshang.qingyun.orderreport.dto.sync.SyncInfoByCodesIDTO;
import com.pinshang.qingyun.orderreport.hystrix.sync.SyncEmployeeUserInfoToOrderReportClientHystrix;

/**
 * 同步职员用户相关信息
 *
 * <AUTHOR>
 *
 * @date 2019年02月26日
 */
@FeignClient(value = ApplicationNameConstant.QINGYUN_ORDER_REPORT_SERVICE, fallbackFactory = SyncEmployeeUserInfoToOrderReportClientHystrix.class, configuration = FeignClientConfiguration.class)
public interface SyncEmployeeUserInfoToOrderReportClient {
    
	/**
	 * 同步主库相关信息到从库
	 * 
	 * @param idto
	 * @return
	 */
	@RequestMapping(value = "/sync/employeeUserInfoTo/syncEmployeeUserInfo", method = RequestMethod.POST)
    public Integer syncEmployeeUserInfo(@RequestBody SyncInfoByCodesIDTO idto);
    
	/**
	 * 查询主从差异信息
	 * 
	 * @return
	 */
	@RequestMapping(value = "/sync/employeeUserInfoTo/selectEmployeeUserDifferenceInfo", method = RequestMethod.POST)
	public DifferenceInfoODTO selectEmployeeUserDifferenceInfo();
	
	/**
	 * 查询从库缺失的编码集合
	 * 
	 * @return
	 */
	@RequestMapping(value = "/sync/employeeUserInfoTo/selectMissingEmployeeUserCodeList", method = RequestMethod.POST)
	public List<String> selectMissingEmployeeUserCodeList();

}

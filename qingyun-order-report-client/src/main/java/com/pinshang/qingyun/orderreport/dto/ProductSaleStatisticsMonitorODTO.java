package com.pinshang.qingyun.orderreport.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 产品销售汇总监控--比较，返回数据
 */
@Data
public class ProductSaleStatisticsMonitorODTO {
    @ApiModelProperty("原始商品总数量")
    private BigDecimal totalQuantity;
    @ApiModelProperty("原始商品总金额")
    private BigDecimal totalAmount;

    @ApiModelProperty("统计销售汇总后的商品总数量")
    private BigDecimal totalSaleQuantity;
    @ApiModelProperty("统计销售汇总后的商品总金额")
    private BigDecimal totalSaleAmount;

    @ApiModelProperty("统计督导汇总后的商品总数量")
    private BigDecimal totalSupervisorQuantity;
    @ApiModelProperty("统计督导汇总后的商品总金额")
    private BigDecimal totalSupervisorAmount;

    @ApiModelProperty("统计工厂汇总后的商品总数量")
    private BigDecimal totalFactoryQuantity;
    @ApiModelProperty("统计工厂汇总后的商品总金额")
    private BigDecimal totalFactoryAmount;


}

package com.pinshang.qingyun.orderreport.hystrix;

import com.github.pagehelper.PageInfo;
import com.pinshang.qingyun.orderreport.dto.LateOrderIDTO;
import com.pinshang.qingyun.orderreport.dto.LateOrderODTO;
import com.pinshang.qingyun.orderreport.dto.OrderDetailIDTO;
import com.pinshang.qingyun.orderreport.dto.OrderDetailODTO;
import com.pinshang.qingyun.orderreport.service.OrderDetailClient;
import com.pinshang.qingyun.orderreport.service.OrderReportJobClient;
import feign.hystrix.FallbackFactory;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @Date 2024/6/21 13:25
 */
@Component
public class OrderReportJobClientHystrix implements FallbackFactory<OrderReportJobClient> {
    @Override
    public OrderReportJobClient create(Throwable throwable) {
        return new OrderReportJobClient() {

            @Override
            public void checkOrderSize() {

            }

            @Override
            public void checkOrderListSize() {

            }

            @Override
            public void checkOrderListLatestSize() {

            }

            @Override
            public void repeatHaving1() {

            }

            @Override
            public void syncOrderList(Integer forwardAmount, Integer amount) {

            }
        };
    }
}

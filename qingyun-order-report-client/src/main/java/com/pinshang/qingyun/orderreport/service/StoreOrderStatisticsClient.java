package com.pinshang.qingyun.orderreport.service;

import com.github.pagehelper.PageInfo;
import com.pinshang.qingyun.base.configure.FeignClientConfiguration;
import com.pinshang.qingyun.base.constant.ApplicationNameConstant;
import com.pinshang.qingyun.orderreport.dto.*;
import com.pinshang.qingyun.orderreport.hystrix.StoreOrderStatisticsClientHystrix;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

/**
 * Created by zxj on 2021/3/8
 */
@FeignClient(value = ApplicationNameConstant.QINGYUN_ORDER_REPORT_SERVICE, fallbackFactory = StoreOrderStatisticsClientHystrix.class, configuration = FeignClientConfiguration.class)
public interface StoreOrderStatisticsClient {
    /**
     * 清美人督导客户订货统计
     * @param storeOrderStatisticsIDTO
     * @return
     */
    @RequestMapping(value = "/statistical/order/queryStoreOrderStatistics", method = RequestMethod.POST)
    StoreOrderStatisticsODTO queryStoreOrderStatistics(@RequestBody StoreOrderStatisticsIDTO storeOrderStatisticsIDTO);

}

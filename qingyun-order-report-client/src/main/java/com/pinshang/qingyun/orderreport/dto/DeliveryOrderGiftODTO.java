package com.pinshang.qingyun.orderreport.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @Date 2019/3/8 14:34
 */
@Data
@ApiModel
public class DeliveryOrderGiftODTO {
    /** 商品/配货/赠品明细id**/
    @ApiModelProperty(" 商品/配货/赠品明细id")
    private Long id;
    /** 订单id **/
    @ApiModelProperty("订单id")
    private Long orderId;
    /** 商品id **/
    @ApiModelProperty("商品id")
    private Long commodityId;
    /** 商品数量 **/
    @ApiModelProperty("商品数量")
    private BigDecimal commodityNum;
    /** 单价 **/
    @ApiModelProperty("单价")
    private BigDecimal commodityPrice;
    /**
     * 促销前的源始金额
     */
    @ApiModelProperty("金额")
    private BigDecimal totalPrice;
    /** 类型 **/
    @ApiModelProperty("类型")
    private Integer type;
    /** 备注 **/
    @ApiModelProperty("备注")
    private String remark;
    /**
     * 商品名称
     */
    @ApiModelProperty("商品名称")
    private String commodityName;
    /**
     * 商品编码
     */
    @ApiModelProperty("商品编码")
    private String commodityCode;
    /**
     * 商品规格
     */
    @ApiModelProperty("商品规格")
    private String commoditySpec;
}

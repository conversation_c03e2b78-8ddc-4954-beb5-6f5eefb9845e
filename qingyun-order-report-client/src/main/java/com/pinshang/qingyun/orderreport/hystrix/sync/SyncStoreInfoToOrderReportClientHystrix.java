package com.pinshang.qingyun.orderreport.hystrix.sync;

import com.pinshang.qingyun.orderreport.dto.sync.DifferenceInfoODTO;
import com.pinshang.qingyun.orderreport.dto.sync.SyncInfoByCodesIDTO;
import com.pinshang.qingyun.orderreport.service.sync.SyncStoreInfoToOrderReportClient;

import feign.hystrix.FallbackFactory;

import org.springframework.stereotype.Component;

import java.util.List;

import lombok.extern.slf4j.Slf4j;

/**
 * 同步客户相关信息
 *
 * <AUTHOR>
 *
 * @date 2019年01月21日
 */
@Slf4j
@Component
public class SyncStoreInfoToOrderReportClientHystrix implements FallbackFactory<SyncStoreInfoToOrderReportClient> {
    @Override
    public SyncStoreInfoToOrderReportClient create(Throwable throwable) {
        return new SyncStoreInfoToOrderReportClient() {

        	@Override
			public Integer syncStoreInfo(SyncInfoByCodesIDTO idto) {
        		log.error("\n syncStoreInfo:" + throwable.toString() + "\nidto=" + idto);
				return null;
			}
        	
        	@Override
			public DifferenceInfoODTO selectStoreDifferenceInfo() {
        		log.error("\n selectStoreDifferenceInfo:" + throwable.toString());
				return null;
			}

			@Override
			public List<String> selectMissingStoreCodeList() {
				log.error("\n selectMissingStoreCodeList:" + throwable.toString());
				return null;
			}

        };
    }

}

package com.pinshang.qingyun.orderreport.service.sync;

import com.pinshang.qingyun.base.configure.FeignClientConfiguration;
import com.pinshang.qingyun.base.constant.ApplicationNameConstant;
import com.pinshang.qingyun.orderreport.dto.sync.DifferenceInfoODTO;
import com.pinshang.qingyun.orderreport.dto.sync.SyncInfoByCodesIDTO;
import com.pinshang.qingyun.orderreport.hystrix.sync.SyncCommodityInfoToOrderReportClientHystrix;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import java.util.List;

/**
 * 同步商品相关信息
 *
 * <AUTHOR>
 *
 * @date 2019年01月21日
 */
@FeignClient(value = ApplicationNameConstant.QINGYUN_ORDER_REPORT_SERVICE, fallbackFactory = SyncCommodityInfoToOrderReportClientHystrix.class, configuration = FeignClientConfiguration.class)
public interface SyncCommodityInfoToOrderReportClient {
    
	/**
	 * 同步主库商品相关信息到从库
	 * 
	 * @param idto
	 * @return
	 */
	@RequestMapping(value = "/sync/commodityInfoTo/syncCommodityInfo", method = RequestMethod.POST)
    public Integer syncCommodityInfo(@RequestBody SyncInfoByCodesIDTO idto);
	
	/**
	 * 查询主从商品差异信息
	 * 
	 * @return
	 */
	@RequestMapping(value = "/sync/commodityInfoTo/selectCommodityDifferenceInfo", method = RequestMethod.POST)
	public DifferenceInfoODTO selectCommodityDifferenceInfo();
	
	/**
	 * 查询从库缺失的商品编码集合
	 * 
	 * @return
	 */
	@RequestMapping(value = "/sync/commodityInfoTo/selectMissingCommodityCodeList", method = RequestMethod.POST)
	public List<String> selectMissingCommodityCodeList();

}

package com.pinshang.qingyun.orderreport.hystrix;

import com.github.pagehelper.PageInfo;
import com.pinshang.qingyun.orderreport.dto.*;
import com.pinshang.qingyun.orderreport.service.StoreOrderStatisticsClient;
import feign.hystrix.FallbackFactory;
import org.springframework.stereotype.Component;

/**
 * Created by zxj on 2021/3/8
 */
@Component
public class StoreOrderStatisticsClientHystrix implements FallbackFactory<StoreOrderStatisticsClient> {
    @Override
    public StoreOrderStatisticsClient create(Throwable throwable) {
        return new StoreOrderStatisticsClient() {

            @Override
            public StoreOrderStatisticsODTO queryStoreOrderStatistics(StoreOrderStatisticsIDTO storeOrderStatisticsIDTO) {
                return null;
            }

        };
    }
}

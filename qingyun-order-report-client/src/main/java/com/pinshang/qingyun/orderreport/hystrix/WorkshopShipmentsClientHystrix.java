package com.pinshang.qingyun.orderreport.hystrix;

import com.pinshang.qingyun.orderreport.dto.*;
import com.pinshang.qingyun.orderreport.service.WorkshopShipmentsClient;
import feign.hystrix.FallbackFactory;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2019/2/28 13:48.
 */
@Component
public class WorkshopShipmentsClientHystrix implements FallbackFactory<WorkshopShipmentsClient> {
    @Override
    public WorkshopShipmentsClient create(Throwable cause) {
        return new WorkshopShipmentsClient(){

            @Override
            public String printAll(List<ProductShipmentsRequestIDTO> requestIDTO) {
                return null;
            }

            @Override
            public WorkshopShipmentsPreviewODTO preview(ProductShipmentsRequestIDTO requestIDTO) {
                return null;
            }

            @Override
            public String print(ProductShipmentsRequestIDTO requestIDTO) {
                return null;
            }

            @Override
            public List<WorkshopODTO> doSearch(ProductShipmentsRequestIDTO requestIDTO) {
                return null;
            }

            @Override
            public List<ShipmentsODTO> list(ShipmentsIDTO vo) {
                return null;
            }
        };
    }

}

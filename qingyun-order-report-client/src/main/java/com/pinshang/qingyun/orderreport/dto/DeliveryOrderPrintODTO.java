package com.pinshang.qingyun.orderreport.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@Data
public class DeliveryOrderPrintODTO {
    private Long id;
    private String orderNo;
    private Date orderTime;
    private BigDecimal payMoney;//商品金额小计
    private BigDecimal totalAmount;// 合计  payMoney + deliveryFees
    private BigDecimal deliveryFees;//配送费小计
    private Integer printNumber;
    private String remark;
    private String createName;

    private String storeCode;
    private String storeName;
    private String shopAddress;
    private Long companyId;
    private String companyName;
    private String companyStartsWith;
    private String mobile;
    private Integer testReport;
    private Integer showPrice;
    private String deliverymanCode;
    private String deliverymanName;
    private String regionalManager;
    private String superintend;
    private List<DeliveryOrderItemPrintODTO> orderPrintItems;
    private String local;
    private boolean madeDate;

    @ApiModelProperty("送货时间要求")
    private String receiveTime;

    private String deliverymanMobile;
    private String regionalManagerMobile;
    private String superintendMobile;
    private String lineGroupName;
}

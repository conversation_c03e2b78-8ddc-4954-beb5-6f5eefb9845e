package com.pinshang.qingyun.orderreport.hystrix;


import com.pinshang.qingyun.orderreport.dto.TJOrderMonitorIDTO;
import com.pinshang.qingyun.orderreport.dto.sync.SyncInfoByCodesIDTO;
import com.pinshang.qingyun.orderreport.service.TJOrderMirrorClient;
import feign.hystrix.FallbackFactory;
import org.springframework.stereotype.Component;

@Component
public class TJOrderMirrorClientHystrix implements FallbackFactory<TJOrderMirrorClient> {
    @Override
    public TJOrderMirrorClient create(Throwable throwable) {
        return new TJOrderMirrorClient() {


            @Override
            public Integer updateOrderMirror(SyncInfoByCodesIDTO idto) {
                return null;
            }

            @Override
            public Integer syncTJOrderMirror(TJOrderMonitorIDTO idto) {
                return null;
            }
        };
    }

}

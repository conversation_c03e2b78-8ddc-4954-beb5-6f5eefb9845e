package com.pinshang.qingyun.orderreport.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2019/1/23 10:19.
 */
@Data
public class DeliveryListPreviewIDTO {

    @ApiModelProperty(value = "订单ID")
    private Long orderId ;

    @ApiModelProperty(value = "线路ID")
    private Long lineId;

    @ApiModelProperty(value = "订单日期")
    @JsonFormat(pattern="yyyy-MM-dd",timezone = "GMT+8")
    private Date orderDate;

    @ApiModelProperty(value = "打印批次ID")
    private Long printDeliveryBatchId;

    @ApiModelProperty(value = "送货员ID")
    private String deliveryManId;

    @ApiModelProperty(value = "打印批次名称")
    private String printDeliveryBatchName;

    @ApiModelProperty(value = "当前操作用户ID")
    private Long userId;

    @ApiModelProperty(value = "配送批次")
    private Integer deliveryBatch;
    /**
     * 页面传入打印状态，根据 printStatus 和 type 值 判断 是否需要更新数据库，记录打印状态
     */
    private Integer printStatus;
    /**
     *  0=送货清单
     *  1=提货单
     */
    private Integer type;

    @ApiModelProperty(value = "补货数据 0-订单、 1-补货订单、  2-查所有0和1  （此字段没值也是查所有0和1 兼容清美组手低版本）")
    private Integer orderModeType;
}

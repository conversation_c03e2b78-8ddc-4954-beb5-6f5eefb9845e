package com.pinshang.qingyun.orderreport.hystrix;

import com.pinshang.qingyun.orderreport.dto.NonlocalShipmentsIDTO;
import com.pinshang.qingyun.orderreport.dto.NonlocalShipmentsODTO;
import com.pinshang.qingyun.orderreport.dto.ProductShipmentsRequestIDTO;
import com.pinshang.qingyun.orderreport.dto.WorkshopShipmentsPreviewODTO;
import com.pinshang.qingyun.orderreport.service.WorkshopNonLocalShipmentsClient;
import feign.hystrix.FallbackFactory;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2019/3/4 13:59.
 */
@Component
public class WorkshopNonLocalShipmentsClientHystrix implements FallbackFactory<WorkshopNonLocalShipmentsClient> {
    @Override
    public WorkshopNonLocalShipmentsClient create(Throwable cause) {
        return new WorkshopNonLocalShipmentsClient(){

            @Override
            public WorkshopShipmentsPreviewODTO preview(ProductShipmentsRequestIDTO requestIDTO) {
                return null;
            }

            @Override
            public String print(ProductShipmentsRequestIDTO requestIDTO) {
                return null;
            }

            @Override
            public String printAll(List<ProductShipmentsRequestIDTO> requestIDTO) {
                return null;
            }

            @Override
            public List<NonlocalShipmentsODTO> queryListForApp(NonlocalShipmentsIDTO vo) {
                return null;
            }
        };
    }
}

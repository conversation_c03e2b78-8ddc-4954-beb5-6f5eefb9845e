package com.pinshang.qingyun.orderreport.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2019/1/23 10:17.
 * 送货清单-预览
 */
@Data
public class DeliveryListPreviewODTO {

    @ApiModelProperty(value = "线路ID")
    private Long id;

    @ApiModelProperty("送货人ID")
    private Long deliveryManId;

    @ApiModelProperty(value = "订单ID")
    private Long orderId;

    @ApiModelProperty(value = "线路编码")
    private String lineCode;

    @ApiModelProperty(value = "线路名称")
    private String lineName;

    @ApiModelProperty(value = "线路组名称")
    private String lineGroupName;

    @ApiModelProperty(value = "发货仓库")
    private String warehouse;

    @ApiModelProperty(value = "送货日期")
    private Date orderDate;

    @ApiModelProperty(value = "送货员姓名")
    private String deliveryManName;

    @ApiModelProperty(value = "打印批次")
    private List<DictionaryODTO> printDeliveryBatch;

    @ApiModelProperty(value = "默认显示的打印批次ID")
    private Long defaultPrintDeliveryBatchId;

    @ApiModelProperty(value = "默认打印批次字符串类型ID")
    private String defaultPrintDeliveryBatchIdStr;

    @ApiModelProperty(value = "默认打印批次名称")
    private String defaultPrintDeliveryBatch;

    @ApiModelProperty(value = "客户名称列表")
    private List<String> storeNames;

    @ApiModelProperty(value = "行数据信息")
    private List<List<String>> goodsInfo;

    @ApiModelProperty(value = "补货数据选项值")
    private String orderModeTypeName;
}

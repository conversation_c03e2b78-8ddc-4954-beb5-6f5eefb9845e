package com.pinshang.qingyun.orderreport.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 送货单打印传参
 */
@Data
@ApiModel
public class DeliveryOrderPrintIDTO {

    @ApiModelProperty("订单ID")
    private List<String> orderIds;

    @ApiModelProperty("标识是否打印")
    private Boolean printFlag;

    @ApiModelProperty(value = "操作人")
    private Long userId;

    @ApiModelProperty(value = "系统设置的打印机类型")
    private Integer printType;

}

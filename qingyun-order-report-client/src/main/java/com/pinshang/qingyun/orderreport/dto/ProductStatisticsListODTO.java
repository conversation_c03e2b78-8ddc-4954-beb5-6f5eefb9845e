package com.pinshang.qingyun.orderreport.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 产品统计清单
 */
@Data
public class ProductStatisticsListODTO {

    @ApiModelProperty(value = "客户编码")
    private String storeCode;

    @ApiModelProperty(value = "客户名称")
    private String storeName;

    @ApiModelProperty(value = "商品名称")
    private String commodityName;

    @ApiModelProperty(value = "商品规格")
    private String commoditySpec;

    @ApiModelProperty(value = "单价")
    private BigDecimal commodityPrice;

    @ApiModelProperty(value = "数量")
    private BigDecimal commodityNum;

    @ApiModelProperty(value = "金额")
    private BigDecimal totalPrice;

}

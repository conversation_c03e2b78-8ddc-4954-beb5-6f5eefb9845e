package com.pinshang.qingyun.orderreport.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.pinshang.qingyun.orderreport.dto.Pagination;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * 产品统计清单请求参数
 */
@Data
public class ProductStatisticsListIDTO extends Pagination {

    @ApiModelProperty(value = "商品ID")
    private Long commodityId;

    @ApiModelProperty(value = "商品名称")
    private String commodityName;

    @ApiModelProperty(value = "送货日期")
    @JsonFormat(pattern="yyyy-MM-dd",timezone = "GMT+8")
    private Date orderTime;

    @ApiModelProperty(value = "结账客户ID")
    private Long storeSettId;

    @ApiModelProperty(value = "结账客户")
    private String storeSettName;

    @ApiModelProperty(value = "送货员ID")
    private Long deliveryManId;

    @ApiModelProperty(value = "送货员")
    private String deliveryManName;

    @ApiModelProperty(value = "发货仓库ID")
    private Long deliveryWarehouseId;

    @ApiModelProperty(value = "发货仓库")
    private String deliveryWarehouseName;

    @ApiModelProperty(value = "线路组ID")
    private Long lineGroupId;

    @ApiModelProperty(value = "线路组")
    private String lineGroupName;

    @ApiModelProperty(value = "客户ID")
    private Long storeId;

    @ApiModelProperty(value = "操作员，打印需要用到")
    private Long userId;

    @ApiModelProperty("公司id")
    private Long companyId;

}

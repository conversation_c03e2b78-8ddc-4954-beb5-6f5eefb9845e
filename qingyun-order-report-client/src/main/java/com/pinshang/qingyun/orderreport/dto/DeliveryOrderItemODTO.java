package com.pinshang.qingyun.orderreport.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @Date 2019/3/8 14:33
 */
@Data
@ApiModel
public class DeliveryOrderItemODTO {
    private Long id;
    /**
     * 客户编码
     */
    @ApiModelProperty("客户编码")
    private String storeCode;
    /**
     * 送货员
     */
    @ApiModelProperty("送货员")
    private String deliveryManName;
    /**
     * 督导
     */
    @ApiModelProperty("督导")
    private String supervisorName;
    /**
     * 客户名称
     */
    @ApiModelProperty("客户名称")
    private String storeName;
    /**
     * 地区
     */
    @ApiModelProperty("地区")
    private String areaName;
    /**
     * 结账客户
     */
    @ApiModelProperty("结账客户")
    private String settlementCustomer;
    /**
     * 销售员
     */
    @ApiModelProperty("销售员")
    private String salesmanName;
    /**
     * 客户备注
     */
    @ApiModelProperty("客户备注")
    private String storeDescribe;
    /**
     * 地址
     */
    @ApiModelProperty("地址")
    private String deliveryAddress;
    /**
     * 类型
     */
    @ApiModelProperty("类型")
    private String storeTypeName;
    /**
     * 联系电话
     */
    @ApiModelProperty("联系电话")
    private String linkmanMobile;
    /**
     * 备注
     */
    @ApiModelProperty("备注")
    private String orderRemark;
    /**
     * 余额
     */
    @ApiModelProperty("余额")
    private BigDecimal collectPrice;
    /**
     * 送货日期
     */
    @ApiModelProperty("送货日期")
    private Date deliveryDate;
    /**
     * 更新日期
     */
    @ApiModelProperty("更新日期")
    private Date updateTime;
    /**
     * 制单日期
     */
    @ApiModelProperty("制单日期")
    private Date createTime;
    /**
     * 操作人员
     */
    @ApiModelProperty("操作人员")
    private String operatorName;
    /**
     * 订单金额
     */
    @ApiModelProperty("订单金额")
    private BigDecimal finalAmount;
    /***
     * 订单配送费
     */
    private BigDecimal freightAmount;

    /***
     * 订单金额合计 = 订单金额+订单配送费
     */
    private BigDecimal sumAmount;


    @ApiModelProperty("送货时间要求")
    private String receiveTime;

    private Integer orderType;

    @ApiModelProperty("订单类型名称")
    private String orderTypeName;
}

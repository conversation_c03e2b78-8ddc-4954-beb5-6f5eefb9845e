package com.pinshang.qingyun.orderreport.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR> wujie
 * @date : 2024/7/18 17:56
 * @description: 采购预估-商品销售汇总查询条件
 */
@Data
public class ProductSaleStatisticsPurchaseQueryDto {

    @ApiModelProperty("送货日期开始时间")
    private Date startDate;

    @ApiModelProperty("送货日期结束时间")
    private Date endDate;

    @ApiModelProperty("订货日期开始时间")
    private Date startCreateTime;

    @ApiModelProperty("订货日期结束时间")
    private Date endCreateTime;

    @ApiModelProperty("商品ID")
    private Long commodityId;

    @ApiModelProperty("商品ID列表")
    private List<Long> commodityIdList;
}

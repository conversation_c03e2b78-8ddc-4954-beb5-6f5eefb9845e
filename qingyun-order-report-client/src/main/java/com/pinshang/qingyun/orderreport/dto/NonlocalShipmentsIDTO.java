package com.pinshang.qingyun.orderreport.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;


@Data
public class NonlocalShipmentsIDTO {

    /**
     * 订单日期
     * "yyyy-MM-dd"
     */
    @ApiModelProperty("订单日期 yyyy-MM-dd")
    private String orderDate;
    /**
     * 发货时间
     * "HH:mm"
     */
    @ApiModelProperty("发货时间 HH:mm")
    private String deliveryTime;
    /**
     * 发货仓库ID
     */
    @ApiModelProperty("发货仓库ID")
    private Long warehouseId;
    /**
     * 线路组ID
     */
    @ApiModelProperty("线路组ID")
    private Long lineGroupId;
    /**
     * 配送批次
     */
    @ApiModelProperty("字典表ID")
    private Long deliveryBatch;

    /**
     * 生产组主任code
     */
    @ApiModelProperty(value = "生产组主任code",required = true)
    private String directorCode;

    @ApiModelProperty(value ="补货数据 0-订单、 1-补货订单、 2-查所有0和1  （此字段没值也是查所有0和1 兼容清美组手低版本）")
    private Integer orderModeType;
}

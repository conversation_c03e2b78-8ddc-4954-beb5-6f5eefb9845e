package com.pinshang.qingyun.orderreport.dto.sync;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 根据编码同步相关信息
 *
 * <AUTHOR>
 *
 * @date 2019年01月21日
 */
@Data
@NoArgsConstructor
public class SyncInfoByCodesIDTO {
	@ApiModelProperty(position = 11, required = true, value = "系统标识：1-订单统计系统、2-大仓系统")
	private Integer projectId;
	@ApiModelProperty(position = 11, value = "编码，多个以英文逗号分隔，空则同步全部")
	private String codes;
	
	public SyncInfoByCodesIDTO(Integer projectId, String codes) {
		this.projectId = projectId;
		this.codes = codes;
	}
	
}

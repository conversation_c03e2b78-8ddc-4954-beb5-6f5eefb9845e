package com.pinshang.qingyun.orderreport.service;

import com.pinshang.qingyun.base.configure.FeignClientConfiguration;
import com.pinshang.qingyun.base.constant.ApplicationNameConstant;
import com.pinshang.qingyun.orderreport.hystrix.OrderReportJobClientHystrix;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * <AUTHOR>
 * @Date 2024/6/21 13:25
 */
@FeignClient(value = ApplicationNameConstant.QINGYUN_ORDER_REPORT_SERVICE, fallbackFactory = OrderReportJobClientHystrix.class, configuration = FeignClientConfiguration.class)
public interface OrderReportJobClient {

    @RequestMapping(value = "/orderReportJob/web/checkOrderSize", method = RequestMethod.GET)
    void checkOrderSize();

    @RequestMapping(value = "/orderReportJob/web/checkOrderListSize", method = RequestMethod.GET)
     void checkOrderListSize();

    @RequestMapping(value = "/orderReportJob/web/checkOrderListLatestSize", method = RequestMethod.GET)
    void checkOrderListLatestSize();

    @RequestMapping(value = "/orderReportJob/web/repeatHaving1", method = RequestMethod.GET)
    void repeatHaving1();

    @RequestMapping(value = "/orderReportJob/web/syncOrderList", method = RequestMethod.POST)
    public void syncOrderList(@RequestParam("forwardAmount") Integer forwardAmount, @RequestParam("amount") Integer amount);
}

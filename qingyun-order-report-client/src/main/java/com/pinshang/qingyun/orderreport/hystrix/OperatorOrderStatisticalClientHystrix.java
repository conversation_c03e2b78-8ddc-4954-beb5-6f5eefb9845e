package com.pinshang.qingyun.orderreport.hystrix;

import com.pinshang.qingyun.orderreport.dto.OperatorOrderMonitorIDTO;
import com.pinshang.qingyun.orderreport.dto.OperatorOrderReqIDTO;
import com.pinshang.qingyun.orderreport.dto.OperatorOrderRespODTO;
import com.pinshang.qingyun.orderreport.service.OperatorOrderStatisticalClient;
import feign.hystrix.FallbackFactory;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2019/3/13 14:54.
 */
@Component
public class OperatorOrderStatisticalClientHystrix implements FallbackFactory<OperatorOrderStatisticalClient> {
    @Override
    public OperatorOrderStatisticalClient create(Throwable cause) {
        return new OperatorOrderStatisticalClient(){
            @Override
            public List<OperatorOrderRespODTO> queryList(OperatorOrderReqIDTO reqVo) {
                return null;
            }

            @Override
            public String print(OperatorOrderReqIDTO reqVo) {
                return null;
            }

            @Override
            public Integer resetOperatorOrderStatistics(OperatorOrderMonitorIDTO reqVo) {
                return null;
            }
        };
    }
}

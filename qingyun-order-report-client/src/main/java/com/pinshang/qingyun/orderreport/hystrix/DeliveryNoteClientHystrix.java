package com.pinshang.qingyun.orderreport.hystrix;

import com.pinshang.qingyun.orderreport.dto.DeliveryNoteIDTO;
import com.pinshang.qingyun.orderreport.dto.DeliveryNoteODTO;
import com.pinshang.qingyun.orderreport.service.DeliveryNoteClient;
import feign.hystrix.FallbackFactory;
import org.springframework.stereotype.Component;

/**
 * @author: ch<PERSON><PERSON><PERSON>
 * @time: 2021/9/16 17:16
 */
@Component
public class DeliveryNoteClientHystrix implements FallbackFactory<DeliveryNoteClient> {
    @Override
    public DeliveryNoteClient create(Throwable cause) {
        return new DeliveryNoteClient() {

            @Override
            public DeliveryNoteODTO selectDeliveryNote(DeliveryNoteIDTO copy) {
                return null;
            }
        };
    }
}

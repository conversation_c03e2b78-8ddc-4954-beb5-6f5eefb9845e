package com.pinshang.qingyun.orderreport.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2019/3/8 14:32
 */
@Data
@ApiModel
public class DeliveryOrderDetailODTO {
    /**
     * 订单客户信息
     */
    @ApiModelProperty("订单客户信息")
    private DeliveryOrderItemODTO detail;
    /**
     * 订单商品列表
     */
    @ApiModelProperty("订单商品列表")
    private List<DeliveryOrderGiftODTO> orderList;
    /**
     * 订单赠品列表
     */
    @ApiModelProperty("订单赠品列表")
    private List<DeliveryOrderGiftODTO> giftList;
    /**
     * 订单配货列表
     */
    @ApiModelProperty("订单配货列表")
    private List<DeliveryOrderGiftODTO> rationList;

    /**
     * 特惠商品列表
     */
    private List<DeliveryOrderGiftODTO> thList;
}

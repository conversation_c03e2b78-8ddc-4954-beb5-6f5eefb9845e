package com.pinshang.qingyun.orderreport.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2019/3/18 11:13.
 */
@Data
public class LateOrderODTO {

    @ApiModelProperty("送货日期")
    //@JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    private Date orderTime;

    @ApiModelProperty(value = "订单创建时间", notes = "年月日时分秒格式,需要自己格式化取时分秒")
    //@JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    private Date createTime;

    @ApiModelProperty("客户编码")
    private String storeCode;

    @ApiModelProperty("客户名称")
    private String storeName;

    @ApiModelProperty("订单金额")
    private String orderAmount;

    @ApiModelProperty("督导姓名")
    private String supervisionName;
    @ApiModelProperty("操作员姓名")
    private String operatorName;
    @ApiModelProperty("补货单号(订单号)")
    private String orderCode;
    @ApiModelProperty("线路组名称")
    private String lineGroupName;
}

package com.pinshang.qingyun.orderreport.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.pinshang.qingyun.orderreport.dto.Pagination;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @description 产品销售汇总数据(总)
 * @date 2019/09/03
 */
@Data
public class CommoditySaleStatisticsODTO{

    @ApiModelProperty(value = "商品工厂ID(t_factory表主键)")
    private Long factoryId;

    @ApiModelProperty(value = "商品工厂名称")
    private String factoryName;

    @ApiModelProperty(value = "商品车间ID")
    private Long flowshopId;

    @ApiModelProperty(value = "商品车间名称")
    private String flowshopName;

    @ApiModelProperty(value = "商品生产组ID(t_factory_workshop表主键)")
    private Long workshopId;

    @ApiModelProperty(value = "商品生产组名称")
    private String workshopName;

    @ApiModelProperty(value = "商品一级分类ID")
    private Long cateId1;

    @ApiModelProperty(value = "商品一级分类名称")
    private String cateName1;

    @ApiModelProperty(value = "商品二级分类ID")
    private Long cateId2;

    @ApiModelProperty(value = "商品二级分类名称")
    private String cateName2;

    @ApiModelProperty(value = "商品三级分类ID")
    private Long cateId3;

    @ApiModelProperty(value = "商品三级分类名称")
    private String cateName3;

    @ApiModelProperty(value = "商品ID")
    private Long commodityId;

    @ApiModelProperty(value = "商品ID,String类型")
    private String commodityIdStr;

    @ApiModelProperty(value = "商品编码")
    private String commodityCode;

    @ApiModelProperty(value = "商品名称(由commodityName（commoditySpec）拼接而成)")
    private String commodityName;

    @ApiModelProperty(value = "计量单位")
    private String commodityUnitName;

    @ApiModelProperty(value = "商品数量")
    private BigDecimal totalQuantity;

    @ApiModelProperty(value = "商品金额")
    private BigDecimal totalAmount;

    @ApiModelProperty(value = "客户类型详细信息")
    private List<CommoditySaleStatisticsItemODTO> commoditySaleStatisticsItems;

    public void setCommodityId(Long commodityId) {
        this.commodityId = commodityId;
        if(commodityId != null){
            this.commodityIdStr = String.valueOf(commodityId);
        }
    }
}

package com.pinshang.qingyun.orderreport.service;

import com.pinshang.qingyun.base.configure.FeignClientConfiguration;
import com.pinshang.qingyun.base.constant.ApplicationNameConstant;
import com.pinshang.qingyun.orderreport.dto.DeliveryListIDTO;
import com.pinshang.qingyun.orderreport.dto.DeliveryListODTO;
import com.pinshang.qingyun.orderreport.dto.DeliveryListPreviewIDTO;
import com.pinshang.qingyun.orderreport.dto.DeliveryListPreviewODTO;
import com.pinshang.qingyun.orderreport.hystrix.DeliveryListClientHystrix;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2019/1/23 9:53.
 * 统计查询 迁移, 送货清单功能
 */
@FeignClient(value = ApplicationNameConstant.QINGYUN_ORDER_REPORT_SERVICE, fallbackFactory = DeliveryListClientHystrix.class, configuration = FeignClientConfiguration.class)
public interface DeliveryListClient {

    /**
     * 送货清单列表查询
     * @param idto 入参
     * @return 返回JSON数据.
     */
    @RequestMapping(value = "/statistical/deliveryList/search", method = RequestMethod.POST)
    List<DeliveryListODTO> searchDeliveryList(@RequestBody DeliveryListIDTO idto);

    /**
     * 送货清单预览
     * @param param param
     * @return 预览数据
     */
    @RequestMapping(value = "/statistical/deliveryList/preview", method = RequestMethod.POST)
    DeliveryListPreviewODTO preview(@RequestBody DeliveryListPreviewIDTO param);

    /**
     * 打印当前送货清单
     * @param param
     * @return
     */
    @RequestMapping(value = "/statistical/deliveryList/print", method = RequestMethod.POST)
    String print(@RequestBody DeliveryListPreviewIDTO param);

    /**
     * 打印所有选中的送货清单
     * @param params
     * @param userId
     * @return
     */
    @RequestMapping(value = "/statistical/deliveryList/printAll/{userId}", method = RequestMethod.POST)
    String printAll(@RequestBody List<DeliveryListPreviewIDTO> params, @PathVariable("userId") Long userId);
}

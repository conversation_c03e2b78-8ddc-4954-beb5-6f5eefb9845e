package com.pinshang.qingyun.orderreport.hystrix;

import com.pinshang.qingyun.orderreport.dto.CommodityQuantityIDTO;
import com.pinshang.qingyun.orderreport.dto.CommodityQuantityIDTO2;
import com.pinshang.qingyun.orderreport.dto.CommodityQuantityODTO;
import com.pinshang.qingyun.orderreport.service.ForeignClient;
import feign.hystrix.FallbackFactory;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @author: chenqiang
 * @time: 22/7/20/020 14:42
 */
@Component
public class ForeignClientHystrix implements FallbackFactory<ForeignClient> {
    @Override
    public ForeignClient create(Throwable throwable) {
        return new ForeignClient() {
            @Override
            public List<CommodityQuantityODTO> selectOrderCountCommodityQuantityByOrderTime(CommodityQuantityIDTO commodityQuantityIDTO) {
                return null;
            }

            @Override
            public List<CommodityQuantityODTO> selectOrderCountCommodityQuantityByOrderTimeV2(CommodityQuantityIDTO2 commodityQuantityIDTO) {
                return null;
            }
        };
    }
}

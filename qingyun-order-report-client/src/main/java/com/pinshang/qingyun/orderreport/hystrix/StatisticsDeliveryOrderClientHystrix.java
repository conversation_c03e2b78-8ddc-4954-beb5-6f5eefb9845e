package com.pinshang.qingyun.orderreport.hystrix;

import com.github.pagehelper.PageInfo;
import com.pinshang.qingyun.orderreport.dto.*;
import com.pinshang.qingyun.orderreport.service.StatisticsDeliveryOrderClient;
import feign.hystrix.FallbackFactory;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2019/3/8 14:25
 */
@Component
public class StatisticsDeliveryOrderClientHystrix implements FallbackFactory<StatisticsDeliveryOrderClient> {
    @Override
    public StatisticsDeliveryOrderClient create(Throwable throwable) {
        return new StatisticsDeliveryOrderClient() {
            @Override
            public PageInfo<DeliveryOrderODTO> queryList(DeliveryOrderIDTO deliveryOrderIDTO) {
                return null;
            }

            @Override
            public DeliveryOrderDetailODTO queryDetail(Long orderId) {
                return null;
            }

            @Override
            public List<DeliveryOrderPrintODTO> print(DeliveryOrderPrintIDTO idto) {
                return null;
            }

        };
    }
}

package com.pinshang.qingyun.orderreport.hystrix;

import com.pinshang.qingyun.orderreport.dto.XdaStoreOrderAmountIDTO;
import com.pinshang.qingyun.orderreport.dto.XdaStoreOrderAmountODTO;
import com.pinshang.qingyun.orderreport.service.XdaStoreOrderAmountClient;
import feign.hystrix.FallbackFactory;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @author: chenqiang
 * @time: 2021/9/28 10:35
 */
@Component
public class XdaStoreOrderAmountClientHystrix implements FallbackFactory<XdaStoreOrderAmountClient> {
    @Override
    public XdaStoreOrderAmountClient create(Throwable cause) {
        return new XdaStoreOrderAmountClient() {
            @Override
            public List<XdaStoreOrderAmountODTO> exportXdaStoreOrderAmount(XdaStoreOrderAmountIDTO idto) {
                return null;
            }
        };
    }

}

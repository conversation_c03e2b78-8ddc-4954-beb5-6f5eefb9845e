package com.pinshang.qingyun.orderreport.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

@Data
@ApiModel
public class CommodityODTO {
    @ApiModelProperty("商品ID")
    private Long id;

    @ApiModelProperty("商品编码")
    private String commodityCode;

    @ApiModelProperty("商品名称")
    private String commodityName;

    @ApiModelProperty("商品规格")
    private String commoditySpec;

    @ApiModelProperty("商品一级分类ID")
    private Long   commodityFirstKindId;

    @ApiModelProperty("商品一级分类名称")
    private String commodityFirstKindName;

    @ApiModelProperty("商品二级分类ID")
    private Long   commoditySecondKindId;

    @ApiModelProperty("商品二级分类名称")
    private String commoditySecondKindName;

    @ApiModelProperty("商品三级分类ID")
    private Long   commodityThirdKindId;

    @ApiModelProperty("商品三级分类名称")
    private String commodityThirdKindName;

    @ApiModelProperty("商品计量单位")
    private String commodityUnitName;

    @ApiModelProperty("商品工厂ID")
    private Long  commodityFactoryId;

    @ApiModelProperty("商品工厂名称")
    private String commodityFactoryName;

    @ApiModelProperty("状态：0-停用,1-启用")
    private Integer commoditState;

    @ApiModelProperty("包装类型ID")
    private Long   commodityPackageId;

    @ApiModelProperty("整包或散装")
    private String commodityPackageName;

    @ApiModelProperty("淘汰状态:0-淘汰,1-正常")
    private Integer status;

    @ApiModelProperty("可采状态:1-可采，0-不可采")
    private Integer purchaseStatus;

    @ApiModelProperty("商品生产组ID")
    private Long commodityWorkshopId;

    @ApiModelProperty("商品生产组名称")
    private String commodityWorkshopName;

    @ApiModelProperty("商品车间ID")
    private Integer commodityFlowshopId;

    @ApiModelProperty("商品车间名称")
    private String commodityFlowshopName;

    @ApiModelProperty("税率id")
    private Long  taxRateId;

    @ApiModelProperty("税率")
    private BigDecimal taxRate;

    @ApiModelProperty("成本价")
    private BigDecimal costPrice;

    @ApiModelProperty("生产成本价")
    private BigDecimal firstCost;

    @ApiModelProperty("零售价")
    private BigDecimal retailPrice;

    @ApiModelProperty("是否称重0-不称量,1-称重")
    private Integer isWeight;

    @ApiModelProperty("是否框汇总")
    private Integer isSummary;

    private Integer isFrame;

    @ApiModelProperty("主条形码")
    private String  barCode;

    @ApiModelProperty("物流配送模式0=直送，1＝配送，2＝直通")
    private Integer logisticsModel;

    @ApiModelProperty("批次状态,0:非批次 1:批次")
    private Integer batchStatus;

    @ApiModelProperty("免税分类")
    private Long taxFreeTypeId;

    @ApiModelProperty("起卖重量(克)")
    private Integer sellWeight;

    @ApiModelProperty("加重幅度(克)")
    private Integer sellWeightRange;

    @ApiModelProperty("采购箱规")
    private BigDecimal boxCapacity;

    @ApiModelProperty("销售箱装量")
    private BigDecimal salesBoxCapacity;
}

package com.pinshang.qingyun.orderreport.service;

import com.pinshang.qingyun.base.configure.FeignClientConfiguration;
import com.pinshang.qingyun.base.constant.ApplicationNameConstant;
import com.pinshang.qingyun.orderreport.dto.TJOrderMonitorIDTO;
import com.pinshang.qingyun.orderreport.dto.sync.SyncInfoByCodesIDTO;
import com.pinshang.qingyun.orderreport.hystrix.TJOrderMirrorClientHystrix;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

/**
 * Created by zxj on 2019/9/11 11:05
 */
@FeignClient(value = ApplicationNameConstant.QINGYUN_ORDER_REPORT_SERVICE, fallbackFactory = TJOrderMirrorClientHystrix.class, configuration = FeignClientConfiguration.class)
public interface TJOrderMirrorClient {

    //修改订单落地数据
    @RequestMapping(value = "/tjOrderMirror/updateOrderMirror", method = RequestMethod.POST)
    Integer updateOrderMirror(@RequestBody SyncInfoByCodesIDTO idto);

    //订单落地数据同步
    @RequestMapping(value = "/tjOrderMirror/syncTJOrderMirror", method = RequestMethod.POST)
    Integer syncTJOrderMirror(@RequestBody TJOrderMonitorIDTO idto);

}

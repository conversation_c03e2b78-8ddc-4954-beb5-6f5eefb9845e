package com.pinshang.qingyun.orderreport.dto;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 产品销售汇总
 */
@Data
public class ProductSaleStatisticsTempODTO {

    //工厂
    private String factoryName;

    //生产组
    private String workshopName;

    //商品编码
    private String commodityCode;

    //商品名称
    private String commodityName;

    //数量
    private BigDecimal commodityNum;

    //金额
    private BigDecimal totalPrice;

    //客户类型ID
    private Long storeTypeId;

    //客户类型
    private String storeTypeName;

    //商品ID
    private Long commodityId;

    //送货日期
    private Date orderTime;

    //订货日期
    private Date createTime;

}

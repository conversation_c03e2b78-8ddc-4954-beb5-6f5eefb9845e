package com.pinshang.qingyun.orderreport.hystrix;

import com.pinshang.qingyun.orderreport.dto.DeliveryListIDTO;
import com.pinshang.qingyun.orderreport.dto.DeliveryListODTO;
import com.pinshang.qingyun.orderreport.dto.DeliveryListPreviewIDTO;
import com.pinshang.qingyun.orderreport.dto.DeliveryListPreviewODTO;
import com.pinshang.qingyun.orderreport.service.DeliveryListClient;
import feign.hystrix.FallbackFactory;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2019/1/23 10:29.
 */
@Component
public class DeliveryListClientHystrix implements FallbackFactory<DeliveryListClient> {
    @Override
    public DeliveryListClient create(Throwable cause) {
        return new DeliveryListClient() {
            @Override
            public List<DeliveryListODTO> searchDeliveryList(DeliveryListIDTO idto) {
                return null;
            }

            @Override
            public DeliveryListPreviewODTO preview(DeliveryListPreviewIDTO param) {
                return null;
            }

            @Override
            public String print(DeliveryListPreviewIDTO param) {
                return null;
            }

            @Override
            public String printAll(List<DeliveryListPreviewIDTO> params, Long userId) {
                return null;
            }
        };
    }
}

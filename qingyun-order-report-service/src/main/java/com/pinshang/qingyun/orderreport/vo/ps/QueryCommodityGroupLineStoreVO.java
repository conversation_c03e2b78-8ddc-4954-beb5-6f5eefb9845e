package com.pinshang.qingyun.orderreport.vo.ps;

import com.pinshang.qingyun.base.page.Pagination;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @Version 0.1.0
 * @Date 2021/1/28 10:18
 * @Copyright © 2019-2021 qm
 * @Description -- 类说明
 **/
@Data
public class QueryCommodityGroupLineStoreVO extends Pagination {

    @ApiModelProperty(value = "线路id，必传")
    private Long lineId;

    @ApiModelProperty(value = "客户id")
    private Long storeId;

    @ApiModelProperty(value = "客户类型")
    private Long storeTypeId;
}

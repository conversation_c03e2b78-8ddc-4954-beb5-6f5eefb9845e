package com.pinshang.qingyun.orderreport.controller;

import com.alibaba.excel.EasyExcel;
import com.github.pagehelper.PageInfo;
import com.pinshang.qingyun.base.api.QYApplicationContext;
import com.pinshang.qingyun.base.api.TokenInfo;
import com.pinshang.qingyun.base.configure.expand.QYAssert;
import com.pinshang.qingyun.base.constant.RedisKeyPrefixConst;
import com.pinshang.qingyun.base.enums.DictionaryEnums;
import com.pinshang.qingyun.base.util.ExcelUtil;
import com.pinshang.qingyun.base.util.FastThreadLocalUtil;
//import com.pinshang.qingyun.box.utils.BeanUtil;
import com.pinshang.qingyun.box.utils.DateUtil;
import com.pinshang.qingyun.box.utils.SpringUtil;
import com.pinshang.qingyun.common.dto.DictionaryIDTO;
import com.pinshang.qingyun.common.dto.DictionaryODTO;
import com.pinshang.qingyun.common.service.DictionaryClient;
//import com.pinshang.qingyun.order.search.service.EsProductSaleStatisticsClient;
//import com.pinshang.qingyun.orderreport.dto.CommodityODTO;
import com.pinshang.qingyun.infrastructure.exportfile.cache.starter.FileCacheQuery;
import com.pinshang.qingyun.infrastructure.exportfile.cache.starter.FileCacheQueryParameter;
//import com.pinshang.qingyun.orderreport.dto.ProductSaleStatisticsIDTO;
//import com.pinshang.qingyun.orderreport.dto.ProductSaleStatisticsTempODTO;
import com.pinshang.qingyun.orderreport.mapper.entry.*;
import com.pinshang.qingyun.orderreport.model.Commodity;
import com.pinshang.qingyun.orderreport.service.CommoditySaleStatisticsService;
import com.pinshang.qingyun.orderreport.service.CommodityService;
import com.pinshang.qingyun.orderreport.service.ProductSaleStatisticsService;
//import com.pinshang.qingyun.orderreport.service.TJCommodityClient;
import com.pinshang.qingyun.orderreport.util.NumberUtils;
import com.pinshang.qingyun.orderreport.vo.ProductSaleStatisticsCommodityInfoListVo;
import com.pinshang.qingyun.orderreport.vo.ProductSaleStatisticsPurchaseQueryVo;
import com.pinshang.qingyun.orderreport.vo.ProductSaleStatisticsResponseVO;
import com.pinshang.qingyun.orderreport.vo.ProductSaleStatisticsVo;
import com.pinshang.qingyun.smm.dto.user.SelectUserFactoryIdListIDTO;
import com.pinshang.qingyun.smm.service.SMMUserClient;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.jetbrains.annotations.Nullable;
import org.joda.time.LocalDate;
import org.redisson.api.RBucket;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.propertyeditors.CustomDateEditor;
import org.springframework.web.bind.WebDataBinder;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.NumberFormat;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 产品销售汇总
 */
@RestController
@RequestMapping(value = "/statistical/productSaleStatistics")
@Slf4j
public class ProductSaleStatisticsController {

    @Autowired
    private ProductSaleStatisticsService productSaleStatisticsService;

//    @Autowired
//    private TJCommodityClient commodityClient;
    @Autowired
    private CommodityService commodityService;

    @Autowired
    private DictionaryClient dictionaryClient;

    @Autowired
    private RedissonClient redissonClient;

//    @Autowired
//    private EsProductSaleStatisticsClient esProductSaleStatisticsClient;

    @Autowired
    private SMMUserClient userClient;

    @Autowired
    private CommoditySaleStatisticsService commoditySaleStatisticsService;

    /**
     * 产品销售汇总
     * @param vo
     * @return
     */
    @PostMapping("/list")
    public ProductSaleStatisticsTableEntry queryList(@RequestBody ProductSaleStatisticsVo vo) {
        if(vo.getFactoryId()==null){
            List<Long> factoryIds = userClient.selectUserFactoryIdList(SelectUserFactoryIdListIDTO.onlyDb(vo.getUserId()));
            if(CollectionUtils.isEmpty(factoryIds)){
                return new ProductSaleStatisticsTableEntry();
            }
            vo.setFactoryIds(factoryIds);
        }
        return productSaleStatisticsService.queryList(vo);
    }

    /**
     * 改造后service只查询DB数据，到api组装格式
     * @param vo
     * @return
     */
    @PostMapping("/listV2")
    public List<ProductSaleStatisticsTempEntry> queryListV2(@RequestBody ProductSaleStatisticsVo vo) {
        if(vo.getFactoryId()==null){
            List<Long> factoryIds = userClient.selectUserFactoryIdList(SelectUserFactoryIdListIDTO.onlyDb(vo.getUserId()));
            if(CollectionUtils.isEmpty(factoryIds)){
                return new ArrayList<>();
            }
            vo.setFactoryIds(factoryIds);
        }
        return productSaleStatisticsService.queryListV2(vo);
    }

    /**
     * 采购预估-产品销售汇总查询
     */
    @PostMapping("/queryListForPurchase")
    public List<ProductSaleStatisticsTempEntry> queryListForPurchase(@RequestBody ProductSaleStatisticsPurchaseQueryVo queryVo) {
        return productSaleStatisticsService.queryListForPurchase(queryVo);
    }

    /**
     * 查询 送货日期 ≥ T，订货日期为T-2 和 T-1 的订单数据
     */
    @PostMapping("/queryListForPurchaseByCreateTime")
    public List<ProductSaleStatisticsPurchaseTempEntry> queryListForPurchaseByCreateTime(@RequestBody ProductSaleStatisticsPurchaseQueryVo queryVo) {
        return productSaleStatisticsService.queryListForPurchaseByCreateTime(queryVo);
    }

    /**
     * 产品销售汇总金额合计
     * @param vo
     * @return
     */
    @PostMapping("/querySum")
    public ProductSaleStatisticsSumEntry querySum(@RequestBody ProductSaleStatisticsVo vo) {
        if(vo.getFactoryId()==null && (vo.getIsPermissionQuery() == null || vo.getIsPermissionQuery())){
            List<Long> factoryIds = userClient.selectUserFactoryIdList(SelectUserFactoryIdListIDTO.onlyDb(vo.getUserId()));
            if(CollectionUtils.isEmpty(factoryIds)){
                return new ProductSaleStatisticsSumEntry();
            }
            vo.setFactoryIds(factoryIds);
        }
        return productSaleStatisticsService.querySum(vo);
    }

    /**
     * 打印
     */
    @PostMapping("/print")
    public String print(@RequestBody ProductSaleStatisticsVo vo){
        if(vo.getFactoryId()==null){
            List<Long> factoryIds = userClient.selectUserFactoryIdList(SelectUserFactoryIdListIDTO.onlyDb(vo.getUserId()));
            if(CollectionUtils.isEmpty(factoryIds)){
//                return "fail";
            	QYAssert.isFalse("您没有该工厂权限!");
            }
            vo.setFactoryIds(factoryIds);
        }
        return productSaleStatisticsService.print(vo);
    }

    @ApiOperation(value = "导出", notes = "导出产品销售汇总")
    @RequestMapping(value = "/export", method = RequestMethod.GET)
    @FileCacheQuery(bizCode = "OR_PRODUCT_SALE_STATISTICS_E")
    public void export(@FileCacheQueryParameter ProductSaleStatisticsVo dto, HttpServletResponse response) throws IOException {
        long qS = System.currentTimeMillis();
        dto.initExportPage();
        ProductSaleStatisticsResponseVO tableEntry = queryProductSaleStatisticsList(dto);
        long qE = System.currentTimeMillis();
        log.info("产品销售汇总-导出--查询时间=" + ((qE - qS) / 1000));
        //处理数据并导出
        dealDataToExport(response, tableEntry.getTableHeader(),tableEntry.getTableData(),true);
        long eE = System.currentTimeMillis();
        log.info("产品销售汇总-导出--excel处理时间="+ ( (eE -qE) /1000 )  );
    }

    /**
     * 表头
     * @param tableHeaderList
     * @return
     */
    private List<List<String>> initTableHeader(List<List<String>> tableHeaderList){
        List<String> header = tableHeaderList.get(0);
        List<List<String>> headerList = new ArrayList<>(header.size()*2);
        for (int i = 0; i < header.size(); i++){
            if (i < 3 || i >= header.size() || i==4 || i==5) {
                headerList.add(Arrays.asList(header.get(i),header.get(i)));
            }else if (i == 3){
                headerList.add(Arrays.asList(header.get(i),header.get(i)));
                headerList.add(Arrays.asList("计量单位","计量单位"));
                headerList.add(Arrays.asList("车间","车间"));
                headerList.add(Arrays.asList("大类","大类"));
                headerList.add(Arrays.asList("中类","中类"));
                headerList.add(Arrays.asList("小类","小类"));
            }else {
                headerList.add(Arrays.asList(header.get(i), "数量"));
                headerList.add(Arrays.asList(header.get(i), "金额"));
            }
        }
        return headerList;
    }


    /**
     * init导出的数据列
     * @param tableDataList
     * @param map
     * @return
     */
    private void initTableDate(List<List<String>> tableDataList, Map<String, Commodity> map,Boolean isTaxRate){
        Map<String,String> taxRateMap=null;
        if(isTaxRate){
            taxRateMap= commoditySaleStatisticsService.getCommdityTaxRateValue(commoditySaleStatisticsService.getTaxrateDictionaryId());
        }

        //结果集从第4列之后，多出5列：计量单位、车间、大类、中类、小类
        for (int i = 0; i < tableDataList.size(); i++){
            List<String> subData = tableDataList.get(i);
            //中间加入5列：计量单位、车间、大类、中类、小类
            Commodity commodity = map.get(subData.get(2));
            List<String> asList = Arrays.asList(commodity.getCommodityUnitName(),
                    commodity.getCommodityFlowshopName(),
                    commodity.getCommodityFirstKindName(),
                    commodity.getCommoditySecondKindName(),
                    commodity.getCommodityThirdKindName());
            subData.addAll(4,asList);

            if(isTaxRate){
                subData.add(commodity.getTaxRateId()==null ? "" : taxRateMap.get(commodity.getTaxRateId().toString()));
            }
        }
    }

    @InitBinder
    public void initData(WebDataBinder wdb){
        wdb.registerCustomEditor(Date.class, new CustomDateEditor(new SimpleDateFormat("yyyy-MM-dd"), true));
    }

    @ApiOperation(value = "产品销售汇总查询列表", notes = "产品销售汇总列表")
    @ApiImplicitParam(name = "dto", value = "", required = true, paramType = "body", dataType = "ProductSaleStatisticsIDTO")
    @RequestMapping(value = "/queryProductSaleStatisticsList", method = RequestMethod.POST)
    public ProductSaleStatisticsResponseVO queryProductSaleStatisticsList(@RequestBody ProductSaleStatisticsVo dto) {
        ProductSaleStatisticsResponseVO tableEntry = new ProductSaleStatisticsResponseVO();

        //处理表头
        DictionaryIDTO dictionaryIDTO = new DictionaryIDTO();
        dictionaryIDTO.setDictionaryId(DictionaryEnums.STORE_TYPE.getId());
        List<DictionaryODTO> storeTypes = dictionaryClient.getDictionaryList(dictionaryIDTO);
        List<List<String>> tableHeader = this.proceedTableHeader(storeTypes);
        tableEntry.setTableHeader(tableHeader);

        TokenInfo info = FastThreadLocalUtil.getQY();
        dto.setUserId(info.getUserId());
        if(dto.getFactoryId()==null){
            List<Long> factoryIds = userClient.selectUserFactoryIdList(SelectUserFactoryIdListIDTO.onlyDb(dto.getUserId()));
            if(CollectionUtils.isEmpty(factoryIds)){
                return tableEntry;
            }
            dto.setFactoryIds(factoryIds);
        }

        List<ProductSaleStatisticsTempEntry> list = null;
        RBucket<Boolean> bucket = redissonClient.getBucket(QYApplicationContext.applicationNameSwitch + RedisKeyPrefixConst.STATISTICS_ES_SWITCH);
        if (bucket.get()==null || !bucket.get()) {
            list = productSaleStatisticsService.queryListV2(dto);
        }else{
            QYAssert.isTrue(Boolean.FALSE,"此功能已经废弃");
//            ProductSaleStatisticsIDTO statisticsIDTO = BeanUtil.copyProperties(dto, ProductSaleStatisticsIDTO.class);
//            List<ProductSaleStatisticsTempODTO> tempODTOS = esProductSaleStatisticsClient.queryList(statisticsIDTO);
//            list = BeanUtil.copyProperties(tempODTOS, ProductSaleStatisticsTempEntry.class);
        }

        if(SpringUtil.isEmpty(list)){
            return tableEntry;
        }
        Map<String,List<ProductSaleStatisticsTempEntry>> map = new LinkedHashMap<>();
        list.forEach(entry -> {
            String commodityCode = entry.getCommodityCode();
            List<ProductSaleStatisticsTempEntry> entryList;
            if(map.containsKey(commodityCode)){
                entryList = map.get(commodityCode);
            }else{
                entryList = new ArrayList<>();
            }
            entryList.add(entry);
            map.put(commodityCode,entryList);
        });

        List<List<String>> tableData = new ArrayList<>();
        map.forEach((k,v)->{
            List<String> row = proceedTableRow(k, v, storeTypes);
            tableData.add(row);
        });
        tableEntry.setTableData(tableData);
        return tableEntry;
    }

    //-----------产品销售汇总查询-半年---------开始----(列表/合计/导出/打印)---------------------------------------------------

    @ApiOperation(value = "产品销售汇总查询列表-半年", notes = "产品销售汇总列表-半年")
    @ApiImplicitParam(name = "dto", value = "", required = true, paramType = "body", dataType = "ProductSaleStatisticsVo")
    @RequestMapping(value = "/queryProductSaleStatisticsListHalfYear", method = RequestMethod.POST)
    public ProductSaleStatisticsTableEntry queryProductSaleStatisticsListHalfYear(@RequestBody ProductSaleStatisticsVo dto){
        //set用户权限的工厂id集合
        List<Long> factoryIds = getFactoryIds(dto.getFactoryId());
        dto.setFactoryIds(factoryIds);
        return productSaleStatisticsService.queryHalfYearList(dto);
    }

    @ApiOperation(value = "产品销售汇总查询合计-半年", notes = "产品销售汇总查询合计-半年")
    @ApiImplicitParam(name = "vo", value = "", required = true, paramType = "body", dataType = "ProductSaleStatisticsVo")
    @PostMapping("/querySumHalfYear")
    public ProductSaleStatisticsSumEntry querySumHalfYear(@RequestBody ProductSaleStatisticsVo vo) {
        //set用户权限的工厂id集合
        List<Long> factoryIds = getFactoryIds(vo.getFactoryId());
        vo.setFactoryIds(factoryIds);
        return productSaleStatisticsService.querySumHalfYear(vo);
    }

    @ApiOperation(value = "导出产品销售汇总-半年", notes = "导出产品销售汇总-半年")
    @RequestMapping(value = "/exportHalfYear", method = RequestMethod.GET)
    @FileCacheQuery(bizCode = "OR_PRODUCT_SALES_HALF_YEAR_E")
    public void exportHalfYear(@FileCacheQueryParameter ProductSaleStatisticsVo dto, HttpServletResponse response) throws IOException{
        long qS = System.currentTimeMillis();
        dto.initExportPage();
        ProductSaleStatisticsTableEntry tableEntry = queryProductSaleStatisticsListHalfYear(dto);
        long qE = System.currentTimeMillis();
        log.info("产品销售汇总-导出--查询时间=" + ((qE - qS) / 1000));
        //处理数据并导出
        dealDataToExport(response, tableEntry.getTableHeader(),tableEntry.getTableData(),false);
        long eE = System.currentTimeMillis();
        log.info("产品销售汇总-导出--excel处理时间="+ ( (eE -qE) /1000 )  );
    }

    @ApiOperation(value = "打印产品销售汇总-半年", notes = "打印产品销售汇总-半年")
    @PostMapping("/printHalfYear")
    public String printHalfYear(@RequestBody ProductSaleStatisticsVo vo){
        Long userId = FastThreadLocalUtil.getQY().getUserId();
        vo.setUserId(userId);
        if(vo.getFactoryId()==null){
            List<Long> factoryIds = userClient.selectUserFactoryIdList(SelectUserFactoryIdListIDTO.onlyDb(userId));
            if(CollectionUtils.isEmpty(factoryIds)){
//                return "fail";
            	QYAssert.isFalse("您没有该工厂权限!");
            }
            vo.setFactoryIds(factoryIds);
        }
        return productSaleStatisticsService.printHalfYear(vo);
    }


    //-----------产品销售汇总查询-半年---------结束----(列表/合计/导出/打印)---------------------------------------------------
    /**
     * 查询用户权限下的工厂id集合
     * @param factoryId
     * @return
     */
    @Nullable
    private List<Long> getFactoryIds(Long factoryId) {
        if(factoryId == null){
            Long userId = FastThreadLocalUtil.getQY().getUserId();
            List<Long> factoryIds = userClient.selectUserFactoryIdList(SelectUserFactoryIdListIDTO.onlyDb(userId));
            if(CollectionUtils.isNotEmpty(factoryIds)){
                return factoryIds;
            }
        }
        return null;
    }

    /**
     * 处理数据并导出
     * @param response
     * @param tableHeader
     * @param tableDataList
     * @throws IOException
     */
    private void dealDataToExport(HttpServletResponse response, List<List<String>> tableHeader,List<List<String>> tableDataList,Boolean isTaxRate) throws IOException {
        //tableDate
        if (SpringUtil.isNotEmpty(tableDataList)){
            List<String> commodityCodeList = tableDataList.stream().map(tableData -> tableData.get(2)).distinct().collect(Collectors.toList());
            Map<String, Commodity> map = commodityService.queryCommodityMap(commodityCodeList);
            initTableDate(tableDataList, map,isTaxRate);
        }
        //tableHeader
        List<List<String>> headerList = initTableHeader(tableHeader);
        if(isTaxRate) {
            headerList.add(Arrays.asList("商品税率", "商品税率"));
        }
        try {
            ExcelUtil.setFileNameAndHead(response, "产品销售汇总" + LocalDate.now().toString("yyyyMMdd"));
            EasyExcel.write(response.getOutputStream()).head(headerList).autoCloseStream(Boolean.FALSE).sheet("产品销售汇总")
                    .doWrite(tableDataList);

        }catch (Exception e){
            log.error("产品销售汇总导出错误", e);
            ExcelUtil.setExceptionResponse( response );
        }
    }


    /**
     * 处理表头
     * @param storeTypes
     * @return
     */
    private List<List<String>> proceedTableHeader(List<DictionaryODTO> storeTypes) {
        List<String> tableHeaderLineOne = new ArrayList<>();
        tableHeaderLineOne.add("工厂");
        tableHeaderLineOne.add("生产组");
        tableHeaderLineOne.add("商品编码");
        tableHeaderLineOne.add("商品名称");
        tableHeaderLineOne.add("数量小计");
        tableHeaderLineOne.add("金额小计");
        storeTypes.forEach(item -> tableHeaderLineOne.add(item.getOptionName()));
        List<String> tableHeaderLineTwo = new ArrayList<>();
        for (int i = 0; i < storeTypes.size(); i++) {
            tableHeaderLineTwo.add("数量");
            tableHeaderLineTwo.add("金额");
        }
        List<List<String>> tableHeader = new ArrayList<>();
        tableHeader.add(tableHeaderLineOne);
        tableHeader.add(tableHeaderLineTwo);
        return tableHeader;
    }

    /**
     * 处理表格的每一行数据
     * @param commodityCode
     * @param list
     * @param storeTypes
     * @return
     */
    private List<String> proceedTableRow(String commodityCode, List<ProductSaleStatisticsTempEntry> list, List<DictionaryODTO> storeTypes) {
        NumberFormat numberFormat = NumberFormat.getNumberInstance();
        numberFormat.setMaximumFractionDigits(2);
        numberFormat.setRoundingMode(RoundingMode.HALF_UP);
        numberFormat.setGroupingUsed(false);
        List<String> row = new ArrayList<>();
        ProductSaleStatisticsTempEntry entry = list.get(0);
        row.add(entry.getFactoryName());
        row.add(entry.getWorkshopName());
        row.add(commodityCode);
        row.add(entry.getCommodityName());
        BigDecimal totalNum = BigDecimal.ZERO;
        BigDecimal totalPrice = BigDecimal.ZERO;
        for (ProductSaleStatisticsTempEntry entity : list) {
            totalNum = totalNum.add(entity.getCommodityNum());
            totalPrice = totalPrice.add(entity.getTotalPrice());
        }
        row.add(NumberUtils.subZeroAndDot(numberFormat.format(totalNum)));
        row.add(NumberUtils.subZeroAndDot(numberFormat.format(totalPrice)));
        for (DictionaryODTO type : storeTypes) {
            // 是否有该类型的客户购买了商品
            boolean hasType = false;
            for (ProductSaleStatisticsTempEntry entity : list) {
                if (type.getId().equals(String.valueOf(entity.getStoreTypeId()))) {
                    row.add(NumberUtils.subZeroAndDot(entity.getCommodityNum() + ""));
                    row.add(NumberUtils.subZeroAndDot(entity.getTotalPrice() + ""));
                    hasType = true;
                    break;
                }
            }
            if (!hasType) {
                row.add("");
                row.add("");
            }
        }
        return row;
    }

    /***
     *
     * 供 清美助手小程序 采购模块下 产品销售汇总使用
     * @see //http://192.168.0.213/zentao/story-view-10417.html
     */
    @ApiOperation(value = "美助手小程序采购模块下产品销售汇总", notes = "美助手小程序采购模块下产品销售汇总")
    @ApiImplicitParam(name = "vo", value = "", required = true, paramType = "body", dataTypeClass = ProductSaleStatisticsCommodityInfoListVo.class)
    @PostMapping("/queryStatisticsCommodityInfoList")
    public PageInfo<ProductSaleStatisticsCommodityInfoListEntry> selectProductSaleStatisticsCommodityInfoListEntry(@RequestBody ProductSaleStatisticsCommodityInfoListVo vo){
        QYAssert.isTrue(StringUtils.isNotBlank(vo.getDeliveryDate()),"送货日期不为空，请选择！");
        checkDeliveryDate(vo.getDeliveryDate());
        TokenInfo tokenInfo = FastThreadLocalUtil.getQY();
        return productSaleStatisticsService.selectProductSaleStatisticsCommodityInfoListEntry(vo,tokenInfo.getUserId());
    }

    private void checkDeliveryDate(String deliveryDate){
        Calendar subCalendar = Calendar.getInstance();
        subCalendar.add(Calendar.DAY_OF_MONTH, -14);

        Calendar addCalendar = Calendar.getInstance();
        addCalendar.add(Calendar.DAY_OF_MONTH, 14);

        Date subDate = DateUtil.parseDate(DateUtil.getDateFormate(subCalendar.getTime(), "yyyy-MM-dd"), "yyyy-MM-dd");
        Date addDate = DateUtil.parseDate(DateUtil.getDateFormate(addCalendar.getTime(), "yyyy-MM-dd"), "yyyy-MM-dd");

        Date date =DateUtil.parseDate(deliveryDate,"yyyy-MM-dd");
        QYAssert.isTrue(date.compareTo(subDate) >= 0 && date.compareTo(addDate) <= 0, "送货日期支持查询最大范围【T-14~T+14】，请重新选择送货日期！");
    }

    /***
     * 送货列表数据
     * 供 清美助手小程序 采购模块下 产品销售汇总使用
     * @see //http://192.168.0.213/zentao/story-view-10417.html
     */
    @ApiOperation(value = "清美助手小程序采购模块下产品销售汇总 送货日期列表", notes = "清美助手小程序采购模块下产品销售汇总 送货日期列表")
    @GetMapping(value = "/queryDeliveryDateList")
    public List<ProductSaleStatisticsDeliveryDateListEntry> queryDeliveryDateList(){
        return productSaleStatisticsService.queryDeliveryDateList();
    }

}

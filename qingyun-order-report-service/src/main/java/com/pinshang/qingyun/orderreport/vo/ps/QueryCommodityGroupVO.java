package com.pinshang.qingyun.orderreport.vo.ps;

import com.pinshang.qingyun.base.page.Pagination;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @Version 0.1.0
 * @Date 2021/1/18 13:30
 * @Copyright © 2019-2021 qm
 * @Description -- 类说明
 **/
@Data
public class QueryCommodityGroupVO extends Pagination {

    @ApiModelProperty(value = "商品组编码/商品组名称", hidden = true)
    private String groupName;

    @ApiModelProperty(value = "商品id")
    private Long commodityId;

    @ApiModelProperty(value = "商品组状态:1-启用状态，0-停用状态")
    private Integer status;

    @ApiModelProperty(value = "商品组id")
    private Long commodityGroupId;

}

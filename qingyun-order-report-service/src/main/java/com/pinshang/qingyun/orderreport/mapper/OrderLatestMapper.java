package com.pinshang.qingyun.orderreport.mapper;

import com.pinshang.qingyun.base.mybatis.MyMapper;
import com.pinshang.qingyun.orderreport.dto.LatestCommoditySaleStatisticsIDTO;
import com.pinshang.qingyun.orderreport.dto.LatestCommoditySaleStatisticsODTO;
import com.pinshang.qingyun.orderreport.mapper.entry.LatestStoreTypeDetailEntry;
import com.pinshang.qingyun.orderreport.model.Order;
import com.pinshang.qingyun.orderreport.model.OrderLatest;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @date 2019/1/14 16:10.
 */
@Repository
public interface OrderLatestMapper extends MyMapper<OrderLatest> {

    int batchInsert(List<OrderLatest> orderList);

    List<LatestStoreTypeDetailEntry> queryLatestAndStoreTypeData(@Param("orderTime") Date orderTime);

    List<Order> queryOrderLatestByStoreIds(@Param("storeIds") Set<Long> storeIdList);

    List<OrderLatest> queryOrderLatestList(@Param("nowDate") Date nowDate);
    //大屏提供 销售前50
    List<LatestCommoditySaleStatisticsODTO> queryLatestCommoditySaleTopN(LatestCommoditySaleStatisticsIDTO idto);

}

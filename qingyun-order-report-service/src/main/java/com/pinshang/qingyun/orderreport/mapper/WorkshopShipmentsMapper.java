package com.pinshang.qingyun.orderreport.mapper;

import com.pinshang.qingyun.orderreport.mapper.entry.NonlocalShipmentsTempEntry;
import com.pinshang.qingyun.orderreport.mapper.entry.WorkshopShipmentsTempEntry;
import com.pinshang.qingyun.orderreport.vo.NonlocalShipmentsVo;
import com.pinshang.qingyun.orderreport.vo.OrderListVo;
import com.pinshang.qingyun.orderreport.vo.WorkshopVo;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2019/2/28 10:42.
 */
@Repository
public interface WorkshopShipmentsMapper {

    List<WorkshopVo> searchListByParams(Map<String, Object> sqlMap);

    List<OrderListVo> queryOrdersByOrderDateAndWorkshopId(Map<String, Object> params);

    //清美人APP ：查询生产组外地发货单列表
    List<NonlocalShipmentsTempEntry> queryListForApp(@Param("vo") NonlocalShipmentsVo vo, @Param("latestFlag") boolean latestFlag);

    //清美人APP ：查询生产组发货单列表,验证是否是生产组主任
    @Select("SELECT id FROM t_commodity WHERE commodity_workshop_director = #{directorCode}")
    List<Long> findDirectorIdsByCode(@Param("directorCode") String directorCode);

    List<WorkshopShipmentsTempEntry> findShipmentsList(@Param("orderDate") String orderDate,
                                                       @Param("lineGroupId") Long lineGroupId,
                                                       @Param("warehouseId") Long warehouseId,
                                                       @Param("directorCode") String directorCode,
                                                       @Param("deliveryTime") String deliveryTime,
                                                       @Param("deliveryBatch") Long deliveryBatch,
                                                       @Param("latestFlag") boolean latestFlag,
                                                       @Param("orderModeType") Integer orderModeType);
}

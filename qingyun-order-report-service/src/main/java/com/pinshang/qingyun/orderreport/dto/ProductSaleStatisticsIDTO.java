package com.pinshang.qingyun.orderreport.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.pinshang.qingyun.base.page.Pagination;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * 产品销售汇总
 */
@Data
public class ProductSaleStatisticsIDTO extends Pagination {

    @ApiModelProperty(value = "结账客户ID")
    private Long storeSettId;
    @ApiModelProperty(value = "结账客户IDS")
    private List<Long> storeSettIds;
    @ApiModelProperty(value = "结账客户，打印显示")
    private String storeSettName;

    @ApiModelProperty(value = "客户Id")
    private Long storeId;
    @ApiModelProperty(value = "客户IDS")
    private List<Long> storeIds;
    @ApiModelProperty(value = "客户编码")
    private String storeCode;

    @ApiModelProperty(value = "送货日期开始")
    @JsonFormat(pattern = "yyyy-MM-dd",timezone="GMT+8")
    private Date startDate;

    @ApiModelProperty(value = "送货日期结束")
    @JsonFormat(pattern = "yyyy-MM-dd",timezone="GMT+8")
    private Date endDate;

    @ApiModelProperty(value = "工厂ID")
    private Long factoryId;

    @ApiModelProperty(value = "工厂名称，打印显示")
    private String factoryName;

    @ApiModelProperty(value = "生产组ID")
    private Long workshopId;

    @ApiModelProperty(value = "生产组名称，打印显示")
    private String workshopName;

    @ApiModelProperty(value = "商品Id")
    private Long commodityId;

    @ApiModelProperty(value = "商品编码")
    private String commodityCode;

    @ApiModelProperty(value = "公司ID")
    private Long storeCompanyId;

    @ApiModelProperty(value = "公司名称，打印显示")
    private String storeCompanyName;

    @ApiModelProperty(value = "客户类型ID")
    private Long storeTypeId;

    @ApiModelProperty(value = "客户类型，打印显示")
    private String storeTypeName;

    @ApiModelProperty(value = "督导ID")
    private Long supervisorId;

    @ApiModelProperty(value = "督导，打印显示")
    private String supervisorName;

    @ApiModelProperty(value = "大区经理ID")
    private Long regionManagerId;

    @ApiModelProperty(value = "大区经理，打印显示")
    private String regionManagerName;

    @ApiModelProperty(value = "线路组ID")
    private Long lineGroupId;

    @ApiModelProperty(value = "线路组，打印显示")
    private String lineGroupName;

    @ApiModelProperty(value = "送货员ID")
    private Long deliveryManId;

    @ApiModelProperty(value = "送货员，打印显示")
    private String deliveryManName;

    @ApiModelProperty(value = "一级分类ID")
    private Long cateId1;

    @ApiModelProperty(value = "二级分类ID")
    private Long cateId2;

    @ApiModelProperty(value = "三级分类ID")
    private Long cateId3;

    @ApiModelProperty(value = "商品品类名称，打印显示")
    private String cateName;

    @ApiModelProperty(value = "操作员，打印需要用到")
    private Long userId;
    @ApiModelProperty(value = "公司id")
    private Long companyId;

}

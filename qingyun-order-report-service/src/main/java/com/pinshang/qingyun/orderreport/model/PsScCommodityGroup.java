package com.pinshang.qingyun.orderreport.model;

import java.io.Serializable;
import java.util.Date;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Id;
import javax.persistence.Table;

/**
 * t_ps_sc_commodity_group
 * <AUTHOR>
@Data
@Table(name = "t_ps_sc_commodity_group")
@AllArgsConstructor
@NoArgsConstructor
public class PsScCommodityGroup implements Serializable {
    /**
     * 主键
     */
    @Id
    private Long id;

    /**
     * 商品组名称
     */
    private String groupName;

    /**
     * 商品组编码
     */
    private String groupCode;

    /**
     * 商品数
     */
    private Long commodityQuantity;

    /**
     * 商品组状态:1-启用状态，0-停用状态
     */
    private Boolean status;

    /**
     * 创建人
     */
    private Long createId;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新人
     */
    private Long updateId;

    /**
     * 更新时间
     */
    private Date updateTime;

    public PsScCommodityGroup(String groupName, String groupCode, Long commodityQuantity, Boolean status, Long createId, Date createTime) {
        this.groupName = groupName;
        this.groupCode = groupCode;
        this.commodityQuantity = commodityQuantity;
        this.status = status;
        this.createId = createId;
        this.createTime = createTime;
    }

    private static final long serialVersionUID = 1L;
}
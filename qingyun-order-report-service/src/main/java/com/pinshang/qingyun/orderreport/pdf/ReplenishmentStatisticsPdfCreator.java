package com.pinshang.qingyun.orderreport.pdf;

import com.itextpdf.text.*;
import com.itextpdf.text.pdf.PdfPCell;
import com.itextpdf.text.pdf.PdfPTable;
import com.itextpdf.text.pdf.PdfWriter;
import com.pinshang.qingyun.orderreport.config.CustomerProperties;
import com.pinshang.qingyun.orderreport.mapper.entry.ReplenishmentStatisticsEntry;
import com.pinshang.qingyun.orderreport.util.ListUtils;
import com.pinshang.qingyun.orderreport.util.PdfUtils;
import com.pinshang.qingyun.orderreport.vo.ReplenishmentStatisticsVo;
import lombok.extern.slf4j.Slf4j;
import org.joda.time.DateTime;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.UUID;

/**
 * 补货数据汇总打印生成pdf
 */
@Component
@Slf4j
public class ReplenishmentStatisticsPdfCreator {

    private final CustomerProperties customerProperties;

    private static final int pageSize = 43;

    @Autowired
    public ReplenishmentStatisticsPdfCreator(CustomerProperties customerProperties) {
        this.customerProperties = customerProperties;
    }

    /**
     * 打印 PDF
     * @param dataList 要打印的数据
     * @return 是否打印成功.
     */
    public String create(List<ReplenishmentStatisticsEntry> dataList, ReplenishmentStatisticsVo vo) {
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String printTime = dateFormat.format(new Date());
        if (dataList == null) {
            log.error("要生成PDF的内容为空.");
            return null;
        }

        String pdfSave = customerProperties.getAbsoluteSavePath();
        String savePath = customerProperties.getSavePath();
        Date date = new DateTime().plusDays(1).toDate();
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");
        String dataDir = format.format(date);
        File file = new File(pdfSave + "/" + savePath + "/" + dataDir);
        if (!file.exists()) {
            boolean mkdirs = file.mkdirs();
            if (!mkdirs) {
                log.error("创建保存pdf的目录失败,请检查权限");
                return null;
            }
        }
        Document document = new Document();
        String fileName = extractFileName(vo);
        try {
            FileOutputStream out = new FileOutputStream(file.getAbsolutePath() + "/" + fileName);
            PdfWriter.getInstance(document, out);
            document.open();
            Font font = PdfUtils.chineseFont(11);
            PdfPTable title = proceedTableHeader(font, vo);
            List<List<ReplenishmentStatisticsEntry>> pageContent = ListUtils.splitList(dataList, pageSize);
            int page = 1;
            int totalPage = pageContent.size();
            for (List<ReplenishmentStatisticsEntry> content : pageContent) {
                document.add(title);
                float[] cellWidth = {80,110,60,260,100};
                PdfPTable table = new PdfPTable(cellWidth);
                table.setWidthPercentage(100);

                PdfPCell cell = new PdfPCell(new Phrase("工厂", font));
                table.addCell(cell);

                cell = new PdfPCell(new Phrase("生产组", font));
                table.addCell(cell);

                cell = new PdfPCell(new Phrase("商品编码", font));
                table.addCell(cell);

                cell = new PdfPCell(new Phrase("商品名称(规格)", font));
                table.addCell(cell);


                cell = new PdfPCell(new Phrase("合计", font));
                table.addCell(cell);
                for (ReplenishmentStatisticsEntry entry : content) {
                    PdfPCell factoryCell = new PdfPCell(new Phrase(entry.getCommodityFactoryName(), font));
                    factoryCell.setFixedHeight(16);
                    table.addCell(factoryCell);

                    PdfPCell workshopCell=new PdfPCell(new Phrase(entry.getCommodityWorkshopName(),font));
                    workshopCell.setFixedHeight(16);
                    table.addCell(workshopCell);


                    PdfPCell commodityCodeCell=new PdfPCell(new Phrase(entry.getCommodityCode(),font));
                    table.addCell(commodityCodeCell);

                    PdfPCell commodityNameSpecCell = new PdfPCell(new Phrase(String.format("%s%s%s%s",entry.getCommodityName(),"(",entry.getCommoditySpec(),")"), font));
                    commodityNameSpecCell.setFixedHeight(16);
                    table.addCell(commodityNameSpecCell);


                    Phrase phrase = new Phrase(entry.getTotalCommodityNum().toString(), font);
                    PdfPCell totalCell = new PdfPCell(phrase);
                    totalCell.setFixedHeight(16);
                    table.addCell(totalCell);
                }

                //表格bottom
                PdfPCell tableBottom = new PdfPCell(new Phrase("第 " + page + " 页, 共 " + totalPage + "页.                打印时间: " + printTime, font));
                tableBottom.setColspan(5);
                tableBottom.setBorder(0);
                table.addCell(tableBottom);
                document.add(table);
                page++;
                if (page <= totalPage) {
                    document.newPage();
                }
            }
            document.close();
        } catch (DocumentException | IOException e) {
            log.error("生成补货数据汇总PDF时异常:{}",e.getMessage());
            return null;
        }
        return String.format("%s%s%s",file.getAbsolutePath(),"/",fileName);
//        return file.getAbsolutePath() + fileName;
//        return savePath + "/" + dataDir + "/" + fileName;
    }

    /**
     * 生成表格头部信息
     * @param font 中文字体
     * @param vo pdf查询参数
     */
    private PdfPTable proceedTableHeader(Font font, ReplenishmentStatisticsVo vo) throws IOException, DocumentException {
        float[] cellWidth = { 80, 80 , 140};
        PdfPTable pdfPTable = new PdfPTable(cellWidth); //3列表格
        pdfPTable.setWidthPercentage(100);
        PdfPCell title = new PdfPCell(new Phrase("补货数据汇总", PdfUtils.chineseFont(18)));
        title.setColspan(3);
        title.setBorder(0);
        title.setFixedHeight(24);
        title.setHorizontalAlignment(Element.ALIGN_CENTER);
        title.setVerticalAlignment(Element.ALIGN_CENTER);
        pdfPTable.addCell(title);

        PdfPCell cell;
        String deliveryManName = vo.getDeliveryManName() == null ? "" : vo.getDeliveryManName();
        cell = new PdfPCell(new Phrase("送货员: " + deliveryManName, font));
        cell.setBorder(0);
        pdfPTable.addCell(cell);

        String deliveryWarehouseName = vo.getDeliveryWarehouseName() == null ? "" : vo.getDeliveryWarehouseName();
        cell = new PdfPCell(new Phrase("发货仓库: " + deliveryWarehouseName, font));
        cell.setBorder(0);
        pdfPTable.addCell(cell);

        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
        Date startDate = vo.getStartDate();
        Date endDate = vo.getEndDate();
        cell = new PdfPCell(new Phrase("送货日期: " + dateFormat.format(startDate) + " 至 " + dateFormat.format(endDate), font));
        cell.setBorder(0);
        pdfPTable.addCell(cell);

        return pdfPTable;
    }

    /**
     * 文件名
     * @param vo
     * @return
     */
    private String extractFileName(ReplenishmentStatisticsVo vo) {
        Date startDate = vo.getStartDate();
        Date endDate = vo.getEndDate();
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
        String fileName;
        if (startDate != null && endDate != null) {
            String start = dateFormat.format(startDate);
            String end = dateFormat.format(endDate);
            fileName = start + "_" + end;
        } else {
            Date date = new DateTime().plusDays(1).toDate();
            fileName = dateFormat.format(date);
        }
        return "/" + fileName + "_" + UUID.randomUUID().toString().substring(0,8) + ".pdf";
    }
}
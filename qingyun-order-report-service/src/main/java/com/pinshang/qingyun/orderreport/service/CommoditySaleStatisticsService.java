package com.pinshang.qingyun.orderreport.service;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.pinshang.qingyun.base.configure.expand.QYAssert;
import com.pinshang.qingyun.box.utils.CalendarTool;
import com.pinshang.qingyun.box.utils.DateUtil;
import com.pinshang.qingyun.box.utils.ListUtil;
import com.pinshang.qingyun.box.utils.SpringUtil;
import com.pinshang.qingyun.common.dto.DictionaryIDTO;
import com.pinshang.qingyun.common.dto.DictionaryODTO;
import com.pinshang.qingyun.common.service.DictionaryClient;
import com.pinshang.qingyun.orderreport.dto.LatestCommoditySaleStatisticsIDTO;
import com.pinshang.qingyun.orderreport.dto.LatestCommoditySaleStatisticsODTO;
import com.pinshang.qingyun.orderreport.mapper.*;
import com.pinshang.qingyun.orderreport.mapper.entry.CommoditySaleStatisticsAndItemLatestEntry;
import com.pinshang.qingyun.orderreport.mapper.entry.CommoditySaleStatisticsMonitorEntry;
import com.pinshang.qingyun.orderreport.mapper.entry.LatestStoreTypeDetailEntry;
import com.pinshang.qingyun.orderreport.model.*;
import com.pinshang.qingyun.orderreport.util.DateUtils;
import com.pinshang.qingyun.orderreport.vo.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.entity.Example;

import java.math.BigDecimal;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.util.*;
import java.util.stream.Collectors;


/**
 * 产品销售汇总（日报/月报）
 */
@Service
@Slf4j
public class CommoditySaleStatisticsService {

    @Autowired
    private CommoditySaleStatisticsMapper commoditySaleStatisticsMapper;
    @Autowired
    private CommoditySaleStatisticsItemMapper commoditySaleStatisticsItemMapper;
    @Autowired
    private CommoditySaleStatisticsMonthMapper commoditySaleStatisticsMonthMapper;
    @Autowired
    private CommoditySaleStatisticsMonthItemMapper commoditySaleStatisticsMonthItemMapper;
    @Autowired
    private OrderMapper orderMapper;
    @Autowired
    private OrderLatestMapper orderLatestMapper;
    @Autowired
    private CommoditySaleStatisticsSupervisorMapper commoditySaleStatisticsSupervisorMapper;
    @Autowired
    private CommoditySaleStatisticsMonthSupervisorMapper commoditySaleStatisticsMonthSupervisorMapper;

    @Autowired
    private DictionaryClient dictionaryClient;

    private static final Long TAXRATE_DICTIONARY_ID=8654395359641413279L;

    public Long getTaxrateDictionaryId() {
        return TAXRATE_DICTIONARY_ID;
    }

    /**
     * 查询汇总比较信息
     * 日报的原始数据来源：订单表
     * 月报的原始数据来源：商品日汇总表
     * @param vo
     * @return
     */
    public CommoditySaleStatisticsMonitorEntry commoditySaleStatisticsCompareInfo(CommoditySaleStatisticsMakeUpReqVo vo){
        QYAssert.notNull(vo,"请求参数不能为空");
        vo.setParamType(vo.getParamType()==null?1:vo.getParamType());
        if(StringUtils.isNotEmpty(vo.getOrderTime())){
            vo.setStartDate(null);
            vo.setEndDate(null);
            if(vo.getParamType()==2){
                //月报比较查询原始数据时，根据月份计算月的第一天和最后一天，然后根据计算得到的日期范围查询日汇总表。
                try {
                    List<String> dateList = DateUtils.startAndEndTime(vo.getOrderTime());
                    vo.setStartDate(dateList.get(0));
                    vo.setEndDate(dateList.get(1));
                } catch (ParseException e) {
                    e.printStackTrace();
                }
            }
        }
        CommoditySaleStatisticsMonitorEntry originalEntry;//原始数据
        CommoditySaleStatisticsMonitorEntry statisticsEntry;//销售汇总数据
        CommoditySaleStatisticsMonitorEntry supervisorEntry;//督导汇总数据
        CommoditySaleStatisticsMonitorEntry factoryEntry = null;//工厂汇总数据
        if(vo.getParamType()==1){//日报比较
            originalEntry = commoditySaleStatisticsMapper.queryOrderCommodityStatistics(vo);
            statisticsEntry = commoditySaleStatisticsMapper.queryCommodityStatisticsBase(vo,"t_tj_commodity_sale_statistics");
            supervisorEntry = commoditySaleStatisticsMapper.queryCommodityStatisticsBase(vo,"t_tj_commodity_sale_statistics_supervisor");
            factoryEntry = commoditySaleStatisticsMapper.queryCommodityStatisticsFactory(vo);
        }else{//月报比较
            originalEntry = commoditySaleStatisticsMapper.queryCommodityStatisticsBase(vo,"t_tj_commodity_sale_statistics");
            statisticsEntry = commoditySaleStatisticsMonthMapper.queryCommodityStatisticsBaseMonth(vo,"t_tj_commodity_sale_statistics_month");
            supervisorEntry = commoditySaleStatisticsMonthMapper.queryCommodityStatisticsBaseMonth(vo,"t_tj_commodity_sale_statistics_month_supervisor");
        }
        CommoditySaleStatisticsMonitorEntry resEntry = new CommoditySaleStatisticsMonitorEntry();
        resEntry.setTotalQuantity(originalEntry==null? BigDecimal.ZERO:originalEntry.getTotalQuantity());
        resEntry.setTotalAmount(originalEntry==null?BigDecimal.ZERO:originalEntry.getTotalAmount());
        resEntry.setTotalSaleQuantity(statisticsEntry==null?BigDecimal.ZERO:statisticsEntry.getTotalQuantity());
        resEntry.setTotalSaleAmount(statisticsEntry==null?BigDecimal.ZERO:statisticsEntry.getTotalAmount());
        resEntry.setTotalSupervisorQuantity(supervisorEntry==null?BigDecimal.ZERO:supervisorEntry.getTotalQuantity());
        resEntry.setTotalSupervisorAmount(supervisorEntry==null?BigDecimal.ZERO:supervisorEntry.getTotalAmount());
        if(vo.getParamType()==1){
            resEntry.setTotalFactoryQuantity(factoryEntry==null?BigDecimal.ZERO:factoryEntry.getTotalQuantity());
            resEntry.setTotalFactoryAmount(factoryEntry==null?BigDecimal.ZERO:factoryEntry.getTotalAmount());
        }
        return resEntry;
    }

    public PageInfo<CommoditySaleStatisticsRespVo> queryCommoditySaleStatistics(CommoditySaleStatisticsReqVo commoditySaleStatisticsReqVo){
        PageHelper.startPage(commoditySaleStatisticsReqVo.getPageNo(), commoditySaleStatisticsReqVo.getPageSize());
         /*** 督导为空时查询 */
        if (commoditySaleStatisticsReqVo.getSupervisorId() == null) {

            /***
             * 此SQL查询当条件选择公司查询：commoditySaleStatisticsReqVo.getCompanyId()!=null
             * SQL则按公司、已商品分组
             * 否则
             * SQL 则按商品分组
             */
            List<CommoditySaleStatisticsRespVo> CommoditySaleStatisticsList = commoditySaleStatisticsMapper.queryCommoditySaleData(commoditySaleStatisticsReqVo);
            if (CommoditySaleStatisticsList != null && CommoditySaleStatisticsList.size() > 0) {
                List<Long> commodityIds = CommoditySaleStatisticsList.stream().map(CommoditySaleStatisticsRespVo::getCommodityId).distinct().collect(Collectors.toList());
                int paramType = commoditySaleStatisticsReqVo.getParamType();
                CommoditySaleStatisticsItemReqVo vo = new CommoditySaleStatisticsItemReqVo();
                vo.setCommodityIds(commodityIds);
                vo.setParamType(paramType);
                vo.setCompanyId(commoditySaleStatisticsReqVo.getCompanyId());
                if (paramType == 1) {
                    vo.setStartDate(commoditySaleStatisticsReqVo.getStartDate());
                    vo.setEndDate(commoditySaleStatisticsReqVo.getEndDate());
                } else {
                    vo.setStartDateStr(commoditySaleStatisticsReqVo.getStartDateStr());
                    vo.setEndDateStr(commoditySaleStatisticsReqVo.getEndDateStr());
                }

                /***
                 * 此SQL查询当条件选择公司查询：commoditySaleStatisticsReqVo.getCompanyId()!=null
                 * SQL则按公司、已商品、客户类型分组
                 * 否则
                 * SQL 则按商品、客户类型分组
                 */
                List<CommoditySaleStatisticsItemRespVo> commoditySaleStatisticsItemlist = commoditySaleStatisticsItemMapper.queryCommoditySaleItemData(vo);

                /***
                 * 涉及到查询公司维度问题
                 * 当选择公司查询 则查询维度为：该公司下的商品   公司+商品
                 * 当未选择公司查询 则查询维度为商品
                 */
                Map<String, List<CommoditySaleStatisticsItemRespVo>> collect=null;
                /*** 选择公司查询 */
                if(commoditySaleStatisticsReqVo.getCompanyId()!=null){
                    /*** 根据公司、及商品进行分组 */
                     collect = commoditySaleStatisticsItemlist.parallelStream().collect(Collectors.groupingBy(item -> item.getCompanyId()+"_"+item.getCommodityId()));
                }else{
                    /*** 根据商品进行分组 */
                     collect = commoditySaleStatisticsItemlist.parallelStream().collect(Collectors.groupingBy(item -> item.getCommodityId().toString()));
                }

                for (CommoditySaleStatisticsRespVo commoditySaleStatisticsRespVo : CommoditySaleStatisticsList) {
                    Long commodityId = commoditySaleStatisticsRespVo.getCommodityId();
                    Long companyId = commoditySaleStatisticsRespVo.getCompanyId();
                    String key=commodityId.toString();
                    if(commoditySaleStatisticsReqVo.getCompanyId()!=null){
                        key=String.format("%s_%s",companyId,commodityId);
                    }
                    commoditySaleStatisticsRespVo.setCommoditySaleStatisticsItems(collect.get(key));
                }
            }
            return new PageInfo<>(CommoditySaleStatisticsList);
        }else {

            /***
             * 督导不为空，只查询该督导的销售商品
             *
             * 此SQL查询当条件选择公司查询：commoditySaleStatisticsReqVo.getCompanyId()!=null
             * SQL则按公司、已商品、督导分组
             * 否则
             * SQL 则按商品、督导分组
             */
            List<CommoditySaleStatisticsRespVo> commoditySaleStatisticsList = commoditySaleStatisticsSupervisorMapper.queryCommoditySaleDataBySupervisor(commoditySaleStatisticsReqVo);
            if (CollectionUtils.isNotEmpty(commoditySaleStatisticsList)){
                List<Long> commodityIds = commoditySaleStatisticsList.stream().map(CommoditySaleStatisticsRespVo::getCommodityId).distinct().collect(Collectors.toList());
                commoditySaleStatisticsReqVo.setCommodityIds(commodityIds);
                /***
                 * 查询该督导商品销售明细
                 * 此SQL查询当条件选择公司查询：commoditySaleStatisticsReqVo.getCompanyId()!=null
                 * SQL则按公司、已商品、督导分组
                 * 否则
                 * SQL 则按商品、督导分组
                 */
                List<CommoditySaleStatisticsItemRespVo> itemRespVos = commoditySaleStatisticsSupervisorMapper.queryCommoditySaleItemDataBySupervisor(commoditySaleStatisticsReqVo);
                Map<String, List<CommoditySaleStatisticsItemRespVo>> map=null;
                /***
                 * 涉及到查询公司维度问题
                 * 当选择公司查询 则查询维度为：该公司下的商品   公司+商品
                 * 当未选择公司查询 则查询维度为商品
                 */
                /*** 选择公司查询 */
                if(commoditySaleStatisticsReqVo.getCompanyId()!=null){
                    map = itemRespVos.stream().collect(Collectors.groupingBy(item -> item.getCompanyId()+"_"+item.getCommodityId()));
                }else{
                    map = itemRespVos.stream().collect(Collectors.groupingBy(item -> item.getCommodityId().toString()));
                }

                /*** 组装督导销售商品明细 */
                for(CommoditySaleStatisticsRespVo commoditySale: commoditySaleStatisticsList){
                    Long commodityId=commoditySale.getCommodityId();
                    Long companyId=commoditySale.getCompanyId();
                    String key=commodityId.toString();
                    if(commoditySaleStatisticsReqVo.getCompanyId()!=null){
                        key=String.format("%s_%s",companyId,commodityId);
                    }
                    commoditySale.setCommoditySaleStatisticsItems(map.get(key));
                }
            }
            return new PageInfo<>(commoditySaleStatisticsList);
        }
    }

    /**
     * 每日产品销售汇总-商品
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public void sumCommodityDataByDay(Date orderTime) {
        if (orderTime == null) {
            return;
        }
        /*** 根据orderTime获取订单商品 t_tj_order_latest 、t_tj_order_list_latest （公司、商品分组）*/
        List<LatestStoreTypeDetailEntry> latestStoreTypeDetails = orderLatestMapper.queryLatestAndStoreTypeData(orderTime);
        if (SpringUtil.isEmpty(latestStoreTypeDetails)) {
            log.info("is method sumCommodityDataByDay 订单统计（公司、商品分组）--t_tj_order_latest、t_tj_order_list_latest表，未查询到orderTime={}的订单数据!", orderTime);
            return;
        }

        /*** 持久化：t_tj_commodity_sale_statistics 表（已公司+商品维度存数据）*/
        this.commoditySaleStatisticsInsert(this.sumCommoditySaleStatisticsData(orderTime, latestStoreTypeDetails));

        /***根据orderTime获取订单商品数据（公司、商品、客户类型分组）*/
        List<CommoditySaleStatisticsItem> commoditySaleStatisticsItemByDate = commoditySaleStatisticsItemMapper.queryCommoditySaleItemForInsert(orderTime);
        if (SpringUtil.isEmpty(commoditySaleStatisticsItemByDate)) {
            log.info("is method sumCommodityDataByDay 订单统计（公司、商品、客户类型分组）--t_tj_order_latest、t_tj_order_list_latest、t_tj_commodity_sale_statistics表，未查询到orderTime={}的订单数据!", orderTime);
        }
        /***持久化：商品表明细 t_tj_commodity_sale_statistics_item（已公司+商品+客户类型维度存数据）*/
        this.commoditySaleStatisticsItemInsert(commoditySaleStatisticsItemByDate);

        /*** 产品销售汇总--商品表督导明细 录入, 已公司+商品+客户类型+督导维度存数据 */
        commoditySaleStatisticsSupervisorInsert(orderTime);
    }

    /**
     * 产品销售汇总--督导数据添加
     * t_tj_commodity_sale_statistics_supervisor （数据以公司、商品、客户类型、督导维度分组存储）
     * @param orderTime
     */
    private void commoditySaleStatisticsSupervisorInsert(Date orderTime){

        /*** 产品销售汇总--获取商品督导数据 t_tj_order_latest、t_tj_order_list_latest、t_store  （数据以公司、商品、客户类型、督导维度分组获取数据）*/
        List<CommoditySaleStatisticsSupervisor> supervisors = commoditySaleStatisticsSupervisorMapper.generateCommoditySaleStatisticsSupervisor(orderTime);

        /*** insert添加督导报表信息 t_tj_commodity_sale_statistics_supervisor */
        if (CollectionUtils.isNotEmpty(supervisors)){
            List<List<CommoditySaleStatisticsSupervisor>> subList = ListUtil.splitSubList(supervisors, 2000);
            subList.forEach(list->commoditySaleStatisticsSupervisorMapper.insertList(list));
        }
    }

    /**
     * 产品销售汇总--商品表督导明细 手动补偿
     * @param date
     */
    @Transactional(rollbackFor = Exception.class)
    public void commoditySaleStatisticsSupervisorMakeUp(Long commodityId,String date){
        //删除指定日数据
        Example example = new Example(CommoditySaleStatisticsSupervisor.class);
        Example.Criteria criteria = example.createCriteria().andEqualTo("orderTime",date);
        if (commodityId != null){
            criteria.andEqualTo("commodityId", commodityId);
        }
        commoditySaleStatisticsSupervisorMapper.deleteByExample(example);
        //补偿录入

        /***
         * 产品销售汇总--督导数据
         * 获取督导数据 维度为：公司、商品、客户类型、督导分组
         */

        List<CommoditySaleStatisticsSupervisor> supervisors = commoditySaleStatisticsSupervisorMapper.generateCommoditySaleStatisticsSupervisorMakeup(date, commodityId);

        //分批insert
        if (CollectionUtils.isNotEmpty(supervisors)){
            List<List<CommoditySaleStatisticsSupervisor>> subList = ListUtil.splitSubList(supervisors, 2000);
            subList.forEach(list->commoditySaleStatisticsSupervisorMapper.insertList(list));
        }

    }

    /***
     * 处理公司当天订单商品 数量、金额
     * 封装CommoditySaleStatistics 数据 用户持久化表数据 t_tj_commodity_sale_statistics
     * @param orderTime
     * @param latestStoreTypeDetail 根据公司、商品进行分组
     * @return
     */
    public List<CommoditySaleStatistics> sumCommoditySaleStatisticsData(Date orderTime,List<LatestStoreTypeDetailEntry> latestStoreTypeDetail) {
        List<CommoditySaleStatistics> commoditySaleStatisticsList = new ArrayList<CommoditySaleStatistics>();
        Date date = new Date();
        latestStoreTypeDetail.stream().collect(Collectors.groupingBy(item -> String.format("%s%s%s", item.getCompanyId(), "_", item.getCommodityId()))).forEach((k, v) -> {

            CommoditySaleStatistics commoditySaleStatistics = new CommoditySaleStatistics();
            String[] keys = k.split("_");
            commoditySaleStatistics.setCompanyId(Long.valueOf(keys[0]));
            commoditySaleStatistics.setCommodityId(Long.valueOf(keys[1]));
            commoditySaleStatistics.setOrderTime(orderTime);
            BigDecimal totalCommodityNum = BigDecimal.ZERO;
            BigDecimal totalPrice = BigDecimal.ZERO;
            for (LatestStoreTypeDetailEntry i : v) {
                if (i.getCommodityNum() != null) {
                    totalCommodityNum = totalCommodityNum.add(i.getCommodityNum());
                }
                if (i.getTotalPrice() != null) {
                    totalPrice = totalPrice.add(i.getTotalPrice());
                }
            }
            commoditySaleStatistics.setTotalAmount(totalPrice);
            commoditySaleStatistics.setTotalQuantity(totalCommodityNum);
            commoditySaleStatistics.setCreateTime(date);
            commoditySaleStatisticsList.add(commoditySaleStatistics);
        });
        return commoditySaleStatisticsList;
    }

    public List<CommoditySaleStatisticsItem> sumCommoditySaleStatisticsItemData(String orderTime,List<LatestStoreTypeDetailEntry> latestStoreTypeDetails){
        List<CommoditySaleStatisticsAndItemLatestEntry> commoditySaleStatisticsAndItemLatest = commoditySaleStatisticsMapper.queryCommoditySaleStatisticsAndItemLatestData(orderTime);
        List<CommoditySaleStatisticsItem> commoditySaleStatisticsItems = new ArrayList<>();
        if(commoditySaleStatisticsAndItemLatest.size() > 0){
            //key -> orderID, value -> storeTypeId
            Map<Long, Long> orderAndStoreTypeMap = latestStoreTypeDetails.stream().collect(Collectors.toMap(LatestStoreTypeDetailEntry::getId,LatestStoreTypeDetailEntry::getStoreTypeId,(key1 , key2)-> key2));
            //key -> storeTypeId, value -> List<CommoditySaleStatisticsAndItemLatestEntry>
            Map<Long,List<CommoditySaleStatisticsAndItemLatestEntry>> commoditySaleStatisticsAndItemLatestMap = new HashMap<>();
            //针对客户类型进行商品分组
            orderAndStoreTypeMap.forEach((orderAndStoreTypeMapKey, orderAndStoreTypeMapValue) -> {
                List<CommoditySaleStatisticsAndItemLatestEntry> commoditySaleStatisticsAndItemLatestTmp = commoditySaleStatisticsAndItemLatest.stream().filter(s -> orderAndStoreTypeMapKey.equals(s.getOrderId())).collect(Collectors.toList());
                List<CommoditySaleStatisticsAndItemLatestEntry> commoditySaleStatisticsAndItemLatestTmp2 = commoditySaleStatisticsAndItemLatestMap.get(orderAndStoreTypeMapValue);
                if(commoditySaleStatisticsAndItemLatestTmp2 != null){
                    commoditySaleStatisticsAndItemLatestTmp.addAll(commoditySaleStatisticsAndItemLatestTmp2);
                }
                commoditySaleStatisticsAndItemLatestMap.put(orderAndStoreTypeMapValue, commoditySaleStatisticsAndItemLatestTmp);
            });

            commoditySaleStatisticsAndItemLatestMap.forEach((key, value) -> {
                //根据商品Id进行分组
                Map<Long, List<CommoditySaleStatisticsAndItemLatestEntry>> collect = value.parallelStream().collect(Collectors.groupingBy(CommoditySaleStatisticsAndItemLatestEntry::getCommodityId));
                //统计某个商品的数量及金额
                collect.forEach((collectKey, collectValue) -> {
                    //统计某个商品的数量及金额
                    BigDecimal commodityNum = collectValue.stream().map(CommoditySaleStatisticsAndItemLatestEntry::getCommodityNum).reduce(BigDecimal.ZERO, BigDecimal::add);
                    BigDecimal totalPrice = collectValue.stream().map(CommoditySaleStatisticsAndItemLatestEntry::getTotalPrice).reduce(BigDecimal.ZERO, BigDecimal::add);
                    Long referId = collectValue.get(0).getReferId();

                    CommoditySaleStatisticsItem commoditySaleStatisticsItem = new CommoditySaleStatisticsItem();
                    commoditySaleStatisticsItem.setReferId(referId);
                    commoditySaleStatisticsItem.setStoreTypeId(key);
                    commoditySaleStatisticsItem.setTotalQuantity(commodityNum);
                    commoditySaleStatisticsItem.setTotalAmount(totalPrice);
                    commoditySaleStatisticsItems.add(commoditySaleStatisticsItem);
                });
            });
        }else{
            log.info("产品销售汇总--商品(t_tj_commodity_sale_statistics)表，数据获取失败！");
        }
        return commoditySaleStatisticsItems;
    }

    /**
     * 查询某月商品汇总数据
     * @return
     */
    public List<CommoditySaleStatistics> queryCommoditySaleStatisticsByMonthData(String startTime, String endTime, List<Long> commodityIds){
        return commoditySaleStatisticsMapper.queryCommoditySaleStatisticsByMonthData(startTime,endTime,commodityIds);
    }


    /**
     * 商品数据汇总（日）-批量插入主表信息
     * @return
     */
    public int commoditySaleStatisticsInsert(List<CommoditySaleStatistics> commoditySaleStatisticsAllByDate){
        return commoditySaleStatisticsMapper.batchInsert(commoditySaleStatisticsAllByDate);
    }

    /**
     * 公司商品数据汇总（日）-分批量插入主表信息
     * @return
     */
    public int companyCommoditySaleStatisticsInsert(List<CommoditySaleStatistics> commoditySaleStatisticsAllByDate){
            return commoditySaleStatisticsMapper.insertList(commoditySaleStatisticsAllByDate);
    }

    /**
     * 商品数据汇总（日）-批量插入子表信息
     * @return
     */
    public int commoditySaleStatisticsItemInsert( List<CommoditySaleStatisticsItem> commoditySaleStatisticsItemAllByDate){
        return commoditySaleStatisticsItemMapper.batchInsert(commoditySaleStatisticsItemAllByDate);
    }

    /**
     * 批量插入某月商品数据(主)
     * @return
     */
    public int batchInsertByMonth(List<CommoditySaleStatisticsMonth> list){
        int insertNum = commoditySaleStatisticsMonthMapper.batchInsert(list);
        QYAssert.isTrue(insertNum > 0,"月报数据汇总(t_tj_commodity_sale_statistics_month)批量插入失败！");
        return insertNum;
    }

    /**
     * 批量插入某月商品数据(详细)
     * @return
     */
    public int batchInsertByMonthItem(List<CommoditySaleStatisticsMonthItem> list){
        int insertNum = commoditySaleStatisticsMonthItemMapper.batchInsert(list);
        QYAssert.isTrue(insertNum > 0,"月报数据汇总（t_tj_commodity_sale_statistics_month_item）批量插入失败！");
        return insertNum;
    }

    /**
     * 查询某月商品汇总明细数据（根据商品ID）
     * @return
     */
    public List<CommoditySaleStatisticsItemRespVo> queryCommoditySaleItemByMonthData(List<Long> commodityIds,String startTime,String endTime){
        return commoditySaleStatisticsItemMapper.queryCommoditySaleItemByMonthData(startTime,endTime,commodityIds);
    }

    /**
     * 查询商品明细数据（根据商品ID）
     * @return
     */
    public List<CommoditySaleStatisticsItemRespVo> queryCommoditySaleItemData(List<Long> commodityIds, String orderTime){
        return orderMapper.queryCommoditySaleItemData(commodityIds,orderTime);
    }

    /**
     * 删除数据
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public void deleteDataByDay(Long commodityId, String orderTime){
        Example example = new Example(CommoditySaleStatistics.class);
        Example.Criteria criteria = example.createCriteria().andEqualTo("orderTime",orderTime);
        if (commodityId != null){
            criteria.andEqualTo("commodityId", commodityId);
        }
        List<CommoditySaleStatistics> commoditySaleStatisticss = this.commoditySaleStatisticsMapper.selectByExample(example);
        List<Long> ids = commoditySaleStatisticss.stream().map(CommoditySaleStatistics::getId).collect(Collectors.toList());
        if(ids.size()>0){
            //删除明细表（t_tj_commodity_sale_statistics_item）
            Example itemExample = new Example(CommoditySaleStatisticsItem.class);
            itemExample.createCriteria().andIn("referId",ids);
            commoditySaleStatisticsItemMapper.deleteByExample(itemExample);

            //删除主表（t_tj_commodity_sale_statistics）
            example.clear();
            example.createCriteria().andIn("id",ids);
            commoditySaleStatisticsMapper.deleteByExample(example);
        }
    }

    /**
     * 删除数据
     * @return
     */
    public void deleteDataByMonth(Long commodityId, String orderTime){
        Example example = new Example(CommoditySaleStatisticsMonth.class);
        Example.Criteria criteria = example.createCriteria().andEqualTo("saleMonth", orderTime);
        if (commodityId != null){
            criteria.andEqualTo("commodityId", commodityId);
        }
        List<CommoditySaleStatisticsMonth> commoditySaleStatisticsMonth = this.commoditySaleStatisticsMonthMapper.selectByExample(example);

        List<Long> ids = commoditySaleStatisticsMonth.stream().map(CommoditySaleStatisticsMonth::getId).collect(Collectors.toList());
        if(ids.size()>0){
            //删除明细表（t_tj_commodity_sale_statistics_month_item）
            Example itemExample = new Example(CommoditySaleStatisticsMonthItem.class);
            itemExample.createCriteria().andIn("referId",ids);
            commoditySaleStatisticsMonthItemMapper.deleteByExample(itemExample);

            //删除主表（t_tj_commodity_sale_statistics_month）
            example.clear();
            example.createCriteria().andIn("id",ids);
            commoditySaleStatisticsMonthMapper.deleteByExample(example);

        }
    }

    @Transactional(rollbackFor = Exception.class)
    public void monthData(List<Long> commodityIds,String orderTime) throws Exception{
        List<String> time = DateUtils.startAndEndTime(orderTime);
        String startTime = time.get(0);
        String endTime = time.get(1);
        /*** 查询t_tj_commodity_sale_statistics这个月的商品数据; 公司、商品分组*/
        List<CommoditySaleStatistics> commoditySaleStatisticsList = this.queryCommoditySaleStatisticsByMonthData(startTime,endTime,commodityIds);
        if(commoditySaleStatisticsList.size() > 0){
            this.sumCommodityDataByMonth(commoditySaleStatisticsList,startTime,endTime);
        }else{
            log.info("月报补偿"+startTime+"-->"+endTime+"商品汇总（t_tj_commodity_sale_statistics）数据查询为0！");
        }
    }

    /***
     * 处理月销售商品 督导数据 t_tj_commodity_sale_statistics_month_supervisor
     * @param commodityId
     * @param orderTime
     * @throws Exception
     */
    @Transactional(rollbackFor = Exception.class)
    public void commoditySaleStatisticsMonthSupervisor(Long commodityId,String orderTime) throws Exception{

        /*** 删除督导明细 t_tj_commodity_sale_statistics_month_supervisor */
        Example exampleSupervisor = new Example(CommoditySaleStatisticsMonthSupervisor.class);
        Example.Criteria criteria = exampleSupervisor.createCriteria().andEqualTo("saleMonth",orderTime);
        if (commodityId != null){
            criteria.andEqualTo("commodityId",commodityId);
        }
        commoditySaleStatisticsMonthSupervisorMapper.deleteByExample(exampleSupervisor);

        /*** 产品销售汇总--月报表督导明细 录入 */
        List<String> time = DateUtils.startAndEndTime(orderTime);
        String startTime = time.get(0);
        String endTime = time.get(1);
        /*** 处理月督导报表数据 */
        commoditySaleStatisticsMonthSupervisorInsert(startTime,endTime,commodityId);
    }


    @Transactional(rollbackFor = Exception.class)
    public void monthTiming(List<Long> commodityIds)throws Exception{
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM");
        //获取前一个月的第一天和最后一天
        List<String> time = DateUtils.startAndEndTime(format.format(CalendarTool.getPreviousDate(new Date())));
        String startTime = time.get(0);
        String endTime = time.get(1);
        //查询该月商品汇总数据
        List<CommoditySaleStatistics> commoditySaleStatisticsList = this.queryCommoditySaleStatisticsByMonthData(startTime,endTime,commodityIds);
        if(commoditySaleStatisticsList.size()>0){
            this.sumCommodityDataByMonth(commoditySaleStatisticsList,startTime,endTime);
        }else{
            log.error("月报定时"+startTime+"-->"+endTime+"商品汇总（t_tj_commodity_sale_statistics）数据查询失败！");
        }


    }


    /***
     * 封装t_tj_commodity_sale_statistics、t_tj_commodity_sale_statistics_item的数据
     * 以及添加数据 执行持久化操作t_tj_commodity_sale_statistics、t_tj_commodity_sale_statistics_item
     * @param commoditySaleItemData  集合数据的 维度是已公司、商品、客户类型分组
     * @param orderTime
     * @throws Exception
     */
    @Transactional(rollbackFor = Exception.class)
    public void sumCommoditySaleStatisticsMakeUpByDay(List<CommoditySaleStatisticsItemRespVo> commoditySaleItemData, String orderTime) throws Exception {
        /*** 封装t_tj_commodity_sale_statistics销售数据 便于t_tj_commodity_sale_statistics 添加 */
        List<CommoditySaleStatistics> commoditySaleStatisticss = this.sumCommoditySaleStatisticsMakeUpData(orderTime,commoditySaleItemData);
        /***
         * 添加数据t_tj_commodity_sale_statistics
         */
        int insertNum = commoditySaleStatisticss.size() > 0 ? this.commoditySaleStatisticsInsert(commoditySaleStatisticss) : 0;
        if(insertNum > 0){
            /*** 封装t_tj_commodity_sale_statistics_item明细销售数据  便于t_tj_commodity_sale_statistics_item 添加*/
            List<CommoditySaleStatisticsItem> CommoditySaleStatisticsItems = this.sumCommoditySaleStatisticsItemData(commoditySaleItemData,commoditySaleStatisticss);
            /***
             * 添加明细t_tj_commodity_sale_statistics_item
             */
            int insertItemNum = commoditySaleStatisticss.size() > 0 ? this.commoditySaleStatisticsItemInsert(CommoditySaleStatisticsItems) : 0;
            if(insertItemNum <= 0){
                log.error("产品销售汇总-商品(t_tj_commodity_sale_statistics_item)表，"+orderTime+"数据录入失败!");
            }
        }else{
            log.error("产品销售汇总-商品(t_tj_commodity_sale_statistics)表，"+orderTime+"数据录入失败!");
        }
    }

    public List<CommoditySaleStatistics> sumCommoditySaleStatisticsMakeUpData(String orderTime,List<CommoditySaleStatisticsItemRespVo> commoditySaleItemData) throws Exception {
        List<LatestStoreTypeDetailEntry> latestStoreTypeDetail = new ArrayList<>();
        for(CommoditySaleStatisticsItemRespVo commoditySaleStatisticsItemRespVo: commoditySaleItemData){
            LatestStoreTypeDetailEntry itemLatestEntry = new LatestStoreTypeDetailEntry();
            itemLatestEntry.setCompanyId(commoditySaleStatisticsItemRespVo.getCompanyId());
            itemLatestEntry.setCommodityId(commoditySaleStatisticsItemRespVo.getCommodityId());
            itemLatestEntry.setCommodityNum(commoditySaleStatisticsItemRespVo.getTotalQuantity());
            itemLatestEntry.setTotalPrice(commoditySaleStatisticsItemRespVo.getTotalAmount());
            latestStoreTypeDetail.add(itemLatestEntry);
        }
        Date date = new SimpleDateFormat("yyyy-MM-dd").parse(orderTime);
        return this.sumCommoditySaleStatisticsData(date,latestStoreTypeDetail);
    }

    /***
     * 封装t_tj_commodity_sale_statistics_item表的数据
     * @param commoditySaleItemData 集合数据维度为： 公司、商品,客户类型分组
     * @param commoditySaleStatisticss
     * @return
     * @throws Exception
     */
    public List<CommoditySaleStatisticsItem> sumCommoditySaleStatisticsItemData(List<CommoditySaleStatisticsItemRespVo> commoditySaleItemData,List<CommoditySaleStatistics> commoditySaleStatisticss) throws Exception{
        List<CommoditySaleStatisticsItem> commoditySaleStatisticsItems = new ArrayList<>();
        /*** key -> 公司id+商品id 分组 companyId+commodityId , value -> referId = t_tj_commodity_sale_statistics主键id*/
        Map<String, Long> orderAndCommodityIdMap = commoditySaleStatisticss.stream().collect(Collectors.toMap(k -> k.getCompanyId()+"_"+k.getCommodityId(),v -> v.getId()));
        /*** 根据客户类型进行分组 */
        Map<Long, List<CommoditySaleStatisticsItemRespVo>> storeTypeKeyMap = commoditySaleItemData.parallelStream().collect(Collectors.groupingBy(CommoditySaleStatisticsItemRespVo::getStoreTypeId));
        storeTypeKeyMap.forEach((key, value) -> {
            /*** 根据公司id+商品ID进行分组 */
            Map<String, List<CommoditySaleStatisticsItemRespVo>> commodityKeyMap = value.parallelStream().collect(Collectors.groupingBy(i-> i.getCompanyId()+"_"+i.getCommodityId()));
            /*** 根据数据维度为 公司下的商品对应的客户类型统计商品的数量及金额 */
            commodityKeyMap.forEach((collectKey, collectValue) -> {
                /*** 商品的数量及金额 */
                BigDecimal commodityNum = collectValue.stream().map(CommoditySaleStatisticsItemRespVo::getTotalQuantity).reduce(BigDecimal.ZERO, BigDecimal::add);
                BigDecimal totalPrice = collectValue.stream().map(CommoditySaleStatisticsItemRespVo::getTotalAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
                Long referId = orderAndCommodityIdMap.get(collectKey);

                CommoditySaleStatisticsItem commoditySaleStatisticsItem = new CommoditySaleStatisticsItem();
                commoditySaleStatisticsItem.setReferId(referId);
                commoditySaleStatisticsItem.setStoreTypeId(key);
                commoditySaleStatisticsItem.setTotalQuantity(commodityNum);
                commoditySaleStatisticsItem.setTotalAmount(totalPrice);
                commoditySaleStatisticsItems.add(commoditySaleStatisticsItem);
            });
        });
        return commoditySaleStatisticsItems;
    }

    /***
     * 存月的销售商品数据
     * 主表t_tj_commodity_sale_statistics_month  公司、商品分组 维度存数据
     * 明细 t_tj_commodity_sale_statistics_month_item 公司、商品、客户类型分组 维度存数据
     * @param commoditySaleStatisticsList  （公司、商品分组 的数据）
     * @param startTime
     * @param endTime
     * @throws Exception
     */
    public void sumCommodityDataByMonth(List<CommoditySaleStatistics> commoditySaleStatisticsList,String startTime,String endTime) throws Exception{
        /*** 产品销售汇总--月份 insert */
        List<CommoditySaleStatisticsMonth> list = new ArrayList<>();
        for(CommoditySaleStatistics commoditySaleStatistics: commoditySaleStatisticsList){
            CommoditySaleStatisticsMonth commoditySaleStatisticsMonth = new CommoditySaleStatisticsMonth();
            commoditySaleStatisticsMonth.setCompanyId(commoditySaleStatistics.getCompanyId());
            commoditySaleStatisticsMonth.setCommodityId(commoditySaleStatistics.getCommodityId());
            commoditySaleStatisticsMonth.setSaleMonth(commoditySaleStatistics.getOrderTimeStr());
            commoditySaleStatisticsMonth.setTotalQuantity(commoditySaleStatistics.getTotalQuantity());
            commoditySaleStatisticsMonth.setTotalAmount(commoditySaleStatistics.getTotalAmount());
            list.add(commoditySaleStatisticsMonth);
        }
        /***
         * 添加主表t_tj_commodity_sale_statistics_month
         * 公司、商品分组 维度存数据
         */
        this.batchInsertByMonth(list);

        /*** 产品销售汇总--月份表明细 */
        Map<String, Long> commodityMapId = list.stream().collect(Collectors.toMap(k -> k.getCompanyId()+"_"+k.getCommodityId() ,v -> v.getId()));
        List<Long> commodityIds = list.stream().map(CommoditySaleStatisticsMonth::getCommodityId).collect(Collectors.toList());
        /*** 获取月明细数据; 公司、商品、客户类型分组 */
        List<CommoditySaleStatisticsItemRespVo> commoditySaleStatisticsItems = this.queryCommoditySaleItemByMonthData(commodityIds,startTime,endTime);
        if(commoditySaleStatisticsItems.size() > 0){
            List<CommoditySaleStatisticsMonthItem> commoditySaleStatisticsMonthItems = new ArrayList<>(commoditySaleStatisticsItems.size());
            commoditySaleStatisticsItems.forEach(commoditySaleStatisticsItemRespVo ->{
                CommoditySaleStatisticsMonthItem commoditySaleStatisticsMonthItem = new CommoditySaleStatisticsMonthItem();
                commoditySaleStatisticsMonthItem.setReferId(commodityMapId.get(commoditySaleStatisticsItemRespVo.getCompanyId()+"_"+commoditySaleStatisticsItemRespVo.getCommodityId()));
                commoditySaleStatisticsMonthItem.setStoreTypeId(commoditySaleStatisticsItemRespVo.getStoreTypeId());
                commoditySaleStatisticsMonthItem.setTotalQuantity(commoditySaleStatisticsItemRespVo.getTotalQuantity());
                commoditySaleStatisticsMonthItem.setTotalAmount(commoditySaleStatisticsItemRespVo.getTotalAmount());
                commoditySaleStatisticsMonthItems.add(commoditySaleStatisticsMonthItem);
            });
            /***
             *添加明细 t_tj_commodity_sale_statistics_month_item
             * 公司、商品、客户类型分组 维度存数据
             */
            this.batchInsertByMonthItem(commoditySaleStatisticsMonthItems);
        }else{
            log.info("日报"+startTime+"-->"+endTime+"商品汇总明细（t_tj_commodity_sale_statistics_item）数据查询为0！");
        }
    }

    /**
     * 产品销售汇总--月报表督导数据添加；数据以公司、商品、客户类型、督导维度存
     * @param startTime
     * @param endTime
     * @param commodityId
     */
    @Transactional(rollbackFor = Exception.class)
    public void commoditySaleStatisticsMonthSupervisorInsert(String startTime, String endTime, Long commodityId){
        /*** 查询t_tj_commodity_sale_statistics_supervisor数据汇总出月报表督导数据 ; 数据以公司、商品、客户类型、督导维度分组*/
        List<CommoditySaleStatisticsMonthSupervisor> monthSupervisors = commoditySaleStatisticsMonthSupervisorMapper
                .generateCommoditySaleStatisticsMonthSupervisor(startTime, endTime, commodityId);

        /*** 添加月督导信息 t_tj_commodity_sale_statistics_month_supervisor*/
        if (CollectionUtils.isNotEmpty(monthSupervisors)) {
            this.forInsertMonthSupervisors(monthSupervisors,50000); //改成批量插入
        }else {
            log.error(startTime+"-->"+endTime+"产品销售汇总--月报表督导明细 数据查询为0");
        }
    }

    /***
     * 批量插入
     * @param monthSupervisorList
     */
    @Transactional(rollbackFor = Exception.class)
    public void forInsertMonthSupervisors(List<CommoditySaleStatisticsMonthSupervisor> monthSupervisorList,int num) {

        if (SpringUtil.isEmpty(monthSupervisorList)) {
            return;
        }
        if(monthSupervisorList.size() <= num){
            commoditySaleStatisticsMonthSupervisorMapper.insertList(monthSupervisorList);
            return;
        }
        List<CommoditySaleStatisticsMonthSupervisor> execList = new ArrayList<>();
        for (CommoditySaleStatisticsMonthSupervisor s : monthSupervisorList) {
            if (execList.size() == num) {
                commoditySaleStatisticsMonthSupervisorMapper.insertList(execList);
                execList.clear();
            }
            execList.add(s);
        }

        if (SpringUtil.isNotEmpty(execList)) {
            commoditySaleStatisticsMonthSupervisorMapper.insertList(execList);
            execList.clear();
        }

    }

    /****
     * 修改统计表：
     * t_tj_order、t_tj_order_latest
     * 场景：
     * 修改客户的所属公司后，需将改该客户下的统计订单修改成对应所属公司
     * 修改订单的条件为：客户下的订单、订单送货时间>当前时间、订单所属公司不等于客户所属公司
     * @param storeId
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public Integer modifyOrderCompanyByStore(Long storeId,Long companyId){

        /*** 获取当前时间 */
        LocalDate localDate=LocalDate.now();
        Date now=DateUtil.parseDate(localDate.toString(),"yyyy-MM-dd");

        /***
         * 获取t_tj_order_latest
         */
        Example queryOrderLatestByExample=new Example(OrderLatest.class);
        queryOrderLatestByExample.createCriteria().andEqualTo("storeId",storeId).andGreaterThan("orderTime",now).andNotEqualTo("companyId",companyId);
        List<OrderLatest> orderLatestList=orderLatestMapper.selectByExample(queryOrderLatestByExample);
        if(orderLatestList!=null && orderLatestList.size()>0){
            List<Long> orderIds= orderLatestList.stream().map(OrderLatest::getId).collect(Collectors.toList());

            /*** 修改t_tj_order_latest 的所属公司*/
            Example orderLatestByExample=new Example(OrderLatest.class);
            orderLatestByExample.createCriteria().andIn("id",orderIds);
            OrderLatest orderLatest=new OrderLatest();
            orderLatest.setCompanyId(companyId);
             orderLatestMapper.updateByExampleSelective(orderLatest,orderLatestByExample);

            /*** 修改t_tj_order 的所属公司*/
            Example orderByExample=new Example(Order.class);
            orderByExample.createCriteria().andIn("id",orderIds);
            Order order=new Order();
            order.setCompanyId(companyId);
             orderMapper.updateByExampleSelective(order,orderByExample);
        }
        return 1;
    }



    /***
     * 字典获取商品税率
     * @param taxRateDictionaryId
     * @return
     */
    public Map<String,String> getCommdityTaxRateValue(Long taxRateDictionaryId) {
        DictionaryIDTO dictionaryParam = new DictionaryIDTO();
        dictionaryParam.setDictionaryId(taxRateDictionaryId);
        List<DictionaryODTO> taxRateDictionaryList = dictionaryClient.getDictionaryList(dictionaryParam);
        if (SpringUtil.isNotEmpty(taxRateDictionaryList)) {
            return taxRateDictionaryList.stream().collect(Collectors.toMap(k-> k.getId().trim(), DictionaryODTO::getOptionValue));
        }
        return new HashMap<String,String>();
    }


    public List<LatestCommoditySaleStatisticsODTO> queryLatestCommoditySaleTopN(LatestCommoditySaleStatisticsIDTO idto){
        QYAssert.notNull(idto.getOrderTime(), "送货日期不可以为空");
        return orderLatestMapper.queryLatestCommoditySaleTopN(idto);
    }
}

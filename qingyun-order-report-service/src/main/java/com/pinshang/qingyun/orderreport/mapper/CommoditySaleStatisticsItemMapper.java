package com.pinshang.qingyun.orderreport.mapper;

import com.pinshang.qingyun.base.mybatis.MyMapper;
import com.pinshang.qingyun.orderreport.model.CommoditySaleStatisticsItem;
import com.pinshang.qingyun.orderreport.vo.CommoditySaleStatisticsItemReqVo;
import com.pinshang.qingyun.orderreport.vo.CommoditySaleStatisticsItemRespVo;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;

@Repository
public interface CommoditySaleStatisticsItemMapper extends MyMapper<CommoditySaleStatisticsItem> {

    int batchInsert(List<CommoditySaleStatisticsItem> commoditySaleStatisticsItems);

    List<CommoditySaleStatisticsItemRespVo> queryCommoditySaleItemData(@Param("vo") CommoditySaleStatisticsItemReqVo vo);

    List<CommoditySaleStatisticsItemRespVo> queryCommoditySaleItemByMonthData(@Param("startTime") String startTime,@Param("endTime") String endTime, @Param("list") List<Long> commodityIds);

    List<CommoditySaleStatisticsItem> queryCommoditySaleItemForInsert(@Param("orderTime") Date orderTime);
}

package com.pinshang.qingyun.orderreport.vo.ps;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @Version 0.1.0
 * @Date 2021/1/21 10:19
 * @Copyright © 2019-2021 qm
 * @Description -- 类说明
 **/
@Data
public class AddCommodityGroupVO {
    @ApiModelProperty(value = "商品组名称", example = "清美商品组", required = true)
    private String groupName;

    @ApiModelProperty(value = "创建人id", example = "123456", required = true, hidden = true)
    private Long createId;

}

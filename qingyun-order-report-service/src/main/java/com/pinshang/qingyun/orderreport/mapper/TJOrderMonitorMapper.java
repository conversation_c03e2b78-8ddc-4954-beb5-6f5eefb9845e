package com.pinshang.qingyun.orderreport.mapper;

import com.pinshang.qingyun.orderreport.mapper.entry.OrderStatisticsEntry;
import com.pinshang.qingyun.orderreport.model.Order;
import com.pinshang.qingyun.orderreport.vo.TJOrderMonitorVo;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface TJOrderMonitorMapper {

	//查询订单统计信息，订单比较
    OrderStatisticsEntry queryOrderStatisticsInfo(@Param("list") List<String> orderCodeList, @Param("vo") TJOrderMonitorVo vo);

    //查询订单编号，用于计算差异订单
    List<Order> queryOrderList(@Param("list") List<String> orderCodeList, @Param("vo") TJOrderMonitorVo vo, @Param("latestFlag") boolean latestFlag);

    //查询最新的订单统计信息，订单比较
    OrderStatisticsEntry queryOrderStatisticsInfoLatest(@Param("list") List<String> orderCodeList, @Param("vo") TJOrderMonitorVo vo);

}


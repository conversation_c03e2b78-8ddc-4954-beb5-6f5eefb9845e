package com.pinshang.qingyun.orderreport.config;

import com.pinshang.qingyun.base.interceptor.QYServiceInterceptor;
import com.pinshang.qingyun.base.interceptor.RepeatRetryServiceInterceptor;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.handler.HandlerInterceptorAdapter;

import javax.annotation.Resource;

/**
 * Created by weican on 2018-11-14.
 */
@Configuration
public class OrderReportConfiguration {
    @Resource
    private RedissonClient redissonClient;
//    @Value("${spring.application.name:unknown}")
//    private String applicationName;
//    @Bean("repeatRetryServiceInterceptor")
//    public HandlerInterceptorAdapter repeatRetryServiceInterceptor(){
//        return new RepeatRetryServiceInterceptor(redissonClient,applicationName);
//    }


    @Bean("qyServiceInterceptor")
    public HandlerInterceptorAdapter qyServiceInterceptor(){
        return new QYServiceInterceptor(redissonClient);
    }
}

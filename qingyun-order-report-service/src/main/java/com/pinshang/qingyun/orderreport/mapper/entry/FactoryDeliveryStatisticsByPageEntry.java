package com.pinshang.qingyun.orderreport.mapper.entry;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/9/2
 */
@Data
public class FactoryDeliveryStatisticsByPageEntry {

    @JsonIgnore
    private String id;

    /** 线路名称 **/
    private String lineName;

    /** 送货员名称 **/
    private String deliverymanName;

    /** 工厂名称 **/
    private String factoryName;

    /** 金额小计 **/
    private BigDecimal totalAmount;

    List<FactoryDeliveryStatisticsItemByPageEntry> items;

}

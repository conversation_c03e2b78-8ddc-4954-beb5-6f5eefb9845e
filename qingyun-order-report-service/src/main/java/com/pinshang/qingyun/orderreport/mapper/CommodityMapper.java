package com.pinshang.qingyun.orderreport.mapper;

import com.pinshang.qingyun.base.mybatis.MyMapper;
import com.pinshang.qingyun.orderreport.model.Commodity;
import com.pinshang.qingyun.orderreport.vo.WorkshopVo;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2019/2/28 18:06.
 */
@Repository
public interface CommodityMapper extends MyMapper<Commodity> {

    @Select("SELECT commodity_workshop_id AS id ,commodity_workshop_name AS workshopName FROM t_commodity WHERE commodity_workshop_id=#{workshopId} LIMIT 1")
    WorkshopVo selectWorkshopByWorkshopId(@Param("workshopId") Long workshopId);

    @Select("SELECT id FROM t_commodity")
    List<Long> queryCommodityIds();

}

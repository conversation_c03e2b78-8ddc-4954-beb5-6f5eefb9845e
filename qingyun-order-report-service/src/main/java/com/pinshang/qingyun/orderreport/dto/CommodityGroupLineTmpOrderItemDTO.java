package com.pinshang.qingyun.orderreport.dto;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.pinshang.qingyun.box.utils.BeanCloneUtils;
import com.pinshang.qingyun.order.dto.shop.TjPreOrderODTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;


/**
 * 商品组打印预览--订单明细
 */
@Data
public class CommodityGroupLineTmpOrderItemDTO {

    private Long orderId;
    private Long storeId;
    private String storeName;
    private Long commodityId;
    private String commodityName;
    private String commoditySpec;
    private BigDecimal expectQuantity;

    public static CommodityGroupLineTmpOrderItemDTO convert(TjPreOrderODTO preOrderODTO){
        CommodityGroupLineTmpOrderItemDTO itemDTO = BeanCloneUtils.copyTo(preOrderODTO,CommodityGroupLineTmpOrderItemDTO.class);
        itemDTO.setExpectQuantity(preOrderODTO.getRequireQuantity());
        return itemDTO;
    }


}

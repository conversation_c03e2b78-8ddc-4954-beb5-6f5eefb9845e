package com.pinshang.qingyun.orderreport.service;

import com.pinshang.qingyun.box.utils.SpringUtil;
import com.pinshang.qingyun.order.dto.orderStatistics.OrderMirrorSyncODTO;
import com.pinshang.qingyun.orderreport.mapper.OrderLatestMapper;
import com.pinshang.qingyun.orderreport.mapper.OrderMirrorMapper;
import com.pinshang.qingyun.orderreport.model.Order;
import com.pinshang.qingyun.orderreport.model.OrderMirror;
import com.pinshang.qingyun.product.dto.sync.StoreODTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.entity.Example;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 订单落地数据
 */
@Slf4j
@Service
public class TJOrderMirrorService {

    @Autowired
    private OrderMirrorMapper orderMirrorMapper;
    @Autowired
    private OrderLatestMapper orderLatestMapper;

    /**
     * 同步客户时，修改客户明天及以后的订单落地数据
     * @param storeODTOList
     */
    @Transactional(rollbackFor = Exception.class)
    public void syncStoreOrderMirror(List<StoreODTO> storeODTOList){
        if (SpringUtil.isEmpty(storeODTOList)) {
            return;
        }
        Map<Long,List<StoreODTO>> storeMap = storeODTOList.stream().collect(Collectors.groupingBy(StoreODTO::getId));
        List<Order> orderList = orderLatestMapper.queryOrderLatestByStoreIds(storeMap.keySet());
        if(SpringUtil.isEmpty(orderList)) {
            return;
        }
        List<OrderMirror> mirrorList = new ArrayList<>();
        orderList.forEach(order->{
            if(SpringUtil.isNotEmpty(storeMap.get(order.getStoreId()))){
                OrderMirror mirror = OrderMirror.syncConvert(storeMap.get(order.getStoreId()).get(0),order.getId());
                mirrorList.add(mirror);
            }
        });

        List<Long> orderIds = orderList.stream().map(Order::getId).collect(Collectors.toList());
        this.insertOrUpdateOrderMirror(orderIds,mirrorList);
    }

    /**
     * 订单落地数据补偿
     * @param orderIds
     */
    @Transactional(rollbackFor = Exception.class)
    public void syncTjOrderMirror(List<Long> orderIds){
        if (SpringUtil.isEmpty(orderIds)) {
            return;
        }
        List<OrderMirror> mirrorList = orderMirrorMapper.queryOrderMirrorEntry(orderIds);
        this.insertOrUpdateOrderMirror(orderIds,mirrorList);
    }

    public void insertOrUpdateOrderMirror(List<Long> orderIds,List<OrderMirror> mirrorList){
        if(SpringUtil.isEmpty(mirrorList)){
            return;
        }
        Example example = new Example(OrderMirror.class);
        example.createCriteria().andIn("orderId",orderIds);
        List<OrderMirror> exsitMirrors = orderMirrorMapper.selectByExample(example);
        if(SpringUtil.isEmpty(exsitMirrors)){
            orderMirrorMapper.insertList(mirrorList);
        }else{
            List<Long> exsitMirrorOrderIds = exsitMirrors.stream().map(OrderMirror::getOrderId).collect(Collectors.toList());
            List<OrderMirror> addMirrorList = mirrorList.stream().filter(item->!exsitMirrorOrderIds.contains(item.getOrderId())).collect(Collectors.toList());
            List<OrderMirror> updateMirrorList = mirrorList.stream().filter(item->exsitMirrorOrderIds.contains(item.getOrderId())).collect(Collectors.toList());
            if(SpringUtil.isNotEmpty(addMirrorList)){
                orderMirrorMapper.insertList(addMirrorList);
            }
            if(SpringUtil.isNotEmpty(updateMirrorList)){
                orderMirrorMapper.batchUpdate(updateMirrorList);
            }
        }
    }


    /**
     * 根据原始订单落地数据，补偿到统计系统
     * @param orderMirrorList
     */
    @Transactional(rollbackFor = Exception.class)
    public void syncOrderMirrorByOriginData(List<OrderMirrorSyncODTO> orderMirrorList){
        if (SpringUtil.isEmpty(orderMirrorList)) {
            return;
        }
        List<OrderMirror> mirrorList = new ArrayList<>();
        orderMirrorList.forEach(entry->{
                OrderMirror mirror = new OrderMirror();
                SpringUtil.copyProperties(entry,mirror);
                mirrorList.add(mirror);
        });
        List<Long> orderIds = orderMirrorList.stream().map(OrderMirrorSyncODTO::getOrderId).collect(Collectors.toList());
        this.insertOrUpdateOrderMirror(orderIds,mirrorList);
    }

}

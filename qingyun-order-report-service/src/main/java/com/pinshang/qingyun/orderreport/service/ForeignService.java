package com.pinshang.qingyun.orderreport.service;

import com.pinshang.qingyun.base.configure.expand.QYAssert;
import com.pinshang.qingyun.orderreport.dto.CommodityQuantityIDTO;
import com.pinshang.qingyun.orderreport.dto.CommodityQuantityIDTO2;
import com.pinshang.qingyun.orderreport.dto.CommodityQuantityODTO;
import com.pinshang.qingyun.orderreport.mapper.OrderMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @author: chenqiang
 * @time: 22/7/20/020 14:25
 */
@Service
public class ForeignService {

    @Autowired
    private OrderMapper orderMapper;

    /**
     * 根据订单日期统计商品数量
     * @param commodityQuantityIDTO
     * @return
     */
    public List<CommodityQuantityODTO> selectOrderCountCommodityQuantityByOrderTime(CommodityQuantityIDTO commodityQuantityIDTO){
        QYAssert.isTrue(null != commodityQuantityIDTO, "参数有误!");
        QYAssert.notNull(commodityQuantityIDTO.getOrderTime(),"订单日期不能为空");
        return orderMapper.selectOrderCountCommodityQuantityByOrderTime(commodityQuantityIDTO);
    }

    /**
     * 根据订单日期统计商品数量
     * @param commodityQuantityIDTO
     * @return
     */
    public List<CommodityQuantityODTO> selectOrderCountCommodityQuantityByOrderTime2(CommodityQuantityIDTO2 commodityQuantityIDTO){
        QYAssert.isTrue(null != commodityQuantityIDTO, "参数有误!");
        QYAssert.notNull(commodityQuantityIDTO.getOrderTimeList(),"订单日期不能为空");
        return orderMapper.selectOrderCountCommodityQuantityByOrderTimeV2(commodityQuantityIDTO);
    }
}

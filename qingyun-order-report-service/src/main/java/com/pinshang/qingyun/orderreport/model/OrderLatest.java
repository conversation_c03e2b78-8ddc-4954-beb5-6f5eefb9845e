package com.pinshang.qingyun.orderreport.model;

import lombok.Data;

import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Transient;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2019/1/14 15:46.
 */
@Table(name = "t_tj_order_latest")
@Data
public class OrderLatest {
    @Id
    private Long id;

    /**订单号 **/
    private String orderCode;
    /**商铺id **/
    private Long storeId;
    /**订单时间 **/
    private Date orderTime;
    /**订单类型 **/
    private Integer orderType;
    /**订单来源 **/
    private Integer modeType;
    /**订单金额 **/
    private BigDecimal orderAmount;
    /**最终金额*/
    private BigDecimal finalAmount;
    /**
     * 订单优惠之前的价格
     */
    private BigDecimal totalAmount;
    /**订单运费金额（免运费为0）**/
    private BigDecimal freightAmount;

    /** 打印类型(1：本地,2：送货员,3：不打印) */
    private Integer printType;
    /**打印份数*/
    private Integer printNum;
    /**备注 **/
    private String orderRemark;
    /**更新者 **/
    private Long updateId;
    /**订单状态(0正常,1删除,2取消) **/
    private Integer orderStatus;
    /** 0:正常   1：未结算 **/
    private Integer settleStatus;
    /**创建者 **/
    private Long createId;
    /**创建时间*/
    protected Date createTime;
    /**修改时间*/
    protected Date updateTime;
    private Integer deliveryBatch;
    /** 订单是否允许修改价格 */
    private Integer changePriceStatus;
    @Transient
    private List<OrderItem> orderList;

    private String deliveryTimeRange;
    /***
     * 公司id
     */
    private Long companyId;

    private Long logisticsCenterId;

    private Integer businessType;
}

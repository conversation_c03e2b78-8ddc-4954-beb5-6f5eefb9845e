package com.pinshang.qingyun.orderreport.util;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.UnsupportedEncodingException;

public class StrUtils {

    private static final Logger LOGGER = LoggerFactory.getLogger(StrUtils.class);

    /**
     * 字符串截取工具类
     * 根据字节截取,中文1个汉字算2个字节(包括全角符号), 英文1个字符是一个字节
     * @param str 要截取的字符串
     * @param subSLength 截取的字节长度
     * @return 返回截取的结果,如果传入NULL返回空字符串
     */
    public static String subStr(String str, int subSLength) {
        if (str == null) {
            return "";
        } else {
            int tempSubLength = subSLength;
            String subStr = str.substring(0, str.length() < subSLength ? str.length() : subSLength);
            int subStrByetsL = 0;
            try {
                subStrByetsL = subStr.getBytes("GBK").length;
            } catch (UnsupportedEncodingException e) {
                LOGGER.error("截取字符串:[{}]时失败,e:[{}]", str, e.getMessage());
            }
            // 说明截取的字符串中包含有汉字
            while (subStrByetsL > tempSubLength) {
                int subSLengthTemp = --subSLength;
                subStr = str.substring(0, subSLengthTemp > str.length() ? str.length() : subSLengthTemp);
                try {
                    subStrByetsL = subStr.getBytes("GBK").length;
                } catch (UnsupportedEncodingException e) {
                    LOGGER.error("截取字符串:[{}]时失败,e:[{}]", str, e.getMessage());
                }
            }
            return subStr;
        }
    }
}

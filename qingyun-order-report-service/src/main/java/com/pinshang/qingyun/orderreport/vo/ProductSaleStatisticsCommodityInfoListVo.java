package com.pinshang.qingyun.orderreport.vo;

import com.pinshang.qingyun.base.page.Pagination;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2024/4/19 14:35
 */
@Data
public class ProductSaleStatisticsCommodityInfoListVo extends Pagination {

    @ApiModelProperty(value = "送货日期yyyy-MM-dd")
    private String deliveryDate;

    @ApiModelProperty(value = "商品编码或名称模糊查询")
    private String concatCodeOrName;
}

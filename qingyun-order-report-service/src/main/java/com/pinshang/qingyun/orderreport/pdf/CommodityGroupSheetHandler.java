package com.pinshang.qingyun.orderreport.pdf;

import com.alibaba.excel.write.handler.SheetWriteHandler;
import com.alibaba.excel.write.metadata.holder.WriteSheetHolder;
import com.alibaba.excel.write.metadata.holder.WriteWorkbookHolder;
import com.pinshang.qingyun.box.utils.DateUtil;
import com.pinshang.qingyun.orderreport.dto.CommodityGroupLinePreviewODTO;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddress;

import java.util.Date;

public class CommodityGroupSheetHandler implements SheetWriteHandler {
    private CommodityGroupLinePreviewODTO previewODTO;

    public CommodityGroupSheetHandler(CommodityGroupLinePreviewODTO previewODTO) {
        this.previewODTO = previewODTO;
    }

    @Override
    public void beforeSheetCreate(WriteWorkbookHolder writeWorkbookHolder, WriteSheetHolder writeSheetHolder) {

    }

    @Override
    public void afterSheetCreate(WriteWorkbookHolder writeWorkbookHolder, WriteSheetHolder writeSheetHolder) {
        Workbook workbook = writeWorkbookHolder.getWorkbook();
        Sheet sheet = writeSheetHolder.getSheet();

        //设置标题
        Row row1 = sheet.createRow(0);
        row1.setHeight((short) 800);
        Cell cell1 = row1.createCell(0);
        cell1.setCellValue(previewODTO.getLineName()+"发货清单");
        CellStyle cellStyle = workbook.createCellStyle();
        cellStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        cellStyle.setAlignment(HorizontalAlignment.CENTER);
        Font font = workbook.createFont();
        font.setBold(true);
        font.setFontHeight((short) 400);
        cellStyle.setFont(font);
        cell1.setCellStyle(cellStyle);
        //设置其他信息的单元格样式
        CellStyle cellStyleInfo = workbook.createCellStyle();
        cellStyleInfo.setVerticalAlignment(VerticalAlignment.CENTER);
        //合并单元格
        sheet.addMergedRegionUnsafe(new CellRangeAddress(0, 0, 0, 6));
        //设置基本信息
        Row row2 = sheet.createRow(1);
        row2.setHeight((short) 400);
        Cell cell2 = row2.createCell(1);
        cell2.setCellValue("线路：" + previewODTO.getLineName());
        cell2.setCellStyle(cellStyleInfo);
        sheet.addMergedRegionUnsafe(new CellRangeAddress(1, 1, 1, 2));
        Cell cell23 = row2.createCell(3);
        cell23.setCellValue("车牌：" + previewODTO.getLicensePlate());
        cell23.setCellStyle(cellStyleInfo);
        sheet.addMergedRegionUnsafe(new CellRangeAddress(1, 1, 3, 4));
        Cell cell25 = row2.createCell(5);
        cell25.setCellValue("送货员：" + previewODTO.getDeliverymanName());
        cell25.setCellStyle(cellStyleInfo);
        sheet.addMergedRegionUnsafe(new CellRangeAddress(1, 1, 5, 6));

        Row row3 = sheet.createRow(2);
        row3.setHeight((short) 400);
        Cell cell3 = row3.createCell(1);
        cell3.setCellValue("电话：" + previewODTO.getPhone());
        cell3.setCellStyle(cellStyleInfo);
        sheet.addMergedRegionUnsafe(new CellRangeAddress(2, 2, 1, 2));
        Cell cell33 = row3.createCell(3);
        cell33.setCellValue("送货日期：" + previewODTO.getOrderTime());
        cell33.setCellStyle(cellStyleInfo);
        sheet.addMergedRegionUnsafe(new CellRangeAddress(2, 2, 3, 4));
        Cell cell35 = row3.createCell(5);
        cell35.setCellValue("导出时间："  + DateUtil.getDateFormate(new Date(),"yyyy-MM-dd HH:mm"));
        cell35.setCellStyle(cellStyleInfo);
        sheet.addMergedRegionUnsafe(new CellRangeAddress(2, 2, 5, 6));
    }

}

package com.pinshang.qingyun.orderreport.service;

import static java.util.stream.Collectors.groupingBy;

import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StopWatch;

import com.github.pagehelper.PageHelper;
import com.pinshang.qingyun.base.configure.expand.QYAssert;
import com.pinshang.qingyun.box.utils.DateUtil;
import com.pinshang.qingyun.box.utils.SpringUtil;
import com.pinshang.qingyun.order.dto.orderStatistics.OrderSyncODTO;
import com.pinshang.qingyun.orderreport.mapper.OrderItemLatestMapper;
import com.pinshang.qingyun.orderreport.mapper.OrderItemMapper;
import com.pinshang.qingyun.orderreport.mapper.OrderItemSyncMapper;
import com.pinshang.qingyun.orderreport.mapper.OrderLatestMapper;
import com.pinshang.qingyun.orderreport.mapper.OrderLinePrintStatusMapper;
import com.pinshang.qingyun.orderreport.mapper.OrderMapper;
import com.pinshang.qingyun.orderreport.mapper.OrderSyncMapper;
import com.pinshang.qingyun.orderreport.mapper.OverOrderTimeMapper;
import com.pinshang.qingyun.orderreport.mapper.PrintStatusMapper;
import com.pinshang.qingyun.orderreport.mapper.TJOrderMonitorMapper;
import com.pinshang.qingyun.orderreport.mapper.entry.OrderStatisticsEntry;
import com.pinshang.qingyun.orderreport.mapper.entry.OverOrderTimeEntry;
import com.pinshang.qingyun.orderreport.model.Order;
import com.pinshang.qingyun.orderreport.model.OrderItem;
import com.pinshang.qingyun.orderreport.model.OrderItemLatest;
import com.pinshang.qingyun.orderreport.model.OrderItemSync;
import com.pinshang.qingyun.orderreport.model.OrderLatest;
import com.pinshang.qingyun.orderreport.model.OrderLinePrintStatus;
import com.pinshang.qingyun.orderreport.model.OrderSync;
import com.pinshang.qingyun.orderreport.model.PrintStatus;
import com.pinshang.qingyun.orderreport.vo.TJOrderMonitorVo;

import lombok.extern.slf4j.Slf4j;
import tk.mybatis.mapper.entity.Example;

/**
 * 订单统计查询--订单监控
 */
@Slf4j
@Service
public class TJOrderMonitorService {
    @Autowired
    private TJOrderMonitorMapper orderStatisticsMonitorMapper;
    @Autowired
    private OrderMapper orderMapper;
    @Autowired
    private OrderItemMapper orderItemMapper;
    @Autowired
    private OrderLatestMapper orderLatestMapper;
    @Autowired
    private OrderItemLatestMapper orderItemLatestMapper;
    @Autowired
    private OrderLinePrintStatusMapper orderLinePrintStatusMapper;
    @Autowired
    private PrintStatusMapper printStatusMapper;
    @Autowired
    private OverOrderTimeMapper overOrderTimeMapper;
    @Autowired
    private OperatorOrderStatisticalService operatorOrderStatisticalService;
    @Autowired
    private TJOrderMirrorService orderMirrorService;
    @Autowired
    private CommoditySaleStatisticsService commoditySaleStatisticsService;
    @Autowired
    private FactoryDeliveryStatisticsService factoryDeliveryStatisticsService;
    @Autowired
    private OrderSyncMapper orderSyncMapper;
    @Autowired
    private OrderItemSyncMapper orderItemSyncMapper;
    
    @Autowired
    private TjOrderMonitorSubService tjOrderMonitorSubService;

    /**
     * 查询订单统计信息
     * @param vo
     * @return
     */
    public OrderStatisticsEntry queryOrderStatisticsInfo(TJOrderMonitorVo vo){
        if(this.checkParam(vo)){
            //送货日期大于或等于今天、或者送货日期范围开始时间大于等于今天，查询latest表
            return orderStatisticsMonitorMapper.queryOrderStatisticsInfoLatest(vo.getOrderCodeList(),vo);
        }
         return orderStatisticsMonitorMapper.queryOrderStatisticsInfo(vo.getOrderCodeList(),vo);
    }

    /**
     * 查询订单号
     * @param vo
     * @return
     */
    public List<String> queryOrderDiffList(TJOrderMonitorVo vo) {
        List<String> orderCodeList = this.queryOrderList(vo).stream().map(Order::getOrderCode).collect(Collectors.toList());
        return orderCodeList;
    }

    /**
     * 根据补偿条件查询统计系统的订单
     * @param vo
     * @return
     */
    public List<Order> queryOrderList(TJOrderMonitorVo vo) {
        return orderStatisticsMonitorMapper.queryOrderList(vo.getOrderCodeList(),vo,this.checkParam(vo));
    }

    /**
     * 订单同步：四张表都是先删后增
     * 优化：先根据订单ID查询统计系统里是否有该订单，比较订单金额是否一致，如果一致，不做处理；如果不一致，先删后增。
     *
     * @param orderSyncODTOList
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public void syncOrder(List<OrderSyncODTO> orderSyncODTOList) {
        if(SpringUtil.isEmpty(orderSyncODTOList)){
            return;
        }
        orderSyncODTOList.removeIf(dto->SpringUtil.isEmpty(dto.getItemList()));
        if(SpringUtil.isEmpty(orderSyncODTOList)){
            return;
        }
        List<Order> orderList = orderSyncODTOList.stream().map(Order::syncConvert).collect(Collectors.toList());
        List<Long> orderIds = orderSyncODTOList.stream().map(OrderSyncODTO::getId).collect(Collectors.toList());
        Example example = new Example(Order.class);
        example.createCriteria().andIn("id",orderIds);
        List<Order> tjOrderList = orderMapper.selectByExample(example);
        //订单在统计系统里都不存在，全部同步，只需要新增不用执行删除操作
        if(SpringUtil.isEmpty(tjOrderList)){
            this.syncAssignOrder(orderList,false);
            return;
        }
        //返回要同步的订单包括： 1.统计系统里不存在的订单，2.统计系统存在但是订单金额不一致的订单。
        Map<Long,List<Order>> tjOrderMap = tjOrderList.stream().collect(groupingBy(Order::getId));
        List<Order> addOrderList = new ArrayList<>();
        List<Order> updateOrderList = new ArrayList<>();
        orderList.forEach(order ->{
            if(SpringUtil.isEmpty(tjOrderMap.get(order.getId()))){
                addOrderList.add(order);
            }else{
                Order existedOrder = tjOrderMap.get(order.getId()).get(0);
                boolean needUpdate = existedOrder.getFinalAmount().compareTo(order.getFinalAmount())!=0;
                needUpdate =   needUpdate || existedOrder.getOrderStatus().intValue()!= order.getOrderStatus().intValue();
                if( needUpdate ){
                    updateOrderList.add(order);
                }
            }
        });
        if(SpringUtil.isNotEmpty(addOrderList)){
            this.syncAssignOrder(addOrderList,false);
        }
        if(SpringUtil.isNotEmpty(updateOrderList)){
            this.syncAssignOrder(updateOrderList,true);
        }
    }
    @Transactional(rollbackFor = Exception.class)
    public void syncOrderItem(List<OrderSyncODTO> orderSyncODTOList) {
        if(SpringUtil.isEmpty(orderSyncODTOList)){
            return;
        }
        orderSyncODTOList.removeIf(dto->SpringUtil.isEmpty(dto.getItemList()));
        if(SpringUtil.isEmpty(orderSyncODTOList)){
            return;
        }
        List<Order> orderList = orderSyncODTOList.stream().map(Order::syncConvert).collect(Collectors.toList());
        this.syncAssignOrderNew(orderList,false);
    }
    /**
     * 订单同步：四张表都是先删后增
     * 优化：先根据订单ID查询统计系统里是否有该订单，比较订单金额是否一致，如果一致，不做处理；如果不一致，先删后增。
     *
     * @param orderSyncODTOList
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public void syncOrderOrLatest(List<OrderSyncODTO> orderSyncODTOList) {
        if(SpringUtil.isEmpty(orderSyncODTOList)){
            return;
        }
        orderSyncODTOList.removeIf(dto->SpringUtil.isEmpty(dto.getItemList()));
        if(SpringUtil.isEmpty(orderSyncODTOList)){
            return;
        }
        long l1 = System.currentTimeMillis();
        List<Order> orderList = orderSyncODTOList.stream().map(Order::syncConvert).collect(Collectors.toList());
        List<Long> orderIds = orderSyncODTOList.stream().map(OrderSyncODTO::getId).collect(Collectors.toList());
        Example example = new Example(Order.class);
        example.createCriteria().andIn("id",orderIds);
        List<Order> tjOrderList = orderMapper.selectByExample(example);

        Example orderLatestExample = new Example(OrderLatest.class);
        orderLatestExample.createCriteria().andIn("id",orderIds);
        List<OrderLatest> tjorderLatestList = orderLatestMapper.selectByExample(orderLatestExample);
        //订单在统计系统里都不存在，全部同步，只需要新增不用执行删除操作
        if(SpringUtil.isEmpty(tjOrderList)){
            this.syncAssignOrderNew(orderList,false);
            return;
        }
        //返回要同步的订单包括： 1.统计系统里不存在的订单，2.统计系统存在但是订单金额不一致的订单。
        Map<Long,List<Order>> tjOrderMap = tjOrderList.stream().collect(groupingBy(Order::getId));
        Map<Long,List<OrderLatest>> tjOrderLatestMap = tjorderLatestList.stream().collect(groupingBy(OrderLatest::getId));

        List<Order> addOrderList = new ArrayList<>();
        List<Order> updateOrderList = new ArrayList<>();
        List<OrderLatest> orderLatestList = new ArrayList();
        List<OrderItemLatest> orderItemLatestList = new ArrayList<>();

        orderList.forEach(order ->{
            if(SpringUtil.isEmpty(tjOrderMap.get(order.getId()))){
                addOrderList.add(order);
            }else{
                Order existedOrder = tjOrderMap.get(order.getId()).get(0);
                boolean needUpdate = existedOrder.getFinalAmount().compareTo(order.getFinalAmount())!=0;
                needUpdate =   needUpdate || existedOrder.getOrderStatus().intValue()!= order.getOrderStatus().intValue();
                if( needUpdate ){
                    updateOrderList.add(order);
                }
            }
            if(SpringUtil.isEmpty(tjOrderLatestMap.get(order.getId()))){
                orderLatestList.add(order.latestConvert());
                List<OrderItemLatest> itemLatests = order.getOrderList().stream().map(OrderItem::convert).collect(Collectors.toList());
                orderItemLatestList.addAll(itemLatests);
            }
        });
        long l2 = System.currentTimeMillis();

        log.error("查询同步订单时间--orderResult"+(l2- l1));

        if(SpringUtil.isNotEmpty(orderLatestList) && SpringUtil.isNotEmpty(orderItemLatestList)){
            List<Long> longList = orderLatestList.stream().map(OrderLatest::getId).collect(Collectors.toList());
            //删除最新订单主表
            Example deleteOrderLatestExample = new Example(OrderLatest.class);
            deleteOrderLatestExample.createCriteria().andIn("id",longList);
            orderLatestMapper.deleteByExample(deleteOrderLatestExample);

            orderItemLatestMapper.deleteOrderLatestItemByOrderId(longList);

            orderLatestMapper.batchInsert(orderLatestList);
            orderItemLatestMapper.batchInsert(orderItemLatestList);
        }
        if(SpringUtil.isNotEmpty(addOrderList)){
            this.syncAssignOrderNew(addOrderList,false);
        }
        if(SpringUtil.isNotEmpty(updateOrderList)){
            this.syncAssignOrderNew(updateOrderList,true);
        }
    }
    /**
     * 同步订单
     * @param orderList
     * @param deleteFlag ： 标识是否需要先根据订单ID删除统计系统的订单，理论上如果这一批订单在统计系统不存在，不需要执行删除操作
     */
    public void syncAssignOrderNew(List<Order> orderList,Boolean deleteFlag) {
        if(SpringUtil.isEmpty(orderList)){
            return;
        }
        orderList.forEach(order -> {
            if(!deleteFlag){
                operatorOrderStatisticalService.processOperatorInsertOrder(order);
            }else{
                operatorOrderStatisticalService.processOperatorUpdateOrder(order);
            }
        });

        List<Long> orderIds = orderList.stream().map(Order::getId).collect(Collectors.toList());
        List<OrderItem> orderItemList = new ArrayList<>();
        //记录最新订单
        List<Long> latestOrderIds = new ArrayList<>();
        List<OrderLatest> orderLatestList = new ArrayList<>();
        List<OrderItemLatest> orderItemLatestList = new ArrayList<>();

        orderList.forEach(order->{
            orderItemList.addAll(order.getOrderList());
            //今天或者今天以后的订单，需要更新latest表
            if(order.getOrderTime() != null && DateUtil.compareDate(DateUtil.getNowDate(),order.getOrderTime())){
                latestOrderIds.add(order.getId());
                orderLatestList.add(order.latestConvert());
                List<OrderItemLatest> itemLatests = order.getOrderList().stream().map(OrderItem::convert).collect(Collectors.toList());
                orderItemLatestList.addAll(itemLatests);
            }
        });
        if(SpringUtil.isNotEmpty(orderItemList)) {
            //删除订单主表
            Example example = new Example(Order.class);
            example.createCriteria().andIn("id",orderIds);
            orderMapper.deleteByExample(example);

            //删除订单明细表
            Example itemEx = new Example(OrderItem.class);
            itemEx.createCriteria().andIn("orderId",orderIds);
            orderItemMapper.deleteByExample(itemEx);

            orderMapper.batchInsert(orderList);
            orderItemMapper.batchInsert(orderItemList);

            if(!deleteFlag){
                //新增订单，保存订单客户当时的基础数据
                orderMirrorService.syncTjOrderMirror(orderIds);
            }
        }
        if(SpringUtil.isNotEmpty(orderItemLatestList)){
            //删除最新订单主表
            Example example = new Example(OrderLatest.class);
            example.createCriteria().andIn("id",latestOrderIds);
            orderLatestMapper.deleteByExample(example);

            //删除最新订单明细表
            Example latestItemEx = new Example(OrderItemLatest.class);
            latestItemEx.createCriteria().andIn("orderId", latestOrderIds);
            orderItemLatestMapper.deleteByExample(latestItemEx);
            orderLatestMapper.batchInsert(orderLatestList);
            orderItemLatestMapper.batchInsert(orderItemLatestList);
        }

    }
    /**
     * 同步订单
     * @param orderList
     * @param deleteFlag ： 标识是否需要先根据订单ID删除统计系统的订单，理论上如果这一批订单在统计系统不存在，不需要执行删除操作
     */
    public void syncAssignOrder(List<Order> orderList,Boolean deleteFlag) {
        if(SpringUtil.isEmpty(orderList)){
            return;
        }
        orderList.forEach(order -> {
            if(!deleteFlag){
                operatorOrderStatisticalService.processOperatorInsertOrder(order);
            }else{
                operatorOrderStatisticalService.processOperatorUpdateOrder(order);
            }
        });

        List<Long> orderIds = orderList.stream().map(Order::getId).collect(Collectors.toList());
        List<OrderItem> orderItemList = new ArrayList<>();
        //记录最新订单
        List<Long> latestOrderIds = new ArrayList<>();
        List<OrderLatest> orderLatestList = new ArrayList<>();
        List<OrderItemLatest> orderItemLatestList = new ArrayList<>();

        orderList.forEach(order->{
            orderItemList.addAll(order.getOrderList());
            //今天或者今天以后的订单，需要更新latest表
            if(order.getOrderTime() != null && DateUtil.compareDate(DateUtil.getNowDate(),order.getOrderTime())){
                latestOrderIds.add(order.getId());
                orderLatestList.add(order.latestConvert());
                List<OrderItemLatest> itemLatests = order.getOrderList().stream().map(OrderItem::convert).collect(Collectors.toList());
                orderItemLatestList.addAll(itemLatests);
            }
        });
        if(SpringUtil.isNotEmpty(orderItemList)) {
            if(deleteFlag){
                //删除订单主表
                Example example = new Example(Order.class);
                example.createCriteria().andIn("id",orderIds);
                orderMapper.deleteByExample(example);

                //删除订单明细表
                Example itemEx = new Example(OrderItem.class);
                itemEx.createCriteria().andIn("orderId",orderIds);
                orderItemMapper.deleteByExample(itemEx);
            }

            orderMapper.batchInsert(orderList);
            orderItemMapper.batchInsert(orderItemList);

            if(!deleteFlag){
                //新增订单，保存订单客户当时的基础数据
                orderMirrorService.syncTjOrderMirror(orderIds);
            }
        }
        if(SpringUtil.isNotEmpty(orderItemLatestList)){
            if(deleteFlag){
                //删除最新订单主表
                Example example = new Example(OrderLatest.class);
                example.createCriteria().andIn("id",latestOrderIds);
                orderLatestMapper.deleteByExample(example);

                //删除最新订单明细表
                Example latestItemEx = new Example(OrderItemLatest.class);
                latestItemEx.createCriteria().andIn("orderId", latestOrderIds);
                orderItemLatestMapper.deleteByExample(latestItemEx);
            }
            orderLatestMapper.batchInsert(orderLatestList);
            orderItemLatestMapper.batchInsert(orderItemLatestList);
        }

    }

    private Boolean checkParam(TJOrderMonitorVo vo){
        QYAssert.notNull(vo,"参数不能为空");
        //用于标识是否需要查询latest表
        Boolean latestFlag1 = vo.getOrderTime() != null && DateUtil.compareDate(DateUtil.getNowDate(),vo.getOrderTime());
        Boolean latestFlag2 = vo.getOrderTime() == null && vo.getStartTime() != null && DateUtil.compareDate(DateUtil.getNowDate(),vo.getStartTime());
        return latestFlag1 || latestFlag2;
    }

    /**
     * 定时清理最新订单统计表及明细表数据
     *
     * 1.新增 线路打印表t_tj_order_line_print_status数据 （保存今天以前的订单客户线路打印状态，不包括今天）
     * 2.新增 晚订货统计表t_tj_over_order_time 数据（保存今天以前的客户没有正常订单只有补货订单的订单数据）
     * 3.删除latest表 今天以前的数据
     * 4.删除t_tj_print_status表今天以前的数据

     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public Integer removeTJOrderLatest(Date orderTime) {
        if(orderTime==null){
            return 0;
        }
        /*** 保存产品销售汇总(商品) */
        commoditySaleStatisticsService.sumCommodityDataByDay(orderTime);

        /*** 产品销售汇总--工厂送货汇总 */
        factoryDeliveryStatisticsService.insertFactoryDeliveryStatistics(orderTime,"t_tj_order_latest","t_tj_order_list_latest");

         /*** 新增 t_tj_order_line_print_status */
        this.processOrderLinePrintStatus(orderTime);

         /*** 新增 t_tj_over_order_time */
        this.processOverOrderTime(orderTime);

         /*** 删除latest明细表 */
        orderItemLatestMapper.deleteOrderLatestItemByOrderTime(orderTime);

         /*** 删除latest主表 */
        Example example = new Example(OrderLatest.class);
        example.createCriteria().andEqualTo("orderTime",orderTime);
        orderLatestMapper.deleteByExample(example);

         /*** 删除t_tj_print_status */
        Example psExample = new Example(PrintStatus.class);
        psExample.createCriteria().andEqualTo("orderTime",orderTime);
        printStatusMapper.deleteByExample(psExample);

        return 1;
    }


    //处理job执行参数，返回送货日期列表
    public List<Date> buildOrderTimeParam(String[] orderTimes){
        List<Date> orderTimeList = new ArrayList<>();
        DateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
        try {
            if(SpringUtil.isEmpty(orderTimes)){
                //job没有传日期参数，默认设置： 今天、明天
                Date today = new Date(System.currentTimeMillis());
                Date tomorrow = new Date(today.getTime() + 24 * 60 * 60 * 1000);
                orderTimeList.add(dateFormat.parse(dateFormat.format(today)));
                orderTimeList.add(dateFormat.parse(dateFormat.format(tomorrow)));
            }else{
                //job传参，日期格式化
                for(String orderTime : orderTimes){
                    try {
                        Date date = dateFormat.parse(orderTime);
                        if (!orderTimeList.contains(date)) {
                            orderTimeList.add(date);
                        }
                    }catch (ParseException e){
                        log.error("统计系统订单同步定时补偿，job参数转换为日期格式异常，输入参数：",orderTime);
                    }
                }
            }
        } catch (ParseException e) {
            log.error("统计系统订单同步定时补偿，job参数转换为日期格式异常",e);
        }
        log.info("统计系统订单同步定时补偿，送货日期参数列表：orderTimeList={}",orderTimeList);
        return orderTimeList;
    }

    /**
     * 处理差异订单
     * @param list1
     * @param list2
     * @return
     */
    public List<String> buildDiffOrderCodeList(List<String> list1,List<String> list2) {
        if(SpringUtil.isEmpty(list1)){
            return Collections.EMPTY_LIST;
        }
        if(SpringUtil.isEmpty(list2)){
            return list1;
        }
        list1.removeAll(list2);
        return list1;
    }

    /**
     * 新增 线路打印表t_tj_order_line_print_status数据 （保存今天以前的订单客户线路打印状态，不包括今天）
     */
    public void processOrderLinePrintStatus(Date orderTime){
        List<OrderLinePrintStatus> list = orderLinePrintStatusMapper.queryOrderPrintStatus(orderTime);
        if(SpringUtil.isNotEmpty(list)){
            orderLinePrintStatusMapper.batchInsert(list);
        }
    }

    /**
     * 新增 晚订货统计表t_tj_over_order_time 数据（保存今天以前的客户没有正常订单只有补货订单的订单数据）
     */
    public void processOverOrderTime(Date orderTime){
        List<OverOrderTimeEntry> overOrderTimeEntryList = overOrderTimeMapper.queryOverOrderList(orderTime);
        if(SpringUtil.isNotEmpty(overOrderTimeEntryList)){
            overOrderTimeMapper.batchInsert(overOrderTimeEntryList);
        }
    }

    /**
     * 查询最新订单表ID
     * @param nowDate
     * @return
     */
    public List<OrderLatest> queryOrderLatestList(Date nowDate){
        return orderLatestMapper.queryOrderLatestList(nowDate);
    }

    /**
     * 查询当前日期之前的订单
     * @param orderTime 日期搜索
     * @return
     */
    public List<OrderSync> queryOrderSyncList(Date orderTime) {
        return orderSyncMapper.queryOrderSyncList(orderTime);
    }

    /**
     * 删除 t_tj_order_sync 表及明细表数据
     * @param orderTime
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public Integer removeTJOrderSync(Date orderTime) {
        List<OrderSync> orderList = this.queryOrderSyncList(orderTime);
        if(SpringUtil.isNotEmpty(orderList)){
            List<Long> orderIdList = orderList.stream().mapToLong(e -> e.getId()).distinct().boxed().collect(Collectors.toList());
            if(SpringUtil.isNotEmpty(orderIdList)){

                /** 删除t_tj_order_sync主表 **/
                Example example = new Example(OrderSync.class);
                example.createCriteria().andIn("id",orderIdList);
                orderSyncMapper.deleteByExample(example);

                /** 删除t_tj_order_list_sync明细表 **/
                Example example2 = new Example(OrderItemSync.class);
                example2.createCriteria().andIn("orderId",orderIdList);
                orderItemSyncMapper.deleteByExample(example2);

            }else{
                log.error("定时清理t_tj_order_sync表job，订单id={}",orderIdList);
                return 0;
            }
        }else{
            log.info("定时清理t_tj_order_sync表job，未查询到最新订单数据");
            return 0;
        }
        return 1;
    }

	public void removeTJOrderSync2(Date date) {
		
		DateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
		String dateStr = dateFormat.format(date);
		log.info(String.format("\n清理[%s] 前 tj.t_tj_order_sync表 和 tj.t_tj_order_list_sync表: 开始执行...", dateStr));
		long start = System.currentTimeMillis();
		long current = start;
		List<OrderSync> orders = null;
		int loops = 0;
		int sum = 0;
		while (true) {
			PageHelper.startPage(1, 1000, false); // 按每页1000条查询
			Example orderExp = new Example(OrderSync.class);
			orderExp.createCriteria().andLessThanOrEqualTo("orderTime", dateStr);
			orderExp.selectProperties("id");
			orders = orderSyncMapper.selectByExample(orderExp);
			if (orders.isEmpty()) {
				break;
			}
			
			List<Long> orderIds = orders.stream().map(OrderSync::getId).collect(Collectors.toList());
			
			tjOrderMonitorSubService.deleteOrderSyncByOrderIds(orderIds);
			
    		current = System.currentTimeMillis();
    		
    		loops++;
    		sum += orderIds.size();
    		
    		if (loops % 100 == 0) { // 每100次循环，打印一次日志，即每1000*100条数据打印一次日志
    			log.info(String.format("\n清理[%s] 前 tj.t_tj_order_sync表 和 tj.t_tj_order_list_sync表: order_id:[%s,...,%s] 共 %s ， 当前耗时:%s ms"
    									, dateStr, orderIds.get(0), orderIds.get(orderIds.size() - 1), orderIds.size(), (current - start)));
    		}
    		
		}
		
		log.info(String.format("\n清理[%s] 前 tj.t_tj_order_sync表 和 tj.t_tj_order_list_sync表: 共处理 %s 订单，总耗时：%s ms， 完成", dateStr, sum, (current - start)));
		
	}

}

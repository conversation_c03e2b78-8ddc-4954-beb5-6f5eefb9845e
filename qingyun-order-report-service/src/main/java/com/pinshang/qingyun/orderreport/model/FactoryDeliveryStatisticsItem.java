package com.pinshang.qingyun.orderreport.model;

import lombok.Data;

import javax.persistence.Id;
import javax.persistence.Table;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @description 工厂送货明细表
 * @date 2020/9/02
 */
@Data
@Table(name = "t_tj_factory_delivery_statistics_item")
public class FactoryDeliveryStatisticsItem {

    @Id
    private Long id;

    /** t_tj_factory_delivery_statistics表ID **/
    private Long referId;

    /** 客户类型ID **/
    private Long storeTypeId;

    /** 客户类型数量小计 **/
    private BigDecimal totalQuantity;

    /** 客户类型金额小计 **/
    private BigDecimal totalAmount;

    public FactoryDeliveryStatisticsItem(){}

    public FactoryDeliveryStatisticsItem(Long storeTypeId, BigDecimal totalQuantity, BigDecimal totalAmount){
        this.storeTypeId = storeTypeId;
        this.totalQuantity = totalQuantity;
        this.totalAmount = totalAmount;
    }

}

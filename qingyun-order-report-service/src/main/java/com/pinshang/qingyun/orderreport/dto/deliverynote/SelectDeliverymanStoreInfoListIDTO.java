package com.pinshang.qingyun.orderreport.dto.deliverynote;

import io.swagger.annotations.ApiModelProperty;

import java.math.BigDecimal;

import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 查询  送货员-客户信息 列表
 */
@Data
@NoArgsConstructor
public class SelectDeliverymanStoreInfoListIDTO {
	@ApiModelProperty(position = 10, required = true, value = "送货员ID，由后端设置，无需前端设置", hidden = true)
	private Long deliverymanId;
	@ApiModelProperty(position = 11, required = true, value = "送货日期：yyyy-MM-dd")
	private String orderTime;
	@ApiModelProperty(position = 12, value = "线路ID")
	private Long storeLineId;

	@ApiModelProperty(position = 21, value = "百度经度")
	private BigDecimal baiduLongitude;
	@ApiModelProperty(position = 22, value = "百度维度")
	private BigDecimal baiduLatitude;
}

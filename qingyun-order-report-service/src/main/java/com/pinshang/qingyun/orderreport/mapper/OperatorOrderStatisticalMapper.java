package com.pinshang.qingyun.orderreport.mapper;

import com.pinshang.qingyun.base.mybatis.MyMapper;
import com.pinshang.qingyun.orderreport.mapper.entry.OperatorStatisticsEntry;
import com.pinshang.qingyun.orderreport.model.Workload;
import com.pinshang.qingyun.orderreport.vo.OperatorOrderMonitorVo;
import com.pinshang.qingyun.orderreport.vo.OperatorOrderRespVo;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2019/3/13 14:24.
 */
@Repository
public interface OperatorOrderStatisticalMapper extends MyMapper<Workload> {
    /**
     * 操作员订单统计查询
     * @param sqlParams
     * @return
     */
    List<OperatorOrderRespVo> queryList(Map<String, Object> sqlParams);

    void insertOrUpdateOperatorOrder(Workload workload);

    void updateItemQuantity(Workload workload);

    int deleteOperatorStatistics(OperatorOrderMonitorVo vo);

    List<OperatorStatisticsEntry> queryOperatorStatisticsEntry(OperatorOrderMonitorVo vo);

    int batchInsert(List<OperatorStatisticsEntry> entryList);

}

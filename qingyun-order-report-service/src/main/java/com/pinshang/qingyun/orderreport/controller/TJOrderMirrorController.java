package com.pinshang.qingyun.orderreport.controller;

import com.github.pagehelper.PageInfo;
import com.pinshang.qingyun.box.utils.SpringUtil;
import com.pinshang.qingyun.order.dto.orderStatistics.OrderMirrorSyncODTO;
import com.pinshang.qingyun.order.dto.orderStatistics.OrderStatisticsMonitorIDTO;
import com.pinshang.qingyun.order.service.OrderStatisticsMonitorClient;
import com.pinshang.qingyun.orderreport.service.TJOrderMirrorService;
import com.pinshang.qingyun.orderreport.service.TJOrderMonitorService;
import com.pinshang.qingyun.orderreport.vo.TJOrderMonitorVo;
import com.pinshang.qingyun.product.dto.sync.SelectSyncInfoByCodeListIDTO;
import com.pinshang.qingyun.product.dto.sync.StoreInfoODTO;
import com.pinshang.qingyun.product.service.sync.SyncStoreInfoClient;
import com.pinshang.qingyun.sync.vo.SyncInfoByCodesVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;


/**
 * 订单落地数据
 */
@RestController
@RequestMapping("/tjOrderMirror")
@Slf4j
public class TJOrderMirrorController {
    @Autowired
    private TJOrderMirrorService tjOrderMirrorService;
    @Autowired
    private TJOrderMonitorService tjOrderMonitorService;
    @Autowired
    private OrderStatisticsMonitorClient orderClient;
    @Autowired
    private SyncStoreInfoClient syncStoreInfoClient;
    private static final int DEFAULT_PAGE_SIZE = 300;


//    @RequestMapping(value = "/updateOrderMirror", method = RequestMethod.POST)
//    public void updateOrderMirror(@RequestBody SyncInfoByCodesVO vo) {
//        int retry = 0;
//        int index = 0;
//        int start = index * DEFAULT_PAGE_SIZE;
//        SelectSyncInfoByCodeListIDTO idto = new SelectSyncInfoByCodeListIDTO(vo.getCodeList());
//        idto.setStartEnd(start, DEFAULT_PAGE_SIZE);
//        StoreInfoODTO storeInfoODTO = syncStoreInfoClient.selectStoreInfo(idto);
//        if (null != storeInfoODTO && SpringUtil.isNotEmpty(storeInfoODTO.getStoreList())) {
//            tjOrderMirrorService.syncStoreOrderMirror(storeInfoODTO.getStoreList());
//            int storeSize = (null != storeInfoODTO && SpringUtil.isNotEmpty(storeInfoODTO.getStoreList()))? storeInfoODTO.getStoreList().size(): 0;
//            while (storeSize >= DEFAULT_PAGE_SIZE){
//                try {
//                    start = (++ index) * DEFAULT_PAGE_SIZE;
//                    idto.setStartEnd(start, DEFAULT_PAGE_SIZE);
//                    storeInfoODTO = syncStoreInfoClient.selectStoreInfo(idto);
//                } catch (Exception e){
//                    retry ++;
//                    index --;
//                    continue;
//                }
//                tjOrderMirrorService.syncStoreOrderMirror(storeInfoODTO.getStoreList());
//                storeSize = (null != storeInfoODTO && SpringUtil.isNotEmpty(storeInfoODTO.getStoreList()))? storeInfoODTO.getStoreList().size(): 0;
//
//                if(retry > 6){
//                    break;
//                }
//            }
//        }
//    }
    
    @RequestMapping(value = "/updateOrderMirror", method = RequestMethod.POST)
	public void updateOrderMirror(@RequestBody SyncInfoByCodesVO vo) {
        int retry = 0;
        Long minId = -1L;
        SelectSyncInfoByCodeListIDTO idto = new SelectSyncInfoByCodeListIDTO(vo.getCodeList());
        idto.setMinIdPageSize(minId, DEFAULT_PAGE_SIZE);
        StoreInfoODTO storeInfoODTO = syncStoreInfoClient.selectStoreInfo(idto);
        if (null != storeInfoODTO && SpringUtil.isNotEmpty(storeInfoODTO.getStoreList())) {
            tjOrderMirrorService.syncStoreOrderMirror(storeInfoODTO.getStoreList());
            
            int storeSize = 0;
    		if (SpringUtil.isNotEmpty(storeInfoODTO.getStoreList())) {
    			storeSize = storeInfoODTO.getStoreList().size();
    			minId = storeInfoODTO.getStoreList().get(storeSize - 1).getId();
    		}
    		
            while (storeSize >= DEFAULT_PAGE_SIZE){
                try {
                    idto.setMinIdPageSize(minId, DEFAULT_PAGE_SIZE);
                    storeInfoODTO = syncStoreInfoClient.selectStoreInfo(idto);
                } catch (Exception e){
                	if(retry > 6){
                        break;
                    }
                    retry ++;
                    continue;
                }
                retry = 0;
                tjOrderMirrorService.syncStoreOrderMirror(storeInfoODTO.getStoreList());
                
              	storeSize = 0;
        		if (SpringUtil.isNotEmpty(storeInfoODTO.getStoreList())) {
        			storeSize = storeInfoODTO.getStoreList().size();
        			minId = storeInfoODTO.getStoreList().get(storeSize - 1).getId();
        		}
            }
        }
    }


    /**
     * 订单落地数据补偿
     * @return
     */
    @PostMapping("/syncTJOrderMirror")
    public Integer syncTJOrderMirror(@RequestBody TJOrderMonitorVo vo){
        OrderStatisticsMonitorIDTO idto = new OrderStatisticsMonitorIDTO();
        SpringUtil.copyProperties(vo,idto);
        int pageNo = 1;
        idto.setPageNo(pageNo);//分页请求订单系统，查询订单
        idto.setPageSize(200);
        int retry = 0;
        PageInfo<OrderMirrorSyncODTO> orderResult = null;
        while(pageNo == 1 || (orderResult != null && orderResult.isHasNextPage())) {
            orderResult = orderClient.queryOrderMirrorSyncList(idto);
            if (orderResult != null && SpringUtil.isNotEmpty(orderResult.getList())) {
                tjOrderMirrorService.syncOrderMirrorByOriginData(orderResult.getList());
            } else {
                if (retry++ > 6) {
                    break;
                }
            }
            idto.setPageNo(++pageNo);
        }
        return 1;
    }

}

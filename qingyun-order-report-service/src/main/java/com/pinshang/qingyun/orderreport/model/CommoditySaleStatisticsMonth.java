package com.pinshang.qingyun.orderreport.model;

import lombok.Data;

import javax.persistence.Id;
import javax.persistence.Table;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @description 产品销售汇总--月份
 * @date 2019/8/30
 */
@Data
@Table(name = "t_tj_commodity_sale_statistics_month")
public class CommoditySaleStatisticsMonth {

    @Id
    private Long id;

    /** 商品id **/
    private Long commodityId;

    /** 数量小计 **/
    private BigDecimal totalQuantity;

    /** 金额小计 **/
    private BigDecimal totalAmount;

    /** 送货月份(yyyy-MM，例：2019-09) **/
    private String saleMonth;

    /** 数据添加时间 **/
    private Date createTime;

    /*** 公司id */
    private Long companyId;

}

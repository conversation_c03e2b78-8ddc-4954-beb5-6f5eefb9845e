package com.pinshang.qingyun.orderreport.mapper.entry;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

@Data
public class NonlocalShipmentsEntry {

    @ApiModelProperty("产品编码")
    private String commodityCode;

    @ApiModelProperty("产品名称")
    private String commodityName;

    @ApiModelProperty(value = "产品规格")
    private String commoditySpec;

    @ApiModelProperty(value = "产品单位")
    private String commodityUnitName;

    @ApiModelProperty("生产组")
    private String workshopName;

    @ApiModelProperty("产品总计")
    private BigDecimal quantity;

    @ApiModelProperty("详情")
    private List<NonlocalShipmentsItemEntry> itemList;

}

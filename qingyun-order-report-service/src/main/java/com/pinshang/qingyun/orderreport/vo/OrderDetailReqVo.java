package com.pinshang.qingyun.orderreport.vo;

import com.pinshang.qingyun.base.page.Pagination;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2019/3/11 10:47
 */
@Data
public class OrderDetailReqVo extends Pagination {
    /**
     * 结账客户Id
     */
    private Long settlementId;
    /**
     *客户编码
     */
    private String storeCode;
    /**
     *起始时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date startOrderDate;
    /**
     *截止日期
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date endOrderDate;
    /**
     *商品品类
     */
    private Long categoryId;
    /**
     *客户类型
     */
    private Long storeTypeId;
    /**
     *商品id
     */
    private Long commodityId;
    /**
     *操作人
     */
    private Long operatorId;
    /**
     * 查询标识
     */
    private transient Boolean latestFlag;

    /***
     * 公司id
     */
    private Long companyId;

    /***
     * 客户id
     */
    private Long storeId;

    /***
     * 订单编码
     */
    private String orderCode;

    /**客户部门**/
    @ApiModelProperty("客户部门")
    private Long departmentId;

    /**主任**/
    @ApiModelProperty("主任")
    private List<Long> officeDirectorIdList;

    /**大区经理**/
    @ApiModelProperty("大区经理")
    private List<Long> regionManagerIdList;

    /**督导**/
    @ApiModelProperty("督导")
    private List<Long> supervisorIdList;

    /**结账客户ID集合**/
    @ApiModelProperty("结账客户ID集合")
    private List<Long> settlementIdList;

    @ApiModelProperty(value = "渠道ID")
    private Long storeChannelId;

    @ApiModelProperty(value = "业务类型")
    private Integer filterBusinessType;
}

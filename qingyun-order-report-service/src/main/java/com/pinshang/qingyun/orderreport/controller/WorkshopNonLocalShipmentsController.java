package com.pinshang.qingyun.orderreport.controller;

import com.pinshang.qingyun.base.api.TokenInfo;
import com.pinshang.qingyun.base.configure.expand.QYAssert;
import com.pinshang.qingyun.base.util.FastThreadLocalUtil;
import com.pinshang.qingyun.box.utils.DateUtil;
import com.pinshang.qingyun.box.utils.SpringUtil;
import com.pinshang.qingyun.orderreport.config.CustomerProperties;
import com.pinshang.qingyun.orderreport.mapper.entry.NonlocalShipmentsEntry;
import com.pinshang.qingyun.orderreport.mapper.entry.NonlocalShipmentsResultEntry;
import com.pinshang.qingyun.orderreport.service.PrintingAllowedCheckService;
import com.pinshang.qingyun.orderreport.service.WorkshopNonLocalShipmentsService;
import com.pinshang.qingyun.orderreport.util.PdfUtils;
import com.pinshang.qingyun.orderreport.vo.NonlocalShipmentsVo;
import com.pinshang.qingyun.orderreport.vo.ProductShipmentsRequestVo;
import com.pinshang.qingyun.orderreport.vo.WorkshopShipmentsVo;
import com.pinshang.qingyun.print.service.PrinterTaskClient;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiModelProperty;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.util.*;

/**
 * 生产组外地发货单
 * <AUTHOR>
 * @date 2019/2/27 14:00.
 */
@RestController
@RequestMapping("/statistical/workshopNonLocalShipments")
@Api(value = "生产组外地发货单api", tags = "workshopNonLocalShipments", description = "生产组外地发货单相关接口")
public class WorkshopNonLocalShipmentsController {
	
	@Autowired
    private PrinterTaskClient printerTaskClient;

    @Autowired
    private CustomerProperties customerProperties;

    @Autowired
    private PrintingAllowedCheckService printingAllowedCheckService;

    private final WorkshopNonLocalShipmentsService workshopNonLocalShipmentsService;

    public WorkshopNonLocalShipmentsController(WorkshopNonLocalShipmentsService workshopNonLocalShipmentsService) {
        this.workshopNonLocalShipmentsService = workshopNonLocalShipmentsService;
    }


    /**
     * 生产组外地发货单预览
     * @param requestVo 请求参数
     * @return
     */
    @RequestMapping(value = "/preview",method = RequestMethod.POST)
    public WorkshopShipmentsVo preview(@RequestBody ProductShipmentsRequestVo requestVo) {
        return workshopNonLocalShipmentsService.preview(requestVo);
    }


    /**
     * 打印
     * @param requestVo 当前搜索条件
     * @return 返回JSON
     */
    @RequestMapping("print")
    public String print(@RequestBody ProductShipmentsRequestVo requestVo) {
        printingAllowedCheckService.isPrintingAllowed(requestVo);
        printerTaskClient.checkUserPrinter(requestVo.getUserId());
        workshopNonLocalShipmentsService.print(requestVo);
        return "ok";
    }

    /**
     * 打印所有
     * @param requestVo 当前搜索条件
     * @return 返回JSON
     */
    @RequestMapping("printAll")
    public String print(@RequestBody List<ProductShipmentsRequestVo> requestVo) {
        QYAssert.isTrue(SpringUtil.isNotEmpty(requestVo),"请选择要打印的内容");
        printingAllowedCheckService.isPrintingAllowed(requestVo.get(0));
        printerTaskClient.checkUserPrinter(requestVo.get(0).getUserId());
        workshopNonLocalShipmentsService.printAll(requestVo);
        return "ok";
    }

    /**
     * pda清美人查询生产组外地发货单列表
     * @param vo
     * @return
     */
    @RequestMapping(value = "/queryListForApp",method = RequestMethod.POST)
    public List<NonlocalShipmentsEntry> queryListForApp(@RequestBody NonlocalShipmentsVo vo) {
        return workshopNonLocalShipmentsService.queryListForApp(vo);
    }

    /**
     * pda清美人查询生产组外地发货单列表
     * @param vo
     * @return
     */
    @RequestMapping(value = "/queryListForAppV1",method = RequestMethod.POST)
    public NonlocalShipmentsResultEntry queryListForAppV1(@RequestBody NonlocalShipmentsVo vo) {
        QYAssert.isTrue(StringUtils.isNotBlank(vo.getOrderDate()),"送货时间不能为空，请输入！");
        Date orderDate = DateUtil.parseDate(vo.getOrderDate(),"yyyy-MM-dd");
        TokenInfo tokenInfo = FastThreadLocalUtil.getQY();
        vo.setDirectorCode(tokenInfo.getEmployeeNumber());
        return new NonlocalShipmentsResultEntry(
                printingAllowedCheckService.getPrintingAllowed(orderDate,orderDate,vo.getLineGroupId(),vo.getDeliveryTime()),
                workshopNonLocalShipmentsService.queryListForApp(vo));
    }

    @RequestMapping(value = "/exportZip",method = RequestMethod.POST)
    @ApiModelProperty(value = "生产组外地发货单Zip", notes = "生产组外地发货单Zip")
    public void  exportZip(@RequestBody List<ProductShipmentsRequestVo> requestVo, HttpServletRequest request, HttpServletResponse response) throws IOException {
        QYAssert.isTrue(SpringUtil.isNotEmpty(requestVo),"请选择要导出的数据");
        printingAllowedCheckService.isPrintingAllowed(requestVo.get(0));
        Map<String,String> strListPdf = workshopNonLocalShipmentsService.getStrListPdf(requestVo);

        Map<String,File> fileList = new HashMap<>();
        strListPdf.forEach((key,value)->{
            File file = new File(key);
            fileList.put(value,file);
        });

        String pdfSave = customerProperties.getAbsoluteSavePath();
        String savePath = customerProperties.getSavePath();

        String zipFilePath = pdfSave + savePath + "生产组外地发货单.zip";

        try {
            PdfUtils.exportZip(response,fileList,zipFilePath,"生产组外地发货单");
        }catch (Exception exception){
            exception.printStackTrace();
        }
    }
    @RequestMapping(value = "/exportPdf",method = RequestMethod.POST)
    @ApiModelProperty(value = "生产组外地发货单pdf", notes = "生产组外地发货单pdf")
    public void  exportPdf(@RequestBody ProductShipmentsRequestVo requestVo, HttpServletRequest request, HttpServletResponse response) throws IOException {
        printingAllowedCheckService.isPrintingAllowed(requestVo);
        String strListPdf = workshopNonLocalShipmentsService.getStrPdf(requestVo);

        File file = new File(strListPdf);
        try {
            PdfUtils.exportPdf(response,file,null);
        }catch (Exception exception){
            exception.printStackTrace();
        }
    }
}

package com.pinshang.qingyun.orderreport.enums;

import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 导出 Excel列
 *
 * <AUTHOR>
 * @date 2024/04/22/0022 13:48
 */
public enum QyEasyExcelExportTitleEnums {


    ORDER_REPORT_REPLENISHMENT_EXPORT("补货数据汇总", Stream.of(
            "commodityFactoryName", "commodityWorkshopName", "commodityCode", "commodityNameSpec","commodityFlowshopName","totalCommodityNum").collect(Collectors.toList())),


    ;

    private String title;
    private String sheetName;
    private List<String> columns;

    private QyEasyExcelExportTitleEnums(String title, List<String> columns) {
        this.columns = columns;
        this.title = title;
    }
    private QyEasyExcelExportTitleEnums(String title,String sheetName, List<String> columns) {
        this.columns = columns;
        this.sheetName=sheetName;
        this.title = title;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public List<String> getColumns() {
        return columns;
    }

    public void setColumns(List<String> columns) {
        this.columns = columns;
    }

    public String getSheetName() {
        return sheetName;
    }

    public void setSheetName(String sheetName) {
        this.sheetName = sheetName;
    }
}

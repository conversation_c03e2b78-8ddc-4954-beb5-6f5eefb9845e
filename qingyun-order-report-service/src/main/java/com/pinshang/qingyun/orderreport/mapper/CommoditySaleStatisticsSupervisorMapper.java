package com.pinshang.qingyun.orderreport.mapper;

import com.pinshang.qingyun.base.mybatis.MyMapper;
import com.pinshang.qingyun.orderreport.model.CommoditySaleStatisticsSupervisor;
import com.pinshang.qingyun.orderreport.vo.CommoditySaleStatisticsItemReqVo;
import com.pinshang.qingyun.orderreport.vo.CommoditySaleStatisticsItemRespVo;
import com.pinshang.qingyun.orderreport.vo.CommoditySaleStatisticsReqVo;
import com.pinshang.qingyun.orderreport.vo.CommoditySaleStatisticsRespVo;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import javax.xml.crypto.Data;
import java.util.Date;
import java.util.List;

@Repository
public interface CommoditySaleStatisticsSupervisorMapper extends MyMapper<CommoditySaleStatisticsSupervisor> {

    /**
     * 生成 产品销售汇总--商品表督导明细
     * @param orderTime
     * @return
     */
    List<CommoditySaleStatisticsSupervisor> generateCommoditySaleStatisticsSupervisor(@Param("orderTime") Date orderTime);

    /**
     * 生成 产品销售汇总--商品表督导明细(补偿)
     * @param orderTime
     * @return
     */
    List<CommoditySaleStatisticsSupervisor> generateCommoditySaleStatisticsSupervisorMakeup(@Param("orderTime") String orderTime, @Param("commodityId")Long commodityId);



    /**
     * 查询督导商品汇总数据
     * @param vo
     * @return
     */
    List<CommoditySaleStatisticsRespVo> queryCommoditySaleDataBySupervisor(@Param("vo") CommoditySaleStatisticsReqVo vo);

    /**
     * 查询各客户类型商品数据明细
     * @param vo
     * @return
     */
    List<CommoditySaleStatisticsItemRespVo> queryCommoditySaleItemDataBySupervisor(@Param("vo") CommoditySaleStatisticsReqVo vo);

}

package com.pinshang.qingyun.orderreport.util;

import com.itextpdf.text.DocumentException;
import com.itextpdf.text.Font;
import com.itextpdf.text.pdf.BaseFont;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.core.io.ClassPathResource;
import org.springframework.core.io.InputStreamResource;

import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.net.URLEncoder;
import java.util.List;
import java.util.Map;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

public class PdfUtils {

    static Logger logger = LoggerFactory.getLogger(PdfUtils.class);

    public static BaseFont chineseBaseFont() {
        ClassPathResource pathResource = new ClassPathResource("simhei.ttf");
        String absolutePath = pathResource.getFilename();
        //FontFactory.register(absolutePath);
        try {
            return BaseFont.createFont(absolutePath, BaseFont.IDENTITY_H, BaseFont.EMBEDDED);
        } catch (DocumentException e) {
            e.printStackTrace();
        } catch (IOException e) {
            e.printStackTrace();
        }
        return null;
    }

    /**
     * 获取中文字体
     *
     * @param fontSize 指定中文字体大小
     * @return 返回中文字体
     * @throws IOException
     * @throws DocumentException
     */
    public static Font chineseFont(float fontSize) throws IOException, DocumentException {
        ClassPathResource pathResource = new ClassPathResource("simhei.ttf");
        String absolutePath = pathResource.getFilename();
        //FontFactory.register(absolutePath);
        BaseFont bf = BaseFont.createFont(absolutePath, BaseFont.IDENTITY_H, BaseFont.EMBEDDED);
        return new Font(bf, fontSize, Font.NORMAL);// 中文字体
    }

    public static Font chineseSTFont(float fontSize) throws IOException, DocumentException {
        ClassPathResource pathResource = new ClassPathResource("simhei.ttf");
        String absolutePath = pathResource.getFilename();
        //FontFactory.register(absolutePath);
        BaseFont bf = BaseFont.createFont(absolutePath, BaseFont.IDENTITY_H, BaseFont.EMBEDDED);
        return new Font(bf, fontSize, Font.NORMAL);// 中文字体
    }

    public static Font chineseSTFont(float fontSize, int fontStyle) throws IOException, DocumentException {
        ClassPathResource pathResource = new ClassPathResource("simhei.ttf");
        String absolutePath = pathResource.getFilename();
        //FontFactory.register(absolutePath);
        BaseFont bf = BaseFont.createFont(absolutePath, BaseFont.IDENTITY_H, BaseFont.EMBEDDED);
        return new Font(bf, fontSize, fontStyle);// 中文字体
    }

    public static Font chineseSTFANGSOFont(float fontSize, int fontStyle) throws IOException, DocumentException {
        ClassPathResource pathResource = new ClassPathResource("simhei.ttf");
        String absolutePath = pathResource.getFilename();
        BaseFont bf = BaseFont.createFont(absolutePath, BaseFont.IDENTITY_H, BaseFont.EMBEDDED);
        return new Font(bf, fontSize, fontStyle);// 中文字体
    }

    /**
     * 导出zip
     * @param response
     * @param fileList
     * @param zipFilePath
     * @param fileName
     * @throws Exception
     */
    public static void exportZip(HttpServletResponse response, Map<String,File> fileList, String zipFilePath, String fileName) throws Exception {
        BufferedInputStream bis = null;
        FileInputStream fileInputStream = null;
        try {
            response.setHeader("content-type", "application/octet-stream");

            response.setHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode(fileName+".zip", "UTF-8"));

            response.setContentType("application/x-download");

            /**
             * 多个 pdf 打包zip
             */
            fileInputStream = PdfUtils.addFileToZipPackage(fileList, zipFilePath);

            bis = new BufferedInputStream(fileInputStream);
            byte[] buffer = new byte[1024];
            int i = bis.read(buffer);

            OutputStream os = response.getOutputStream();
            while (i != -1) {
                os.write(buffer, 0, i);
                i = bis.read(buffer);
            }
        } catch (IOException e) {
            e.printStackTrace();
        }finally {
            if (bis != null) {
                bis.close();
            }
            if (fileInputStream != null) {
                fileInputStream.close();
            }
            /**
             * 删除临时文件
             */
            new File(zipFilePath).delete();
        }
    }
    /**
     * 导出pdf 打包zip(多个打包)
     * @param pdfFile
     * @param zipFilePath
     * @return
     * @throws IOException
     */
    public static FileInputStream addFileToZipPackage(Map<String,File> pdfFile, String zipFilePath) throws IOException {

        ZipOutputStream zipOutputStream=null;
        FileInputStream fileInputStream=null;

        File zipFile=new File(zipFilePath);

        if(zipFile.exists()){
            zipFile.delete();
        }else {
            zipFile.createNewFile();
        }
        FileOutputStream fileOutputStream = new FileOutputStream(zipFile);
        zipOutputStream = new ZipOutputStream(fileOutputStream);
        for(String key: pdfFile.keySet()){
            File file = pdfFile.get(key);
            byte[] buf = new byte[1024];
            zipOutputStream.putNextEntry(new ZipEntry(key));
            int len;
            fileInputStream = new FileInputStream(file);
            while ((len = fileInputStream.read(buf)) != -1) {
                zipOutputStream.write(buf, 0, len);
            }
            fileInputStream.close();
            file.delete();
        }
        zipOutputStream.close();

        fileInputStream = new FileInputStream(zipFile) ;

        fileOutputStream.close();

        return fileInputStream;
    }

    /**
     * 导出 pdf(单个)
     * @param response
     * @param file
     */
    public static void exportPdf(HttpServletResponse response, File file,String fileName){
        BufferedOutputStream bos = null;
        FileInputStream fileinputstream = null;
        try{
            if(null == fileName){
                fileName = file.getName();
            }
            fileinputstream = new FileInputStream(file);
            response.setContentType("application/x-download");
            response.setContentType("*/*;charset=gbk");
            response.setHeader("Content-Disposition","attachment; filename=" + URLEncoder.encode(fileName, "UTF-8"));
            bos = new BufferedOutputStream(response.getOutputStream());
            int l = 0;
            byte abyte0[] = new byte[2048];
            while(-1 != (l = fileinputstream.read(abyte0,0,abyte0.length))){
                bos.write(abyte0,0,l);
            }
            bos.close();
            fileinputstream.close();
        }catch(Exception ex){
            ex.printStackTrace();
        }finally{
            try{
                if(bos != null){
                    bos.close();
                }
            }catch(Exception ex){
                ex.printStackTrace();
            }
            try{
                if(fileinputstream != null){
                    fileinputstream.close();
                }
            }catch(Exception ex){
                ex.printStackTrace();
            }
        }
    }
}

package com.pinshang.qingyun.orderreport.enums;

/**
 * 产品销售汇总 汇总类型
 */
public enum ProductSaleStatisticsTypeEnums {
    DEFAULT("默认没有汇总",0),
	COMMODITY_STATS("商品销售汇总", 1),
	SUPERVISOR_STATS("督导汇总", 2),
	FACTORY_DELIVERY_STATS("工厂发货汇总", 3),
	;
    private String name;
    private Integer code;

    ProductSaleStatisticsTypeEnums(String name, Integer code) {
        this.name = name;
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public Integer getCode() {
        return code;
    }
}

package com.pinshang.qingyun.orderreport.pdf;

import com.itextpdf.text.*;
import com.itextpdf.text.pdf.PdfPCell;
import com.itextpdf.text.pdf.PdfPTable;
import com.itextpdf.text.pdf.PdfWriter;
import com.pinshang.qingyun.orderreport.config.CustomerProperties;
import com.pinshang.qingyun.orderreport.util.ListUtils;
import com.pinshang.qingyun.orderreport.util.PdfUtils;
import com.pinshang.qingyun.orderreport.vo.OperatorOrderReqVo;
import com.pinshang.qingyun.orderreport.vo.OperatorOrderRespVo;
import org.joda.time.DateTime;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.UUID;

/**
 * <AUTHOR>
 * @date 2019/3/13 14:43.
 */
@Component
public class OperatorOrderStatisticalPdfCreator {

    private static final Logger LOGGER = LoggerFactory.getLogger(OperatorOrderStatisticalPdfCreator.class);

    private final CustomerProperties customerProperties;

    private static final Integer PAGE_ROW = 45;

    @Autowired
    public OperatorOrderStatisticalPdfCreator(CustomerProperties customerProperties) {
        this.customerProperties = customerProperties;
    }

    /**
     * 生成 操作员订单统计PDF
     * @param pdfContent 要生成PDF的内容
     * @param vo  参数VO
     * @return 返回生成PDF的文件保存地址
     */
    public String create(List<OperatorOrderRespVo> pdfContent, OperatorOrderReqVo vo) {
        Date date1 = new Date();
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String printTime = format.format(date1);
        String absoluteSavePath = customerProperties.getAbsoluteSavePath();
        String savePath = customerProperties.getSavePath();
        Date date = new DateTime().plusDays(1).toDate();
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
        String dateDir = dateFormat.format(date);
        File file = new File(absoluteSavePath + "/" + savePath + "/" + dateDir);
        if (!file.exists()) {
            if (!file.mkdirs()) {
                LOGGER.error("创建用于保存PDF的目录时失败,请检查权限.");
                return null;
            }
        }
        String fileName = extractFileName(vo);
        Document document = new Document();
        document.setMargins(15, 15, 15, 15);
        try {
            FileOutputStream outputStream = new FileOutputStream(file.getAbsolutePath() + "/" + fileName);
            PdfWriter.getInstance(document, outputStream);
            document.open();
            Font chineseFont = PdfUtils.chineseFont(11);
            PdfPTable tableTitle = proceedTableTitle(vo, chineseFont);
            List<List<OperatorOrderRespVo>> pageData = ListUtils.splitList(pdfContent, PAGE_ROW);
            int page = 1;
            int totalPage = pageData.size();
            for (List<OperatorOrderRespVo> list : pageData) {
                document.add(tableTitle);
                float[] cellWidth = { 90, 50 };
                PdfPTable table = new PdfPTable(cellWidth);
                PdfPCell cell = new PdfPCell(new Phrase("操作员名称", chineseFont));
                table.setLockedWidth(true);
                table.setTotalWidth(cellWidth[0] + cellWidth[1]);
                table.addCell(cell);
                table.setHorizontalAlignment(Element.ALIGN_LEFT);
                cell = new PdfPCell(new Phrase("合计", chineseFont));
                table.addCell(cell);
                for (OperatorOrderRespVo responseVo : list) {
                    table.addCell(new Phrase(responseVo.getOperatorName(), chineseFont));
                    table.addCell(new Phrase(responseVo.getOrderQuantity() + "", chineseFont));
                }
                // 分页的table
                PdfPTable pageTable = new PdfPTable(1);
                pageTable.setHorizontalAlignment(Element.ALIGN_LEFT);
                PdfPCell tableBottom = new PdfPCell(new Phrase("第 " + page + " 页, 共 " + totalPage + "页.                打印时间: " + printTime, chineseFont));
                tableBottom.setBorder(0);
                pageTable.addCell(tableBottom);

                table.addCell(tableBottom);
                document.add(table);
                document.add(pageTable);
                page++;
                if (page <= totalPage) {
                    document.newPage();
                }
            }
            document.close();
        } catch (DocumentException | IOException e) {
            LOGGER.error("生成PDF时异常.{}", e.getMessage());
            return null;
        }
        return String.format("%s%s%s",file.getAbsolutePath(),"/",fileName);
//        return file.getAbsolutePath() + fileName;
//        return savePath + "/" + dateDir + "/" + fileName;
    }

    /**
     * 生成PDF表头
     * @param vo 参数
     * @param font 中文字体
     * @return 返回表头
     */
    private PdfPTable proceedTableTitle(OperatorOrderReqVo vo, Font font) throws IOException, DocumentException {
        float[] cellWidth = { 90, 240 };
        PdfPTable table = new PdfPTable(cellWidth);
        table.setTotalWidth(cellWidth[0] + cellWidth[1]);
        table.setHorizontalAlignment(Element.ALIGN_LEFT);
        table.setSpacingBefore(4);
        table.setLockedWidth(true);
        PdfPCell cell = new PdfPCell(new Phrase("操作员订单统计", PdfUtils.chineseFont(18)));
        cell.setColspan(2);
        cell.setBorder(0);
        cell.setFixedHeight(27);
        cell.setHorizontalAlignment(Element.ALIGN_CENTER);
        cell.setVerticalAlignment(Element.ALIGN_CENTER);
        table.addCell(cell);

        String operatorName = vo.getOperatorName() == null ? "" : vo.getOperatorName();
        cell = new PdfPCell(new Phrase("操作员: " + operatorName, font));
        cell.setBorder(0);
        table.addCell(cell);

        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
        Date startDate = vo.getStartDate();
        Date endDate = vo.getEndDate();
        cell = new PdfPCell(new Phrase("订单日期: " + dateFormat.format(startDate) + " 至 " + dateFormat.format(endDate), font));
        cell.setBorder(0);
        table.addCell(cell);
        return table;
    }

    private String extractFileName(OperatorOrderReqVo vo) {
        Date startDate = vo.getStartDate();
        Date endDate = vo.getEndDate();
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
        String fileName;
        if (startDate != null && endDate != null) {
            String start = dateFormat.format(startDate);
            String end = dateFormat.format(endDate);
            fileName = start + "_" + end;
        } else {
            Date date = new DateTime().plusDays(1).toDate();
            String orderDate = dateFormat.format(date);
            fileName = orderDate;
        }
        String substring = UUID.randomUUID().toString().substring(0, 8);
        return fileName + "_" + substring + ".pdf";
    }
}

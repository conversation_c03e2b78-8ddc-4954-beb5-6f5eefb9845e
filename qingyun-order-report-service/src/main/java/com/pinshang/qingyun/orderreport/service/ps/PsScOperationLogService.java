package com.pinshang.qingyun.orderreport.service.ps;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.pinshang.qingyun.base.configure.expand.QYAssert;
import com.pinshang.qingyun.orderreport.mapper.PsScOperationLogMapper;
import com.pinshang.qingyun.orderreport.vo.ps.PsScOperationLogVO;
import com.pinshang.qingyun.orderreport.vo.ps.QueryOperationLogVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * <AUTHOR>
 * @Version 0.1.0
 * @Date 2021/3/1 15:17
 * @Copyright © 2019-2021 qm
 * @Description -- 类说明
 **/
@Service
public class PsScOperationLogService {

    @Autowired
    private PsScOperationLogMapper psScOperationLogMapper;

    public PageInfo<PsScOperationLogVO> queryOperationList(@RequestBody QueryOperationLogVO dto) {
        QYAssert.notNull(dto.getBusinessType(), "业务类型不能为空！");
        QYAssert.hasLength(dto.getOperationStartTime(), "日志起始时间不能为空！");
        QYAssert.hasLength(dto.getOperationEndTime(), "日志结束时间不能为空！");

        return PageHelper.startPage(dto.getPageNo(), dto.getPageSize()).doSelectPageInfo(() -> {
            psScOperationLogMapper.queryOperationList(dto);
        });
    }
}

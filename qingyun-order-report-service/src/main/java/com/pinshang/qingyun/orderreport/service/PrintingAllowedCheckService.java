package com.pinshang.qingyun.orderreport.service;

import com.pinshang.qingyun.base.configure.expand.QYAssert;
import com.pinshang.qingyun.box.utils.DateUtil;
import com.pinshang.qingyun.box.utils.SpringUtil;
import com.pinshang.qingyun.common.dto.DictionaryODTO;
import com.pinshang.qingyun.common.service.DictionaryClient;
import com.pinshang.qingyun.order.dto.DeliveryTimeLineIDTO;
import com.pinshang.qingyun.order.service.DeliveryTimeClient;
import com.pinshang.qingyun.orderreport.vo.DeliveryListPreviewReqVo;
import com.pinshang.qingyun.orderreport.vo.ProductShipmentsRequestVo;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/8/15 12:36
 */
@Service
public class PrintingAllowedCheckService {

    @Autowired
    private DictionaryClient dictionaryClient;

    @Autowired
    private DeliveryTimeClient deliveryTimeClient;


    /***
     * 涉及功能:
     *   送货清单
     *   提货单
     * check是否可以进行 打印、PDF导出
     */
    public void isPrintingAllowed(List<DeliveryListPreviewReqVo> vo) {
        Date orderDate = vo.get(0).getOrderDate();
        if (!this.checkDateLatest(orderDate, orderDate)) {
            return;
        }
        List<Long> lineIdList = vo.stream().filter(p -> p.getLineId() != null).map(p -> p.getLineId()).distinct().collect(Collectors.toList());
        if (SpringUtil.isEmpty(lineIdList)) {
            return;
        }
        String endTime = deliveryTimeClient.selectDeliveryTimeEndTimeByLineIds(new DeliveryTimeLineIDTO(lineIdList));
        if (StringUtils.isBlank(endTime)) {
            return;
        }
        int buf = getBuf();
        String str = this.compareToDateAndGet(DateUtil.parseDate(endTime, "HH:mm"), buf);
        QYAssert.isTrue(StringUtils.isBlank(str), String.format("截单后%s才能打印和导出，请于%s后再次操作，此前可以预览。", (buf > 0 ? "再过" + buf + "分钟" : ""), str));
    }


    /***
     * 涉及功能:
     *   生产组发货单
     *   生产组外地发货单
     *   产品发货总量
     * check是否可以进行 打印、PDF导出
     * @param vo
     */
    public void isPrintingAllowed(ProductShipmentsRequestVo vo) {
        String endTime = getEndTime(vo.getStartOrderDate(), vo.getEndOrderDate(), vo.getLineGroupId(), vo.getDeliveryTime());
        if (StringUtils.isBlank(endTime)) {
            return;
        }
        int buf = getBuf();
        String str = this.compareToDateAndGet(DateUtil.parseDate(endTime, "HH:mm"), buf);
        QYAssert.isTrue(StringUtils.isBlank(str), String.format("截单后%s才能打印和导出，请于%s后再次操作，此前可以预览。", (buf > 0 ? "再过" + buf + "分钟" : ""), str));
    }

    /***
     * 涉及功能:
     *   清美助手 生产组发货单
     *   清美助手 生产组外地发货单
     */
    public String getPrintingAllowed(Date startOrderDate, Date endOrderDate, Long lineGroupId, String deliveryTime) {
        String endTime = getEndTime(startOrderDate, endOrderDate, lineGroupId, deliveryTime);
        if (StringUtils.isBlank(endTime)) {
            return null;
        }
        String str = this.compareToDateAndGet(DateUtil.parseDate(endTime, "HH:mm"), getBuf());
        if (StringUtils.isBlank(str)) {
            return "";
        }
        return String.format("请以%s后的数据为准", str);
    }


    private String getEndTime(Date startOrderDate, Date endOrderDate, Long lineGroupId, String deliveryTime) {

        if (!this.checkDateLatest(startOrderDate, endOrderDate)) {
            return null;
        }
        String endTime = null;
        if (lineGroupId != null) {
            endTime = deliveryTimeClient.selectEndTimeById(lineGroupId);
        } else {
            endTime = deliveryTime;
        }
        return endTime;
    }

    /***
     * 判断送货日期 是不是都等于等于 T+1   是才验字是否能打印 否则走原来逻辑
     * @param startOrderDate
     * @param endOrderDate
     * @return
     */
    private boolean checkDateLatest(Date startOrderDate, Date endOrderDate) {
        if (startOrderDate == null || endOrderDate == null) {
            return false;
        }
        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.DAY_OF_MONTH, 1);
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);
        Date now = calendar.getTime();
        return now.compareTo(startOrderDate) == 0 && now.compareTo(endOrderDate) == 0;
    }

    /***
     * 送货日期=T+1时，
     *   若选了线路组，当前时间t≤线路组截单时间+ x 分钟，则操作失败，并弹框提示；
     *   反之，则正常走原有的打印、导出流程；
     *   若未选择线路组，选择了“发货时间”，当前时间t≤发货时间+ x 分钟，则则操作失败，并弹框提示；
     *   反之，则正常走原有的打印、导出流程；
     * 送货日期≠T+1时，正常走原有的打印、导出流程
     *
     * @param localDate
     * @param buff  x=xdaPreOrderDelayNN字典值+OrderSyncbuf字典值
     * @return
     */
    private String compareToDateAndGet(Date localDate, int buff) {
        if (localDate == null) {
            return null;
        }
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(localDate);
        calendar.add(Calendar.MINUTE, buff);
        SimpleDateFormat dateFormat = new SimpleDateFormat("HH:mm");
        String calendarTimeStr = dateFormat.format(calendar.getTime());
        int calendarTime = Integer.parseInt(calendarTimeStr.replace(":", ""));
        int nowTime = Integer.parseInt(dateFormat.format(new Date()).replace(":", ""));
        if (nowTime <= calendarTime) {
            return calendarTimeStr;
        }
        return null;
    }

    /***
     * 获取字典里的缓冲时间 (分钟)
     * @return
     */
    private int getBuf() {
        int buf = 0;
        DictionaryODTO odtoBuf = dictionaryClient.getDictionaryByCode("OrderSyncbuf");
        if(odtoBuf !=null && StringUtils.isNotBlank(odtoBuf.getOptionValue()) && odtoBuf.getOptionState()!=null && 1 == odtoBuf.getOptionState()){
            buf = buf + Integer.parseInt(odtoBuf.getOptionValue());
        }

        DictionaryODTO odtoNN = dictionaryClient.getDictionaryByCode("xdaPreOrderDelayNN");
        if(odtoNN !=null && StringUtils.isNotBlank(odtoNN.getOptionValue()) && odtoNN.getOptionState()!=null && 1 == odtoNN.getOptionState()){
            buf = buf + Integer.parseInt(odtoNN.getOptionValue());
        }
        return buf;
    }
}

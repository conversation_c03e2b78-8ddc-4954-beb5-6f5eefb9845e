package com.pinshang.qingyun.orderreport.mapper.entry;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.github.pagehelper.PageInfo;
import com.pinshang.qingyun.base.configure.codec.DecimalSerializerKeep2;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * 清美人客户订货统计查询
 */
@Data
@NoArgsConstructor
public class StoreOrderStatisticsEntry {

    @ApiModelProperty("订货客户总数")
    private Integer totalStoreQuantity;

    @ApiModelProperty("订货总金额")
    @JsonSerialize(using = DecimalSerializerKeep2.class)
    private BigDecimal totalOrderAmount;

    @ApiModelProperty("客户订货统计列表")
    private PageInfo<StoreOrderListEntry> pageList;

    public Integer getTotalStoreQuantity() {
        return totalStoreQuantity==null?0:totalStoreQuantity;
    }

    public BigDecimal getTotalOrderAmount() {
        return totalOrderAmount;
    }
}

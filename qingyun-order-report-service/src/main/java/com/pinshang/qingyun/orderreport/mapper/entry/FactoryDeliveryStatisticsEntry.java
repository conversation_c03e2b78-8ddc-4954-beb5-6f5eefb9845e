package com.pinshang.qingyun.orderreport.mapper.entry;

import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @description 查询商品销售统计及商品明细数据
 * @date 2020/9/2
 */
@Data
public class FactoryDeliveryStatisticsEntry {

    /** 线路ID **/
    private Long lineId;

    /** 线路名称 **/
    private String lineName;

    /** 线路组ID **/
    private Long lineGroupId;

    /** 送货员ID **/
    private Long deliverymanId;

    /** 送货员名称 **/
    private String deliverymanName;

    /** 工厂Id **/
    private Long factoryId;

    /** 工厂名称 **/
    private String factoryName;

    /** 客户类型ID **/
    private Long storeTypeId;

    /** 客户类型数量小计 **/
    private BigDecimal totalQuantity;

    /** 客户类型金额小计 **/
    private BigDecimal totalAmount;

}

package com.pinshang.qingyun.orderreport.model;

import lombok.Data;

import javax.persistence.Id;
import javax.persistence.Table;
import java.math.BigDecimal;
import java.util.Date;

/**
 * @Description: 产品销售汇总--月报表督导明细
 * @Author: fangting
 * @CreateDate: 2020/8/10 15:44
 */
@Table(name = "t_tj_commodity_sale_statistics_month_supervisor")
@Data
public class CommoditySaleStatisticsMonthSupervisor {

    @Id
    private Long id;

    /**
     * 送货月份(yyyy-MM，例：2019-09)
     */
    private String saleMonth;

    /**
     * 商品id
     */
    private Long commodityId;

    /**
     * 督导ID
     */
    private Long supervisorId;

    /**
     * 客户类型ID
     */
    private Long storeTypeId;

    /**
     * 数量小计
     */
    private BigDecimal totalQuantity;

    /**
     * 金额小计
     */
    private BigDecimal totalAmount;

    /*** 公司id */
    private Long companyId;
}

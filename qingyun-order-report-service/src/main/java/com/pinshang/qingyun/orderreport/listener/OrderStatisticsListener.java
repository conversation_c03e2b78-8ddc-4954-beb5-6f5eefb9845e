package com.pinshang.qingyun.orderreport.listener;

import com.alibaba.fastjson.JSON;
import com.pinshang.qingyun.base.annotations.OnlineSwitchWatcher;
import com.pinshang.qingyun.base.api.QYApplicationContext;
import com.pinshang.qingyun.kafka.base.BaseKafkaOnlineSwitchProcessor;
import com.pinshang.qingyun.kafka.base.KafkaMessageWrapper;
import com.pinshang.qingyun.kafka.base.KafkaTopicConstant;
import com.pinshang.qingyun.orderreport.model.Order;
import com.pinshang.qingyun.orderreport.service.OrderService;
import com.pinshang.qingyun.orderreport.service.TjOrderSendKafkaService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2019/1/14 15:30.
 */
@Component
@Slf4j
@OnlineSwitchWatcher
public class OrderStatisticsListener extends BaseKafkaOnlineSwitchProcessor {

    private final OrderService orderService;
    @Autowired
    private TjOrderSendKafkaService orderSendKafkaService;

    public OrderStatisticsListener(OrderService orderService) {
        this.orderService = orderService;
    }

//    @KafkaListener(id=KafkaTopicConstant.STATISTICAL_ORDER_TOPIC,topics = KafkaTopicConstant.STATISTICAL_ORDER_TOPIC)
//    public void compatibleListener(String message) {
//        processMessage(message);
//    }

    @KafkaListener(id="${application.name.switch}" + KafkaTopicConstant.STATISTICAL_ORDER_TOPIC,topics = "${application.name.switch}" + KafkaTopicConstant.STATISTICAL_ORDER_TOPIC)
    public void listener(String message) {
        processMessage(message);
    }

    private void processMessage(String message) {
        if (log.isInfoEnabled()) {
            log.info("topic:[{}],message:[{}]", KafkaTopicConstant.STATISTICAL_ORDER_TOPIC, message);
        }
        KafkaMessageWrapper wrapper = JSON.parseObject(message, KafkaMessageWrapper.class);
        if(wrapper.getData()!=null){
            String string = wrapper.getData().toString();
            Order order = JSON.parseObject(string, Order.class);

            if (null != order) {
                //鲜达等渠道，修改订单时发消息UpdateTime有误，所以统计系统统一处理
            	order.setUpdateTime(new Date());
            }
            //发消息同步到ES,已不再使用无需再发到ES
//            orderSendKafkaService.sendEsOrderMsg(order,wrapper.getOptionType());

            switch (wrapper.getOptionType()) {
                case INSERT:
                    orderService.processCombTypeIsNull(order,wrapper.getUuid());
                    orderService.processInsertOrder(order);
                    break;
                case UPDATE:
                    orderService.processCombTypeIsNull(order,wrapper.getUuid());
                    orderService.processUpdateOrder(order);
                    break;
                case CANCEL:
                    orderService.processCancelOrder(order);
                    break;
                default:
                    log.error("不能处理该消息:{}", message);
                    break;
            }

        }else{
            log.error("错误的订单消息--topic:[{}],message:[{}]", KafkaTopicConstant.STATISTICAL_ORDER_TOPIC, message);
        }

    }

    @Override
    public List<String> getKafkaIds() {
        return Arrays.asList(
                QYApplicationContext.applicationNameSwitch + KafkaTopicConstant.STATISTICAL_ORDER_TOPIC
        );
    }
}

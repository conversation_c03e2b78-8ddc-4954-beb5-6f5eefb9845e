package com.pinshang.qingyun.orderreport.controller;

import com.github.pagehelper.PageInfo;
import com.pinshang.qingyun.base.configure.expand.QYAssert;
import com.pinshang.qingyun.infrastructure.exportfile.cache.starter.FileCacheQuery;
import com.pinshang.qingyun.infrastructure.exportfile.cache.starter.FileCacheQueryParameter;
import com.pinshang.qingyun.orderreport.mapper.entry.OrderDetailEntry;
import com.pinshang.qingyun.orderreport.service.OrderDetailService;
import com.pinshang.qingyun.orderreport.vo.LateOrderReqVo;
import com.pinshang.qingyun.orderreport.vo.LateOrderRespVo;
import com.pinshang.qingyun.orderreport.vo.OrderDetailReqVo;
import io.swagger.annotations.ApiModelProperty;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

/**
 * <AUTHOR>
 * @Date 2019/3/11 10:44
 */
@RestController
@RequestMapping("/statistical/order")
public class OrderDetailController {

    @Autowired
    private OrderDetailService orderDetailService;

    /**
     * 订单明细列表数据查询
     * @param orderDetailReqVo
     * @return
     */
    //todo 订单明细 、ES
    @PostMapping("/queryList")
    @ApiModelProperty(value = "订单明细查询", notes = "订单明细查询")
    public PageInfo<OrderDetailEntry> queryList(@RequestBody OrderDetailReqVo orderDetailReqVo){
        QYAssert.isTrue(orderDetailReqVo != null ,"查询参数为空异常!");
        QYAssert.isTrue(orderDetailReqVo.getStartOrderDate() != null,"起止日期不能为空!");
        return orderDetailService.queryList(orderDetailReqVo);
    }

    /**
     * 导出订单明细
     * @param orderDetailReqVo 实体
     * @param httpServletResponse httpServletResponse
     * @throws IOException IOException
     */
    @GetMapping("/exportOrderDetail")
    @ApiModelProperty(value = "订单明细查询-导出", notes = "订单明细查询-导出")
    @FileCacheQuery(bizCode = "ORDER_DETAIL_EXPORT")
    public void exportOrderDetail(OrderDetailReqVo orderDetailReqVo, HttpServletResponse httpServletResponse) throws Exception {
        QYAssert.isTrue(orderDetailReqVo != null ,"查询参数为空异常!");
        QYAssert.isTrue(orderDetailReqVo.getStartOrderDate() != null,"起止日期不能为空!");
        orderDetailReqVo.setPageNo(1);
        orderDetailReqVo.setPageSize(Integer.MAX_VALUE);
        orderDetailService.exportOrderDetail(orderDetailReqVo,httpServletResponse);
    }

    /**
     * 晚订货查询
     * @param lateOrderReqVo
     */
    @PostMapping("/queryLateOrder")
    @ApiModelProperty(value = "晚订货查询", notes = "晚订货查询")
    public PageInfo<LateOrderRespVo> queryLateOrder(@RequestBody LateOrderReqVo lateOrderReqVo) {
        return orderDetailService.queryLateOrder(lateOrderReqVo);
    }

    /**
     * 导出晚订货查询
     * @param lateOrderReqVo 实体
     * @param response response
     * @throws IOException IOException
     */
    @GetMapping("/exportLateOrder")
    @ApiModelProperty(value = "晚订货查询-导出", notes = "晚订货查询-导出")
    @FileCacheQuery(bizCode = "OR_LATE_ORDER_E")
    public void exportLateOrder(@FileCacheQueryParameter LateOrderReqVo lateOrderReqVo, HttpServletResponse response) throws IOException {
        orderDetailService.exportLateOrder(lateOrderReqVo, response);
    }
}

package com.pinshang.qingyun.orderreport.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.pinshang.qingyun.base.page.Pagination;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;
import java.util.List;

/**
 * 送货单
 * <AUTHOR>
 * @Date 2019/3/5 16:24
 */
@Data
@NoArgsConstructor
public class DeliveryOrderReqVo extends Pagination {
    /**
     * 客户编码
     */
	@ApiModelProperty("客户编码")
    private String storeCode;
    /**
     * 客户编码集合
     */
	@ApiModelProperty("客户编码集合")
    private List<String> storeCodeList;
    /**
     * 客户名称
     */
//	@ApiModelProperty("客户名称")
//    private String storeName;
    /**
     * 客户类型
     */
	@ApiModelProperty("客户类型")
    private Long storeTypeId;
//    /**
//     * 结账客户Id
//     */
//	@ApiModelProperty("结账客户id")
//    private Long settlementCustomerId;
    /**
     * 结账客户Code
     */
	@ApiModelProperty("结账客户Code")
    private String settlementCustomerCode;
    /**
     * 送货员
     */
	@ApiModelProperty("送货员")
    private Long deliveryManId;
    /**
     * 线路组
     */
	@ApiModelProperty("线路组")
    private Long lineGroupId;
    /**
     * 送货起始日期
     */
	@ApiModelProperty("送货起始日期")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date startOrderDate;
    /**
     * 送货截止日期
     */
	@ApiModelProperty("送货截止日期")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date endOrderDate;
    /**
     * 渠道
     */
	@ApiModelProperty("渠道")
    private Long storeChannelId;
    /**
     * 订单编码
     */
	@ApiModelProperty("订单编码")
    private String orderCode;
    /**
     * 督导
     */
	@ApiModelProperty("督导")
    private Long supervisorId;
    /**
     * 操作员
     */
	@ApiModelProperty("操作员")
    private Long operatorId;
    /**
     * 配送批次
     */
	@ApiModelProperty("配送批次")
    private Integer deliveryBatch;
    
    @ApiModelProperty("商品ID")
    private Long commodityId;
    
    /**
     * 排序规则
     */
    @ApiModelProperty("排序规则:'1'按时间倒序排序,'2'按结账客户排序.")
    private Integer sortRule;

    /**
     * 查询标识
     */
    @ApiModelProperty(value = "查询标识", hidden = true)
    private transient Boolean latestFlag;


    @ApiModelProperty("送货时间要求")
    private String receiveTime;

    @ApiModelProperty("主任")
    private Long officeDirectorId;


    /***
     *
     * 之前统计查询其它地方 页面叫 补货数据——必选，3个选项：
     * 不包含补货数据（默认）
     * 单查补货数据
     * 包含补货数据
     *
     * 当时送货单 未涉及 补货数据 查询
     * 后面单独补的需求 送货单页面展示 和下拉数据与之前不一样 找产品确认过   补货数据 变成 是否补货  下拉数据  是 否
     * 是否补货=是，即单查补货数据
     *
     * 是否补货=否，即不包含补货数据
     */
    @ApiModelProperty(" 0-否 代表普通订单 不包含补货数据、 1-是  代表补货订单、 空查所有")
    private Integer orderModeType;

    /** 客户手机号**/
    @ApiModelProperty("客户手机号")
    private String storePhone;
}

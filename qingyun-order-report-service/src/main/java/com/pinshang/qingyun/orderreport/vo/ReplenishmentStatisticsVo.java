package com.pinshang.qingyun.orderreport.vo;

import com.pinshang.qingyun.base.page.Pagination;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * 补货数据汇总查询参数
 */
@Data
public class ReplenishmentStatisticsVo extends Pagination {

    private Long deliveryManId;

    private String deliveryManName;

    private Long deliveryWarehouseId;

    private String deliveryWarehouseName;
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date startDate;
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date endDate;

    private Long userId;

    private Long commodityWorkshopId; //生成组id
    private Long commodityFlowshopId; //车间id
    private Long commodityFactoryId; //工厂id

}

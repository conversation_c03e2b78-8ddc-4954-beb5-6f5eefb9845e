package com.pinshang.qingyun.orderreport.mapper;

import com.pinshang.qingyun.base.mybatis.MyMapper;
import com.pinshang.qingyun.orderreport.model.PsScLineStore;
import com.pinshang.qingyun.orderreport.vo.ps.CommodityGroupCommodityVO;
import com.pinshang.qingyun.orderreport.vo.ps.CommodityGroupLineStoreVO;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface PsScLineStoreMapper extends MyMapper<PsScLineStore> {
    /**
     * 查询线路没有关联的客户
     * @return
     */
    List<CommodityGroupLineStoreVO> selectNotLinkByStoreCodeList(@Param("lineId") Long lineId, @Param("storeCodeList") List<String> storeCodeList);

    /**
     * 商品组与客户互斥逻辑(筛选当前商品组在不同线路下不重复的客户)
     * @return
     */
    List<CommodityGroupLineStoreVO> selectNotRepeatStoreListInCommodityGroup(@Param("commodityGroupId") Long commodityGroupId, @Param("storeList") List<CommodityGroupLineStoreVO> storeList);

    int delCommodityGroupLineStore(@Param("lineId") Long lineId, @Param("storeId") Long storeId);

    List<Long> selectByLineId(@Param("lineId")Long lineId);
}
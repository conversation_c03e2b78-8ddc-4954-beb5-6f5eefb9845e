package com.pinshang.qingyun.orderreport.controller;

import com.alibaba.excel.EasyExcel;
import com.pinshang.qingyun.base.api.QYApplicationContext;
import com.pinshang.qingyun.base.api.TokenInfo;
import com.pinshang.qingyun.base.configure.expand.QYAssert;
import com.pinshang.qingyun.base.constant.RedisKeyPrefixConst;
import com.pinshang.qingyun.base.util.ExcelUtil;
import com.pinshang.qingyun.base.util.FastThreadLocalUtil;
import com.pinshang.qingyun.box.utils.SpringUtil;
import com.pinshang.qingyun.infrastructure.exportfile.cache.starter.FileCacheQuery;
import com.pinshang.qingyun.infrastructure.exportfile.cache.starter.FileCacheQueryParameter;
import com.pinshang.qingyun.orderreport.mapper.entry.FreshStoreProductShipmentsEntry;
import com.pinshang.qingyun.orderreport.model.Store;
import com.pinshang.qingyun.orderreport.service.FreshStoreProductShipmentsService;
import com.pinshang.qingyun.orderreport.vo.FreshStoreProductShipmentsReqVo;
import com.pinshang.qingyun.orderreport.vo.FreshStoreProductShipmentsRespVo;
import com.pinshang.qingyun.smm.dto.user.SelectUserFactoryIdListIDTO;
import com.pinshang.qingyun.smm.service.SMMUserClient;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.joda.time.LocalDate;
import org.redisson.api.RBucket;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.propertyeditors.CustomDateEditor;
import org.springframework.web.bind.WebDataBinder;
import org.springframework.web.bind.annotation.InitBinder;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static java.util.stream.Collectors.groupingBy;

/**
 * <AUTHOR>
 * @Date 2019/3/11 11:51
 */
@RestController
@RequestMapping("/statistical/fresh/productShipments")
@Slf4j
public class FreshStoreProductShipmentsController {
    @Autowired
    private FreshStoreProductShipmentsService freshStoreProductShipmentsService;
    @Autowired
    private SMMUserClient userClient;

    @PostMapping("/queryList")
    public FreshStoreProductShipmentsRespVo queryList(@RequestBody FreshStoreProductShipmentsReqVo reqVo){
        List<Long> factoryIds = new ArrayList<>();
        if(reqVo.getFactoryId() == null){
            factoryIds = userClient.selectUserFactoryIdList(SelectUserFactoryIdListIDTO.onlyDb(reqVo.getLoginUserId()));
            if(SpringUtil.isEmpty(factoryIds)){
                return new FreshStoreProductShipmentsRespVo();
            }
            reqVo.setFactoryIds(factoryIds);
        }
        return freshStoreProductShipmentsService.queryList(reqVo);
    }

    @PostMapping("/queryListV2")
    public List<FreshStoreProductShipmentsEntry> queryListV2(@RequestBody FreshStoreProductShipmentsReqVo vo) {
        List<Long> factoryIds = new ArrayList<>();
        if(vo.getFactoryId() == null){
            factoryIds = userClient.selectUserFactoryIdList(SelectUserFactoryIdListIDTO.onlyDb(vo.getLoginUserId()));
            if(SpringUtil.isEmpty(factoryIds)){
                return new ArrayList<>();
            }
            vo.setFactoryIds(factoryIds);
        }
        return freshStoreProductShipmentsService.queryListV2(vo);
    }

    @PostMapping("/queryFreshStoreList")
    public List<Store> queryFreshStoreList(@RequestBody List<Long> storeIdList) {
        return freshStoreProductShipmentsService.queryFreshStoreList(storeIdList);
    }

    @Autowired
    private RedissonClient redissonClient;

    @RequestMapping(value = "/queryListFreshStoreProduct", method = RequestMethod.POST)
    @ApiOperation(value = "鲜食产品发货总量", notes = "鲜食产品发货总量数据查询")
    @ApiImplicitParam(name = "idto", required = true, paramType = "body", dataType = "FreshStoreProductShipmentsReqVo")
    public FreshStoreProductShipmentsRespVo queryListFreshStoreProduct(@RequestBody FreshStoreProductShipmentsReqVo idto){
        QYAssert.isTrue(idto != null ,"查询参数为空异常!");
        QYAssert.isTrue(idto.getOrderDate() != null, "送货日期不能为空!");
        QYAssert.isTrue(idto.getLineGroupId() != null, "线路组不能为空!");
        //return freshStoreProductShipmentsClient.queryList(idto);
        TokenInfo info = FastThreadLocalUtil.getQY();
        idto.setLoginUserId(info.getUserId());

        FreshStoreProductShipmentsRespVo resultODTO = new FreshStoreProductShipmentsRespVo();
        List<Long> factoryIds = new ArrayList<>();
        if(idto.getFactoryId() == null){
            factoryIds = userClient.selectUserFactoryIdList(SelectUserFactoryIdListIDTO.onlyDb(idto.getLoginUserId()));
            if(SpringUtil.isEmpty(factoryIds)){
                resultODTO.setTitles(this.getTitles(null));
                return resultODTO;
            }
            idto.setFactoryIds(factoryIds);
        }
        List<FreshStoreProductShipmentsEntry> list = null;
        RBucket<Boolean> bucket = redissonClient.getBucket(QYApplicationContext.applicationNameSwitch + RedisKeyPrefixConst.STATISTICS_ES_SWITCH);
        if (bucket.get()==null || !bucket.get()) {
            list = freshStoreProductShipmentsService.queryListV2(idto);
        }else{
            QYAssert.isTrue(Boolean.FALSE,"此功能已经废弃");
//            FreshProductShipmentsIDTO shipmentsIDTO = BeanUtil.copyProperties(idto, FreshProductShipmentsIDTO.class);
//            List<FreshProductShipmentsTempODTO> tempODTOS = esFreshProductShipmentsClient.queryList(shipmentsIDTO);
//            list = BeanUtil.copyProperties(tempODTOS, FreshStoreProductShipmentsEntry.class);
        }
        if(SpringUtil.isEmpty(list)){
            resultODTO.setTitles(this.getTitles(null));
            return resultODTO;
        }
        List<Long> storeIds = list.stream().map(FreshStoreProductShipmentsEntry::getStoreId).distinct().collect(Collectors.toList());
        List<Store> stores = freshStoreProductShipmentsService.queryFreshStoreList(storeIds);
        resultODTO.setTitles(this.getTitles(stores));

        Map<Long, List<FreshStoreProductShipmentsEntry>> commodityGroupData = list.parallelStream().collect(groupingBy(FreshStoreProductShipmentsEntry::getCommodityId));
        List<Long> commodityIds = list.parallelStream().map(FreshStoreProductShipmentsEntry::getCommodityId).distinct()
                .collect(Collectors.toList());
        List<List<String>> body = new ArrayList<>(commodityIds.size());
        for (Long goodsId : commodityIds) {
            List<FreshStoreProductShipmentsEntry> entries = commodityGroupData.get(goodsId);
            FreshStoreProductShipmentsEntry entry = entries.get(0);
            List<String> row = new ArrayList<>(resultODTO.getTitles().size());
            row.add(entry.getFactoryName());
            row.add(entry.getWorkshopName());
            row.add(entry.getFlowshopName());
            row.add(entry.getCommodityCode());
            row.add(entry.getCommodityName());
            row.add(entry.getUnit());
            Double rowCount = 0.0;
            for (Store storeODTO : stores) {
                Double quantity = 0.0;
                for (FreshStoreProductShipmentsEntry shipmentsEntry : entries) {
                    if(shipmentsEntry.getStoreId().equals(storeODTO.getId())){
                        quantity += shipmentsEntry.getCommodityTotal();
                        break;
                    }
                }
                if(BigDecimal.valueOf(quantity).compareTo(BigDecimal.ZERO) == 0){
                    row.add("");
                }else {
                    row.add(BigDecimal.valueOf(quantity).setScale(2, RoundingMode.HALF_UP).stripTrailingZeros().toPlainString());
                }
                rowCount += quantity;
            }
            row.add(6, BigDecimal.valueOf(rowCount).setScale(2, RoundingMode.HALF_UP).stripTrailingZeros().toPlainString());
            body.add(row);
        }
        resultODTO.setDataList(body);
        return resultODTO;
    }

    private List<String> getTitles(List<Store> stores){
        List<String> titles = new ArrayList<>();
        titles.add("工厂");
        titles.add("生产组");
        titles.add("车间");
        titles.add("商品编号");
        titles.add("商品名称(规格)");
        titles.add("计量单位");
        titles.add("合计");
        if(SpringUtil.isNotEmpty(stores)){
            stores.forEach(item->{
                titles.add(item.getStoreName());
            });
        }
        return titles;
    }

    @RequestMapping(value = "/export", method = RequestMethod.GET)
    @ApiOperation(value = "导出excel", notes = "导出鲜食产品发货总量")
    @FileCacheQuery(bizCode = "ORDER_REPORT_PRODUCT_SHIPMENTS")
    public void exportExcel(@FileCacheQueryParameter FreshStoreProductShipmentsReqVo idto, HttpServletResponse response) throws IOException {
        long qS = System.currentTimeMillis();
        FreshStoreProductShipmentsRespVo respVo = queryListFreshStoreProduct(idto);
        long qE = System.currentTimeMillis();
        log.info("导出鲜食产品发货总量-导出--查询时间=" + ((qE - qS) / 1000));

        try {
            ExcelUtil.setFileNameAndHead(response, "产品发货总量(鲜食)" + LocalDate.now().toString("yyyyMMdd"));
            EasyExcel.write(response.getOutputStream()).head(createHead(respVo.getTitles())).autoCloseStream(Boolean.FALSE).sheet("产品发货总量(鲜食)")
                    .doWrite(respVo.getDataList());
        }catch (Exception e){
            log.error("鲜食产品发货总量导出错误", e);
            ExcelUtil.setExceptionResponse( response );
        }
        long eE = System.currentTimeMillis();
        log.info("鲜食产品发货总量-导出--excel处理时间="+ ((eE -qE) /1000 ));
    }
    private List<List<String>> createHead(List<String> list){
        List<List<String>> headList = new ArrayList<>(list.size());
        list.forEach(lt->headList.add(Arrays.asList(lt)));
        return headList;
    }

    @InitBinder
    public void initData(WebDataBinder wdb){
        wdb.registerCustomEditor(Date.class, new CustomDateEditor(new SimpleDateFormat("yyyy-MM-dd"), true));
    }
}

package com.pinshang.qingyun.orderreport.mapper;

import com.pinshang.qingyun.base.mybatis.MyMapper;
import com.pinshang.qingyun.orderreport.model.PsScLine;
import com.pinshang.qingyun.orderreport.vo.ps.*;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface PsScLineMapper extends MyMapper<PsScLine> {

    PsScLine selectByGroupLineName(@Param("lineName") String lineName);

    int modCommodityGroupLine(ModCommodityGroupLineVO dto);

    List<PsScLineVO> queryCommodityGroupLineList(QueryCommodityGroupLineVO dto);

    List<PsScLineStoreVO> queryCommodityGroupLineStoreList(QueryCommodityGroupLineStoreVO dto);

    List<PsScLineInfoVO> queryCommodityGroupLineAllList();

    /**
     * 获取互斥的商品组信息
     * @param commodityGroupId
     * @param lineId
     * @return
     */
    List<MutexCommodityGroupVO> mutexCommodityGroup2Store(@Param("commodityGroupId") Long commodityGroupId, @Param("lineId") Long lineId);

    /**
     * 获取互斥的客户信息
     *
     * @param lineId
     * @return
     */
    List<MutexCommodityGroupStoreVO> mutexStore2CommodityGroup(@Param("lineId") Long lineId, @Param("commodityGroupId") Long commodityGroupId, @Param("storeIdsList") List<Long> storeIdsList);

    /**
     * 商品组下拉列表
     * 可选商品组要排除当前线路所有客户已经具备的商品组
     * @param dto
     * @return
     */
    List<PsScCommodityGroupSelectVO> queryCommodityGroupSelectList1(QueryCommodityGroupSelectVO dto);

    /**
     * 商品组下拉列表
     * 可选商品组为所有启用的商品组
     * @param dto
     * @return
     */
    List<PsScCommodityGroupSelectVO> queryCommodityGroupSelectList2(QueryCommodityGroupSelectVO dto);
}
package com.pinshang.qingyun.orderreport.util;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.EasyExcelFactory;
import com.alibaba.excel.write.builder.ExcelWriterBuilder;
import com.pinshang.qingyun.base.configure.expand.QYAssert;
import com.pinshang.qingyun.box.utils.TimeUtil;
import com.pinshang.qingyun.orderreport.enums.QyEasyExcelExportTitleEnums;
import org.apache.commons.lang3.StringUtils;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.net.URLEncoder;
import java.util.Collection;
import java.util.Date;
import java.util.Iterator;

/**
 * <AUTHOR>
 * @date 2024/07/29/0029 15:00
 */
public class QyEasyExcelFactory extends EasyExcelFactory {

    public static ExcelWriterBuilder write(HttpServletResponse response,
                                           QyEasyExcelExportTitleEnums qyEasyExcelExportTitleEnums,
                                           Collection<?> data) throws IOException {
        response.setContentType("application/vnd.ms-excel");
        response.setCharacterEncoding("utf-8");
        String fileName = URLEncoder.encode(qyEasyExcelExportTitleEnums.getTitle(), "UTF-8")+"_"+ TimeUtil.formatDateToLong(new Date()) + ".xlsx";
        response.setHeader("Content-disposition", "attachment;filename=" + fileName);
        String sheetName = fileName;
        if(StringUtils.isNotBlank(qyEasyExcelExportTitleEnums.getSheetName())){
            sheetName = qyEasyExcelExportTitleEnums.getSheetName();
        }
        ServletOutputStream outputStream = response.getOutputStream();
        ExcelWriterBuilder excelWriterBuilder = new ExcelWriterBuilder();
        excelWriterBuilder.file(outputStream);
        if (data != null) {
            Iterator<?> iterator = data.iterator();
            QYAssert.isTrue(iterator.hasNext(),"导出数据为空！");
            Object next = iterator.next();
            if (next != null) {
                excelWriterBuilder.head(next.getClass());
            }
            EasyExcel.write(outputStream, next.getClass())
                    .includeColumnFiledNames(qyEasyExcelExportTitleEnums.getColumns()).needHead(true)
                    .orderByIncludeColumn(true).autoCloseStream(Boolean.FALSE)
                    .sheet(sheetName).doWrite(data);
        }

        return excelWriterBuilder;
    }


}

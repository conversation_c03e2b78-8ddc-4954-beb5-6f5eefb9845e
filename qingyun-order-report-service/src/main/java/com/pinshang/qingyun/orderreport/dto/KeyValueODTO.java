package com.pinshang.qingyun.orderreport.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 键值对
 */
@Data
@NoArgsConstructor
public class KeyValueODTO {
	@ApiModelProperty(position = 11, value = "键-Int")
	private Integer keyInt;
	@ApiModelProperty(position = 12, value = "键-Long")
	private Long keyLong;
	@ApiModelProperty(position = 13, value = "键-String")
	private String keyStr;

	@ApiModelProperty(position = 21, value = "值-Int")
	private Integer valueInt;
	@ApiModelProperty(position = 22, value = "值-Long")
	private Long valueLong;
	@ApiModelProperty(position = 23, value = "值-String")
	private String valueStr;
}

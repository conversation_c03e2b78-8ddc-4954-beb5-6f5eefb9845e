package com.pinshang.qingyun.orderreport.util;

import org.apache.commons.lang3.StringUtils;
import org.joda.time.DateTime;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

import static com.pinshang.qingyun.box.utils.DateTimeUtil.parse;
import static org.apache.commons.lang3.time.DateFormatUtils.format;

public class DateUtils {

    private DateUtils() {

    }


    public static Boolean isLatestDate(String orderDate) {
        boolean latestFlag = false;
        try {
            Date date;
            if (StringUtils.isEmpty(orderDate)) {
                date = new DateTime().plusDays(1).toDate();
            }else{
                date = new SimpleDateFormat("yyyy-MM-dd").parse(orderDate);
            }
            latestFlag = !new DateTime(date).isBefore(new DateTime().withMillisOfDay(0));
        } catch (ParseException e) {
            return latestFlag;
        }
        return latestFlag;
    }

    /**
     * 获取两个时间段间的月度差数
     * @return
     */
    public static int getMonthDiff(String startTime, String endTime) throws ParseException {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM");
        Calendar c1 = Calendar.getInstance();
        Calendar c2 = Calendar.getInstance();
        c1.setTime(sdf.parse(startTime));
        c2.setTime(sdf.parse(endTime));
        int year1 = c1.get(Calendar.YEAR);
        int year2 = c2.get(Calendar.YEAR);
        int month1 = c1.get(Calendar.MONTH);
        int month2 = c2.get(Calendar.MONTH);
        // 获取年的差值
        int yearInterval = year1 - year2;
        // 如果 d1的 月-日 小于 d2的 月-日 那么 yearInterval-- 这样就得到了相差的年数
        if (month1 < month2 ) {
            yearInterval--;
        }
        // 获取月数差值
        int monthInterval = (month1 + 12) - month2;
        monthInterval %= 12;
        int monthsDiff = Math.abs(yearInterval * 12 + monthInterval);
        return monthsDiff;
    }


    /**
     * 获取两个时间段间的月数列表
     * @return
     */
    public static List<String> sameMonth(String startTime, String endTime){
        List<String> result = new ArrayList<String>();
        Calendar min = Calendar.getInstance();
        Calendar max = Calendar.getInstance();
        min.setTime(parse(startTime,"yyyy-MM"));
        min.set(min.get(Calendar.YEAR), min.get(Calendar.MONTH), 1);
        max.setTime(parse(endTime,"yyyy-MM"));
        max.set(max.get(Calendar.YEAR), max.get(Calendar.MONTH), 2);

        Calendar curr = min;
        Date newDate = parse(format(new Date(),"yyyy-MM"),"yyyy-MM");
        while (curr.before(max) && min.getTime().before(newDate)) {
            result.add(format(curr.getTime(),"yyyy-MM"));
            curr.add(Calendar.MONTH, 1);
        }
        return result;
    }

    /**
     * 获取某个月的第一天和最后一天
     * @return
     */
    public static List<String> startAndEndTime(String time) throws ParseException {
        List<String> list = new ArrayList<>();
        Date start = new SimpleDateFormat("yyyy-MM").parse(time);
        Calendar calEnd = Calendar.getInstance();
        calEnd.setTime(start);
        calEnd.set(Calendar.DATE, 1);//把日期设置为当月第一天
        list.add(new SimpleDateFormat("yyyy-MM-dd").format(calEnd.getTime()));
        calEnd.roll(Calendar.DATE, -1);//日期回滚一天，也就是最后一天
        list.add(new SimpleDateFormat("yyyy-MM-dd").format(calEnd.getTime()));
        return list;
    }

    public static Date getPreDate(){
        SimpleDateFormat smf = new SimpleDateFormat("yyyy-MM-dd");
        Calendar cal=Calendar.getInstance();
        cal.add(Calendar.DATE,-1);
        String now= smf.format(cal.getTime());
        Date preDate = null;
        try {
            preDate = smf.parse(now);
        } catch (ParseException e) {
            e.printStackTrace();
        }
        return preDate;
    }



    public static void main(String[] args) {
        System.out.println(isLatestDate("2019-06-18"));
        System.out.println(isLatestDate("2019-06-19"));
        System.out.println(isLatestDate("2019-06-17"));
        System.out.println(isLatestDate(""));
    }
}
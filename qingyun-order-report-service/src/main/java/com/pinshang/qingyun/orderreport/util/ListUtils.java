package com.pinshang.qingyun.orderreport.util;

import java.util.ArrayList;
import java.util.List;

public class ListUtils {
    public static <T> List<List<T>> splitList(List<T> list, int pageSize) {
        List<List<T>> result = new ArrayList<>();
        ArrayList<T> al = new ArrayList<>();
        for(T x : list){
            al.add(x);
            if (pageSize == al.size()){
                result.add(al);
                al = new ArrayList<T>();
            }
        }
        if (0 != al.size()) {
            result.add(al);
        }
        return result;
    }
}
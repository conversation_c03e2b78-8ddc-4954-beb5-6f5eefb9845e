package com.pinshang.qingyun.orderreport.mapper.entry;

import lombok.Data;

import java.math.BigDecimal;

/**
 * @description: 物流配送订单金额统计
 * @author: hhf
 * @time: 2022/6/15/015 14:45
 */
@Data
public class LogisticsDeliveryOrderAmountStatisticsTempEntry {

    /**送货日期**/
    private String orderTime;

    /**线路组id**/
    private String storeLineGroupId;

    /**线路组名称**/
    private String storeLineGroupName;

    /**金额**/
    private BigDecimal orderAmount;

    /**客户类型ID**/
    private Long storeTypeId;

    /**客户类型名称**/
    private String storeTypeName;

}

package com.pinshang.qingyun.orderreport.service;

import com.pinshang.qingyun.base.enums.PrinterDataGroupTypeEnum;
import com.pinshang.qingyun.box.utils.SpringUtil;
//import com.pinshang.qingyun.common.dto.PrinterTaskINDTO;
//import com.pinshang.qingyun.common.service.PrintClient;
import com.pinshang.qingyun.print.dto.PrinterTaskIdto;
import com.pinshang.qingyun.print.service.PrinterTaskClient;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2019/2/18 13:45.
 */
@Component
public class PrintTaskService {

//    private final PrintClient printClient;
//
//    public PrintTaskService(PrintClient printClient) {
//        this.printClient = printClient;
//    }
    
    @Autowired
    private PrinterTaskClient printerTaskClient;

    public void savePrintTasks(List<String> filePath, PrinterDataGroupTypeEnum statisticalQueryDelivery, Long userId, int quantity, Integer printTypeCode) {
    	if (SpringUtil.isEmpty(filePath)) {
    		return;
    	}
        List<PrinterTaskIdto> tasks = new ArrayList<>();
        for (String path : filePath) {
            tasks.add(buildTask(path, statisticalQueryDelivery, userId, quantity, printTypeCode));
        }
        printerTaskClient.saveTasks(tasks);
    }

    public void savePrintTask(String filePath, PrinterDataGroupTypeEnum statisticalQueryDelivery, Long userId, int quantity, Integer printTypeCode) {
    	PrinterTaskIdto task = buildTask(filePath, statisticalQueryDelivery, userId, quantity, printTypeCode);
        printerTaskClient.saveTask(task);
    }

    private PrinterTaskIdto buildTask(String filePath, PrinterDataGroupTypeEnum statisticalQueryDelivery, Long userId, int quantity, Integer printTypeCode) {
    	PrinterTaskIdto task = new PrinterTaskIdto();
        task.setFilePath(filePath);
        task.setUserId(userId);
//        task.setDataType(statisticalQueryDelivery.getCode().intValue());
        task.setPrinterType(printTypeCode);
        task.setNumber(quantity);
//        task.setAvg(0);
        return task;
    }
}

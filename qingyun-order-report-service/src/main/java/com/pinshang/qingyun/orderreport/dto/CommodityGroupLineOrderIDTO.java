package com.pinshang.qingyun.orderreport.dto;

import com.pinshang.qingyun.base.page.Pagination;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * 商品组配送单查询入参
 */
@Data
public class CommodityGroupLineOrderIDTO extends Pagination {

    @ApiModelProperty(value = "线路组ID")
    private Long lineGroupId;

    @ApiModelProperty(value = "送货员ID")
    private Long deliverymanId;

    @ApiModelProperty(value = "送货日期")
    private Date orderTime;

    @ApiModelProperty(value = "线路ID：打印预览时传")
    private Long lineId;

}

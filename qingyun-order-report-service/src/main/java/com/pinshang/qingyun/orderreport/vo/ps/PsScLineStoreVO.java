package com.pinshang.qingyun.orderreport.vo.ps;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @Version 0.1.0
 * @Date 2021/1/28 10:09
 * @Copyright © 2019-2021 qm
 * @Description -- 类说明
 **/
@Data
public class PsScLineStoreVO {

    @ApiModelProperty(value = "id")
    private String id;

    @ApiModelProperty(value = "客户编码")
    private String storeCode;

    @ApiModelProperty(value = "客户名称")
    private String storeName;

    @ApiModelProperty(value = "客户类型")
    private String storeTypeName;

    @ApiModelProperty(value = "客户电话")
    private String linkmanMobile;

    @ApiModelProperty(value = "地址")
    private String storeDeliveryAddress;

}

package com.pinshang.qingyun.orderreport.model;

import lombok.Data;

import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Transient;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @description 产品销售汇总--商品
 * @date 2019/8/30
 */
@Data
@Table(name = "t_tj_commodity_sale_statistics")
public class CommoditySaleStatistics {

    @Id
    private Long id;

    /** 商品id **/
    private Long commodityId;

    /** 数量小计 **/
    private BigDecimal totalQuantity;

    /** 金额小计 **/
    private BigDecimal totalAmount;

    /** 送货日期 **/
    private Date orderTime;

    /** 送货日期 String格式 **/
    @Transient
    private String orderTimeStr;

    /** 数据添加时间 **/
    private Date createTime;

    /** 公司id **/
    private Long companyId;

}

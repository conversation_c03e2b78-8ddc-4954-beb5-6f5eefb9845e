package com.pinshang.qingyun.orderreport.pdf;

import com.itextpdf.text.*;
import com.itextpdf.text.pdf.PdfPCell;
import com.itextpdf.text.pdf.PdfPTable;
import com.itextpdf.text.pdf.PdfWriter;
import com.pinshang.qingyun.base.enums.CloudDeliveryBatchTypeEnum;
import com.pinshang.qingyun.orderreport.config.CustomerProperties;
import com.pinshang.qingyun.orderreport.util.ListUtils;
import com.pinshang.qingyun.orderreport.util.PdfUtils;
import com.pinshang.qingyun.orderreport.util.StrUtils;
import com.pinshang.qingyun.orderreport.vo.DeliveryListPreviewRespVo;
import org.apache.commons.lang3.StringUtils;
import org.joda.time.DateTime;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.UUID;

@Component
public class DeliveryListPdfCreator {

    private static final Logger LOGGER = LoggerFactory.getLogger(DeliveryListPdfCreator.class);

    private final CustomerProperties customerProperties;

    private static final float TABLE_WIDTH_A4 = 100f;

    /**
     * 多少家客户变为横向打印
     * 默认是11号字体 A4纸的情况下, 13家客户换横行打印
     * 设置成15 是因为 第一个格的 商品名称  和最后一个格的 合计
     * 所以要 + 2
     */
    private static final Integer rotateDelimiter = 15;

    /**
     * 纵向每页显示多少行,按照当前模板 42行是最好的
     */
    private static final Integer pageRows = 42;

    /**
     * 横向打印的时候,每页27条
     */
    private static final Integer horizontalPage = 25;

    /**
     * 商品名称保留多长
     */
    private static final Integer goodsNameLength = 18;
    private static final Integer storeNameLength = 20;


    private static final Integer A4_WIDTH = 595;

    @Autowired
    public DeliveryListPdfCreator(CustomerProperties customerProperties) {
        this.customerProperties = customerProperties;
    }

    /**
     * 生成PDF
     * @param previewData 要生成PDF的内容.
     * @return 文件生成路径
     */
    public String create(DeliveryListPreviewRespVo previewData) {
        //是否横向打印,默认为否
        boolean horizontal = false;
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");
        SimpleDateFormat format2 = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        Date date = new Date();
        String printTime = format2.format(date);
        String fileName = null;
        if (previewData == null) {
            return "";
        }
        String pdfSave = customerProperties.getAbsoluteSavePath();
        String datePath = format.format(new DateTime().plusDays(1).toDate());
        String savePath = customerProperties.getSavePath();
        File file = new File(pdfSave + "/" +savePath + "/" +datePath);
        if (!file.exists()) {
            boolean mkdirs = file.mkdirs();
            if (!mkdirs) {
                LOGGER.error("创建pdf保存目录时异常,请检查权限");
                return "";
            }
        }
        Rectangle rect = new Rectangle(PageSize.A4);
        List<String> storeNames = previewData.getStoreNames();
        Document document;
        if (storeNames.size() <= rotateDelimiter) {
            //当有20家客户时使用默认纵向打印,如果大于20则横向打印
            //因为这个storeNames 包括了第一列商品名称和最后一列 合计.所以是22
            document = new Document();
        } else {
            horizontal = true;
            document = new Document(rect.rotate());
        }
        Font fontChinese;
        try {
            Font  productNameFont = PdfUtils.chineseFont(9);
            fileName = extractFileName(previewData);
            FileOutputStream out = new FileOutputStream(file.getAbsolutePath() + fileName);
            fontChinese = PdfUtils.chineseFont(11);
            Font numberFont = PdfUtils.chineseFont(8);
            PdfWriter.getInstance(document, out);// 文档输出流。
            document.setMargins(15, 15, 15, 15);
            document.open();
            //表头
            List<List<String>> goodsInfo = previewData.getGoodsInfo();
            //通过商品数量判断总共多少页 24行一页
            PdfPTable tableHeader = proceedTableHeader(previewData, TABLE_WIDTH_A4, fontChinese);
            float[] cellWidth = new float[storeNames.size() ];
            cellWidth[0] = 135;
            float totalWidth = cellWidth[0];
            if (horizontal) {
                cellWidth[0] = 100;
                for (int i = 1; i < storeNames.size(); i++) {
                    //最后一列合计,宽度要适当宽一些.
                    if (i == storeNames.size() - 1) {
                        cellWidth[i] = 36;
                        totalWidth += cellWidth[i];
                    } else {
                        cellWidth[i] = 31;
                        totalWidth += cellWidth[i];
                    }
                }
            } else {
                cellWidth[0] = 100;
                for (int i = 1; i < storeNames.size(); i++) {
                    //最后一列合计,宽度要适当宽一些.
                    if (i == storeNames.size() - 1) {
                        cellWidth[i] = 34;
                        totalWidth += cellWidth[i];
                    } else {
                        cellWidth[i] = 30;
                        totalWidth += cellWidth[i];
                    }
                }
            }

            ///////////////////////////////////////////////////////////////////////////
            // 处理表格内容, 首先根据一而显示多少行截取list,这样最外层的list的每一个item代表的是
            // 一页的内容,第二层list表示的是每一行的内容.最内层的list就是每一个单元格的内容.
            ///////////////////////////////////////////////////////////////////////////
            List<List<List<String>>> pageList;
            int rowCount=horizontal?horizontalPage:pageRows;
            for (int i = 0; i < storeNames.size(); i++) {
                String storeName = storeNames.get(i);
                int size = 0;
                if (storeName.length() > 18) {
                    size = (storeName.length()-18)/3;
                }
                int nowPageSize=0;
                if (horizontal) {
                    nowPageSize = horizontalPage - size;
                }else{
                    nowPageSize = pageRows - size;
                }
                if(nowPageSize<rowCount){
                    rowCount = nowPageSize;
                }
            }
            if (horizontal) {
                pageList = ListUtils.splitList(goodsInfo, rowCount);
            } else {
                pageList = ListUtils.splitList(goodsInfo, rowCount);
            }
            int page = 1;
            int totalPage = pageList.size();
            for (List<List<String>> lists : pageList) {
                //表头
                document.add(tableHeader);
                PdfPTable table = new PdfPTable(cellWidth);
                table.setWidthPercentage(TABLE_WIDTH_A4);
                table.setHorizontalAlignment(Element.ALIGN_LEFT);
                table.setTotalWidth(totalWidth);
                table.setLockedWidth(false);
                //表头的店铺名
                for (int i = 0; i < storeNames.size(); i++) {
                    String storeName = storeNames.get(i);
                    //if (storeName.length() > (storeNameLength >> 1)) {
                    //    storeName = StrUtils.subStr(storeName, storeNameLength);
                    //}
                    PdfPCell cell = new PdfPCell(new Paragraph(storeName, productNameFont));
                    if (i == 0) {
                        cell.setUseAscender(true);
                        cell.setVerticalAlignment(Element.ALIGN_CENTER);
                    }
//                    cell.setFixedHeight(20f); // 需要自动伸展高度
                    table.addCell(cell);
                }
                /**
                 * 线上客户反应列表数据格式变化,需要设置默认true
                 */
                table.setLockedWidth(true);
                //处理每一行
                for (List<String> list : lists) {
                    for (int i = 0; i < list.size(); i++) {
                        PdfPCell cell;
                        String goodsName = list.get(i);
                        //每一行的第一个单元格,商品名称 太长需要截取
                        if (i == 0) {
                            goodsName = goodsName.replace("）", ")").replace("（", "(");
                            if (goodsName.length() > (goodsNameLength >> 1)) {
                                goodsName = StrUtils.subStr(goodsName, goodsNameLength);
                            }
                            cell = new PdfPCell(new Phrase(goodsName, fontChinese));
                        } else {
                            if (goodsName.length() > 5) {
                                cell = new PdfPCell(new Phrase(goodsName, numberFont));
                            } else {
                                cell = new PdfPCell(new Phrase(goodsName, fontChinese));
                            }
                        }
                        cell.setNoWrap(true);
                        table.addCell(cell);
                    }
                }
                //表格的footer, 页码信息及控制分页
                PdfPTable pageTable = new PdfPTable(1);
                PdfPCell cell = new PdfPCell(new Phrase("第 " + page + "页, 共 "+ totalPage+" 页                        打印时间: " + printTime, fontChinese));
                cell.setBorder(0);
                cell.setColspan(storeNames.size());
                pageTable.addCell(cell);
                document.add(table);
                document.add(pageTable);
                page++;
                if (page <= totalPage) {
                    document.newPage();
                }
            }
            document.close();
        } catch (DocumentException | IOException e) {
            LOGGER.error("生成PDF内容时异常:{}", e.getMessage());
        }
        return String.format("%s%s",file.getAbsolutePath(),fileName);
        //return savePath + "/" +datePath + "/" +fileName;
    }

    /**
     * 生成文件名
     * @param previewData 预览数据.
     * @return 返回文件名
     */
    private String extractFileName(DeliveryListPreviewRespVo previewData) {
        String lineCode = previewData.getLineCode();
        Date orderDate = previewData.getOrderDate();
        String date = "";
        if (orderDate != null) {
            date = new SimpleDateFormat("yyyy-MM-dd").format(orderDate);
        }
        return "/" + lineCode +  "_" + date + "_" + UUID.randomUUID().toString().substring(0,8) + ".pdf";
    }

    /**
     * 生成PDF 表头信息
     * @param previewData 创建PDF所需要的数据,包括了所有的数据
     * @param tableWidthA4 A4纸的宽度百分比
     * @param fontChinese 写入中文所使用的字体
     */
    private PdfPTable proceedTableHeader(DeliveryListPreviewRespVo previewData, float tableWidthA4, Font fontChinese) throws IOException, DocumentException {
        String deliveryManName = previewData.getDeliveryManName() == null ? "" : previewData.getDeliveryManName();
        String lineCode = previewData.getLineCode() == null ? "" : previewData.getLineCode();
        String lineGroupName = previewData.getLineGroupName() == null ? "" : previewData.getLineGroupName();
        String lineName = previewData.getLineName() == null ? "" : previewData.getLineName();
        Date orderDate = previewData.getOrderDate();
        String warehouse = previewData.getWarehouse() == null ? "" : previewData.getWarehouse();
        String printBatch = previewData.getDefaultPrintDeliveryBatch() == null ? "" : previewData.getDefaultPrintDeliveryBatch();

        float[] cellWidth = { 200, 150 , 200 };
        PdfPTable tableHeader = new PdfPTable(3);
        tableHeader.setWidthPercentage(tableWidthA4);
        tableHeader.setHorizontalAlignment(Element.ALIGN_LEFT);
        tableHeader.setTotalWidth(cellWidth);
        tableHeader.setLockedWidth(true);

        PdfPCell title;
        title = new PdfPCell(new Paragraph("", PdfUtils.chineseFont(18)));
        title.setBorder(0);
        title.setFixedHeight(27);
        tableHeader.addCell(title);

        title = new PdfPCell(new Paragraph("送货清单 ", PdfUtils.chineseFont(18)));
        title.setBorder(0);
        title.setHorizontalAlignment(Element.ALIGN_CENTER);
        title.setVerticalAlignment(Element.ALIGN_CENTER);
        title.setFixedHeight(27);
        tableHeader.addCell(title);

        String orderModeTypeName = previewData.getOrderModeTypeName();
        title = new PdfPCell(new Paragraph(StringUtils.isBlank(orderModeTypeName) ? "" : "（"+orderModeTypeName+"）", fontChinese));
        title.setBorder(0);
        title.setHorizontalAlignment(Element.ALIGN_RIGHT);
        title.setVerticalAlignment(Element.ALIGN_RIGHT);
        title.setFixedHeight(27);
        tableHeader.addCell(title);

        PdfPCell cell = new PdfPCell(new Paragraph("线路: " + lineCode+"_"+lineName, fontChinese));
        cell.setBorder(0);
        tableHeader.addCell(cell);

//        cell = new PdfPCell(new Paragraph("线路名称: " + lineName, fontChinese));
//        cell.setBorder(0);
//        tableHeader.addCell(cell);

        cell = new PdfPCell(new Paragraph("线路组: " + lineGroupName, fontChinese));
        cell.setBorder(0);
        tableHeader.addCell(cell);

        cell = new PdfPCell(new Paragraph("发货仓库:" + warehouse, fontChinese));
        cell.setBorder(0);
        tableHeader.addCell(cell);

        float[] cellWidth1 = { 200, 150 , 200 };
        tableHeader.setTotalWidth(cellWidth1);
        tableHeader.setLockedWidth(true);
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");

        String deliveryBatchName = "";
        if(null != previewData.getDeliveryBatch()){
            Integer deliveryBatch = previewData.getDeliveryBatch();
            deliveryBatchName = CloudDeliveryBatchTypeEnum.getName(deliveryBatch);
        }
        cell = new PdfPCell(new Paragraph("送货日期: " + format.format(orderDate)+"  "+deliveryBatchName, fontChinese));
        cell.setBorder(0);
        tableHeader.addCell(cell);

        cell = new PdfPCell(new Paragraph("送货员: " + deliveryManName, fontChinese));
        cell.setBorder(0);
        tableHeader.addCell(cell);

        cell = new PdfPCell(new Paragraph("打印批次: " + printBatch, fontChinese));
        cell.setBorder(0);
        tableHeader.addCell(cell);

        cell = new PdfPCell(new Paragraph(" ", fontChinese));
        cell.setBorder(0);
        tableHeader.addCell(cell);

        return tableHeader;
    }
}

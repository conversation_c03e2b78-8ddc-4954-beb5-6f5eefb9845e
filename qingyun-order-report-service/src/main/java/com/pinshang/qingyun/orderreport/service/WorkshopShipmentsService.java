package com.pinshang.qingyun.orderreport.service;

import com.pinshang.qingyun.base.configure.expand.QYAssert;
import com.pinshang.qingyun.base.enums.PrinterDataGroupTypeEnum;
import com.pinshang.qingyun.box.utils.SpringUtil;
import com.pinshang.qingyun.orderreport.mapper.CommodityMapper;
import com.pinshang.qingyun.orderreport.mapper.FactoryWorkShopCommodityNumMapper;
import com.pinshang.qingyun.orderreport.mapper.WorkshopShipmentsMapper;
import com.pinshang.qingyun.orderreport.mapper.entry.CommodityNumEntry;
import com.pinshang.qingyun.orderreport.mapper.entry.WorkshopShipmentsEntry;
import com.pinshang.qingyun.orderreport.mapper.entry.WorkshopShipmentsItemEntry;
import com.pinshang.qingyun.orderreport.mapper.entry.WorkshopShipmentsTempEntry;
import com.pinshang.qingyun.orderreport.model.Commodity;
import com.pinshang.qingyun.orderreport.model.FactoryWorkShopCommodityNum;
import com.pinshang.qingyun.orderreport.pdf.WorkshopShipmentsPdfCreator;
import com.pinshang.qingyun.orderreport.util.DateUtils;
import com.pinshang.qingyun.orderreport.util.NumberUtils;
import com.pinshang.qingyun.orderreport.util.ProductShipmentRequestParamHelper;
import com.pinshang.qingyun.orderreport.util.WorkshopShipmentsHelper;
import com.pinshang.qingyun.orderreport.vo.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Example;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.Collator;
import java.text.NumberFormat;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

import static java.util.stream.Collectors.groupingBy;

/**
 * <AUTHOR>
 * @date 2019/2/27 14:19.
 */
@Slf4j
@Service
public class WorkshopShipmentsService {

    private final WorkshopShipmentsMapper workshopShipmentsMapper;
    private final CommodityMapper commodityMapper;
    private final WorkshopShipmentsHelper workshopShipmentsHelper;
    private final WorkshopShipmentsPdfCreator workshopShipmentsPdfCreator;
    private final FactoryWorkShopCommodityNumMapper factoryWorkShopCommodityNumMapper;
    private final PrintTaskService printTaskService;
    @Autowired
    private CommonPdfPrintService commonPdfPrintService;

    public WorkshopShipmentsService(WorkshopShipmentsMapper workshopShipmentsMapper, CommodityMapper commodityMapper, WorkshopShipmentsHelper workshopShipmentsHelper, WorkshopShipmentsPdfCreator workshopShipmentsPdfCreator, PrintTaskService printTaskService, FactoryWorkShopCommodityNumMapper factoryWorkShopCommodityNumMapper) {
        this.workshopShipmentsMapper = workshopShipmentsMapper;
        this.commodityMapper = commodityMapper;
        this.workshopShipmentsHelper = workshopShipmentsHelper;
        this.workshopShipmentsPdfCreator = workshopShipmentsPdfCreator;
        this.printTaskService = printTaskService;
        this.factoryWorkShopCommodityNumMapper = factoryWorkShopCommodityNumMapper;
    }

    public List<WorkshopVo> doSearch(ProductShipmentsRequestVo requestVo) {
        Map<String, Object> sqlMap = ProductShipmentRequestParamHelper.buildSqlMap(requestVo);
        return workshopShipmentsMapper.searchListByParams(sqlMap);
    }

    public WorkshopShipmentsVo preview(ProductShipmentsRequestVo requestVo) {
        NumberFormat numberFormat = NumberFormat.getNumberInstance();
        numberFormat.setMaximumFractionDigits(2);
        numberFormat.setGroupingUsed(false);
        numberFormat.setRoundingMode(RoundingMode.HALF_UP);
        Long workshopId = requestVo.getWorkshopId();
        Date startOrderDate = requestVo.getStartOrderDate();
        Date endOrderDate = requestVo.getEndOrderDate();
        WorkshopShipmentsVo result = new WorkshopShipmentsVo();
        WorkshopVo workshopVo = commodityMapper.selectWorkshopByWorkshopId(workshopId);
        if (workshopVo == null) {
            return null;
        }
        workshopShipmentsHelper.processResponseTitle(requestVo, result);
        result.setWorkshopName(workshopVo.getWorkshopName());
        result.setStartOrderDate(startOrderDate);
        result.setEndOrderDate(endOrderDate);
        result.setOrderModeTypeName(requestVo.getOrderModeTypeName());
        Map<String, Object> params = ProductShipmentRequestParamHelper.buildSqlMap(requestVo);
        params.put("workshopId", workshopVo.getId());
        //指定订单日期内客户购买的所有商品,并且限制了生产组
        List<OrderListVo> dbResult = workshopShipmentsMapper.queryOrdersByOrderDateAndWorkshopId(params);
        Map<Long, OrderListVo> goodsListMapping = new HashMap<>();
        Map<String, List<OrderListVo>> lineListMapping = new HashMap<>();
        List<Long> goodsIds = new ArrayList<>();
        for (OrderListVo orderListVo : dbResult) {
            goodsListMapping.put(orderListVo.getGoodsId(), orderListVo);
            goodsIds.add(orderListVo.getGoodsId());
            String lineCode = orderListVo.getLineCode();
            if (lineListMapping.get(lineCode) == null) {
                List<OrderListVo> listVos = new ArrayList<>();
                listVos.add(orderListVo);
                lineListMapping.put(lineCode, listVos);
            } else {
                List<OrderListVo> listVos = lineListMapping.get(lineCode);
                listVos.add(orderListVo);
                lineListMapping.put(lineCode, listVos);
            }
        }
        goodsIds = goodsIds.parallelStream().distinct().collect(Collectors.toList());

        goodsIds = sort(goodsIds);
        List<String> tableHeader = new ArrayList<>();
        tableHeader.add("车位");
        tableHeader.add("线路名称");
        tableHeader.add("送货员");
        List<String> unitLine = new ArrayList<>();
        unitLine.add("");
        unitLine.add("");
        unitLine.add("");
        goodsIds.forEach(id->{
            OrderListVo orderListVo = goodsListMapping.get(id);
            if (orderListVo != null) {
                tableHeader.add(orderListVo.getGoodsName() + "(" + orderListVo.getSpecification() + ")");
                unitLine.add(orderListVo.getUnit());
            }
        });
        result.setTableHeader(tableHeader);
        Set<String> lineCodes = lineListMapping.keySet();
        List<List<String>> tableDataList = new ArrayList<>();
        tableDataList.add(unitLine);
        List<Long> finalGoodsIds = goodsIds;
        lineCodes.iterator().forEachRemaining(s -> {
            List<OrderListVo> orderList = lineListMapping.get(s);
            List<String> tableDataListItem = new ArrayList<>();
            if (orderList != null && orderList.size() != 0) {
                OrderListVo vo = orderList.get(0);
                tableDataListItem.add(vo.getCarportName() == null ? "" : vo.getCarportName());
                tableDataListItem.add(vo.getLineName());
                tableDataListItem.add(vo.getDeliveryManName());
                finalGoodsIds.forEach(goodsId->{
                    Double count = 0.0;
                    for (OrderListVo orderListVo : orderList) {
                        if (Objects.equals(orderListVo.getGoodsId(), goodsId)) {
                            count = count + orderListVo.getGoodsNumber();
                        }
                    }
                    String e = NumberUtils.subZeroAndDot(numberFormat.format(count));
                    tableDataListItem.add("0".equals(e) ? "" : e);
                });
            }
            tableDataList.add(tableDataListItem);
        });
        // 根据车位排序, 第一个字段是车位
        if (tableDataList.size() > 0) {
            tableDataList.sort(Comparator.comparing(item -> item.get(0)));
        }
        result.setTableDate(tableDataList);
        List<String> tableBottom = new ArrayList<>();
        tableBottom.add("");
        tableBottom.add("合计");
        tableBottom.add("");
        for (int i = 0; i < goodsIds.size(); i++) {
            Double count = 0.0;
            for (List<String> column : tableDataList) {
                String subCount = column.get(i + 3);
                if (StringUtils.isNoneBlank(subCount) && NumberUtils.isNumeric(subCount)) {
                    Double sub = new Double(subCount);
                    count += sub;
                }
            }
            String e = NumberUtils.subZeroAndDot(numberFormat.format(count));
            tableBottom.add("0".equals(e) ? "" : e);
        }
        result.setTableBottom(tableBottom);
        return result;
    }


    public boolean print(ProductShipmentsRequestVo requestVo) {
        WorkshopShipmentsVo preview = preview(requestVo);
        if (preview == null || preview.getTableDate() == null || preview.getTableDate().size() == 0) {
            return false;
        }
        String fileName = workshopShipmentsPdfCreator.createPdf(preview);
        try {
            //order_report_TJ_WORKSHOP_SHIPMENTS 生产组发货
            commonPdfPrintService.uploadAndPrintPdf(fileName,"order_report_TJ_WORKSHOP_SHIPMENTS",PrinterDataGroupTypeEnum.STATISTICAL_QUERY_DELIVERY,requestVo.getUserId());
        } catch (Exception e) {
            log.error("生产组外地发货单打印失败:{}", e);
            return false;
        }
        return true;
    }

    public boolean printAll(List<ProductShipmentsRequestVo> requestVo) {
        for (ProductShipmentsRequestVo shipmentsRequestVo : requestVo) {
            print(shipmentsRequestVo);
        }
        return true;
    }
    public String getStrPdf(ProductShipmentsRequestVo requestVo){
        WorkshopShipmentsVo preview = preview(requestVo);
        if (preview == null || preview.getTableDate() == null || preview.getTableDate().size() == 0) {
            return null;
        }
        return workshopShipmentsPdfCreator.createPdf(preview);
    }

    public Map<String,String> getStrListPdf(List<ProductShipmentsRequestVo> requestVo){
        Map<String,String> strListPdf = new HashMap<>();
        String fileName;
        SimpleDateFormat dateFormatHHMM = new SimpleDateFormat("yyyyMMddHHmm");
        String dataDir = dateFormatHHMM.format(new Date());
        for (ProductShipmentsRequestVo shipmentsRequestVo : requestVo) {
            WorkshopShipmentsVo preview = preview(shipmentsRequestVo);
            if (preview == null || preview.getTableDate() == null || preview.getTableDate().size() == 0) {
                continue;
            }
            if(null != (fileName = workshopShipmentsPdfCreator.createPdf(preview))){
                WorkshopVo workshopVo = commodityMapper.selectWorkshopByWorkshopId(shipmentsRequestVo.getWorkshopId());
                String workshopName = "";
                if(null != workshopVo && null != workshopVo.getWorkshopName()){
                    workshopName = workshopVo.getWorkshopName();
                }
                strListPdf.put(fileName,"生产组发货单"+ workshopName + dataDir + ".pdf");
            }
        }

        return strListPdf;
    }

    public boolean  isDirector(String directorCode){
        if(StringUtils.isNotBlank(directorCode)){
            List<Long> ids = workshopShipmentsMapper.findDirectorIdsByCode(directorCode);
            return (ids != null && !ids.isEmpty());
        }
        QYAssert.isTrue(false,"查询是否为车间主任时查询码为空!");
        return false;
    }

    public List<WorkshopShipmentsEntry> findShipmentsList(ShipmentsVo vo){
        Boolean latestFlag = DateUtils.isLatestDate(vo.getOrderDate());
        List<WorkshopShipmentsTempEntry> entryList = workshopShipmentsMapper.findShipmentsList(vo.getOrderDate(),vo.getLineGroupId(),vo.getWarehouseId(),
                vo.getDirectorCode(),vo.getDeliveryTime(),vo.getDeliveryBatch(),latestFlag,vo.getOrderModeType());
        if(SpringUtil.isEmpty(entryList)){
            return null;
        }
        //定义返回list
        List<WorkshopShipmentsEntry> list = new ArrayList<>();

        //先根据生产组排序
        Map<String,List<WorkshopShipmentsTempEntry>> workShopMap = entryList.stream().collect(groupingBy(WorkshopShipmentsTempEntry::getWorkshopName));
        Map<String,List<WorkshopShipmentsTempEntry>> workShopMapSort = new LinkedHashMap<>();
        Collator instance = Collator.getInstance(Locale.CHINA);
        workShopMap.keySet().stream().sorted((o1, o2) -> instance.compare(o1, o2)).forEachOrdered(x ->workShopMapSort.put(x,workShopMap.get(x)));
        workShopMapSort.forEach((workshopName,workshopCmdList)->{
            Map<String,List<WorkshopShipmentsTempEntry>> cmdMap = workshopCmdList.stream().collect(groupingBy(WorkshopShipmentsTempEntry::getCommodityCode));
            //map排序
            Map<String,List<WorkshopShipmentsTempEntry>> cmdMapSort = new LinkedHashMap<>();
            cmdMap.entrySet().stream().sorted(Map.Entry.comparingByKey()).forEachOrdered(x ->cmdMapSort.put(x.getKey(),x.getValue()));

            //组装list：列表按照商品编码正序
            cmdMapSort.forEach((k,v)->{
                WorkshopShipmentsTempEntry commodity = v.get(0);
                BigDecimal totalQuantity = v.stream().map(WorkshopShipmentsTempEntry::getItemQuantity).reduce(BigDecimal.ZERO, BigDecimal::add);
                WorkshopShipmentsEntry record = new WorkshopShipmentsEntry();
                SpringUtil.copyProperties(commodity,record);
                record.setQuantity(totalQuantity);

                List<WorkshopShipmentsItemEntry> itemList = new ArrayList<>();
                //详情按照车位排序
                v.stream().sorted(Comparator.comparing(WorkshopShipmentsTempEntry::getCarportName,Comparator.nullsLast(String::compareTo))).forEach(temp->{
                    WorkshopShipmentsItemEntry item = new WorkshopShipmentsItemEntry();
                    item.setCarportName(temp.getCarportName());
                    item.setDeliverymanName(temp.getDeliverymanName());
                    item.setItemQuantity(temp.getItemQuantity());
                    itemList.add(item);
                });
                record.setItemList(itemList);
                list.add(record);
            });
        });
        return list;
    }

    /**
     * 生产组商品 排序
     * @param list
     * @return
     */
    public List<Long> sort(List<Long> list){
        if(SpringUtil.isEmpty(list)){
            return list;
        }
        Example example = new Example(FactoryWorkShopCommodityNum.class);
        example.createCriteria().andIn("commodityId",list);
        example.orderBy("sortNum").asc();
        List<FactoryWorkShopCommodityNum> factoryWorkShopCommodityNums = factoryWorkShopCommodityNumMapper.selectByExample(example);

        if(CollectionUtils.isEmpty(factoryWorkShopCommodityNums)){
            return list;
        }
        List<Long> commodityIdList = factoryWorkShopCommodityNums.stream().map(FactoryWorkShopCommodityNum::getCommodityId).collect(Collectors.toList());
        list.removeIf(item -> commodityIdList.contains(item));
        if(null != list && list.size() > 0){
            commodityIdList.addAll(list);
        }
        return commodityIdList;
    }
}

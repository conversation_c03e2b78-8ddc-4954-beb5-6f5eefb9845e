package com.pinshang.qingyun.orderreport.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.pinshang.qingyun.base.page.Pagination;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2019/2/28 14:07.
 */
@Data
public class ProductShipmentsRequestIDTO extends Pagination {

    @ApiModelProperty("工厂ID")
    private Long factoryId;

    @ApiModelProperty("生产组ID")
    private Long warehouseId;

    @ApiModelProperty("订单开始日期")
    @JsonFormat(pattern="yyyy-MM-dd",timezone = "GMT+8")
    private Date startOrderDate;

    @ApiModelProperty("订单结束日期")
    @JsonFormat(pattern="yyyy-MM-dd",timezone = "GMT+8")
    private Date endOrderDate;

    @ApiModelProperty("线路组ID")
    private Long lineGroupId;

    @ApiModelProperty("发货时间")
    private String deliveryTime;

    @ApiModelProperty("工厂名称")
    private String factoryName;

    @ApiModelProperty("生产组名称")
    private String warehouseName;

    @ApiModelProperty("线路组名称")
    private String lineGroupName;

    @ApiModelProperty("客户类型ID")
    private Long storeTypeId;

    @ApiModelProperty("生产组ID")
    private Long workshopId;

    @ApiModelProperty(value = "用户ID", hidden = true)
    private Long userId;

    @ApiModelProperty("送货批次.")
    private Integer deliveryBatch;
}

package com.pinshang.qingyun.orderreport.mapper.entry;

import lombok.Data;

import java.math.BigDecimal;
import java.math.RoundingMode;

/**
 *
 */
@Data
public class ProductStatisticsListEntry {

    private String storeCode;

    private String storeName;

    private String commodityName;

    private String commoditySpec;

    private BigDecimal commodityPrice;

    private BigDecimal commodityNum;

    private BigDecimal totalPrice;

    public BigDecimal getCommodityPrice() {
        return commodityPrice.setScale(2, RoundingMode.UP);
    }
}

package com.pinshang.qingyun.orderreport.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.pinshang.qingyun.base.page.Pagination;
import com.pinshang.qingyun.orderreport.enums.OrderModeTypeEnums;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2019/2/27 14:15.
 */
@Data
public class ProductShipmentsRequestVo  extends Pagination {

    @ApiModelProperty("工厂ID")
    private Long factoryId;

    @ApiModelProperty("生产组ID")
    private Long warehouseId;

    @ApiModelProperty("订单开始日期")
    @JsonFormat(pattern="yyyy-MM-dd",timezone = "GMT+8")
    private Date startOrderDate;

    @ApiModelProperty("订单结束日期")
    @JsonFormat(pattern="yyyy-MM-dd",timezone = "GMT+8")
    private Date endOrderDate;

    @ApiModelProperty("线路组ID")
    private Long lineGroupId;

    @ApiModelProperty("发货时间")
    private String deliveryTime;

    @ApiModelProperty("工厂名称")
    private String factoryName;

    @ApiModelProperty("生产组名称")
    private String warehouseName;

    @ApiModelProperty("线路组名称")
    private String lineGroupName;

    @ApiModelProperty("客户类型ID")
    private Long storeTypeId;

    @ApiModelProperty("生产组ID")
    private Long workshopId;

    @ApiModelProperty(value = "用户ID", hidden = true)
    private Long userId;

    @ApiModelProperty("送货批次.")
    private Integer deliveryBatch;

    @JsonIgnore
    private List<Long> factoryIds;

    @ApiModelProperty("补货数据 0-订单、 1-补货订单、 2-查所有0和1  （此字段没值也是查所有0和1 兼容清美组手低版本）")
    private Integer orderModeType;

    public void setFactoryIds(List<Long> factoryIds) {
        if(CollectionUtils.isNotEmpty(factoryIds)){
            this.factoryIds = factoryIds;
        }
    }

    public String getOrderModeTypeName() {
        if (this.orderModeType == null) {
            orderModeType = 2;
        }
        return OrderModeTypeEnums.getName(orderModeType);
    }
}

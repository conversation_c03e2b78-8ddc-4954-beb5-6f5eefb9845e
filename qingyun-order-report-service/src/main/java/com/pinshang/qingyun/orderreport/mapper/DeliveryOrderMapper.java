package com.pinshang.qingyun.orderreport.mapper;

import com.pinshang.qingyun.orderreport.mapper.entry.DeliveryOrderDetailEntry;
import com.pinshang.qingyun.orderreport.mapper.entry.DeliveryOrderEntry;
import com.pinshang.qingyun.orderreport.mapper.entry.DeliveryOrderItemPrintEntry;
import com.pinshang.qingyun.orderreport.mapper.entry.DeliveryOrderPrintEntry;
import com.pinshang.qingyun.orderreport.vo.DeliveryOrderReqVo;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2019/3/5 17:03
 */
@Repository
public interface DeliveryOrderMapper {

    List<DeliveryOrderEntry> queryList(@Param("vo")DeliveryOrderReqVo vo);

    //todo o.order_remark, store_destribe
    DeliveryOrderDetailEntry queryDetail(@Param("id") Long id);

    List<DeliveryOrderPrintEntry> query4Print(@Param("ids") List<Long> ids);

    List<DeliveryOrderItemPrintEntry> queryPrintItems(@Param("orderId")Long orderId);
}

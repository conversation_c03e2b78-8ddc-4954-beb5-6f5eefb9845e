package com.pinshang.qingyun.orderreport.pdf;

import com.itextpdf.text.*;
import com.itextpdf.text.pdf.PdfPCell;
import com.itextpdf.text.pdf.PdfPTable;
import com.itextpdf.text.pdf.PdfWriter;
import com.pinshang.qingyun.orderreport.config.CustomerProperties;
import com.pinshang.qingyun.orderreport.util.ListUtils;
import com.pinshang.qingyun.orderreport.util.PdfUtils;
import com.pinshang.qingyun.orderreport.vo.BillOfLadingPreviewRespVo;
import org.joda.time.DateTime;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.OutputStream;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.UUID;

@Component
public class BillOfLeadingPdfCreator {


    private static final Logger LOGGER = LoggerFactory.getLogger(BillOfLeadingPdfCreator.class);

    private final CustomerProperties customerProperties;

    private static final int pageSize = 43;

    @Autowired
    public BillOfLeadingPdfCreator(CustomerProperties customerProperties) {
        this.customerProperties = customerProperties;
    }


    /**
     * 打印 PDF
     * @param dataList 要打印的数据
     * @return 是否打印成功.
     */
    public String create(BillOfLadingPreviewRespVo dataList) {
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String printTime = dateFormat.format(new Date());
        if (dataList == null) {
            LOGGER.error("要生成PDF的内容为空.");
            return null;
        }

        String pdfSave = customerProperties.getAbsoluteSavePath();
        String savePath = customerProperties.getSavePath();
        Date date = new DateTime().plusDays(1).toDate();
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");
        String dataDir = format.format(date);
        File file = new File(pdfSave + "/" + savePath + "/" + dataDir);
        if (!file.exists()) {
            boolean mkdirs = file.mkdirs();
            if (!mkdirs) {
                LOGGER.error("创建保存pdf的目录失败,请检查权限");
                return null;
            }
        }

        Document document = new Document();
        document.setMargins(15, 15, 15, 15);
        OutputStream out;
        String fileName = extractFileName(dataList);
        try {
            out = new FileOutputStream(file.getAbsolutePath() + "/" +fileName);
            PdfWriter.getInstance(document, out);
            document.open();
            Font font = PdfUtils.chineseFont(11);
            Font fontEight = PdfUtils.chineseFont(8);
            Font fontTen = PdfUtils.chineseFont(10);
            PdfPTable title = proceedTableHeader(font, dataList);

            List<String> tableHeader = dataList.getTableHeader();

            List<List<String>> tableData = dataList.getTableData();
            List<List<List<String>>> pageContent = ListUtils.splitList(tableData, pageSize);

            float[] cellWidth = new float[tableHeader.size() ];
            cellWidth[0] = 99;
            for (int i = 1; i < tableHeader.size(); i++) {
                cellWidth[i] = 41;
            }
            int page = 1;
            int totalPage = pageContent.size();
            for (List<List<String>> content : pageContent) {
                PdfPTable pdfPTable = new PdfPTable(cellWidth);
                pdfPTable.setWidthPercentage(99);
                //添加头部信息
                document.add(title);
                for (String header : tableHeader) {
                    PdfPCell cell = new PdfPCell(new Phrase(header, font));
                    pdfPTable.addCell(cell);
                }
                //表格内容
                for (List<String> row : content) {
                    int i=0;;
                    for (String cellString : row) {
                        PdfPCell cell;
                        if(i != 0 && cellString.length()>=5){
                            cell = new PdfPCell(new Phrase(cellString, fontEight));
                        }else if(i != 0 && cellString.length() == 4){
                            cell = new PdfPCell(new Phrase(cellString, fontTen));
                        }else{
                            cell = new PdfPCell(new Phrase(cellString, font));
                        }
                        i++;

                        cell.setFixedHeight(16f);
                        pdfPTable.addCell(cell);
                    }
                }
                //表格bottom
                PdfPCell cell = new PdfPCell(new Phrase("第 " + page + " 页, 共 " + totalPage + "页.                打印时间: " + printTime, font));
                cell.setColspan(tableHeader.size());
                cell.setBorder(0);
                pdfPTable.addCell(cell);
                page++;
                document.add(pdfPTable);
                if (page <= totalPage) {
                    document.newPage();
                }
            }
            document.close();
        } catch (DocumentException | IOException e) {
            LOGGER.error("生成提货单PDF时异常:{}",e.getMessage());
            return null;
        }
        return String.format("%s%s",file.getAbsolutePath(),fileName);
       // return savePath + "/" + dataDir + "/" + fileName;
    }

    /**
     * 生成表格头部信息
     * @param font 中文字体
     * @param dataList pdf文档内容
     */
    private PdfPTable proceedTableHeader(Font font, BillOfLadingPreviewRespVo dataList) throws IOException, DocumentException {
        PdfPTable pdfPTable = new PdfPTable(3); //3列表格
        pdfPTable.setWidthPercentage(99);
        PdfPCell title = new PdfPCell(new Paragraph("提货单 ", PdfUtils.chineseFont(18)));
        title.setBorder(0);
        title.setColspan(4);
        title.setHorizontalAlignment(Element.ALIGN_CENTER);
        title.setVerticalAlignment(Element.ALIGN_CENTER);
        title.setFixedHeight(27);
        pdfPTable.addCell(title);

        PdfPCell cell;
        String lineCode = dataList.getLineCode() == null ? "" : dataList.getWarehouse();
        cell = new PdfPCell(new Phrase("线路编码: " + lineCode, font));
        cell.setBorder(0);
        pdfPTable.addCell(cell);

        String lineName = dataList.getLineName() == null ? "" : dataList.getLineName();
        cell = new PdfPCell(new Phrase("线路名称: " + lineName, font));
        cell.setBorder(0);
        pdfPTable.addCell(cell);

        String deliveryManName = dataList.getDeliveryManName() == null ? "" : dataList.getDeliveryManName();
        cell = new PdfPCell(new Phrase("送货员: " + deliveryManName, font));
        cell.setBorder(0);
        pdfPTable.addCell(cell);

        String date = "";
        if (dataList.getOrderDate() != null) {
            date = new SimpleDateFormat("yyyy-MM-dd").format(dataList.getOrderDate());
        }
        cell = new PdfPCell(new Phrase("订单日期: " + date, font));
        cell.setBorder(0);
        pdfPTable.addCell(cell);

        String lineGroup = dataList.getLineGroup() == null ? "" : dataList.getLineGroup();
        cell = new PdfPCell(new Phrase("线路组: " + lineGroup, font));
        cell.setBorder(0);
        pdfPTable.addCell(cell);

        String warehouse = dataList.getWarehouse() == null ? "" : dataList.getWarehouse();
        cell = new PdfPCell(new Phrase("发货仓库: " + warehouse, font));
        cell.setBorder(0);
        pdfPTable.addCell(cell);
        return pdfPTable;
    }

    /**
     * 文件名
     * @param dataList 要生成PDF的内容
     * @return 返回文件名
     */
    private String extractFileName(BillOfLadingPreviewRespVo dataList) {
        Date orderDate = dataList.getOrderDate();
        String lineCode = dataList.getLineCode();
        String date = "";
        if (orderDate != null) {
            date = new SimpleDateFormat("yyyy-MM-dd").format(orderDate);
        }
        return "/" + lineCode + "_" + date + "_" + UUID.randomUUID().toString().substring(0,8) + ".pdf";
    }
}
package com.pinshang.qingyun.orderreport.controller.api;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.pinshang.qingyun.base.api.TokenInfo;
import com.pinshang.qingyun.base.util.FastThreadLocalUtil;
import com.pinshang.qingyun.orderreport.dto.deliverynote.DeliverymanStoreInfoODTO;
import com.pinshang.qingyun.orderreport.dto.deliverynote.SelectDeliverymanStoreInfoListIDTO;
import com.pinshang.qingyun.orderreport.service.DeliveryNoteService;

@RestController
@RequestMapping("/deliveryNote/api")
@Api(value = "送货员配送-Api", tags = "送货员配送-Api", description = "DeliveryNoteApiController")
public class DeliveryNoteApiController {

	@Autowired
	private DeliveryNoteService deliveryNoteService;

	@ApiOperation(value = "查询  送货员-客户信息 列表")
	@PostMapping(value = "/selectDeliverymanStoreInfoList")
	public List<DeliverymanStoreInfoODTO> selectDeliverymanStoreInfoList(@RequestBody SelectDeliverymanStoreInfoListIDTO idto) {
		TokenInfo tokenInfo = FastThreadLocalUtil.getQY();
		idto.setDeliverymanId(tokenInfo.getEmployeeId());
		return deliveryNoteService.selectDeliverymanStoreInfoList(idto);
	}

}

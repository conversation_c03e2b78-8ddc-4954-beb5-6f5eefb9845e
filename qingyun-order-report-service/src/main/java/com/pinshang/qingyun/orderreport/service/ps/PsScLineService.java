package com.pinshang.qingyun.orderreport.service.ps;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.pinshang.qingyun.base.configure.expand.QYAssert;
import com.pinshang.qingyun.base.enums.log.LogBusinessTypeEnum;
import com.pinshang.qingyun.base.enums.log.LogOperateEnum;
import com.pinshang.qingyun.base.enums.log.LogOperateTypeEnum;
import com.pinshang.qingyun.box.utils.StringUtil;
import com.pinshang.qingyun.common.service.CodeClient;
import com.pinshang.qingyun.orderreport.config.OrderReportConstant;
import com.pinshang.qingyun.orderreport.mapper.*;
import com.pinshang.qingyun.orderreport.model.*;
import com.pinshang.qingyun.orderreport.vo.ps.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Version 0.1.0
 * @Date 2021/1/18 10:40
 * @Copyright © 2019-2021 qm
 * @Description -- 类说明
 **/
@Service
@Slf4j
public class PsScLineService {
    @Autowired
    private PsScLineMapper psScLineMapper;

    @Autowired
    private PsScLineStoreMapper psScLineStoreMapper;

    @Autowired
    private PsScOperationLogMapper psScOperationLogMapper;

    @Autowired
    private PsScCommodityGroupMapper psScCommodityGroupMapper;

    @Autowired
    private EmployeeUserMapper employeeUserMapper;

    @Autowired
    private StoreMapper storeMapper;

    @Autowired
    private CodeClient codeClient;

    @Autowired
    private RedissonClient redissonClient;

    private String add_store_error = "您本次共成功添加%s个客户，失败%s个客户";

    public PageInfo<PsScLineVO> queryCommodityGroupLineList(QueryCommodityGroupLineVO dto) {
        return PageHelper.startPage(dto.getPageNo(), dto.getPageSize()).doSelectPageInfo(() -> {
            psScLineMapper.queryCommodityGroupLineList(dto);
        });
    }

    public PageInfo<PsScLineStoreVO> queryCommodityGroupLineStoreList(QueryCommodityGroupLineStoreVO dto) {
        return PageHelper.startPage(dto.getPageNo(), dto.getPageSize()).doSelectPageInfo(() -> {
            psScLineMapper.queryCommodityGroupLineStoreList(dto);
        });
    }

    public List<PsScLineInfoVO> queryCommodityGroupLineAllList() {
        return psScLineMapper.queryCommodityGroupLineAllList();
    }

    public List<PsScCommodityGroupSelectVO> queryCommodityGroupSelectList(QueryCommodityGroupSelectVO dto) {
        if (dto.getLineId() == null || dto.getLineId() == -1) {
            return psScLineMapper.queryCommodityGroupSelectList2(dto);
        } else {
            List<PsScCommodityGroupSelectVO> psScCommodityGroupSelectList = psScLineMapper.queryCommodityGroupSelectList1(dto);
            PsScCommodityGroup psScCommodityGroup = psScCommodityGroupMapper.queryCommodityGroupByLineId(dto.getLineId());
            PsScCommodityGroupSelectVO psScCommodityGroupSelectVO = new PsScCommodityGroupSelectVO(psScCommodityGroup.getId().toString(), psScCommodityGroup.getGroupCode(), psScCommodityGroup.getGroupName());
            QYAssert.notNull(psScCommodityGroup, "选择的线路不存在！");
            if (psScCommodityGroupSelectList.isEmpty()) {
                psScCommodityGroupSelectList.add(psScCommodityGroupSelectVO);
                return psScCommodityGroupSelectList;
            }
            Boolean flag = Boolean.TRUE;
            for (PsScCommodityGroupSelectVO cg : psScCommodityGroupSelectList) {
                if (cg.getCommodityGroupCode().equals(psScCommodityGroup.getGroupCode())) {
                    flag = Boolean.FALSE;
                }
            }
            if (flag) {
                psScCommodityGroupSelectList.add(0, psScCommodityGroupSelectVO);
            }
            return psScCommodityGroupSelectList;
        }
    }


    @Transactional(rollbackFor = Exception.class)
    public Boolean modCommodityGroupLine(ModCommodityGroupLineVO dto) {
        RLock lock = redissonClient.getLock(OrderReportConstant.MOD_COMMODITY_GROUP_SC_LINE_LOCK);
        try {
            lock.lock(OrderReportConstant.LOCK_EXPIRE_TIME, TimeUnit.SECONDS);
            QYAssert.notNull(dto.getId(), "缺失修改主键！");
            PsScLine psScLine = psScLineMapper.selectByPrimaryKey(dto.getId());
            QYAssert.notNull(psScLine, "您修改的商品组线路不存在！");
            if (!StringUtil.isNullOrEmpty(dto.getLineName())) {
                PsScLine psScLineOld = psScLineMapper.selectByGroupLineName(dto.getLineName());
                QYAssert.isTrue(psScLineOld == null, "您修改的商品组线路名称已存在");
            }
            Long commodityGroupId = dto.getCommodityGroupId();
            if (commodityGroupId != null) {
                QYAssert.isTrue(!commodityGroupId.equals(psScLine.getCommodityGroupId()), "您修改的商品组已存在");
                // 商品组与客户互斥逻辑
                if (psScLine.getStoreQuantity() > 0) {
                    List<MutexCommodityGroupVO> mutexCommodityGroupVOList = psScLineMapper.mutexCommodityGroup2Store(commodityGroupId, dto.getId());
                    QYAssert.isTrue(mutexCommodityGroupVOList.isEmpty(), "您修改的商品组已被客户在其他线路组使用！");
                }
            }
            psScLineMapper.modCommodityGroupLine(dto);
            modCommodityGroupLineLog(psScLine, dto);
        } finally {
            lock.unlock();
        }
        return Boolean.TRUE;
    }


    @Transactional(rollbackFor = Exception.class)
    public Boolean onOffCommodityGroupLine(OnOffCommodityGroupLineVO dto) {
        QYAssert.notNull(dto.getLineId(), "缺失线路id！");
        PsScLine psScLine = psScLineMapper.selectByPrimaryKey(dto.getLineId());
        QYAssert.notNull(psScLine, "您修改的商品组线路不存在！");
        ModCommodityGroupLineVO modCommodityGroupLineVO = new ModCommodityGroupLineVO();
        modCommodityGroupLineVO.setId(dto.getLineId());
        modCommodityGroupLineVO.setUpdateId(dto.getCreateId());
        modCommodityGroupLineVO.setStatus(!psScLine.getStatus());
        psScLineMapper.modCommodityGroupLine(modCommodityGroupLineVO);
        modCommodityGroupLineLog(psScLine, modCommodityGroupLineVO);
        return Boolean.TRUE;
    }


    @Transactional(rollbackFor = Exception.class)
    public PsResult addCommodityGroupLineStore(AddCommodityGroupLineStoreVO dto) {
        PsResult psResult = new PsResult();
        psResult.setSuccess(Boolean.FALSE);
        RLock lock = redissonClient.getLock(OrderReportConstant.ADD_COMMODITY_GROUP_SC_LINE_STORE_LOCK);
        try {
            lock.lock(OrderReportConstant.LOCK_EXPIRE_TIME, TimeUnit.SECONDS);
            Long lineId = dto.getLineId();
            QYAssert.isTrue(!(dto == null || lineId == null || dto.getCreateId() == null), "参数有误!");
            QYAssert.isTrue(StringUtils.isNotBlank(dto.getStoreCodes()), "客户编码不能为空!");
            PsScLine psScLine = psScLineMapper.selectByPrimaryKey(dto.getLineId());
            QYAssert.notNull(psScLine, "线路不存在!");
            String storeCodes = dto.getStoreCodes();
            List<String> storeCodesList = Arrays.asList(storeCodes.split("\n"));
            final Integer commoditySize = 200;
            if (null != storeCodesList && storeCodesList.size() > commoditySize) {
                QYAssert.isTrue(false, "每次最多200个客户!");
            }
            // 去重处理
            storeCodesList = storeCodesList.stream().distinct().collect(Collectors.toList());
            // 筛选当前线路下不重复的客户
            List<CommodityGroupLineStoreVO> commodityGroupLineStoreList = psScLineStoreMapper.selectNotLinkByStoreCodeList(dto.getLineId(), storeCodesList);
            QYAssert.isTrue(!commodityGroupLineStoreList.isEmpty(), String.format(add_store_error, 0, storeCodesList.size()));
            // 商品组与客户互斥逻辑(筛选当前商品组在不同线路下不重复的客户)
            List<CommodityGroupLineStoreVO> notRepeatStoreList = psScLineStoreMapper.selectNotRepeatStoreListInCommodityGroup(psScLine.getCommodityGroupId(), commodityGroupLineStoreList);
            QYAssert.isTrue(!notRepeatStoreList.isEmpty(), String.format(add_store_error, 0, storeCodesList.size()));
//            List<Long> storeIdsList = notRepeatStoreList.stream().map(CommodityGroupLineStoreVO -> CommodityGroupLineStoreVO.getStoreId()).collect(Collectors.toList());
            // 商品组与客户互斥逻辑
//            List<MutexCommodityGroupStoreVO> mutexCommodityGroupStoreList = psScLineMapper.mutexStore2CommodityGroup(dto.getLineId(), psScLine.getCommodityGroupId(), storeIdsList);
//            Map<String, Long> codeMap = mutexCommodityGroupStoreList.stream().filter(item->!item.getFlag()).collect(Collectors.toMap(MutexCommodityGroupStoreVO::getStoreCode, MutexCommodityGroupStoreVO::getStoreId));
            Map<String, Long> codeMap = notRepeatStoreList.stream().collect(Collectors.toMap(CommodityGroupLineStoreVO::getStoreCode, CommodityGroupLineStoreVO::getStoreId));

            List<String> codesListError = new ArrayList<>();
            List<PsScLineStore> codesListSuccess = new ArrayList<>(codeMap.size());
            List<String> storeCodeLogList = new ArrayList<>(codeMap.size());
            storeCodesList.forEach(code -> {
                if (codeMap.containsKey(code)) {
                    PsScLineStore psScLineStore = new PsScLineStore();
                    psScLineStore.setLineId(lineId);
                    psScLineStore.setStoreId(codeMap.get(code));
                    storeCodeLogList.add(code);
                    codesListSuccess.add(psScLineStore);
                } else {
                    codesListError.add(code);
                }
            });
            if (codesListSuccess.isEmpty()) {
                psResult.setMsg(String.format(add_store_error, codesListSuccess.size(), codesListError.size()));
                return psResult;
            }
            psScLineStoreMapper.insertList(codesListSuccess);

            psScLine.setStoreQuantity(psScLine.getStoreQuantity() + codesListSuccess.size());
            psScLineMapper.updateByPrimaryKeySelective(psScLine);

            addCommodityGroupLineStoreLog(psScLine, storeCodeLogList, dto.getCreateId());
            if (!codesListError.isEmpty()) {
                psResult.setMsg(String.format(add_store_error, codesListSuccess.size(), codesListError.size()));
                return psResult;
            }
        } finally {
            lock.unlock();
        }
        psResult.setSuccess(Boolean.TRUE);
        return psResult;
    }
    @Transactional(rollbackFor = Exception.class)
    public Boolean delCommodityGroupLineStore(DelCommodityGroupLineStoreVO dto) {
        QYAssert.notNull(dto.getId(),"删除主键缺失！");
        RLock lock = redissonClient.getLock(OrderReportConstant.DEL_COMMODITY_GROUP_SC_LINE_STORE_LOCK);
        try {
            lock.lock(OrderReportConstant.LOCK_EXPIRE_TIME, TimeUnit.SECONDS);
            PsScLineStore psScLineStore = psScLineStoreMapper.selectByPrimaryKey(dto.getId());
            QYAssert.notNull(psScLineStore, "需要删除的商品不存在！");
            PsScLine psScLine = psScLineMapper.selectByPrimaryKey(psScLineStore.getLineId());
            psScLine.setStoreQuantity(psScLine.getStoreQuantity() - 1);
            psScLineMapper.updateByPrimaryKeySelective(psScLine);
            psScLineStoreMapper.deleteByPrimaryKey(dto.getId());
            delCommodityGroupLineStoreLog(psScLine, psScLineStore, dto.getCreateId());
        } finally {
            lock.unlock();
        }
        return Boolean.TRUE;
    }


    @Transactional(rollbackFor = Exception.class)
    public Boolean addCommodityGroupLine(AddCommodityGroupLineVO dto) {
        PsScLine psScLine = checkAddCommodityGroupLine(dto);
        RLock lock = redissonClient.getLock(OrderReportConstant.ADD_COMMODITY_GROUP_SC_LINE_LOCK);
        try {
            lock.lock(OrderReportConstant.LOCK_EXPIRE_TIME, TimeUnit.SECONDS);
            PsScLine psScLineOld = psScLineMapper.selectByGroupLineName(dto.getLineName());
            QYAssert.isTrue(psScLineOld == null, "您输入的商品组线路名称已存在");
//            List<String> codeList = codeClient.batchCreateCode(OrderReportConstant.PS_SC_COMMODITY_LINE, 1);
            String code = codeClient.createCode(OrderReportConstant.PS_SC_COMMODITY_LINE);
            QYAssert.notNull(code, "生成线路编码失败！");
            psScLine.setLineCode(code);
            psScLineMapper.insert(psScLine);
            addCommodityGroupLineLog(psScLine);
        } finally {
            lock.unlock();
        }

        return Boolean.TRUE;
    }

    /**
     * 商品组与客户互斥逻辑
     */
    public void mutexCommodityGroup2Store(Long commodityGroupId, Long lineId) {
        List<MutexCommodityGroupVO> mutexCommodityGroupVOList = psScLineMapper.mutexCommodityGroup2Store(commodityGroupId, lineId);

    }

    /**
     * 新增商品组线路日志
     *
     * @param psScLine
     */
    @Transactional(rollbackFor = Exception.class)
    public void addCommodityGroupLineLog(PsScLine psScLine) {
        Date date = new Date();
        List<PsScOperationLog> psScOperationLogList = new ArrayList<>(9);
        PsScOperationLog psScOperationLog1 = new PsScOperationLog(LogBusinessTypeEnum.COMMODITY_GROUP_LINE, LogOperateTypeEnum.ADD, LogOperateEnum.ADD, psScLine.getCreateId(),
                psScLine.getLineName(), psScLine.getLineCode(), "line_group_name", "线路组名称", "新增线路", null, psScLine.getLineGroupName() + "", date
        );
        psScOperationLogList.add(psScOperationLog1);

        PsScOperationLog psScOperationLog2 = new PsScOperationLog(LogBusinessTypeEnum.COMMODITY_GROUP_LINE, LogOperateTypeEnum.ADD, LogOperateEnum.ADD, psScLine.getCreateId(),
                psScLine.getLineName(), psScLine.getLineCode(), "line_code", "线路编码", "新增线路", null, psScLine.getLineCode(), date
        );
        psScOperationLogList.add(psScOperationLog2);

        PsScOperationLog psScOperationLog3 = new PsScOperationLog(LogBusinessTypeEnum.COMMODITY_GROUP_LINE, LogOperateTypeEnum.ADD, LogOperateEnum.ADD, psScLine.getCreateId(),
                psScLine.getLineName(), psScLine.getLineCode(), "line_name", "线路名称", "新增线路", null, psScLine.getLineName(), date
        );
        psScOperationLogList.add(psScOperationLog3);

        PsScCommodityGroup psScCommodityGroup = psScCommodityGroupMapper.selectByPrimaryKey(psScLine.getCommodityGroupId());
        PsScOperationLog psScOperationLog4 = new PsScOperationLog(LogBusinessTypeEnum.COMMODITY_GROUP_LINE, LogOperateTypeEnum.ADD, LogOperateEnum.ADD, psScLine.getCreateId(),
                psScLine.getLineName(), psScLine.getLineCode(), "commodity_group_id", "商品组名称", "新增线路", null, psScCommodityGroup.getGroupName(), date
        );
        psScOperationLogList.add(psScOperationLog4);

        PsScOperationLog psScOperationLog5 = new PsScOperationLog(LogBusinessTypeEnum.COMMODITY_GROUP_LINE, LogOperateTypeEnum.ADD, LogOperateEnum.ADD, psScLine.getCreateId(),
                psScLine.getLineName(), psScLine.getLineCode(), "delivery_warehouse_name", "发货仓库名称", "新增线路", null, psScLine.getDeliveryWarehouseName(), date
        );
        psScOperationLogList.add(psScOperationLog5);

        String deliverymanName = employeeUserMapper.queryEmployeeUserByEmployeeId(psScLine.getDeliverymanId());
        PsScOperationLog psScOperationLog6 = new PsScOperationLog(LogBusinessTypeEnum.COMMODITY_GROUP_LINE, LogOperateTypeEnum.ADD, LogOperateEnum.ADD, psScLine.getCreateId(),
                psScLine.getLineName(), psScLine.getLineCode(), "deliveryman_id", "送货员", "新增线路", null, deliverymanName, date
        );
        psScOperationLogList.add(psScOperationLog6);

        PsScOperationLog psScOperationLog7 = new PsScOperationLog(LogBusinessTypeEnum.COMMODITY_GROUP_LINE, LogOperateTypeEnum.ADD, LogOperateEnum.ADD, psScLine.getCreateId(),
                psScLine.getLineName(), psScLine.getLineCode(), "carport_name", "线路车位", "新增线路", null, psScLine.getCarportName(), date
        );
        psScOperationLogList.add(psScOperationLog7);

        PsScOperationLog psScOperationLog8 = new PsScOperationLog(LogBusinessTypeEnum.COMMODITY_GROUP_LINE, LogOperateTypeEnum.ADD, LogOperateEnum.ADD, psScLine.getCreateId(),
                psScLine.getLineName(), psScLine.getLineCode(), "license_plate", "线路车牌号", "新增线路", null, psScLine.getLicensePlate(), date
        );
        psScOperationLogList.add(psScOperationLog8);

        PsScOperationLog psScOperationLog9 = new PsScOperationLog(LogBusinessTypeEnum.COMMODITY_GROUP_LINE, LogOperateTypeEnum.ADD, LogOperateEnum.ADD, psScLine.getCreateId(),
                psScLine.getLineName(), psScLine.getLineCode(), "status", "线路状态", "新增线路", null, "启用", date
        );
        psScOperationLogList.add(psScOperationLog9);
        psScOperationLogMapper.insertList(psScOperationLogList);
    }

    /**
     * 修改商品组线路日志
     *
     * @param psScLine
     */
    @Transactional(rollbackFor = Exception.class)
    public void modCommodityGroupLineLog(PsScLine psScLine, ModCommodityGroupLineVO dto) {
        Date date = new Date();
        String lineName = psScLine.getLineName();

        List<PsScOperationLog> psScOperationLogList = new ArrayList<>(7);
        if (!StringUtil.isNullOrEmpty(dto.getLineName())) {
            lineName = dto.getLineName();
            PsScOperationLog psScOperationLog2 = new PsScOperationLog(LogBusinessTypeEnum.COMMODITY_GROUP_LINE, LogOperateTypeEnum.MOD, LogOperateEnum.MOD, dto.getUpdateId(),
                    lineName, psScLine.getLineCode(), "line_name", "线路名称", "修改线路", psScLine.getLineName(), dto.getLineName(), date
            );
            psScOperationLogList.add(psScOperationLog2);

        }
        if (!StringUtil.isNullOrEmpty(dto.getLineGroupName())) {
            PsScOperationLog psScOperationLog1 = new PsScOperationLog(LogBusinessTypeEnum.COMMODITY_GROUP_LINE, LogOperateTypeEnum.MOD, LogOperateEnum.MOD, dto.getUpdateId(),
                    lineName, psScLine.getLineCode(), "line_group_name", "线路组名称", "修改线路", psScLine.getLineGroupName(), dto.getLineGroupName() + "", date
            );
            psScOperationLogList.add(psScOperationLog1);
        }

        if (dto.getCommodityGroupId() != null) {
            PsScCommodityGroup psScCommodityGroupOld = psScCommodityGroupMapper.selectByPrimaryKey(psScLine.getCommodityGroupId());
            PsScCommodityGroup psScCommodityGroupNew = psScCommodityGroupMapper.selectByPrimaryKey(dto.getCommodityGroupId());
            PsScOperationLog psScOperationLog3 = new PsScOperationLog(LogBusinessTypeEnum.COMMODITY_GROUP_LINE, LogOperateTypeEnum.MOD, LogOperateEnum.MOD, dto.getUpdateId(),
                    lineName, psScLine.getLineCode(), "commodity_group_id", "商品组名称", "修改线路", psScCommodityGroupOld == null ? "" : psScCommodityGroupOld.getGroupName(), psScCommodityGroupNew == null ? "" : psScCommodityGroupNew.getGroupName(), date
            );
            psScOperationLogList.add(psScOperationLog3);
        }

        if (dto.getDeliveryWarehouseId() != null) {
            PsScOperationLog psScOperationLog4 = new PsScOperationLog(LogBusinessTypeEnum.COMMODITY_GROUP_LINE, LogOperateTypeEnum.MOD, LogOperateEnum.MOD, dto.getUpdateId(),
                    lineName, psScLine.getLineCode(), "delivery_warehouse_name", "发货仓库名称", "修改线路", psScLine.getDeliveryWarehouseName() + "", dto.getDeliveryWarehouseName() + "", date
            );
            psScOperationLogList.add(psScOperationLog4);
        }

        if (dto.getDeliverymanId() != null) {
            String deliverymanNameOld = employeeUserMapper.queryEmployeeUserByEmployeeId(psScLine.getDeliverymanId());
            String deliverymanNameNew = employeeUserMapper.queryEmployeeUserByEmployeeId(dto.getDeliverymanId());
            PsScOperationLog psScOperationLog5 = new PsScOperationLog(LogBusinessTypeEnum.COMMODITY_GROUP_LINE, LogOperateTypeEnum.MOD, LogOperateEnum.MOD, dto.getUpdateId(),
                    lineName, psScLine.getLineCode(), "deliveryman_id", "送货员", "修改线路", deliverymanNameOld, deliverymanNameNew, date
            );
            psScOperationLogList.add(psScOperationLog5);
        }

        if (!StringUtil.isNullOrEmpty(dto.getCarportName())) {
            PsScOperationLog psScOperationLog6 = new PsScOperationLog(LogBusinessTypeEnum.COMMODITY_GROUP_LINE, LogOperateTypeEnum.MOD, LogOperateEnum.MOD, dto.getUpdateId(),
                    lineName, psScLine.getLineCode(), "carport_name", "线路车位", "修改线路", psScLine.getCarportName(), dto.getCarportName(), date
            );
            psScOperationLogList.add(psScOperationLog6);
        }

        if (!StringUtil.isNullOrEmpty(dto.getLicensePlate())) {
            PsScOperationLog psScOperationLog7 = new PsScOperationLog(LogBusinessTypeEnum.COMMODITY_GROUP_LINE, LogOperateTypeEnum.MOD, LogOperateEnum.MOD, dto.getUpdateId(),
                    lineName, psScLine.getLineCode(), "license_plate", "线路车牌号", "修改线路", psScLine.getLicensePlate(), dto.getLicensePlate(), date
            );
            psScOperationLogList.add(psScOperationLog7);
        }

        if (dto.getStatus() != null) {
            PsScOperationLog psScOperationLog8 = new PsScOperationLog(LogBusinessTypeEnum.COMMODITY_GROUP_LINE, LogOperateTypeEnum.MOD, dto.getStatus() ? LogOperateEnum.ON : LogOperateEnum.OFF, dto.getUpdateId(),
                    lineName, psScLine.getLineCode(), "license_plate", "线路状态", dto.getStatus() ? "启用线路" : "停用线路", psScLine.getStatus() ? "启用" : "停用", dto.getStatus() ? "启用" : "停用", date
            );
            psScOperationLogList.add(psScOperationLog8);
        }

        if (!psScOperationLogList.isEmpty()) {
            psScOperationLogMapper.insertList(psScOperationLogList);
        }
    }

    /**
     * 添加线路客户日志
     *
     * @param psScLine
     */
    @Transactional(rollbackFor = Exception.class)
    public void addCommodityGroupLineStoreLog(PsScLine psScLine, List<String> storeCodeLogList, Long createId) {
        Date date = new Date();
        String lineName = psScLine.getLineName();

        List<PsScOperationLog> psScOperationLogList = new ArrayList<>(storeCodeLogList.size());

        storeCodeLogList.forEach(storeCode -> {
            PsScOperationLog psScOperationLog = new PsScOperationLog(LogBusinessTypeEnum.COMMODITY_GROUP_LINE_STORE, LogOperateTypeEnum.ADD, LogOperateEnum.APPEND, createId,
                    lineName, psScLine.getLineCode(), "store_id", "客户编码", "添加客户", null, storeCode, date
            );
            psScOperationLogList.add(psScOperationLog);
        });


        if (!psScOperationLogList.isEmpty()) {
            psScOperationLogMapper.insertList(psScOperationLogList);
        }
    }
    /**
     * 删除线路客户日志
     *
     * @param psScLine
     */
    @Transactional(rollbackFor = Exception.class)
    public void delCommodityGroupLineStoreLog(PsScLine psScLine, PsScLineStore psScLineStore, Long createId) {
        Store store = storeMapper.selectByPrimaryKey(psScLineStore.getStoreId());
        if (store == null) {
            log.error("客户id为",psScLineStore.getStoreId(),"的客户不存在！线路删除客户没有记录日志！");
            return;
        }
        PsScOperationLog psScOperationLog = new PsScOperationLog(LogBusinessTypeEnum.COMMODITY_GROUP_LINE_STORE, LogOperateTypeEnum.DEL, LogOperateEnum.DEL, createId,
                psScLine.getLineName(), psScLine.getLineCode(), "store_id", "客户编码", "删除客户", store.getStoreCode(), null, new Date()
        );
        psScOperationLogMapper.insert(psScOperationLog);

    }


    private PsScLine checkAddCommodityGroupLine(AddCommodityGroupLineVO dto){
        QYAssert.notNull(dto, "参数缺失！");
        QYAssert.isTrue(!StringUtil.isNullOrEmpty(dto.getCarportName()), "车位不能为空！");
        QYAssert.isTrue(!StringUtil.isNullOrEmpty(dto.getLicensePlate()), "车牌号不能为空！");
        QYAssert.isTrue(!StringUtil.isNullOrEmpty(dto.getLineName()), "线路名称不能为空！");
        QYAssert.notNull(dto.getLineGroupId(), "线路组id 不能为空！");
        QYAssert.notNull(dto.getLineGroupName(), "线路组名称 不能为空！");
        QYAssert.notNull(dto.getCommodityGroupId(), "商品组id 不能为空！");
        QYAssert.notNull(dto.getDeliverymanId(), "送货员id 不能为空！");
        QYAssert.notNull(dto.getDeliveryWarehouseId(), "发货仓库id 不能为空！");
        QYAssert.notNull(dto.getDeliveryWarehouseName(), "发货仓库名称 不能为空！");
        QYAssert.notNull(dto.getCreateId(), "创建人 不能为空！");
        QYAssert.notNull(dto.getUpdateId(), "更新人 不能为空！");
        Date date = new Date();
        PsScLine psScLine = new PsScLine();
        BeanUtils.copyProperties(dto, psScLine);
        psScLine.setCreateTime(date);
        psScLine.setUpdateTime(date);
        psScLine.setStoreQuantity(0L);
        psScLine.setStatus(Boolean.TRUE);
        return psScLine;
    }


}

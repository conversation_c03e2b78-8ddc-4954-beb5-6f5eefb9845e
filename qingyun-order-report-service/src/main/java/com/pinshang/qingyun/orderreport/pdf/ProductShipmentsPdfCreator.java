package com.pinshang.qingyun.orderreport.pdf;

import com.itextpdf.text.*;
import com.itextpdf.text.pdf.PdfPCell;
import com.itextpdf.text.pdf.PdfPTable;
import com.itextpdf.text.pdf.PdfWriter;
import com.pinshang.qingyun.orderreport.config.CustomerProperties;
import com.pinshang.qingyun.orderreport.util.ListUtils;
import com.pinshang.qingyun.orderreport.util.PdfUtils;
import com.pinshang.qingyun.orderreport.vo.ProductShipmentResponseVo;
import com.pinshang.qingyun.orderreport.vo.ProductShipmentsRequestVo;
import org.apache.commons.lang3.StringUtils;
import org.joda.time.DateTime;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.UUID;

/**
 * <AUTHOR>
 * @date 2019/3/4 18:21.
 */
@Component
public class ProductShipmentsPdfCreator {

    private final CustomerProperties customerProperties;

    private static final Integer pageRow = 44;


    private static final Logger LOGGER = LoggerFactory.getLogger(ProductShipmentsPdfCreator.class);

    /**
     * 商品名称保留多长
     */
    private static final Integer goodsNameLength = 16;

    @Autowired
    public ProductShipmentsPdfCreator(CustomerProperties customerProperties) {
        this.customerProperties = customerProperties;
    }

    public String create(ProductShipmentsRequestVo requestVo, ProductShipmentResponseVo pdfContent) {
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String printTime = dateFormat.format(new Date());
        String pdfSave = customerProperties.getAbsoluteSavePath();
        String savePath = customerProperties.getSavePath();
        Date date = new DateTime().plusDays(1).toDate();
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");
        String dataDir = format.format(date);
        File file = new File(pdfSave + "/" +savePath + "/" + dataDir);
        if (!file.exists()) {
            boolean mkdirs = file.mkdirs();
            if (!mkdirs) {
                LOGGER.error("创建用于保存pdf的目录时失败,请检查权限问题");
                return null;
            }
        }

        String fileName = extractFileName(requestVo);

        Document document = new Document(PageSize.A4);
        document.setMargins(15, 15, 15, 15);
        FileOutputStream outputStream;
        try {
            outputStream = new FileOutputStream(file.getAbsolutePath() + fileName);
            PdfWriter.getInstance(document, outputStream);
            document.open();
            Font font = PdfUtils.chineseFont(11);
            PdfPTable header = proceedTableHeader(font, requestVo);

            List<List<List<String>>> pageContent = ListUtils.splitList(pdfContent.getTableData(), pageRow);
            int pageNo = 1;
            int totalPage = pageContent.size();
            List<String> tableTitle = pdfContent.getTableTitle();
            for (List<List<String>> page : pageContent) {
                //表头
                PdfPTable table = new PdfPTable(new float[]{15, 50, 10, 15});
                table.setWidthPercentage(100);
                document.add(header);
                for (String title : tableTitle) {
                    // 因为要隐藏 商品编号字段
                    if (!"生产组".equals(title) && !"商品名称(规格)".equals(title) && !"计量单位".equals(title) && !"合计".equals(title)) {
                        continue;
                    }
                    PdfPCell cell = new PdfPCell(new Phrase(title, font));
                    cell.setPadding(0);
                    cell.setFixedHeight(16);
                    table.addCell(cell);
                }
                //内容
                for (List<String> row : page) {
                    for (int i = 0 ; i< row.size(); i ++) {
                        String goodsName = row.get(i);
                        if (i != 0 && i != 2 && i != 3 && i != row.size() -1) {
                            // 因为要隐藏 商品编号字段
                            continue;
                        }
                        PdfPCell cell = new PdfPCell(new Phrase(goodsName, font));
                        cell.setFixedHeight(16);
                        cell.setPadding(1);
                        table.addCell(cell);
                    }
                }
                //页尾
                PdfPCell cell = new PdfPCell(new Phrase("第 " + pageNo + " 页, 共 " + totalPage + "页.                打印时间: " + printTime, font));
                cell.setColspan(4);
                cell.setBorder(0);
                table.addCell(cell);
                pageNo++;
                document.add(table);
                if (pageNo <= totalPage) {
                    document.newPage();
                }
            }
            document.close();
        } catch (DocumentException | IOException e) {
            LOGGER.error("产品发货总量生成PDF时异常:{}", e.getMessage());
            return null;
        }
        return String.format("%s%s",file.getAbsolutePath(),fileName);
        //return savePath + "/" + dataDir + "/"  +fileName;
    }

    /**
     * 设置pdf表格头
     * @param font 中文字体
     * @param requestVo 请求参数,用于设置表头
     */
    private PdfPTable proceedTableHeader(Font font, ProductShipmentsRequestVo requestVo) throws DocumentException, IOException {
        String deliveryTime = requestVo.getDeliveryTime();
        String factoryName = requestVo.getFactoryName();
        String lineGroupName = requestVo.getLineGroupName();
        Date startOrderDate = requestVo.getStartOrderDate();
        Date endOrderDate = requestVo.getEndOrderDate();
        String warehouseName = requestVo.getWarehouseName();
        if (StringUtils.isBlank(deliveryTime)) {
            deliveryTime = "";
        }
        if (StringUtils.isBlank(factoryName)) {
            factoryName = "";
        }
        if (StringUtils.isBlank(lineGroupName)) {
            lineGroupName = "";
        }
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");
        String startDate = "";
        if (startOrderDate != null) {
            startDate = format.format(startOrderDate);
        }
        String endDate = "";
        if (endOrderDate != null) {
            endDate = format.format(endOrderDate);
        }
        if (StringUtils.isBlank(warehouseName)) {
            warehouseName = "";
        }

        float[] cellWidth = {90,90,90};

        PdfPTable pdfPTable = new PdfPTable(cellWidth);

        pdfPTable.setWidthPercentage(100);
        PdfPCell title;
        title = new PdfPCell(new Paragraph("", PdfUtils.chineseFont(18)));
        title.setBorder(0);
        title.setFixedHeight(27);
        pdfPTable.addCell(title);

        title = new PdfPCell(new Paragraph("产品发货总量", PdfUtils.chineseFont(18)));
        title.setBorder(0);
        title.setHorizontalAlignment(Element.ALIGN_CENTER);
        title.setVerticalAlignment(Element.ALIGN_CENTER);
        title.setFixedHeight(27);
        pdfPTable.addCell(title);

        String orderModeTypeName =requestVo.getOrderModeTypeName();
        title = new PdfPCell(new Paragraph(StringUtils.isBlank(orderModeTypeName) ? "" : "（"+orderModeTypeName+"）", font));
        title.setBorder(0);
        title.setHorizontalAlignment(Element.ALIGN_RIGHT);
        title.setVerticalAlignment(Element.ALIGN_RIGHT);
        title.setFixedHeight(27);
        pdfPTable.addCell(title);


        PdfPCell cell;
        cell = new PdfPCell(new Phrase("工厂: " + factoryName, font));
        cell.setBorder(0);
        pdfPTable.addCell(cell);

        cell = new PdfPCell(new Phrase("发货时间: " + deliveryTime, font));
        cell.setBorder(0);
        pdfPTable.addCell(cell);

        cell = new PdfPCell(new Phrase("发货仓库: " + warehouseName, font));
        cell.setBorder(0);
        pdfPTable.addCell(cell);

        cell = new PdfPCell(new Phrase("线路组: " + lineGroupName, font));
        cell.setBorder(0);
        pdfPTable.addCell(cell);

        cell = new PdfPCell(new Phrase("订单日期: " + startDate + "至" + endDate, font));
        cell.setBorder(0);
        cell.setColspan(2);
        pdfPTable.addCell(cell);
        return pdfPTable;
    }

    /**
     * 生成pdf文件名
     * @param requestVo 条件
     * @return 返回文件名
     */
    private String extractFileName(ProductShipmentsRequestVo requestVo) {
        Date startOrderDate = requestVo.getStartOrderDate();
        Date endOrderDate = requestVo.getEndOrderDate();
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");
        String startDate = "";
        if (startOrderDate != null) {
            startDate = format.format(startOrderDate);
        }
        String endDate = "";
        if (endOrderDate != null) {
            endDate = format.format(endOrderDate);
        }
        String uuid = UUID.randomUUID().toString().substring(0, 8);
        return "/" + startDate +"_" + endDate +"_" + uuid +".pdf";
    }
}

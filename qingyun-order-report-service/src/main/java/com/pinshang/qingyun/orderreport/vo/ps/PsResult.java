package com.pinshang.qingyun.orderreport.vo.ps;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @Version 0.1.0
 * @Date 2021/1/29 14:10
 * @Copyright © 2019-2021 qm
 * @Description -- 类说明 配送结果
 **/
@Data
public class PsResult {
    @ApiModelProperty(position = 1, value = "结果为false，页面提示msg信息", example = "false", required = true)
    private Boolean success;

    @ApiModelProperty(position = 2, value = "页面提示msg信息", example = "您本次共成功添加16个商品，失败1个商品", required = true)
    private String msg;

}

package com.pinshang.qingyun.orderreport.mapper;

import com.pinshang.qingyun.base.mybatis.MyMapper;
import com.pinshang.qingyun.orderreport.model.OrderItemLatest;
import com.pinshang.qingyun.orderreport.vo.TJOrderMonitorVo;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2019/1/14 16:11.
 */
@Repository
public interface OrderItemLatestMapper extends MyMapper<OrderItemLatest> {

    int batchInsert(List<OrderItemLatest> orderList);

    int deleteOrderLatestItemByOrderTime(@Param("orderTime") Date orderTime);

    Long selectOrderItemLatestListCountByOrderCreateTime(TJOrderMonitorVo tjOrderMonitorVo);

    List<OrderItemLatest> selectOrderItemLatestListByOrderCreateTime(TJOrderMonitorVo tjOrderMonitorVo);

    List<OrderItemLatest> selectOrderItemLatestListByOrderIdList(@Param("orderIdList")List<Long> orderIdList);

    int deleteOrderLatestItemByOrderId(@Param("orderIdList") List<Long> orderIdList);

}

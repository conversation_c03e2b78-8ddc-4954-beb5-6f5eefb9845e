package com.pinshang.qingyun.orderreport.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 商品组配送单查询列表返回
 */
@Data
public class CommodityGroupLineOrderODTO {

    @ApiModelProperty(value = "线路ID",position = 1)
    private Long lineId;

    @ApiModelProperty(value = "线路编码",position = 1)
    private String lineCode;

    @ApiModelProperty(value = "线路名称",position = 1)
    private String lineName;

    @ApiModelProperty(value = "线路组ID",position = 2)
    private Long lineGroupId;

    @ApiModelProperty(value = "线路组名称",position = 2)
    private String lineGroupName;

    @ApiModelProperty(value = "商品组ID",position = 3)
    private Long commodityGroupId;

    @ApiModelProperty(value = "商品组名称",position = 3)
    private String commodityGroupName;

    @ApiModelProperty(value = "送货员",position = 4)
    private String deliverymanName;

    /**
     * 统计送货日期范围内下过订单且订单商品在商品组范围内的客户数量
     */
    @ApiModelProperty(value = "下单客户数量",position = 5)
    private Integer orderStoreQuantity;

    @ApiModelProperty(value = "是否可打印：0=不可打印，1=可打印",position = 6)
    private Integer isCanPrint;

    @ApiModelProperty(value = "线路状态:1-启用，0-停用",hidden = true)
    private Integer lineStatus;
    @ApiModelProperty(value = "商品组状态:1-启用，0-停用",hidden = true)
    private Integer commodityGroupStatus;
    @ApiModelProperty(value = "线路关联客户数",hidden = true)
    private Integer storeQuantity;
    @ApiModelProperty(value = "商品组关联商品数",hidden = true)
    private Integer commodityQuantity;

    public Integer getStoreQuantity() {
        return storeQuantity==null?0:storeQuantity;
    }

    public Integer getCommodityQuantity() {
        return commodityQuantity==null?0:commodityQuantity;
    }

    public Integer getOrderStoreQuantity() {
        return orderStoreQuantity==null?0:orderStoreQuantity;
    }

    public Integer getIsCanPrint() {
        if (lineStatus == 0 || commodityGroupStatus == 0 || this.getStoreQuantity() == 0 || this.getCommodityQuantity() == 0
                || getOrderStoreQuantity() == 0) {
            return 0;
        }
        return 1;
    }

}

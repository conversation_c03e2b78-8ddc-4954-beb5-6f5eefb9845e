package com.pinshang.qingyun.orderreport.mapper.entry;

import com.pinshang.qingyun.box.utils.SpringUtil;
import com.pinshang.qingyun.orderreport.dto.FreshProductShipmentsTempODTO;
import lombok.Data;

/**
 * <AUTHOR>
 * @Date 2019/3/11 14:16
 */
@Data
public class FreshStoreProductShipmentsEntry {
    private Long commodityId;
    private String commodityName;
    private String factoryName;
    private String commodityCode;
    private String workshopName;
    private String flowshopName;
    private String unit;
    private Long storeId;
    private Double commodityTotal;

    public static FreshStoreProductShipmentsEntry convert(FreshProductShipmentsTempODTO dto){
        FreshStoreProductShipmentsEntry entry = new FreshStoreProductShipmentsEntry();
        SpringUtil.copyProperties(dto,entry);
        return entry;
    }

}

package com.pinshang.qingyun.orderreport.mapper.entry;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 产品销售汇总
 */
@Data
public class ProductSaleStatisticsPurchaseTempEntry {

    //数量
    private BigDecimal commodityNum;

    //金额
    private BigDecimal totalPrice;

    //商品ID
    private Long commodityId;

    //送货日期
    private Date orderTime;

    //订货日期
    private Date createTime;

}

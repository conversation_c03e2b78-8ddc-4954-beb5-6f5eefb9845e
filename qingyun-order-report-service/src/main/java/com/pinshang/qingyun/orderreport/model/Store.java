package com.pinshang.qingyun.orderreport.model;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Id;
import javax.persistence.Table;

/**
 * <AUTHOR>
 * @date 2019/1/23 13:58.
 */
@Data
@Table(name = "t_store")
@NoArgsConstructor
@AllArgsConstructor
public class Store {

    @Id
    private Long id;

    private String storeCode;

    private String storeName;
    private String storeShortName;
    private Long storeTypeId;
    private String storeTypeName;
    private Integer storeStatus;
    private Integer downPriceStatus;
    private Long storeLineId;
    private String storeLineCode;
    private String storeLineName;
    private Long storeLineGroupId;
    private String storeLineGroupName;
    private Long deliveryTimeId;
    private String deliveryTime;
    private Long deliveryWarehouseId;
    private String deliveryWarehouseName;
    private String carportName;
    private Long deliverymanId;
    private String deliverymanName;
    private Long teamLeaderId;
    private String teamLeaderName;
    private Long salesmanId;
    private String salesmanName;
    private Long supervisorId;
    private String supervisorName;
    private Long officeDirectorId;
    private String officeDirectorName;
    private Long regionManagerId;
    private String regionManagerName;
    private Long storeSettId;
    private String storeSettName;
    private Long storeCompanyId;
    private String storeCompanyName;
    private Long printDeliveryBatch;
    private String printDeliveryBatchName;
    private Integer printDeliveryQueue;

    public Store(Long id, String storeName) {
        this.id = id;
        this.storeName = storeName;
    }
}

package com.pinshang.qingyun.orderreport.controller;

import com.alibaba.excel.EasyExcel;
import com.pinshang.qingyun.base.api.QYApplicationContext;
import com.pinshang.qingyun.base.api.TokenInfo;
import com.pinshang.qingyun.base.configure.expand.QYAssert;
import com.pinshang.qingyun.base.constant.RedisKeyPrefixConst;
import com.pinshang.qingyun.base.enums.DictionaryEnums;
import com.pinshang.qingyun.base.util.ExcelUtil;
import com.pinshang.qingyun.base.util.FastThreadLocalUtil;
import com.pinshang.qingyun.box.utils.SpringUtil;
import com.pinshang.qingyun.common.dto.DictionaryIDTO;
import com.pinshang.qingyun.common.dto.DictionaryODTO;
import com.pinshang.qingyun.common.service.DictionaryClient;
import com.pinshang.qingyun.infrastructure.exportfile.cache.starter.FileCacheQuery;
import com.pinshang.qingyun.infrastructure.exportfile.cache.starter.FileCacheQuery;
import com.pinshang.qingyun.infrastructure.exportfile.cache.starter.FileCacheQueryParameter;
import com.pinshang.qingyun.orderreport.mapper.entry.ProductShipmentsEntry;
import com.pinshang.qingyun.orderreport.service.PrintingAllowedCheckService;
import com.pinshang.qingyun.orderreport.service.ProductShipmentsService;
import com.pinshang.qingyun.orderreport.vo.ProductShipmentResponseVo;
import com.pinshang.qingyun.orderreport.vo.ProductShipmentsRequestVo;
import com.pinshang.qingyun.smm.dto.user.SelectUserFactoryIdListIDTO;
import com.pinshang.qingyun.smm.service.SMMUserClient;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.joda.time.LocalDate;
import org.redisson.api.RBucket;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.propertyeditors.CustomDateEditor;
import org.springframework.web.bind.WebDataBinder;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.SimpleDateFormat;
import java.util.*;

import static java.util.stream.Collectors.groupingBy;
import static java.util.stream.Collectors.toList;

/**
 * 产品发货总量
 * <AUTHOR>
 * @date 2019/3/4 15:57.
 */
@RestController
@RequestMapping("/statistical/productShipments")
@Slf4j
public class ProductShipmentsController {

    @Autowired
    private DictionaryClient dictionaryClient;

    @Resource
    private RedissonClient redissonClient;

//    @Autowired
//    private EsProductShipmentsClient esProductShipmentsClient;

    @Autowired
    private SMMUserClient userClient;

    @Autowired
    private PrintingAllowedCheckService printingAllowedCheckService;

    private final ProductShipmentsService productShipmentsService;

    public ProductShipmentsController(ProductShipmentsService productShipmentsService) {
        this.productShipmentsService = productShipmentsService;
    }

    /**
     * 查数据库搜索,按条件搜索
     * @param requestVo 请求参数封装
     * @return 返回JSON数据给前台
     */
    @RequestMapping(value = "/doSearch", method = RequestMethod.POST)
    public ProductShipmentResponseVo doSearch(@RequestBody ProductShipmentsRequestVo requestVo) {
        List<Long> factoryIds = new ArrayList<>();
        if(requestVo.getFactoryId() == null){
            factoryIds = userClient.selectUserFactoryIdList(SelectUserFactoryIdListIDTO.onlyDb(requestVo.getUserId()));
            if(SpringUtil.isEmpty(factoryIds)){
                return new ProductShipmentResponseVo();
            }
            requestVo.setFactoryIds(factoryIds);
        }
        return productShipmentsService.doSearch(requestVo);
    }


    /**
     * 打印
     * @param vo 当前搜索条件
     * @return 返回JSON
     */
    @RequestMapping(value = "print", method = RequestMethod.POST)
    public String print(@RequestBody ProductShipmentsRequestVo vo) {
        List<Long> factoryIds = new ArrayList<>();
        if(vo.getFactoryId() == null){
            factoryIds = userClient.selectUserFactoryIdList(SelectUserFactoryIdListIDTO.onlyDb(vo.getUserId()));
            if(SpringUtil.isEmpty(factoryIds)){
                QYAssert.isFalse("没有工厂权限！");
            }
            vo.setFactoryIds(factoryIds);
        }
        productShipmentsService.print(vo);
        return "ok";
    }

    @PostMapping("/doSearchV2")
    public List<ProductShipmentsEntry> doSearchV2(@RequestBody ProductShipmentsRequestVo vo) {
        List<Long> factoryIds = new ArrayList<>();
        if(vo.getFactoryId() == null){
            factoryIds = userClient.selectUserFactoryIdList(SelectUserFactoryIdListIDTO.onlyDb(vo.getUserId()));
            if(SpringUtil.isEmpty(factoryIds)){
                return new ArrayList<>();
            }
            vo.setFactoryIds(factoryIds);
        }
        return productShipmentsService.doSearchV2(vo);
    }


    /**
     * 查数据库搜索,按条件搜索
     * @param requestIDTO 请求参数封装
     * @return 返回JSON数据给前台
     */
    @ApiOperation(value = "产品发货总量搜索", notes = "产品发货总量搜索")
    @ApiImplicitParam(name = "requestIDTO", value = "", required = true, paramType = "body", dataType = "ProductShipmentsRequestVo")
    @RequestMapping(value = "/search",method = RequestMethod.POST)
    public ProductShipmentResponseVo doSearchProduct(@RequestBody ProductShipmentsRequestVo requestIDTO) {
        TokenInfo info = FastThreadLocalUtil.getQY();
        requestIDTO.setUserId(info.getUserId());
        //return productShipmentsClient.doSearch(requestIDTO);
        ProductShipmentResponseVo result = new ProductShipmentResponseVo();

        // 找到所有店铺类型
        DictionaryIDTO dictionaryIDTO = new DictionaryIDTO(DictionaryEnums.STORE_TYPE.getId());
        List<DictionaryODTO> storeTypes = dictionaryClient.getDictionaryList(dictionaryIDTO);;
        List<String> tableTitle = new ArrayList<>();
        tableTitle.add("生产组");
        tableTitle.add("商品编号");
        tableTitle.add("商品名称(规格)");
        tableTitle.add("条码");
        tableTitle.add("计量单位");
        storeTypes.forEach(d-> tableTitle.add(d.getOptionName()));
        tableTitle.add("合计");
        result.setTableTitle(tableTitle);
        //---- 表格第一行数据处理完毕.
        List<Long> factoryIds = new ArrayList<>();
        if(requestIDTO.getFactoryId() == null){
            factoryIds = userClient.selectUserFactoryIdList(SelectUserFactoryIdListIDTO.onlyDb(requestIDTO.getUserId()));
            if(SpringUtil.isEmpty(factoryIds)){
                result.setTableData(new ArrayList<>());
                return result;
            }
            requestIDTO.setFactoryIds(factoryIds);
        }
        List<ProductShipmentsEntry> dbResult = new ArrayList<>();
        RBucket<Boolean> bucket = redissonClient.getBucket(QYApplicationContext.applicationNameSwitch + RedisKeyPrefixConst.STATISTICS_ES_SWITCH);
        if (bucket.get()==null || !bucket.get()) {
            dbResult = productShipmentsService.doSearchV2(requestIDTO);
        }else{
            QYAssert.isTrue(Boolean.FALSE,"此功能已经废弃");
//            ProductShipmentsRequestIDTO productShipmentsRequestIDTO = BeanUtil.copyProperties(requestIDTO, ProductShipmentsRequestIDTO.class);
//            List<ProductShipmentsTempODTO> list = esProductShipmentsClient.queryList(productShipmentsRequestIDTO);
//            dbResult = BeanUtil.copyProperties(list, ProductShipmentsEntry.class);
        }
        Map<Long, List<ProductShipmentsEntry>> commodityStoreType = dbResult.stream().collect(groupingBy(ProductShipmentsEntry::getCommodityId));
        List<Long> commodityIds = dbResult.parallelStream().map(ProductShipmentsEntry::getCommodityId).distinct().collect(toList());
        List<List<String>> tableBody = new ArrayList<>();
        for (Long commodityId : commodityIds) {
            List<ProductShipmentsEntry> productShipmentsDbVos = commodityStoreType.get(commodityId);
            ProductShipmentsEntry productShipmentsDbVo = productShipmentsDbVos.get(0);
            List<String> tableRowData = new ArrayList<>();
            tableRowData.add(productShipmentsDbVo.getWorkshopName());
            tableRowData.add(productShipmentsDbVo.getCommodityCode());
            tableRowData.add(productShipmentsDbVo.getCommodityName());
            tableRowData.add(productShipmentsDbVo.getBarCode());
            tableRowData.add(productShipmentsDbVo.getUnit());
            Double count = 0.0;
            for (DictionaryODTO storeType : storeTypes) {
                Double quantity = 0.0;
                for (ProductShipmentsEntry shipmentsDbVo : productShipmentsDbVos) {
                    if (shipmentsDbVo.getStoreTypeId().equals(Long.valueOf(storeType.getId()))) {
                        quantity += shipmentsDbVo.getStoreTypeTotal();
                        break;
                    }
                }
                if (BigDecimal.valueOf(quantity).compareTo(BigDecimal.ZERO) == 0) {
                    tableRowData.add("");
                } else {
                    tableRowData.add(BigDecimal.valueOf(quantity).setScale(2, RoundingMode.HALF_UP).stripTrailingZeros().toPlainString());
                }
                count += quantity;
            }
            tableRowData.add(BigDecimal.valueOf(count).setScale(2, RoundingMode.HALF_UP).stripTrailingZeros().toPlainString());
            tableBody.add(tableRowData);
        }
        result.setTableData(tableBody);
        return result;
    }

    /**
     * 产品发货总量 导出excel
     * @param requestVo 请求参数VO
     * @param response 响应体
     */
    @ApiOperation(value = "产品发货总量 导出excel", notes = "产品发货总量 导出excel")
    @ApiImplicitParam(name = "requestVo", value = "", required = true, paramType = "body", dataType = "ProductShipmentsRequestVo")
    @RequestMapping(value = "/export", method = RequestMethod.GET)
    @FileCacheQuery(bizCode = "ORDER_REPORT_PRODUCT_SHIPMENTS")
    public void exportExcel(ProductShipmentsRequestVo requestVo, HttpServletResponse response) throws IOException {
        long qS = System.currentTimeMillis();
        requestVo.initExportPage();
        ProductShipmentResponseVo shipmentResponseVo = doSearchProduct(requestVo);
        long qE = System.currentTimeMillis();
        log.info("产品发货总量-导出--查询时间=" + ((qE - qS) / 1000));

        try {
            ExcelUtil.setFileNameAndHead(response, "产品发货总量" + LocalDate.now().toString("yyyyMMdd"));
            EasyExcel.write(response.getOutputStream()).head(createHead(shipmentResponseVo.getTableTitle())).autoCloseStream(Boolean.FALSE).sheet("产品发货总量")
                    .doWrite(shipmentResponseVo.getTableData());

        }catch (Exception e){
            log.error("产品发货总量导出错误", e);
            ExcelUtil.setExceptionResponse( response );
        }
        long eE = System.currentTimeMillis();
        log.info("产品发货总量-导出--excel处理时间="+ ( (eE -qE) /1000 )  );
    }

    /**
     * 检查是否可以导出
     * @param requestVo 请求参数VO
     */
    @ApiOperation(value = "检查是否可以导出")
    @ApiImplicitParam(name = "requestVo", required = true, paramType = "body", dataType = "ProductShipmentsRequestVo")
    @PostMapping(value = "/isExportAllowed")
    public void isExportAllowed(@RequestBody ProductShipmentsRequestVo requestVo){
        printingAllowedCheckService.isPrintingAllowed(requestVo);
    }

    private List<List<String>> createHead(List<String> list){
        List<List<String>> headList = new ArrayList<>(list.size());
        list.forEach(lt->headList.add(Arrays.asList(lt)));
        return headList;
    }

    @InitBinder
    public void initData(WebDataBinder wdb){
        wdb.registerCustomEditor(Date.class, new CustomDateEditor(new SimpleDateFormat("yyyy-MM-dd"), true));
    }
}

package com.pinshang.qingyun.orderreport.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2019/1/23 10:36.
 */
@Data
public class DeliveryListReqVo {

    private Long lineGroupId;
    private String lineGroupName;
    private Long deliveryManId;
    private String deliveryManName;
    private String deliveryTime;
    private Date orderDate;
    private Long userId;
    private Long teamLeaderId;
    private Integer deliveryBatch;
    /**
     *  0=送货清单
     *  1=提货单
     */
    private int type;

    /*** 补货数据 0-订单、 1-补货订单、  2-查所有0和1  （此字段没值也是查所有0和1 兼容清美组手低版本） */
    private Integer orderModeType;
}

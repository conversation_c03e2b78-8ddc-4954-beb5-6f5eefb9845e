package com.pinshang.qingyun.orderreport.vo.ps;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @Version 0.1.0
 * @Date 2021/1/28 11:14
 * @Copyright © 2019-2021 qm
 * @Description -- 类说明
 **/
@Data
public class QueryCommodityGroupSelectVO {
    @ApiModelProperty(value = "线路id，1值为-1时-可选商品组为所有启用的商品组，新增使用；2值为空时可选商品组为所有的商品组，查询使用，3-为线路id时，可选商品组要排除当前线路所有客户已经具备的商品组，线路修改商品组时使用", required = true)
    private Long lineId;
}

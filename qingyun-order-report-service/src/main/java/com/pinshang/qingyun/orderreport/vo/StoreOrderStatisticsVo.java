package com.pinshang.qingyun.orderreport.vo;

import com.github.pagehelper.PageInfo;
import com.pinshang.qingyun.base.page.Pagination;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 清美人客户订货统计查询
 */
@Data
@NoArgsConstructor
public class StoreOrderStatisticsVo extends Pagination {

    @ApiModelProperty("督导ID")
    private Long supervisorId;

    @ApiModelProperty("送货日期:yyyy-MM-dd")
    private String orderTime;

}

package com.pinshang.qingyun.orderreport.mapper;

import com.pinshang.qingyun.base.mybatis.MyMapper;
import com.pinshang.qingyun.orderreport.mapper.entry.FactoryDeliveryStatisticsByPageEntry;
import com.pinshang.qingyun.orderreport.mapper.entry.FactoryDeliveryStatisticsEntry;
import com.pinshang.qingyun.orderreport.model.FactoryDeliveryStatistics;
import com.pinshang.qingyun.orderreport.vo.FactoryDeliveryStatisticsReqVo;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;

@Repository
public interface FactoryDeliveryStatisticsMapper extends MyMapper<FactoryDeliveryStatistics> {

    List<FactoryDeliveryStatisticsEntry> queryFactoryDeliveryStatistics(@Param("orderTime") Date orderTime, @Param("mainTable")String mainTable, @Param("itemTable")String itemTable);

    int batchInsert(List<FactoryDeliveryStatistics> factoryDeliveryStatistics);

    List<FactoryDeliveryStatisticsByPageEntry> queryFactoryDeliveryMain(FactoryDeliveryStatisticsReqVo vo);

    List<Long> queryFactoryDeliveryIdList(@Param("commodityId")Long commodityId,@Param("orderTime")String orderTime);

}

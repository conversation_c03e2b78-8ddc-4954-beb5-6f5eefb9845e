package com.pinshang.qingyun.orderreport.mapper.entry;

import lombok.Data;

import java.math.BigDecimal;
import java.math.RoundingMode;

/**
 * <AUTHOR>
 * @Date 2019/3/6 14:11
 */
@Data
public class DeliveryOrderGiftEntry {
    private Long id;
    /** 订单id **/
    private Long orderId;
    /** 商品id **/
    private Long commodityId;
    /** 商品数量 **/
    private BigDecimal commodityNum;
    /** 单价 **/
    private BigDecimal commodityPrice;
    /**
     * 促销前的源始金额
     */
    private BigDecimal totalPrice;
    /** 类型 **/
    private Integer type;
    /** 备注 **/
    private String remark;
    /**
     * 商品名称
     */
    private String commodityName;
    /**
     * 商品编码
     */
    private String commodityCode;
    /**
     * 商品规格
     */
    private String commoditySpec;

    public BigDecimal getCommodityPrice() {
        return commodityPrice.setScale(2, RoundingMode.UP);
    }
}

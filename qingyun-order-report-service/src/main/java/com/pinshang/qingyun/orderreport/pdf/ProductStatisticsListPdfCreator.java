package com.pinshang.qingyun.orderreport.pdf;

import com.itextpdf.text.*;
import com.itextpdf.text.pdf.PdfPCell;
import com.itextpdf.text.pdf.PdfPTable;
import com.itextpdf.text.pdf.PdfWriter;
import com.pinshang.qingyun.orderreport.config.CustomerProperties;
import com.pinshang.qingyun.orderreport.mapper.entry.ProductStatisticsListEntry;
import com.pinshang.qingyun.orderreport.util.ListUtils;
import com.pinshang.qingyun.orderreport.util.PdfUtils;
import com.pinshang.qingyun.orderreport.vo.ProductStatisticsListVo;
import com.pinshang.qingyun.orderreport.vo.ReplenishmentStatisticsVo;
import lombok.extern.slf4j.Slf4j;
import org.joda.time.DateTime;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.UUID;

/**
 * 产品统计清单PDF
 */
@Component
@Slf4j
public class ProductStatisticsListPdfCreator {

    private final CustomerProperties customerProperties;

    private static final int pageSize = 54;

    @Autowired
    public ProductStatisticsListPdfCreator(CustomerProperties customerProperties) {
        this.customerProperties = customerProperties;
    }

    /**
     * 打印 PDF
     * @param dataList 要打印的数据
     * @return 是否打印成功.
     */
    public String create(List<ProductStatisticsListEntry> dataList, ProductStatisticsListVo vo) {
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String printTime = dateFormat.format(new Date());
        if (dataList == null) {
            log.error("要生成PDF的内容为空.");
            return null;
        }

        String pdfSave = customerProperties.getAbsoluteSavePath();
        String savePath = customerProperties.getSavePath();
        Date date = new DateTime().plusDays(1).toDate();
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");
        String dataDir = format.format(date);
        File file = new File(pdfSave + "/" + savePath + "/" + dataDir);
        if (!file.exists()) {
            boolean mkdirs = file.mkdirs();
            if (!mkdirs) {
                log.error("创建保存pdf的目录失败,请检查权限");
                return null;
            }
        }
        String fileName = UUID.randomUUID().toString().substring(0, 8) + ".pdf";
        Document document = new Document();
        try {
            FileOutputStream outputStream = new FileOutputStream(file.getAbsolutePath() + "/" + fileName);
            PdfWriter.getInstance(document, outputStream);
            document.open();
            Font font = PdfUtils.chineseFont(8);
            PdfPTable title = proceedTableHeader(font, vo);

            List<List<ProductStatisticsListEntry>> pageContent = ListUtils.splitList(dataList, pageSize);

            int page = 1;
            int totalPage = pageContent.size();
            for (List<ProductStatisticsListEntry> content : pageContent) {
                document.add(title);
                float[] cellWidth = {34,140,140,34,34,34};
                PdfPTable table = new PdfPTable(cellWidth);
                table.setWidthPercentage(99);
                table.addCell(new Phrase("客户编码", font));
                table.addCell(new Phrase("客户名称", font));
                table.addCell(new Phrase("商品名称", font));
                table.addCell(new Phrase("单价", font));
                table.addCell(new Phrase("数量", font));
                table.addCell(new Phrase("金额", font));
                for (ProductStatisticsListEntry entry : content) {
                    table.addCell(new Phrase(entry.getStoreCode(), font));
                    table.addCell(new Phrase(entry.getStoreName(), font));
                    table.addCell(new Phrase(entry.getCommodityName(), font));

                    BigDecimal commodityPrice = entry.getCommodityPrice();
                    table.addCell(new Phrase(BigDecimal.ZERO.compareTo(commodityPrice) == 0 ? "0" : commodityPrice.stripTrailingZeros().toPlainString(), font));

                    BigDecimal commodityNum = entry.getCommodityNum();
                    table.addCell(new Phrase(BigDecimal.ZERO.compareTo(commodityNum) == 0 ? "0" : commodityNum.stripTrailingZeros().toPlainString(), font));

                    BigDecimal amount = entry.getTotalPrice();
                    table.addCell(new Phrase(BigDecimal.ZERO.compareTo(amount) == 0 ? "0" : amount.stripTrailingZeros().toPlainString(), font));
                }
                PdfPCell tableBottom = new PdfPCell(new Phrase("第 " + page + " 页, 共 " + totalPage + "页.                打印时间: " + printTime, font));
                tableBottom.setColspan(6);
                tableBottom.setBorder(0);
                table.addCell(tableBottom);
                document.add(table);
                page++;
                if (page <= totalPage) {
                    document.newPage();
                }
            }
            document.close();
        } catch (DocumentException | IOException e) {
            log.error("生成补货数据汇总PDF时异常:{}",e.getMessage());
            return null;
        }
        return String.format("%s%s%s",file.getAbsolutePath(),"/",fileName);
//        return file.getAbsolutePath() + fileName;
//        return savePath + "/" + dataDir + "/" + fileName;
    }

    /**
     * 生成表格头部信息
     * @param font 中文字体
     * @param vo pdf查询参数
     */
    private PdfPTable proceedTableHeader(Font font, ProductStatisticsListVo vo) throws IOException, DocumentException {
        PdfPTable table = new PdfPTable(3);
        table.setWidthPercentage(99);
        PdfPCell cell = new PdfPCell(new Phrase("产品统计清单",PdfUtils.chineseFont(23)));
        cell.setColspan(3);
        cell.setBorder(0);
        cell.setFixedHeight(32);
        cell.setHorizontalAlignment(Element.ALIGN_CENTER);
        cell.setVerticalAlignment(Element.ALIGN_CENTER);
        table.addCell(cell);

        String settlementName = vo.getStoreSettName() == null ? "" : vo.getStoreSettName();
        cell = new PdfPCell(new Phrase("结账客户: " + settlementName, font));
        cell.setBorder(0);
        table.addCell(cell);

        String deliveryManName = vo.getDeliveryManName() == null ? "" : vo.getDeliveryManName();
        cell = new PdfPCell(new Phrase("送货员: " + deliveryManName, font));
        cell.setBorder(0);
        table.addCell(cell);

        Date orderDate = vo.getOrderTime();
        if (orderDate == null) {
            orderDate = new DateTime().plusDays(1).toDate();
        }
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
        String date = dateFormat.format(orderDate);
        cell = new PdfPCell(new Phrase("送货日期: " + date, font));
        cell.setBorder(0);
        table.addCell(cell);

        String warehouseName = vo.getDeliveryWarehouseName() == null ? "" : vo.getDeliveryWarehouseName();
        cell = new PdfPCell(new Phrase("发货仓库:" + warehouseName, font));
        cell.setBorder(0);
        table.addCell(cell);

        String lineGroupName = vo.getLineGroupName() == null ? "" : vo.getLineGroupName();
        cell = new PdfPCell(new Phrase("发货时间:" + lineGroupName, font));
        cell.setBorder(0);
        table.addCell(cell);

        cell = new PdfPCell(new Phrase("" ));
        cell.setBorder(0);
        table.addCell(cell);

        return table;
    }

}
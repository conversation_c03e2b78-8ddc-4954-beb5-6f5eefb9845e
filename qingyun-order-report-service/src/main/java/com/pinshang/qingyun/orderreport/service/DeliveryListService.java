package com.pinshang.qingyun.orderreport.service;

import com.pinshang.qingyun.base.enums.PrinterDataGroupTypeEnum;
import com.pinshang.qingyun.box.utils.SpringUtil;
import com.pinshang.qingyun.common.dto.DictionaryODTO;
import com.pinshang.qingyun.common.service.DictionaryClient;
import com.pinshang.qingyun.orderreport.mapper.DeliveryListMapper;
import com.pinshang.qingyun.orderreport.mapper.StoreMapper;
import com.pinshang.qingyun.orderreport.model.Store;
import com.pinshang.qingyun.orderreport.pdf.DeliveryListPdfCreator;
import com.pinshang.qingyun.orderreport.util.NumberUtils;
import com.pinshang.qingyun.orderreport.vo.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.joda.time.DateTime;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.entity.Example;

import java.math.RoundingMode;
import java.text.DateFormat;
import java.text.NumberFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2019/1/23 10:33.
 */
@Service
@Slf4j
public class DeliveryListService {
    @Autowired
    private PrintStatusService printStatusService;
    private final DeliveryListMapper deliveryListMapper;
    private final DeliveryListPdfCreator pdfCreator;
    private final StoreMapper storeMapper;
    private final PrintTaskService printTaskService;
    @Autowired
    private CommonPdfPrintService commonPdfPrintService;
    @Autowired
    private DictionaryClient dictionaryClient;
    public DeliveryListService(DeliveryListMapper deliveryListMapper, DeliveryListPdfCreator pdfCreator, StoreMapper storeMapper, PrintTaskService printTaskService) {
        this.deliveryListMapper = deliveryListMapper;
        this.pdfCreator = pdfCreator;
        this.storeMapper = storeMapper;
        this.printTaskService = printTaskService;
    }

    /**
     * 送货清单/提货单 搜索
     * @param params 参数
     * @return 线组纬度的数据.
     */
    public List<DeliveryListRespVo> searchDeliveryList(DeliveryListReqVo params) {

        // 是否查询latest表的数据, 取决于 参数中的orderDate, 如果是今天或者今天往后的日期,就查询lastest表
        boolean latestFlag = true;
        DateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
        if (params.getOrderDate() != null) {
            // 用户选的送货日期是否是今天(凌晨)及今天(凌晨)往后的日期
            latestFlag = !new DateTime(params.getOrderDate()).isBefore(new DateTime().withMillisOfDay(0));
        } else {
            try {
                params.setOrderDate(dateFormat.parse( dateFormat.format(new DateTime().plusDays(1).toDate()) ));
            } catch (ParseException e) {
                log.error("送货日期转换出错",e);
            }
        }
        return latestFlag?deliveryListMapper.searchLatestList(params):deliveryListMapper.searchList(params);
    }

    /**
     * 预览某个线路的送货清单
     * @param param  参数
     * @return 预览数据
     */
    public DeliveryListPreviewRespVo preview(DeliveryListPreviewReqVo param) {
        // 是否查询latest表的数据, 取决于 参数中的orderDate, 如果是今天或者今天往后的日期,就查询lastest表
        NumberFormat instance = NumberFormat.getNumberInstance();
        instance.setRoundingMode(RoundingMode.HALF_UP);
        instance.setMaximumFractionDigits(2);
        instance.setGroupingUsed(false);
        if ( param.getLineId()==null ) {
            return null;
        }
        if (param.getOrderDate() == null) {
            param.setOrderDate(new DateTime().plusDays(1).toDate());
        }
        String orderDate = new SimpleDateFormat("yyyy-MM-dd").format(param.getOrderDate());
        {
            // 用户选的送货日期是否是今天(凌晨)及今天(凌晨)往后的日期
            boolean latestFlag = !new DateTime(param.getOrderDate()).isBefore(new DateTime().withMillisOfDay(0));

            // 预览打印的头数据处理.线路编码,线路名称,线路组,发货仓库等
            DeliveryListPreviewRespVo result = deliveryListMapper.searchPreviewTitle(param.getLineId());
            result.setOrderDate(param.getOrderDate());
            result.setOrderModeTypeName(param.getOrderModeTypeName());
            // 在指定日期内下单的客户,并且在指定的线路上的客户
            List<StoreVo> stores = deliveryListMapper.queryStoreNameByLineId(param.getLineId(), orderDate, param.getDeliveryBatch(), latestFlag,param.getOrderModeType());
            if (stores == null || stores.size() == 0) {
                return result;
            }
            for (StoreVo store : stores) {
                if(null != store.getDeliveryBatch()){
                    result.setDeliveryBatch(store.getDeliveryBatch());
                    break;
                }
            }
            // 打印批次
            Long printDeliveryBatchId = param.getPrintDeliveryBatchId();
            if (printDeliveryBatchId != null) {
                // 检验打印批次是否合法
                List<DictionaryVo> printBatchs = queryPrintDelivery(Collections.singletonList(printDeliveryBatchId));
                if (SpringUtil.isEmpty(printBatchs)) {
                    log.error("非法的打印批次,请检查,批次ID:{}", printDeliveryBatchId);
                    return result;
                }
                // 给结果集设置打印批次
                setPrintDeliveryBatchs(result, stores);
                // 从查询出来的客户中取出给定打印批次的客户
                stores = fetchPrintDeliveryBatchStore(stores, printDeliveryBatchId);
                result.setDefaultPrintDeliveryBatchId(printDeliveryBatchId);
                result.setDefaultPrintDeliveryBatchIdStr(printDeliveryBatchId.toString());
                result.setDefaultPrintDeliveryBatch(printBatchs.get(0).getName());
            } else {
                // 有多个打印批次,要设置打印批次,并且取第一个出来做默认显示
                stores = processMultiPrintDeliveryBatch(result, stores);
            }
            // 所有的客户名称
            List<String> storeNames = new ArrayList<>();
            storeNames.add("商品名称");
            storeNames.addAll(stores.parallelStream().map(StoreVo::getStoreName).collect(Collectors.toList()));
            storeNames.add("合计");
            // 上述客户买的所有的商品,如果有重复只取一条
            List<OrderListVo> goodsList  = deliveryListMapper.queryStoreBuyGoodsByOrderDateAndLineGroupId(param.getLineId(), orderDate, result.getDefaultPrintDeliveryBatchId() + "", param.getDeliveryBatch(), latestFlag,param.getOrderModeType());
            // 提取出商品列表 goodsId#goodsName(goodsSpec)
            List<String> goodsInfo = new ArrayList<>();
            goodsList.forEach(orderListVo -> goodsInfo.add(orderListVo.getGoodsId() + "^!" + orderListVo.getGoodsName() + "(" + orderListVo.getSpecification() + ")"));

            // 上述每个客户购买的商品及商品数量,两层map,外层map的key为客户id,value为一个map(即内层map)
            // 内层map key为商品的id,value为购买的数量
            Map<Long, Map<String, Double>> storeBuyGoodsInfo = new HashMap<>();
            boolean finalLatestFlag = latestFlag;
            stores.forEach(s -> {
                Map<String, Double> map = new HashMap<>();
                List<StoreBuyGoods> buys  = deliveryListMapper.findGoodsByStoreId(s.getId(), orderDate, param.getDeliveryBatch(), finalLatestFlag,param.getOrderModeType());
                // 处理客户购买的商品数目,有可能一种商品,出现两条,一条是正常购买的,一条是配货配出来的,还有可能再出现一条是赠品的
                // 合并这些商品数目
                List<StoreBuyGoods> mergeResult = processStoreBuysNumber(buys);
                mergeResult.forEach(b -> map.put(b.getGoodsId() + "", b.getGoodsNumber()));
                storeBuyGoodsInfo.put(s.getId(), map);
            });
            // 每个客户,购买了什么商品,以及购买了多少
            List<List<String>> everyGoodsBuyNumber = new ArrayList<>();
            List<StoreVo> finalStores = stores;
            goodsInfo.forEach(g -> {
                String[] split = g.split("\\^!");
                String goodsId = split[0];
                List<String> list = new ArrayList<>();
                list.add(split[1]);
                finalStores.forEach(s -> {
                    Long storeId = s.getId();
                    Map<String, Double> goodsMapper = storeBuyGoodsInfo.get(storeId);
                    Double buyNumber = goodsMapper.get(goodsId);
                    if (buyNumber == null) {
                        buyNumber = 0.0;
                    }
                    String e = NumberUtils.subZeroAndDot(instance.format(buyNumber));
                    list.add("0".equals(e) ? "" : e);
                });
                everyGoodsBuyNumber.add(list);
            });
            result.setStoreNames(storeNames);
            // 处理每一行数据,统计一下这个商品总共购买了多少
            processGoodsCount(everyGoodsBuyNumber);
            result.setGoodsInfo(everyGoodsBuyNumber);
            if(SpringUtil.isNotEmpty(stores)) {
                result.setOrderId(stores.get(0).getOrderId());
            }
            return result;
        }
    }

    /**
     * 每一行合计一次总数
     *
     * @param everyGoodsBuyNumber 表格的数据列表
     */
    private void processGoodsCount(List<List<String>> everyGoodsBuyNumber) {
        NumberFormat numberInstance = NumberFormat.getNumberInstance();
        numberInstance.setMaximumFractionDigits(2);
        numberInstance.setRoundingMode(RoundingMode.HALF_UP);
        numberInstance.setGroupingUsed(false);
        everyGoodsBuyNumber.forEach(list -> {
            Double total = 0.0;
            for (int i = 1; i < list.size(); i++) {
                String sNumber = list.get(i);
                if (StringUtils.isBlank(sNumber) || "null".equals(sNumber)) {
                    sNumber = "0.0";
                }
                Double number = Double.valueOf(sNumber);
                total += number;
            }
            list.add(NumberUtils.subZeroAndDot(numberInstance.format(total)));
        });
    }

    /**
     * 合并用户购买的商品数量 一个商品可能有多条记录,原因在于 用户购买的商品中一种,还有一种是配货配出来的,还有另外一种是赠品
     * 所以,要把这些商品的数目,给合并掉
     *
     * @param buys
     * @return
     */
    private List<StoreBuyGoods> processStoreBuysNumber(List<StoreBuyGoods> buys) {
        // 合并商品数目
        List<StoreBuyGoods> mergeResult = new ArrayList<>();
        Map<Long, Double> map = new HashMap<>();
        buys.forEach(g -> {
            Long goodsId = g.getGoodsId();
            map.merge(goodsId, g.getGoodsNumber(), (a, b) -> b + a);
        });
        Set<Long> longs = map.keySet();
        longs.forEach(id -> {
            StoreBuyGoods storeBuyGoods = new StoreBuyGoods();
            storeBuyGoods.setGoodsId(id);
            storeBuyGoods.setGoodsNumber(map.get(id));
            mergeResult.add(storeBuyGoods);
        });
        return mergeResult;
    }


    /**
     * 没有传打印批次,从已有的批次中选一个 取出所有的打印批次,赋值给result, 并且取默认显示第一个指印批次的客户
     * @param result 要返回给前台的结果
     * @param stores 一批客户,有多个打印批次
     * @return 从多个打印批次的客户中,取其中一个打印批次里的一批客户,返回给前台显示,并且设置多个打印批次给前台
     */
    private List<StoreVo> processMultiPrintDeliveryBatch(DeliveryListPreviewRespVo result, List<StoreVo> stores) {
        Set<Long> filterSet = new TreeSet<>();
        stores.forEach(s -> {
            if (s.getPrintDeliveryBatchId() == null) {
                // 客户没有打印批次,默认打印批次1
                filterSet.add(9131078242860601294L);
            } else {
                filterSet.add(s.getPrintDeliveryBatchId());
            }
        });
        // 这一批客户,是同一打印批次的
        List<Long> printBatchIds = new ArrayList<>(filterSet);
        if (printBatchIds.size() == 1) {
            List<DictionaryVo> dictionaryVos = queryPrintDelivery(printBatchIds);
            result.setPrintDeliveryBatch(dictionaryVos);
            result.setDefaultPrintDeliveryBatch(dictionaryVos.get(0).getName());
            result.setDefaultPrintDeliveryBatchId(dictionaryVos.get(0).getId());
            result.setDefaultPrintDeliveryBatchIdStr(dictionaryVos.get(0).getId().toString());
            return stores;
        } else {
            // 不只一个打印批次
            List<DictionaryVo> printBatchs = queryPrintDelivery(printBatchIds);
            result.setDefaultPrintDeliveryBatch(printBatchs.get(0).getName());
            result.setDefaultPrintDeliveryBatchId(printBatchs.get(0).getId());
            result.setDefaultPrintDeliveryBatchIdStr(printBatchs.get(0).getId().toString());
            result.setPrintDeliveryBatch(printBatchs);
            // 多个打印批次,默认返回第一个打印批次的客户
            Long defaultPrintBatchID = printBatchs.get(0).getId();
            return fetchPrintDeliveryBatchStore(stores, defaultPrintBatchID);
        }
    }

    /**
     * 从给定的打印批次中,过滤出给定打印批次的客户
     * @param stores 客户列表
     * @param printDeliveryBatch 打印批次
     * @return
     */
    private List<StoreVo> fetchPrintDeliveryBatchStore(List<StoreVo> stores, Long printDeliveryBatch) {
        List<StoreVo> list = new ArrayList<>();
        stores.forEach(s -> {
            if (Objects.equals(printDeliveryBatch, s.getPrintDeliveryBatchId())) {
                list.add(s);
            }
        });
        return list;
    }

    /**
     * 给结果集设置打印批次
     * @param result  结果集
     * @param stores 客户
     */
    private void setPrintDeliveryBatchs(DeliveryListPreviewRespVo result, List<StoreVo> stores) {
        List<Long> printDeliveryIds = stores.parallelStream().map(StoreVo::getPrintDeliveryBatchId).distinct().collect(Collectors.toList());
        List<DictionaryVo> printBatchList = queryPrintDelivery(printDeliveryIds);
        result.setPrintDeliveryBatch(printBatchList);
    }

    /**
     * 批量查询打印批次
     * @param printDeliveryIds 打印批次ID
     * @return 打印批次列表
     */
    private List<DictionaryVo> queryPrintDelivery(List<Long> printDeliveryIds) {
        List<DictionaryVo> result = new ArrayList<>();
        Example ex = new Example(Store.class);
        ex.setDistinct(true);
        ex.selectProperties("printDeliveryBatch", "printDeliveryBatchName");
        ex.createCriteria().andIn("printDeliveryBatch", printDeliveryIds);
        List<Store> stores = storeMapper.selectByExample(ex);
        if (SpringUtil.isEmpty(stores)) {
            return result;
        }
        stores.forEach(item -> {
            DictionaryVo vo = new DictionaryVo();
            vo.setId(item.getPrintDeliveryBatch());
            vo.setName(item.getPrintDeliveryBatchName());
            result.add(vo);
        });

        List<DictionaryODTO> dictionaryList = dictionaryClient.findListByIds(printDeliveryIds);
        if(SpringUtil.isEmpty(dictionaryList)){
            return result;
        }
        Map<Long,DictionaryODTO> dictionaryMap = dictionaryList.stream().collect(Collectors.toMap(k -> Long.valueOf(k.getId()), Function.identity()));
        result.stream().forEach(r -> {
            DictionaryODTO dictionaryODTO = dictionaryMap.get(r.getId());
            if(dictionaryODTO != null){
                r.setName(dictionaryODTO.getOptionName());
                r.setSort(dictionaryODTO.getSort());
            }
        });
        return result.stream().collect(Collectors.toMap(DictionaryVo::getId, Function.identity(), (v1, v2) -> v2)).values().stream().sorted(
                Comparator.comparing(DictionaryVo::getSort,Comparator.nullsFirst(Integer::compareTo))).collect(Collectors.toList());
    }

    private List<String> print(DeliveryListPreviewReqVo param) {
        List<String> savePathList = new ArrayList<>();
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
        Long lineId = param.getLineId();
        Date orderDate = param.getOrderDate();
        String formatDate = dateFormat.format(orderDate);
        Integer deliveryBatch = param.getDeliveryBatch();
        boolean latestFlag = new DateTime(param.getOrderDate()).isAfter(new DateTime().withMillisOfDay(0));
        List<StoreVo> stores = deliveryListMapper.queryStoreNameByLineId(lineId, formatDate, deliveryBatch, latestFlag,param.getOrderModeType());
        //多个打印批次处理
        List<Long> printBatchIds = param.getPrintDeliveryBatchId()!=null?Arrays.asList(param.getPrintDeliveryBatchId()):stores.parallelStream().map(StoreVo::getPrintDeliveryBatchId).distinct().collect(Collectors.toList());
        for (Long printBatchId : printBatchIds) {
            param.setPrintDeliveryBatchId(printBatchId);
            DeliveryListPreviewRespVo previewData = preview(param);
            if (previewData == null || previewData.getGoodsInfo() == null || previewData.getGoodsInfo().size() == 0) {
                continue;
            }
            savePathList.add(pdfCreator.create(previewData));
        }
//        try {
            commonPdfPrintService.uploadAndPrintPdfList(savePathList,"order_report_TJ_DELIVERY_LIST",PrinterDataGroupTypeEnum.STATISTICAL_QUERY_DELIVERY,param.getUserId());
//        } catch (Exception e) {
//            log.error("print error:", e);
//        }
        return savePathList;
    }
    @Async
    public void asyncPrint(List<DeliveryListPreviewReqVo> params, Long id) {
        printAll(params,id);
    }


    @Transactional(rollbackFor = Exception.class)
    public String printAll(List<DeliveryListPreviewReqVo> params, Long id) {
        for (DeliveryListPreviewReqVo previewVo : params) {
            previewVo.setUserId(id);
            print(previewVo);
        }
        printStatusService.saveOrUpdate(params);
        return "ok";
    }

    public Map<String,String> getStrListPdf(List<DeliveryListPreviewReqVo> params, Long id){
        Map<String,String> map = new HashMap<>();
        for (DeliveryListPreviewReqVo previewVo : params) {
            previewVo.setUserId(id);
            Map<String,String> strPdf = getStrPdf(previewVo);
            if (SpringUtil.isNotEmpty(strPdf)) {
                map.putAll(strPdf);
            }
        }
        return map;
    }
    public Map<String,String> getStrPdf(DeliveryListPreviewReqVo param){
        Map<String,String> map = new HashMap<>();
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
        Long lineId = param.getLineId();
        Date orderDate = param.getOrderDate();
        String formatDate = dateFormat.format(orderDate);
        Integer deliveryBatch = param.getDeliveryBatch();
        boolean latestFlag = new DateTime(param.getOrderDate()).isAfter(new DateTime().withMillisOfDay(0));
        List<StoreVo> stores = deliveryListMapper.queryStoreNameByLineId(lineId, formatDate, deliveryBatch, latestFlag,param.getOrderModeType());
        //多个打印批次处理
        DeliveryListPreviewRespVo deliveryListPreviewRespVo = deliveryListMapper.searchPreviewTitle(lineId);
        String deliveryManName = deliveryListPreviewRespVo.getDeliveryManName();
        List<Long> printBatchIds = param.getPrintDeliveryBatchId()!=null?Arrays.asList(param.getPrintDeliveryBatchId()):stores.parallelStream().map(StoreVo::getPrintDeliveryBatchId).distinct().collect(Collectors.toList());
        SimpleDateFormat dateFormatHHMM = new SimpleDateFormat("yyyyMMddHHmm");
        String dataDir = dateFormatHHMM.format(new Date());
        for (Long printBatchId : printBatchIds) {
            param.setPrintDeliveryBatchId(printBatchId);
            DeliveryListPreviewRespVo previewData = preview(param);
            if (previewData == null || previewData.getGoodsInfo() == null || previewData.getGoodsInfo().size() == 0) {
                continue;
            }
            map.put(pdfCreator.create(previewData),"送货清单"+"_"+deliveryManName+"_"+dataDir);
        }
        return map;
    }
}

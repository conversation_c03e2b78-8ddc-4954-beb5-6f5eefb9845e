package com.pinshang.qingyun.orderreport.vo.ps;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @Version 0.1.0
 * @Date 2021/1/25 13:35
 * @Copyright © 2019-2021 qm
 * @Description -- 类说明
 **/
@Data
public class AddCommodityGroupLineVO {

    @ApiModelProperty(value = "线路组id", required = true)
    private Long lineGroupId;

    @ApiModelProperty(value = "线路组id", required = true)
    private String lineGroupName;

    @ApiModelProperty(value = "线路编码", required = true, hidden = true)
    private String lineCode;

    @ApiModelProperty(value = "线路名称", required = true)
    private String lineName;

    @ApiModelProperty(value = "商品组id", required = true)
    private Long commodityGroupId;

    @ApiModelProperty(value = "发货仓库id", required = true)
    private Long deliveryWarehouseId;

    @ApiModelProperty(value = "发货仓库名称", required = true)
    private String deliveryWarehouseName;

    @ApiModelProperty(value = "送货员id", required = true)
    private Long deliverymanId;

    @ApiModelProperty(value = "车位", required = true)
    private String carportName;

    @ApiModelProperty(value = "车牌号", required = true)
    private String licensePlate;

    @ApiModelProperty(value = "商品组线路状态:1-启用状态，0-停用状态", required = true, hidden = true)
    private Boolean status;

    @ApiModelProperty(value = "创建人", required = true, hidden = true)
    private Long createId;

    @ApiModelProperty(value = "创建时间", required = true, hidden = true)
    private Date createTime;

    @ApiModelProperty(value = "更新人", required = true, hidden = true)
    private Long updateId;

    @ApiModelProperty(value = "更新时间", required = true, hidden = true)
    private Date updateTime;
}

package com.pinshang.qingyun.orderreport.mapper;

import com.pinshang.qingyun.base.mybatis.MyMapper;
import com.pinshang.qingyun.orderreport.mapper.entry.CommoditySaleStatisticsAndItemLatestEntry;
import com.pinshang.qingyun.orderreport.mapper.entry.CommoditySaleStatisticsMonitorEntry;
import com.pinshang.qingyun.orderreport.model.CommoditySaleStatistics;
import com.pinshang.qingyun.orderreport.vo.CommoditySaleStatisticsMakeUpReqVo;
import com.pinshang.qingyun.orderreport.vo.CommoditySaleStatisticsReqVo;
import com.pinshang.qingyun.orderreport.vo.CommoditySaleStatisticsRespVo;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface CommoditySaleStatisticsMapper extends MyMapper<CommoditySaleStatistics> {

    int batchInsert(List<CommoditySaleStatistics> commoditySaleStatisticss);

    List<CommoditySaleStatistics> queryCommoditySaleStatisticsByMonthData(@Param("startTime") String startTime,@Param("endTime") String endTime,@Param("list") List<Long> commodityIds);

    List<CommoditySaleStatisticsAndItemLatestEntry> queryCommoditySaleStatisticsAndItemLatestData(@Param("orderTime") String orderTime);

    List<CommoditySaleStatisticsRespVo> queryCommoditySaleData(@Param("vo") CommoditySaleStatisticsReqVo vo);

    //查询订单商品汇总数据
    CommoditySaleStatisticsMonitorEntry queryOrderCommodityStatistics(@Param("vo") CommoditySaleStatisticsMakeUpReqVo vo);

    //销售汇总、督导汇总
    CommoditySaleStatisticsMonitorEntry queryCommodityStatisticsBase(@Param("vo") CommoditySaleStatisticsMakeUpReqVo vo,@Param("tableName")String tableName);

    //工厂汇总
    CommoditySaleStatisticsMonitorEntry queryCommodityStatisticsFactory(@Param("vo") CommoditySaleStatisticsMakeUpReqVo vo);


}

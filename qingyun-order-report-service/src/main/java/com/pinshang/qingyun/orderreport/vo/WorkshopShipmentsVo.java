package com.pinshang.qingyun.orderreport.vo;

import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * 生产组(或外地)发货单 预览 返回实体类
 * <AUTHOR>
 * @date 2019/2/28 17:50.
 */
@Data
public class WorkshopShipmentsVo {

    private String workshopName;
    private String lineGroupName;
    private String deliveryHouse;
    private Date startOrderDate;
    private Date endOrderDate;
    //发货时间
    private String deliveryTime;

    //客户类型
    private String storeType;

    //送货批次
    private String deliveryBatch;

    //补货数据选项
    private String orderModeTypeName;

    private List<String> tableHeader;
    private List<List<String>> tableDate;
    private List<String> tableBottom;
}

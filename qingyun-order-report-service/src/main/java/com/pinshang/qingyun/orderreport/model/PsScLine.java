package com.pinshang.qingyun.orderreport.model;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;

import javax.persistence.Id;
import javax.persistence.Table;

/**
 * t_ps_sc_line
 * <AUTHOR>
@Data
@Table(name = "t_ps_sc_line")
public class PsScLine implements Serializable {
    /**
     * 主键
     */
    @Id
    private Long id;

    /**
     * 线路组id
     */
    private Long lineGroupId;

    /**
     * 线路组名称
     */
    private String lineGroupName;

    /**
     * 线路编码
     */
    private String lineCode;

    /**
     * 线路名称
     */
    private String lineName;

    /**
     * 商品组id
     */
    private Long commodityGroupId;

    /**
     * 发货仓库id
     */
    private Long deliveryWarehouseId;

    /**
     * 发货仓库名称
     */
    private String deliveryWarehouseName;

    /**
     * 送货员id
     */
    private Long deliverymanId;

    /**
     * 车位
     */
    private String carportName;

    /**
     * 车牌号
     */
    private String licensePlate;

    /**
     * 客户数量
     */
    private Long storeQuantity;

    /**
     * 商品组线路状态:1-启用状态，0-停用状态
     */
    private Boolean status;

    /**
     * 创建人
     */
    private Long createId;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新人
     */
    private Long updateId;

    /**
     * 更新时间
     */
    private Date updateTime;

    private static final long serialVersionUID = 1L;
}
package com.pinshang.qingyun.orderreport.util;

import com.pinshang.qingyun.base.enums.DictionaryEnums;
import com.pinshang.qingyun.box.utils.SpringUtil;
import com.pinshang.qingyun.common.dto.DictionaryODTO;
import com.pinshang.qingyun.common.service.DictionaryClient;
import com.pinshang.qingyun.orderreport.mapper.StoreMapper;
import com.pinshang.qingyun.orderreport.vo.ProductShipmentsRequestVo;
import com.pinshang.qingyun.orderreport.vo.WorkshopShipmentsVo;
import com.pinshang.qingyun.renderer.enums.FieldTypeEnum;
import com.pinshang.qingyun.renderer.service.IRenderService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2019/2/28 18:27.
 */
@Component
public class WorkshopShipmentsHelper {

    private final StoreMapper storeMapper;
    @Autowired
    private DictionaryClient dictionaryClient;

    @Autowired
    private IRenderService renderService;

    public WorkshopShipmentsHelper(StoreMapper storeMapper) {
        this.storeMapper = storeMapper;
    }

    /**
     * 处理返回数据的标题 生产组  线路组 发货仓库 送货日期
     * @param requestVo
     * @param result
     */
    public void processResponseTitle(ProductShipmentsRequestVo requestVo, WorkshopShipmentsVo result) {
        if (requestVo.getLineGroupId() == null) {
            result.setLineGroupName("全部");
        } else {
            String lineGroupName = storeMapper.selectLineGroupNameById(requestVo.getLineGroupId());
            if (StringUtils.isNotEmpty(lineGroupName)) {
                result.setLineGroupName(lineGroupName);
            }
        }
        if (requestVo.getWarehouseId() == null) {
            result.setDeliveryHouse("全部");
        } else {
            String warehouseName = storeMapper.selectWarehouseNameById(requestVo.getWarehouseId());
            if (StringUtils.isNotEmpty(warehouseName)) {
                result.setDeliveryHouse(warehouseName);
            }
        }

        //门店类型
        if(requestVo.getStoreTypeId() == null){
            result.setStoreType("全部");
        }else {
            DictionaryODTO dictionaryODTO = dictionaryClient.findDictionaryDetailsById(requestVo.getStoreTypeId());
            result.setStoreType(dictionaryODTO.getOptionName());
        }

        //配送批次
        if(requestVo.getDeliveryBatch() == null){
            result.setDeliveryBatch("全部");
        }else {
            String deliveryBatch = "无配送批次";

            List<com.pinshang.qingyun.renderer.client.dto.DictionaryODTO> dictionaryODTOS = renderService
                    .getDataById(Arrays.asList(DictionaryEnums.DELIVERY_BATCH.getId()), FieldTypeEnum.DICTIONARY_BY_DICTIONARY_ID, com.pinshang.qingyun.renderer.client.dto.DictionaryODTO.class);
            if (SpringUtil.isNotEmpty(dictionaryODTOS)) {
                for (com.pinshang.qingyun.renderer.client.dto.DictionaryODTO dictionaryODTO : dictionaryODTOS) {
                    if (dictionaryODTO.getOptionCode().equals(requestVo.getDeliveryBatch().toString())) {
                        deliveryBatch = dictionaryODTO.getOptionName();
                        break;
                    }
                }
            }

            result.setDeliveryBatch(deliveryBatch);
        }

        //发货时间
        result.setDeliveryTime(requestVo.getDeliveryTime() == null?"全部":requestVo.getDeliveryTime());


    }
}

package com.pinshang.qingyun.orderreport.controller;

import com.pinshang.qingyun.infrastructure.exportfile.cache.starter.FileCacheQuery;
import com.pinshang.qingyun.infrastructure.exportfile.cache.starter.FileCacheQueryParameter;
import com.pinshang.qingyun.orderreport.dto.OperatorOrderReqIDTO;
import com.pinshang.qingyun.orderreport.service.OperatorOrderStatisticalService;
import com.pinshang.qingyun.orderreport.vo.OperatorOrderMonitorVo;
import com.pinshang.qingyun.orderreport.vo.OperatorOrderReqVo;
import com.pinshang.qingyun.orderreport.vo.OperatorOrderRespVo;
import io.swagger.annotations.ApiModelProperty;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;

/**
 * 操作员订单统计
 * <AUTHOR>
 * @date 2019/3/12 17:13.
 */
@RestController
@RequestMapping(value = "/statistical/operatorOrder")
public class OperatorOrderStatisticalController {

    private final OperatorOrderStatisticalService operatorOrderStatisticalService;

    public OperatorOrderStatisticalController(OperatorOrderStatisticalService operatorOrderStatisticalService) {
        this.operatorOrderStatisticalService = operatorOrderStatisticalService;
    }

    @PostMapping("/list")
    @ApiModelProperty(value = "操作员订单统计", notes = "操作员订单统计")
    public List<OperatorOrderRespVo> queryList(@RequestBody OperatorOrderReqVo reqVo) {
        return operatorOrderStatisticalService.queryList(reqVo);
    }

    @PostMapping("/print")
    public String print(@RequestBody OperatorOrderReqVo reqVo) {
        operatorOrderStatisticalService.print(reqVo);
        return "ok";
    }

    @PostMapping("/resetOperatorOrderStatistics")
    public Integer resetOperatorOrderStatistics(@RequestBody OperatorOrderMonitorVo vo) {
        return operatorOrderStatisticalService.resetOperatorOrderStatistics(vo);
    }

    @GetMapping("/export")
    @ApiModelProperty(value = "操作员订单统计-导出", notes = "操作员订单统计-导出")
    @FileCacheQuery(bizCode = "OR_OPERATOR_ORDER_E")
    public void export(@FileCacheQueryParameter OperatorOrderReqIDTO operatorOrderReqIDTO, HttpServletResponse httpServletResponse) throws IOException {
        operatorOrderStatisticalService.export(operatorOrderReqIDTO, httpServletResponse);
    }

}

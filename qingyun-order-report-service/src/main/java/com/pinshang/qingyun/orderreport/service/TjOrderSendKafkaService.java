package com.pinshang.qingyun.orderreport.service;

import cn.jiguang.common.utils.StringUtils;
import com.alibaba.fastjson.JSON;
import com.pinshang.qingyun.base.api.QYApplicationContext;
import com.pinshang.qingyun.box.utils.*;
import com.pinshang.qingyun.kafka.base.KafkaMessageWrapper;
import com.pinshang.qingyun.kafka.base.KafkaTopicConstant;
import com.pinshang.qingyun.kafka.enums.KafkaMessageOperationTypeEnum;
import com.pinshang.qingyun.kafka.enums.KafkaMessageTypeEnum;
import com.pinshang.qingyun.order.dto.orderStatistics.OrderListGiftODTO;
import com.pinshang.qingyun.order.dto.orderStatistics.OrderSyncODTO;
import com.pinshang.qingyun.orderreport.mapper.EmployeeUserMapper;
import com.pinshang.qingyun.orderreport.mapper.StoreMapper;
import com.pinshang.qingyun.orderreport.model.EmployeeUser;
import com.pinshang.qingyun.orderreport.model.Order;
import com.pinshang.qingyun.orderreport.model.Store;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
@Slf4j
public class TjOrderSendKafkaService {
    @Autowired
    private KafkaTemplate<String, String> kafkaTemplate;
    @Autowired
    private EmployeeUserMapper employeeUserMapper;
    @Autowired
    private StoreMapper storeMapper;


    /**
     * 订单同步到ES
     */
    @Async
    @Deprecated
    public void sendEsOrderMsg(Order order,KafkaMessageOperationTypeEnum operationTypeEnum){
        if(order==null || order.getId()==null || order.getStoreId()==null ){
            return;
        }
        OrderSyncODTO orderKafkaVo = new OrderSyncODTO();
        SpringUtil.copyProperties(order,orderKafkaVo);
        if(order.getOrderStatus()==0){
            if(SpringUtil.isEmpty(order.getOrderList())){
                return;
            }
            //操作员姓名
            if(order.getOrderType()!=null && order.getOrderType()==2){
                Store store = storeMapper.selectByPrimaryKey(order.getStoreId());
                orderKafkaVo.setCreateName(store.getStoreName());
                orderKafkaVo.setUpdateName(store.getStoreName());
            }else{
                List<EmployeeUser> userList = employeeUserMapper.queryEmployeeUser(order.getCreateId());
                if(SpringUtil.isNotEmpty(userList)){
                    orderKafkaVo.setCreateName(StringUtils.isEmpty(userList.get(0).getEmployeeName())?null:userList.get(0).getEmployeeName());
                }
                if(order.getUpdateId().equals(order.getCreateId())){
                    orderKafkaVo.setUpdateName(orderKafkaVo.getCreateName());
                }else{
                    List<EmployeeUser> updateUserList = employeeUserMapper.queryEmployeeUser(order.getUpdateId());
                    if(SpringUtil.isNotEmpty(updateUserList)){
                        orderKafkaVo.setUpdateName(StringUtils.isEmpty(userList.get(0).getEmployeeName())?null:updateUserList.get(0).getEmployeeName());
                    }
                }
            }
            //订单明细
            List<OrderListGiftODTO> itemList = BeanCloneUtils.copyTo(order.getOrderList(),OrderListGiftODTO.class);
            orderKafkaVo.setItemList(itemList);
        }
        KafkaMessageWrapper message = new KafkaMessageWrapper(KafkaMessageTypeEnum.ES_STATISTICAL_ORDER, orderKafkaVo, operationTypeEnum);
        log.info("topic = {}, message = {}", QYApplicationContext.applicationNameSwitch + KafkaTopicConstant.ES_STATISTICAL_ORDER_TOPIC, JsonUtil.java2json(message));
        try {
            kafkaTemplate.send(QYApplicationContext.applicationNameSwitch + KafkaTopicConstant.ES_STATISTICAL_ORDER_TOPIC, JsonUtil.java2json(message));
        }catch (Exception e){
            log.error("订单同步到ES消息异常：\n error=" + e + "\n dto=" + JSON.toJSONString(orderKafkaVo) + "\n message=" + message);
        }
    }

    @Async
    @Deprecated
    public void sendMsgUpdateEsOrderStatus(List<Long> orderIdList){
        if(SpringUtil.isEmpty(orderIdList)){
            return;
        }
        KafkaMessageWrapper message = new KafkaMessageWrapper(KafkaMessageTypeEnum.ES_STATISTICAL_ORDER, orderIdList, KafkaMessageOperationTypeEnum.DELETE);
        log.info("topic = {}, message = {}", QYApplicationContext.applicationNameSwitch + KafkaTopicConstant.ES_STATISTICAL_ORDER_TOPIC, JsonUtil.java2json(message));
        try {
            kafkaTemplate.send(QYApplicationContext.applicationNameSwitch + KafkaTopicConstant.ES_STATISTICAL_ORDER_TOPIC, JsonUtil.java2json(message));
        }catch (Exception e){
            log.error("定时清理测试账号订单同步到ES消息异常：\n error=" + e + "\n dto=" + JSON.toJSONString(orderIdList) + "\n message=" + message);
        }
    }

}

package com.pinshang.qingyun.orderreport.mapper;

import com.pinshang.qingyun.base.mybatis.MyMapper;
import com.pinshang.qingyun.orderreport.dto.CommodityQuantityIDTO;
import com.pinshang.qingyun.orderreport.dto.CommodityQuantityIDTO2;
import com.pinshang.qingyun.orderreport.dto.CommodityQuantityODTO;
import com.pinshang.qingyun.orderreport.mapper.entry.StoreOrderListEntry;
import com.pinshang.qingyun.orderreport.mapper.entry.StoreOrderStatisticsEntry;
import com.pinshang.qingyun.orderreport.model.Order;
import com.pinshang.qingyun.orderreport.vo.CommoditySaleStatisticsItemRespVo;
import com.pinshang.qingyun.orderreport.vo.StoreOrderStatisticsVo;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2019/1/14 16:10.
 */
@Repository
public interface OrderMapper extends MyMapper<Order> {
    int batchInsert(List<Order> orderList);

    /**
     * 批量删除订单,包括最新表和历史表主表和子表
     * @param ids
     */
    void batchDeleteOrder(List<Long> ids);

    List<CommoditySaleStatisticsItemRespVo> queryCommoditySaleItemData(@Param("list") List<Long> commodityIds,@Param("orderTime") String orderTime);

    List<StoreOrderListEntry> queryStoreOrderStatistics(@Param("vo") StoreOrderStatisticsVo vo);

    StoreOrderStatisticsEntry queryStoreOrderSum(@Param("vo") StoreOrderStatisticsVo vo);

    /**
     * 根据订单日期统计商品数量
     * @param commodityQuantityIDTO
     * @return
     */
    List<CommodityQuantityODTO> selectOrderCountCommodityQuantityByOrderTime(CommodityQuantityIDTO commodityQuantityIDTO);

    List<CommodityQuantityODTO> selectOrderCountCommodityQuantityByOrderTimeV2(CommodityQuantityIDTO2 commodityQuantityIDTO);
}

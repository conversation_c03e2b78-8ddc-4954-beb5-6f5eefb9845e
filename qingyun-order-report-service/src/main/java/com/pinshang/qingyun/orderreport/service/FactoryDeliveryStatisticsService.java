package com.pinshang.qingyun.orderreport.service;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.pinshang.qingyun.base.configure.expand.QYAssert;
import com.pinshang.qingyun.box.utils.ListUtil;
import com.pinshang.qingyun.box.utils.SpringUtil;
import com.pinshang.qingyun.common.dto.DictionaryIDTO;
import com.pinshang.qingyun.common.dto.DictionaryODTO;
import com.pinshang.qingyun.common.service.DictionaryClient;
import com.pinshang.qingyun.orderreport.mapper.FactoryDeliveryStatisticsItemMapper;
import com.pinshang.qingyun.orderreport.mapper.FactoryDeliveryStatisticsMapper;
import com.pinshang.qingyun.orderreport.mapper.entry.FactoryDeliveryStatisticsByPageEntry;
import com.pinshang.qingyun.orderreport.mapper.entry.FactoryDeliveryStatisticsEntry;
import com.pinshang.qingyun.orderreport.mapper.entry.FactoryDeliveryStatisticsItemByPageEntry;
import com.pinshang.qingyun.orderreport.model.FactoryDeliveryStatistics;
import com.pinshang.qingyun.orderreport.model.FactoryDeliveryStatisticsItem;
import com.pinshang.qingyun.orderreport.vo.FactoryDeliveryStatisticsReqVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.entity.Example;

import java.util.*;
import java.util.stream.Collectors;


/**
 * 产品销售汇总--工厂送货汇总
 */
@Service
@Slf4j
public class FactoryDeliveryStatisticsService {

    @Autowired
    private FactoryDeliveryStatisticsMapper factoryDeliveryStatisticsMapper;
    @Autowired
    private FactoryDeliveryStatisticsItemMapper factoryDeliveryStatisticsItemMapper;
    @Autowired
    private DictionaryClient dictionaryClient;

    @Transactional(rollbackFor = Exception.class)
    public void insertFactoryDeliveryStatistics(Date orderTime,String mainTable, String itemTable){
        List<FactoryDeliveryStatisticsEntry> factoryDeliveryEntryList = factoryDeliveryStatisticsMapper.queryFactoryDeliveryStatistics(orderTime,mainTable,itemTable);
        if(SpringUtil.isNotEmpty(factoryDeliveryEntryList)){
            Map<String,FactoryDeliveryStatistics> factoryMap = new HashMap<>();
            Map<String,List<FactoryDeliveryStatisticsItem>> factoryItemMap = new HashMap<>();
            for (FactoryDeliveryStatisticsEntry item : factoryDeliveryEntryList) {
                String key = item.getFactoryId()+"_"+item.getLineId()+"_"+item.getDeliverymanId()+"_"+item.getLineGroupId();
                FactoryDeliveryStatistics factory = factoryMap.get(key);
                FactoryDeliveryStatisticsItem factoryItem = new FactoryDeliveryStatisticsItem(item.getStoreTypeId(),item.getTotalQuantity(),item.getTotalAmount());
                if (factory == null) {
                    factory = new FactoryDeliveryStatistics(orderTime, item.getFactoryId(), item.getFactoryName(), item.getLineId(), item.getLineName(),
                            item.getLineGroupId(), item.getDeliverymanId(), item.getDeliverymanName(), item.getTotalQuantity(), item.getTotalAmount());
                    factoryMap.put(key,factory);
                    factoryItemMap.put(key,new ArrayList<FactoryDeliveryStatisticsItem>(){{add(factoryItem);}});
                }else{
                    factory.setTotalQuantity(item.getTotalQuantity().add(factory.getTotalQuantity()));
                    factory.setTotalAmount(item.getTotalAmount().add(factory.getTotalAmount()));
                    factoryMap.put(key,factory);
                    factoryItemMap.get(key).add(factoryItem);
                }
            }
            if(!factoryMap.isEmpty()){
                deleteDataByDay(orderTime);
                //分批插入工厂主表数据
                List<FactoryDeliveryStatistics> mainList = new ArrayList<FactoryDeliveryStatistics>(factoryMap.values());
                if (CollectionUtils.isNotEmpty(mainList)){
                    List<List<FactoryDeliveryStatistics>> mainSubList = ListUtil.splitSubList(mainList, 2000);
                    mainSubList.forEach(subList->{
                        int num = factoryDeliveryStatisticsMapper.batchInsert(subList);
                        QYAssert.isTrue(num > 0,"工厂送货汇总添加失败，时间：" + orderTime);
                    });
                }

                //分批插入工厂明细数据
                List<FactoryDeliveryStatisticsItem> itemlist = new ArrayList<>();
                for (FactoryDeliveryStatistics factory : mainList) {
                    String key = factory.getFactoryId()+"_"+factory.getLineId()+"_"+factory.getDeliverymanId()+"_"+factory.getLineGroupId();
                    for (FactoryDeliveryStatisticsItem factoryDeliveryStatisticsItem : factoryItemMap.get(key)) {
                        factoryDeliveryStatisticsItem.setReferId(factory.getId());
                        itemlist.add(factoryDeliveryStatisticsItem);
                    }
                }
                if (CollectionUtils.isNotEmpty(itemlist)){
                    List<List<FactoryDeliveryStatisticsItem>> itemSubList = ListUtil.splitSubList(itemlist, 2000);
                    itemSubList.forEach(subList->{
                        int itemNum = factoryDeliveryStatisticsItemMapper.batchInsert(subList);
                        QYAssert.isTrue(itemNum > 0,"工厂送货汇总明细表添加失败，时间：" + orderTime);
                    });
                }
            }
        }
    }

    public PageInfo<FactoryDeliveryStatisticsByPageEntry> queryFactoryDeliveryStatistics(FactoryDeliveryStatisticsReqVo vo){
        //查询工厂送货信息
        PageInfo<FactoryDeliveryStatisticsByPageEntry> result = PageHelper.startPage(vo.getPageNo(), vo.getPageSize()).doSelectPageInfo(() -> {
            factoryDeliveryStatisticsMapper.queryFactoryDeliveryMain(vo);
        });
        if(result != null && SpringUtil.isNotEmpty(result.getList())){
            //获取客户类型
            DictionaryIDTO dictionaryVo = new DictionaryIDTO();
            dictionaryVo.setDictionaryId(5043047439271865177L);
            List<DictionaryODTO> dictionaryList = dictionaryClient.getDictionaryList(dictionaryVo);
            QYAssert.isTrue(SpringUtil.isNotEmpty(dictionaryList),"字典中未查询到客户类型信息！");
            //工厂送货类型组装
            Map<String,FactoryDeliveryStatisticsItemByPageEntry> itemsMap = new HashMap<>();
            for (FactoryDeliveryStatisticsByPageEntry entry : result.getList()) {
                List<FactoryDeliveryStatisticsItemByPageEntry> itemsNew = new ArrayList<>();
                List<FactoryDeliveryStatisticsItemByPageEntry> items = entry.getItems();
                QYAssert.isTrue(SpringUtil.isNotEmpty(items),"工厂送货,未查询到工厂客户类型明细信息！");
                itemsMap = items.stream().collect(Collectors.toMap(FactoryDeliveryStatisticsItemByPageEntry::getStoreTypeId, p->p));
                for (DictionaryODTO dictionary : dictionaryList) {
                    if(itemsMap.containsKey(dictionary.getId())){
                        FactoryDeliveryStatisticsItemByPageEntry itemEntry = itemsMap.get(dictionary.getId());
                        itemEntry.setStoreTypeName(dictionary.getOptionName());
                        itemsNew.add(itemEntry);
                    }else{
                        itemsNew.add(new FactoryDeliveryStatisticsItemByPageEntry(dictionary.getId(),dictionary.getOptionName()));
                    }
                }
                if(SpringUtil.isNotEmpty(itemsNew)){
                    entry.setItems(itemsNew);
                }
            }
        }
        return result;
    }

    /**
     * 删除数据
     * @return
     */
    public void deleteDataByDay(Date orderTime){
        Example example = new Example(FactoryDeliveryStatistics.class);
        example.createCriteria().andEqualTo("orderTime", orderTime);
        List<FactoryDeliveryStatistics> commoditySaleStatisticss = this.factoryDeliveryStatisticsMapper.selectByExample(example);

        List<Long> ids = commoditySaleStatisticss.stream().map(FactoryDeliveryStatistics::getId).collect(Collectors.toList());

        if(ids.size()>0){
            //删除明细表（t_tj_factory_delivery_statistics_item）
            Example itemExample = new Example(FactoryDeliveryStatisticsItem.class);
            itemExample.createCriteria().andIn("referId",ids);
            factoryDeliveryStatisticsItemMapper.deleteByExample(itemExample);

            //删除主表（t_tj_factory_delivery_statistics）
            example.clear();
            example.createCriteria().andIn("id",ids);
            factoryDeliveryStatisticsMapper.deleteByExample(example);
        }
    }

    public void deleteFactoryDeliveryStatistics(Long commodityId,String orderTime) {
        List<Long> idList = factoryDeliveryStatisticsMapper.queryFactoryDeliveryIdList(commodityId,orderTime);
        if(SpringUtil.isNotEmpty(idList)){
            Example example = new Example(FactoryDeliveryStatisticsItem.class);
            example.createCriteria().andIn("referId",idList);
            factoryDeliveryStatisticsItemMapper.deleteByExample(example);

            Example example1 = new Example(FactoryDeliveryStatistics.class);
            example.createCriteria().andIn("id",idList);
            factoryDeliveryStatisticsMapper.deleteByExample(example1);
        }
    }
}

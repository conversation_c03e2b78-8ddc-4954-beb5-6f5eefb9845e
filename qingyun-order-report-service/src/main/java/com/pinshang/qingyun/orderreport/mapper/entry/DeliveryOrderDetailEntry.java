package com.pinshang.qingyun.orderreport.mapper.entry;

import com.pinshang.qingyun.base.enums.order.OrderTypeEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @Date 2019/3/6 9:47
 */
@Data
public class DeliveryOrderDetailEntry {
    private Long id;
    /**
     * 客户编码
     */
    private String storeCode;
    /**
     * 送货员
     */
    private String deliveryManName;
    /**
     * 督导
     */
    private String supervisorName;
    /**
     * 客户名称
     */
    private String storeName;
    /**
     * 地区
     */
    private String areaName;
    /**
     * 结账客户
     */
    private String settlementCustomer;
    /**
     * 销售员
     */
    private String salesmanName;
    /**
     * 客户备注
     */
    private String storeDescribe;
    /**
     * 地址
     */
    private String deliveryAddress;
    /**
     * 类型
     */
    private String storeTypeName;
    /**
     * 联系电话
     */
    private String linkmanMobile;
    /**
     * 备注
     */
    private String orderRemark;
    /**
     * 余额
     */
    private BigDecimal collectPrice;
    /**
     * 送货日期
     */
    private Date deliveryDate;
    /**
     * 更新日期
     */
    @ApiModelProperty("更新日期")
    private Date updateTime;
    /**
     * 制单日期
     */
    @ApiModelProperty("制单日期")
    private Date createTime;
    /**
     * 操作人员
     */
    private String operatorName;
    /**
     * 订单金额
     */
    private BigDecimal finalAmount;
    /***
     * 订单配送费
     */
    private BigDecimal freightAmount;


    @ApiModelProperty("送货时间要求")
    private String receiveTime;

    /***
     * 订单类型
     */
    private Integer orderType;

    /***
     * 订单类型名称
     */
    private String orderTypeName;
    

    public BigDecimal getFreightAmount() {
    	return freightAmount != null ? freightAmount : BigDecimal.ZERO;
    }
    
    public BigDecimal getFinalAmount() {
    	return finalAmount != null ? finalAmount : BigDecimal.ZERO;
    }
    
    public BigDecimal getSumAmount() {
    	return getFinalAmount().add(getFreightAmount());
    }


    public String getOrderTypeName() {
        if(orderType == null){
            return orderTypeName;
        }
        return OrderTypeEnum.getDesc(orderType);
    }
}

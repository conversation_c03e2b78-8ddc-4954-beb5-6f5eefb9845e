package com.pinshang.qingyun.orderreport.model;

import java.io.Serializable;
import java.util.Date;

import com.pinshang.qingyun.base.enums.log.LogBusinessTypeEnum;
import com.pinshang.qingyun.base.enums.log.LogOperateEnum;
import com.pinshang.qingyun.base.enums.log.LogOperateTypeEnum;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Id;
import javax.persistence.Table;

/**
 * t_ps_sc_line
 * <AUTHOR>
@Data
@Table(name = "t_ps_sc_operation_log")
@AllArgsConstructor
@NoArgsConstructor
public class PsScOperationLog implements Serializable {
    /**
     * 主键
     */
    @Id
    private Long id;

    /**
     * 业务类型
     */
    private Integer businessType;

    /**
     * 业务名称，对应web页面名称，xxx日志管理
     */
    private String businessName;

    /**
     * 操作表名
     */
    private String operationTable;

    /**
     * web页面显示的xxx编码
     */
    private String webCode;

    /**
     * web页面显示的xxx名称
     */
    private String webName;

    /**
     * 操作表列名，使用，间隔
     */
    private String operationColumn;

    /**
     * 操作表列名称，使用，间隔
     */
    private String operationColumnName;

    /**
     * 日志类型 1-新增，2-删除，3-修改
     */
    private Integer logType;

    /**
     * 操作类型：1-新增，2-修改，3-启用，4-停用，5-添加，6-删除（新增商品组、修改商品组、启用商品组、停用商品组、添加商品、删除商品）
     */
    private Integer operationType;

    /**
     * 操作类型名称，如停用商品组
     */
    private String operationTypeName;

    /**
     * 操作前详情，对应操作列原值
     */
    private String operationOld;

    /**
     * 操作后详情，对应操作列新值
     */
    private String operationNew;

    /**
     * 操作人
     */
    private Long createId;

    /**
     * 操作时间
     */
    private Date createTime;

    public PsScOperationLog(LogBusinessTypeEnum logBusinessTypeEnum, LogOperateTypeEnum logOperateTypeEnum, LogOperateEnum logOperateEnum, Long createId, String webName, String webCode, String operationColumn, String operationColumnName, String operationTypeName, String operationOld, String operationNew, Date date) {
        this.setBusinessType(logBusinessTypeEnum.getCode());
        this.setBusinessName(logBusinessTypeEnum.getName());
        this.setOperationTable(logBusinessTypeEnum.getTable());
        this.setCreateId(createId);
        this.setCreateTime(date);
        this.setLogType(logOperateTypeEnum.getCode());
        this.setWebName(webName);
        this.setWebCode(webCode);
        this.setOperationColumn(operationColumn);
        this.setOperationColumnName(operationColumnName);
        this.setOperationTypeName(operationTypeName);
        this.setOperationType(logOperateEnum.getCode());
        this.setOperationOld(operationOld);
        this.setOperationNew(operationNew);
    }


    private static final long serialVersionUID = 1L;
}
package com.pinshang.qingyun.orderreport.vo.ps;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @Version 0.1.0
 * @Date 2021/1/26 13:51
 * @Copyright © 2019-2021 qm
 * @Description -- 类说明
 **/
@Data
public class PsScLineVO {
    @ApiModelProperty(value = "主键")
    private String id;

    @ApiModelProperty(value = "线路组id")
    private String lineGroupId;

    @ApiModelProperty(value = "线路组名称")
    private String lineGroupName;

    @ApiModelProperty(value = "线路编码")
    private String lineCode;

    @ApiModelProperty(value = "线路名称")
    private String lineName;

    @ApiModelProperty(value = "商品组id")
    private String commodityGroupId;

    @ApiModelProperty(value = "商品组名称")
    private String commodityGroupName;

    @ApiModelProperty(value = "发货仓库id")
    private String deliveryWarehouseId;

    @ApiModelProperty(value = "发货仓库名称")
    private String deliveryWarehouseName;

    @ApiModelProperty(value = "送货员code")
    private String deliverymanCode;

    @ApiModelProperty(value = "送货员")
    private String deliverymanName;

    @ApiModelProperty(value = "送货员手机号")
    private String deliverymanPhone;

    @ApiModelProperty(value = "车位")
    private String carportName;

    @ApiModelProperty(value = "车牌号")
    private String licensePlate;

    @ApiModelProperty(value = "客户数量")
    private Long storeQuantity;

    @ApiModelProperty(value = "商品组线路状态:true-启用状态，false-停用状态")
    private Boolean status;

}

package com.pinshang.qingyun.orderreport.controller;

import com.pinshang.qingyun.orderreport.dto.DeliveryNoteIDTO;
import com.pinshang.qingyun.orderreport.mapper.entry.DeliveryNoteEntry;
import com.pinshang.qingyun.orderreport.service.DeliveryNoteService;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * @author: chen<PERSON>ang
 * @time: 2021/9/17 11:03
 */
@RestController
@RequestMapping(value = "/deliveryNote")
public class DeliveryNoteController {

    @Resource
    private DeliveryNoteService deliveryNoteService;

    @ApiOperation(value = "查看当前送货员的送货单", notes = "查看当前送货员的送货单")
    @PostMapping(value = "/selectDeliveryNote")
    public DeliveryNoteEntry selectDeliveryNote(@RequestBody DeliveryNoteIDTO deliveryNoteIDTO){
        return deliveryNoteService.selectDeliveryNote(deliveryNoteIDTO);
    }
}

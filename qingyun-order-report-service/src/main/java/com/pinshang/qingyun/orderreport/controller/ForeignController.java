package com.pinshang.qingyun.orderreport.controller;

import com.pinshang.qingyun.orderreport.dto.CommodityQuantityIDTO;
import com.pinshang.qingyun.orderreport.dto.CommodityQuantityIDTO2;
import com.pinshang.qingyun.orderreport.dto.CommodityQuantityODTO;
import com.pinshang.qingyun.orderreport.service.ForeignService;
import io.swagger.annotations.ApiModelProperty;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 对外接口提供
 * @author: chenqiang
 * @time: 22/7/20/020 14:24
 */
@RestController
@RequestMapping(value = "/foreign")
public class ForeignController {

    @Autowired
    private ForeignService foreignService;

    @PostMapping("/selectOrderCountCommodityQuantityByOrderTime")
    @ApiModelProperty(value = "根据订单日期统计商品数量", notes = "根据订单日期统计商品数量")
    public List<CommodityQuantityODTO> selectOrderCountCommodityQuantityByOrderTime(@RequestBody CommodityQuantityIDTO commodityQuantityIDTO){
       return foreignService.selectOrderCountCommodityQuantityByOrderTime(commodityQuantityIDTO);
    }

    @PostMapping("/selectOrderCountCommodityQuantityByOrderTimeV2")
    @ApiModelProperty(value = "根据订单日期统计商品数量", notes = "根据订单日期统计商品数量")
    public List<CommodityQuantityODTO> selectOrderCountCommodityQuantityByOrderTimeV2(@RequestBody CommodityQuantityIDTO2 commodityQuantityIDTO){
        return foreignService.selectOrderCountCommodityQuantityByOrderTime2(commodityQuantityIDTO);
    }
}

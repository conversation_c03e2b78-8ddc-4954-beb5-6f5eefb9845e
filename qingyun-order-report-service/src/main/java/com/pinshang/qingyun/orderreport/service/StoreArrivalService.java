package com.pinshang.qingyun.orderreport.service;

import java.math.BigDecimal;
import java.util.List;

import lombok.extern.slf4j.Slf4j;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.pinshang.qingyun.base.configure.expand.QYAssert;
import com.pinshang.qingyun.box.utils.SpringUtil;
import com.pinshang.qingyun.box.utils.StringUtil;
import com.pinshang.qingyun.orderreport.dto.storearrival.DistanceInfoODTO;
import com.pinshang.qingyun.orderreport.dto.storearrival.SelectStoreArrivalInfoPageIDTO;
import com.pinshang.qingyun.orderreport.dto.storearrival.StoreArrivalInfoODTO;
import com.pinshang.qingyun.orderreport.dto.storearrival.StoreArrivalKafkaIDTO;
import com.pinshang.qingyun.orderreport.kafka.SendKafkaLogMsgService;
import com.pinshang.qingyun.orderreport.mapper.StoreArrivalMapper;
import com.pinshang.qingyun.orderreport.mapper.StoreMapper;
import com.pinshang.qingyun.orderreport.model.Store;
import com.pinshang.qingyun.orderreport.model.StoreArrival;
import com.pinshang.qingyun.orderreport.model.StoreArrivalLog;
import com.pinshang.qingyun.store.dto.store.SelectStoreCoordinateListIDTO;
import com.pinshang.qingyun.store.dto.store.StoreCoordinateODTO;
import com.pinshang.qingyun.store.service.StoreNewClient;

/**
 * 客户到货
 */
@Slf4j
@Service
public class StoreArrivalService {
	
	@Autowired
	private StoreMapper storeMapper;
	
	@Autowired
	private StoreNewClient storeNewClient;

	@Autowired
	private StoreArrivalMapper storeArrivalMapper;
	
	@Autowired
	private SendKafkaLogMsgService sendKafkaLogMsgService;
	
	@Value("${pinshang.img-server-url}")
    private String imgServerUrl;

	/**
	 * 分页查询 客户到货信息 列表
	 * 
	 * @param idto
	 * @return
	 */
	public PageInfo<StoreArrivalInfoODTO> selectStoreArrivalInfoPage(SelectStoreArrivalInfoPageIDTO idto) {
		QYAssert.isTrue(null != idto, "参数不能为空!");
		PageInfo<StoreArrivalInfoODTO> pageInfo = PageHelper.startPage(idto.getPageNo(), idto.getPageSize()).doSelectPageInfo(() -> {
			storeArrivalMapper.selectStoreArrivalInfoList(idto);
		});
		
		List<StoreArrivalInfoODTO> list = pageInfo.getList();
		if (SpringUtil.isNotEmpty(list)) {
			if (!imgServerUrl.endsWith("/")) {
				imgServerUrl = imgServerUrl + "/";
			}
			list.forEach(o -> {
				o.buildArrivalPicList(imgServerUrl);
			});
		}
		
		return pageInfo;
	}
	
	/**
	 * 保存  客户到货信息
	 * 
	 * @param idto
	 */
	@Transactional(rollbackFor = Exception.class)
	public void saveStoreArrival(StoreArrivalKafkaIDTO idto) {
		if (null == idto) {
			log.warn("\n保存客户到货信息，不成功：idto is null!");
			return;
		}
		
		if (null == idto.getOrderTime() || StringUtil.isNullOrEmpty(idto.getStoreCode())) {
			log.warn("\n保存客户到货信息，不成功：orderTime or storeCode is null! idto={}", idto);
			return;
		}
		
//		if (null == idto.getUserId()) {
//			log.warn("\n保存客户到货信息，不成功：userId is null! idto={}", idto);
//			return;
//		}
		
		Store store = new Store();
		store.setStoreCode(idto.getStoreCode());
		List<Store> storeList = storeMapper.select(store);
		if (SpringUtil.isEmpty(storeList)) {
			log.warn("\n保存客户到货信息，不成功：客户编码不存在! idto={}", idto);
			return;
		}
		
		store = storeList.get(0);
		
		Integer operateType = null;
		StoreArrival storeArrival = storeArrivalMapper.selectOne(new StoreArrival(store.getId(), idto.getOrderTime()));
		if (null == storeArrival) {
			storeArrival = StoreArrival.forInsert(store, idto);
			storeArrivalMapper.insert(storeArrival);
			operateType = StoreArrivalLog.OperateTypeEnums.INSERT.getCode();
		} else {
			StoreArrival.forUpdate(storeArrival, store, idto);
			storeArrivalMapper.updateByPrimaryKey(storeArrival);
			operateType = StoreArrivalLog.OperateTypeEnums.UPDATE.getCode();
		}
		
		this.sendStoreArrivalKafkaMsg(idto, storeArrival, store, operateType);
	}
	
	/**
	 * 发送  客户到货消息
	 * 
	 * @param idto
	 * @param storeArrival
	 * @param store
	 * @param operateType
	 */
	private void sendStoreArrivalKafkaMsg(StoreArrivalKafkaIDTO idto, StoreArrival storeArrival, Store store, Integer operateType) {
		BigDecimal locationLon = idto.getBaiduLongitude();
		BigDecimal locationLat = idto.getBaiduLatitude();
		BigDecimal storeLon = null;
		BigDecimal storeLat = null;
		
		if (null != locationLon && null != locationLat) {
			List<StoreCoordinateODTO> scList = storeNewClient.selectStoreCoordinateList(SelectStoreCoordinateListIDTO.init(store.getId()));
			if (SpringUtil.isNotEmpty(scList)) {
				StoreCoordinateODTO sc = scList.get(0);
				storeLon = sc.getBaiduLongitude();
				storeLat = sc.getBaiduLatitude();
			}
		}
		
		DistanceInfoODTO di = DistanceInfoODTO.getDistanceInfo(locationLon, locationLat, storeLon, storeLat);
		StoreArrivalLog log = new StoreArrivalLog(storeArrival, store.getStoreName(), operateType, idto.getLocationAddress(), di);
		sendKafkaLogMsgService.sendLogMsg("t_log_tj_store_arrival", log);
	}

}

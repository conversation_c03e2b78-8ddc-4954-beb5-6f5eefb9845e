package com.pinshang.qingyun.orderreport.service;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.pinshang.qingyun.box.utils.SpringUtil;
import com.pinshang.qingyun.orderreport.mapper.CommodityMapper;
import com.pinshang.qingyun.orderreport.model.Commodity;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Example;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static java.util.stream.Collectors.groupingBy;

/**
 * 商品查询
 */
@Service
@Slf4j
public class CommodityService {

    @Autowired
    private CommodityMapper commodityMapper;

    public Map<String,String> queryCommodityCateName(List<String> commodityCodes){
        Map<String,String> cateNameMap = new HashMap<>();
        if(SpringUtil.isEmpty(commodityCodes)){
            return cateNameMap;
        }
        Example example = new Example(Commodity.class);
        example.createCriteria().andIn("commodityCode",commodityCodes);
        example.selectProperties("commodityCode", "commodityFirstKindName", "commoditySecondKindName", "commodityThirdKindName");
        List<Commodity> commodityList = commodityMapper.selectByExample(example);
        commodityList.stream().collect(groupingBy(Commodity::getCommodityCode)).forEach((k, v) ->{
            Commodity c = v.get(0);
            cateNameMap.put(k, c.getCommodityFirstKindName()+","+c.getCommoditySecondKindName()+","+c.getCommodityThirdKindName());
        });
        return cateNameMap;
    }

    public Map<String,Commodity> queryCommodityMap(List<String> commodityCodes){
        Map<String,Commodity> map = new HashMap<>();
        if(SpringUtil.isEmpty(commodityCodes)){
            return map;
        }
        Example example = new Example(Commodity.class);
        example.createCriteria().andIn("commodityCode",commodityCodes);
        example.selectProperties("commodityCode", "commodityFirstKindName", "commoditySecondKindName", "commodityThirdKindName","commodityUnitName","commodityFlowshopName","taxRateId");
        List<Commodity> commodityList = commodityMapper.selectByExample(example);
        commodityList.stream().collect(groupingBy(Commodity::getCommodityCode)).forEach((k, v) ->{
            Commodity c = v.get(0);
            map.put(k, c);
        });
        return map;
    }

    public PageInfo<Long> queryCommodityIds(int pageNo, int pageSize){
        return PageHelper.startPage(pageNo, pageSize).doSelectPageInfo(() -> {
            commodityMapper.queryCommodityIds();
        });
    }

    public List<Long> queryCommodityIdList(){
        return commodityMapper.queryCommodityIds();
    }
}

package com.pinshang.qingyun.orderreport.model;

import lombok.Data;

import javax.persistence.Entity;
import javax.persistence.Table;
import java.math.BigDecimal;
import java.util.Date;

/**
 * @description:
 * @author: hhf
 * @time: 2021/8/13 10:19
 */
@Data
@Entity
@Table(name = "t_tj_order_sync")
public class OrderSync {

    /**订单ID**/
    private Long id;
    /**订单编号**/
    private String orderCode;
    /**商铺id **/
    private Long storeId;
    /**公司id**/
    private Long companyId;
    /**订单时间 **/
    private Date orderTime;
    /**订单类型 1=PC下单,2=APP下单, 10-门店订货下单 **/
    private Integer orderType;
    /**订单来源 0：普通订单，1：补货单**/
    private Integer modeType;

    /** 打印类型(1：本地,2：送货员,3：不打印) */
    private Integer printType;
    /**打印份数*/
    private Integer printNum;

    /**订单优惠之前的价格*/
    private BigDecimal totalAmount;
    /**订单金额 **/
    private BigDecimal orderAmount;
    /**最终金额*/
    private BigDecimal finalAmount;
    /**备注 **/
    private String orderRemark;

    /**订单状态(0正常,1删除,2取消) **/
    private Integer orderStatus;
    /** 0:正常   1：未结算 **/
    private Integer settleStatus;

    /**创建者 **/
    private Long createId;
    /**创建时间*/
    protected Date createTime;
    /**更新者 **/
    private Long updateId;
    /**修改时间*/
    protected Date updateTime;
    /**0-无需批次配送, 1-1配，2-2配，3-3配，9-临时批次**/
    private Integer deliveryBatch;
    /** 订单是否允许修改价格 */
    private Integer changePriceStatus;
}

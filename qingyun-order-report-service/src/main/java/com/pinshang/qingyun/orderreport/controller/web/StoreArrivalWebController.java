package com.pinshang.qingyun.orderreport.controller.web;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;

import java.util.List;

import javax.servlet.http.HttpServletResponse;

import lombok.extern.slf4j.Slf4j;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.alibaba.excel.EasyExcel;
import com.github.pagehelper.PageInfo;
import com.pinshang.qingyun.base.util.ExcelUtil;
import com.pinshang.qingyun.orderreport.dto.storearrival.SelectStoreArrivalInfoPageIDTO;
import com.pinshang.qingyun.orderreport.dto.storearrival.StoreArrivalInfoODTO;
import com.pinshang.qingyun.orderreport.service.StoreArrivalService;
import com.pinshang.qingyun.renderer.annotation.MethodRender;
import com.pinshang.qingyun.renderer.service.IRenderService;

/**
 * 客户到货
 */
@Slf4j
@RestController
@RequestMapping("/storeArrival/web")
@Api(value = "客户到货管理-Web", description = "StoreArrivalWebController")
public class StoreArrivalWebController {
	
	@Autowired
	private IRenderService renderService;

	@Autowired
	private StoreArrivalService storeArrivalService;

	@MethodRender
	@ApiOperation(value = "分页查询 客户到货信息 列表")
	@ApiImplicitParam(name = "idto", value = "请求IDTO", required = true, paramType = "body", dataTypeClass = SelectStoreArrivalInfoPageIDTO.class)
	@PostMapping(value = "/selectStoreArrivalInfoPage")
	public PageInfo<StoreArrivalInfoODTO> selectStoreArrivalInfoPage(@RequestBody SelectStoreArrivalInfoPageIDTO idto) {
		return storeArrivalService.selectStoreArrivalInfoPage(idto);
	}
	
	
	@ApiOperation(value = "导出  客户到货信息 列表")
    @GetMapping(value = "/exportStoreArrivalInfoList")
    public void exportStoreArrivalInfoList(SelectStoreArrivalInfoPageIDTO idto, HttpServletResponse response) throws Exception {
		idto.initExportPage();
    	List<StoreArrivalInfoODTO> list = storeArrivalService.selectStoreArrivalInfoPage(idto).getList();
    	
    	renderService.render(list, "/storeArrival/web/exportStoreArrivalInfoList");
    	
    	long beginTime = System.currentTimeMillis();
        try {
            ExcelUtil.setFileNameAndHead(response, "客户到货信息列表" + beginTime);
            EasyExcel.write(response.getOutputStream(), StoreArrivalInfoODTO.class).autoCloseStream(Boolean.FALSE).sheet("到货信息").doWrite(list == null ? null : list);
        } catch (Exception e) {
            log.error("导出  客户到货信息 列表  异常：", e);
            ExcelUtil.setExceptionResponse(response);
        }
        log.info("导出  客户到货信息 列表  完成，耗时{}秒", ((System.currentTimeMillis() - beginTime) / 1000));
    }

}

package com.pinshang.qingyun.orderreport.dto.storearrival;

import io.swagger.annotations.ApiModelProperty;

import java.math.BigDecimal;

import lombok.Data;
import lombok.NoArgsConstructor;

import com.pinshang.qingyun.box.utils.DistanceUtils;

/**
 * 距离信息
 */
@Data
@NoArgsConstructor
public class DistanceInfoODTO {
	@ApiModelProperty(position = 11, required = true, value = "距离")
	private Integer distance;
	@ApiModelProperty(position = 11, value = "备注信息")
	private String remark;

	private static DistanceInfoODTO init(Integer distance, String remark) {
		DistanceInfoODTO o = new DistanceInfoODTO();
		o.distance = distance;
		o.remark = remark;
		return o;
	}
	
	public final static Integer MAX_DISTANCE = 99999999;
	private final static BigDecimal THOUSAND = BigDecimal.valueOf(1000);
	
	/**
	 * 计算坐标距离
	 * 
	 * @param locationLon 定位经度
	 * @param locationLat 定位维度
	 * @param storeLon 客户经度
	 * @param storeLat 客户维度
	 * @return
	 */
	public static DistanceInfoODTO getDistanceInfo(BigDecimal locationLon, BigDecimal locationLat, BigDecimal storeLon, BigDecimal storeLat) {
		if (null == locationLon || null == locationLat) {
			return DistanceInfoODTO.init(MAX_DISTANCE, DistanceRemarkEnums.LOCATION_FAIL.getRemark());
		}
		
		if (null == storeLon || null == storeLat) {
			return DistanceInfoODTO.init(MAX_DISTANCE, DistanceRemarkEnums.STORE_LON_LAT_FAULT.getRemark());
		}
		
		int distance = (int)DistanceUtils.GetDistance(locationLon.doubleValue(), locationLat.doubleValue(), storeLon.doubleValue(), storeLat.doubleValue());
		return DistanceInfoODTO.init(distance, null);
	}
	
	/**
	 * 格式化距离
	 * 
	 * 1、距离x < 0 -> 距离异常
	 * 2、距离abc < 1000 --> abc m
	 * 3、距离abcd < 1000 -> a.bc km
	 * 
	 * @param di
	 * @return
	 */
	public static DistanceInfoODTO getFormatDistance(BigDecimal locationLon, BigDecimal locationLat, BigDecimal storeLon, BigDecimal storeLat) {
		if (null == locationLon || null == locationLat) {
			return DistanceInfoODTO.init(MAX_DISTANCE, DistanceRemarkEnums.LOCATION_FAIL.getRemark());
		}
		if (null == storeLon || null == storeLat) {
			return DistanceInfoODTO.init(MAX_DISTANCE, DistanceRemarkEnums.STORE_LON_LAT_FAULT.getRemark());
		}
		
		int distance = (int)DistanceUtils.GetDistance(locationLon.doubleValue(), locationLat.doubleValue(), storeLon.doubleValue(), storeLat.doubleValue());
		if ((distance < 0)) {
			return DistanceInfoODTO.init(MAX_DISTANCE, DistanceRemarkEnums.DISTANCE_ABNORMAL.getRemark());
    	}
		
		if (distance < THOUSAND.intValue()) {
			return DistanceInfoODTO.init(distance, String.format("%s%s", distance, "m"));
		} else {
			return DistanceInfoODTO.init(distance, String.format("%s%s", BigDecimal.valueOf(distance).divide(THOUSAND, 2, BigDecimal.ROUND_HALF_UP), "km"));
		}
    }
	
	/**
	 * 距离备注
	 */
	public enum DistanceRemarkEnums {
		LOCATION_FAIL("定位失败"), //
		STORE_LON_LAT_FAULT("无地址坐标"), //
		DISTANCE_ABNORMAL("距离异常"), //
		;
		private String remark;

		DistanceRemarkEnums(String remark) {
			this.remark = remark;
		}

		public String getRemark() {
			return remark;
		}
	}

}

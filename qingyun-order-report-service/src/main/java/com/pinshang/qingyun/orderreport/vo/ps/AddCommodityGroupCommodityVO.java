package com.pinshang.qingyun.orderreport.vo.ps;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @Version 0.1.0
 * @Date 2021/1/22 15:42
 * @Copyright © 2019-2021 qm
 * @Description -- 类说明
 **/
@Data
public class AddCommodityGroupCommodityVO {

    @ApiModelProperty(position = 1, value = "商品组id", example = "963258", required = true)
    private Long groupId;

    @ApiModelProperty(position = 2,value = "商品编码，回车键间隔")
    private String commodityCodes;

    @ApiModelProperty(hidden = true, value = "操作人")
    private Long createId;
}

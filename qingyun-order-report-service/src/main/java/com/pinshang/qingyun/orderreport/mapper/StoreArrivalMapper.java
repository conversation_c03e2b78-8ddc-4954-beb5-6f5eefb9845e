package com.pinshang.qingyun.orderreport.mapper;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.springframework.stereotype.Repository;

import com.pinshang.qingyun.base.mybatis.MyMapper;
import com.pinshang.qingyun.orderreport.dto.storearrival.SelectStoreArrivalInfoPageIDTO;
import com.pinshang.qingyun.orderreport.dto.storearrival.StoreArrivalInfoODTO;
import com.pinshang.qingyun.orderreport.model.StoreArrival;

/**
 * 客户到货
 */
@Mapper
@Repository
public interface StoreArrivalMapper extends MyMapper<StoreArrival> {

	/**
	 * 查询 客户到货信息 列表
	 * 
	 * @param idto
	 * @return
	 */
	public List<StoreArrivalInfoODTO> selectStoreArrivalInfoList(SelectStoreArrivalInfoPageIDTO idto);

}

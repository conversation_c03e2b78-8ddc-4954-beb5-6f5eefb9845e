package com.pinshang.qingyun.orderreport.mapper;

import com.pinshang.qingyun.base.mybatis.MyMapper;
import com.pinshang.qingyun.orderreport.model.PsScCommodityGroupItem;
import com.pinshang.qingyun.orderreport.vo.ps.CommodityGroupCommodityVO;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface PsScCommodityGroupItemMapper extends MyMapper<PsScCommodityGroupItem> {

    /**
     * 通过商品id获取商品组id
     * @param commodityGroupId
     * @return
     */
    List<Long> selectByCommodityGroupId(Long commodityGroupId);

    /**
     * 查询商品组不存在的商品
     *
     * @param commodityCodeList
     * @return
     */
    List<CommodityGroupCommodityVO> selectInExistenceByCommodityCodeList(@Param("groupId") Long groupId, @Param("commodityCodeList") List<String> commodityCodeList);

    /**
     * 查询商品组下的商品id
     * @param groupId
     * @return
     */
    List<Long> queryCommodityByGroupId(@Param("groupId") Long groupId);
}
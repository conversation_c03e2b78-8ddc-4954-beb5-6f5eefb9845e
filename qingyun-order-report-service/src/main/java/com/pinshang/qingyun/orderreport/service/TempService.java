package com.pinshang.qingyun.orderreport.service;

import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import tk.mybatis.mapper.entity.Example;

import com.pinshang.qingyun.base.configure.expand.QYAssert;
import com.pinshang.qingyun.box.utils.DateUtil;
import com.pinshang.qingyun.box.utils.ListUtil;
import com.pinshang.qingyun.box.utils.SpringUtil;
import com.pinshang.qingyun.orderreport.mapper.OrderItemSyncMapper;
import com.pinshang.qingyun.orderreport.mapper.OrderSyncMapper;
import com.pinshang.qingyun.orderreport.model.OrderItemSync;
import com.pinshang.qingyun.orderreport.model.OrderSync;

/**
 * 临时接口
 */
@Deprecated
@Slf4j
@Service
public class TempService {
	
    @Autowired
    private OrderSyncMapper orderSyncMapper;
    
    @Autowired
    private OrderItemSyncMapper orderItemSyncMapper;
    
    private static final String DEFAULT_DATE_FORMAT = "yyyy-MM-dd";
    private static final String DEFAULT_BEGIN_ORDER_TIME_STR = "2019-12-24";
    private static final String DEFAULT_END_ORDER_TIME_STR = "2022-08-31";
    private static final Date DEFAULT_BEGIN_ORDER_TIME = DateUtil.parseDate(DEFAULT_BEGIN_ORDER_TIME_STR, DEFAULT_DATE_FORMAT);
    private static final Date DEFAULT_END_ORDER_TIME = DateUtil.parseDate(DEFAULT_END_ORDER_TIME_STR, DEFAULT_DATE_FORMAT);
    
    /**
     * 清理 tj.t_tj_order_sync表 和 tj.t_tj_order_list_sync表
     * 
     * @return
     */
    @Deprecated
    public DeleteTJOrderSyncByDateResult removeTJOrderSyncByDate(String specificOrderTime) {
        log.warn("\n清理 tj.t_tj_order_sync表 和 tj.t_tj_order_list_sync表: 开始执行...");
        DeleteTJOrderSyncByDateResult result = new DeleteTJOrderSyncByDateResult();
        result.setBeginTime(System.currentTimeMillis());

    	// 默认执行区间
        Date beginOrderTime = null, endOrderTime = null;
        if (null == specificOrderTime) {
        	beginOrderTime = DEFAULT_BEGIN_ORDER_TIME;
        	endOrderTime = DEFAULT_END_ORDER_TIME;
        	result.initDefault();
        } else {
        	// 执行特定日期
        	beginOrderTime = DateUtil.parseDate(specificOrderTime, DEFAULT_DATE_FORMAT);
        	endOrderTime = DateUtil.parseDate(specificOrderTime, DEFAULT_DATE_FORMAT);
        	QYAssert.isTrue(null != beginOrderTime && null != endOrderTime, "特定日期格式必须为：" + DEFAULT_DATE_FORMAT);
        	QYAssert.isTrue(!beginOrderTime.before(DEFAULT_BEGIN_ORDER_TIME) && !endOrderTime.after(DEFAULT_END_ORDER_TIME), "特定日期必须属于[" + DEFAULT_BEGIN_ORDER_TIME_STR + "," + DEFAULT_END_ORDER_TIME_STR + "]");
        	result.initSpecific(specificOrderTime);
        }
    	
    	Date thisOrderTime = beginOrderTime;
    	while(!thisOrderTime.after(endOrderTime)) {
    		String thisOrderTimeStr = DateUtil.getDateFormate(thisOrderTime, DEFAULT_DATE_FORMAT);
    		int thisDeleteOrderSyncQuantity = 0, thisDeleteOrderItemSyncQuantity = 0;
    		
    		// 查询 t_tj_order_sync.id
    		Example selectOrderExample = new Example(OrderSync.class);
    		selectOrderExample.createCriteria().andEqualTo("orderTime", thisOrderTimeStr);
    		selectOrderExample.selectProperties("id");
            List<OrderSync> orderList = orderSyncMapper.selectByExample(selectOrderExample);
            if (SpringUtil.isNotEmpty(orderList)) {
            	List<Long> orderIdList = orderList.stream().map(OrderSync::getId).collect(Collectors.toList());
            	
            	// 删除 t_tj_order_sync
            	List<List<Long>> orderIdListList = ListUtil.splitSubList(orderIdList, 500);
            	for (List<Long> subOrderIdList: orderIdListList) {
            		Example deleteOrderExample = new Example(OrderSync.class);
            		deleteOrderExample.createCriteria().andIn("id", subOrderIdList);
            		thisDeleteOrderSyncQuantity = orderSyncMapper.deleteByExample(deleteOrderExample);
            		result.deleteOrderSyncQuantity += thisDeleteOrderSyncQuantity;
            		
            		// 删除 t_tj_order_list_sync
            		Example deleteOrderItemExample = new Example(OrderItemSync.class);
            		deleteOrderItemExample.createCriteria().andIn("orderId", subOrderIdList);
            		thisDeleteOrderItemSyncQuantity = orderItemSyncMapper.deleteByExample(deleteOrderItemExample);
            		result.deleteOrderItemSyncQuantity += thisDeleteOrderItemSyncQuantity;
            	}
            }
            log.warn("\n清理 tj.t_tj_order_sync表 和 tj.t_tj_order_list_sync表: thisOrderTimeStr={}，thisDeleteOrderSyncQuantity={}，thisDeleteOrderItemSyncQuantity={}!", thisOrderTimeStr, thisDeleteOrderSyncQuantity, thisDeleteOrderItemSyncQuantity);
            
    		System.out.println("specificOrderTime=" + specificOrderTime + "thisOrderTime=" + thisOrderTime + ",thisOrderTimeStr=" + thisOrderTimeStr);
    		thisOrderTime = DateUtil.addDay(thisOrderTime, 1);
    	}
    	
    	result.setEndTime(System.currentTimeMillis());
    	log.warn("\n清理 tj.t_tj_order_sync表 和 tj.t_tj_order_list_sync表: 开始结束，result={}!", result);
    	return result;
    }
    
    public static void main(String[] args) {
    	testDateRage(null);
//    	testDateRage("2022-08-31");
    	
//    	testDate();
    }
    
    private static void testDateRage(String specificOrderTime) {
    	// 默认执行区间
        Date beginOrderTime = null, endOrderTime = null;
        if (null == specificOrderTime) {
        	beginOrderTime = DEFAULT_BEGIN_ORDER_TIME;
        	endOrderTime = DEFAULT_END_ORDER_TIME;
        } else {
        	// 执行特定日期
        	beginOrderTime = DateUtil.parseDate(specificOrderTime, DEFAULT_DATE_FORMAT);
        	endOrderTime = DateUtil.parseDate(specificOrderTime, DEFAULT_DATE_FORMAT);
        	QYAssert.isTrue(null != beginOrderTime && null != endOrderTime, "特定日期格式必须为：" + DEFAULT_DATE_FORMAT);
        	QYAssert.isTrue(!beginOrderTime.before(DEFAULT_BEGIN_ORDER_TIME) && !endOrderTime.after(DEFAULT_END_ORDER_TIME), "特定日期必须属于[" + DEFAULT_BEGIN_ORDER_TIME_STR + "," + DEFAULT_END_ORDER_TIME_STR + "]");
        }
        
    	Date thisOrderTime = beginOrderTime;
    	while(!thisOrderTime.after(endOrderTime)) {
    		String thisOrderTimeStr = DateUtil.getDateFormate(thisOrderTime, DEFAULT_DATE_FORMAT);
    		
    		// 具体业务代码
    		
    		System.out.println("specificOrderTime=" + specificOrderTime + "thisOrderTime=" + thisOrderTime + ",thisOrderTimeStr=" + thisOrderTimeStr);
    		thisOrderTime = DateUtil.addDay(thisOrderTime, 1);
    	}
    }
    private static void testDate() {
    	Calendar calendar = Calendar.getInstance();
        Date nowDate = DateUtil.getNowDate();
        calendar.setTime(nowDate);
        calendar.add(Calendar.MONTH, -6);
        Date date = calendar.getTime();
        System.out.println("date=" + date);
    }
    
    @Data
    @NoArgsConstructor
    public static class DeleteTJOrderSyncByDateResult {
    	private String specificOrderTime;
    	private String beginOrderTime;
    	private String endOrderTime;
    	
    	private long deleteOrderSyncQuantity;
    	private long deleteOrderItemSyncQuantity;
    	
    	private long beginTime;
    	private long endTime;
    	private long time;
    	
    	public DeleteTJOrderSyncByDateResult initDefault() {
    		this.setSpecificOrderTime(null);
    		this.setBeginOrderTime(DEFAULT_BEGIN_ORDER_TIME_STR);
    		this.setEndOrderTime(DEFAULT_END_ORDER_TIME_STR);
    		return this;
    	}
    	
    	public DeleteTJOrderSyncByDateResult initSpecific(String specificOrderTime) {
    		this.setSpecificOrderTime(specificOrderTime);
    		this.setBeginOrderTime(specificOrderTime);
    		this.setEndOrderTime(specificOrderTime);
    		return this;
    	}
    	
    	public long getTime() {
    		return this.endTime - this.beginTime;
    	}
    	
    }

}

package com.pinshang.qingyun.orderreport.service;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.pinshang.qingyun.base.configure.expand.QYAssert;
import com.pinshang.qingyun.base.enums.PrinterDataGroupTypeEnum;
import com.pinshang.qingyun.box.utils.DateUtil;
import com.pinshang.qingyun.box.utils.SpringUtil;
import com.pinshang.qingyun.orderreport.mapper.ReplenishmentStatisticsMapper;
import com.pinshang.qingyun.orderreport.mapper.entry.ReplenishmentStatisticsEntry;
import com.pinshang.qingyun.orderreport.pdf.PdfPrintService;
import com.pinshang.qingyun.orderreport.pdf.ReplenishmentStatisticsPdfCreator;
import com.pinshang.qingyun.orderreport.vo.ReplenishmentStatisticsVo;
//import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 补货数据汇总service
 */
@Service
//@Slf4j
public class ReplenishmentStatisticsService {

    @Autowired
    private ReplenishmentStatisticsMapper replenishmentStatisticsMapper;
    @Autowired
    private ReplenishmentStatisticsPdfCreator replenishmentStatisticsPdfCreator;
    @Autowired
    private PrintTaskService printTaskService;
    @Autowired
    private PdfPrintService pdfPrintService;
    @Autowired
    private CommonPdfPrintService commonPdfPrintService;
    /**
     * 补货数据汇总（主要查询补货订单的商品数量汇总）
     * @param vo
     * @return
     */
    public PageInfo<ReplenishmentStatisticsEntry> queryList(ReplenishmentStatisticsVo vo) {
        QYAssert.notNull(vo.getStartDate(),"送货日期不能为空");
        PageInfo<ReplenishmentStatisticsEntry> pageInfo= PageHelper.startPage(vo.getPageNo(), vo.getPageSize()).doSelectPageInfo(() -> {
            if(DateUtil.compareDate(DateUtil.getNowDate(),vo.getStartDate())){
                replenishmentStatisticsMapper.queryLatestList(vo);
            }else{
                replenishmentStatisticsMapper.queryList(vo);
            }
        });
        return pageInfo;
    }


    /**
     * 补货打印
     * @param vo
     * @return
     */
    public String print(ReplenishmentStatisticsVo vo) {
        QYAssert.notNull(vo.getStartDate(),"送货日期不能为空");
        List<ReplenishmentStatisticsEntry> dataList = DateUtil.compareDate(DateUtil.getNowDate(),vo.getStartDate())?
                replenishmentStatisticsMapper.queryLatestList(vo):replenishmentStatisticsMapper.queryList(vo);
        QYAssert.isTrue(SpringUtil.isNotEmpty(dataList),"没有查询到要打印的数据");
        String result = replenishmentStatisticsPdfCreator.create(dataList,vo);
//        try {
            commonPdfPrintService.uploadAndPrintPdf(result,"order_report_TJ_REPLENISHMENT",PrinterDataGroupTypeEnum.STATISTICAL_QUERY_DELIVERY,vo.getUserId());
//            printTaskService.savePrintTask(result, PrinterDataGroupTypeEnum.STATISTICAL_QUERY_DELIVERY, vo.getUserId(), 1, PrinterTypeEnum.LASER.getCode());
//        } catch (Exception e) {
//            return "fail";
//        }
        return "ok";
    }
}

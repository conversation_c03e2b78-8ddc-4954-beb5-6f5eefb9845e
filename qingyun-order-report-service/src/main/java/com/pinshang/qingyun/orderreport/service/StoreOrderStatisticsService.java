package com.pinshang.qingyun.orderreport.service;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.pinshang.qingyun.orderreport.mapper.OrderMapper;
import com.pinshang.qingyun.orderreport.mapper.entry.StoreOrderListEntry;
import com.pinshang.qingyun.orderreport.mapper.entry.StoreOrderStatisticsEntry;
import com.pinshang.qingyun.orderreport.vo.StoreOrderStatisticsVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * Created by zxj on 2021/3/8
 */
@Service
@Slf4j
public class StoreOrderStatisticsService {

    @Autowired
    private OrderMapper orderMapper;

    /**
     * 查询督导客户订货统计
     * @param vo
     * @return
     */
    public StoreOrderStatisticsEntry queryStoreOrderStatistics(StoreOrderStatisticsVo vo){
        StoreOrderStatisticsEntry entry = new StoreOrderStatisticsEntry();
        if(vo.getPageNo()==1){
            entry = orderMapper.queryStoreOrderSum(vo);
        }
        PageInfo<StoreOrderListEntry> pageInfo = PageHelper.startPage(vo.getPageNo(), vo.getPageSize()).doSelectPageInfo(() -> {
            orderMapper.queryStoreOrderStatistics(vo);
        });
        if(pageInfo!=null && CollectionUtils.isNotEmpty(pageInfo.getList())){
            entry.setPageList(pageInfo);
        }
        return entry;
    }

}

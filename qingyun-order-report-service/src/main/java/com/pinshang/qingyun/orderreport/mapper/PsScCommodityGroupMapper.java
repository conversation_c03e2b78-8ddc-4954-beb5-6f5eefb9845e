package com.pinshang.qingyun.orderreport.mapper;

import com.pinshang.qingyun.base.mybatis.MyMapper;
import com.pinshang.qingyun.orderreport.model.PsScCommodityGroup;
import com.pinshang.qingyun.orderreport.vo.ps.*;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface PsScCommodityGroupMapper extends MyMapper<PsScCommodityGroup> {
    List<PsScCommodityGroupVO> queryCommodityGroupList(QueryCommodityGroupVO dto);

    List<PsScCommodityGroupCommodityVO> queryCommodityGroupCommodityList(QueryCommodityGroupCommodityVO dto);

    PsScCommodityGroup queryCommodityGroupByLineId(Long lineId);

    int delByCommodityId(DelCommodityGroupCommodityVO dto);

    PsScCommodityGroup selectByGroupName(String groupName);

    int updateGroupNameByPrimaryKey(@Param("groupId") Long groupId, @Param("groupName") String groupName);
}
package com.pinshang.qingyun.orderreport.mapper.entry;

import com.pinshang.qingyun.box.utils.SpringUtil;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/8/15 17:05
 */
@Data
public class WorkshopShipmentsResultEntry {

    @ApiModelProperty("请以hh:mm后的数据为准   有值就展示")
    private String msg;

    @ApiModelProperty("数据列表")
    private List<WorkshopShipmentsEntry> dataList;

    /**
     * 更新时间
     */
    @ApiModelProperty("更新时间 HH:mm:ss")
    private String updateTime = LocalTime.now().format(DateTimeFormatter.ofPattern("HH:mm:ss"));

    public WorkshopShipmentsResultEntry(String msg, List<WorkshopShipmentsEntry> dataList) {
        this.msg = SpringUtil.isNotEmpty(dataList) ? (StringUtils.isBlank(msg) ? "" : msg) : "";
        this.dataList = dataList;
    }
}

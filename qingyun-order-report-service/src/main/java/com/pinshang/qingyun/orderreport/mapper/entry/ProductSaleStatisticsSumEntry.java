package com.pinshang.qingyun.orderreport.mapper.entry;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.pinshang.qingyun.base.configure.codec.DecimalSerializerKeep2;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 产品销售汇总合计
 */
@Data
public class ProductSaleStatisticsSumEntry {

    @JsonSerialize(using = DecimalSerializerKeep2.class)
    private BigDecimal totalCommodityPrice;

}

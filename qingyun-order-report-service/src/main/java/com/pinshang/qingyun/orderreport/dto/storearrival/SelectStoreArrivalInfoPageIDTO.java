package com.pinshang.qingyun.orderreport.dto.storearrival;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import com.pinshang.qingyun.base.page.Pagination;

/**
 * 分页查询 客户到货信息 列表
 */
@Data
@NoArgsConstructor
public class SelectStoreArrivalInfoPageIDTO extends Pagination {
	private static final long serialVersionUID = 1L;
	@ApiModelProperty(position = 11, value = "送货日期-起始：yyyy-MM-dd")
	private String beginOrderTime;
	@ApiModelProperty(position = 11, value = "送货日期-结束：yyyy-MM-dd")
	private String endOrderTime;
	@ApiModelProperty(position = 12, value = "线路ID")
	private Long lineId;
	@ApiModelProperty(position = 13, value = "客户ID")
	private Long storeId;
	@ApiModelProperty(position = 14, value = "送货员ID")
	private Long deliverymanId;
	@ApiModelProperty(position = 15, value = "客户类型ID")
	private Long storeTypeId;
}

package com.pinshang.qingyun.orderreport.vo.ps;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @Version 0.1.0
 * @Date 2021/1/27 17:15
 * @Copyright © 2019-2021 qm
 * @Description -- 类说明
 **/
@Data
public class AddCommodityGroupLineStoreVO {

    @ApiModelProperty(position = 1, value = "线路id", example = "123456", required = true)
    private Long lineId;

    @ApiModelProperty(position = 2, value = "客户编码，回车键间隔")
    private String storeCodes;

    @ApiModelProperty(hidden = true, value = "操作人")
    private Long createId;
}

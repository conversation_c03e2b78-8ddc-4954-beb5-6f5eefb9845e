package com.pinshang.qingyun.orderreport.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.pinshang.qingyun.base.page.Pagination;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @description 产品销售汇总查询条件
 * @date 2019/09/03
 */
@Data
public class FactoryDeliveryStatisticsReqVo extends Pagination {

    /**送货日期-开始**/
    @JsonFormat(pattern="yyyy-MM-dd",timezone = "GMT+8")
    private Date startDate;

    /**送货日期-结束**/
    @JsonFormat(pattern="yyyy-MM-dd",timezone = "GMT+8")
    private Date endDate;

    /**工厂ID**/
    private Long factoryId;

    /**线路组ID**/
    private Long lineGroupId;

    /**送货员ID**/
    private Long deliverymanId;
}

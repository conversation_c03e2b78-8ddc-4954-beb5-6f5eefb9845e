package com.pinshang.qingyun.orderreport.controller.ps;

import com.github.pagehelper.PageInfo;
import com.pinshang.qingyun.base.api.TokenInfo;
import com.pinshang.qingyun.base.configure.expand.QYAssert;
import com.pinshang.qingyun.base.util.FastThreadLocalUtil;
import com.pinshang.qingyun.orderreport.service.ps.PsScLineService;
import com.pinshang.qingyun.orderreport.vo.ps.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 * @Version 0.1.0
 * @Date 2021/1/18 10:40
 * @Copyright © 2019-2021 qm
 * @Description -- 类说明
 **/
@RestController
@RequestMapping(value = "/ps/line")
@Api(value = "配送-水产-商品组线路api", tags = "commodityGroupLine", description = "配送-水产-商品组线路相关接口")
public class PsScLineController {

    @Autowired
    private PsScLineService psScLineService;


    @ApiOperation(value = "商品组线路列表查询", notes = "商品组线路列表查询")
    @PostMapping("/queryCommodityGroupLineList")
    public PageInfo<PsScLineVO> queryCommodityGroupLineList(@RequestBody QueryCommodityGroupLineVO dto) {
        return psScLineService.queryCommodityGroupLineList(dto);
    }

    @ApiOperation(value = "商品组线路所有列表", notes = "商品组线路所有列表")
    @GetMapping("/queryCommodityGroupLineAllList")
    public List<PsScLineInfoVO> queryCommodityGroupLineAllList() {
        return psScLineService.queryCommodityGroupLineAllList();
    }

    @ApiOperation(value = "商品组下拉列表", notes = "商品组下拉列表查询")
    @PostMapping("/queryCommodityGroupSelectList")
    public List<PsScCommodityGroupSelectVO> queryCommodityGroupSelectList(@RequestBody QueryCommodityGroupSelectVO dto) {
        return psScLineService.queryCommodityGroupSelectList(dto);
    }

    @ApiOperation(value = "线路下的客户列表查询", notes = "线路下的客户列表查询")
    @PostMapping("/queryCommodityGroupLineStoreList")
    public PageInfo<PsScLineStoreVO> queryCommodityGroupLineStoreList(@RequestBody QueryCommodityGroupLineStoreVO dto) {
        QYAssert.notNull(dto.getLineId(), "缺失线路id！");
        return psScLineService.queryCommodityGroupLineStoreList(dto);
    }

    @ApiOperation(value = "新增商品组线路", notes = "新增商品组线路")
    @PostMapping("/addCommodityGroupLine")
    public Boolean addCommodityGroupLine(@RequestBody AddCommodityGroupLineVO dto) {
        TokenInfo tokenInfo = FastThreadLocalUtil.getQY();
        QYAssert.notNull(tokenInfo, "登录过期！");
        dto.setCreateId(tokenInfo.getUserId());
        dto.setUpdateId(tokenInfo.getUserId());
        return psScLineService.addCommodityGroupLine(dto);
    }

    @ApiOperation(value = "修改商品组线路", notes = "修改商品组线路")
    @PostMapping("/modCommodityGroupLine")
    public Boolean modCommodityGroupLine(@RequestBody ModCommodityGroupLineVO dto) {
        TokenInfo tokenInfo = FastThreadLocalUtil.getQY();
        QYAssert.notNull(tokenInfo, "登录过期！");
        dto.setUpdateId(tokenInfo.getUserId());
        return psScLineService.modCommodityGroupLine(dto);
    }

    @ApiOperation(value = "启用/停用商品组线路", notes = "启用/停用商品组线路")
    @PostMapping("/onOffCommodityGroupLine")
    public Boolean onOffCommodityGroupLine(@RequestBody OnOffCommodityGroupLineVO dto) {
        TokenInfo tokenInfo = FastThreadLocalUtil.getQY();
        QYAssert.notNull(tokenInfo, "登录过期！");
        dto.setCreateId(tokenInfo.getUserId());
        return psScLineService.onOffCommodityGroupLine(dto);
    }

    @ApiOperation(value = "线路添加客户", notes = "线路添加客户")
    @PostMapping("/addCommodityGroupLineStore")
    public PsResult addCommodityGroupLineStore(@RequestBody AddCommodityGroupLineStoreVO dto) {
        TokenInfo tokenInfo = FastThreadLocalUtil.getQY();
        QYAssert.notNull(tokenInfo, "登录过期！");
        dto.setCreateId(tokenInfo.getUserId());
        return psScLineService.addCommodityGroupLineStore(dto);
    }

    @ApiOperation(value = "线路删除客户", notes = "线路删除客户")
    @PostMapping("/delCommodityGroupLineStore")
    public Boolean delCommodityGroupLineStore(@RequestBody DelCommodityGroupLineStoreVO dto) {
        TokenInfo tokenInfo = FastThreadLocalUtil.getQY();
        QYAssert.notNull(tokenInfo, "登录过期！");
        dto.setCreateId(tokenInfo.getUserId());
        return psScLineService.delCommodityGroupLineStore(dto);
    }


}

package com.pinshang.qingyun.orderreport.kafka;

import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import lombok.extern.slf4j.Slf4j;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSONObject;
import com.pinshang.qingyun.base.api.QYApplicationContext;
import com.pinshang.qingyun.box.utils.JsonUtil;
import com.pinshang.qingyun.infrastructure.components.IMqSenderComponent;
import com.pinshang.qingyun.infrastructure.components.params.mq.MqMessage;
import com.pinshang.qingyun.kafka.enums.KafkaMessageOperationTypeEnum;
import com.pinshang.qingyun.kafka.enums.KafkaMessageTypeEnum;
import com.pinshang.qingyun.kafka.enums.KafkaTopicEnum;

/**
 * 发送Kafka日志消息
 */
@Slf4j
@Service
public class SendKafkaLogMsgService {

	@Autowired
	private IMqSenderComponent mqSenderComponent;

	/**
	 * 发送日志消息
	 * 
	 * @param jsonObject
	 */
	public void sendLogMsg(JSONObject jsonObject) {
		String data = JsonUtil.java2json(jsonObject);
		try {
			mqSenderComponent.send(QYApplicationContext.applicationNameSwitch
					+ KafkaTopicEnum.LOG_CREATE_TOPIC.getTopic(), data,
					MqMessage.MQ_KAFKA, KafkaMessageTypeEnum.LOG_CREATE.name(),
					KafkaMessageOperationTypeEnum.INSERT.name());
		} catch (Exception e) {
			log.error("发送日志消息-出错 message={}", data, e);
		}
	}

	@Async
	public <T> void sendLogMsg(String tableName, T t) {
		List<T> list = Stream.of(t).collect(Collectors.toList());
		this.sendLogMsg(tableName, list);
	}

	@Async
	public <T> void sendLogMsg(String tableName, List<T> list) {
		JSONObject jsonObject = new JSONObject();
		jsonObject.put("tableName", tableName);
		jsonObject.put("data", list);
		this.sendLogMsg(jsonObject);
	}

}

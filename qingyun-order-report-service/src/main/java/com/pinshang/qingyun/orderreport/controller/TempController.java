package com.pinshang.qingyun.orderreport.controller;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.pinshang.qingyun.orderreport.service.TempService;

/**
 * 临时接口
 */
@Deprecated
@RestController
@RequestMapping("/temp")
public class TempController {

	@Autowired
	private TempService tempService;

	/**
	 * 清理 tj.t_tj_order_sync表 和 tj.t_tj_order_list_sync表
	 * 
	 * @return
	 */
	@Deprecated
	@GetMapping("/removeTJOrderSyncByDate")
	public TempService.DeleteTJOrderSyncByDateResult removeTJOrderSyncByDate(@RequestParam(value = "orderTime", required = false) String orderTime) {
		return tempService.removeTJOrderSyncByDate(orderTime);
	}

}

package com.pinshang.qingyun.orderreport.model;

import lombok.Data;

import javax.persistence.Id;
import javax.persistence.Table;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @description 工厂送货汇总表( 工厂(商品属性) +线路(客户属性)+客户类型(客户属性))
 * @date 2020/9/02
 */
@Data
@Table(name = "t_tj_factory_delivery_statistics")
public class FactoryDeliveryStatistics {

    @Id
    private Long id;

    /** 送货时间 **/
    private Date orderTime;

    /** 工厂Id **/
    private Long factoryId;

    /** 工厂名称 **/
    private String factoryName;

    /** 线路Id **/
    private Long lineId;

    /** 线路名称 **/
    private String lineName;

    /** 线路组ID **/
    private Long lineGroupId;

    /** 送货员ID **/
    private Long deliverymanId;

    /** 送货员名称 **/
    private String deliverymanName;

    /** 数量小计 **/
    private BigDecimal totalQuantity;

    /** 金额小计 **/
    private BigDecimal totalAmount;

    public FactoryDeliveryStatistics(){};

    public FactoryDeliveryStatistics(Date orderTime, Long factoryId, String factoryName, Long lineId, String lineName,
                                     Long lineGroupId, Long deliverymanId, String deliverymanName, BigDecimal totalQuantity, BigDecimal totalAmount){
        this.orderTime = orderTime;
        this.factoryId = factoryId;
        this.factoryName = factoryName;
        this.lineId = lineId;
        this.lineName = lineName;
        this.lineGroupId = lineGroupId;
        this.deliverymanId = deliverymanId;
        this.deliverymanName = deliverymanName;
        this.totalQuantity = totalQuantity;
        this.totalAmount = totalAmount;
    };

}

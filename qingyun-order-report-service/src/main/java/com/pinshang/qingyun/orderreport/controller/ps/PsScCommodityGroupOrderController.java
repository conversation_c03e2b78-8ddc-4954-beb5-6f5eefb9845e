package com.pinshang.qingyun.orderreport.controller.ps;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.write.style.HorizontalCellStyleStrategy;
import com.alibaba.excel.write.style.column.SimpleColumnWidthStyleStrategy;
import com.alibaba.excel.write.style.row.SimpleRowHeightStyleStrategy;
import com.github.pagehelper.PageInfo;
import com.pinshang.qingyun.base.api.TokenInfo;
import com.pinshang.qingyun.base.configure.expand.QYAssert;
import com.pinshang.qingyun.base.util.ExcelUtil;
import com.pinshang.qingyun.base.util.FastThreadLocalUtil;
import com.pinshang.qingyun.orderreport.dto.*;
import com.pinshang.qingyun.orderreport.pdf.CommodityGroupSheetHandler;
import com.pinshang.qingyun.orderreport.pdf.EasyExcelUtils;
import com.pinshang.qingyun.orderreport.service.ps.PsScCommodityGroupOrderService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.joda.time.LocalDate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.propertyeditors.CustomDateEditor;
import org.springframework.web.bind.WebDataBinder;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.Date;

/**
 * 商品组配送单打印
 */
@Slf4j
@RestController
@RequestMapping(value = "/ps/commodityGroupOrder")
@Api(value = "商品组配送单", tags = "commodityGroupOrder", description = "商品组配送单打印相关接口")
public class PsScCommodityGroupOrderController {

    @Autowired
    private PsScCommodityGroupOrderService commodityGroupOrderService;


    @ApiOperation(value = "商品组配送单列表", notes = "商品组配送单列表")
    @PostMapping("/queryCommodityGroupOrderList")
    public PageInfo<CommodityGroupLineOrderODTO> queryCommodityGroupOrderList(@RequestBody CommodityGroupLineOrderIDTO vo) {
        return commodityGroupOrderService.queryCommodityGroupOrderList(vo);
    }

    @ApiOperation(value = "打印预览", notes = "打印预览")
    @RequestMapping(value = "/preview",method = RequestMethod.POST)
    public CommodityGroupLinePreviewODTO preview(@RequestBody CommodityGroupLinePrintIDTO vo) {
        QYAssert.notEmpty(vo.getLineIdList(),"请求参数lineId不能为空");
        return commodityGroupOrderService.preview(vo.getLineIdList().get(0),vo.getOrderTime());
    }

    @ApiOperation(value = "打印", notes = "打印")
    @RequestMapping(value = "/print",method = RequestMethod.POST)
    public Boolean print(@RequestBody CommodityGroupLinePrintIDTO vo) {
        TokenInfo info = FastThreadLocalUtil.getQY();
        vo.setUserId(info.getUserId());
        return commodityGroupOrderService.print(vo);
    }

    @ApiOperation(value = "导出", notes = "导出商品组配送单")
    @RequestMapping(value = "/export", method = RequestMethod.GET)
    public void export( CommodityGroupLinePrintIDTO vo,HttpServletResponse response) throws IOException {
        CommodityGroupLinePreviewODTO previewODTO = this.preview(vo);
        try {
            ExcelUtil.setFileNameAndHead(response, "商品组配送单" + LocalDate.now().toString("yyyyMMdd"));
            EasyExcel.write(response.getOutputStream())
                    .head(commodityGroupOrderService.processExportHeader(previewODTO.getTableHeader()))
                    .autoCloseStream(Boolean.FALSE)
                    .registerWriteHandler(new SimpleColumnWidthStyleStrategy(20))
                    .registerWriteHandler(new SimpleRowHeightStyleStrategy((short)40,(short)20))
                    .registerWriteHandler(new CommodityGroupSheetHandler(previewODTO))
                    .registerWriteHandler(new HorizontalCellStyleStrategy(EasyExcelUtils.getHeadStyle(),EasyExcelUtils.getContentStyle()))
                    .useDefaultStyle(true).relativeHeadRowIndex(4)
                    .sheet("商品组配送单")
                    .doWrite(previewODTO.getTableData());
        }catch (Exception e){
            log.error("商品组配送单导出错误", e);
            ExcelUtil.setExceptionResponse( response );
        }
    }

    @InitBinder
    public void initData(WebDataBinder wdb){
        wdb.registerCustomEditor(Date.class, new CustomDateEditor(new SimpleDateFormat("yyyy-MM-dd"), true));
    }

}

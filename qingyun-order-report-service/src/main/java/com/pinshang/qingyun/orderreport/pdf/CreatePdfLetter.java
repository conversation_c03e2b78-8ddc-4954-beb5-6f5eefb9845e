package com.pinshang.qingyun.orderreport.pdf;

import com.itextpdf.text.*;
import com.itextpdf.text.pdf.BaseFont;
import com.itextpdf.text.pdf.PdfPCell;
import com.itextpdf.text.pdf.PdfPTable;
import com.itextpdf.text.pdf.PdfWriter;
import com.pinshang.qingyun.box.utils.TimeUtil;
//import com.pinshang.qingyun.common.service.PrintClient;
import com.pinshang.qingyun.orderreport.config.CustomerProperties;
import com.pinshang.qingyun.orderreport.mapper.entry.DeliveryOrderItemPrintEntry;
import com.pinshang.qingyun.orderreport.mapper.entry.DeliveryOrderPrintEntry;
import com.pinshang.qingyun.orderreport.util.PdfUtils;
import com.pinshang.qingyun.print.service.PrinterTaskClient;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.nio.ByteBuffer;
import java.nio.charset.Charset;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

/**
 * @ClassName Test
 * @Description TODO()
 * <AUTHOR>
 * @Date 2016年10月20日 下午5:22:34
 * @version 1.0.0
 */
@Component
public class CreatePdfLetter {

	private static Logger logger = LoggerFactory.getLogger(CreatePdfLetter.class);
	
	@Autowired
	private CustomerProperties customerProperties;
//	@Autowired
//	private PrintClient printClient;
	@Autowired
    private PrinterTaskClient printerTaskClient;
	private static String filePathDir;

	@PostConstruct
	public void init() {
		filePathDir = customerProperties.getAbsoluteSavePath();
	}
	public static void main(String[] args) {


//		System.out.println(getVal(2.00));

		DeliveryOrderPrintEntry op = new DeliveryOrderPrintEntry();
		op.setDeliverymanName("杨青华(106宁波)");
		op.setMobile("67791026");
		op.setPrintNumber(1);
		op.setOrderNo("1558658144551900754");
		op.setStoreCode("3083339");
		op.setStoreName("高后_工技大松江校区一食堂 菜一");
		op.setShopAddress("松江区龙腾路333号");
		op.setOrderTime(new Date());
		op.setLocal("001");
		op.setId(111L);
		op.setTestReport(1);
		op.setSuperintendMobile("13817175364");
		op.setRegionalManager("朱仲华(103无锡)");
		op.setSuperintend("朱仲华(103无锡)");
		op.setCreateName("（上海大仓）华电通用轻型燃机设备有限公司（索迪斯）早餐类迪斯）早餐类");
		op.setLineGroupName("苏州直送晚上发货组");
		op.setShowPrice(0);
		op.setPayMoney(new BigDecimal(30));
		List<DeliveryOrderItemPrintEntry> orderPrintItems = new ArrayList<>();
		for (int i = 0; i < 50; i++) {
			DeliveryOrderItemPrintEntry oi = new DeliveryOrderItemPrintEntry();
			oi.setNumber(new BigDecimal(1));
			oi.setMadeDate("见包装");
			oi.setProductName("蛋黄肉粽(140g*30)");
			oi.setProductRemark("");
			oi.setUnitPrice(new BigDecimal(112.3));
			oi.setMoney(new BigDecimal(112.3));
			orderPrintItems.add(oi);
		}
		op.setOrderPrintItems(orderPrintItems);
		// 创建一个Document对象
		Document document = new Document(PageSize.LETTER);
		List<String> filePaths = new ArrayList<>();
		String filePath = "d:\\pdf\\a.pdf";
		filePaths.add(filePath);
//		CreatePdfB5.execCreatePdfByOrder( op, 42, "11-25");
	}
	/**
	 * @Description (TODO)
	 * @param orderPrint
	 * @param size
	 *            void
	 * <AUTHOR>
	 * @Date 2016年10月21日 下午3:02:38
	 */
	public int execCreatePdfByOrder(Document document,DeliveryOrderPrintEntry orderPrint, int size, String autoRemark) {
		List<DeliveryOrderItemPrintEntry> orderPrintItems = orderPrint.getOrderPrintItems();
		int nowPageIndex = 0;
		int rowChar =42;
		if (orderPrint.getShowPrice() == 1) {
			rowChar = 62;
		}
		List<int[]> pageArray = new ArrayList<>();
		int pageBeginIndex=0;
		int k = 0;
		int state=0;
		int nowPageSize = size;
		for (; k < orderPrintItems.size(); k++) {
			DeliveryOrderItemPrintEntry orderPrintItem = orderPrintItems.get(k);
			Charset charset = Charset.forName("UTF-8");
			ByteBuffer buffer = charset.encode(orderPrintItem.getProductName());
			int rowCount = buffer.remaining()/rowChar;
			if(buffer.remaining()/rowChar!=0){
				state+=rowCount;
				if(state/3 >0){
					nowPageSize += state / 3;
					state=0;
				}
			}
			rowCount++;
			if(nowPageIndex > nowPageSize || (nowPageIndex+rowCount) > nowPageSize){
				// 当前行商品名称大于size 或者 当前页行数+当前行数 大于size
				pageArray.add(new int[]{pageBeginIndex,k});
				//if(orderPrintItems.size()-1 != k) {
				//	k++;
				//}
				pageBeginIndex = k;
				nowPageIndex=0;
				nowPageSize = size;
			}
			nowPageIndex+=rowCount;
		}
		if(pageBeginIndex != k && pageBeginIndex < k ) {
			pageArray.add(new int[]{pageBeginIndex, k});
		}
		int pageIndex=0;
		for (int i = 0; i < pageArray.size(); i++) {
			int[] ints = pageArray.get(i);
			int beginIndex = ints[0];
			//if(i > 0){
			//	--beginIndex;
			//}
			List<DeliveryOrderItemPrintEntry> orderPrintItemList = orderPrintItems.subList(beginIndex,ints[1]);

			pageIndex = createPdf(document,orderPrint, pageIndex, pageArray.size(),i+1,
					pageArray.size()==i+1,autoRemark,orderPrintItemList);
			document.newPage();
		}
		return pageArray.size();
	}

	/**
	 * @Description (TODO)
	 * @param orderPrint
	 * @param orderPrintItems
	 * @param index
	 * @param pageSize
	 * @param pageNo
	 *            void
	 * <AUTHOR>
	 * @Date 2016年10月21日 下午2:49:31
	 */
	private  int createPdf(Document document, DeliveryOrderPrintEntry orderPrint,
						   int index, int pageSize, int pageNo,boolean viewPrice,  String autoRemark, List<DeliveryOrderItemPrintEntry> orderPrintItems) {
		// Rectangle rectangle = new Rectangle(100,160);
		// 创建一个Document对象
		try {
			// 生成名为 AsianTest.pdf 的文档
			/**
			 * 新建一个字体,iText的方法 STSongStd-Light 是字体，在iTextAsian.jar 中以property为后缀
			 * UniGB-UCS2-H 是编码，在iTextAsian.jar 中以cmap为后缀 H 代表文字版式是 横版， 相应的 V
			 * 代表竖版
			 */
			// BaseFont bfChinese =
			// BaseFont.createFont("C:\\Windows\\Fonts\\simsun.ttc,1",
			// BaseFont.IDENTITY_H, BaseFont.NOT_EMBEDDED);
//			BaseFont bfChinese = BaseFont.createFont("STSong-Light", "UniGB-UCS2-H", BaseFont.NOT_EMBEDDED);
			// BaseFont bfChinese = BaseFont.createFont("STSongStd-Light",
			// "UniGB-UCS2-H", BaseFont.NOT_EMBEDDED);
			// BaseFont bfChinese = BaseFont.createFont("STSongStd-Light",
			// "UniGB-UCS2-H", BaseFont.NOT_EMBEDDED);
			// 设置字体
			Font fontChinese = PdfUtils.chineseSTFANGSOFont( 9, Font.NORMAL);// 创建字体，设置family，size，style,还可以设置color
			// 打开文档，将要写入内容
			/**
			 * 头信息
			 */
//			Paragraph paragraph = new Paragraph(orderPrint.getCompanyName(),fontChinese);
//			paragraph.setAlignment(Element.ALIGN_CENTER);
//			document.add(paragraph);
			setHead(document, fontChinese, fontChinese, pageNo, pageSize, orderPrint);
			float[] widths = { 2f, 17f, 4f, 4f, 5f, 5f};// 设置表格的列宽和列数
			if(orderPrint.getShowPrice()==1){
				widths= new float[]{ 2f, 27f, 4f, 5f};// 设置表格的列宽和列数
			}
//			float[] widths = { 2f, 13f, 4f, 4f, 5f, 5f, 4f };// 设置表格的列宽和列数
//			if(orderPrint.getShowPrice()==1){
//				widths= new float[]{ 2f, 21f, 4f, 5f, 6f};// 设置表格的列宽和列数
//			}
																// 默认是4列
			PdfPTable table = new PdfPTable(widths);// 建立一个pdf表格
			table.setHorizontalAlignment(Element.ALIGN_LEFT);
			table.setWidthPercentage(width);// 设置表格宽度为100%
			setContextHead(fontChinese, table, fontChinese,orderPrint.getShowPrice());
			index = setContext(fontChinese, table,  index, orderPrint,
					viewPrice,orderPrintItems);
			document.add(table);
			setButtom(document, fontChinese, fontChinese, orderPrint);
		} catch (DocumentException de) {
			System.err.println(de.getMessage());
		} catch (IOException ioe) {
			System.err.println(ioe.getMessage());
		}
		return index;
	}

	/**
	 * @Description (头信息)
	 * @param fontChinese
	 * @param table
	 *            void
	 * <AUTHOR>
	 * @Date 2016年10月21日 上午11:17:51
	 */
	private static void setContextHead(Font fontChinese, PdfPTable table, Font fontChineseRemark,int show) {
		// table.setSpacingBefore(20f);
		PdfPCell cell = new PdfPCell(new Paragraph(" ", fontChinese));//
		cell.setHorizontalAlignment(Element.ALIGN_CENTER);
		table.addCell(cell);
		cell = new PdfPCell(new Paragraph("单位|品名规格", fontChinese));//
		cell.setHorizontalAlignment(Element.ALIGN_CENTER);
		table.addCell(cell);
		cell = new PdfPCell(new Paragraph("数量", fontChinese));//
		cell.setHorizontalAlignment(Element.ALIGN_CENTER);
		table.addCell(cell);
		if(show==0){
			cell = new PdfPCell(new Paragraph("单价", fontChinese));//
			cell.setHorizontalAlignment(Element.ALIGN_CENTER);
			table.addCell(cell);
			cell = new PdfPCell(new Paragraph("金额", fontChinese));//
			cell.setHorizontalAlignment(Element.ALIGN_CENTER);
			table.addCell(cell);
		}
		cell = new PdfPCell(new Paragraph("生产日期", fontChinese));//
		cell.setHorizontalAlignment(Element.ALIGN_CENTER);
		table.addCell(cell);
//		cell = new PdfPCell(new Paragraph("备注", fontChineseRemark));//
//		cell.setHorizontalAlignment(Element.ALIGN_CENTER);
//		table.addCell(cell);
	}

	/**
	 * @Description (内容)
	 * @param fontChinese
	 * @param table
	 *            void
	 * <AUTHOR>
	 * @Date 2016年10月21日 上午11:17:51
	 */
	private int setContext(Font fontChinese, PdfPTable table, int index,
						   DeliveryOrderPrintEntry orderPrint, boolean viewPrice, List<DeliveryOrderItemPrintEntry> orderPrintItems) {
		for (DeliveryOrderItemPrintEntry orderPrintItem : orderPrintItems) {
			index++;
			PdfPCell cell = new PdfPCell(new Paragraph("" + index, fontChinese));// 下标
			cell.setHorizontalAlignment(Element.ALIGN_CENTER);
			cell.setVerticalAlignment(Element.ALIGN_MIDDLE);
			// cell.setFixedHeight(24f);
			table.addCell(cell);
			String productName = orderPrintItem.getProductName();
			cell = new PdfPCell(new Paragraph(productName, fontChinese));// 品名规格
			cell.setHorizontalAlignment(Element.ALIGN_LEFT);
			cell.setVerticalAlignment(Element.ALIGN_MIDDLE);
			//cell.setFixedHeight(12f);
			table.addCell(cell);
			cell = new PdfPCell(new Paragraph(getVal(orderPrintItem.getNumber().doubleValue()), fontChinese));// 数量
			cell.setHorizontalAlignment(Element.ALIGN_CENTER);
			cell.setVerticalAlignment(Element.ALIGN_MIDDLE);
			table.addCell(cell);
			if(orderPrint.getShowPrice()==0){
				cell = new PdfPCell(new Paragraph(getVal(orderPrintItem.getUnitPrice().setScale(2, RoundingMode.UP).doubleValue()), fontChinese));// 单价
				cell.setHorizontalAlignment(Element.ALIGN_CENTER);
				cell.setVerticalAlignment(Element.ALIGN_MIDDLE);
				//cell.setFixedHeight(12f);
				table.addCell(cell);
				cell = new PdfPCell(new Paragraph(getVal(orderPrintItem.getMoney().doubleValue()), fontChinese));// 金额
				cell.setHorizontalAlignment(Element.ALIGN_CENTER);
				cell.setVerticalAlignment(Element.ALIGN_MIDDLE);
				//cell.setFixedHeight(12f);
				table.addCell(cell);
			}
			String made="见包装";
			if("散装".equals(orderPrintItem.getMadeDate()) || orderPrint.isMadeDate()){
				Calendar calendar  = Calendar.getInstance();
				calendar.setTime(orderPrint.getOrderTime());
				calendar.add(Calendar.DATE, -1);
				made=TimeUtil.toString(calendar.getTime(), "MM-dd");
			}
			cell = new PdfPCell(new Paragraph(made, fontChinese));// 生成日期
			cell.setHorizontalAlignment(Element.ALIGN_CENTER);
			cell.setVerticalAlignment(Element.ALIGN_MIDDLE);
			//cell.setFixedHeight(12f);
			table.addCell(cell);
//			String remark = orderPrintItem.getProductRemark() == null ? "" : orderPrintItem.getProductRemark();
//			cell = new PdfPCell(new Paragraph(remark, fontChinese));// 备注
//			cell.setHorizontalAlignment(Element.ALIGN_CENTER);
//			cell.setVerticalAlignment(Element.ALIGN_MIDDLE);
//			cell.setFixedHeight(12f);
//			table.addCell(cell);
		}
		if(orderPrint.getShowPrice()==0 && viewPrice){
			PdfPCell cell = new PdfPCell(new Paragraph("", fontChinese));// 下标
			cell.setHorizontalAlignment(Element.ALIGN_CENTER);
			table.addCell(cell);
			cell = new PdfPCell(new Paragraph("商品金额小计", fontChinese));// 品名规格
			cell.setHorizontalAlignment(Element.ALIGN_LEFT);
			table.addCell(cell);
			cell = new PdfPCell(new Paragraph("", fontChinese));// 数量
			cell.setHorizontalAlignment(Element.ALIGN_CENTER);
			table.addCell(cell);
			cell = new PdfPCell(new Paragraph("", fontChinese));// 单价
			cell.setHorizontalAlignment(Element.ALIGN_CENTER);
			table.addCell(cell);
			cell = new PdfPCell(new Paragraph(getVal(orderPrint.getPayMoney().doubleValue()), fontChinese));// 金额
			cell.setHorizontalAlignment(Element.ALIGN_CENTER);
			table.addCell(cell);
			cell = new PdfPCell(new Paragraph("", fontChinese));// 生成日期
			cell.setHorizontalAlignment(Element.ALIGN_CENTER);
			table.addCell(cell);
//			cell = new PdfPCell(new Paragraph("", fontChineseRemark));// 备注
//			cell.setHorizontalAlignment(Element.ALIGN_CENTER);
//			table.addCell(cell);

			cell = new PdfPCell(new Paragraph("", fontChinese));// 下标
			cell.setHorizontalAlignment(Element.ALIGN_CENTER);
			table.addCell(cell);
			cell = new PdfPCell(new Paragraph("配送费小计", fontChinese));// 品名规格
			cell.setHorizontalAlignment(Element.ALIGN_LEFT);
			table.addCell(cell);
			cell = new PdfPCell(new Paragraph("", fontChinese));// 数量
			cell.setHorizontalAlignment(Element.ALIGN_CENTER);
			table.addCell(cell);
			cell = new PdfPCell(new Paragraph("", fontChinese));// 单价
			cell.setHorizontalAlignment(Element.ALIGN_CENTER);
			table.addCell(cell);
			cell = new PdfPCell(new Paragraph(getVal(orderPrint.getDeliveryFees().doubleValue()), fontChinese));// 金额
			cell.setHorizontalAlignment(Element.ALIGN_CENTER);
			table.addCell(cell);
			cell = new PdfPCell(new Paragraph("", fontChinese));// 生成日期
			cell.setHorizontalAlignment(Element.ALIGN_CENTER);
			table.addCell(cell);
//			cell = new PdfPCell(new Paragraph("", fontChineseRemark));// 备注
//			cell.setHorizontalAlignment(Element.ALIGN_CENTER);
//			table.addCell(cell);


			cell = new PdfPCell(new Paragraph("", fontChinese));// 下标
			cell.setHorizontalAlignment(Element.ALIGN_CENTER);
			table.addCell(cell);
			cell = new PdfPCell(new Paragraph("合计", fontChinese));// 品名规格
			cell.setHorizontalAlignment(Element.ALIGN_LEFT);
			table.addCell(cell);
			cell = new PdfPCell(new Paragraph("", fontChinese));// 数量
			cell.setHorizontalAlignment(Element.ALIGN_CENTER);
			table.addCell(cell);
			cell = new PdfPCell(new Paragraph("", fontChinese));// 单价
			cell.setHorizontalAlignment(Element.ALIGN_CENTER);
			table.addCell(cell);
			cell = new PdfPCell(new Paragraph(getVal(orderPrint.getTotalAmount().doubleValue()), fontChinese));// 金额
			cell.setHorizontalAlignment(Element.ALIGN_CENTER);
			table.addCell(cell);
			cell = new PdfPCell(new Paragraph("", fontChinese));// 生成日期
			cell.setHorizontalAlignment(Element.ALIGN_CENTER);
			table.addCell(cell);
//			cell = new PdfPCell(new Paragraph("", fontChineseRemark));// 备注
//			cell.setHorizontalAlignment(Element.ALIGN_CENTER);
//			table.addCell(cell);
		}
		return index;
	}
	
	  public static BigDecimal setScaleRoundDown(int newScale, BigDecimal b) {
	        return b.setScale(newScale, BigDecimal.ROUND_DOWN);
	 }

	
	public static String getVal(Double b){
		if(b==null || b==0){
		logger.error("b   is null");
			return "0";
		}
//		if (b % 1 == 0) {
//			return b.intValue()+"";
//		} else {
//			BigDecimal bg = new BigDecimal(b);
//	        return bg.setScale(2, BigDecimal.ROUND_DOWN).toString();
//		}
		String val = b.toString();
		if(val.endsWith(".00")){
			val=val.substring(0, val.indexOf("."));
		}else if(val.endsWith(".0")){
			val=val.substring(0, val.indexOf("."));
		}
		return val;
	}
	static float width = 62f;

	/**
	 * @Description (TODO)
	 * @param document
	 * @param fontChinese
	 * @param subBoldFontChinese
	 * @throws DocumentException
	 *             void
	 * <AUTHOR>
	 * @Date 2016年10月21日 上午11:12:27
	 */
	private  void setHead(Document document, Font fontChinese, Font subBoldFontChinese, int pageNo, int pageSize,
								DeliveryOrderPrintEntry orderPrint) throws DocumentException {


		String codeStr ="";
		List<String> printBatchNo = printerTaskClient.getPrintBatchNo("GBcstndCsaw4LmteAfwz",orderPrint.getCompanyStartsWith(), 1);
		if(printBatchNo!=null && !printBatchNo.isEmpty()){
			codeStr= printBatchNo.get(0);
		}



		float[] widths2 = { 15f, 36f, 15f, 25f };// 设置表格的列宽和列数 默认是4列
		PdfPTable table2 = new PdfPTable(widths2);// 建立一个pdf表格
		table2.setWidthPercentage(width);// 设置表格宽度为100%
		table2.setHorizontalAlignment(Element.ALIGN_LEFT);
		table2.setWidthPercentage(width);// 设置表格宽度为100%
		table2.setHorizontalAlignment(Element.ALIGN_LEFT);

		BaseFont bfChinese = PdfUtils.chineseBaseFont();
		Font numberFont = new Font(bfChinese, 11);
		PdfPCell cell = new PdfPCell(new Paragraph(codeStr, numberFont));//
		cell.setColspan(4);
		cell.setBorder(0);
		cell.setHorizontalAlignment(Element.ALIGN_RIGHT);
		cell.setPaddingLeft(47);
		table2.addCell(cell);


		Font companyName = new Font(bfChinese, 13, Font.BOLD);
		cell = new PdfPCell(new Paragraph(orderPrint.getCompanyName(), companyName));//
		cell.setColspan(4);
		cell.setBorder(0);
		cell.setPaddingLeft(47);
		cell.setHorizontalAlignment(Element.ALIGN_LEFT);
		table2.addCell(cell);

		cell = new PdfPCell(new Paragraph(orderPrint.getLocal(), subBoldFontChinese));//
		cell.setColspan(2);
		cell.setBorder(0);
		cell.setHorizontalAlignment(Element.ALIGN_LEFT);
		table2.addCell(cell);
		String testRepot= "";
		if(orderPrint.getTestReport()==0){
			testRepot="(化验)　";
		}
		cell = new PdfPCell(new Paragraph(testRepot+"第" + pageNo + "页 共" + pageSize + "页", fontChinese));//
		cell.setColspan(2);
		cell.setBorder(0);
		cell.setHorizontalAlignment(Element.ALIGN_RIGHT);
		table2.addCell(cell);

		cell = new PdfPCell(new Paragraph("客户编号：", subBoldFontChinese));//
		cell.setHorizontalAlignment(Element.ALIGN_CENTER);
		cell.setBorder(0);
		table2.addCell(cell);
		cell = new PdfPCell(new Paragraph(orderPrint.getStoreCode(), fontChinese));//
		cell.setBorder(0);
		table2.addCell(cell);
		cell = new PdfPCell(new Paragraph("订单编号：", subBoldFontChinese));//
		cell.setBorder(0);
		cell.setHorizontalAlignment(Element.ALIGN_CENTER);
		table2.addCell(cell);
		cell = new PdfPCell(new Paragraph(orderPrint.getOrderNo(), fontChinese));//
		cell.setBorder(0);
		table2.addCell(cell);

		cell = new PdfPCell(new Paragraph("付款单位：", subBoldFontChinese));//
		cell.setHorizontalAlignment(Element.ALIGN_CENTER);
		cell.setBorder(0);
		table2.addCell(cell);
		cell = new PdfPCell(new Paragraph(orderPrint.getStoreName(), fontChinese));//
		cell.setBorder(0);
		cell.setFixedHeight(12L);
		table2.addCell(cell);
		cell = new PdfPCell(new Paragraph("销售日期：", subBoldFontChinese));//
		cell.setBorder(0);
		cell.setHorizontalAlignment(Element.ALIGN_CENTER);
		table2.addCell(cell);
		cell = new PdfPCell(
				new Paragraph(TimeUtil.parseSimpleDateTime(orderPrint.getOrderTime()), fontChinese));//
		cell.setBorder(0);
		table2.addCell(cell);

		cell = new PdfPCell(new Paragraph("送货地址：", subBoldFontChinese));//
		cell.setHorizontalAlignment(Element.ALIGN_CENTER);
		cell.setBorder(0);
		table2.addCell(cell);
		cell = new PdfPCell(new Paragraph(orderPrint.getShopAddress(), fontChinese));//
		cell.setBorder(0);
		table2.addCell(cell);
		cell = new PdfPCell(new Paragraph("联系电话：", subBoldFontChinese));//
		cell.setBorder(0);
		cell.setHorizontalAlignment(Element.ALIGN_CENTER);
		table2.addCell(cell);
		cell = new PdfPCell(new Paragraph(orderPrint.getMobile(), fontChinese));//
		cell.setBorder(0);
		table2.addCell(cell);
		document.add(table2);
	}

	private static void setButtom(Document document, Font fontChinese, Font subBoldFontChinese, DeliveryOrderPrintEntry orderPrint)
			throws DocumentException {
		float[] widths2 = { 30f, 36f };// 设置表格的列宽和列数 默认是4列
		PdfPTable table2 = new PdfPTable(widths2);// 建立一个pdf表格
		table2.setHorizontalAlignment(Element.ALIGN_LEFT);
		table2.setWidthPercentage(width);// 设置表格宽度为100%
		String dDeliverymanName =orderPrint.getDeliverymanName();
		String lineGroupName = orderPrint.getLineGroupName() == null ? "" : orderPrint.getLineGroupName();
		PdfPCell cell = new PdfPCell(new Paragraph("线路组：" + lineGroupName, fontChinese));//
		cell.setBorder(0);
		cell.setFixedHeight(12f);
		table2.addCell(cell);

		String manger = orderPrint.getRegionalManager() == null ? "" : orderPrint.getRegionalManager();
		cell = new PdfPCell(new Paragraph("区域经理：" + manger+orderPrint.getRegionalManagerMobile(), fontChinese));//
		cell.setBorder(0);
//		cell.setFixedHeight(12f);
		table2.addCell(cell);
		String createName=orderPrint.getCreateName() == null ? orderPrint.getStoreCode() : orderPrint.getCreateName();

		cell = new PdfPCell(new Paragraph("操作员：" +createName, fontChinese));//
		cell.setBorder(0);
		cell.setFixedHeight(12f);
		table2.addCell(cell);

		cell = new PdfPCell(new Paragraph("督导：" +  orderPrint.getSuperintend()+orderPrint.getSuperintendMobile(), fontChinese));//
		cell.setBorder(0);
		table2.addCell(cell);



		cell = new PdfPCell(new Paragraph("打印日期：" +TimeUtil.toString(new Date(),null), fontChinese));//
		cell.setBorder(0);
		cell.setFixedHeight(12f);
		table2.addCell(cell);
		cell = new PdfPCell(new Paragraph("送货员：" + dDeliverymanName+orderPrint.getDeliverymanMobile(), fontChinese));//
		cell.setBorder(0);
//		cell.setFixedHeight(12f);
		table2.addCell(cell);

		cell = new PdfPCell(new Paragraph("送货时间："+orderPrint.getReceiveTime() , fontChinese));//
		cell.setBorder(0);
		table2.addCell(cell);
		cell = new PdfPCell(new Paragraph("签收人：", fontChinese));//
		cell.setBorder(0);
		table2.addCell(cell);


		String remark=orderPrint.getRemark();
		if(null==remark){
			remark="";
		}
//		if(orderPrint.getStoreIsPrepay()==0){
//			remark+= "  账户余额：" + getVal(orderPrint.getResidualAmount());//
//		}
		cell = new PdfPCell(new Paragraph("备注："+remark, fontChinese));//
		cell.setColspan(2);
		cell.setBorder(0);
		table2.addCell(cell);
		document.add(table2);
	}
	
}

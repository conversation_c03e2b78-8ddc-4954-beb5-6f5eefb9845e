package com.pinshang.qingyun.orderreport.controller;

import com.pinshang.qingyun.orderreport.service.StatisticalOrderService;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @date 2019/3/6 16:38.
 */
@RestController
@RequestMapping("/statistical/order")
public class StatisticalOrderController {

    private final StatisticalOrderService statisticalOrderService;

    public StatisticalOrderController(StatisticalOrderService statisticalOrderService) {
        this.statisticalOrderService = statisticalOrderService;
    }

    @RequestMapping(value = "discardTestUserOrder", method = RequestMethod.GET)
    public boolean discardTestUserOrder() {
        return statisticalOrderService.discardTestUserOrder();
    }
}

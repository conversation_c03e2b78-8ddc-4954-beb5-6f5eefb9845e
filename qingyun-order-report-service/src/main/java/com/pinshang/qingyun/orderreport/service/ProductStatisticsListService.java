package com.pinshang.qingyun.orderreport.service;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.pinshang.qingyun.base.configure.expand.QYAssert;
import com.pinshang.qingyun.base.enums.PrinterDataGroupTypeEnum;
import com.pinshang.qingyun.box.utils.DateUtil;
import com.pinshang.qingyun.box.utils.SpringUtil;
import com.pinshang.qingyun.orderreport.mapper.ProductStatisticsListMapper;
import com.pinshang.qingyun.orderreport.mapper.entry.ProductStatisticsListEntry;
import com.pinshang.qingyun.orderreport.mapper.entry.ProductStatisticsListSumEntry;
import com.pinshang.qingyun.orderreport.pdf.ProductStatisticsListPdfCreator;
import com.pinshang.qingyun.orderreport.vo.ProductStatisticsListVo;
//import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 产品统计清单
 */
@Service
//@Slf4j
public class ProductStatisticsListService {

    @Autowired
    private ProductStatisticsListMapper productStatisticsListMapper;
    @Autowired
    private ProductStatisticsListPdfCreator productStatisticsListPdfCreator;
    @Autowired
    private PrintTaskService printTaskService;
    @Autowired
    private CommonPdfPrintService commonPdfPrintService;
    /**
     * 产品统计清单列表（根据指定送货日期和商品，查询哪些客户预定了这些商品，以及商品的总数量和金额）
     * @param vo
     * @return
     */
    public PageInfo<ProductStatisticsListEntry> queryList(ProductStatisticsListVo vo) {
        checkParam(vo);
        return PageHelper.startPage(vo.getPageNo(), vo.getPageSize()).doSelectPageInfo(() -> {
            if(DateUtil.compareDate(DateUtil.getNowDate(),vo.getOrderTime())){
                productStatisticsListMapper.queryLatestList(vo);
            }else{
                productStatisticsListMapper.queryList(vo);
            }
        });
    }

    /**
     * 查询数量合计、金额合计
     * @param vo
     * @return
     */
    public ProductStatisticsListSumEntry querySum(ProductStatisticsListVo vo) {
        checkParam(vo);
        return DateUtil.compareDate(DateUtil.getNowDate(),vo.getOrderTime())?
                productStatisticsListMapper.queryLatestSum(vo) : productStatisticsListMapper.querySum(vo);
    }

    /**
     * 产品统计清单打印
     * @param vo
     * @return
     */
    public String print(ProductStatisticsListVo vo) {
        checkParam(vo);
        List<ProductStatisticsListEntry> dataList = DateUtil.compareDate(DateUtil.getNowDate(),vo.getOrderTime())?
                productStatisticsListMapper.queryLatestList(vo):productStatisticsListMapper.queryList(vo);
        QYAssert.isTrue(SpringUtil.isNotEmpty(dataList),"没有查询到要打印的数据");
        String result = productStatisticsListPdfCreator.create(dataList,vo);
//        try {
            commonPdfPrintService.uploadAndPrintPdf(result,"order_report_TJ_PRODUCT",PrinterDataGroupTypeEnum.STATISTICAL_QUERY_DELIVERY,vo.getUserId());
//          printTaskService.savePrintTask(result, PrinterDataGroupTypeEnum.STATISTICAL_QUERY_DELIVERY, vo.getUserId(), 1, PrinterTypeEnum.LASER.getCode());
//        } catch (Exception e) {
//            return "fail";
//        }
        return "ok";
    }

    private void checkParam(ProductStatisticsListVo vo){
        QYAssert.notNull(vo.getCommodityId(),"商品不能为空");
        QYAssert.notNull(vo.getOrderTime(),"送货日期不能为空");
    }
}

package com.pinshang.qingyun.orderreport.mapper.entry;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @author: chen<PERSON><PERSON>
 * @time: 2021/9/16 17:21
 */
@Data
@NoArgsConstructor
public class DeliveryNoteOrderEntry {
	
	@JsonSerialize(using = ToStringSerializer.class)
	@ApiModelProperty(position = 0, required = true, value = "客户ID", hidden = true)
	private Long storeId;

    private String storeName;

    private String storeCode;

    /**
     * 送货地址
     */
    private String deliveryAddress;

    /**
     * 联系人
     */
    private String storeLinkman;

    private String linkmanMobile;

    /**
     * 线路组名称
     */
    private String lineName;

    /**
     * 发货时间
     */
    private String deliveryTime;


    /**
     * 是否有图片
     */
    private Boolean isTruePic;

    /**
     * 距离
     */
    @ApiModelProperty(value = "距离")
    private String distanceStr;
    @ApiModelProperty(value = "距离，临时变量", hidden = true)
    private Integer distance;
    
    public void setDistanceInfo(Integer distance, String distanceStr) {
    	this.distance = distance;
    	this.distanceStr = distanceStr;
    }
    
}

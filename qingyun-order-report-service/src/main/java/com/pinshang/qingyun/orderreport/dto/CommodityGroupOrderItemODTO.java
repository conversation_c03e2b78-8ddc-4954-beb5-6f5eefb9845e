package com.pinshang.qingyun.orderreport.dto;

import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Map;

/**
 * 商品组打印--订单明细
 */
@Data
public class CommodityGroupOrderItemODTO {
   @JsonIgnore
    private Long commodityId;
    @ApiModelProperty(value = "商品名称",position = 1)
    private String commodityName;
    @ApiModelProperty(value = "规格",position = 2)
    private String commoditySpec;
    @ApiModelProperty(value = "客户预定数量",position = 3)
    private Map<Long,BigDecimal> storeQuantityMap;
    @ApiModelProperty(value = "预订数量汇总",position = 3)
    private BigDecimal totalQuantity;

    public BigDecimal getTotalQuantity() {
        if(this.storeQuantityMap.isEmpty()){
            return null;
        }
        return storeQuantityMap.values().stream().reduce(BigDecimal.ZERO, BigDecimal::add);
    }
}

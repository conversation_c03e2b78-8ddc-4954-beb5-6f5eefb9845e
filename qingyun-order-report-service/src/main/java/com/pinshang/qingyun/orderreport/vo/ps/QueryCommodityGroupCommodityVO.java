package com.pinshang.qingyun.orderreport.vo.ps;

import com.pinshang.qingyun.base.page.Pagination;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @Version 0.1.0
 * @Date 2021/1/25 10:28
 * @Copyright © 2019-2021 qm
 * @Description -- 类说明
 **/
@Data
public class QueryCommodityGroupCommodityVO extends Pagination {

    @ApiModelProperty(value = "商品品类id")
    private Long commodityKindId;

    @ApiModelProperty(value = "商品id")
    private Long commodityId;

    @ApiModelProperty(value = "商品组id", required = true)
    private Long commodityGroupId;

}
package com.pinshang.qingyun.orderreport.controller;

import com.pinshang.qingyun.base.configure.expand.QYAssert;
import com.pinshang.qingyun.box.utils.SpringUtil;
import com.pinshang.qingyun.orderreport.service.BillOfLeadingService;
import com.pinshang.qingyun.orderreport.service.PrintingAllowedCheckService;
import com.pinshang.qingyun.orderreport.vo.BillOfLadingPreviewRespVo;
import com.pinshang.qingyun.orderreport.vo.DeliveryListPreviewReqVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.List;

/**
 * 提货单
 * <AUTHOR>
 * @date 2019/1/28 14:56.
 */
@RestController
@RequestMapping(value = "/statistical/billOfLading")
public class BillOfLeadingController {


    private final BillOfLeadingService billOfLeadingService;

    @Autowired
    private PrintingAllowedCheckService printingAllowedCheckService;

    public BillOfLeadingController(BillOfLeadingService billOfLeadingService) {
        this.billOfLeadingService = billOfLeadingService;
    }

    /**
     * 提货单 预览
     * @param lineId 线路编码
     * @param orderDate 订单日期
     * @return 返回提货单预览页面
     */
    @RequestMapping("/preview")
    public BillOfLadingPreviewRespVo doPreview(Long lineId, String orderDate) {
        return billOfLeadingService.preview(lineId, orderDate,true);
    }

    /**
     * 打印
     */
    @RequestMapping("/print")
    @ResponseBody
    public String print(@RequestBody DeliveryListPreviewReqVo param){
        List<DeliveryListPreviewReqVo> params = new ArrayList<>();
        params.add(param);
        printingAllowedCheckService.isPrintingAllowed(params);
        return billOfLeadingService.printAll(params,param.getUserId());
    }

    /**
     * 打印全部
     */
    @RequestMapping("/printAll/{userId}")
    @ResponseBody
    public String print(@RequestBody List<DeliveryListPreviewReqVo> params, @PathVariable("userId") Long userId){
        QYAssert.isTrue(SpringUtil.isNotEmpty(params),"请选择要打印的内容");
        printingAllowedCheckService.isPrintingAllowed(params);
        return billOfLeadingService.printAll(params, userId);
    }
}

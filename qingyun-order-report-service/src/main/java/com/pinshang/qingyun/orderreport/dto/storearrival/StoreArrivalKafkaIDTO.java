package com.pinshang.qingyun.orderreport.dto.storearrival;

import io.swagger.annotations.ApiModelProperty;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

import lombok.Data;
import lombok.NoArgsConstructor;

import com.pinshang.qingyun.renderer.annotation.FieldRender;
import com.pinshang.qingyun.renderer.constant.RenderFieldHelper;
import com.pinshang.qingyun.renderer.enums.FieldTypeEnum;

/*
 * 客户到货消息
 */
@Data
@NoArgsConstructor
public class StoreArrivalKafkaIDTO {
    /**
     * 订单图片 最多五张
     */
    private List<String> picList;

    /**
     * 备注
     */
    private String remarks;

    /**
     * 客户编码
     */
    private String storeCode;

    /**
     * 送货日期
     */
    private Date orderTime;
    
    @ApiModelProperty(position = 21, required = true, value = "百度经度")
    private BigDecimal baiduLongitude;
	@ApiModelProperty(position = 22, required = true, value = "百度维度")
    private BigDecimal baiduLatitude;
	@ApiModelProperty(position = 23, required = true, value = "定位地址")
	private String locationAddress;

	/**
     * 操作人ID
     */
    private Long userId;
    private String userName;
    
    
    
    
    // ============================= 以上是消息信息，以下是本地补充信息 =============================
	public String getPics() {
		if (null == this.picList) {
			return null;
		}
		
		String result = "";
		for (String picUrl: this.picList) {
			result += ("," + picUrl);
		}
		
		if (result.startsWith(",")) {
			result = result.substring(1);
		}
		
		return result;
	}
	
}

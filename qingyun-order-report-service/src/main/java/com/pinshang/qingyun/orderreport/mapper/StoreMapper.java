package com.pinshang.qingyun.orderreport.mapper;

import com.pinshang.qingyun.base.mybatis.MyMapper;
import com.pinshang.qingyun.orderreport.model.Store;
import com.pinshang.qingyun.orderreport.vo.DictionaryVo;
import com.pinshang.qingyun.orderreport.vo.FreshStoreProductShipmentsReqVo;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2019/1/23 14:05.
 */
@Repository
public interface StoreMapper extends MyMapper<Store> {

    @Select("SELECT store_type_id AS id,store_type_name AS name FROM t_store WHERE store_type_name IS NOT NULL GROUP BY store_type_id")
    List<DictionaryVo> selectAllStoreTypes();

    @Select("SELECT store_line_group_name FROM t_store  WHERE store_line_group_id = #{lineGroupId} LIMIT 1")
    String selectLineGroupNameById(@Param("lineGroupId") Long lineGroupId);

    @Select("SELECT delivery_warehouse_name FROM t_store  WHERE delivery_warehouse_id = #{warehouseId} LIMIT 1")
    String selectWarehouseNameById(@Param("warehouseId") Long warehouseId);

    List<Store> query4FreshStoreProductShipments(@Param("vo")FreshStoreProductShipmentsReqVo vo);

    List<Store> queryFreshProductShipmentsStore(@Param("storeIds")List<Long> storeIds);
}

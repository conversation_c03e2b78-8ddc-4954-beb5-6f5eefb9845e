package com.pinshang.qingyun.orderreport.util;

import com.pinshang.qingyun.orderreport.vo.ProductShipmentsRequestVo;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.joda.time.DateTime;

import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

import static org.apache.commons.lang3.time.DateUtils.isSameDay;

/**
 * <AUTHOR>
 * @date 2019/2/28 10:38.
 */
public class ProductShipmentRequestParamHelper {

    /**
     * 构造SQL查询需要的参数
     * @param requestVo 前台请求传过来的参数
     * @return 返回sql查询需要的参数
     */
    public static Map<String, Object> buildSqlMap(ProductShipmentsRequestVo requestVo) {
        DateFormat format = new SimpleDateFormat("yyyy-MM-dd");
        Map<String, Object> params = new HashMap<>();
        Long factoryId = requestVo.getFactoryId();
        if (factoryId != null) {
            params.put("factoryId", factoryId);
        }
        if (CollectionUtils.isNotEmpty(requestVo.getFactoryIds())) {
            params.put("factoryIds", requestVo.getFactoryIds());
        }
        Long lineGroupId = requestVo.getLineGroupId();
        if (lineGroupId != null) {
            params.put("lineGroupId", lineGroupId);
        }
        Long warehouseId = requestVo.getWarehouseId();
        if (warehouseId != null) {
            params.put("warehouseId", warehouseId);
        }
        if (StringUtils.isNoneBlank(requestVo.getDeliveryTime())) {
            params.put("deliveryTime", requestVo.getDeliveryTime());
        }
        Integer deliveryBatch = requestVo.getDeliveryBatch();
        if (deliveryBatch!=null) {
            params.put("deliveryBatch", deliveryBatch);
        }
        Date startOrderDate = requestVo.getStartOrderDate();
        Date endOrderDate = requestVo.getEndOrderDate();
        if (startOrderDate == null) {
            params.put("orderDate", format.format(new DateTime().plusDays(1).toDate()));
            params.put("latestFlag", true);
        } else {
            if (endOrderDate == null) {
                params.put("startDate", format.format(startOrderDate));
                params.put("endDate", format.format(new DateTime().plusDays(1).toDate()));
                params.put("latestFlag", false);
            } else {
                //两个日期都不为空,判断两个日期是否相等
                if (isSameDay(startOrderDate, endOrderDate)) {
                    params.put("orderDate", format.format(startOrderDate));
                    params.put("latestFlag", !new DateTime(startOrderDate).isBefore(new DateTime().withMillisOfDay(0)));
                } else {
                    params.put("startDate", format.format(startOrderDate));
                    params.put("endDate", format.format(endOrderDate));
                    params.put("latestFlag", false);
                }
            }
        }
        if (requestVo.getStoreTypeId() != null) {
            params.put("storeTypeId", requestVo.getStoreTypeId());
        }

        if(requestVo.getOrderModeType() != null){
            params.put("orderModeType",requestVo.getOrderModeType());
        }
        return params;
    }
}

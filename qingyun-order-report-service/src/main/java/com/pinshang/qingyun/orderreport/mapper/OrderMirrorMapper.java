package com.pinshang.qingyun.orderreport.mapper;

import com.pinshang.qingyun.base.mybatis.MyMapper;
import com.pinshang.qingyun.orderreport.model.OrderMirror;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * Created by zxj on 2019/8/29 14:50
 */
@Repository
public interface OrderMirrorMapper extends MyMapper<OrderMirror> {
    //void batchInsert(@Param("orderIds") List<Long> orderIds);

    void batchUpdate(@Param("list") List<OrderMirror> mirrorList);

    List<OrderMirror> queryOrderMirrorEntry(@Param("orderIds") List<Long> orderIds);

}

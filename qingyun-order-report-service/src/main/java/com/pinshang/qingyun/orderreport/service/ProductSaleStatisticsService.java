package com.pinshang.qingyun.orderreport.service;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.pinshang.qingyun.base.api.QYApplicationContext;
import com.pinshang.qingyun.base.configure.expand.QYAssert;
import com.pinshang.qingyun.base.constant.RedisKeyPrefixConst;
import com.pinshang.qingyun.base.enums.DictionaryEnums;
import com.pinshang.qingyun.base.enums.PrinterDataGroupTypeEnum;
import com.pinshang.qingyun.base.enums.smm.CategoryScopeEnums;
import com.pinshang.qingyun.box.utils.BeanCloneUtils;
import com.pinshang.qingyun.box.utils.BeanUtils;
import com.pinshang.qingyun.box.utils.DateUtil;
import com.pinshang.qingyun.box.utils.SpringUtil;
import com.pinshang.qingyun.common.dto.DictionaryIDTO;
import com.pinshang.qingyun.common.dto.DictionaryODTO;
import com.pinshang.qingyun.common.service.DictionaryClient;
//import com.pinshang.qingyun.order.search.service.EsProductSaleStatisticsClient;
//import com.pinshang.qingyun.orderreport.dto.ProductSaleStatisticsIDTO;
//import com.pinshang.qingyun.orderreport.dto.ProductSaleStatisticsSumODTO;
//import com.pinshang.qingyun.orderreport.dto.ProductSaleStatisticsTempODTO;
import com.pinshang.qingyun.orderreport.enums.ProductSaleStatisticsTypeEnums;
import com.pinshang.qingyun.orderreport.mapper.ProductSaleStatisticsMapper;
import com.pinshang.qingyun.orderreport.mapper.entry.*;
import com.pinshang.qingyun.orderreport.pdf.ProductSaleStatisticsPdfCreator;
import com.pinshang.qingyun.orderreport.util.DateUtils;
import com.pinshang.qingyun.orderreport.util.NumberUtils;
import com.pinshang.qingyun.orderreport.vo.ProductSaleStatisticsCommodityInfoListVo;
import com.pinshang.qingyun.orderreport.vo.ProductSaleStatisticsPurchaseQueryVo;
import com.pinshang.qingyun.orderreport.vo.ProductSaleStatisticsTempDateVo;
import com.pinshang.qingyun.orderreport.vo.ProductSaleStatisticsVo;
import com.pinshang.qingyun.smm.dto.user.SelectUserCategoryInfoIDTO;
import com.pinshang.qingyun.smm.dto.user.UserCategoryInfoODTO;
import com.pinshang.qingyun.smm.service.SMMUserClient;
//import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.redisson.api.RBucket;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.NumberFormat;
import java.util.*;

/**
 * 产品销售汇总
 */
@Service
//@Slf4j
public class ProductSaleStatisticsService {

    @Autowired
    private ProductSaleStatisticsMapper productSaleStatisticsMapper;
    @Autowired
    private ProductSaleStatisticsPdfCreator productSaleStatisticsPdfCreator;
    @Autowired
    private PrintTaskService printTaskService;
    @Autowired
    private DictionaryClient dictionaryClient;
    @Autowired
    private RedissonClient redissonClient;
//    @Autowired
//    private EsProductSaleStatisticsClient esProductSaleStatisticsClient;
    @Autowired
    private SMMUserClient userClient;
    @Autowired
    private CommonPdfPrintService commonPdfPrintService;
    /**
     * 产品销售汇总
     * @param vo
     * @return
     */
    public ProductSaleStatisticsTableEntry queryList(ProductSaleStatisticsVo vo){
        checkParam(vo);
        ProductSaleStatisticsTableEntry tableEntry = new ProductSaleStatisticsTableEntry();

        //处理表头
        DictionaryIDTO dictionaryIDTO = new DictionaryIDTO();
        dictionaryIDTO.setDictionaryId(DictionaryEnums.STORE_TYPE.getId());
        List<DictionaryODTO> storeTypes = dictionaryClient.getDictionaryList(dictionaryIDTO);
        List<List<String>> tableHeader = this.proceedTableHeader(storeTypes);
        tableEntry.setTableHeader(tableHeader);

        List<ProductSaleStatisticsTempEntry> list = null;
        //验证搜索开关，查询es
        RBucket<Boolean> bucket = redissonClient.getBucket(QYApplicationContext.applicationNameSwitch + RedisKeyPrefixConst.STATISTICS_ES_SWITCH);
        if (bucket.get()==null || !bucket.get()) {
            //处理表格内容：已排序：工厂ID升序，生产组名称升序，商品编码升序
            list = DateUtil.compareDate(DateUtil.getNowDate(),vo.getStartDate())?
                    productSaleStatisticsMapper.queryLatestList(vo):productSaleStatisticsMapper.queryList(vo);
        }else{
            QYAssert.isTrue(Boolean.FALSE,"此功能已经废弃");
//            ProductSaleStatisticsIDTO productSaleStatisticsIDTO = new ProductSaleStatisticsIDTO();
//            SpringUtil.copyProperties(vo,productSaleStatisticsIDTO);
//            List<ProductSaleStatisticsTempODTO> esList = esProductSaleStatisticsClient.queryList(productSaleStatisticsIDTO);
//            if(SpringUtil.isNotEmpty(esList)){
//                list = BeanCloneUtils.copyTo(esList,ProductSaleStatisticsTempEntry.class);
//            }
        }
        if(SpringUtil.isNotEmpty(list)){
            List<List<String>> tableData = getTableData(storeTypes, list);
            tableEntry.setTableData(tableData);
        }
        return tableEntry;
    }

    /**
     * 设置对象信息
     * @param storeTypes
     * @param list
     * @return
     */
    @NotNull
    private List<List<String>> getTableData(List<DictionaryODTO> storeTypes, List<ProductSaleStatisticsTempEntry> list) {
        Map<String,List<ProductSaleStatisticsTempEntry>> map = new LinkedHashMap<>();
        list.forEach(entry -> {
            String commodityCode = entry.getCommodityCode();
            List<ProductSaleStatisticsTempEntry> entryList;
            if(map.containsKey(commodityCode)){
                entryList = map.get(commodityCode);
            }else{
                entryList = new ArrayList<>();
            }
            entryList.add(entry);
            map.put(commodityCode,entryList);
        });

        List<List<String>> tableData = new ArrayList<>();
        map.forEach((k,v)->{
            List<String> row = proceedTableRow(k, v, storeTypes);
            tableData.add(row);
        });
        return tableData;
    }

    /**
     * 查询金额合计
     * @param vo
     * @return
     */
    public ProductSaleStatisticsSumEntry querySum(ProductSaleStatisticsVo vo) {
        checkParam(vo);
        RBucket<Boolean> bucket = redissonClient.getBucket(QYApplicationContext.applicationNameSwitch + RedisKeyPrefixConst.STATISTICS_ES_SWITCH);
        if (bucket.get()==null || !bucket.get()) {
            if(DateUtil.compareDate(DateUtil.getNowDate(),vo.getStartDate())){
                return productSaleStatisticsMapper.queryLatestSum(vo);
            }else{
                ProductSaleStatisticsTypeEnums statsTypeEnums = this.checkStatsType(vo);
                if(statsTypeEnums.equals(ProductSaleStatisticsTypeEnums.DEFAULT)){
                    return productSaleStatisticsMapper.querySum(vo);
                }
                if(statsTypeEnums.equals(ProductSaleStatisticsTypeEnums.SUPERVISOR_STATS)){
                    return productSaleStatisticsMapper.querySumBySupervisorStatistics(this.checkDateType(vo));
                }
                return productSaleStatisticsMapper.querySumByCommodityStatistics(this.checkDateType(vo));
            }
        }else{
            QYAssert.isTrue(Boolean.FALSE,"此功能已经废弃");
//            ProductSaleStatisticsIDTO productSaleStatisticsIDTO = new ProductSaleStatisticsIDTO();
//            SpringUtil.copyProperties(vo,productSaleStatisticsIDTO);
//            ProductSaleStatisticsSumODTO sumODTO = esProductSaleStatisticsClient.querySum(productSaleStatisticsIDTO);
//            if(sumODTO!=null){
//                return BeanCloneUtils.copyTo(sumODTO,ProductSaleStatisticsSumEntry.class);
//            }
            return null;
        }
    }

    /**
     * 产品销售汇总打印
     * @param vo
     * @return
     */
    public String print(ProductSaleStatisticsVo vo) {
        checkParam(vo);
        ProductSaleStatisticsTableEntry tableEntry = this.queryList(vo);
        QYAssert.isTrue(tableEntry!=null && SpringUtil.isNotEmpty(tableEntry.getTableData()),"没有查询到要打印的数据");
        String result = productSaleStatisticsPdfCreator.create(tableEntry,vo);
//        try {
            commonPdfPrintService.uploadAndPrintPdf(result,"order_report_TJ_PRODUCT_SALE",PrinterDataGroupTypeEnum.STATISTICAL_QUERY_DELIVERY,vo.getUserId());
//            printTaskService.savePrintTask(result, PrinterDataGroupTypeEnum.STATISTICAL_QUERY_DELIVERY, vo.getUserId(), 1, PrinterTypeEnum.LASER.getCode());
//        } catch (Exception e) {
//            return "fail";
//        }
        return "ok";
    }

    private void checkParam(ProductSaleStatisticsVo vo){
        QYAssert.notNull(vo.getStartDate(),"送货日期不能为空");
    }

    /**
     * 处理表头
     * @param storeTypes
     * @return
     */
    private List<List<String>> proceedTableHeader(List<DictionaryODTO> storeTypes) {
        List<String> tableHeaderLineOne = new ArrayList<>();
        tableHeaderLineOne.add("工厂");
        tableHeaderLineOne.add("生产组");
        tableHeaderLineOne.add("商品编码");
        tableHeaderLineOne.add("商品名称");
        tableHeaderLineOne.add("数量小计");
        tableHeaderLineOne.add("金额小计");
        storeTypes.forEach(item -> tableHeaderLineOne.add(item.getOptionName()));
        List<String> tableHeaderLineTwo = new ArrayList<>();
        for (int i = 0; i < storeTypes.size(); i++) {
            tableHeaderLineTwo.add("数量");
            tableHeaderLineTwo.add("金额");
        }
        List<List<String>> tableHeader = new ArrayList<>();
        tableHeader.add(tableHeaderLineOne);
        tableHeader.add(tableHeaderLineTwo);
        return tableHeader;
    }

    /**
     * 处理表格的每一行数据
     * @param commodityCode
     * @param list
     * @param storeTypes
     * @return
     */
    private List<String> proceedTableRow(String commodityCode, List<ProductSaleStatisticsTempEntry> list, List<DictionaryODTO> storeTypes) {
        NumberFormat numberFormat = NumberFormat.getNumberInstance();
        numberFormat.setMaximumFractionDigits(2);
        numberFormat.setRoundingMode(RoundingMode.HALF_UP);
        numberFormat.setGroupingUsed(false);
        List<String> row = new ArrayList<>();
        ProductSaleStatisticsTempEntry entry = list.get(0);
        row.add(entry.getFactoryName());
        row.add(entry.getWorkshopName());
        row.add(commodityCode);
        row.add(entry.getCommodityName());
        BigDecimal totalNum = BigDecimal.ZERO;
        BigDecimal totalPrice = BigDecimal.ZERO;
        for (ProductSaleStatisticsTempEntry entity : list) {
            totalNum = totalNum.add(entity.getCommodityNum());
            totalPrice = totalPrice.add(entity.getTotalPrice());
        }
        row.add(NumberUtils.subZeroAndDot(numberFormat.format(totalNum)));
        row.add(NumberUtils.subZeroAndDot(numberFormat.format(totalPrice)));

        for (DictionaryODTO type : storeTypes) {
            // 是否有该类型的客户购买了商品
            boolean hasType = false;
            for (ProductSaleStatisticsTempEntry entity : list) {
                if (type.getId().equals(String.valueOf(entity.getStoreTypeId()))) {
                    row.add(NumberUtils.subZeroAndDot(entity.getCommodityNum() + ""));
                    row.add(NumberUtils.subZeroAndDot(entity.getTotalPrice() + ""));
                    hasType = true;
                    break;
                }
            }
            if (!hasType) {
                row.add("");
                row.add("");
            }
        }
        return row;
    }

    /**
     * 产品销售汇总原始数据
     * @param vo
     * @return
     */
    public List<ProductSaleStatisticsTempEntry> queryListV2(ProductSaleStatisticsVo vo){
        checkParam(vo);
        if(DateUtil.compareDate(DateUtil.getNowDate(),vo.getStartDate())){
            return productSaleStatisticsMapper.queryLatestList(vo);
        }else{
            ProductSaleStatisticsTypeEnums statsTypeEnums = this.checkStatsType(vo);
            if(statsTypeEnums.equals(ProductSaleStatisticsTypeEnums.DEFAULT)){
                return productSaleStatisticsMapper.queryList(vo);
            }
            if(statsTypeEnums.equals(ProductSaleStatisticsTypeEnums.SUPERVISOR_STATS)){
                return productSaleStatisticsMapper.queryListBySupervisorStatistics(this.checkDateType(vo));
            }
            return productSaleStatisticsMapper.queryListByCommodityStatistics(this.checkDateType(vo));
        }
    }

    /**
     * 采购预估-产品销售汇总查询
     */
    public List<ProductSaleStatisticsTempEntry> queryListForPurchase(ProductSaleStatisticsPurchaseQueryVo queryVo) {
        List<ProductSaleStatisticsTempEntry> result = new ArrayList<>();
        // 历史销售汇总数据查询不到订货日期，且当查询整月的历史数据时，送货日期也无法提供，需要对送货日期进行分组查询
        List<Date> orderTimeList = DateUtil.getBetweenDates(queryVo.getStartDate(), queryVo.getEndDate());
        for (Date orderTime : orderTimeList) {
            ProductSaleStatisticsVo tempVo = BeanCloneUtils.copyTo(queryVo, ProductSaleStatisticsVo.class);
            tempVo.setStartDate(DateUtil.getDateNoTime(orderTime));
            tempVo.setEndDate(DateUtil.getDateNoTime(orderTime));
            List<ProductSaleStatisticsTempEntry> temp = this.queryListV2(tempVo);
            if (SpringUtil.isNotEmpty(temp)) {
                // 补全送货日期
                temp.forEach(t -> t.setOrderTime(orderTime));
                result.addAll(temp);
            }
        }
        return result;
    }

    /**
     * 查询 送货日期 ≥ T，订货日期为T-2 和 T-1 的数据
     */
    public List<ProductSaleStatisticsPurchaseTempEntry> queryListForPurchaseByCreateTime(ProductSaleStatisticsPurchaseQueryVo queryVo) {
        return productSaleStatisticsMapper.queryListForPurchaseByCreateTime(queryVo);
    }

    /**
     * 验证使用哪种汇总业务表：1-商品汇总，2=督导汇总，0=订单主表
     * @param vo
     * @return
     */
    public ProductSaleStatisticsTypeEnums checkStatsType(ProductSaleStatisticsVo vo){
        Boolean orderStats = false;
        Boolean supervisorStats = false;
        List<String> orderFieldList = Arrays.asList(new String[]{"storeSettId","storeSettIds","storeId","storeIds",
                "storeCompanyId","regionManagerId","lineGroupId","deliveryManId",});
        List<String> supervisorFieldList = Arrays.asList(new String[]{"supervisorId"});
        String[] fieldNames = BeanUtils.getFiledName(vo);
        for (int j = 0; j < fieldNames.length; j++) {
            String name = fieldNames[j];
            if (orderFieldList.contains(name)) {
                Object value = BeanUtils.getFieldValueByName(name, vo);
                if (value != null) {
                    orderStats = true;
                    break;
                }
            }
            if(supervisorFieldList.contains(name)) {
                Object value = BeanUtils.getFieldValueByName(name, vo);
                if (value != null) {
                    supervisorStats = true;
                    //break;
                }
            }
        }
        if(orderStats){
            return ProductSaleStatisticsTypeEnums.DEFAULT;
        }
        if(supervisorStats){
            return ProductSaleStatisticsTypeEnums.SUPERVISOR_STATS;
        }
        return ProductSaleStatisticsTypeEnums.COMMODITY_STATS;
    }

    /**
     *  分析日期区间
     * @param vo
     * @return
     */
    private ProductSaleStatisticsVo checkDateType(ProductSaleStatisticsVo vo){
        Date startDate = vo.getStartDate();
        Date endDateOrigin = vo.getEndDate();
        Date endDate = vo.getEndDate();
        List<ProductSaleStatisticsTempDateVo> tempDateVoList = new ArrayList<>();

        if(DateUtil.compareDate(DateUtil.getNowDate(),endDateOrigin)){
            ProductSaleStatisticsTempDateVo dateVo = new ProductSaleStatisticsTempDateVo();
            dateVo.setDateType(1);
            dateVo.setStartDate(DateUtil.getNowDate());
            dateVo.setEndDate(endDateOrigin);
            tempDateVoList.add(dateVo);
            endDate = DateUtils.getPreDate();
        }


        String startMonth = DateUtil.getDateMonth(startDate);
        String endMonth = DateUtil.getDateMonth(endDate);
        Boolean isFirstDay = DateUtil.isFirstDayOfMonth(startDate);
        Boolean isLastDay = DateUtil.isLastDayOfMonth(endDate);

        if(startMonth.equals(endMonth)){
            if(isFirstDay && isLastDay && !startMonth.equals(DateUtil.getDateMonth(DateUtil.getNowDate()))){
                ProductSaleStatisticsTempDateVo dateVo = new ProductSaleStatisticsTempDateVo();
                dateVo.setDateType(3);
                dateVo.setSaleMonth(startMonth);
                tempDateVoList.add(dateVo);
            }else{
                ProductSaleStatisticsTempDateVo dateVo = new ProductSaleStatisticsTempDateVo();
                dateVo.setDateType(2);
                dateVo.setStartDate(startDate);
                dateVo.setEndDate(endDate);
                tempDateVoList.add(dateVo);
            }
        }else{
            if(isFirstDay){
                ProductSaleStatisticsTempDateVo dateVo = new ProductSaleStatisticsTempDateVo();
                dateVo.setDateType(3);
                dateVo.setSaleMonth(startMonth);
                tempDateVoList.add(dateVo);

                ProductSaleStatisticsTempDateVo dateVo1 = new ProductSaleStatisticsTempDateVo();
                dateVo1.setDateType(2);
                dateVo1.setStartDate(DateUtil.firstDayOfMonth(endDate));
                dateVo1.setEndDate(endDate);
                tempDateVoList.add(dateVo1);
            }else if(isLastDay){
                ProductSaleStatisticsTempDateVo dateVo = new ProductSaleStatisticsTempDateVo();
                dateVo.setDateType(3);
                dateVo.setSaleMonth(endMonth);
                tempDateVoList.add(dateVo);

                ProductSaleStatisticsTempDateVo dateVo1 = new ProductSaleStatisticsTempDateVo();
                dateVo1.setDateType(2);
                dateVo1.setStartDate(startDate);
                dateVo1.setEndDate(DateUtil.lastDayOfMonth(startDate));
                tempDateVoList.add(dateVo1);
            }else{
                ProductSaleStatisticsTempDateVo dateVo = new ProductSaleStatisticsTempDateVo();
                dateVo.setDateType(2);
                dateVo.setStartDate(startDate);
                dateVo.setEndDate(endDate);
                tempDateVoList.add(dateVo);
            }
        }
        vo.setDataTypeList(tempDateVoList);
        return vo;
    }


    /**
     * 产品销售汇总(半年)
     * @param vo
     * @return
     */
    public ProductSaleStatisticsTableEntry queryHalfYearList(ProductSaleStatisticsVo vo){
        //查询结果集
        List<ProductSaleStatisticsTempEntry> list = queryHalfYearListV2(vo);
        //声明对象
        ProductSaleStatisticsTableEntry tableEntry = new ProductSaleStatisticsTableEntry();

        //处理表头
        DictionaryIDTO dictionaryIDTO = new DictionaryIDTO(DictionaryEnums.STORE_TYPE.getId());
        List<DictionaryODTO> storeTypes = dictionaryClient.getDictionaryList(dictionaryIDTO);
        List<List<String>> tableHeader = this.proceedTableHeader(storeTypes);
        tableEntry.setTableHeader(tableHeader);

        //处理查询结果集
        if(SpringUtil.isNotEmpty(list)){
            List<List<String>> tableData = getTableData(storeTypes, list);
            tableEntry.setTableData(tableData);
        }

        return tableEntry;
    }

    /**
     * 产品销售汇总原始数据
     * @param vo
     * @return
     */
    public List<ProductSaleStatisticsTempEntry> queryHalfYearListV2(ProductSaleStatisticsVo vo){
        checkParam(vo);
        if(DateUtil.compareDate(DateUtil.getNowDate(),vo.getStartDate())){
            return productSaleStatisticsMapper.queryLatestList(vo);
        }else{
            ProductSaleStatisticsTypeEnums statsTypeEnums = this.checkStatsType(vo);
            if(statsTypeEnums.equals(ProductSaleStatisticsTypeEnums.DEFAULT)){
                return productSaleStatisticsMapper.queryHalfYearList(vo);
            }
            if(statsTypeEnums.equals(ProductSaleStatisticsTypeEnums.SUPERVISOR_STATS)){
                return productSaleStatisticsMapper.queryListBySupervisorStatistics(this.checkDateType(vo));
            }
            return productSaleStatisticsMapper.queryListByCommodityStatistics(this.checkDateType(vo));
        }
    }

    /**
     * 查询金额合计（半年）
     * @param vo
     * @return
     */
    public ProductSaleStatisticsSumEntry querySumHalfYear(ProductSaleStatisticsVo vo) {
        checkParam(vo);
        if(DateUtil.compareDate(DateUtil.getNowDate(),vo.getStartDate())){
            return productSaleStatisticsMapper.queryLatestSum(vo);
        }else{
            ProductSaleStatisticsTypeEnums statsTypeEnums = this.checkStatsType(vo);
            if(statsTypeEnums.equals(ProductSaleStatisticsTypeEnums.DEFAULT)){
                return productSaleStatisticsMapper.querySumHalfYear(vo);
            }
            if(statsTypeEnums.equals(ProductSaleStatisticsTypeEnums.SUPERVISOR_STATS)){
                return productSaleStatisticsMapper.querySumBySupervisorStatistics(this.checkDateType(vo));
            }
            return productSaleStatisticsMapper.querySumByCommodityStatistics(this.checkDateType(vo));
        }
    }

    /**
     * 产品销售汇总打印(半年)
     * @param vo
     * @return
     */
    public String printHalfYear(ProductSaleStatisticsVo vo) {
        checkParam(vo);
        ProductSaleStatisticsTableEntry tableEntry = this.queryHalfYearList(vo);
        QYAssert.isTrue(tableEntry!=null && SpringUtil.isNotEmpty(tableEntry.getTableData()),"没有查询到要打印的数据");
        String result = productSaleStatisticsPdfCreator.create(tableEntry,vo);
//        try {
            commonPdfPrintService.uploadAndPrintPdf(result,"order_report_HalfYear_TJ_REPLENISHMENT",PrinterDataGroupTypeEnum.STATISTICAL_QUERY_DELIVERY,vo.getUserId());
//            printTaskService.savePrintTask(result, PrinterDataGroupTypeEnum.STATISTICAL_QUERY_DELIVERY, vo.getUserId(), 1, PrinterTypeEnum.LASER.getCode());
//        } catch (Exception e) {
//            return "fail";
//        }
        return "ok";
    }


    /**
     * 供 清美助手小程序 采购模块下 产品销售汇总使用
     * @param vo
     * @return
     */
    public PageInfo<ProductSaleStatisticsCommodityInfoListEntry> selectProductSaleStatisticsCommodityInfoListEntry(ProductSaleStatisticsCommodityInfoListVo vo, Long userId) {

        UserCategoryInfoODTO userCategoryInfoODTO = userClient.selectUserCategoryInfo(SelectUserCategoryInfoIDTO.onlyDb(userId));
        if (userCategoryInfoODTO == null || userCategoryInfoODTO.getCategoryScope() == null || userCategoryInfoODTO.getCategoryScope().compareTo(CategoryScopeEnums.NON.getCode()) == 0) {
            return new PageInfo<>();
        }

        if (userCategoryInfoODTO.getCategoryScope().compareTo(CategoryScopeEnums.ALL.getCode()) == 0) {
            return PageHelper.startPage(vo.getPageNo(), vo.getPageSize()).doSelectPageInfo(() -> productSaleStatisticsMapper.selectProductSaleStatisticsCommodityInfoListEntry(vo,
                    DateUtil.compareDate(DateUtil.getNowDate(), DateUtil.parseDate(vo.getDeliveryDate(), "yyyy-MM-dd")),
                    null));
        }
        Map<String, List<Long>> map = new HashMap<>();
        if (SpringUtil.isNotEmpty(userCategoryInfoODTO.getFirstCategoryList())) {
            map.put("firstCategory", userCategoryInfoODTO.getFirstCategoryList());
        }
        if (SpringUtil.isNotEmpty(userCategoryInfoODTO.getSecondCategoryList())) {
            map.put("secondCategory", userCategoryInfoODTO.getSecondCategoryList());
        }
        if (SpringUtil.isNotEmpty(userCategoryInfoODTO.getThirdCategoryList())) {
            map.put("thirdCategory", userCategoryInfoODTO.getThirdCategoryList());
        }

        return PageHelper.startPage(vo.getPageNo(), vo.getPageSize()).doSelectPageInfo(() -> productSaleStatisticsMapper.selectProductSaleStatisticsCommodityInfoListEntry(vo,
                DateUtil.compareDate(DateUtil.getNowDate(), DateUtil.parseDate(vo.getDeliveryDate(), "yyyy-MM-dd")),
                map));

    }

    /***
     * 送货列表数据
     * 供 清美助手小程序 采购模块下 产品销售汇总使用
     * @see //http://192.168.0.213/zentao/story-view-10417.html
     */
    public List<ProductSaleStatisticsDeliveryDateListEntry> queryDeliveryDateList() {
        String[] weeks = {"周日", "周一", "周二", "周三", "周四", "周五", "周六"};
        List<ProductSaleStatisticsDeliveryDateListEntry> dateList = new ArrayList<>();
        for (int i = -14; i <= 14; i++) {
            Calendar calendar = Calendar.getInstance();
            calendar.add(Calendar.DAY_OF_MONTH, i);
            dateList.add(new ProductSaleStatisticsDeliveryDateListEntry(DateUtil.getDateFormate(calendar.getTime(), "yyyy-MM-dd"),weeks[calendar.get(Calendar.DAY_OF_WEEK) - 1]));
        }
        return dateList;
    }

}

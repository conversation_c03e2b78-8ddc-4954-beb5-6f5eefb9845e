package com.pinshang.qingyun.orderreport.vo.ps;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @Version 0.1.0
 * @Date 2021/1/26 16:54
 * @Copyright © 2019-2021 qm
 * @Description -- 类说明
 **/
@Data
public class OnOffCommodityGroupLineVO {
    @ApiModelProperty(value = "线路id，后台自动判断启用/停用", example = "线路id", required = true)
    private Long lineId;

    @ApiModelProperty(value = "创建人id", example = "123456", required = true, hidden = true)
    private Long createId;
}

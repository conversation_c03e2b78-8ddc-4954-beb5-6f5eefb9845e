package com.pinshang.qingyun.orderreport.service;

import com.pinshang.qingyun.box.utils.SpringUtil;
import com.pinshang.qingyun.orderreport.mapper.OrderLinePrintStatusMapper;
import com.pinshang.qingyun.orderreport.model.OrderLinePrintStatus;
import com.pinshang.qingyun.orderreport.model.PrintStatus;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Example;

import java.util.Date;
import java.util.List;

/**
 * Created by weican on 2019-02-14.
 */
@Service
@Slf4j
public class OrderLinePrintStatusService {
    @Autowired
    private OrderLinePrintStatusMapper orderLinePrintStatusMapper;
    public OrderLinePrintStatus findByOrderTimeAndLineId(Date orderTime, Long lineId){
        Example example = new Example(PrintStatus.class);
        example.createCriteria().andEqualTo("orderTime",orderTime).andEqualTo("lineId",lineId);
        List<OrderLinePrintStatus> list = orderLinePrintStatusMapper.selectByExample(example);
        if(SpringUtil.isNotEmpty(list)){
            return list.get(0);
        }
        return null;
    }
}

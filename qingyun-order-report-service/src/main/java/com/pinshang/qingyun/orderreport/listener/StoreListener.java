package com.pinshang.qingyun.orderreport.listener;

import com.alibaba.fastjson.JSON;
import com.pinshang.qingyun.base.annotations.OnlineSwitchWatcher;
import com.pinshang.qingyun.base.api.QYApplicationContext;
import com.pinshang.qingyun.kafka.base.BaseKafkaOnlineSwitchProcessor;
import com.pinshang.qingyun.kafka.base.KafkaMessageWrapper;
import com.pinshang.qingyun.kafka.base.KafkaTopicConstant;
import com.pinshang.qingyun.orderreport.service.TJOrderMirrorService;
import com.pinshang.qingyun.product.dto.sync.StoreInfoODTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.List;

/**
 * 同步客户信息时，修改客户明天及以后的订单落地数据
 */
@Component
@Slf4j
@OnlineSwitchWatcher
public class StoreListener extends BaseKafkaOnlineSwitchProcessor {

	@Autowired
	protected TJOrderMirrorService tjOrderMirrorService;

	@KafkaListener(	id="${application.name.switch}" + KafkaTopicConstant.SYNC_STORE_TOPIC+"qingyunOrderReportSyncKafkaListenerContainerFactory",
					topics = { "${application.name.switch}" + KafkaTopicConstant.SYNC_STORE_TOPIC })
    public void listener(String message) {
		KafkaMessageWrapper wrapper = JSON.parseObject(message, KafkaMessageWrapper.class);
		String jsonStr = wrapper.getData().toString();
		if (StringUtils.isNotEmpty(jsonStr)) {
			StoreInfoODTO vo = JSON.parseObject(jsonStr, StoreInfoODTO.class);
			if(vo!=null){
				//修改客户明天及以后的订单落地数据
				tjOrderMirrorService.syncStoreOrderMirror(vo.getStoreList());
			}
		}
    }

	@Override
	public List<String> getKafkaIds() {
		return Arrays.asList(
				QYApplicationContext.applicationNameSwitch + KafkaTopicConstant.SYNC_STORE_TOPIC+"qingyunOrderReportSyncKafkaListenerContainerFactory"
		);
	}
}

package com.pinshang.qingyun.orderreport.vo.ps;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @Version 0.1.0
 * @Date 2021/1/26 17:56
 * @Copyright © 2019-2021 qm
 * @Description -- 类说明 商品组与客户后得到的商品组
 **/
@Data
public class MutexCommodityGroupVO {

    @ApiModelProperty(value = "商品组id", example = "123456", required = true)
    private Long commodityGroupId;

    @ApiModelProperty(value = "商品组名称", example = "清美商品组", required = true)
    private String commodityGroupName;
}

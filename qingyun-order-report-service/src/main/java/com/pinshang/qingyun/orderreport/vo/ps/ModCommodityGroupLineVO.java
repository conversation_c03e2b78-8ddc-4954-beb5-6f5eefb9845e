package com.pinshang.qingyun.orderreport.vo.ps;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @Version 0.1.0
 * @Date 2021/1/26 14:43
 * @Copyright © 2019-2021 qm
 * @Description -- 类说明
 **/
@Data
public class ModCommodityGroupLineVO {
    @ApiModelProperty(value = "主键", required = true)
    private Long id;

    @ApiModelProperty(value = "线路组id 要和线路组名称一起修改", required = true)
    private Long lineGroupId;

    @ApiModelProperty(value = "线路组名称 要和线路组id一起修改", required = true)
    private String lineGroupName;

    @ApiModelProperty(value = "线路名称", required = true)
    private String lineName;

    @ApiModelProperty(value = "商品组id", required = true)
    private Long commodityGroupId;

    @ApiModelProperty(value = "发货仓库id 要和发货仓库名称一起修改", required = true)
    private Long deliveryWarehouseId;

    @ApiModelProperty(value = "发货仓库名称 要和发货仓库id一起修改", required = true)
    private String deliveryWarehouseName;

    @ApiModelProperty(value = "送货员id", required = true)
    private Long deliverymanId;

    @ApiModelProperty(value = "车位", required = true)
    private String carportName;

    @ApiModelProperty(value = "车牌号", required = true)
    private String licensePlate;

    @ApiModelProperty(value = "商品组线路状态:1-启用状态，0-停用状态", required = true, hidden = true)
    private Boolean status;

    @ApiModelProperty(value = "更新人", required = true, hidden = true)
    private Long updateId;

}

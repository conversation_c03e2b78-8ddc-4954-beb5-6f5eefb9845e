package com.pinshang.qingyun.orderreport.mapper.entry;

import lombok.Data;

import java.math.BigDecimal;

/**
 * 清美人APP 生产组发货单明细
 */
@Data
public class WorkshopShipmentsItemEntry {

    /**
     * 车位
     */
    private String carportName;

    /**
     * 送货员
     */
    private String deliverymanName;

    /**
     * 线路产品合计
     */
    private BigDecimal itemQuantity;


    public String getCarportName() {
        if (carportName == null) {
            return "";
        }
        return carportName;
    }

    public String getDeliverymanName() {
        if (deliverymanName == null) {
            return "";
        }
        return deliverymanName;
    }
}

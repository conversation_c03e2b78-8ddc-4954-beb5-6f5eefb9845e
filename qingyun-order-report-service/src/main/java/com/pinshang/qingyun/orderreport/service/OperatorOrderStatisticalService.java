package com.pinshang.qingyun.orderreport.service;

import com.alibaba.excel.EasyExcel;
import com.pinshang.qingyun.base.configure.expand.QYAssert;
import com.pinshang.qingyun.base.enums.PrinterDataGroupTypeEnum;
import com.pinshang.qingyun.base.util.ExcelUtil;
import com.pinshang.qingyun.box.utils.DateUtil;
import com.pinshang.qingyun.box.utils.SpringUtil;
import com.pinshang.qingyun.orderreport.dto.OperatorOrderReqIDTO;
import com.pinshang.qingyun.orderreport.mapper.OperatorOrderStatisticalMapper;
import com.pinshang.qingyun.orderreport.mapper.OrderItemMapper;
import com.pinshang.qingyun.orderreport.mapper.OrderMapper;
import com.pinshang.qingyun.orderreport.mapper.entry.OperatorStatisticsEntry;
import com.pinshang.qingyun.orderreport.model.Order;
import com.pinshang.qingyun.orderreport.model.OrderItem;
import com.pinshang.qingyun.orderreport.model.Workload;
import com.pinshang.qingyun.orderreport.pdf.OperatorOrderStatisticalPdfCreator;
import com.pinshang.qingyun.orderreport.vo.OperatorOrderMonitorVo;
import com.pinshang.qingyun.orderreport.vo.OperatorOrderReqVo;
import com.pinshang.qingyun.orderreport.vo.OperatorOrderRespVo;
import lombok.extern.slf4j.Slf4j;
import org.joda.time.DateTime;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Example;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.apache.commons.lang3.time.DateUtils.isSameDay;

/**
 * <AUTHOR>
 * @date 2019/3/13 14:19.
 */
@Service
@Slf4j
public class OperatorOrderStatisticalService {

    private final OperatorOrderStatisticalMapper operatorOrderStatisticalMapper;
    private final OperatorOrderStatisticalPdfCreator operatorOrderStatisticalPdfCreator;
    private final PrintTaskService printTaskService;
    private final OrderItemMapper orderItemMapper;
    private final OrderMapper orderMapper;
    @Autowired
    private CommonPdfPrintService commonPdfPrintService;

    public OperatorOrderStatisticalService(OperatorOrderStatisticalMapper operatorOrderStatisticalMapper, OperatorOrderStatisticalPdfCreator operatorOrderStatisticalPdfCreator, PrintTaskService printTaskService, OrderItemMapper orderItemMapper, OrderMapper orderMapper) {
        this.operatorOrderStatisticalMapper = operatorOrderStatisticalMapper;
        this.operatorOrderStatisticalPdfCreator = operatorOrderStatisticalPdfCreator;
        this.printTaskService = printTaskService;
        this.orderItemMapper = orderItemMapper;
        this.orderMapper = orderMapper;
    }

    public List<OperatorOrderRespVo> queryList(OperatorOrderReqVo reqVo) {
        Map<String, Object> sqlParams = buildSqlParam(reqVo);
        return operatorOrderStatisticalMapper.queryList(sqlParams);
    }

    public void export(OperatorOrderReqIDTO operatorOrderReqIDTO, HttpServletResponse httpServletResponse) throws IOException {
        OperatorOrderReqVo operatorOrderReqVo = new OperatorOrderReqVo();
        BeanUtils.copyProperties(operatorOrderReqIDTO,operatorOrderReqVo);
        long queryStart = System.currentTimeMillis();
        List<OperatorOrderRespVo> operatorOrderList = queryList(operatorOrderReqVo);
        long queryEnd = System.currentTimeMillis();
        log.info("操作员订单统计----导出查询时间=======" + ((queryStart - queryEnd)/1000));
        try {
            ExcelUtil.setFileNameAndHead(httpServletResponse,"操作员订单统计" + DateTimeFormatter.ofPattern("yyyyMMdd").format(LocalDateTime.now()));
            EasyExcel.write(httpServletResponse.getOutputStream(),OperatorOrderRespVo.class).autoCloseStream(Boolean.FALSE).sheet("操作员订单统计").doWrite(operatorOrderList);;
        } catch (Exception e) {
            log.error("操作员订单统计导出出错",e);
            ExcelUtil.setExceptionResponse(httpServletResponse);
        }
        long exportEnd = System.currentTimeMillis();
        log.info("操作员订单统计----导出excel时间=======" + (queryEnd - exportEnd)/1000);
    }


    /**
     * 构建SQL参数
     *
     * @param reqVo 操作员ID
     * @return 返回Sql查询参数map
     */
    private Map<String, Object> buildSqlParam(OperatorOrderReqVo reqVo) {
        Map<String, Object> result = new HashMap<>();
        Long operatorId = reqVo.getOperatorId();
        Date startDate = reqVo.getStartDate();
        Date endDate = reqVo.getEndDate();
        if (operatorId != null) {
            result.put("operatorId", operatorId);
        }

        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
        if (startDate != null && endDate != null) {
            boolean sameDay = isSameDay(startDate, endDate);
            if (sameDay) {
                String format = dateFormat.format(startDate);
                result.put("orderDate", format);
            } else {
                result.put("startDate", dateFormat.format(startDate));
                result.put("endDate", dateFormat.format(endDate));
            }
        } else {
            result.put("latestFlag", true);
            result.put("orderDate", dateFormat.format(new DateTime().plusDays(1).toDate()));
        }

        return result;
    }

    /**
     * 打印
     * @param reqVo
     */
    public void print(OperatorOrderReqVo reqVo) {
        List<OperatorOrderRespVo> list = queryList(reqVo);
        QYAssert.isTrue(SpringUtil.isNotEmpty(list), "数据为空,不能打印!");
        String fileName = operatorOrderStatisticalPdfCreator.create(list, reqVo);
        commonPdfPrintService.uploadAndPrintPdf(fileName,"order_report_TJ_OPERATOR_ORDER",PrinterDataGroupTypeEnum.STATISTICAL_QUERY_DELIVERY,reqVo.getUserId());

//        printTaskService.savePrintTask(fileName, PrinterDataGroupTypeEnum.STATISTICAL_QUERY_DELIVERY, reqVo.getUserId(), 1, PrinterTypeEnum.LASER.getCode());
    }

    /**
     * 操作员订单统计, 新增订单
     * @param order 订单
     */
    public void processOperatorInsertOrder(Order order) {
        if(order.getOrderStatus()==0){
            Workload workload = new Workload();
            workload.setOrderTime(order.getOrderTime());
            workload.setOperatorId(order.getCreateId());
            workload.setOrderQuantity(1);
            workload.setItemQuantity(order.getOrderList().size());
            operatorOrderStatisticalMapper.insertOrUpdateOperatorOrder(workload);
        }
    }

    /**
     * 操作员订单统计, 修改订单
     * @param order 订单
     */
    public void processOperatorUpdateOrder(Order order) {
        //增加一步是否修改日期的判断
        Order oldOrder = orderMapper.selectByPrimaryKey(order.getId());
        Boolean updateOrderTimeFlag = order.getOrderTime().compareTo(oldOrder.getOrderTime())!=0;
        if(updateOrderTimeFlag){
            //如果修改了订单日期，那么针对修改后的日期新增操作员订单统计;原来订单日期的操作员统计取消
            this.processOperatorInsertOrder(order);
            this.processOperatorCancelOrder(oldOrder);
        }else{
            Example ex = new Example(OrderItem.class);
            ex.createCriteria().andEqualTo("orderId", order.getId());
            List<OrderItem> orderItems = orderItemMapper.selectByExample(ex);
            if (order.getOrderList().size() != orderItems.size()) {
                Integer diff = orderItems.size() - order.getOrderList().size();
                Workload workload = new Workload();
                workload.setItemQuantity(diff);
                workload.setOperatorId(order.getCreateId());
                workload.setOrderTime(order.getOrderTime());
                workload.setOrderQuantity(0);
                operatorOrderStatisticalMapper.updateItemQuantity(workload);
            }
        }

    }

    /**
     * 因为取消订单没有发送订单明细子表信息,所以需要重新
     * 操作员订单统计 取消订单
     * @param order 订单
     */
    public void processOperatorCancelOrder(Order order) {
        Long id = order.getId();
        Example ex = new Example(OrderItem.class);
        ex.createCriteria().andEqualTo("orderId", id);
        List<OrderItem> orderItems = orderItemMapper.selectByExample(ex);
        Workload workload = new Workload();
        workload.setItemQuantity(orderItems.size());
        workload.setOperatorId(order.getCreateId());
        workload.setOrderTime(order.getOrderTime());
        workload.setOrderQuantity(1);
        operatorOrderStatisticalMapper.updateItemQuantity(workload);
    }

    /**
     * 根据订单操作员和订单日期，重新统计操作员订单数据
     * @param vo
     */
    public Integer resetOperatorOrderStatistics(OperatorOrderMonitorVo vo){
        if(vo==null || vo.getOperatorId()==null || (vo.getOrderTime() == null && vo.getStartTime()==null)){
            return 0;
        }
        //删除指定条件下的操作员统计
        operatorOrderStatisticalMapper.deleteOperatorStatistics(vo);
        vo.setLatestFlag(false);
        if((vo.getOrderTime() != null && DateUtil.compareDate(DateUtil.getNowDate(),vo.getOrderTime())) ||
           (vo.getStartTime() != null && DateUtil.compareDate(DateUtil.getNowDate(),vo.getStartTime()))){
            vo.setLatestFlag(true);
        }
        List<OperatorStatisticsEntry> entryList = operatorOrderStatisticalMapper.queryOperatorStatisticsEntry(vo);
        if(SpringUtil.isNotEmpty(entryList)){
            operatorOrderStatisticalMapper.batchInsert(entryList);
        }
        return 1;
    }
}

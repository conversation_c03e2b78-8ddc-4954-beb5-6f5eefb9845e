package com.pinshang.qingyun.orderreport.mapper;

import com.pinshang.qingyun.orderreport.mapper.entry.BillOfLeadingPreviewEntity;
import com.pinshang.qingyun.orderreport.vo.BillOfLadingPreviewRespVo;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2019/1/28 15:44.
 */
@Repository
public interface BillOfLeadingMapper {

    BillOfLadingPreviewRespVo queryTitle(Long lineId);

    List<BillOfLeadingPreviewEntity> queryPreviewData(@Param("lineId") Long lineId,
                                                      @Param("orderDate") String orderDate,
                                                      @Param("latestFlag") boolean latestFlag);

}

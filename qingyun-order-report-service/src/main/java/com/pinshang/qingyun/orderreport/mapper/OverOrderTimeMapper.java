package com.pinshang.qingyun.orderreport.mapper;

import com.pinshang.qingyun.base.mybatis.MyMapper;
import com.pinshang.qingyun.orderreport.mapper.entry.OverOrderTimeEntry;
import com.pinshang.qingyun.orderreport.model.OverOrderTime;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;

@Repository
public interface OverOrderTimeMapper extends MyMapper<OverOrderTime> {
    List<OverOrderTimeEntry> queryOverOrderList(@Param("orderTime") Date orderTime);

    int batchInsert(List<OverOrderTimeEntry> overOrderTimeList);
}

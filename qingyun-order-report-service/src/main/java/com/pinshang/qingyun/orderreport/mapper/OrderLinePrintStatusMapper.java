package com.pinshang.qingyun.orderreport.mapper;

import com.pinshang.qingyun.base.mybatis.MyMapper;
import com.pinshang.qingyun.orderreport.model.OrderLinePrintStatus;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;

@Repository
public interface OrderLinePrintStatusMapper extends MyMapper<OrderLinePrintStatus> {
    int batchInsert(List<OrderLinePrintStatus> orderLinePrintStatusList);

    List<OrderLinePrintStatus> queryOrderPrintStatus(@Param("orderTime") Date orderTime);
}

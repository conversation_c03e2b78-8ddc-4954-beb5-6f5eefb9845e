package com.pinshang.qingyun.orderreport.service;

import com.pinshang.qingyun.base.configure.expand.QYAssert;
import com.pinshang.qingyun.base.enums.PrinterDataGroupTypeEnum;
import com.pinshang.qingyun.base.enums.PrinterTypeEnum;
import com.pinshang.qingyun.stream.plugin.core.MockMultipartFile;
import com.pinshang.qingyun.upload.dto.odto.FileUploadRestSingleODTO;
import com.pinshang.qingyun.upload.dto.odto.FileUploadResultODTO;
import com.pinshang.qingyun.upload.service.FileUploadClientFactory;
//import com.pinshang.qingyun.upload.service.XsFileUploadClient;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.FileInputStream;
//import java.io.FileNotFoundException;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.List;

@Service
@Slf4j
public class CommonPdfPrintService {

    @Autowired
    private PrintTaskService printTaskService;

  /*  @Autowired
    private XsFileUploadClient xsFileUploadClient;*/
    @Autowired
    private FileUploadClientFactory fileUploadClientFactory;

    public void uploadAndPrintPdf(String filePath, String customizedPath,PrinterDataGroupTypeEnum printerDataGroupTypeEnum,Long userId) {
        log.info("上传并打印pdf文件，filePath={},customizedPath={}", filePath, customizedPath);
        FileUploadRestSingleODTO fileUploadRestSingleODTO = null;
        try {
            File file = new File(filePath);
            InputStream inputStream = new FileInputStream(file);
            MultipartFile multipartFile = new MockMultipartFile("file", file.getName(), MediaType.MULTIPART_FORM_DATA_VALUE, inputStream);
            fileUploadRestSingleODTO = fileUploadClientFactory.getXsClient().restUpload4path(multipartFile, "PS_PRINT", customizedPath);
        } catch (Exception e) {
        	log.error("xsFileUploadClient上传pdf异常", e);
        	QYAssert.isFalse("上传pdf异常!");
        }
            log.info("xsFileUploadClient上传pdf-result ： {}", fileUploadRestSingleODTO);
            if (fileUploadRestSingleODTO == null) {
                QYAssert.isFalse("上传pdf异常!");
            }
            FileUploadResultODTO data = fileUploadRestSingleODTO.getData();
            if (data == null) {
                QYAssert.isFalse("上传pdf异常!");
            }
            String visitUrl = data.getVisitUrl();
            // 调用打印机服务
            savePrintTask(printTaskService, visitUrl,printerDataGroupTypeEnum,userId);
//        } catch (Exception e) {
//            log.error("xsFileUploadClient上传pdf异常", e);
//            QYAssert.isFalse("上传pdf异常!");
//        }
    }

    public void uploadAndPrintPdfList(List<String> filePath, String customizedPath, PrinterDataGroupTypeEnum printerDataGroupTypeEnum, Long userId) {
        log.info("上传并打印pdf文件，filePath={},customizedPath={}", filePath, customizedPath);
//        try {
            List<String> visitUrlList = new ArrayList<>();
            filePath.forEach(item->{
                try {
                    File file = new File(item);
                    InputStream inputStream = new FileInputStream(file);

                    MultipartFile multipartFile = new MockMultipartFile("file", file.getName(), MediaType.MULTIPART_FORM_DATA_VALUE, inputStream);
                    FileUploadRestSingleODTO fileUploadRestSingleODTO = fileUploadClientFactory.getXsClient().restUpload4path(multipartFile, "PS_PRINT", customizedPath);
                    log.info("xsFileUploadClient上传pdf-result ： {}", fileUploadRestSingleODTO);
                    if (fileUploadRestSingleODTO == null) {
                        QYAssert.isFalse("上传pdf异常!");
                    }
                    FileUploadResultODTO data = fileUploadRestSingleODTO.getData();
                    if (data == null) {
                        QYAssert.isFalse("上传pdf异常!");
                    }
                    visitUrlList.add(data.getVisitUrl());
                } catch (Exception e) {
                    e.printStackTrace();
                }
            });
            // 调用打印机服务
            savePrintTaskList(printTaskService, visitUrlList,printerDataGroupTypeEnum,userId);
//        } catch (Exception e) {
//            log.error("xsFileUploadClient上传pdf异常", e);
//            QYAssert.isFalse("上传pdf异常!");
//        }
    }
    public void savePrintTask(PrintTaskService printTaskService, String fileName,PrinterDataGroupTypeEnum printerDataGroupTypeEnum,Long userId) {
        log.info("调用打印机服务，fileName={}", fileName);
//        try {
            printTaskService.savePrintTask(fileName, printerDataGroupTypeEnum, userId, 1, PrinterTypeEnum.LASER.getCode());
//        } catch (Exception e) {
//            log.error("打印异常!", e);
//            QYAssert.isFalse("打印异常!");
//        }
    }
    public void savePrintTaskList(PrintTaskService printTaskService, List<String> fileNameList,PrinterDataGroupTypeEnum printerDataGroupTypeEnum,Long userId) {
        log.info("调用打印机服务，fileName={}", fileNameList);
//        try {
            printTaskService.savePrintTasks(fileNameList, printerDataGroupTypeEnum, userId, 1, PrinterTypeEnum.LASER.getCode());
//        } catch (Exception e) {
//            log.error("打印异常!", e);
//            QYAssert.isFalse("打印异常!");
//        }
    }
}

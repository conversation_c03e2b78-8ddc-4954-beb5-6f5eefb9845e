package com.pinshang.qingyun.orderreport.vo.ps;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @Version 0.1.0
 * @Date 2021/1/22 14:42
 * @Copyright © 2019-2021 qm
 * @Description -- 类说明
 **/
@Data
public class ModCommodityGroupVO {
    @ApiModelProperty(value = "商品组id", example = "商品组id", required = true)
    private Long groupId;

    @ApiModelProperty(value = "修改后的商品组名称", example = "修改后的商品组名称", required = true)
    private String groupName;

    @ApiModelProperty(value = "创建人id", example = "123456", required = true, hidden = true)
    private Long createId;
}

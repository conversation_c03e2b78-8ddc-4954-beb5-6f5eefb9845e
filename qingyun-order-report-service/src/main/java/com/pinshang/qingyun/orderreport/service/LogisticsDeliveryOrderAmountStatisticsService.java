package com.pinshang.qingyun.orderreport.service;

import com.pinshang.qingyun.base.configure.expand.QYAssert;
import com.pinshang.qingyun.base.enums.DictionaryEnums;
import com.pinshang.qingyun.box.utils.DateUtil;
import com.pinshang.qingyun.box.utils.SpringUtil;
import com.pinshang.qingyun.common.dto.DictionaryIDTO;
import com.pinshang.qingyun.common.dto.DictionaryODTO;
import com.pinshang.qingyun.common.service.DictionaryClient;
import com.pinshang.qingyun.orderreport.mapper.LogisticsDeliveryOrderAmountStatisticsMapper;
import com.pinshang.qingyun.orderreport.mapper.entry.LogisticsDeliveryOrderAmountStatisticsTableEntry;
import com.pinshang.qingyun.orderreport.mapper.entry.LogisticsDeliveryOrderAmountStatisticsTempEntry;
import com.pinshang.qingyun.orderreport.util.NumberUtils;
import com.pinshang.qingyun.orderreport.vo.LogisticsDeliveryOrderAmountStatisticsVo;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.DateFormat;
import java.text.NumberFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * @description: 物流配送订单金额统计
 * @author: hhf
 * @time: 2022/6/15/015 14:06
 */
@Service
public class LogisticsDeliveryOrderAmountStatisticsService {

    @Autowired
    private LogisticsDeliveryOrderAmountStatisticsMapper mapper;
    @Autowired
    private DictionaryClient dictionaryClient;


    /**
     * 列表查询
     * @param vo
     * @return
     */
    public LogisticsDeliveryOrderAmountStatisticsTableEntry queryList(LogisticsDeliveryOrderAmountStatisticsVo vo){
        LogisticsDeliveryOrderAmountStatisticsTableEntry tableEntry = new LogisticsDeliveryOrderAmountStatisticsTableEntry();

        //处理表头
        DictionaryIDTO dictionaryIDTO = new DictionaryIDTO(DictionaryEnums.STORE_TYPE.getId());
        List<DictionaryODTO> storeTypes = dictionaryClient.getDictionaryList(dictionaryIDTO);
        List<String> tableHeader = this.proceedTableHeader(storeTypes);
        tableEntry.setTableHeader(tableHeader);

        //处理数据
        List<LogisticsDeliveryOrderAmountStatisticsTempEntry> list = this.findList(vo);

        //处理查询结果集
        if(SpringUtil.isNotEmpty(list)){
            List<List<String>> tableData = getTableData(storeTypes, list);
            tableEntry.setTableData(tableData);
        }
        return tableEntry;
    }

    /**
     * 处理表头
     * @param storeTypes
     * @return
     */
    private List<String> proceedTableHeader(List<DictionaryODTO> storeTypes) {
        List<String> tableHeader = new ArrayList<>();
        tableHeader.add("送货日期");
        tableHeader.add("线路组");
        storeTypes.forEach(item -> tableHeader.add(item.getOptionName()));
        tableHeader.add("总计");
        return tableHeader;
    }

    /**
     * 查询数据
     * @param vo
     * @return
     */
    public List<LogisticsDeliveryOrderAmountStatisticsTempEntry> findList(LogisticsDeliveryOrderAmountStatisticsVo vo) {
        QYAssert.notNull(vo.getOrderTime(),"送货日期不能为空");
        DateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
        List<LogisticsDeliveryOrderAmountStatisticsTempEntry> list = new ArrayList<>();
        try {
            Date date = dateFormat.parse(vo.getOrderTime());
            if(DateUtil.compareDate(DateUtil.getNowDate(),date)){
                list = mapper.queryLatestList(vo);
            }else{
                list = mapper.queryList(vo);
            }
        } catch (ParseException e) {
            e.printStackTrace();
        }

        return list;
    }

    /**
     * 设置对象信息
     * @param storeTypes
     * @param list
     * @return
     */
    @NotNull
    private List<List<String>> getTableData(List<DictionaryODTO> storeTypes, List<LogisticsDeliveryOrderAmountStatisticsTempEntry> list) {
        Map<String,List<LogisticsDeliveryOrderAmountStatisticsTempEntry>> map = new LinkedHashMap<>();
        list.forEach(entry -> {
            String storeLineGroupId = entry.getStoreLineGroupId();
            List<LogisticsDeliveryOrderAmountStatisticsTempEntry> entryList;
            if(map.containsKey(storeLineGroupId)){
                entryList = map.get(storeLineGroupId);
            }else{
                entryList = new ArrayList<>();
            }
            entryList.add(entry);
            map.put(storeLineGroupId,entryList);
        });

        List<List<String>> tableData = new ArrayList<>();
        map.forEach((k,v)->{
            List<String> row = proceedTableRow(v, storeTypes);
            tableData.add(row);
        });
        return tableData;
    }

    /**
     * 处理表格的每一行数据
     * @param list
     * @param storeTypes
     * @return
     */
    private List<String> proceedTableRow(List<LogisticsDeliveryOrderAmountStatisticsTempEntry> list, List<DictionaryODTO> storeTypes) {
        NumberFormat numberFormat = NumberFormat.getNumberInstance();
        numberFormat.setMaximumFractionDigits(2);
        numberFormat.setRoundingMode(RoundingMode.HALF_UP);
        numberFormat.setGroupingUsed(false);
        List<String> row = new ArrayList<>();
        LogisticsDeliveryOrderAmountStatisticsTempEntry entry = list.get(0);
        row.add(entry.getOrderTime());
        row.add(entry.getStoreLineGroupName());
        BigDecimal totalPrice = BigDecimal.ZERO;
        for (DictionaryODTO type : storeTypes) {
            // 是否有该类型的客户购买了商品
            boolean hasType = false;
            for (LogisticsDeliveryOrderAmountStatisticsTempEntry entity : list) {
                if (type.getId().equals(String.valueOf(entity.getStoreTypeId()))) {
                    row.add(NumberUtils.subZeroAndDot(entity.getOrderAmount() + ""));
                    //合计金额
                    totalPrice = totalPrice.add(entity.getOrderAmount());
                    hasType = true;
                    break;
                }
            }
            if (!hasType) {
                row.add("");
            }
        }
        row.add(NumberUtils.subZeroAndDot(numberFormat.format(totalPrice)));
        return row;
    }

}

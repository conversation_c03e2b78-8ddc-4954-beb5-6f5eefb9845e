package com.pinshang.qingyun.orderreport.mapper;

import com.pinshang.qingyun.orderreport.mapper.entry.ProductStatisticsListEntry;
import com.pinshang.qingyun.orderreport.mapper.entry.ProductStatisticsListSumEntry;
import com.pinshang.qingyun.orderreport.vo.ProductStatisticsListVo;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface ProductStatisticsListMapper {
    //产品统计清单：查询最新订单表数据
    List<ProductStatisticsListEntry> queryLatestList(@Param("vo") ProductStatisticsListVo vo);

    //产品统计清单：查询历史订单表数据
    List<ProductStatisticsListEntry> queryList(@Param("vo") ProductStatisticsListVo vo);

    ProductStatisticsListSumEntry querySum(@Param("vo") ProductStatisticsListVo vo);

    ProductStatisticsListSumEntry queryLatestSum(@Param("vo") ProductStatisticsListVo vo);

}

package com.pinshang.qingyun.orderreport.vo.ps;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @Version 0.1.0
 * @Date 2021/1/25 10:35
 * @Copyright © 2019-2021 qm
 * @Description -- 类说明
 **/
@Data
public class PsScCommodityGroupCommodityVO {

    @ApiModelProperty(value = "主键")
    private String id;

    @ApiModelProperty(value = "商品组id")
    private String commodityGroupId;

    @ApiModelProperty(value = "商品id")
    private String commodityId;

    @ApiModelProperty(value = "商品编号")
    private String commodityCode;

    @ApiModelProperty(value = "商品名称")
    private String commodityName;

    @ApiModelProperty(value = "商品规格")
    private String commoditySpec;

    @ApiModelProperty(value = "商品计量单位")
    private String commodityUnitName;

    @ApiModelProperty(value = "商品包装规格")
    private String commodityPackageSpec;

    @ApiModelProperty(value = "商品是否可售")
    private String commodityState;

    @ApiModelProperty(value = "商品品类")
    private String commodityThirdKindName;

}

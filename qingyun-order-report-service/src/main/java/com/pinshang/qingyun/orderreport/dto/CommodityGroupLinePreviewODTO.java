package com.pinshang.qingyun.orderreport.dto;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.pinshang.qingyun.box.utils.DateUtil;
import com.pinshang.qingyun.box.utils.SpringUtil;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 商品组打印预览
 */
@Data
public class CommodityGroupLinePreviewODTO {
    @ApiModelProperty(value = "商品组名称",position = 1)
    private String commodityGroupName;
    @ApiModelProperty(value = "线路编码",position = 2)
    private String lineCode;
    @ApiModelProperty(value = "线路名称",position = 2)
    private String lineName;
    @ApiModelProperty(value = "车牌",position = 3)
    private String licensePlate;
    @ApiModelProperty(value = "送货员",position = 4)
    private String deliverymanName;
    @ApiModelProperty(value = "电话",position = 5)
    private String phone;
    @ApiModelProperty(value = "送货日期：yyyy-MM-dd，已转换格式，前端不需再格式化",position = 6)
    private String orderTime;
    @ApiModelProperty(value = "打印时间：yyyy-MM-dd HH:mm，已转换格式，前端不需再格式化",position = 7)
    private String printTime;

    @ApiModelProperty(value = "动态表头",position = 15)
    private List<List<String>> tableHeader;
    @ApiModelProperty(value = "动态表格",position = 15)
    private List<List<String>> tableData;

    @JsonIgnore
    private Long commodityGroupId;
    @JsonIgnore
    private Map<Long,String> storeMap;
    @JsonIgnore
    private List<CommodityGroupOrderItemODTO> commodityList;

    public String getPrintTime() {
        return DateUtil.get4yMdHm(new Date());
    }

    public static CommodityGroupLinePreviewODTO init(CommodityGroupLineTmpDetailDTO detailDTO,Date orderTime){
        CommodityGroupLinePreviewODTO previewODTO = new CommodityGroupLinePreviewODTO();
        SpringUtil.copyProperties(detailDTO,previewODTO);
        previewODTO.setOrderTime(DateUtil.get4yMd(orderTime));
        return previewODTO;
    }

    public String getCommodityGroupName() {
        return commodityGroupName==null?"":commodityGroupName;
    }

    public String getLineCode() {
        return lineCode==null?"":lineCode;
    }

    public String getLineName() {
        return lineName==null?"":lineName;
    }

    public String getLicensePlate() {
        return licensePlate==null?"":licensePlate;
    }

    public String getDeliverymanName() {
        return deliverymanName==null?"":deliverymanName;
    }

    public String getPhone() {
        return phone==null?"":phone;
    }

    public String getOrderTime() {
        return orderTime==null?"":orderTime;
    }
}

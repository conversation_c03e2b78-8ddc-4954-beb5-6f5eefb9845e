package com.pinshang.qingyun.orderreport.mapper;

import com.pinshang.qingyun.base.mybatis.MyMapper;
import com.pinshang.qingyun.orderreport.mapper.entry.DeliveryOrderGiftEntry;
import com.pinshang.qingyun.orderreport.model.OrderItem;
import com.pinshang.qingyun.orderreport.vo.TJOrderMonitorVo;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2019/1/14 16:11.
 */
@Repository
public interface OrderItemMapper extends MyMapper<OrderItem> {

    int batchInsert(List<OrderItem> orderList);

    List<DeliveryOrderGiftEntry> findGiftByOrderIdAndType(@Param("orderId")Long orderId, @Param("type")Integer type);

    Long selectOrderItemListCountByOrderCreateTime(TJOrderMonitorVo tjOrderMonitorVo);

    List<OrderItem> selectOrderItemListByOrderCreateTime(TJOrderMonitorVo tjOrderMonitorVo);

    List<OrderItem> selectOrderItemListByOrderIdList(@Param("orderIdList")List<Long> orderIdList);

    List<OrderItem> repeatHaving1();
}

package com.pinshang.qingyun.orderreport.controller;

import com.pinshang.qingyun.orderreport.model.Commodity;
import com.pinshang.qingyun.orderreport.service.CommodityService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * 统计系统：商品公共方法查询
 */
@RestController
@RequestMapping(value = "/statistical/tjCommodity")
public class TJCommodityController {

    @Autowired
    private CommodityService commodityService;

    /**
     * 查询商品的分类名称
     * @param commodityCodes
     * @return
     */
    @PostMapping("/queryCommodityCateName")
    public Map<String, String> queryCommodityCateName(@RequestBody List<String> commodityCodes){
        return commodityService.queryCommodityCateName(commodityCodes);
    }

    /**
     * 查询商品的分类名称
     * @param commodityCodes
     * @return
     */
    @PostMapping("/queryCommodityMap")
    public Map<String, Commodity> queryCommodityMap(@RequestBody List<String> commodityCodes){
        return commodityService.queryCommodityMap(commodityCodes);
    }

}
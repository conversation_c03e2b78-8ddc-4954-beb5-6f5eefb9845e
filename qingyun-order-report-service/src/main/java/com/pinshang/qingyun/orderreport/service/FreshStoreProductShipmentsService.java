package com.pinshang.qingyun.orderreport.service;

import com.pinshang.qingyun.base.api.QYApplicationContext;
import com.pinshang.qingyun.base.configure.expand.QYAssert;
import com.pinshang.qingyun.base.constant.RedisKeyPrefixConst;
import com.pinshang.qingyun.box.utils.DateUtil;
import com.pinshang.qingyun.box.utils.SpringUtil;
//import com.pinshang.qingyun.order.search.service.EsFreshProductShipmentsClient;
//import com.pinshang.qingyun.orderreport.dto.FreshProductShipmentsIDTO;
//import com.pinshang.qingyun.orderreport.dto.FreshProductShipmentsTempODTO;
import com.pinshang.qingyun.orderreport.mapper.FreshStoreProductShipmentsMapper;
import com.pinshang.qingyun.orderreport.mapper.StoreMapper;
import com.pinshang.qingyun.orderreport.mapper.entry.FreshStoreProductShipmentsEntry;
import com.pinshang.qingyun.orderreport.model.Store;
import com.pinshang.qingyun.orderreport.vo.FreshStoreProductShipmentsReqVo;
import com.pinshang.qingyun.orderreport.vo.FreshStoreProductShipmentsRespVo;
import org.redisson.api.RBucket;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static java.util.stream.Collectors.groupingBy;

/**
 * <AUTHOR>
 * @Date 2019/3/11 12:48
 */
@Service
public class FreshStoreProductShipmentsService {

    @Autowired
    private StoreMapper storeMapper;
    @Autowired
    private FreshStoreProductShipmentsMapper freshStoreProductShipmentsMapper;
    @Autowired
    private RedissonClient redissonClient;
//    @Autowired
//    private EsFreshProductShipmentsClient esFreshProductShipmentsClient;

    public FreshStoreProductShipmentsRespVo queryList(FreshStoreProductShipmentsReqVo reqVo){
        reqVo.setLatestFlag(DateUtil.compareDate(DateUtil.getNowDate(), reqVo.getOrderDate()));
        FreshStoreProductShipmentsRespVo respVo = new FreshStoreProductShipmentsRespVo();
        List<FreshStoreProductShipmentsEntry> dataList = new ArrayList<>();

        //验证搜索开关，查询es
        RBucket<Boolean> bucket = redissonClient.getBucket(QYApplicationContext.applicationNameSwitch + RedisKeyPrefixConst.STATISTICS_ES_SWITCH);
        if (bucket.get()==null || !bucket.get()) {
            dataList = freshStoreProductShipmentsMapper.queryList(reqVo);
            if(SpringUtil.isEmpty(dataList)){
                respVo.setTitles(this.getTitles(null));
                return respVo;
            }
        }else {
            QYAssert.isTrue(Boolean.FALSE,"此功能已经废弃");
//            FreshProductShipmentsIDTO idto = new FreshProductShipmentsIDTO();
//            SpringUtil.copyProperties(reqVo,idto);
//            List<FreshProductShipmentsTempODTO> esList = esFreshProductShipmentsClient.queryList(idto);
//            if(SpringUtil.isEmpty(esList)){
//                respVo.setTitles(this.getTitles(null));
//                return respVo;
//            }
//            dataList = esList.stream().map(FreshStoreProductShipmentsEntry::convert).collect(Collectors.toList());
        }
        List<Long> storeIds = dataList.stream().map(FreshStoreProductShipmentsEntry::getStoreId).distinct().collect(Collectors.toList());
        List<Store> stores = storeMapper.queryFreshProductShipmentsStore(storeIds);
        //处理动态视图列名
        respVo.setTitles(this.getTitles(stores));
        //处理视图数据
        if(dataList == null || dataList.isEmpty()){
            return respVo;
        }
        Map<Long, List<FreshStoreProductShipmentsEntry>> commodityGroupData = dataList.parallelStream().collect(groupingBy(FreshStoreProductShipmentsEntry::getCommodityId));
        List<Long> goodsIds = dataList.parallelStream().map(FreshStoreProductShipmentsEntry::getCommodityId).distinct()
                .collect(Collectors.toList());
        List<List<String>> body = new ArrayList<>(goodsIds.size());
        for (Long goodsId : goodsIds) {
            List<FreshStoreProductShipmentsEntry> entries = commodityGroupData.get(goodsId);
            FreshStoreProductShipmentsEntry entry = entries.get(0);
            List<String> row = new ArrayList<>(respVo.getTitles().size());
            row.add(entry.getFactoryName());
            row.add(entry.getWorkshopName());
            row.add(entry.getFlowshopName());
            row.add(entry.getCommodityCode());
            row.add(entry.getCommodityName());
            row.add(entry.getUnit());
            Double rowCount = 0.0;
            for (Long storeId : storeIds) {
                Double quantity = 0.0;
                for (FreshStoreProductShipmentsEntry shipmentsEntry : entries) {
                    if(shipmentsEntry.getStoreId().longValue() == storeId){
                        quantity += shipmentsEntry.getCommodityTotal();
                        break;
                    }
                }
                if(BigDecimal.valueOf(quantity).compareTo(BigDecimal.ZERO) == 0){
                    row.add("");
                }else {
                    row.add(BigDecimal.valueOf(quantity).setScale(2, RoundingMode.HALF_UP).stripTrailingZeros().toPlainString());
                }
                rowCount += quantity;
            }
            row.add(6, BigDecimal.valueOf(rowCount).setScale(2, RoundingMode.HALF_UP).stripTrailingZeros().toPlainString());
            body.add(row);
        }
        respVo.setDataList(body);
        return respVo;
    }

    private List<String> getTitles(List<Store> stores){
        List<String> titles = new ArrayList<>();
        titles.add("工厂");
        titles.add("生产组");
        titles.add("车间");
        titles.add("商品编号");
        titles.add("商品名称(规格)");
        titles.add("计量单位");
        titles.add("合计");
        if(SpringUtil.isNotEmpty(stores)){
            titles.addAll(stores.stream().map(Store::getStoreName).collect(Collectors.toList()));
        }
        return titles;
    }

    public List<FreshStoreProductShipmentsEntry> queryListV2(FreshStoreProductShipmentsReqVo vo){
        vo.setLatestFlag(DateUtil.compareDate(DateUtil.getNowDate(), vo.getOrderDate()));
        return freshStoreProductShipmentsMapper.queryList(vo);
    }

    /**
     * 查询动态表头客户名
     * @param storeIds
     * @return
     */
    public List<Store> queryFreshStoreList(List<Long> storeIds){
        return storeMapper.queryFreshProductShipmentsStore(storeIds);
    }
}

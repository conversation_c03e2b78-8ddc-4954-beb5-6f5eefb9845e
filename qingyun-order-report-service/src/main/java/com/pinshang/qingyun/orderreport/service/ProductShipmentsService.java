package com.pinshang.qingyun.orderreport.service;

import com.pinshang.qingyun.base.api.QYApplicationContext;
import com.pinshang.qingyun.base.configure.expand.QYAssert;
import com.pinshang.qingyun.base.constant.RedisKeyPrefixConst;
import com.pinshang.qingyun.base.enums.PrinterDataGroupTypeEnum;
import com.pinshang.qingyun.base.enums.PrinterTypeEnum;
import com.pinshang.qingyun.box.utils.BeanCloneUtils;
import com.pinshang.qingyun.box.utils.DateUtil;
import com.pinshang.qingyun.box.utils.SpringUtil;
//import com.pinshang.qingyun.order.search.dto.EsProductSaleStatisticsODTO;
//import com.pinshang.qingyun.order.search.dto.EsProductShipmentsODTO;
//import com.pinshang.qingyun.order.search.service.EsProductSaleStatisticsClient;
//import com.pinshang.qingyun.order.search.service.EsProductShipmentsClient;
import com.pinshang.qingyun.orderreport.dto.ProductSaleStatisticsIDTO;
import com.pinshang.qingyun.orderreport.dto.ProductShipmentsRequestIDTO;
import com.pinshang.qingyun.orderreport.dto.ProductShipmentsTempODTO;
import com.pinshang.qingyun.orderreport.mapper.ProductShipmentsMapper;
import com.pinshang.qingyun.orderreport.mapper.StoreMapper;
import com.pinshang.qingyun.orderreport.mapper.entry.ProductSaleStatisticsTempEntry;
import com.pinshang.qingyun.orderreport.mapper.entry.ProductShipmentsEntry;
import com.pinshang.qingyun.orderreport.pdf.ProductShipmentsPdfCreator;
import com.pinshang.qingyun.orderreport.util.ProductShipmentRequestParamHelper;
import com.pinshang.qingyun.orderreport.vo.DictionaryVo;
import com.pinshang.qingyun.orderreport.vo.ProductShipmentResponseVo;
import com.pinshang.qingyun.orderreport.vo.ProductShipmentsRequestVo;

import org.redisson.api.RBucket;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;

import static java.util.stream.Collectors.groupingBy;
import static java.util.stream.Collectors.toList;

/**
 * <AUTHOR>
 * @date 2019/3/4 16:25.
 */
@Service
public class ProductShipmentsService {

    private final StoreMapper storeMapper;
    private final ProductShipmentsMapper productShipmentsMapper;
    private final PrintTaskService printTaskService;
    private final ProductShipmentsPdfCreator productShipmentsPdfCreator;
    @Autowired
    private RedissonClient redissonClient;
//    @Autowired
//    private EsProductShipmentsClient esProductShipmentsClient;
    
    @Autowired
    private CommonPdfPrintService commonPdfPrintService;

    public ProductShipmentsService(StoreMapper storeMapper, ProductShipmentsMapper productShipmentsMapper, PrintTaskService printTaskService, ProductShipmentsPdfCreator productShipmentsPdfCreator) {
        this.storeMapper = storeMapper;
        this.productShipmentsMapper = productShipmentsMapper;
        this.printTaskService = printTaskService;
        this.productShipmentsPdfCreator = productShipmentsPdfCreator;
    }

    public ProductShipmentResponseVo doSearch(ProductShipmentsRequestVo requestVo) {
        ProductShipmentResponseVo result = new ProductShipmentResponseVo();

        // 找到所有店铺类型
        List<DictionaryVo> storeTypes = storeMapper.selectAllStoreTypes();
        List<String> tableTitle = new ArrayList<>();
        tableTitle.add("生产组");
        tableTitle.add("商品编号");
        tableTitle.add("商品名称(规格)");
        tableTitle.add("计量单位");
        storeTypes.forEach(d-> tableTitle.add(d.getName()));
        tableTitle.add("合计");
        result.setTableTitle(tableTitle);
        //---- 表格第一行数据处理完毕.

        //构造查询需要的SQL参数
        Map<String, Object> params = ProductShipmentRequestParamHelper.buildSqlMap(requestVo);
        //开始查商品
        List<ProductShipmentsEntry> dbResult = new ArrayList<>();
        RBucket<Boolean> bucket = redissonClient.getBucket(QYApplicationContext.applicationNameSwitch + RedisKeyPrefixConst.STATISTICS_ES_SWITCH);
        if (bucket.get()==null || !bucket.get()) {
            dbResult = productShipmentsMapper.selectShipmentsTableBody(params);
        }else{
            QYAssert.isTrue(Boolean.FALSE,"此功能已经废弃");
//            ProductShipmentsRequestIDTO productShipmentsRequestIDTO = new ProductShipmentsRequestIDTO();
//            SpringUtil.copyProperties(requestVo,productShipmentsRequestIDTO);
//            List<ProductShipmentsTempODTO> esList = esProductShipmentsClient.queryList(productShipmentsRequestIDTO);
//            if(SpringUtil.isNotEmpty(esList)){
//                dbResult = BeanCloneUtils.copyTo(esList, ProductShipmentsEntry.class);
//            }
        }
        List<Long> goodsIds = dbResult.parallelStream().map(ProductShipmentsEntry::getCommodityId).distinct().collect(toList());
        Map<Long, List<ProductShipmentsEntry>> commodityStoreType = dbResult.parallelStream().collect(groupingBy(ProductShipmentsEntry::getCommodityId));
        List<List<String>> tableBody = new ArrayList<>();
        for (Long goodsId : goodsIds) {
            List<ProductShipmentsEntry> productShipmentsDbVos = commodityStoreType.get(goodsId);
            ProductShipmentsEntry productShipmentsDbVo = productShipmentsDbVos.get(0);
            List<String> tableRowData = new ArrayList<>();
            tableRowData.add(productShipmentsDbVo.getWorkshopName());
            tableRowData.add(productShipmentsDbVo.getCommodityCode());
            tableRowData.add(productShipmentsDbVo.getCommodityName());
            tableRowData.add(productShipmentsDbVo.getUnit());
            Double count = 0.0;
            for (DictionaryVo storeType : storeTypes) {
                Double quantity = 0.0;
                for (ProductShipmentsEntry shipmentsDbVo : productShipmentsDbVos) {
                    if (Objects.equals(shipmentsDbVo.getStoreTypeId(), storeType.getId())) {
                        quantity += shipmentsDbVo.getStoreTypeTotal();
                        break;
                    }
                }
                if (BigDecimal.valueOf(quantity).compareTo(BigDecimal.ZERO) == 0) {
                    tableRowData.add("");
                } else {
                    tableRowData.add(BigDecimal.valueOf(quantity).setScale(2, RoundingMode.HALF_UP).stripTrailingZeros().toPlainString());
                }
                count += quantity;
            }
            tableRowData.add(BigDecimal.valueOf(count).setScale(2, RoundingMode.HALF_UP).stripTrailingZeros().toPlainString());
            tableBody.add(tableRowData);
        }
        result.setTableData(tableBody);
        return result;
    }

    public boolean print(ProductShipmentsRequestVo vo) {
        ProductShipmentResponseVo pdfContent = doSearch(vo);
        if (pdfContent == null || pdfContent.getTableData() == null || pdfContent.getTableData().size() == 0) {
            return false;
        }
        String fileName = productShipmentsPdfCreator.create(vo, pdfContent);
        //order_report_TJ_PRODUCT_SHIPMENTS 产品发货总量
        commonPdfPrintService.uploadAndPrintPdf(fileName,"order_report_TJ_PRODUCT_SHIPMENTS",PrinterDataGroupTypeEnum.STATISTICAL_QUERY_DELIVERY,vo.getUserId());
        return true;
    }

    public List<ProductShipmentsEntry> doSearchV2(ProductShipmentsRequestVo vo){
        Map<String, Object> params = ProductShipmentRequestParamHelper.buildSqlMap(vo);
        return productShipmentsMapper.selectShipmentsTableBody(params);
    }
}

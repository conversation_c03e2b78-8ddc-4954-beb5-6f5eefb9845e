package com.pinshang.qingyun.orderreport.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.pinshang.qingyun.base.page.Pagination;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @description 产品销售汇总查询条件
 * @date 2019/09/03
 */
@Data
public class CommoditySaleStatisticsItemReqVo{

    /**送货日期-开始**/
    @JsonFormat(pattern="yyyy-MM-dd",timezone = "GMT+8")
    private Date startDate;

    /**送货日期-结束**/
    @JsonFormat(pattern="yyyy-MM-dd",timezone = "GMT+8")
    private Date endDate;

    /**送货日期-开始 月报**/
    private String startDateStr;

    /**送货日期-结束 月报**/
    private String endDateStr;

    /**产品销售汇总(商品) 和 产品销售汇总(月报) 接口共用，类型区分**/
    private Integer paramType;

    /**商品id集合**/
    private List<Long> commodityIds;

    /***
     * 公司id
     */
    private Long companyId;

    /***
     * 业务类型  14-排除计划销售订单 15-排除顺丰配送订单
     */
    private Integer filterBusinessType;
}

package com.pinshang.qingyun.orderreport.mapper.entry;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @description 查询统计订单主表信息
 * @date 2019/8/30
 */
@Data
public class LatestStoreTypeDetailEntry {

    /** latest主键Id **/
    private Long id;

    /** 客户类型Id(表：t_store) **/
    private Long storeTypeId;

    /** 送货日期 **/
    private Date orderTime;

    /** 商品id **/
    private Long commodityId;

    /** 商品数量 **/
    private BigDecimal commodityNum;

    /** 促销前的源始金额 **/
    private BigDecimal totalPrice;

    /** 公司id **/
    private Long companyId;
}

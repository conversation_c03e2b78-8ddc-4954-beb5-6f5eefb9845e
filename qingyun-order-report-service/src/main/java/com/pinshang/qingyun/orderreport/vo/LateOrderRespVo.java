package com.pinshang.qingyun.orderreport.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.format.DateTimeFormat;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2019/3/18 9:50.
 */
@Data
public class LateOrderRespVo {

    @ExcelProperty(value = "制单时间")
    private Date createTime;
    @DateTimeFormat("yyyy-MM-dd")
    @ExcelProperty(value = "送货日期")
    private Date orderTime;
    @ExcelProperty(value = "客户编码")
    private String storeCode;
    @ExcelProperty(value = "客户名称")
    private String storeName;
    @ExcelProperty(value = "订单金额")
    private String orderAmount;
    @ExcelProperty(value = "督导")
    private String supervisionName;
    @ExcelProperty(value = "操作员")
    private String operatorName;
    @ExcelProperty(value = "订单编号")
    private String orderCode;
    @ExcelProperty(value = "线路组")
    private String lineGroupName;
}

package com.pinshang.qingyun.orderreport.vo.ps;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @Version 0.1.0
 * @Date 2021/1/25 10:56
 * @Copyright © 2019-2021 qm
 * @Description -- 类说明
 **/
@Data
public class DelCommodityGroupCommodityVO {
    @ApiModelProperty(position = 1, value = "删除商品组商品主键", example = "123456", required = true)
    private Long id;

//    @ApiModelProperty(value = "商品组id")
//    private Long commodityGroupId;
//
//    @ApiModelProperty(value = "商品id")
//    private Long commodityId;

    @ApiModelProperty(value = "创建人id", example = "123456", required = true, hidden = true)
    private Long createId;
}

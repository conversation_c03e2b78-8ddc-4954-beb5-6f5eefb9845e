package com.pinshang.qingyun.orderreport.model;

import lombok.Data;

import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;

@Data
@Table(name = "t_tj_order_line_print_status")
public class OrderLinePrintStatus {

    @Id
    private Long id;
    private Date orderTime;
    //打印状态：0=未打印，1=送货清单已打印  2= 提货单已打印，3=送货清单和提货单都已打印
    private Integer printStatus;
    private Long lineId;
    private String lineCode;
    private String lineName;
    private Long lineGroupId;
    private String lineGroupName;
    private Long deliveryTimeId;
    private String deliveryTime;
    private Long teamLeaderId;
    private String teamLeaderName;
    private Long deliverymanId;
    private String deliverymanName;
    private Date createTime;
}
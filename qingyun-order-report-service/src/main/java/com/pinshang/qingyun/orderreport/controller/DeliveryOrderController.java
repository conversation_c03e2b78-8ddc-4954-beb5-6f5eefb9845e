package com.pinshang.qingyun.orderreport.controller;

import com.alibaba.excel.EasyExcel;
import com.github.pagehelper.PageInfo;
import com.pinshang.qingyun.base.configure.expand.QYAssert;
import com.pinshang.qingyun.base.util.ExcelUtil;
import com.pinshang.qingyun.infrastructure.exportfile.cache.starter.FileCacheQuery;
import com.pinshang.qingyun.infrastructure.exportfile.cache.starter.FileCacheQueryParameter;
import com.pinshang.qingyun.orderreport.mapper.entry.DeliveryOrderEntry;
import com.pinshang.qingyun.orderreport.mapper.entry.DeliveryOrderPrintEntry;
import com.pinshang.qingyun.orderreport.service.DeliveryOrderService;
import com.pinshang.qingyun.orderreport.vo.DeliveryOrderDetailRespVo;
import com.pinshang.qingyun.orderreport.vo.DeliveryOrderPrintVo;
import com.pinshang.qingyun.orderreport.vo.DeliveryOrderReqVo;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.propertyeditors.CustomDateEditor;
import org.springframework.web.bind.WebDataBinder;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2019/3/5 16:08
 */
@Slf4j
@RestController
@RequestMapping("/statistical/deliveryOrder")
public class DeliveryOrderController {

    @Autowired
    private DeliveryOrderService deliveryOrderService;

    /**
     * 送货单列表页
     * @param deliveryOrderReqVo
     * @return
     */
    @PostMapping("/queryList")
    @ApiModelProperty(value = "送货单", notes = "送货单")
    public PageInfo<DeliveryOrderEntry> queryList(@RequestBody DeliveryOrderReqVo deliveryOrderReqVo){
        QYAssert.isTrue(deliveryOrderReqVo != null ,"查询参数为空异常!");
        QYAssert.isTrue(deliveryOrderReqVo.getStartOrderDate() != null,"送货日期不能为空!");
        return deliveryOrderService.queryList(deliveryOrderReqVo);
    }

    /**
     * 送货单详情
     * @param orderId
     * @return
     */
    @GetMapping("/orderDetail/{id}")
    public DeliveryOrderDetailRespVo queryDeliveryOrderDetail(@PathVariable("id")Long orderId){
        QYAssert.notNull(orderId,"id不可以为空");
        return deliveryOrderService.queryDeliveryOrderDetail(orderId);
    }

    @PostMapping("/print")
    public List<DeliveryOrderPrintEntry> print(@RequestBody DeliveryOrderPrintVo vo){
        return deliveryOrderService.print(vo);
    }

    @ApiOperation(value = "导出列表", notes = "导出列表")
    @RequestMapping(value = "/export", method = RequestMethod.GET)
    @FileCacheQuery(bizCode = "OR_DELIVERY_ORDER_E")
    public void exportExcel(@FileCacheQueryParameter DeliveryOrderReqVo reqVO, HttpServletResponse response) throws IOException {
        long qS = System.currentTimeMillis();
        reqVO.initExportPage();
        PageInfo<DeliveryOrderEntry> pageInfo = deliveryOrderService.queryList(reqVO);
        long qE = System.currentTimeMillis();
        log.info("送货单-导出--查询时间=" + ((qE - qS) / 1000));

        try {
            ExcelUtil.setFileNameAndHead(response, "送货单" + System.currentTimeMillis());
            EasyExcel.write(response.getOutputStream(), DeliveryOrderEntry.class).autoCloseStream(Boolean.FALSE).sheet("送货单")
                    .doWrite(pageInfo.getList());

        }catch (Exception e){
            log.error("送货单导出错误", e);
            ExcelUtil.setExceptionResponse( response );
        }
        long eE = System.currentTimeMillis();
        log.info("送货单-导出--excel处理时间="+ ( (eE -qE) /1000 )  );
    }

    @InitBinder
    public void initData(WebDataBinder wdb){
        wdb.registerCustomEditor(Date.class, new CustomDateEditor(new SimpleDateFormat("yyyy-MM-dd"), true));
    }
}

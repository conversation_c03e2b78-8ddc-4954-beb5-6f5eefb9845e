package com.pinshang.qingyun.orderreport.mapper.entry;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.format.DateTimeFormat;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @Date 2019/3/5 16:45
 */
@Data
public class DeliveryOrderEntry {
    /**
     * 订单id
     */
    @ExcelIgnore
    private String id;
    /**
     * 送货日期
     */
    @DateTimeFormat("yyyy-MM-dd")
    @ExcelProperty(value = "送货日期")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date deliveryDate;
    /**
     * 客户编号
     */
    @ExcelProperty(value = "客户编号")
    private String storeCode;
    /**
     * 客户名称
     */
    @ExcelProperty(value = "客户名称")
    private String storeName;
    /**
     * 送货地址
     */
    @ExcelProperty(value = "送货地址")
    private String deliveryAddress;
    /**
     * 订单金额
     */
    @ExcelProperty(value = "订单金额")
    private BigDecimal finalAmount;
    /**
     * 送货员
     */
    @ExcelProperty(value = "送货员")
    private String deliveryManName;

    /**
     * 送货员
     */
    @ApiModelProperty("送货时间要求")
    @ExcelProperty(value = "送货时间要求")
    private String receiveTime;
    /**
     * 客户类型
     */
    @ExcelProperty(value = "客户类型")
    private String storeTypeName;
    /**
     * 督导
     */
    @ExcelProperty(value = "督导")
    private String supervisorName;
    /**
     * 主任
     */
    @ExcelProperty(value = "主任")
    private String officeDirectorName;
    /**
     * 结账客户
     */
    @ExcelProperty(value = "结账客户")
    private String settlementCustomer;
    /**
     * 操作员
     */
    @ExcelProperty(value = "操作员")
    private String operatorName;
    /**
     * 订单编码
     */
    @ExcelProperty(value = "订单编码")
    private String orderCode;
    /**
     * 班组长
     */
    @ExcelProperty(value = "班组长")
    private String teamLeaderName;



}

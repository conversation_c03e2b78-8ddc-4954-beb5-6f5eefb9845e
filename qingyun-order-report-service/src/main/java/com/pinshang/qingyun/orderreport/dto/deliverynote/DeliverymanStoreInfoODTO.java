package com.pinshang.qingyun.orderreport.dto.deliverynote;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;

/**
 * 送货员-客户信息
 */
@Data
@NoArgsConstructor
public class DeliverymanStoreInfoODTO {
	@JsonSerialize(using = ToStringSerializer.class)
	@ApiModelProperty(position = 10, required = true, value = "客户ID")
	private Long storeId;
	@ApiModelProperty(position = 11, required = true, value = "客户编码")
	private String storeCode;
	@ApiModelProperty(position = 12, required = true, value = "客户名称")
	private String storeName;
	@ApiModelProperty(position = 13, required = true, value = "客户类型名称")
	private String storeTypeName;
	@ApiModelProperty(position = 14, required = true, value = "客户线路名称")
	private String storeLineName;
	@ApiModelProperty(position = 15, required = true, value = "客户联系人")
	private String storeLinkman;
	@ApiModelProperty(position = 16, required = true, value = "客户联系人手机号")
	private String linkmanMobile;
	@ApiModelProperty(position = 17, required = true, value = "客户送货地址")
	private String storeDeliveryAddress;

	@ApiModelProperty(position = 21, required = true, value = "距离", hidden = true)
	private Integer distance;
	@ApiModelProperty(position = 22, required = true, value = "距离")
	private String distanceStr;

	@ApiModelProperty(position = 31, required = true, value = "订单数量")
	private Integer orderQuantity;

	@ApiModelProperty(position = 41, required = true, value = "收发次数")
	private Integer sfQuantity;

	public void setDistanceInfo(Integer distance, String distanceStr) {
		this.distance = distance;
		this.distanceStr = distanceStr;
	}

	public Integer getOrderQuantity() {
		return null == orderQuantity ? 0 : orderQuantity;
	}

	public Integer getSfQuantity() {
		return null == sfQuantity ? 0 : sfQuantity;
	}

}

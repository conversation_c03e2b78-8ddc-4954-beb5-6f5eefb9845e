package com.pinshang.qingyun.orderreport.model;

import lombok.Data;

import javax.persistence.Id;
import javax.persistence.Table;
import java.math.BigDecimal;

@Data
@Table(name = "t_commodity")
public class Commodity {

    @Id
    private Long id;

    private String commodityCode;
    private String commodityName;
    private String commoditySpec;
    private Long   commodityFirstKindId;
    private String commodityFirstKindName;
    private Long   commoditySecondKindId;
    private String commoditySecondKindName;
    private Long   commodityThirdKindId;
    private String commodityThirdKindName;
    private String commodityUnitName;
    private Long  commodityFactoryId;
    private String commodityFactoryName;
    private Integer commodityState;
    private Long   commodityPackageId;
    private String commodityPackageName;
    private Integer status;
    private Integer purchaseStatus;
    private Long commodityWorkshopId;
    private String commodityWorkshopName;
    private Integer commodityFlowshopId;
    private String commodityFlowshopName;
    private Long  taxRateId;
    private BigDecimal taxRate;
    private BigDecimal costPrice;
    private BigDecimal firstCost;
    private BigDecimal retailPrice;
    private Integer isWeight;
    private Integer isSummary;
    private Integer isFrame;
    private String  barCode;
    private Integer logisticsModel;
    private Integer batchStatus;
    private Long taxFreeTypeId;
    private Integer sellWeight;
    private Integer sellWeightRange;
    private BigDecimal boxCapacity;
    private BigDecimal salesBoxCapacity;
}

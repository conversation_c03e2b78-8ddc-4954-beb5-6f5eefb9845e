package com.pinshang.qingyun.orderreport.vo;

import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @description 产品销售汇总数据(总)
 * @date 2019/09/03
 */
@Data
public class CommoditySaleStatisticsRespVo {

    /**商品工厂ID(t_factory表主键)**/
    private Long factoryId;

    /**商品工厂名称**/
    private String factoryName;

    /**商品车间ID**/
    private Long flowshopId;

    /**商品车间名称**/
    private String flowshopName;

    /**商品生产组ID(t_factory_workshop表主键)**/
    private Long workshopId;

    /**商品生产组名称**/
    private String workshopName;

    /**商品一级分类ID**/
    private Long cateId1;

    /**商品一级分类名称**/
    private String cateName1;

    /**商品二级分类ID**/
    private Long cateId2;

    /**商品二级分类名称**/
    private String cateName2;

    /**商品三级分类ID**/
    private Long cateId3;

    /**商品三级分类名称**/
    private String cateName3;

    /**商品ID**/
    private Long commodityId;

    /**商品编码**/
    private String commodityCode;

    /**商品名称(由commodityName（commoditySpec）拼接而成)**/
    private String commodityName;

    /**计量单位**/
    private String commodityUnitName;

    /**商品数量**/
    private BigDecimal totalQuantity;

    /**商品金额**/
    private BigDecimal totalAmount;

    /**客户类型详细信息**/
    private List<CommoditySaleStatisticsItemRespVo> commoditySaleStatisticsItems;


    /***
     * 公司id
     */
    private Long companyId;

    /***
     * 税率id
     */
    private String taxRateId;

    /***
     * 税率值
     */
    private String taxRateValue;
}

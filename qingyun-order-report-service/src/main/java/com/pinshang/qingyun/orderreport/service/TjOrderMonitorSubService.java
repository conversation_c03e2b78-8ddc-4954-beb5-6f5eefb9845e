package com.pinshang.qingyun.orderreport.service;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.pinshang.qingyun.orderreport.mapper.OrderItemSyncMapper;
import com.pinshang.qingyun.orderreport.mapper.OrderSyncMapper;
import com.pinshang.qingyun.orderreport.model.OrderItemSync;
import com.pinshang.qingyun.orderreport.model.OrderSync;

import tk.mybatis.mapper.entity.Example;


/**
 * 纯粹为了事务而从TJOrderMonitorService分离出来的服务
 * 
 */
@Service
public class TjOrderMonitorSubService {

	@Autowired
    private OrderSyncMapper orderSyncMapper;
    @Autowired
    private OrderItemSyncMapper orderItemSyncMapper;
	
    /**
     * 
     * @param orderIds
     * @see TJOrderMonitorService#removeTJOrderSync2
     */
    @Transactional
	public void deleteOrderSyncByOrderIds(List<Long> orderIds) {
		Example orderDelExp = new Example(OrderSync.class);
		orderDelExp.createCriteria().andIn("id", orderIds);
		orderSyncMapper.deleteByExample(orderDelExp);
		
		Example orderItemDelExp = new Example(OrderItemSync.class);
		orderItemDelExp.createCriteria().andIn("orderId", orderIds);
		orderItemSyncMapper.deleteByExample(orderItemDelExp);
	}

}

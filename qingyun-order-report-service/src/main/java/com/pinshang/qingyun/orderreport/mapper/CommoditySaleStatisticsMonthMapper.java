package com.pinshang.qingyun.orderreport.mapper;

import com.pinshang.qingyun.base.mybatis.MyMapper;
import com.pinshang.qingyun.orderreport.mapper.entry.CommoditySaleStatisticsMonitorEntry;
import com.pinshang.qingyun.orderreport.model.CommoditySaleStatisticsMonth;
import com.pinshang.qingyun.orderreport.vo.CommoditySaleStatisticsMakeUpReqVo;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface CommoditySaleStatisticsMonthMapper extends MyMapper<CommoditySaleStatisticsMonth> {

    int batchInsert(List<CommoditySaleStatisticsMonth> list);

    //查询商品月报汇总数据
    CommoditySaleStatisticsMonitorEntry queryCommodityStatisticsBaseMonth(@Param("vo") CommoditySaleStatisticsMakeUpReqVo vo,@Param("tableName")String tableName);

}

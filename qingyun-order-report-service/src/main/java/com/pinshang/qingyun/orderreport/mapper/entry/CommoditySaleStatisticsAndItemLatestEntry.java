package com.pinshang.qingyun.orderreport.mapper.entry;

import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @description 查询商品销售统计及商品明细数据
 * @date 2019/8/30
 */
@Data
public class CommoditySaleStatisticsAndItemLatestEntry {

    /** t_tj_commodity_sale_statistics表ID **/
    private Long referId;

    /** 订单id **/
    private Long orderId;

    /** 商品id **/
    private Long commodityId;

    /** 商品数量 **/
    private BigDecimal commodityNum;

    /** 商品金额 **/
    private BigDecimal totalPrice;

}

package com.pinshang.qingyun.orderreport.mapper;

import com.pinshang.qingyun.orderreport.vo.*;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;

@Repository
public interface DeliveryListMapper {

    List<DeliveryListRespVo> searchLatestList(DeliveryListReqVo params);
    List<DeliveryListRespVo> searchList(DeliveryListReqVo params);


    DeliveryListPreviewRespVo searchPreviewTitle(@Param("lineId") Long lineId);

    List<StoreVo> queryStoreNameByLineId(@Param("lineId") Long lineId,
                                           @Param("orderDate")String orderDate,
                                           @Param("deliveryBatch")Object deliveryBatch,
                                           @Param("latestFlag")boolean latestFlag,
                                           @Param("orderModeType") Integer orderModeType);


    List<OrderListVo> queryStoreBuyGoodsByOrderDateAndLineGroupId(@Param("lineId")Long lineId,
                                                                  @Param("orderDate")String orderDate,
                                                                  @Param("printBatchId") String printBatchId,
                                                                  @Param("deliveryBatch")Integer deliveryBatchStart,
                                                                  @Param("latestFlag")boolean latestFlag,
                                                                  @Param("orderModeType") Integer orderModeType);


    List<StoreBuyGoods> findGoodsByStoreId(@Param("storeId")Long id,
                                           @Param("orderDate")String orderDate,
                                           @Param("deliveryBatch")Object deliveryBatchStart,
                                           @Param("latestFlag")boolean latestFlag,
                                           @Param("orderModeType") Integer orderModeType);


}

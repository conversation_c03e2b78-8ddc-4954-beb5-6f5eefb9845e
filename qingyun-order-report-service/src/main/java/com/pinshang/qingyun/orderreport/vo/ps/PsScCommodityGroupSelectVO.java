package com.pinshang.qingyun.orderreport.vo.ps;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @Version 0.1.0
 * @Date 2021/1/28 11:11
 * @Copyright © 2019-2021 qm
 * @Description -- 类说明
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
public class PsScCommodityGroupSelectVO {

    @ApiModelProperty(value = "商品组id")
    private String commodityGroupId;

    @ApiModelProperty(value = "商品组编码")
    private String commodityGroupCode;

    @ApiModelProperty(value = "商品组名称")
    private String commodityGroupName;
}

package com.pinshang.qingyun.orderreport.model;

import com.pinshang.qingyun.base.po.BaseIDPO;
import com.pinshang.qingyun.product.dto.sync.StoreODTO;
import lombok.Data;

import javax.persistence.Table;

/**
 * 订单落地数据
 */
@Table(name = "t_tj_order_mirror")
@Data
public class OrderMirror extends BaseIDPO{
    //订单ID
    private Long orderId;
    //订单编号
    private String orderCode;
    //送货员
    private Long deliverymanId;
    private String deliverymanName;
    //销售员
    private Long salesmanId;
    private String salesmanName;
    //督导
    private Long supervisorId;
    private String supervisorName;
    //大区经理
    private Long regionManagerId;
    private String regionManagerName;
    //主任
    private Long officeDirectorId;
    private String officeDirectorName;

    public static OrderMirror syncConvert(StoreODTO dto,Long orderId) {
        OrderMirror mirror = new OrderMirror();
         mirror.setDeliverymanId(dto.getDeliverymanId());
         mirror.setDeliverymanName(dto.getDeliverymanName());
         mirror.setSalesmanId(dto.getSalesmanId());
         mirror.setSalesmanName(dto.getSalesmanName());
         mirror.setSupervisorId(dto.getSupervisorId());
         mirror.setSupervisorName(dto.getSupervisorName());
         mirror.setRegionManagerId(dto.getRegionManagerId());
         mirror.setRegionManagerName(dto.getRegionManagerName());
         mirror.setOfficeDirectorId(dto.getOfficeDirectorId());
         mirror.setOfficeDirectorName(dto.getOfficeDirectorName());
         if(orderId!=null){
             mirror.setOrderId(orderId);
         }
        return mirror;
    }

}

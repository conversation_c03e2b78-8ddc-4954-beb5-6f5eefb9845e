package com.pinshang.qingyun.orderreport.model;

import com.pinshang.qingyun.base.po.BaseIDPO;
import com.pinshang.qingyun.box.utils.SpringUtil;
import com.pinshang.qingyun.order.dto.orderStatistics.OrderListGiftODTO;
import lombok.Data;

import javax.persistence.Id;
import javax.persistence.Table;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2019/1/14 15:49.
 */
@Data
@Table(name = "t_tj_order_list")
public class OrderItem extends BaseIDPO {

    /** 订单id **/
    private Long orderId;
    /** 商品id **/
    private Long commodityId;
    /** 商品数量 **/
    private BigDecimal commodityNum;
    /** 单价 **/
    private BigDecimal commodityPrice;
    /**
     * 促销前的源始金额
     */
    private BigDecimal totalPrice;
    /** 类型 **/
    private Integer type;
    /** 备注 **/
    private String remark;

    /**
     * 商品类型-1-非组合 2-组合  3-组合子品
     */

    private Integer combType;
    /**
     * 属于组合商品id
     */
    private Long combCommodityId;

    public OrderItemLatest convert() {
        OrderItemLatest result = new OrderItemLatest();
        SpringUtil.copyProperties(this, result);
        return result;
    }

    public static OrderItem syncConvert(OrderListGiftODTO dto) {
        OrderItem orderItem = new OrderItem();
        SpringUtil.copyProperties(dto,orderItem);
        return orderItem;
    }
    public String toSyncString() {
        if(null != commodityNum){
            commodityNum = commodityNum.stripTrailingZeros();
        }
        if(null != totalPrice){
            totalPrice = totalPrice.stripTrailingZeros();
        }
        if(null != commodityPrice){
            commodityPrice = commodityPrice.stripTrailingZeros();
        }
        return
                "orderId=" + orderId +
                        ", commodityId=" + commodityId +
                        ", commodityNum=" + commodityNum +
                        ", totalPrice=" + totalPrice +
                        ", commodityPrice=" + commodityPrice +
                        ", type=" + type +
                        ", combType=" + combType +
                        ", combCommodityId=" + combCommodityId ;
    }
}

package com.pinshang.qingyun.orderreport.mapper.entry;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2024/4/19 14:23
 */
@Data
public class ProductSaleStatisticsCommodityInfoListEntry {

    private String commodityId;
    @ApiModelProperty(value = "商品编码")
    private String commodityCode;

    @ApiModelProperty(value = "商品名称")
    private String commodityName;

    @ApiModelProperty(value = "商品规格")
    private String commoditySpec;

    @ApiModelProperty(value = "单位名称")
    private String commodityUnitName;

    @ApiModelProperty(value = "订货数量")
    private Integer commodityQuantity;

    @ApiModelProperty(value = "品类")
    private String categoryName;
    private String categoryIds;
}

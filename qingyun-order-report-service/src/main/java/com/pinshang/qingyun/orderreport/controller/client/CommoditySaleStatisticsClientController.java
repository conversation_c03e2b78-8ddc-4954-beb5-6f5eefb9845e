package com.pinshang.qingyun.orderreport.controller.client;

import com.pinshang.qingyun.orderreport.dto.LatestCommoditySaleStatisticsIDTO;
import com.pinshang.qingyun.orderreport.dto.LatestCommoditySaleStatisticsODTO;
import com.pinshang.qingyun.orderreport.service.CommoditySaleStatisticsService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/12/28 15:03
 */
@RestController
@RequestMapping("/statistics/client")
public class CommoditySaleStatisticsClientController {
    @Autowired
    private CommoditySaleStatisticsService commoditySaleStatisticsService;

    @PostMapping("/queryLatestCommoditySaleTopN")
    public List<LatestCommoditySaleStatisticsODTO> queryLatestCommoditySaleTopN(@RequestBody LatestCommoditySaleStatisticsIDTO idto){
        return commoditySaleStatisticsService.queryLatestCommoditySaleTopN(idto);
    }
}
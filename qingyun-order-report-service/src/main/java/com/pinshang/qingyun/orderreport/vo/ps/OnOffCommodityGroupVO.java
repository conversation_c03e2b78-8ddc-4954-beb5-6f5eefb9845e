package com.pinshang.qingyun.orderreport.vo.ps;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @Version 0.1.0
 * @Date 2021/1/25 14:05
 * @Copyright © 2019-2021 qm
 * @Description -- 类说明
 **/
@Data
public class OnOffCommodityGroupVO {
    @ApiModelProperty(value = "商品组id，后台自动判断启用/停用", example = "商品组id", required = true)
    private Long groupId;

    @ApiModelProperty(value = "创建人id", example = "123456", required = true, hidden = true)
    private Long createId;
}

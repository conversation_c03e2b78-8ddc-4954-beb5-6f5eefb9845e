package com.pinshang.qingyun.orderreport.mapper.entry;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 产品销售汇总
 */
@Data
public class ProductSaleStatisticsTempEntry {

    //工厂
    private String factoryName;

    //生产组
    private String workshopName;

    //商品编码
    private String commodityCode;

    //商品名称
    private String commodityName;

    //数量
    private BigDecimal commodityNum;

    //金额
    private BigDecimal totalPrice;

    //客户类型ID
    private Long storeTypeId;

    //公司ID
    private Long companyId;

    //税率ID
    private String taxRateId;

    //税率值
    private String taxRateValue;

    //商品ID
    private Long commodityId;

    //送货日期
    private Date orderTime;

}

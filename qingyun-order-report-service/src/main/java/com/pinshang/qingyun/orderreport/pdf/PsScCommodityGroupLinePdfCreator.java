package com.pinshang.qingyun.orderreport.pdf;

import com.itextpdf.text.*;
import com.itextpdf.text.pdf.PdfPCell;
import com.itextpdf.text.pdf.PdfPTable;
import com.itextpdf.text.pdf.PdfWriter;
import com.pinshang.qingyun.box.utils.DateUtil;
import com.pinshang.qingyun.box.utils.SpringUtil;
import com.pinshang.qingyun.orderreport.config.CustomerProperties;
import com.pinshang.qingyun.orderreport.dto.CommodityGroupLinePreviewODTO;
import com.pinshang.qingyun.orderreport.util.ListUtils;
import com.pinshang.qingyun.orderreport.util.PdfUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.joda.time.DateTime;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.List;

/**
 * 商品组线路打印
 */
@Component
@Slf4j
public class PsScCommodityGroupLinePdfCreator {

    private final CustomerProperties customerProperties;

    private static final float TABLE_WIDTH_A4 = 99;

    /**
     * 多少家客户变为横向打印
     */
    private static final Integer rotateDelimiter = 6;

    /**
     * 纵向每页显示多少行
     */
    private static final Integer pageRows = 45;

    /**
     * 横向每页显示多少行
     */
    private static final Integer horizontalPage = 27;

    private static final Integer A4_WIDTH = 500;

    @Autowired
    public PsScCommodityGroupLinePdfCreator(CustomerProperties customerProperties) {
        this.customerProperties = customerProperties;
    }

    /**
     * 生成文件名
     * @param previewData 预览数据.
     * @return 返回文件名
     */
    private String extractFileName(CommodityGroupLinePreviewODTO previewData) {
        String lineCode = previewData.getLineCode();
        String orderTime = previewData.getOrderTime();
        return "/PsSc_" + lineCode +  "_" + orderTime + "_" + UUID.randomUUID().toString().substring(0,8) + ".pdf";
    }

    /**
     * 生成PDF 表头信息
     * @param previewData 创建PDF所需要的数据,包括了所有的数据
     * @param fontChinese 写入中文所使用的字体
     */
    private PdfPTable proceedTableTitle(CommodityGroupLinePreviewODTO previewData, Font fontChinese,int pageNo,int totalPage) throws IOException, DocumentException {
        PdfPTable tableHeader = new PdfPTable(3);
        tableHeader.setWidthPercentage(TABLE_WIDTH_A4);
        tableHeader.setHorizontalAlignment(Element.ALIGN_CENTER);
        tableHeader.setTotalWidth(A4_WIDTH);
        tableHeader.setLockedWidth(true);

        PdfPCell title = new PdfPCell(new Paragraph(previewData.getCommodityGroupName()+"发货清单 ", PdfUtils.chineseFont(18)));
        title.setColspan(3);
        title.setBorder(0);
        title.setHorizontalAlignment(Element.ALIGN_CENTER);
        title.setVerticalAlignment(Element.ALIGN_CENTER);
        title.setFixedHeight(27);
        tableHeader.addCell(title);

        title = new PdfPCell(new Paragraph( "第" + pageNo + "页 共" + totalPage + "页", PdfUtils.chineseFont(8)));
        title.setColspan(3);
        title.setBorder(0);
        title.setFixedHeight(27);
        title.setHorizontalAlignment(Element.ALIGN_RIGHT);
        tableHeader.addCell(title);

        PdfPCell cell = new PdfPCell(new Paragraph("线路: " + previewData.getLineName(), fontChinese));
        cell.setBorder(0);
        tableHeader.addCell(cell);

        cell = new PdfPCell(new Paragraph("车牌: " + previewData.getLicensePlate(), fontChinese));
        cell.setBorder(0);
        tableHeader.addCell(cell);

        cell = new PdfPCell(new Paragraph("送货员: " + previewData.getDeliverymanName(), fontChinese));
        cell.setBorder(0);
        tableHeader.addCell(cell);

        cell = new PdfPCell(new Paragraph("电话:" + previewData.getPhone(), fontChinese));
        cell.setBorder(0);
        tableHeader.addCell(cell);

        cell = new PdfPCell(new Paragraph("送货日期:" + previewData.getOrderTime(), fontChinese));
        cell.setBorder(0);
        tableHeader.addCell(cell);

        cell = new PdfPCell(new Paragraph("打印时间:" + DateUtil.getDateFormate(new Date(),"yyyy-MM-dd HH:mm"), fontChinese));
        cell.setBorder(0);
        tableHeader.addCell(cell);

        return tableHeader;
    }

    private PdfPTable proceedTableHeader(Boolean horizontal,List<List<String>> tableHeader, float[] cellWidth, Font fontChinese){
        List<String> header1 = tableHeader.get(0);
        List<String> header2 = tableHeader.get(1);
        int size = header2.size();
        int width = 0;
        cellWidth[0] = 0;
        cellWidth[1] = 0;
        if(horizontal){
            width = 450/(size-3);
            cellWidth[0] = (width+30) < 62 ? 62 : (width+30);
            cellWidth[1] = (width+10) < 40 ? 40 : (width+10);
        }else{
            width = 250/(size-3) > 60 ? 60 : 250/(size-3);
            cellWidth[0] = (width+50) < 72 ? 72 : (width+50);
            cellWidth[1] = (width+15) < 40 ? 40 : (width+15);
        }
        for (int i = 2; i < size; i++) {
            cellWidth[i] = width;
            if(i == header2.size() - 1){
                cellWidth[i] = width+10;
            }
        }

        PdfPTable table = new PdfPTable(cellWidth);
        table.setWidthPercentage(TABLE_WIDTH_A4);
        table.setHorizontalAlignment(Element.ALIGN_CENTER);
        try {
            for (int i = 0; i < header1.size(); i++) {
                String headerStr = header1.get(i);
                headerStr = headerStr.replace("）", ")").replace("（", "(").replace(" ","");
                if(headerStr.length()>20){
                    headerStr = headerStr.substring(0,20);
                }
                PdfPCell cell = new PdfPCell(new Phrase(headerStr, fontChinese));
                cell.setUseAscender(true);
                cell.setHorizontalAlignment(PdfPCell.ALIGN_LEFT);
                cell.setVerticalAlignment(PdfPCell.ALIGN_MIDDLE);
                if (i<header1.size()-1) {
                    cell.setColspan(2);
                }
                table.addCell(cell);
            }
        }catch (Exception e){
            log.error("生成表头异常");
        }
        for (String headerStr : header2) {
            PdfPCell cell = new PdfPCell(new Phrase(headerStr, fontChinese));
            table.addCell(cell);
        }
        return table;
    }

    /**
     * 生成PDF
     * @param previewDataList 要生成PDF的内容.
     * @return 文件生成路径
     */
    public String batchCreatePDF(List<CommodityGroupLinePreviewODTO> previewDataList) {
        //是否横向打印,默认为否
        boolean horizontal = false;
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");
        String fileName = null;
        if (CollectionUtils.isEmpty(previewDataList)) {
            return "";
        }
        String pdfSave = customerProperties.getAbsoluteSavePath();
        String datePath = format.format(new DateTime().plusDays(1).toDate());
        String savePath = customerProperties.getSavePath();
        File file = new File(pdfSave + "/" +savePath + "/" +datePath);
        if (!file.exists()) {
            boolean mkdirs = file.mkdirs();
            if (!mkdirs) {
                log.error("创建pdf保存目录时异常,请检查权限");
                return "";
            }
        }
        Rectangle rect = new Rectangle(PageSize.A4);
        Map<Long,String> storeMap = previewDataList.get(0).getStoreMap();
        if(SpringUtil.isEmpty(storeMap)){
            return "";
        }
        Document document;
        if (storeMap.keySet().size() <= rotateDelimiter) {
            document = new Document();
        } else {
            horizontal = true;
            document = new Document(rect.rotate());
        }
        Font fontChinese;
        try {
            fileName = extractFileName(previewDataList.get(0));
            FileOutputStream out = new FileOutputStream(file.getAbsolutePath() + fileName);
            fontChinese = PdfUtils.chineseFont(11);
            PdfWriter.getInstance(document, out);// 文档输出流。
            document.setMargins(15, 15, 10, 10);
            document.open();

            int page = 1;
            int totalPage = 0;
            for(CommodityGroupLinePreviewODTO previewData : previewDataList) {
                List<List<List<String>>> pageList;
                if (horizontal) {
                    pageList = ListUtils.splitList(previewData.getTableData(), horizontalPage);
                } else {
                    pageList = ListUtils.splitList(previewData.getTableData(), pageRows);
                }
                totalPage = totalPage+pageList.size();
            }

            for(CommodityGroupLinePreviewODTO previewData :  previewDataList){
                List<List<List<String>>> pageList;
                if (horizontal) {
                    pageList = ListUtils.splitList(previewData.getTableData(), horizontalPage);
                } else {
                    pageList = ListUtils.splitList(previewData.getTableData(), pageRows);
                }
                float[] cellWidth = new float[previewData.getTableHeader().get(1).size() ];
                for (List<List<String>> lists : pageList) {
                    //标题
                    PdfPTable tableTitle = proceedTableTitle(previewData, fontChinese,page,totalPage);
                    document.add(tableTitle);
                    //表头
                    PdfPTable tableHeaderPdf = proceedTableHeader(horizontal,previewData.getTableHeader(), cellWidth, fontChinese);
                    document.add(tableHeaderPdf);
                    PdfPTable table = new PdfPTable(cellWidth);
                    table.setWidthPercentage(TABLE_WIDTH_A4);
                    table.setHorizontalAlignment(Element.ALIGN_CENTER);

                    //处理每一行
                    for (List<String> list : lists) {
                        for (int i = 0; i < list.size(); i++) {
                            String cellStr = list.get(i);
                            Font font = i==1?PdfUtils.chineseFont(7):fontChinese;
                            PdfPCell cell = new PdfPCell(new Phrase(cellStr, font));
                            if (i==0 || i==1) {
                                int len = horizontal ? (int)cellWidth[0]/8 : (int)cellWidth[0]/10;
                                if (i==0 && cellStr.length() > len) {
                                    cellStr = cellStr.replace("）", ")").replace("（", "(").replace(" ","");
                                    cellStr = cellStr.substring(0,len);
                                }
                                int len1 = horizontal ? (int)cellWidth[1]/5 : (int)cellWidth[1]/5;
                                if (i==1) {
                                    cellStr = cellStr.replace(")", "").replace("）", "").replace("(", "").replace("（", "").replace(" ","");
                                    if(cellStr.length() > len1){
                                        cellStr = cellStr.substring(0,len1);
                                    }
                                }
                                cell = new PdfPCell(new Phrase(cellStr, fontChinese));
                            }
                            cell.setNoWrap(true);
                            table.addCell(cell);
                        }
                    }
                    document.add(table);
                    page++;
                    if (page <= totalPage) {
                        document.newPage();
                    }
                }
            }
            document.close();
        } catch (DocumentException | IOException e) {
            log.error("生成PDF内容时异常:{}", e.getMessage());
        }
        return savePath + "/" +datePath + "/" +fileName;
    }
}

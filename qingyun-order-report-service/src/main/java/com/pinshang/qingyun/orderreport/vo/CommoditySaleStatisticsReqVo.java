package com.pinshang.qingyun.orderreport.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.pinshang.qingyun.base.page.Pagination;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @description 产品销售汇总查询条件
 * @date 2019/09/03
 */
@Data
public class CommoditySaleStatisticsReqVo extends Pagination {

    /**商品工厂ID(t_factory表主键)**/
    private Long factoryId;

    /**商品生产组ID(t_factory_workshop表主键)**/
    private Long workshopId;

    /**商品一级分类ID**/
    private Long cateId1;

    /**商品Id**/
    private Long commodityId;

    /**商品车间ID**/
    private Long flowshopId;

    /**送货日期-开始 日报**/
    @JsonFormat(pattern="yyyy-MM-dd",timezone = "GMT+8")
    private Date startDate;

    /**送货日期-结束 日报**/
    @JsonFormat(pattern="yyyy-MM-dd",timezone = "GMT+8")
    private Date endDate;

    /**送货日期-开始 月报**/
    private String startDateStr;

    /**送货日期-结束 月报**/
    private String endDateStr;

    /**产品销售汇总(商品) 和 产品销售汇总(月报) 接口共用，类型区分**/
    private Integer paramType;

    /**
     * 督导
     */
    private Long supervisorId;

    private List<Long> commodityIds;

    /***
     * 公司id
     */
    private Long companyId;


    @JsonIgnore
    private List<Long> factoryIds;
    @ApiModelProperty(value = "登录用户ID", hidden = true)
    private Long loginUserId;

    public void setFactoryIds(List<Long> factoryIds) {
        if(CollectionUtils.isNotEmpty(factoryIds)){
            this.factoryIds = factoryIds;
        }
    }
}

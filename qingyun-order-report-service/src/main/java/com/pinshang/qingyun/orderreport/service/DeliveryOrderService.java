package com.pinshang.qingyun.orderreport.service;

import java.io.File;
import java.io.FileNotFoundException;
import java.io.FileOutputStream;
import java.text.ParseException;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import com.itextpdf.text.Document;
import com.itextpdf.text.DocumentException;
import com.itextpdf.text.PageSize;
import com.itextpdf.text.pdf.PdfWriter;
import com.pinshang.qingyun.common.dto.DictionaryODTO;
import com.pinshang.qingyun.common.service.DictionaryClient;
import com.pinshang.qingyun.orderreport.config.CustomerProperties;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RBucket;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.pinshang.qingyun.base.api.QYApplicationContext;
import com.pinshang.qingyun.base.configure.expand.QYAssert;
import com.pinshang.qingyun.base.constant.RedisKeyPrefixConst;
import com.pinshang.qingyun.base.enums.PrinterDataGroupTypeEnum;
import com.pinshang.qingyun.box.utils.DateUtil;
import com.pinshang.qingyun.box.utils.SpringUtil;
import com.pinshang.qingyun.box.utils.TimeUtil;
//import com.pinshang.qingyun.common.service.PrintClient;
import com.pinshang.qingyun.order.dto.EmployeePrinterODTO;
import com.pinshang.qingyun.order.dto.StorePrinterODTO;
import com.pinshang.qingyun.order.dto.SystemPrinterODTO;
//import com.pinshang.qingyun.order.search.dto.DeliveryOrderDetailRespDTO;
//import com.pinshang.qingyun.order.search.dto.DeliveryOrderReqDTO;
//import com.pinshang.qingyun.order.search.service.EsDeliveryOrderClient;
import com.pinshang.qingyun.order.service.SystemPrinterClient;
import com.pinshang.qingyun.orderreport.mapper.DeliveryOrderMapper;
import com.pinshang.qingyun.orderreport.mapper.OrderItemMapper;
import com.pinshang.qingyun.orderreport.mapper.entry.DeliveryOrderDetailEntry;
import com.pinshang.qingyun.orderreport.mapper.entry.DeliveryOrderEntry;
import com.pinshang.qingyun.orderreport.mapper.entry.DeliveryOrderGiftEntry;
import com.pinshang.qingyun.orderreport.mapper.entry.DeliveryOrderItemPrintEntry;
import com.pinshang.qingyun.orderreport.mapper.entry.DeliveryOrderPrintEntry;
import com.pinshang.qingyun.orderreport.pdf.CreatePdfB5;
import com.pinshang.qingyun.orderreport.pdf.CreatePdfLetter;
import com.pinshang.qingyun.orderreport.vo.DeliveryOrderDetailRespVo;
import com.pinshang.qingyun.orderreport.vo.DeliveryOrderPrintVo;
import com.pinshang.qingyun.orderreport.vo.DeliveryOrderReqVo;
import com.pinshang.qingyun.print.service.PrinterTaskClient;
import com.pinshang.qingyun.store.dto.storeCompany.StoreCompanyNameODTO;
import com.pinshang.qingyun.store.service.StoreCompanyClient;

import lombok.extern.slf4j.Slf4j;

import javax.annotation.PostConstruct;

/**
 * <AUTHOR>
 * @Date 2019/3/5 16:58
 */
@Service
@Slf4j
public class DeliveryOrderService {

    @Autowired
    private DeliveryOrderMapper deliveryOrderMapper;
    @Autowired
    private OrderItemMapper orderItemMapper;
    @Autowired
    private SystemPrinterClient systemPrinterClient;
//    @Autowired
//    private PrintClient printClient;
    @Autowired
    private PrinterTaskClient printerTaskClient;
    @Autowired
    private RedissonClient redissonClient;
    @Autowired
    private DictionaryClient dictionaryClient;
//    @Autowired
//    private EsDeliveryOrderClient esDeliveryOrderClient;

    @Autowired
    private StoreCompanyClient storeCompanyClient;
    @Autowired
    private CreatePdfB5 createPdfB5;
    @Autowired
    private CreatePdfLetter createPdfLetter;
    @Autowired
    private CommonPdfPrintService commonPdfPrintService;



    public PageInfo<DeliveryOrderEntry> queryList(DeliveryOrderReqVo vo){
        if(vo.getStartOrderDate() != null){
            vo.setLatestFlag(DateUtil.compareDate(DateUtil.getNowDate(), vo.getStartOrderDate()));
        }
        RBucket<Boolean> bucket = redissonClient.getBucket(QYApplicationContext.applicationNameSwitch + RedisKeyPrefixConst.STATISTICS_ES_SWITCH);
        if (bucket.get() == null || !bucket.get()) {
            //查数据库
            return PageHelper.startPage(vo.getPageNo(), vo.getPageSize()).doSelectPageInfo(() -> deliveryOrderMapper.queryList(vo));
        }else {
            QYAssert.isTrue(Boolean.FALSE,"此功能已经废弃");
//            //查es
//            PageInfo<com.pinshang.qingyun.order.search.dto.DeliveryOrderEntry> pageInfo = esDeliveryOrderClient.queryList(BeanCloneUtils.copyTo(vo, DeliveryOrderReqDTO.class));
//            return BeanUtil.copyProperties(pageInfo, DeliveryOrderEntry.class);
            return null;
        }
    }

    public DeliveryOrderDetailRespVo queryDeliveryOrderDetail(Long orderId){
        DeliveryOrderDetailRespVo respVo =null ;
        RBucket<Boolean> bucket = redissonClient.getBucket(QYApplicationContext.applicationNameSwitch + RedisKeyPrefixConst.STATISTICS_ES_SWITCH);
        if (bucket.get() == null || !bucket.get()) {
            DeliveryOrderDetailEntry orderDetailEntry = deliveryOrderMapper.queryDetail(orderId);
            //商品列表
            List<DeliveryOrderGiftEntry> goods = orderItemMapper.findGiftByOrderIdAndType(orderId, 1);
//        if(goods != null && !goods.isEmpty()){
//            goods.forEach(x -> x.setTotalPrice(x.getCommodityPrice().multiply(x.getCommodityNum())));
//        }
            //赠品列表
            List<DeliveryOrderGiftEntry> gifts = orderItemMapper.findGiftByOrderIdAndType(orderId, 2);
//        if(gifts != null && !gifts.isEmpty()){
//            gifts.forEach(x -> x.setTotalPrice(x.getCommodityPrice().multiply(x.getCommodityNum())));
//        }
            //配货列表
            List<DeliveryOrderGiftEntry> ration = orderItemMapper.findGiftByOrderIdAndType(orderId, 3);
//        if(ration != null && !ration.isEmpty()){
//            ration.forEach(x -> x.setTotalPrice(x.getCommodityPrice().multiply(x.getCommodityNum())));
//        }
            //特惠列表
            List<DeliveryOrderGiftEntry> th = orderItemMapper.findGiftByOrderIdAndType(orderId, 5);
            respVo = new DeliveryOrderDetailRespVo(orderDetailEntry, goods, gifts, ration, th);

        }else {
            QYAssert.isTrue(Boolean.FALSE,"此功能已经废弃");
            //查询es
//            DeliveryOrderDetailRespDTO detailRespDTO = esDeliveryOrderClient.queryDeliveryOrderDetail(orderId);
//            respVo = DeliveryOrderDetailRespVo.covert(detailRespDTO);
        }
        return respVo;
    }


//    public String print(String ids, Boolean printType){
//        List<Long> idList = Arrays.stream(ids.split(",")).map(Long::valueOf).collect(Collectors.toList());
//        List<DeliveryOrderPrintEntry> printEntryList = deliveryOrderMapper.query4Print(idList);
//        QYAssert.isTrue(printEntryList != null && !printEntryList.isEmpty(), "未查询到打印相关数据!");
//        for (DeliveryOrderPrintEntry printEntry : printEntryList) {
//            List<DeliveryOrderItemPrintEntry> items = deliveryOrderMapper.queryPrintItems(printEntry.getId());
//            printEntry.setOrderPrintItems(items);
//
//
//        }
//        return null;
//    }


    /**
     * 打印：根据用户设置打印机类型，打印PDF
     * 返回生成PDF需要的Entry，导出送货单使用。
     * @param vo
     * @return
     */
    public List<DeliveryOrderPrintEntry> print(DeliveryOrderPrintVo vo) {
        List<DeliveryOrderPrintEntry> resList = new ArrayList<>();
        List<Long> orderIds = vo.getOrderIds().stream().map(Long::valueOf).collect(Collectors.toList());
        List<DeliveryOrderPrintEntry> printEntryList = deliveryOrderMapper.query4Print(orderIds);
        QYAssert.isTrue(printEntryList != null && !printEntryList.isEmpty(), "未查询到打印相关数据!");
        List<Long> storeIds = printEntryList.stream().map(DeliveryOrderPrintEntry::getStoreId).collect(Collectors.toList());
        List<StoreCompanyNameODTO> storeCompanyNameODTOS = storeCompanyClient.selectComapnyNameByStoreIds(storeIds);
        QYAssert.isTrue(printEntryList != null && !printEntryList.isEmpty(), "客户所属公司为空!");

        printerTaskClient.checkUserPrinter(vo.getUserId());
        Map<Long,StoreCompanyNameODTO> storeCompanyMap = new HashMap<>();
        for (StoreCompanyNameODTO storeCompanyNameODTO : storeCompanyNameODTOS) {
            storeCompanyMap.put(storeCompanyNameODTO.getStoreId(),storeCompanyNameODTO);
        }

        for (DeliveryOrderPrintEntry printEntry : printEntryList) {
            try {
                List<DeliveryOrderItemPrintEntry> items = deliveryOrderMapper.queryPrintItems(printEntry.getId());
                printEntry.setOrderPrintItems(items);
                /**栏位**/
                EmployeePrinterODTO localPrinter = systemPrinterClient.queryEmployeePrinterByDeliveryManCode(printEntry.getDeliverymanCode());
                printEntry.setLocal(localPrinter==null?"-1":localPrinter.getLocal());
                List<StorePrinterODTO> storePrinters = systemPrinterClient.queryStorePrinterByStoreCode(printEntry.getStoreCode());
                printEntry.setMadeDate(SpringUtil.isNotEmpty(storePrinters) && storePrinters.get(0).getMadeDate()!=0 ? true : false);
                resList.add(printEntry);
                StoreCompanyNameODTO storeCompanyNameODTO = storeCompanyMap.get(printEntry.getStoreId());
                if(storeCompanyNameODTO!=null){
                    printEntry.setCompanyName(storeCompanyNameODTO.getCompanyName());
                    printEntry.setCompanyStartsWith(storeCompanyNameODTO.getCompanyStartsWith());
                }else{
                    printEntry.setCompanyName("");
                }
                if(vo.getPrintFlag()){//需要打印
                    QYAssert.isTrue(printEntry.getPrintNumber()!=null,"当前订单打印数量为空！");
                    String printerName = printerTaskClient.findPcNoPrintNameByUserId(vo.getUserId());
                    if(StringUtils.isBlank(printerName)){
                        List<SystemPrinterODTO> systemPrinterList = systemPrinterClient.querySystemPrinterByUserId(vo.getUserId());
                        QYAssert.isTrue(SpringUtil.isNotEmpty(systemPrinterList),"当前用户没有配置打印机！");
                        printerName = systemPrinterList.get(0).getPrintName();
                    }
                    xxPrinter(printEntry,printerName,vo.getUserId());
                }
            } catch (Exception e) {
                log.error("送货单生成PDF异常，订单号：{}",printEntry.getOrderNo(), e);
            }
        }
        return resList;
    }
    private static String filePathDir;

    @Autowired
    private CustomerProperties customerProperties;
    @PostConstruct
    public void init() {
        filePathDir = customerProperties.getAbsoluteSavePath();
    }
    private void xxPrinter(DeliveryOrderPrintEntry printEntry,String printerName,Long userId) throws Exception {
        String dir = "/order/"+ TimeUtil.parseSimpleDateTime(new Date());
        if(filePathDir==null){
            filePathDir ="D:/";
        }
        String path= filePathDir+dir;
        File f = new File(path);
        if(!f.isDirectory()) {
            f.mkdirs();
        }
        String filePath = path+"/"+printEntry.getStoreCode()+"_"+printerName.endsWith("0") + "_" + printEntry.getOrderNo() + ".pdf";
        Document document = null;
        if(printerName.endsWith("0")) {//B5 进入
            document = new Document(PageSize.B5, 40, 10, 25, 0);
        }else{
            document = new Document(PageSize.LETTER, 36.0F, 36.0F, 14f, 36.0F);
        }
        // 生成名为 AsianTest.pdf 的文档
        try {
            String autoPrintRemark="";
            try{
                DictionaryODTO parent = dictionaryClient.getDictionaryByCode("AUTO_PRINT_REMARK");
                autoPrintRemark = parent.getOptionValue();
            }catch (Exception e){

            }
            PdfWriter.getInstance(document, new FileOutputStream(filePath));
            document.open();
            if(printerName.endsWith("0")){
                createPdfB5.execCreatePdfByOrder(document,printEntry, 36, autoPrintRemark);
            }else{
                createPdfLetter.execCreatePdfByOrder(document,printEntry, 37, autoPrintRemark);
            }
            document.close();
            //order_report_TJ_DELIVERY 送货单
            commonPdfPrintService.uploadAndPrintPdf(filePath,"order_report_TJ_DELIVERY",PrinterDataGroupTypeEnum.STATISTICAL_QUERY_DELIVERY,userId);
            //List<String> p = new ArrayList<>();
            //p.add(path);
            //printTaskService.savePrintTasks(p, PrinterDataGroupTypeEnum.STATISTICAL_QUERY_DELIVERY, userId, 1, PrinterTypeEnum.LASER.getCode());
        } catch (Exception e) {
            log.error("送货单打印错误:{}", e);
            throw e;
        }finally {
            try {
                document.close();
            }catch (Exception e){

            }
        }
    }

}

package com.pinshang.qingyun.orderreport.service;

import com.pinshang.qingyun.base.api.TokenInfo;
import com.pinshang.qingyun.base.configure.expand.QYAssert;
import com.pinshang.qingyun.base.util.FastThreadLocalUtil;
import com.pinshang.qingyun.box.utils.SpringUtil;
import com.pinshang.qingyun.box.utils.StringUtil;
import com.pinshang.qingyun.distribution.dto.lattice.LatticeCustomerNumberIDTO;
import com.pinshang.qingyun.distribution.dto.lattice.LatticeCustomerNumberODTO;
import com.pinshang.qingyun.distribution.service.LatticeClient;
import com.pinshang.qingyun.orderreport.dto.DeliveryNoteIDTO;
import com.pinshang.qingyun.orderreport.dto.KeyValueODTO;
import com.pinshang.qingyun.orderreport.dto.deliverynote.DeliverymanStoreInfoODTO;
import com.pinshang.qingyun.orderreport.dto.deliverynote.SelectDeliverymanStoreInfoListIDTO;
import com.pinshang.qingyun.orderreport.dto.storearrival.DistanceInfoODTO;
import com.pinshang.qingyun.orderreport.dto.storearrival.DistanceInfoODTO.DistanceRemarkEnums;
import com.pinshang.qingyun.orderreport.mapper.DeliveryNoteMapper;
import com.pinshang.qingyun.orderreport.mapper.entry.DeliveryNoteEntry;
import com.pinshang.qingyun.orderreport.mapper.entry.DeliveryNoteOrderEntry;
import com.pinshang.qingyun.store.dto.store.SelectStoreCoordinateListIDTO;
import com.pinshang.qingyun.store.dto.store.StoreCoordinateODTO;
import com.pinshang.qingyun.store.service.StoreNewClient;

import org.joda.time.DateTime;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

import java.math.BigDecimal;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @author: chenqiang
 * @time: 2021/9/17 11:03
 */
@Service
public class DeliveryNoteService {
	
	@Autowired
	private LatticeClient latticeClient;
	
	@Autowired
	private StoreNewClient storeNewClient;

    @Resource
    private DeliveryNoteMapper deliveryNoteMapper;
    
    /**
     * 查看当前送货员的送货单
     * @param deliveryNoteIDTO
     * @return
     */
    public DeliveryNoteEntry selectDeliveryNote(DeliveryNoteIDTO deliveryNoteIDTO){
        QYAssert.isTrue(null != deliveryNoteIDTO,"参数有误!");
        QYAssert.isTrue(null != deliveryNoteIDTO.getOrderTime(),"送货日期不能为空");
        if(!new DateTime(deliveryNoteIDTO.getOrderTime()).isBefore(new DateTime().plusDays(2).toDate().getTime()) ||
                new DateTime(deliveryNoteIDTO.getOrderTime()).isBefore(new DateTime().withMillisOfDay(0)) ) {
            QYAssert.isTrue(false, "只能选择T、T+1、T+2!");
        }
        DeliveryNoteEntry deliveryNoteEntry = new DeliveryNoteEntry();
        deliveryNoteEntry.setOrderTime(deliveryNoteIDTO.getOrderTime());
        TokenInfo qy = FastThreadLocalUtil.getQY();
        deliveryNoteEntry.setDeliveryUserName(qy.getEmployeeNumber()+"_"+qy.getRealName());
        List<DeliveryNoteOrderEntry> deliveryNoteOrderEntries = deliveryNoteMapper.selectDeliveryNoteOrderList(deliveryNoteIDTO);
        
        if (SpringUtil.isNotEmpty(deliveryNoteOrderEntries)) {
        	boolean isReorder = Boolean.FALSE;
        	BigDecimal locationLon = deliveryNoteIDTO.getBaiduLongitude();
        	BigDecimal locationLat = deliveryNoteIDTO.getBaiduLatitude();
        	if (null == locationLon || null == locationLat) {
        		deliveryNoteOrderEntries.forEach(entry -> {
					entry.setDistanceInfo(DistanceInfoODTO.MAX_DISTANCE, DistanceRemarkEnums.LOCATION_FAIL.getRemark());
				});
        	} else {
        		List<Long> storeIdList = deliveryNoteOrderEntries.stream().map(DeliveryNoteOrderEntry:: getStoreId).collect(Collectors.toList());
        		Map<Long, StoreCoordinateODTO> storeCoordinateMap = this.getStoreCoordinateMap(storeIdList);
            	if (SpringUtil.isEmpty(storeCoordinateMap)) {
            		deliveryNoteOrderEntries.forEach(entry -> {
    					entry.setDistanceInfo(DistanceInfoODTO.MAX_DISTANCE, DistanceRemarkEnums.STORE_LON_LAT_FAULT.getRemark());
    				});
            	} else {
    				for (DeliveryNoteOrderEntry entry: deliveryNoteOrderEntries) {
						StoreCoordinateODTO storeCoordinate = storeCoordinateMap.get(entry.getStoreId());
						if (null == storeCoordinate) {
							entry.setDistanceInfo(DistanceInfoODTO.MAX_DISTANCE, DistanceRemarkEnums.STORE_LON_LAT_FAULT.getRemark());
						} else {
							DistanceInfoODTO di = DistanceInfoODTO.getFormatDistance(locationLon, locationLat, storeCoordinate.getBaiduLongitude(), storeCoordinate.getBaiduLatitude());
							entry.setDistanceInfo(di.getDistance(), di.getRemark());
							isReorder = Boolean.TRUE;
						}
					}
    			}
        	}
        	
        	// 重新排序
        	if (isReorder) {
        		deliveryNoteOrderEntries.sort((a, b) -> a.getDistance().compareTo(b.getDistance()));
        	}
        }
        
        deliveryNoteEntry.setDeliveryNoteOrderEntryList(deliveryNoteOrderEntries);
        return deliveryNoteEntry;
    }
    
    /**
     * 查询  客户坐标Map
     * 
     * @param storeIdList
     * @return
     */
    private Map<Long, StoreCoordinateODTO> getStoreCoordinateMap(List<Long> storeIdList) {
    	if (SpringUtil.isEmpty(storeIdList)) {
        	return Collections.emptyMap();
        }
    	
    	List<StoreCoordinateODTO> storeCoordinateList = storeNewClient.selectStoreCoordinateList(SelectStoreCoordinateListIDTO.init(storeIdList));
    	if (SpringUtil.isEmpty(storeCoordinateList)) {
    		return Collections.emptyMap();
    	}
    	
    	return storeCoordinateList.stream().collect(Collectors.toMap(StoreCoordinateODTO:: getStoreId, v -> v));
    }
    
    /**
     * 查询  送货员-客户信息 列表
     * 
     * @param idto
     * @return
     */
    public List<DeliverymanStoreInfoODTO> selectDeliverymanStoreInfoList(SelectDeliverymanStoreInfoListIDTO idto) {
    	QYAssert.isTrue(null != idto, "参数不能为空!");
    	
    	QYAssert.isTrue(null != idto.getDeliverymanId(), "送货员标识不能为空!");
    	QYAssert.isTrue(null != idto.getOrderTime(), "送货日期不能为空");
        if(!new DateTime(idto.getOrderTime()).isBefore(new DateTime().plusDays(2).toDate().getTime()) ||
                new DateTime(idto.getOrderTime()).isBefore(new DateTime().withMillisOfDay(0)) ) {
            QYAssert.isTrue(false, "只能选择T、T+1、T+2!");
        }
        
        List<DeliverymanStoreInfoODTO> list = deliveryNoteMapper.selectDeliverymanStoreInfoList(idto);
        if (SpringUtil.isNotEmpty(list)) {
        	List<Long> storeIdList = list.stream().map(DeliverymanStoreInfoODTO:: getStoreId).collect(Collectors.toList());
        	Map<Long, Integer> storeOrderQuantityMap = this.getStoreOrderQuantityMap(idto.getOrderTime(), storeIdList);
        	Map<Long, Integer> storeSfQuantityMap = this.getStoreSfQuantityMap(idto.getOrderTime(), storeIdList);
        	
        	boolean isReorder = Boolean.FALSE;
        	BigDecimal locationLon = idto.getBaiduLongitude();
        	BigDecimal locationLat = idto.getBaiduLatitude();
        	if (null == locationLon || null == locationLat) {
        		list.forEach(entry -> {
					entry.setDistanceInfo(DistanceInfoODTO.MAX_DISTANCE, DistanceRemarkEnums.LOCATION_FAIL.getRemark());
					entry.setOrderQuantity(storeOrderQuantityMap.get(entry.getStoreId()));
					entry.setSfQuantity(storeSfQuantityMap.get(entry.getStoreId()));
				});
        	} else {
        		
        		Map<Long, StoreCoordinateODTO> storeCoordinateMap = this.getStoreCoordinateMap(storeIdList);
            	if (SpringUtil.isEmpty(storeCoordinateMap)) {
            		list.forEach(entry -> {
    					entry.setDistanceInfo(DistanceInfoODTO.MAX_DISTANCE, DistanceRemarkEnums.STORE_LON_LAT_FAULT.getRemark());
    					entry.setOrderQuantity(storeOrderQuantityMap.get(entry.getStoreId()));
    					entry.setSfQuantity(storeSfQuantityMap.get(entry.getStoreId()));
    				});
            	} else {
    				for (DeliverymanStoreInfoODTO entry: list) {
						StoreCoordinateODTO storeCoordinate = storeCoordinateMap.get(entry.getStoreId());
						if (null == storeCoordinate) {
							entry.setDistanceInfo(DistanceInfoODTO.MAX_DISTANCE, DistanceRemarkEnums.STORE_LON_LAT_FAULT.getRemark());
						} else {
							DistanceInfoODTO di = DistanceInfoODTO.getFormatDistance(locationLon, locationLat, storeCoordinate.getBaiduLongitude(), storeCoordinate.getBaiduLatitude());
							entry.setDistanceInfo(di.getDistance(), di.getRemark());
							isReorder = Boolean.TRUE;
						}
						
						entry.setOrderQuantity(storeOrderQuantityMap.get(entry.getStoreId()));
						entry.setSfQuantity(storeSfQuantityMap.get(entry.getStoreId()));
					}
    			}
        	}
        	
        	// 重新排序
        	if (isReorder) {
        		list.sort((a, b) -> a.getDistance().compareTo(b.getDistance()));
        	}
        }
    	
    	return list;
    }
    
    /**
     * 查询  客户订单数量Map
     * 
     * @param orderTime
     * @param storeIdList
     * @return
     */
    private Map<Long, Integer> getStoreOrderQuantityMap(String orderTime, List<Long> storeIdList) {
    	if (StringUtil.isNullOrEmpty(orderTime) || SpringUtil.isEmpty(storeIdList)) {
    		return Collections.emptyMap();
    	}
    	
    	List<KeyValueODTO> list = deliveryNoteMapper.selectStoreOrderQuantityList(orderTime, storeIdList);
    	if (SpringUtil.isEmpty(list)) {
    		return Collections.emptyMap();
    	}
    	
    	return list.stream().collect(Collectors.toMap(KeyValueODTO:: getKeyLong, KeyValueODTO:: getValueInt));
    }
    
    /**
     * 查询  客户收发次数Map
     * 
     * @param orderTime
     * @param storeIdList
     * @return
     */
    private Map<Long, Integer> getStoreSfQuantityMap(String orderTime, List<Long> storeIdList) {
    	if (StringUtil.isNullOrEmpty(orderTime) || SpringUtil.isEmpty(storeIdList)) {
    		return Collections.emptyMap();
    	}
    	
    	LatticeCustomerNumberIDTO idto = new LatticeCustomerNumberIDTO();
    	idto.setDeliveryDate(orderTime);
    	idto.setCustomerIds(storeIdList);
    	List<LatticeCustomerNumberODTO> list = latticeClient.customerNumber(idto);
    	if (SpringUtil.isEmpty(list)) {
    		return Collections.emptyMap();
    	}
    	
    	return list.stream().collect(Collectors.toMap(LatticeCustomerNumberODTO:: getCustomerId, LatticeCustomerNumberODTO:: getNumber));
    }
    
}

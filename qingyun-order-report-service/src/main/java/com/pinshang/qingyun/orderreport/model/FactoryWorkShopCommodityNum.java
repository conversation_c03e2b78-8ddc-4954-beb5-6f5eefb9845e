package com.pinshang.qingyun.orderreport.model;

import com.pinshang.qingyun.base.po.BasePO;
import lombok.Data;

import javax.persistence.Table;
import java.util.Date;

/**
 * 生产组商品排序
 * <AUTHOR>
 * @date 2022年10月10日
 */
@Data
@Table(name="t_factory_workshop_commodity_num")
public class FactoryWorkShopCommodityNum  extends BasePO {

    private Long commodityId;

    private Integer sortNum;

    private String createName;

    private String updateName;
}

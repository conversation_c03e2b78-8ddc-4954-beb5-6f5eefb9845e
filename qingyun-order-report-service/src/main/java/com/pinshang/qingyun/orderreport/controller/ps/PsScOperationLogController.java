package com.pinshang.qingyun.orderreport.controller.ps;

import com.github.pagehelper.PageInfo;
import com.pinshang.qingyun.orderreport.service.ps.PsScOperationLogService;
import com.pinshang.qingyun.orderreport.vo.ps.PsScOperationLogVO;
import com.pinshang.qingyun.orderreport.vo.ps.QueryOperationLogVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @Version 0.1.0
 * @Date 2021/3/1 15:18
 * @Copyright © 2019-2021 qm
 * @Description -- 类说明
 **/
@RestController
@RequestMapping(value = "/ps/operationLog")
@Api(value = "配送-水产-商品组和线路日志api", tags = "operationLog", description = "配送-水产-商品组和线路日志")
public class PsScOperationLogController {
    @Autowired
    private PsScOperationLogService psScOperationLogService;

    @ApiOperation(value = "商品组和线路日志列表", notes = "商品组和线路日志列表")
    @PostMapping("/queryOperationList")
    public PageInfo<PsScOperationLogVO> queryOperationList(@RequestBody QueryOperationLogVO dto) {
        return psScOperationLogService.queryOperationList(dto);
    }
}

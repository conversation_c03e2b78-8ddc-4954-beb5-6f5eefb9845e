package com.pinshang.qingyun.orderreport.controller;

import com.pinshang.qingyun.orderreport.mapper.entry.XdaStoreOrderAmountEntry;
import com.pinshang.qingyun.orderreport.service.XdaStoreOrderAmountService;
import com.pinshang.qingyun.orderreport.vo.XdaStoreOrderAmountVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * @author: chenqiang
 * @time: 2021/9/28 10:23
 */
@RestController
@RequestMapping(value = "/statistical/xdaStoreOrderAmount")
public class XdaStoreOrderAmountController {

    @Autowired
    private XdaStoreOrderAmountService xdaStoreOrderAmountService;

    @RequestMapping(value = "/exportXdaStoreOrderAmount",method = RequestMethod.POST)
    public List<XdaStoreOrderAmountEntry> exportXdaStoreOrderAmount(@RequestBody XdaStoreOrderAmountVo vo){
        return xdaStoreOrderAmountService.exportXdaStoreOrderAmount(vo);
    }
}

package com.pinshang.qingyun.orderreport.controller;

import com.alibaba.excel.EasyExcel;
import com.github.pagehelper.PageInfo;
import com.pinshang.qingyun.base.util.ExcelUtil;
import com.pinshang.qingyun.infrastructure.exportfile.cache.starter.FileCacheQuery;
import com.pinshang.qingyun.infrastructure.exportfile.cache.starter.FileCacheQueryParameter;
import com.pinshang.qingyun.orderreport.enums.QyEasyExcelExportTitleEnums;
import com.pinshang.qingyun.orderreport.mapper.entry.ReplenishmentStatisticsEntry;
import com.pinshang.qingyun.orderreport.service.ReplenishmentStatisticsService;
import com.pinshang.qingyun.orderreport.util.QyEasyExcelFactory;
import com.pinshang.qingyun.orderreport.vo.ProductShipmentResponseVo;
import com.pinshang.qingyun.orderreport.vo.ProductShipmentsRequestVo;
import com.pinshang.qingyun.orderreport.vo.ReplenishmentStatisticsVo;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.joda.time.LocalDate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

/**
 * 补货数据汇总Controller
 */
@RestController
@RequestMapping(value = "/statistical/replenishmentStatistics")
@Slf4j
public class ReplenishmentStatisticsController {

    @Autowired
    private ReplenishmentStatisticsService replenishmentStatisticsService;

    /**
     * 补货数据汇总
     * @param vo
     * @return
     */
    @PostMapping("/list")
    @ApiModelProperty(value = "补货数据汇总", notes = "补货数据汇总")
    public PageInfo<ReplenishmentStatisticsEntry> queryList(@RequestBody ReplenishmentStatisticsVo vo) {
        return replenishmentStatisticsService.queryList(vo);
    }

    /**
     * 打印
     */
    @PostMapping("/print")
    public String print(@RequestBody ReplenishmentStatisticsVo vo){
        return replenishmentStatisticsService.print(vo);
    }



    /**
     * 产品发货总量 导出excel
     * @param requestVo 请求参数VO
     * @param response 响应体
     */
    @ApiOperation(value = "补货数据汇总 导出excel", notes = "补货数据汇总 导出excel")
    @RequestMapping(value = "/export", method = RequestMethod.GET)
    @FileCacheQuery(bizCode = "OR_PRODUCT_REPLENISHMENT_E")
    public void exportExcel(@FileCacheQueryParameter ReplenishmentStatisticsVo requestVo, HttpServletResponse response) throws IOException {
        long qS = System.currentTimeMillis();
        requestVo.initExportPage();
        long qE = System.currentTimeMillis();

        PageInfo<ReplenishmentStatisticsEntry> replenishmentStatisticsEntryPageInfo = replenishmentStatisticsService.queryList(requestVo);
        log.info("补货数据汇总-导出--查询时间=" + ((qE - qS) / 1000));
        ExcelUtil.setFileNameAndHead(response, "产品发货总量" + LocalDate.now().toString("yyyyMMdd"));
        QyEasyExcelFactory.write(response,QyEasyExcelExportTitleEnums.ORDER_REPORT_REPLENISHMENT_EXPORT,replenishmentStatisticsEntryPageInfo.getList());

//        try {
//
//        }catch (Exception e){
//            log.error("补货数据汇总导出错误", e);
//            ExcelUtil.setExceptionResponse( response );
//        }
        long eE = System.currentTimeMillis();
        log.info("补货数据汇总-导出--excel处理时间="+ ( (eE -qE) /1000 )  );
    }

}
package com.pinshang.qingyun.orderreport.mapper;

import com.pinshang.qingyun.orderreport.mapper.entry.LogisticsDeliveryOrderAmountStatisticsTempEntry;
import com.pinshang.qingyun.orderreport.vo.LogisticsDeliveryOrderAmountStatisticsVo;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * @description: 物流配送订单金额统计
 * @author: hhf
 * @time: 2022/6/15/015 14:14
 */
@Repository
public interface LogisticsDeliveryOrderAmountStatisticsMapper {

    /**
     * 物流配送订单金额统计列表
     * @return
     */
    List<LogisticsDeliveryOrderAmountStatisticsTempEntry> queryLatestList(LogisticsDeliveryOrderAmountStatisticsVo vo);


    /**
     * 物流配送订单金额统计列表
     * @return
     */
    List<LogisticsDeliveryOrderAmountStatisticsTempEntry> queryList(LogisticsDeliveryOrderAmountStatisticsVo vo);

}

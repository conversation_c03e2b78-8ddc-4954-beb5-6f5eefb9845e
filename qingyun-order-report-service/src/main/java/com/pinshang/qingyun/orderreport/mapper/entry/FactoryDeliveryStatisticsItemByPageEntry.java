package com.pinshang.qingyun.orderreport.mapper.entry;

import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2020/9/2
 */
@Data
public class FactoryDeliveryStatisticsItemByPageEntry {

    /** 客户类型 **/
    private String  storeTypeId;

    /** 客户类型 **/
    private String storeTypeName;

    /** 客户类型数量小计 **/
    private BigDecimal totalQuantity;

    /** 客户类型金额小计 **/
    private BigDecimal totalAmount;

    public FactoryDeliveryStatisticsItemByPageEntry(){}

    public FactoryDeliveryStatisticsItemByPageEntry(String  storeTypeId, String storeTypeName){
        this.storeTypeId = storeTypeId;
        this.storeTypeName = storeTypeName;
    }

}

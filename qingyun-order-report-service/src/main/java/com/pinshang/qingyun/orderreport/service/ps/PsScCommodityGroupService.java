package com.pinshang.qingyun.orderreport.service.ps;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.pinshang.qingyun.base.configure.expand.QYAssert;
import com.pinshang.qingyun.base.enums.log.LogBusinessTypeEnum;
import com.pinshang.qingyun.base.enums.log.LogOperateEnum;
import com.pinshang.qingyun.base.enums.log.LogOperateTypeEnum;
import com.pinshang.qingyun.common.service.CodeClient;
import com.pinshang.qingyun.orderreport.config.OrderReportConstant;
import com.pinshang.qingyun.orderreport.mapper.CommodityMapper;
import com.pinshang.qingyun.orderreport.mapper.PsScCommodityGroupItemMapper;
import com.pinshang.qingyun.orderreport.mapper.PsScCommodityGroupMapper;
import com.pinshang.qingyun.orderreport.mapper.PsScOperationLogMapper;
import com.pinshang.qingyun.orderreport.model.*;
import com.pinshang.qingyun.orderreport.vo.ps.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Version 0.1.0
 * @Date 2021/1/18 10:40
 * @Copyright © 2019-2021 qm
 * @Description -- 类说明
 **/
@Service
@Slf4j
public class PsScCommodityGroupService {
    @Autowired
    private PsScCommodityGroupItemMapper psScCommodityGroupItemMapper;

    @Autowired
    private PsScCommodityGroupMapper psScCommodityGroupMapper;

    @Autowired
    private PsScOperationLogMapper psScOperationLogMapper;

    @Autowired
    private CommodityMapper commodityMapper;

    @Autowired
    private CodeClient codeClient;

    @Autowired
    private RedissonClient redissonClient;

    private String add_commodity_error = "您本次共成功添加%s个商品，失败%s个商品";

    /**
     * 商品组列表
     *
     * @param dto
     * @return
     */
    public PageInfo<PsScCommodityGroupVO> queryCommodityGroupList(QueryCommodityGroupVO dto) {
        if (dto.getCommodityId() != null) {
            List<Long> commodityGroupIdList = psScCommodityGroupItemMapper.selectByCommodityGroupId(dto.getCommodityId());
            dto.setCommodityGroupId(commodityGroupIdList.isEmpty() ? -1L : dto.getCommodityGroupId() == null ? commodityGroupIdList.get(0) : commodityGroupIdList.get(0).equals(dto.getCommodityGroupId()) ? dto.getCommodityGroupId() : -1L);
        }
        return PageHelper.startPage(dto.getPageNo(), dto.getPageSize()).doSelectPageInfo(() -> {
            psScCommodityGroupMapper.queryCommodityGroupList(dto);
        });
    }

    /**
     * 商品组下的商品列表
     *
     * @param dto
     * @return
     */
    public PageInfo<PsScCommodityGroupCommodityVO> queryCommodityGroupCommodityList(QueryCommodityGroupCommodityVO dto) {

        return PageHelper.startPage(dto.getPageNo(), dto.getPageSize()).doSelectPageInfo(() -> {
            psScCommodityGroupMapper.queryCommodityGroupCommodityList(dto);
        });
    }

    /**
     * 删除商品组下的商品
     *
     * @param dto
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public Boolean delCommodityGroupCommodity(DelCommodityGroupCommodityVO dto) {
        QYAssert.notNull(dto.getId(),"删除主键缺失！");
        RLock lock = redissonClient.getLock(OrderReportConstant.DEL_COMMODITY_GROUP_COMMODITY_LOCK);
        try {
            lock.lock(OrderReportConstant.LOCK_EXPIRE_TIME, TimeUnit.SECONDS);
            PsScCommodityGroupItem psScCommodityGroupItem = psScCommodityGroupItemMapper.selectByPrimaryKey(dto.getId());
            QYAssert.notNull(psScCommodityGroupItem,"需要删除的商品组商品不存在！");
            PsScCommodityGroup psScCommodityGroup = psScCommodityGroupMapper.selectByPrimaryKey(psScCommodityGroupItem.getCommodityGroupId());
            psScCommodityGroup.setCommodityQuantity(psScCommodityGroup.getCommodityQuantity() - 1);
            psScCommodityGroupMapper.updateByPrimaryKeySelective(psScCommodityGroup);
            psScCommodityGroupItemMapper.deleteByPrimaryKey(dto.getId());
            delCommodityGroupCommodityLog(psScCommodityGroup, psScCommodityGroupItem, dto.getCreateId());
        } finally {
            lock.unlock();
        }
        return Boolean.TRUE;
    }

    @Transactional(rollbackFor = Exception.class)
    public Boolean addCommodityGroup(AddCommodityGroupVO dto) {
        RLock lock = redissonClient.getLock(OrderReportConstant.ADD_COMMODITY_GROUP_SC_LOCK);
        try {
            lock.lock(OrderReportConstant.LOCK_EXPIRE_TIME, TimeUnit.SECONDS);
            String code = codeClient.createCode(OrderReportConstant.PS_SC_COMMODITY_GROUP);
//            List<String> codeList = codeClient.batchCreateCode(OrderReportConstant.PS_SC_COMMODITY_GROUP, 1);
            QYAssert.notNull(code, "生成商品组编码失败！");
            PsScCommodityGroup psScCommodityGroup = psScCommodityGroupMapper.selectByGroupName(dto.getGroupName());
            QYAssert.isTrue(psScCommodityGroup == null, "您输入的商品组名称已存在");
            psScCommodityGroup = new PsScCommodityGroup(dto.getGroupName(), code, 0L, Boolean.TRUE, dto.getCreateId(), new Date());
            psScCommodityGroupMapper.insert(psScCommodityGroup);
            addCommodityGroupLog(psScCommodityGroup);
        } finally {
            lock.unlock();
        }
        return Boolean.TRUE;
    }

    /**
     * 商品组添加商品
     *
     * @param dto
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public PsResult addCommodityGroupCommodity(AddCommodityGroupCommodityVO dto) {
        PsResult psResult = new PsResult();
        psResult.setSuccess(Boolean.FALSE);
        RLock lock = redissonClient.getLock(OrderReportConstant.ADD_COMMODITY_GROUP_SC_COMMODITY_LOCK);
        try {
            lock.lock(OrderReportConstant.LOCK_EXPIRE_TIME, TimeUnit.SECONDS);
            QYAssert.isTrue(!(dto == null || dto.getGroupId() == null || dto.getCreateId() == null), "参数有误!");
            QYAssert.isTrue(StringUtils.isNotBlank(dto.getCommodityCodes()), "商品编码不能为空!");
            PsScCommodityGroup psScCommodityGroup = psScCommodityGroupMapper.selectByPrimaryKey(dto.getGroupId());
            QYAssert.notNull(psScCommodityGroup, "商品组不存在!");
            String commodityCodes = dto.getCommodityCodes();
            List<String> codesList = Arrays.asList(commodityCodes.split("\n"));
            final Integer commoditySize = 200;
            final Integer codesSize = codesList.size();
            if (null != codesList && codesSize > commoditySize) {
                QYAssert.isTrue(false, "每次最多200个商品!");
            }
            // 去重处理
            codesList = codesList.stream().distinct().collect(Collectors.toList());
            List<CommodityGroupCommodityVO> commodityGroupCommodityList = psScCommodityGroupItemMapper.selectInExistenceByCommodityCodeList(dto.getGroupId(), codesList);
            QYAssert.isTrue(!commodityGroupCommodityList.isEmpty(), String.format(add_commodity_error, 0, codesSize));

//            // 商品表-商品code过滤
//            Example example = new Example(Commodity.class);
//            example.createCriteria().andIn("commodityCode",codesList);
//            List<Commodity> commodityList = commodityMapper.selectByExample(example);
//            QYAssert.isTrue(!CollectionUtils.isEmpty(commodityList), "商品编码不存在!");
//            List<Long> commodityIdList = commodityList.stream().map(Commodity::getId).collect(Collectors.toList());
//
//            // 商品组商品表-商品id过滤
//            Example exampleItem = new Example(PsScCommodityGroupItem.class);
//            example.createCriteria().andIn("commodity_id", commodityIdList);
//            List<PsScCommodityGroupItem> commodityItemList = psScCommodityGroupItemMapper.selectByExample(exampleItem);


            Map<String, Long> codeMap = commodityGroupCommodityList.stream().collect(Collectors.toMap(CommodityGroupCommodityVO::getCommodityCode, CommodityGroupCommodityVO::getCommodityId));
            List<String> codesListError = new ArrayList<>();
            List<PsScCommodityGroupItem> idListSuccess = new ArrayList<>(commodityGroupCommodityList.size());
            List<String> codeListSuccess = new ArrayList<>(commodityGroupCommodityList.size());
            codesList.forEach(code -> {
                if (codeMap.containsKey(code)) {
                    PsScCommodityGroupItem psScCommodityGroupItem = new PsScCommodityGroupItem();
                    psScCommodityGroupItem.setCommodityId(codeMap.get(code));
                    psScCommodityGroupItem.setCommodityGroupId(dto.getGroupId());
                    idListSuccess.add(psScCommodityGroupItem);
                    codeListSuccess.add(code);
                } else {
                    codesListError.add(code);
                }
            });

            if (idListSuccess.isEmpty()) {
                psResult.setMsg(String.format(add_commodity_error, idListSuccess.size(), codesListError.size()));
                return psResult;
            }

            psScCommodityGroupItemMapper.insertList(idListSuccess);

            psScCommodityGroup.setCommodityQuantity(psScCommodityGroup.getCommodityQuantity() + idListSuccess.size());
            psScCommodityGroupMapper.updateByPrimaryKeySelective(psScCommodityGroup);

            addCommodityGroupCommodityLog(codeListSuccess, psScCommodityGroup, dto);

            if (!codesListError.isEmpty()) {
                psResult.setMsg(String.format(add_commodity_error, idListSuccess.size(), codesListError.size()));
                return psResult;
            }
        } finally {
            lock.unlock();
        }
        psResult.setSuccess(Boolean.TRUE);
        return psResult;
    }

    @Transactional(rollbackFor = Exception.class)
    public Boolean modCommodityGroup(ModCommodityGroupVO dto) {
        RLock lock = redissonClient.getLock(OrderReportConstant.MOD_COMMODITY_GROUP_SC_LOCK);
        try {
            lock.lock(OrderReportConstant.LOCK_EXPIRE_TIME, TimeUnit.SECONDS);
            PsScCommodityGroup psScCommodityGroup = psScCommodityGroupMapper.selectByGroupName(dto.getGroupName());
            QYAssert.isTrue(psScCommodityGroup == null, "您修改的商品组名称已存在！");

            PsScCommodityGroup psScCommodityGroupOld = psScCommodityGroupMapper.selectByPrimaryKey(dto.getGroupId());
            QYAssert.notNull(psScCommodityGroupOld, "您修改的商品组不存在！");
            String groupNameOld = psScCommodityGroupOld.getGroupName();
            psScCommodityGroupOld.setGroupName(dto.getGroupName());

            psScCommodityGroupMapper.updateByPrimaryKeySelective(psScCommodityGroupOld);

            modCommodityGroupLog(psScCommodityGroupOld, groupNameOld, dto.getCreateId());
        } finally {
            lock.unlock();
        }
        return Boolean.TRUE;
    }

    @Transactional(rollbackFor = Exception.class)
    public Boolean onOffCommodityGroup(OnOffCommodityGroupVO dto) {
        QYAssert.notNull(dto.getGroupId(), "商品组id不能为空！");
        PsScCommodityGroup psScCommodityGroupOld = psScCommodityGroupMapper.selectByPrimaryKey(dto.getGroupId());
        QYAssert.notNull(psScCommodityGroupOld, "您修改的商品组不存在！");
        Boolean statusOld = psScCommodityGroupOld.getStatus();
        PsScCommodityGroup psScCommodityGroup = new PsScCommodityGroup();
        psScCommodityGroup.setId(psScCommodityGroupOld.getId());
        psScCommodityGroup.setUpdateId(dto.getCreateId());
        psScCommodityGroup.setUpdateTime(new Date());
        psScCommodityGroup.setStatus(!statusOld);
        psScCommodityGroupMapper.updateByPrimaryKeySelective(psScCommodityGroup);

        onOffCommodityGroupLog(psScCommodityGroupOld, statusOld, dto.getCreateId());
        return Boolean.TRUE;
    }

    /**
     * 开启/停用商品组日志
     *
     * @param psScCommodityGroup
     */
    @Transactional(rollbackFor = Exception.class)
    public void onOffCommodityGroupLog(PsScCommodityGroup psScCommodityGroup, Boolean statusOld, Long createId) {
        PsScOperationLog psScOperationLog = new PsScOperationLog(LogBusinessTypeEnum.COMMODITY_GROUP, LogOperateTypeEnum.MOD, statusOld ? LogOperateEnum.OFF : LogOperateEnum.ON, createId,
                psScCommodityGroup.getGroupName(), psScCommodityGroup.getGroupCode(), "status", "商品组状态", statusOld ? "停用商品组" : "启用商品组", statusOld ? "启用" : "停用", statusOld ? "停用" : "启用", new Date()
        );
        psScOperationLogMapper.insert(psScOperationLog);
    }

    /**
     * 修改商品组日志
     *
     * @param psScCommodityGroup
     */
    @Transactional(rollbackFor = Exception.class)
    public void modCommodityGroupLog(PsScCommodityGroup psScCommodityGroup, String operationOld, Long createId) {
        PsScOperationLog psScOperationLog = new PsScOperationLog(LogBusinessTypeEnum.COMMODITY_GROUP, LogOperateTypeEnum.MOD, LogOperateEnum.MOD, createId,
                psScCommodityGroup.getGroupName(), psScCommodityGroup.getGroupCode(), "group_name", "商品组名称", "修改商品组", operationOld, psScCommodityGroup.getGroupName(), new Date()
        );
        psScOperationLogMapper.insert(psScOperationLog);
    }

    /**
     * 新增商品组日志
     *
     * @param psScCommodityGroup
     */
    @Transactional(rollbackFor = Exception.class)
    public void addCommodityGroupLog(PsScCommodityGroup psScCommodityGroup) {
        Date date = new Date();
        List<PsScOperationLog> psScOperationLogList = new ArrayList<>(2);
        PsScOperationLog psScOperationLog1 = new PsScOperationLog(LogBusinessTypeEnum.COMMODITY_GROUP, LogOperateTypeEnum.ADD, LogOperateEnum.ADD, psScCommodityGroup.getCreateId(),
                psScCommodityGroup.getGroupName(), psScCommodityGroup.getGroupCode(), "group_code", "商品组编码", "新增商品组", null, psScCommodityGroup.getGroupCode(), date
        );
        psScOperationLogList.add(psScOperationLog1);
        PsScOperationLog psScOperationLog2 = new PsScOperationLog(LogBusinessTypeEnum.COMMODITY_GROUP, LogOperateTypeEnum.ADD, LogOperateEnum.ADD, psScCommodityGroup.getCreateId(),
                psScCommodityGroup.getGroupName(), psScCommodityGroup.getGroupCode(), "group_name", "商品组名称", "新增商品组", null, psScCommodityGroup.getGroupName(), date
        );
        psScOperationLogList.add(psScOperationLog2);
        psScOperationLogMapper.insertList(psScOperationLogList);
    }

    /**
     * 商品组新增商品日志
     *
     * @param list
     */
    @Transactional(rollbackFor = Exception.class)
    public void addCommodityGroupCommodityLog(List<String> list, PsScCommodityGroup psScCommodityGroup, AddCommodityGroupCommodityVO dto) {
        if (list == null || list.isEmpty()) {
            return;
        }
        List<PsScOperationLog> psScOperationLogList = new ArrayList<>(list.size());
        Date date = new Date();
        list.forEach(code -> {
            PsScOperationLog psScOperationLog = new PsScOperationLog(LogBusinessTypeEnum.COMMODITY_GROUP_ITEM, LogOperateTypeEnum.ADD, LogOperateEnum.APPEND, dto.getCreateId(),
                    psScCommodityGroup.getGroupName(), psScCommodityGroup.getGroupCode(), "commodity_id", "商品编码", "添加商品", null, code, date
            );
            psScOperationLogList.add(psScOperationLog);
        });

        psScOperationLogMapper.insertList(psScOperationLogList);
    }

    /**
     * 删除商品组商品日志
     *
     * @param psScCommodityGroup
     */
    @Transactional(rollbackFor = Exception.class)
    public void delCommodityGroupCommodityLog(PsScCommodityGroup psScCommodityGroup, PsScCommodityGroupItem psScCommodityGroupItem, Long createId) {
        Commodity commodity = commodityMapper.selectByPrimaryKey(psScCommodityGroupItem.getCommodityId());
        if (commodity == null) {
            log.error("商品id为", psScCommodityGroupItem.getCommodityId(), "的商品不存在，商品组删除商品没有记录日志！");
            return;
        }
        PsScOperationLog psScOperationLog = new PsScOperationLog(LogBusinessTypeEnum.COMMODITY_GROUP_ITEM, LogOperateTypeEnum.DEL, LogOperateEnum.DEL, createId,
                psScCommodityGroup.getGroupName(), psScCommodityGroup.getGroupCode(), "commodity_id", "商品编码", "删除商品", commodity.getCommodityCode(), null, new Date()
        );

        psScOperationLogMapper.insert(psScOperationLog);
    }


    /**
     * 记录操作日志
     *
     * @param dto
     */
    @Transactional(rollbackFor = Exception.class)
    public void recordOperateLog(PsScOperationLog dto) {
        psScOperationLogMapper.insert(dto);

    }


}

package com.pinshang.qingyun.orderreport.util;

import java.util.regex.Pattern;

public class NumberUtils {

    private NumberUtils() {

    }

    /**
     * 去掉给定数字多余的0
     * @param s 给定的数字
     * @return 返回去除后的值
     */
    public static String subZeroAndDot(String s) {
        if (s.indexOf(".") > 0) {
            s = s.replaceAll("0+?$", "");//去掉多余的0
            s = s.replaceAll("[.]$", "");//如最后一位是.则去掉
        }
        return s;
    }

    public static boolean isNumeric(String str){
        String regEx = "^[+|-]?[0-9]+[.]?[0-9]*";
        Pattern pattern = Pattern.compile(regEx);
        return pattern.matcher(str).matches();
    }

    public static void main(String[] args) {
        System.out.println(subZeroAndDot("1"));
        System.out.println(subZeroAndDot("10"));
        System.out.println(subZeroAndDot("6.0"));
        System.out.println(subZeroAndDot("20.01066200"));
        System.out.println(subZeroAndDot("603.062500000090"));
        System.out.println(isNumeric("5.4"));
        System.out.println(isNumeric("0.4"));
        System.out.println(isNumeric("6"));
        System.out.println(isNumeric("88"));
        System.out.println(isNumeric("32.64"));
        System.out.println(isNumeric("323132.64132131"));
        System.out.println(isNumeric("测试"));
    }
}
package com.pinshang.qingyun.orderreport.controller;

import com.github.pagehelper.PageInfo;
import com.pinshang.qingyun.orderreport.mapper.entry.StoreOrderListEntry;
import com.pinshang.qingyun.orderreport.mapper.entry.StoreOrderStatisticsEntry;
import com.pinshang.qingyun.orderreport.service.StoreOrderStatisticsService;
import com.pinshang.qingyun.orderreport.vo.StoreOrderStatisticsVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;


/**
 * Created by zxj on 2021/3/8
 */
@RestController
@RequestMapping("/statistical/order")
public class StoreOrderStatisticsController {

    @Autowired
    private StoreOrderStatisticsService storeOrderStatisticsService;

    /**
     * 督导客户订货统计
     * @param vo
     * @return
     */
    @PostMapping("/queryStoreOrderStatistics")
    public StoreOrderStatisticsEntry queryStoreOrderStatistics(@RequestBody StoreOrderStatisticsVo vo){
        return storeOrderStatisticsService.queryStoreOrderStatistics(vo);
    }

}

package com.pinshang.qingyun.orderreport.vo;

import com.pinshang.qingyun.base.page.Pagination;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;

import java.util.Date;
import java.util.List;

/**
 * 产品销售汇总请求参数
 */
@Data
public class ProductSaleStatisticsVo extends Pagination {

    private Long storeSettId;
    private List<Long> storeSettIds;
    private String storeSettName;

    private Long storeId;
    private List<Long> storeIds;
    private String storeCode;

    private Date startDate;

    private Date endDate;

    private Long factoryId;

    private String factoryName;

    private Long workshopId;

    private String workshopName;

    private Long commodityId;

    private List<Long> commodityIdList;

    private String commodityCode;

    private Long storeCompanyId;

    private String storeCompanyName;

    private Long storeTypeId;

    private String storeTypeName;

    private Long supervisorId;

    private String supervisorName;

    private Long regionManagerId;

    private String regionManagerName;

    private Long lineGroupId;

    private String lineGroupName;

    private Long deliveryManId;

    private String deliveryManName;

    private Long cateId1;

    private Long cateId2;

    private Long cateId3;

    private String cateName;

    private Long userId;

    private List<ProductSaleStatisticsTempDateVo> dataTypeList;

    /***
     * 公司id
     */
    private Long companyId;

   // @JsonIgnore
    private List<Long> factoryIds;
    /**
     * 是否根据用户拥有的工厂权限
     */
    private Boolean isPermissionQuery;

    public void setFactoryIds(List<Long> factoryIds) {
        if(CollectionUtils.isNotEmpty(factoryIds)){
            this.factoryIds = factoryIds;
        }
    }


    /**产品销售汇总查询表标识: 1-原先的表:t_tj_order/t_tj_order_list,2-半年数据表:t_tj_order_sync/t_tj_order_list_sync**/
    private Integer queryTableFlag;
}

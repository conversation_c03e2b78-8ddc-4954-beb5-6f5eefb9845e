package com.pinshang.qingyun.orderreport.service;

import com.pinshang.qingyun.base.api.ApiResult;
import com.pinshang.qingyun.base.api.QYApplicationContext;
import com.pinshang.qingyun.base.enums.SmsMessageTypeEnums;
import com.pinshang.qingyun.base.enums.weChat.PlatformTypeEnums;
import com.pinshang.qingyun.base.enums.weChat.WeChatSystemTypeEnums;
import com.pinshang.qingyun.box.utils.ConcurrentDateUtil;
import com.pinshang.qingyun.common.dto.DictionaryODTO;
import com.pinshang.qingyun.common.dto.SmsMessageVo;
import com.pinshang.qingyun.common.service.DictionaryClient;
import com.pinshang.qingyun.weixin.dto.SendTemplateMsgIDTO;
import com.pinshang.qingyun.weixin.dto.SendTemplateMsgKeyValueIDTO;
import com.pinshang.qingyun.weixin.dto.WeiXinSendMessageIDTO;
import com.pinshang.qingyun.weixin.service.WeixinTemplateClient;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RBucket;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * @Author: sk
 * @Date: 2022/3/3
 */
@Slf4j
@Service
public class WeChatSendMessageOrderReportService {

    @Autowired
    private WeixinTemplateClient weixinTemplateClient;
    @Autowired
    private RedissonClient redissonClient;

    @Resource
    private DictionaryClient dictionaryClient;
    /**
     * 发送微信消息
     * @param content
     */
    @Async
    public void sendWeChatMessage(String content){
        //发送微信模板信息
        WeiXinSendMessageIDTO idto = new WeiXinSendMessageIDTO();
        idto.setContent(content);
        idto.setMessageType(SmsMessageTypeEnums.REPORT_INFO_WARN);
        weixinTemplateClient.sendMessageWarning(idto);
    }

    /**
     * 数据告警发消息
     * @param sendTemplateMsg
     */
    public void sendMessage(SendTemplateMsgIDTO sendTemplateMsg){
        StringBuffer sb = new StringBuffer();
        sb.append("告警发送时间:"+ this.getSDF_FULL_DATE_TIME());
        RBucket<Boolean> bucket = redissonClient.getBucket(QYApplicationContext.redisKeyProfile + SmsMessageTypeEnums.REPORT_INFO_WARN.getCode());
        Boolean accessToken = bucket.get();
        if(accessToken != null && accessToken){
            Map<String, SendTemplateMsgKeyValueIDTO> dataMap = new HashMap<>(6);
            dataMap.put("first",new SendTemplateMsgKeyValueIDTO(SmsMessageTypeEnums.REPORT_INFO_WARN.getName(),null));
            dataMap.put("keyword1",new SendTemplateMsgKeyValueIDTO(QYApplicationContext.redisKeyProfile,null));
            dataMap.put("keyword2",new SendTemplateMsgKeyValueIDTO(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()),null));
            dataMap.put("keyword3",new SendTemplateMsgKeyValueIDTO("微信告警",null));
            dataMap.put("remark",new SendTemplateMsgKeyValueIDTO(sb.toString(),null));
            DictionaryODTO weChatPlatformType = dictionaryClient.getDictionaryByCode("XdMessagePlatformType");
            DictionaryODTO weChatSystemType = dictionaryClient.getDictionaryByCode("XdMessageChatSystemType");
            if(null != weChatPlatformType){
                sendTemplateMsg.setPlatformType(Integer.valueOf(weChatPlatformType.getOptionValue()));
            }
            if(null != weChatSystemType){
                sendTemplateMsg.setSystemType(Integer.valueOf(weChatSystemType.getOptionValue()));;
            }
            sendTemplateMsg.setCode(SmsMessageTypeEnums.REPORT_INFO_WARN.getCode());
            sendTemplateMsg.setDataMap(dataMap);
            log.error("调用发短信接口 : weixinTemplateClient.sendMessage(sendTemplateMsg)");
            ApiResult result = weixinTemplateClient.sendMessage(sendTemplateMsg);
            if(!result.getSuccess()){
                log.error("调用发短信接口 失败 code {} Exception {}", result.getCode(), result.getMsg());
            }
        }
    }
    public Boolean sendWeiXinMsg(SmsMessageVo vo) {
        SendTemplateMsgIDTO sendTemplateMsg = new SendTemplateMsgIDTO();
        Map<String, SendTemplateMsgKeyValueIDTO> dataMap = new HashMap<>();
        dataMap.put("first", new SendTemplateMsgKeyValueIDTO(vo.getMessageType().getName(), null));
        dataMap.put("keyword1", new SendTemplateMsgKeyValueIDTO(QYApplicationContext.redisKeyProfile, null));
        dataMap.put("keyword2", new SendTemplateMsgKeyValueIDTO(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()), null));
        dataMap.put("keyword3",new SendTemplateMsgKeyValueIDTO("微信告警",null));
        dataMap.put("remark", new SendTemplateMsgKeyValueIDTO(vo.getContent(), null));
        DictionaryODTO weChatPlatformType = dictionaryClient.getDictionaryByCode("XdMessagePlatformType");
        DictionaryODTO weChatSystemType = dictionaryClient.getDictionaryByCode("XdMessageChatSystemType");
        if(null != weChatPlatformType){
            sendTemplateMsg.setPlatformType(Integer.valueOf(weChatPlatformType.getOptionValue()));
        }
        if(null != weChatSystemType){
            sendTemplateMsg.setSystemType(Integer.valueOf(weChatSystemType.getOptionValue()));;
        }
        sendTemplateMsg.setCode(SmsMessageTypeEnums.REPORT_INFO_WARN.getCode());
        sendTemplateMsg.setDataMap(dataMap);
        ApiResult result = weixinTemplateClient.sendMessage(sendTemplateMsg);
        if (result.getSuccess()) {
            return Boolean.TRUE;
        }
        return Boolean.FALSE;
    }
    public static String getSDF_FULL_DATE_TIME(){
        DateFormat dateFormat = ConcurrentDateUtil.SDF_FULL_DATE_TIME.get();
        return dateFormat.format(new Date());
    }
}

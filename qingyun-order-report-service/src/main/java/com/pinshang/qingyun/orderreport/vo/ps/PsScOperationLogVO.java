package com.pinshang.qingyun.orderreport.vo.ps;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @Version 0.1.0
 * @Date 2021/3/1 15:24
 * @Copyright © 2019-2021 qm
 * @Description -- 类说明
 **/
@Data
public class PsScOperationLogVO {

    @ApiModelProperty(value = "操作时间，格式 yyyy-MM-dd HH:mm:ss", position = 1)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date createTime;

    @ApiModelProperty(value = "操作人", position = 2)
    private String operator;

    @ApiModelProperty(value = "web页面显示的xxx编码", position = 3)
    private String webCode;

    @ApiModelProperty(value = "web页面显示的xxx名称", position = 4)
    private String webName;

    @ApiModelProperty(value = "操作类型，如停用商品组", position = 5)
    private String operationTypeName;

    @ApiModelProperty(value = "操作项，如线路编码", position = 6)
    private String operationColumnName;

    @ApiModelProperty(value = "操作前详情，对应操作列原值", position = 7)
    private String operationOld;

    @ApiModelProperty(value = "操作后详情，对应操作列新值", position = 8)
    private String operationNew;


}

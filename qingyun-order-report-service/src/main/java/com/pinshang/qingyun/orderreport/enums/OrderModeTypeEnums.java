package com.pinshang.qingyun.orderreport.enums;

/**
 * 产品销售汇总 汇总类型
 */
public enum OrderModeTypeEnums {
    不包含补货数据(0,"不包含补货数据"),
    单查补货数据(1,"单查补货数据"),
	包含补货数据(2,"包含补货数据"),

	;
    private Integer code;
    private String name;


    OrderModeTypeEnums(Integer code, String name) {
        this.code = code;
        this.name = name;
    }

    public static OrderModeTypeEnums get(Integer code) {
        for (OrderModeTypeEnums at : OrderModeTypeEnums.values()) {
            if (code.equals(at.getCode())) {
                return at;
            }
        }
        return null;
    }


    public static String getName(Integer code) {
        for (OrderModeTypeEnums at : OrderModeTypeEnums.values()) {
            if (code.equals(at.getCode())) {
                return at.getName();
            }
        }
        return "";
    }


    public Integer getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

}

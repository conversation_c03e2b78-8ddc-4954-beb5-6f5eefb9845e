package com.pinshang.qingyun.orderreport.mapper.entry;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.format.DateTimeFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.pinshang.qingyun.base.configure.codec.DecimalSerializerKeep2;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Date;

/**
 * <AUTHOR>
 * @Date 2019/3/11 10:45
 */
@Data
public class OrderDetailEntry {
    /**
     * 订单日期
     */
    @DateTimeFormat("yyyy-MM-dd")
    @ExcelProperty(value = "订单日期")
    private Date orderTime;
    /**
     *订单编码
     */
    @ExcelProperty(value = "订单编码")
    private String orderCode;
    /**
     *客户编码
     */
    @ExcelProperty(value = "客户编码")
    private String storeCode;
    /**
     *客户名称
     */
    @ExcelProperty(value = "客户名称")
    private String storeName;
    /**
     *商品编码
     */
    @ExcelProperty(value = "商品编码")
    private String commodityCode;
    /**
     *商品名称
     */
    @ExcelProperty(value = "名称")
    private String commodityName;
    /**
     *品类
     */
    @ExcelProperty(value = "品类")
    private String cateName;
    /**
     *规格
     */
    @ExcelProperty(value = "规格")
    private String commoditySpec;
    /**
     *单价
     */
    @ExcelProperty(value = "单价")
    private BigDecimal commodityPrice;
    /**
     *订货数量
     */
    @ExcelProperty(value = "订货数量")
    @JsonSerialize(using = DecimalSerializerKeep2.class)
    private BigDecimal orderGoodsNum;
    /**
     *商品金额
     */
    @ExcelProperty(value = "商品金额")
    @JsonSerialize(using = DecimalSerializerKeep2.class)
    private BigDecimal commodityAmount;
    /**
     *商品备注
     */
    @ExcelProperty(value = "商品备注")
    private String commodityRemark;
    @ExcelProperty(value = "订单所属公司")
    private String companyName;
    /**
     *操作人
     */
    @ExcelProperty(value = "操作人")
    private String operator;
    @ApiModelProperty("订单创建时间")
    @ExcelProperty(value = "订单创建时间")
    private Date orderCreateTime;
    @ApiModelProperty("订单修改时间")
    @ExcelProperty(value = "订单修改时间")
    private Date orderUpdateTime;
    /**部门名称**/
    @ApiModelProperty("客户部门")
    @ExcelProperty(value = "客户部门")
    private String departmentName;
    /**主任**/
    @ApiModelProperty("主任")
    @ExcelProperty(value = "主任")
    private String officeDirectorName;
    /**大区经理**/
    @ApiModelProperty("大区经理")
    @ExcelProperty(value = "大区经理")
    private String regionManagerName;
    /**督导名称**/
    @ApiModelProperty("督导")
    @ExcelProperty(value = "督导")
    private String supervisorName;


    @ExcelIgnore
    private Long companyId;
    public BigDecimal getCommodityPrice() {
        if(commodityPrice!=null){
            commodityPrice=commodityPrice.setScale(2,RoundingMode.UP);
        }
        return commodityPrice;
    }



    public void setCommodityAmount(BigDecimal commodityAmount) {
        if(commodityAmount != null){
            this.commodityAmount = commodityAmount.setScale(2, RoundingMode.HALF_UP);
        }
    }
}

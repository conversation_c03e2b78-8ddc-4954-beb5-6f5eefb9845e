package com.pinshang.qingyun.orderreport.vo.ps;

import com.pinshang.qingyun.base.enums.log.LogBusinessTypeEnum;
import com.pinshang.qingyun.base.page.Pagination;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @Version 0.1.0
 * @Date 2021/3/1 15:40
 * @Copyright © 2019-2021 qm
 * @Description -- 类说明
 **/
@Data
public class QueryOperationLogVO extends Pagination {

    @ApiModelProperty(value = "业务类型：1-商品组管理日志页，2-线路管理日志页", required = true, position = 1)
    private LogBusinessTypeEnum businessType;

    @ApiModelProperty(value = "业务类型id：1-商品组管理日志页，2-线路管理日志页", hidden = true)
    private Integer businessTypeId;

    @ApiModelProperty(value = "操作开始时间", required = true, position = 2)
    private String operationStartTime;

    @ApiModelProperty(value = "操作结束时间", required = true, position = 3)
    private String operationEndTime;

    @ApiModelProperty(value = "操作人", position = 4)
    private Long operatorId;

    @ApiModelProperty(value = "操作类型：1-新增，2-修改，3-启用，4-停用，5-添加，6-删除（新增商品组、修改商品组、启用商品组、停用商品组、添加商品、删除商品）", position = 5)
    private Integer operationType;

    public Integer getBusinessTypeId() {
        return businessType.getCode();
    }
}

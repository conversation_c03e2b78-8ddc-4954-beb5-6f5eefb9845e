package com.pinshang.qingyun.orderreport.mapper;

import com.pinshang.qingyun.base.mybatis.MyMapper;
import com.pinshang.qingyun.orderreport.model.EmployeeUser;
import com.pinshang.qingyun.orderreport.model.Order;
import com.pinshang.qingyun.orderreport.vo.CommoditySaleStatisticsItemRespVo;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;


@Repository
public interface EmployeeUserMapper extends MyMapper<EmployeeUser> {
    List<EmployeeUser> queryEmployeeUser(@Param("userId")Long userId);

    String queryEmployeeUserByEmployeeId(Long employeeId);
}

package com.pinshang.qingyun.orderreport.mapper.entry;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@Data
public class DeliveryOrderPrintEntry {
    private Long id;
    private String orderNo;
    private Date orderTime;
    private BigDecimal payMoney;//商品金额小计
    private BigDecimal totalAmount;// 合计  payMoney + deliveryFees
    private BigDecimal deliveryFees;//配送费小计
    private Integer printNumber;
    private String remark;
    private String createName;
    private Long companyId;
    private String companyName;
    private String companyStartsWith;

    private Long storeId;
    private String storeCode;
    private String storeName;
    private String shopAddress;
    private String mobile;
    private Integer testReport;
    private Integer showPrice;
    private String deliverymanCode;
    private String deliverymanName;
    private String deliverymanMobile;
    private String regionalManager;
    private String regionalManagerMobile;
    private String superintend;
    private String superintendMobile;
    private List<DeliveryOrderItemPrintEntry> orderPrintItems;
    private String local;
    private String lineGroupName;
    private String receiveTime;
    private boolean madeDate;

    public String getDeliverymanMobile() {
        return deliverymanMobile == null?"":"("+deliverymanMobile+")";
    }

    public String getSuperintendMobile() {
        return superintendMobile== null?"":"("+superintendMobile+")";
    }

    public String getRegionalManagerMobile() {
        return regionalManagerMobile== null?"":"("+regionalManagerMobile+")";
    }

    public BigDecimal getDeliveryFees() {
        return deliveryFees == null? BigDecimal.ZERO :deliveryFees;
    }

    public BigDecimal getTotalAmount() {
        return getDeliveryFees().add(getPayMoney());
    }

    public String getReceiveTime() {
        return receiveTime == null ?"":receiveTime;
    }
}

package com.pinshang.qingyun.orderreport.vo.ps;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @Version 0.1.0
 * @Date 2021/1/18 10:51
 * @Copyright © 2019-2021 qm
 * @Description -- 类说明
 **/
@Data
public class PsScCommodityGroupVO {
    @ApiModelProperty(value = "商品组id")
    private String id;

    @ApiModelProperty(value = "商品组名称")
    private String groupName;

    @ApiModelProperty(value = "商品组编码")
    private String groupCode;

    @ApiModelProperty(value = "商品数")
    private Long commodityQuantity;

    @ApiModelProperty(value = "商品组状态:1-启用状态，0-停用状态")
    private Integer status;

    @ApiModelProperty(value = "创建人")
    private String createId;

    @ApiModelProperty(value = "创建人姓名")
    private String createName;

    @ApiModelProperty(value = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

}

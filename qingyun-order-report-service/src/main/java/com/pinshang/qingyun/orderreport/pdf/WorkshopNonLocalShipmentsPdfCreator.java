package com.pinshang.qingyun.orderreport.pdf;

import com.itextpdf.text.*;
import com.itextpdf.text.pdf.PdfPCell;
import com.itextpdf.text.pdf.PdfPTable;
import com.itextpdf.text.pdf.PdfWriter;
import com.pinshang.qingyun.orderreport.config.CustomerProperties;
import com.pinshang.qingyun.orderreport.util.ListUtils;
import com.pinshang.qingyun.orderreport.util.PdfUtils;
import com.pinshang.qingyun.orderreport.util.StrUtils;
import com.pinshang.qingyun.orderreport.vo.WorkshopShipmentsVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.joda.time.DateTime;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.UUID;

/**
 * <AUTHOR>
 * @date 2019/3/4 11:32.
 */
@Component
@Slf4j
public class WorkshopNonLocalShipmentsPdfCreator {

    private static final Logger LOGGER = LoggerFactory.getLogger(WorkshopNonLocalShipmentsPdfCreator.class);

    private static final Integer PAGE_ROW = 42;

    private final CustomerProperties customerProperties;

    /**
     * 多少商品变为横向打印
     * 默认是11号字体 A4纸的情况下, 12家客户换行
     * 设置成15 是因为 第一个格的 商品名称  和最后一个格的 合计
     * 所以要 + 2
     */
    private static final Integer rotateDelimiter = 15;

    /**
     * 横向打印的时候,每页27条
     */
    private static final Integer horizontalPage = 27;

    @Autowired
    public WorkshopNonLocalShipmentsPdfCreator(CustomerProperties customerProperties) {
        this.customerProperties = customerProperties;
    }

    /**
     * 生成PDF
     * @param preview 预览数据
     * @return 返回生成pdf后的相对路径
     */
    public String create(WorkshopShipmentsVo preview) {
        //是否横向打印,默认为否
        boolean horizontal = false;
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String printTime = dateFormat.format(new Date());

        String pdfSave = customerProperties.getAbsoluteSavePath();
        String savePath = customerProperties.getSavePath();
        Date date = new DateTime().plusDays(1).toDate();
        String dataDir = new SimpleDateFormat("yyyy-MM-dd").format(date);
        File file = new File(pdfSave + "/" +savePath + "/" + dataDir);
        if (!file.exists()) {
            boolean mkdirs = file.mkdirs();
            if (!mkdirs) {
                LOGGER.error("创建pdf目录时失败,请检查权限问题");
                return null;
            }
        }

        String fileName = extractFileName(preview);
        try {
            Font font = PdfUtils.chineseFont(11);
            Font numberFont = PdfUtils.chineseFont(8);
            FileOutputStream outputStream = new FileOutputStream(file + "/" + fileName);
            Rectangle rect = new Rectangle(PageSize.A4);
            Document document;
            PdfPTable tableHeader = proceedTableHeader(preview, font);
            List<List<String>> tableDate = preview.getTableDate();
            if (tableDate.get(0).size() > rotateDelimiter) {
                horizontal = true;
                document = new Document(rect.rotate());
            } else {
                document = new Document(rect);
            }
            document.setMargins(15, 15, 15, 15);
            PdfWriter.getInstance(document, outputStream);
            document.open();
            List<List<List<String>>> tableDataList;
            if (horizontal) {
                tableDataList = ListUtils.splitList(tableDate, horizontalPage);
            } else {
                tableDataList = ListUtils.splitList(tableDate, PAGE_ROW);
            }
            List<String> headerString = preview.getTableHeader();

            int colNumber = headerString.size();
            float[] cellWidth = new float[colNumber];
            cellWidth[0] = 117;
            float totalWidth = cellWidth[0];
            // 如果横向打印
            for (int i = 1; i < colNumber; i++) {
                cellWidth[i] = 31;
                totalWidth += cellWidth[i];
            }

            int page = 1;
            int totalPage = tableDataList.size();
            for (List<List<String>> pageData : tableDataList) {
                document.add(tableHeader);
                PdfPTable table = new PdfPTable(cellWidth);
                // 100% 宽度
                table.setWidthPercentage(100);
                table.setHorizontalAlignment(Element.ALIGN_LEFT);
                table.setTotalWidth(totalWidth);
                table.setLockedWidth(true);
                for (String header : headerString) {
                    /* 商品名称+规格, 保留12(对应22个字节)个字,每行2个字共6行 */
                    int commodityNameLength = 22;
                    header = StrUtils.subStr(header, commodityNameLength);
                    PdfPCell cell = new PdfPCell(new Phrase(header, font));
                    cell.setPadding(0);
                    table.addCell(cell);
                }
                for (List<String> row : pageData) {
                    for (int i = 0; i < row.size(); i++) {
                        String cellString = row.get(i);
                        Phrase phrase;
                        if (i == 0) {
                            /* 线路组名称，按9个汉字大小显示(对应18个字节)一行显示完 */
                            int lineGroupNameLength = 18;
                            cellString = StrUtils.subStr(cellString, lineGroupNameLength);
                            phrase = new Phrase(cellString, font);
                        } else {
                            if (cellString.length() > 4) {
                                phrase = new Phrase(cellString, numberFont);
                            } else {
                                phrase = new Phrase(cellString, font);
                            }
                        }
                        table.addCell(phrase);
                    }
                }
                if (page == totalPage) {
                    List<String> tableBottom = preview.getTableBottom();
                    for (int i = 0; i < tableBottom.size(); i++) {
                        String bottom = tableBottom.get(i);
                        Phrase phrase;
                        if (i == 0) {
                            phrase = new Phrase(bottom, font);
                        } else {
                            if (bottom.length() > 4) {
                                phrase = new Phrase(bottom, numberFont);
                            } else {
                                phrase = new Phrase(bottom, font);
                            }
                        }
                        table.addCell(phrase);
                    }
                }
                // 分页的table
                PdfPTable pageTable = new PdfPTable(1);
                pageTable.setHorizontalAlignment(Element.ALIGN_LEFT);
                PdfPCell tableBottom = new PdfPCell(new Phrase("第 " + page + " 页, 共 " + totalPage + "页.                打印时间: " + printTime, font));
                tableBottom.setBorder(0);
                pageTable.addCell(tableBottom);
                page++;
                document.add(table);
                document.add(pageTable);
                if (page <= totalPage) {
                    document.newPage();
                }
            }
            document.close();
        } catch (Exception e) {
            //
            LOGGER.error("生成PDF时异常:{}", e.getMessage());
            return null;
        }
        return String.format("%s%s%s",file.getAbsolutePath(),"/",fileName);
       // return savePath + "/" + dataDir + "/" +fileName;
    }

    /**
     * PDF 文档头信息
     * @param preview 预览数据
     * @param chineseFont 中文字体
     * @return 返回表头
     */
    private PdfPTable proceedTableHeader(WorkshopShipmentsVo preview, Font chineseFont) throws IOException, DocumentException {
        float[] cellWidth = { 200, 110 , 125 , 125};
        PdfPTable table = new PdfPTable(cellWidth.length);
        table.setHorizontalAlignment(Element.ALIGN_LEFT);
        table.setTotalWidth(cellWidth);
        table.setLockedWidth(true);

        PdfPCell title;
        title = new PdfPCell(new Paragraph("", PdfUtils.chineseFont(18)));
        title.setBorder(0);
        title.setFixedHeight(37);
        table.addCell(title);

        title = new PdfPCell(new Paragraph("生产组外地发货单 ", PdfUtils.chineseFont(18)));
        title.setBorder(0);
        title.setColspan(2);
        title.setHorizontalAlignment(Element.ALIGN_LEFT);
        title.setVerticalAlignment(Element.ALIGN_LEFT);
        title.setFixedHeight(37);
        table.addCell(title);

        String orderModeTypeName = preview.getOrderModeTypeName();
        title = new PdfPCell(new Paragraph(StringUtils.isBlank(orderModeTypeName) ? "" : "（"+orderModeTypeName+"）",chineseFont));
        title.setBorder(0);
        title.setFixedHeight(37);
        table.addCell(title);

        PdfPCell cell ;
        String workshopName = preview.getWorkshopName();
        if (StringUtils.isBlank(workshopName)) {
            workshopName = "全部";
        }
        cell = new PdfPCell(new Phrase("生产组: " + workshopName, chineseFont));
        cell.setBorder(0);
        table.addCell(cell);
        String lineGroupName = preview.getLineGroupName();
        if (StringUtils.isBlank(lineGroupName)) {
            lineGroupName = "全部";
        }
        cell = new PdfPCell(new Phrase("线路组: " + lineGroupName, chineseFont));
        cell.setBorder(0);
        table.addCell(cell);
        String deliveryHouse = preview.getDeliveryHouse();
        if (StringUtils.isBlank(deliveryHouse)) {
            deliveryHouse = "全部";
        }

        cell = new PdfPCell(new Phrase("", chineseFont));
        cell.setBorder(0);
        table.addCell(cell);

        cell = new PdfPCell(new Phrase("发货仓库: " + deliveryHouse, chineseFont));
        cell.setBorder(0);
        table.addCell(cell);

        float[] cellWidth1 = { 200, 110 , 125 , 125};
        table.setTotalWidth(cellWidth1);
        table.setLockedWidth(true);

        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
        Date startOrderDate = preview.getStartOrderDate();
        Date endOrderDate = preview.getEndOrderDate();
        String start = dateFormat.format(startOrderDate);
        String end = dateFormat.format(endOrderDate);
        cell = new PdfPCell(new Phrase("送货日期: " + start + " 至 " +end, chineseFont));
        cell.setBorder(0);
        table.addCell(cell);

        String deliveryTime = preview.getDeliveryTime();
        if (StringUtils.isBlank(deliveryTime)) {
            deliveryTime = "全部";
        }
        cell = new PdfPCell(new Phrase("发货时间: " + deliveryTime, chineseFont));
        cell.setBorder(0);
        table.addCell(cell);

        String deliveryBatch = preview.getDeliveryBatch();

        cell = new PdfPCell(new Phrase("配送批次: " + deliveryBatch, chineseFont));
        cell.setBorder(0);
        table.addCell(cell);

        String storeType = preview.getStoreType();

        cell = new PdfPCell(new Phrase("客户类型: " + storeType, chineseFont));
        cell.setBorder(0);
        table.addCell(cell);

        return table;
    }

    /**
     * 抽取文件名
     * @param preview 预览数据
     * @return 返回要生成的文件名
     */
    private String extractFileName(WorkshopShipmentsVo preview) {
        Date startOrderDate = preview.getStartOrderDate();
        Date endOrderDate = preview.getEndOrderDate();
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
        String fileName;
        if (startOrderDate != null && endOrderDate != null) {
            String start = dateFormat.format(startOrderDate);
            String end = dateFormat.format(endOrderDate);
            fileName = start + "-" + end;
        } else {
            Date date = new DateTime().plusDays(1).toDate();
            fileName = dateFormat.format(date);
        }
        return fileName + "_" + UUID.randomUUID().toString().substring(0, 8) + ".pdf";
    }
}

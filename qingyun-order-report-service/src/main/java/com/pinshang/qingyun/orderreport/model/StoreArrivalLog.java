package com.pinshang.qingyun.orderreport.model;

import java.text.SimpleDateFormat;

import javax.persistence.Entity;
import javax.persistence.Table;

import lombok.Data;
import lombok.NoArgsConstructor;

import com.pinshang.qingyun.base.po.BaseSimplePO;
import com.pinshang.qingyun.box.utils.EnumUtils;
import com.pinshang.qingyun.orderreport.dto.storearrival.DistanceInfoODTO;

/**
 * 客户到货日志
 */
@Data
@NoArgsConstructor
@Entity
@Table(name = "t_log_tj_store_arrival")
public class StoreArrivalLog extends BaseSimplePO {
	// 创建人名称
	private String createName;

	// 送货日期：yyyy-MM-dd
	private String orderTime;
	// 线路ID
	private Long lineId;
	// 线路名称
	private String lineName;
	// 送货员ID
	private Long deliverymanId;
	// 送货员名称
	private String deliverymanName;
	// 客户ID
	private Long storeId;
	// 客户名称
	private String storeName;
	// 操作类型：1-新增、2-修改 —— 参见：StoreArrivalLog.OperateTypeEnums
	private Integer operateType;
	// 操作地址
	private String operateAddress;
	// 操作距离（m）
	private Integer operateDistance;
	// 备注（区分定位坐标异常/客户坐标异常）
	private String remark;

	private final static SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
	public StoreArrivalLog(StoreArrival model, String storeName, Integer operateType,
			String operateAddress, DistanceInfoODTO di) {
		this.setId(null);
		this.setCreateId(model.getUpdateId());
		this.setCreateName(model.getUpdateName());
		// this.setCreateTime(model.getUpdateTime());

		this.orderTime = dateFormat.format(model.getOrderTime());
		this.lineId = model.getLineId();
		this.lineName = model.getLineName();
		this.deliverymanId = model.getDeliverymanId();
		this.deliverymanName = model.getDeliverymanName();
		this.storeId = model.getStoreId();
		this.storeName = storeName;
		this.operateType = operateType;
		this.operateAddress = operateAddress;
		if (null != di) {
			this.operateDistance = di.getDistance();
			this.remark = di.getRemark();
		}
	}

	public enum OperateTypeEnums {
		INSERT(1, "新增"), //
		UPDATE(2, "修改"), //
		;
		private Integer code;
		private String name;

		OperateTypeEnums(Integer code, String name) {
			this.code = code;
			this.name = name;
		}

		public String getName() {
			return name;
		}

		public Integer getCode() {
			return code;
		}

		public static OperateTypeEnums get(Integer code) {
			return EnumUtils.fromEnumProperty(OperateTypeEnums.class, "code",
					code);
		}

		public static String getName(Integer code) {
			OperateTypeEnums o = get(code);
			return null == o ? "" : o.name;
		}
	}

}

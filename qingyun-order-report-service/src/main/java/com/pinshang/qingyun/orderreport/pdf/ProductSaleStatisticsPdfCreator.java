package com.pinshang.qingyun.orderreport.pdf;

import com.itextpdf.text.*;
import com.itextpdf.text.pdf.PdfPCell;
import com.itextpdf.text.pdf.PdfPTable;
import com.itextpdf.text.pdf.PdfWriter;
import com.pinshang.qingyun.orderreport.config.CustomerProperties;
import com.pinshang.qingyun.orderreport.mapper.entry.ProductSaleStatisticsTableEntry;
import com.pinshang.qingyun.orderreport.util.ListUtils;
import com.pinshang.qingyun.orderreport.util.PdfUtils;
import com.pinshang.qingyun.orderreport.vo.ProductSaleStatisticsVo;
import lombok.extern.slf4j.Slf4j;
import org.joda.time.DateTime;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.UUID;

/**
 * 产品销售汇总PDF
 */
@Component
@Slf4j
public class ProductSaleStatisticsPdfCreator {

    private final CustomerProperties customerProperties;

    private static final int pageSize = 27;

    @Autowired
    public ProductSaleStatisticsPdfCreator(CustomerProperties customerProperties) {
        this.customerProperties = customerProperties;
    }

    /**
     * 打印 PDF
     * @param data 要打印的数据
     * @return 是否打印成功.
     */
    public String create(ProductSaleStatisticsTableEntry data, ProductSaleStatisticsVo vo) {
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String printTime = dateFormat.format(new Date());
        if (data == null) {
            log.error("要生成PDF的内容为空.");
            return null;
        }

        String pdfSave = customerProperties.getAbsoluteSavePath();
        String savePath = customerProperties.getSavePath();
        Date date = new DateTime().plusDays(1).toDate();
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");
        String dataDir = format.format(date);
        File file = new File(pdfSave + "/" + savePath + "/" + dataDir);
        if (!file.exists()) {
            boolean mkdirs = file.mkdirs();
            if (!mkdirs) {
                log.error("创建保存pdf的目录失败,请检查权限");
                return null;
            }
        }
        String fileName = UUID.randomUUID().toString().substring(0, 8) + ".pdf";
        Rectangle rectangle = new Rectangle(PageSize.A4);
        Document document = new Document(rectangle.rotate());
        document.setMargins(15, 15, 15, 15);
        try {
            FileOutputStream outputStream = new FileOutputStream(file.getAbsolutePath() + "/" + fileName);
            PdfWriter.getInstance(document, outputStream);
            document.open();
            Font font = PdfUtils.chineseFont(8);
            Font smallFont = PdfUtils.chineseFont(6);
            PdfPTable title = proceedTableTitle(font, vo);
            List<List<String>> pdfContent = data.getTableData();
            List<List<List<String>>> lists = ListUtils.splitList(pdfContent, pageSize);
            int page = 1;
            int totalPage = lists.size();

            float[] cellWidth = new float[lists.get(0).get(0).size() ];
            for (int i = 0; i < cellWidth.length; i++) {
                if (i < 6 || (i + 2) >= cellWidth.length) {
                    switch (i) {
                        case 0:
                            cellWidth[i] = 18;  // 工厂
                            break;
                        case 1:
                            cellWidth[i] = 20;  // 生产组
                            break;
                        case 2:
                            cellWidth[i] = 17;  // 产品编码
                            break;
                        case 3:
                            cellWidth[i] = 32;  //产品名称
                            break;
                        case 4:
                        case 5:
                            cellWidth[i] = 14;  //数量小计、金额小计
                            break;
                        default:
                            cellWidth[i] = 17;
                            break;
                    }
                } else {
                    cellWidth[i] = 14;
                }
            }
            for (List<List<String>> pageContent : lists) {
                document.add(title);
                PdfPTable tableHeader = proceedTableHeader(data.getTableHeader(), cellWidth, font);
                document.add(tableHeader);
                PdfPTable table = new PdfPTable(cellWidth);
                table.setWidthPercentage(100);
                for (List<String> rows : pageContent) {
                    for (int i = 0; i < rows.size(); i++) {
                        String cellStr = rows.get(i);
                        PdfPCell cell;
                        if (i >= 6) {
                            if (i >= rows.size()) {
                                if (cellStr.length() >= 9) {
                                    cell = new PdfPCell(new Phrase(cellStr, smallFont));
                                } else {
                                    cell = new PdfPCell(new Phrase(cellStr, font));
                                }
                            } else {
                                if (cellStr.length() > 7) {
                                    cell = new PdfPCell(new Phrase(cellStr, smallFont));
                                } else {
                                    cell = new PdfPCell(new Phrase(cellStr, font));
                                }
                            }
                        } else {
                            if (i == 3) {
                                cellStr = cellStr.replace("（", "(").replace("）", ")");
                            }
                            cell = new PdfPCell(new Phrase(cellStr, font));
                        }
                        cell.setPadding(1);
                        cell.setFixedHeight(16);
                        table.addCell(cell);
                    }
                }
                PdfPCell tableBottom = new PdfPCell(new Phrase("第 " + page + " 页, 共 " + totalPage + "页.                打印时间: " + printTime, font));
                tableBottom.setColspan(cellWidth.length);
                tableBottom.setBorder(0);
                table.addCell(tableBottom);
                page++;
                document.add(table);
                if (page <= totalPage) {
                    document.newPage();
                }
            }
            document.close();
        } catch (DocumentException | IOException e) {
            log.error("生成补货数据汇总PDF时异常:{}",e.getMessage());
            return null;
        }
        return String.format("%s%s%s",file.getAbsolutePath(),"/",fileName);
//        return file.getAbsolutePath() + fileName;
//        return savePath + "/" + dataDir + "/" + fileName;
    }

    /**
     * 生成表格头部信息
     * @param font 中文字体
     * @param vo pdf查询参数
     */
    private PdfPTable proceedTableTitle(Font font, ProductSaleStatisticsVo vo) throws IOException, DocumentException {
        PdfPTable table = new PdfPTable(3);
        table.setWidthPercentage(99);
        PdfPCell cell = new PdfPCell(new Phrase("产品销售汇总",PdfUtils.chineseFont(18)));
        cell.setColspan(3);
        cell.setBorder(0);
        cell.setFixedHeight(32);
        cell.setHorizontalAlignment(Element.ALIGN_CENTER);
        cell.setVerticalAlignment(Element.ALIGN_CENTER);
        table.addCell(cell);

        String settlementName = vo.getStoreSettName() == null ? "" : vo.getStoreSettName();
        cell = new PdfPCell(new Phrase("结账客户: " + settlementName, font));
        cell.setBorder(0);
        table.addCell(cell);

        String storeCode = vo.getStoreCode() == null ? "" : vo.getStoreCode();
        cell = new PdfPCell(new Phrase("客户编码: " + storeCode, font));
        cell.setBorder(0);
        table.addCell(cell);

        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
        String start = dateFormat.format(vo.getStartDate());
        String end = dateFormat.format(vo.getEndDate());
        cell = new PdfPCell(new Phrase("送货日期: " + start + " 至 " + end, font));
        cell.setBorder(0);
        table.addCell(cell);

        String factoryName = vo.getFactoryName() == null ? "" : vo.getFactoryName();
        cell = new PdfPCell(new Phrase("工厂: " + factoryName, font));
        cell.setBorder(0);
        table.addCell(cell);

        String factoryWorkshopName = vo.getWorkshopName() == null ? "" : vo.getWorkshopName();
        cell = new PdfPCell(new Phrase("生产组: " + factoryWorkshopName, font));
        cell.setBorder(0);
        table.addCell(cell);

        String commodityCode = vo.getCommodityCode() == null ? "" : vo.getCommodityCode();
        cell = new PdfPCell(new Phrase("商品编码: " + commodityCode, font));
        cell.setBorder(0);
        table.addCell(cell);

        String companyName = vo.getStoreCompanyName() == null ? "" : vo.getStoreCompanyName();
        cell = new PdfPCell(new Phrase("公司: " + companyName, font));
        cell.setBorder(0);
        table.addCell(cell);

        String typeName = vo.getStoreTypeName() == null ? "" : vo.getStoreTypeName();
        cell = new PdfPCell(new Phrase("客户类型: " + typeName, font));
        cell.setBorder(0);
        table.addCell(cell);

        String supervisorName = vo.getSupervisorName() == null ? "" : vo.getSupervisorName();
        cell = new PdfPCell(new Phrase("督导: " + supervisorName, font));
        cell.setBorder(0);
        table.addCell(cell);

        String regionManagerName = vo.getRegionManagerName() == null ? "" : vo.getRegionManagerName();
        cell = new PdfPCell(new Phrase("大区经理: " + regionManagerName, font));
        cell.setBorder(0);
        table.addCell(cell);

        String lineGroupName = vo.getLineGroupName() == null ? "" : vo.getLineGroupName();
        cell = new PdfPCell(new Phrase("线路组: " + lineGroupName, font));
        cell.setBorder(0);
        table.addCell(cell);

        String deliveryManName = vo.getDeliveryManName() == null ? "" : vo.getDeliveryManName();
        cell = new PdfPCell(new Phrase("送货员: " + deliveryManName, font));
        cell.setBorder(0);
        table.addCell(cell);

        String category = vo.getCateName() == null ? "" : vo.getCateName();
        cell = new PdfPCell(new Phrase("商品品类: " + category, font));
        cell.setBorder(0);
        table.addCell(cell);

        cell = new PdfPCell(new Phrase(""));
        cell.setBorder(0);
        table.addCell(cell);
        table.addCell(cell);
        return table;
    }

    /**
     * 处理表格的header, 分两行
     * @param tableHeader 表头
     * @param cellWidth 多少列
     * @param font  字体
     * @return 返回表格
     */
    private PdfPTable proceedTableHeader(List<List<String>> tableHeader, float[] cellWidth, Font font) {
        PdfPTable table = new PdfPTable(cellWidth);
        table.setWidthPercentage(100);
        List<String> headerLineOne = tableHeader.get(0);
        List<String> headerLineTwo = tableHeader.get(1);
        for (int i = 0; i < headerLineOne.size(); i++) {
            String headerStr = headerLineOne.get(i);
            PdfPCell cell = new PdfPCell(new Phrase(headerStr, font));
            cell.setUseAscender(true);
            cell.setVerticalAlignment(PdfPCell.ALIGN_MIDDLE);
            cell.setHorizontalAlignment(PdfPCell.ALIGN_CENTER);
            if (i < 6 || (i ) >= headerLineOne.size()) {
                cell.setRowspan(2);
            } else {
                cell.setColspan(2);
            }
            table.addCell(cell);
        }
        for (String headerStr : headerLineTwo) {
            PdfPCell cell = new PdfPCell(new Phrase(headerStr, font));
            table.addCell(cell);
        }
        return table;
    }

}
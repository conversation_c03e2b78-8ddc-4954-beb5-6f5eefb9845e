package com.pinshang.qingyun.orderreport.dto;

import com.pinshang.qingyun.box.utils.SpringUtil;
import com.pinshang.qingyun.orderreport.util.NumberUtils;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 商品组打印预览tmpVo
 */
@Data
public class CommodityGroupLinePreviewTmpVo {
    private Map<Long,String> storeMap;
    private List<CommodityGroupOrderItemODTO> commodityList;
    private List<List<String>> tableHeader;
    private List<List<String>> tableData;

    public List<List<String>> getTableHeader() {
        if(SpringUtil.isEmpty(storeMap)){
            return null;
        }
        List<String> storeNameList = new ArrayList<>(this.storeMap.values());
        List<String> title1 = new ArrayList<>();
        title1.add("客户");
        title1.addAll(storeNameList);
        title1.add("汇总");

        List<String> title2 = new ArrayList<>();
        title2.add("商品名称");
        title2.add("规格");
        storeNameList.forEach(item->{
            title2.add("预定");
            title2.add("实发");
        });
        title2.add("预定量");

        List<List<String>> tableHeader = new ArrayList<>();
        tableHeader.add(title1);
        tableHeader.add(title2);
        return tableHeader;
    }

    public List<List<String>> getTableData() {
        if(SpringUtil.isEmpty(this.commodityList) || SpringUtil.isEmpty(this.storeMap)){
            return null;
        }
        List<List<String>> tableData = new ArrayList<>();
        this.commodityList.forEach(item->{
            List<String> row = new ArrayList<>();
            row.add(item.getCommodityName());
            row.add(item.getCommoditySpec());
            this.storeMap.forEach((k,v)->{
                if(item.getStoreQuantityMap().get(k)!=null){
                    row.add(NumberUtils.subZeroAndDot(item.getStoreQuantityMap().get(k) + ""));
                }else{
                    row.add(null);
                }
                row.add(null);//实发
            });
            row.add(NumberUtils.subZeroAndDot(item.getTotalQuantity() + ""));
            tableData.add(row);
        });
        return tableData;
    }

}

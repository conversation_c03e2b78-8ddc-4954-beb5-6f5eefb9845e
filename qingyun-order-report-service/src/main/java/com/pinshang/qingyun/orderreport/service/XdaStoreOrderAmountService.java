package com.pinshang.qingyun.orderreport.service;

import com.pinshang.qingyun.base.configure.expand.QYAssert;
import com.pinshang.qingyun.orderreport.mapper.XdaStoreOrderAmountMapper;
import com.pinshang.qingyun.orderreport.mapper.entry.XdaStoreOrderAmountEntry;
import com.pinshang.qingyun.orderreport.vo.XdaStoreOrderAmountVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * @author: chenqiang
 * @time: 2021/9/28 10:24
 */
@Service
public class XdaStoreOrderAmountService {

    @Resource
    private XdaStoreOrderAmountMapper xdaStoreOrderAmountMapper;

    public List<XdaStoreOrderAmountEntry> exportXdaStoreOrderAmount(XdaStoreOrderAmountVo vo){
        QYAssert.notNull(vo.getBTime(),"送货日期不能为空");
        QYAssert.notNull(vo.getETime(),"送货日期不能为空");
        return xdaStoreOrderAmountMapper.exportXdaStoreOrderAmount(vo);
    }
}

package com.pinshang.qingyun.orderreport.service;

import com.pinshang.qingyun.base.enums.PrinterDataGroupTypeEnum;
import com.pinshang.qingyun.box.utils.SpringUtil;
import com.pinshang.qingyun.orderreport.mapper.CommodityMapper;
import com.pinshang.qingyun.orderreport.mapper.WorkshopShipmentsMapper;
import com.pinshang.qingyun.orderreport.mapper.entry.NonlocalShipmentsEntry;
import com.pinshang.qingyun.orderreport.mapper.entry.NonlocalShipmentsItemEntry;
import com.pinshang.qingyun.orderreport.mapper.entry.NonlocalShipmentsTempEntry;
import com.pinshang.qingyun.orderreport.pdf.WorkshopNonLocalShipmentsPdfCreator;
import com.pinshang.qingyun.orderreport.util.DateUtils;
import com.pinshang.qingyun.orderreport.util.NumberUtils;
import com.pinshang.qingyun.orderreport.util.ProductShipmentRequestParamHelper;
import com.pinshang.qingyun.orderreport.util.WorkshopShipmentsHelper;
import com.pinshang.qingyun.orderreport.vo.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.Collator;
import java.text.NumberFormat;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

import static java.util.stream.Collectors.groupingBy;

/**
 * <AUTHOR>
 * @date 2019/3/4 11:08.
 */
@Service
@Slf4j
public class WorkshopNonLocalShipmentsService {

    private final WorkshopShipmentsMapper workshopShipmentsMapper;
    private final CommodityMapper commodityMapper;
    private final WorkshopShipmentsHelper workshopShipmentsHelper;
    private final PrintTaskService printTaskService;
    private final WorkshopNonLocalShipmentsPdfCreator workshopNonLocalShipmentsPdfCreator;

    @Autowired
    private WorkshopShipmentsService workshopShipmentsService;
    @Autowired
    private CommonPdfPrintService commonPdfPrintService;

    public WorkshopNonLocalShipmentsService(WorkshopShipmentsMapper workshopShipmentsMapper, CommodityMapper commodityMapper, WorkshopShipmentsHelper workshopShipmentsHelper, PrintTaskService printTaskService, WorkshopNonLocalShipmentsPdfCreator workshopNonLocalShipmentsPdfCreator, WorkshopShipmentsService workshopShipmentsService) {
        this.workshopShipmentsMapper = workshopShipmentsMapper;
        this.commodityMapper = commodityMapper;
        this.workshopShipmentsHelper = workshopShipmentsHelper;
        this.printTaskService = printTaskService;
        this.workshopNonLocalShipmentsPdfCreator = workshopNonLocalShipmentsPdfCreator;
    }

    public WorkshopShipmentsVo preview(ProductShipmentsRequestVo requestVo) {
        NumberFormat numberFormat = NumberFormat.getNumberInstance();
        numberFormat.setMaximumFractionDigits(2);
        numberFormat.setRoundingMode(RoundingMode.HALF_UP);
        numberFormat.setGroupingUsed(false);
        WorkshopShipmentsVo result = new WorkshopShipmentsVo();
        Long workshopId = requestVo.getWorkshopId();
        Date startOrderDate = requestVo.getStartOrderDate();
        Date endOrderDate = requestVo.getEndOrderDate();
        WorkshopVo workshopVo = commodityMapper.selectWorkshopByWorkshopId(workshopId);
        if (workshopVo == null) {
            return null;
        }
        workshopShipmentsHelper.processResponseTitle(requestVo, result);
        result.setWorkshopName(workshopVo.getWorkshopName());
        result.setStartOrderDate(startOrderDate);
        result.setEndOrderDate(endOrderDate);
        result.setOrderModeTypeName(requestVo.getOrderModeTypeName());
        Map<String, Object> params = ProductShipmentRequestParamHelper.buildSqlMap(requestVo);
        params.put("workshopId", workshopVo.getId());
        //指定订单日期内客户购买的所有商品,并且限制了生产组
        List<OrderListVo> dbResult = workshopShipmentsMapper.queryOrdersByOrderDateAndWorkshopId(params);
        Map<Long, OrderListVo> goodsListMapping = new HashMap<>();
        List<Long> goodsIds = new ArrayList<>();
        for (OrderListVo vo : dbResult) {
            Long goodsId = vo.getGoodsId();
            goodsIds.add(goodsId);
            goodsListMapping.put(goodsId, vo);
        }
        //商品ID去重
        goodsIds = goodsIds.parallelStream().distinct().collect(Collectors.toList());

        goodsIds = workshopShipmentsService.sort(goodsIds);

        List<String> tableHeader = new ArrayList<>();
        tableHeader.add("线路组");
        List<String> unitLine = new ArrayList<>();
        unitLine.add("");
        goodsIds.forEach(id->{
            OrderListVo orderListVo = goodsListMapping.get(id);
            if (orderListVo != null) {
                tableHeader.add(orderListVo.getGoodsName() + "(" + orderListVo.getSpecification() + ")");
                unitLine.add(orderListVo.getUnit());
            }
        });
        result.setTableHeader(tableHeader);

        //用于保存线路组id和该线路组下所有的orderList
        Map<Long, List<OrderListVo>> lineGroupOrderListMapping = new HashMap<>();
        dbResult.forEach(orderListVo -> {
            Long lineGroupId = orderListVo.getLineGroupId();
            List<OrderListVo> listVos = lineGroupOrderListMapping.get(lineGroupId);
            if (listVos == null || listVos.size() == 0) {
                listVos = new ArrayList<>();
                listVos.add(orderListVo);
                lineGroupOrderListMapping.put(lineGroupId, listVos);
            } else {
                listVos.add(orderListVo);
                lineGroupOrderListMapping.put(lineGroupId, listVos);
            }
        });
        List<List<String>> tableData = new ArrayList<>();
        tableData.add(unitLine);
        //遍历组路组对应映射关系
        List<Long> lineGroupIds = new ArrayList<>(lineGroupOrderListMapping.keySet());
        List<Long> finalGoodsIds = goodsIds;
        lineGroupIds.forEach(lineGroupId->{
            List<String> tableDataItem = new ArrayList<>();
            List<OrderListVo> listVos = lineGroupOrderListMapping.get(lineGroupId);
            if (listVos != null && listVos.size() != 0) {
                OrderListVo vo = listVos.get(0);
                tableDataItem.add(vo.getLineGroupName() == null ? "" : vo.getLineGroupName());
                //根据产品ID的顺序循环统计求合
                finalGoodsIds.forEach(goodsId->{
                    Double itemCount = 0.0;
                    for (OrderListVo vo1 : listVos) {
                        if (vo1 != null) {
                            if (Objects.equals(vo1.getGoodsId(), goodsId)) {
                                itemCount += vo1.getGoodsNumber();
                            }
                        }
                    }
                    String e = NumberUtils.subZeroAndDot(numberFormat.format(itemCount));
                    tableDataItem.add("0".equals(e) ? "" : e);
                });
            }
            tableData.add(tableDataItem);
        });
        Collator instance = Collator.getInstance(Locale.CHINA);
        tableData.sort((o1, o2) -> instance.compare(o1.get(0), o2.get(0)));
        result.setTableDate(tableData);

        //表格底部合计
        List<String> tableBottom = new ArrayList<>();
        tableBottom.add("合计");
        for (int i = 0; i < goodsIds.size(); i++) {
            Double count = 0.0;
            for (List<String> row : tableData) {
                String s = row.get(i + 1);
                if (StringUtils.isNoneBlank(s) && NumberUtils.isNumeric(s)) {
                    Double rowCount = new Double(s);
                    count += rowCount;
                }
            }
            String e = NumberUtils.subZeroAndDot(numberFormat.format(count));
            tableBottom.add("0".equals(e) ? "" : e);
        }
        result.setTableBottom(tableBottom);
        return result;
    }


    public boolean print(ProductShipmentsRequestVo requestVo) {
        WorkshopShipmentsVo preview = preview(requestVo);
        if (preview == null || preview.getTableDate() == null || preview.getTableDate().size() == 0) {
            return false;
        }
        String fileName = workshopNonLocalShipmentsPdfCreator.create(preview);
        try {
            commonPdfPrintService.uploadAndPrintPdf(fileName,"order_report_TJ_WORKSHOP_NONLOCAL_SHIPMENTS", PrinterDataGroupTypeEnum.STATISTICAL_QUERY_DELIVERY,requestVo.getUserId());
           // printTaskService.savePrintTask(fileName, PrinterDataGroupTypeEnum.STATISTICAL_QUERY_DELIVERY,requestVo.getUserId(), 1, PrinterTypeEnum.LASER.getCode());
        } catch (Exception e) {
            //
            log.error("生产组外地发货单打印失败:{}", e);
            return false;
        }
        return true;
    }

    public boolean printAll(List<ProductShipmentsRequestVo> requestVo) {
        for (ProductShipmentsRequestVo shipmentsRequestVo : requestVo) {
            print(shipmentsRequestVo);
        }
        return true;
    }

    public String getStrPdf(ProductShipmentsRequestVo requestVo){
        WorkshopShipmentsVo preview = preview(requestVo);
        if (preview == null || preview.getTableDate() == null || preview.getTableDate().size() == 0) {
            return null;
        }
        return workshopNonLocalShipmentsPdfCreator.create(preview);
    }

    public Map<String,String> getStrListPdf(List<ProductShipmentsRequestVo> requestVo){

        Map<String,String> strListPdf = new HashMap<>();
        String fileName;
        SimpleDateFormat dateFormatHHMM = new SimpleDateFormat("yyyyMMddHHmm");
        String dataDir = dateFormatHHMM.format(new Date());
        for (ProductShipmentsRequestVo productShipmentsRequestVo : requestVo) {
            WorkshopShipmentsVo preview = preview(productShipmentsRequestVo);
            if (preview == null || preview.getTableDate() == null || preview.getTableDate().size() == 0) {
                continue;
            }
            if(null != (fileName = workshopNonLocalShipmentsPdfCreator.create(preview))){
                WorkshopVo workshopVo = commodityMapper.selectWorkshopByWorkshopId(productShipmentsRequestVo.getWorkshopId());
                String workshopName = "";
                if(null != workshopVo && null != workshopVo.getWorkshopName()){
                    workshopName = workshopVo.getWorkshopName();
                }
                strListPdf.put(fileName,"生产组外地发货单"+"_"+ workshopName +"_"+ dataDir + ".pdf");
            }
        }
        return strListPdf;
    }
    /**
     * 清美人APP 生产组外地发货单列表
     * @param vo
     * @return
     */
    public List<NonlocalShipmentsEntry> queryListForApp(NonlocalShipmentsVo vo) {
        Boolean latestFlag  = DateUtils.isLatestDate(vo.getOrderDate());
        List<NonlocalShipmentsEntry> list = new ArrayList<>();
        List<NonlocalShipmentsTempEntry> entryList = workshopShipmentsMapper.queryListForApp(vo,latestFlag);
        if(SpringUtil.isNotEmpty(entryList)){
            //按生产组排序
            Map<String,List<NonlocalShipmentsTempEntry>> workShopMap = entryList.stream().collect(groupingBy(NonlocalShipmentsTempEntry::getWorkshopName));
            Map<String,List<NonlocalShipmentsTempEntry>> workShopMapSort = new LinkedHashMap<>();
            Collator instance = Collator.getInstance(Locale.CHINA);
            workShopMap.keySet().stream().sorted((o1, o2) -> instance.compare(o1, o2)).forEachOrdered(x ->workShopMapSort.put(x,workShopMap.get(x)));

            //按商品分组
            workShopMapSort.forEach((workshopName,workshopCmdList)-> {
                Map<String, List<NonlocalShipmentsTempEntry>> cmdMap = workshopCmdList.stream().collect(groupingBy(NonlocalShipmentsTempEntry::getCommodityCode));
                Map<String, List<NonlocalShipmentsTempEntry>> cmdMapSort = new LinkedHashMap<>();
                cmdMap.entrySet().stream().sorted(Map.Entry.comparingByKey()).forEachOrdered(x -> cmdMapSort.put(x.getKey(), x.getValue()));
                cmdMapSort.forEach((k, v) -> {
                    NonlocalShipmentsTempEntry commodity = v.get(0);
                    BigDecimal totalQuantity = v.stream().map(NonlocalShipmentsTempEntry::getQuantity).reduce(BigDecimal.ZERO, BigDecimal::add);
                    NonlocalShipmentsEntry record = new NonlocalShipmentsEntry();
                    SpringUtil.copyProperties(commodity, record);
                    record.setQuantity(totalQuantity);

                    //组装详情: <线路组ID，商品数量>
                    List<NonlocalShipmentsItemEntry> tempList = new ArrayList<>();
                    Map<Long, List<NonlocalShipmentsTempEntry>> tempMap = v.stream().collect(groupingBy(NonlocalShipmentsTempEntry::getLineGroupId));
                    //Collator instance = Collator.getInstance(Locale.CHINA);
                    List<Long> lineGroupIdList = v.stream().sorted((o1, o2) -> instance.compare(o1.getLineGroupName(), o2.getLineGroupName())).mapToLong(NonlocalShipmentsTempEntry::getLineGroupId).distinct().boxed().collect(Collectors.toList());
                    lineGroupIdList.forEach(lineGroupId -> {
                        NonlocalShipmentsItemEntry itemEntry = new NonlocalShipmentsItemEntry();
                        List<NonlocalShipmentsTempEntry> mapVals = tempMap.get(lineGroupId);
                        itemEntry.setLineGroupName(mapVals.get(0).getLineGroupName());
                        BigDecimal lineTotalQuantity = mapVals.stream().map(NonlocalShipmentsTempEntry::getQuantity).reduce(BigDecimal.ZERO, BigDecimal::add);
                        itemEntry.setItemQuantity(lineTotalQuantity);
                        tempList.add(itemEntry);
                    });
                    record.setItemList(tempList);
                    list.add(record);
                });
            });
        }
        return list;
    }
}

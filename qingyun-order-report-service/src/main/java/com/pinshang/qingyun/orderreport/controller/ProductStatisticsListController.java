package com.pinshang.qingyun.orderreport.controller;

import com.github.pagehelper.PageInfo;
import com.pinshang.qingyun.orderreport.mapper.entry.ProductStatisticsListEntry;
import com.pinshang.qingyun.orderreport.mapper.entry.ProductStatisticsListSumEntry;
import com.pinshang.qingyun.orderreport.service.ProductStatisticsListService;
import com.pinshang.qingyun.orderreport.vo.ProductStatisticsListVo;
import io.swagger.annotations.ApiModelProperty;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 产品统计清单
 */
@RestController
@RequestMapping(value = "/statistical/productStatistics")
public class ProductStatisticsListController {

    @Autowired
    private ProductStatisticsListService productStatisticsListService;

    /**
     * 产品统计清单
     * @param vo
     * @return
     */
    @PostMapping("/list")
    @ApiModelProperty(value = "产品统计清单", notes = "产品统计清单")
    public PageInfo<ProductStatisticsListEntry> queryList(@RequestBody ProductStatisticsListVo vo) {
        return productStatisticsListService.queryList(vo);
    }

    /**
     * 产品统计清单合计
     * @param vo
     * @return
     */
    @PostMapping("/querySum")
    public ProductStatisticsListSumEntry querySum(@RequestBody ProductStatisticsListVo vo) {
        return productStatisticsListService.querySum(vo);
    }

    /**
     * 打印
     */
    @PostMapping("/print")
    public String print(@RequestBody ProductStatisticsListVo vo){
        return productStatisticsListService.print(vo);
    }

}
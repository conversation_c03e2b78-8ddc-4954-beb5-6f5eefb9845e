package com.pinshang.qingyun.orderreport.model;

import lombok.Data;

import javax.persistence.Id;
import javax.persistence.Table;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @description 产品销售汇总--月份表明细
 * @date 2019/8/30
 */
@Data
@Table(name = "t_tj_commodity_sale_statistics_month_item")
public class CommoditySaleStatisticsMonthItem {

    @Id
    private Long id;

    /** t_tj_commodity_sale_statistics_month表ID **/
    private Long referId;

    /** 客户类型ID **/
    private Long storeTypeId;

    /** 客户类型数量小计 **/
    private BigDecimal totalQuantity;

    /** 客户类型金额小计 **/
    private BigDecimal totalAmount;

}

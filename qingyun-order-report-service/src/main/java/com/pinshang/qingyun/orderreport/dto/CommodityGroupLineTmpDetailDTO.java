package com.pinshang.qingyun.orderreport.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;


/**
 * 商品组打印预览--线路详情
 */
@Data
public class CommodityGroupLineTmpDetailDTO extends CommodityGroupLineOrderODTO {

    @ApiModelProperty(value = "车牌",position = 11)
    private String licensePlate;
    @ApiModelProperty(value = "电话",position = 12)
    private String phone;

    public Integer getIsCanPrint() {
        if(super.getLineStatus() ==0 || super.getCommodityGroupStatus()==0 || super.getStoreQuantity()==0 || super.getCommodityQuantity()==0){
            return 0;
        }
        return 1;
    }

}

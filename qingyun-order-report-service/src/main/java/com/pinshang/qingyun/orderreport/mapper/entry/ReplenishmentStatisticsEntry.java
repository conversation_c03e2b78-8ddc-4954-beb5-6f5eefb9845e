package com.pinshang.qingyun.orderreport.mapper.entry;

import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 补货数据汇总返回对象
 */
@Data
public class ReplenishmentStatisticsEntry {

    private Long commodityId;

    private String commodityName;

    private String commoditySpec;

    @ExcelProperty("合计")
    private BigDecimal totalCommodityNum;
    @ExcelProperty("商品编码")
    private String commodityCode;

    private Long commodityFactoryId;

    @ExcelProperty("工厂")
    private String commodityFactoryName;

    private Long commodityWorkshopId;
    @ExcelProperty("生产组")
    private String commodityWorkshopName;
    @ExcelProperty("商品名称(规格)")
    private String commodityNameSpec;

    private Long commodityFlowshopId;

    @ExcelProperty("车间")
    private String commodityFlowshopName;

}

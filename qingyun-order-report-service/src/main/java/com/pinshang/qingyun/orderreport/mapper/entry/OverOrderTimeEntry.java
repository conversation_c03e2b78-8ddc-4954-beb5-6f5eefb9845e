package com.pinshang.qingyun.orderreport.mapper.entry;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 晚订货查询Entry
 */
@Data
public class OverOrderTimeEntry {

    private String orderCode;

    private Date orderTime;

    private BigDecimal finalAmount;

    //订单下单日期(t_order表的 create_time)
    private Date orderCreateTime;

    private Integer modeType;

    private Long storeId;

    private Long lineGroupId;

    private String lineGroupName;

    //操作员(t_order表create_id)
    private Long operatorId;

    private String operatorName;

    //督导ID(t_employee表主键)
    private Long supervisorId;

    private String supervisorName;

}

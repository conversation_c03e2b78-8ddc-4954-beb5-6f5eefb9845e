package com.pinshang.qingyun.orderreport.dto.storearrival;

import io.swagger.annotations.ApiModelProperty;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;

import lombok.Data;
import lombok.NoArgsConstructor;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.format.DateTimeFormat;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.pinshang.qingyun.box.utils.StringUtil;
import com.pinshang.qingyun.renderer.annotation.FieldRender;
import com.pinshang.qingyun.renderer.constant.RenderFieldHelper;
import com.pinshang.qingyun.renderer.enums.FieldTypeEnum;

/**
 * 客户到货信息
 */
@Data
@NoArgsConstructor
public class StoreArrivalInfoODTO {
	
	@ColumnWidth(value = 10)
	@DateTimeFormat("yyyy-MM-dd")
	@ExcelProperty(value = "送货日期", index = 0)
	@JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
	@ApiModelProperty(position = 11, required = true, value = "送货日期：yyyy-MM-dd")
	private Date orderTime;

	@ColumnWidth(value = 10)
	@ExcelProperty(value = "线路编码", index = 1)
	@ApiModelProperty(position = 12, required = true, value = "线路编码")
	private String lineCode;
	
	@ColumnWidth(value = 10)
	@ExcelProperty(value = "线路名称 ", index = 2)
	@ApiModelProperty(position = 12, required = true, value = "线路名称")
	private String lineName;

	@ColumnWidth(value = 10)
	@ExcelProperty(value = "送货员 ", index = 3)
	@ApiModelProperty(position = 13, required = true, value = "送货员名称")
	private String deliverymanName;

	@ExcelIgnore
	@JsonSerialize(using = ToStringSerializer.class)
	@ApiModelProperty(position = 14, required = true, value = "客户ID", hidden = true)
	private Long storeId;
	
	@ColumnWidth(value = 10)
	@ExcelProperty(value = "客户编码 ", index = 4)
	@FieldRender(fieldType = FieldTypeEnum.STORE, fieldName = RenderFieldHelper.Store.storeCode, keyName = "storeId")
	@ApiModelProperty(position = 14, required = true, value = "客户编码")
	private String storeCode;
	
	@ColumnWidth(value = 10)
	@ExcelProperty(value = "客户名称 ", index = 5)
	@FieldRender(fieldType = FieldTypeEnum.STORE, fieldName = RenderFieldHelper.Store.storeName, keyName = "storeId")
	@ApiModelProperty(position = 14, required = true, value = "客户名称")
	private String storeName;

	@ExcelIgnore
	@JsonSerialize(using = ToStringSerializer.class)
	@ApiModelProperty(position = 15, required = true, value = "客户类型ID", hidden = true)
	private Long storeTypeId;
	
	@ColumnWidth(value = 10)
	@ExcelProperty(value = "客户类型 ", index = 6)
	@ApiModelProperty(position = 15, required = true, value = "客户类型名称")
	@FieldRender(fieldType = FieldTypeEnum.DICTIONARY, fieldName = RenderFieldHelper.Dictionary.optionName, keyName = "storeTypeId")
	private String storeTypeName;

	@ColumnWidth(value = 10)
	@ExcelProperty(value = "到货时间 ", index = 7)
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
	@ApiModelProperty(position = 16, required = true, value = "到货时间：yyyy-MM-dd HH:mm:ss")
	private Date arrivalTime;

	@ExcelIgnore
	@ApiModelProperty(position = 17, required = true, value = "到货图片s", hidden = true)
	private String arrivalPics;
	@ExcelIgnore
	@ApiModelProperty(position = 17, required = true, value = "到货图片集合")
	private List<String> arrivalPicList;

	@ExcelIgnore
	@ApiModelProperty(position = 18, required = true, value = "到货备注")
	private String arrivalRemarks;
	
	public List<String> getArrivalPicList() {
		return null == arrivalPicList? Collections.emptyList(): arrivalPicList;
	}

	public void buildArrivalPicList(String imgServerUrl) {
		if (StringUtil.isNullOrEmpty(this.arrivalPics)) {
			this.arrivalPics = null;
			return;
		}

		this.arrivalPics = this.arrivalPics.replace(" ", "");
		String[] pics = this.arrivalPics.split("\\,");
		if (pics.length > 0) {
			this.arrivalPicList = new ArrayList<>(pics.length);
			for (String pic : pics) {
				if (!StringUtil.isNullOrEmpty(pic)) {
					this.arrivalPicList.add(imgServerUrl + pic.trim());
				}
			}
		}
	}

	public static void main(String[] args) {
		StoreArrivalInfoODTO o = new StoreArrivalInfoODTO();
		o.setArrivalPics(", a , b  ,   c ,, ,");
		o.buildArrivalPicList("cctv");
		System.out.println(o.getArrivalPics());
		System.out.println(o.getArrivalPicList());
	}

}

package com.pinshang.qingyun.orderreport.controller;

import com.github.pagehelper.PageInfo;
import com.pinshang.qingyun.base.configure.expand.QYAssert;
import com.pinshang.qingyun.box.utils.DateUtil;
import com.pinshang.qingyun.box.utils.ListUtil;
import com.pinshang.qingyun.box.utils.SpringUtil;
import com.pinshang.qingyun.order.dto.orderStatistics.OrderStatisticsMonitorIDTO;
import com.pinshang.qingyun.order.dto.orderStatistics.OrderStatisticsODTO;
import com.pinshang.qingyun.order.dto.orderStatistics.OrderSyncODTO;
import com.pinshang.qingyun.order.service.OrderStatisticsMonitorClient;
import com.pinshang.qingyun.orderreport.mapper.entry.OrderStatisticsEntry;
import com.pinshang.qingyun.orderreport.model.OrderLatest;
import com.pinshang.qingyun.orderreport.service.CommoditySaleStatisticsService;
import com.pinshang.qingyun.orderreport.service.TJOrderMonitorService;
import com.pinshang.qingyun.orderreport.vo.TJOrderMonitorVo;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 订单统计查询--订单监控
 */
@RestController
@RequestMapping("/tjOrderMonitor")
@Slf4j
public class TJOrderMonitorController {
    @Autowired
    private TJOrderMonitorService orderStatisticsMonitorService;
    @Autowired
    private OrderStatisticsMonitorClient orderClient;
    @Autowired
    private CommoditySaleStatisticsService commoditySaleStatisticsService;
    @Autowired
    private RedissonClient redissonClient;

    /**
     * 订单比较：查询订单统计信息
     * @param vo
     * @return
     */
    @RequestMapping(value = "/queryTJOrderStatisticsInfo", method = RequestMethod.POST)
    public OrderStatisticsEntry queryOrderInfo(@RequestBody TJOrderMonitorVo vo) {
        return orderStatisticsMonitorService.queryOrderStatisticsInfo(vo);
    }

    /**
     * 差异订单：查询订单编号
     * @param vo
     * @return
     */
    @RequestMapping(value = "/queryTJOrderDiffList", method = RequestMethod.POST)
    public List<String> queryOrderDiffList(@RequestBody TJOrderMonitorVo vo) {
        return orderStatisticsMonitorService.queryOrderDiffList(vo);
    }

    /**
     * 订单同步
     * 优化订单同步代码，把原有批量删除再批量插入改为，先比较订单金额是否一致，不一致才执行删除再插入数据操作
     * @param vo
     * @return
     */
    @RequestMapping(value = "/syncTJOrder", method = RequestMethod.POST)
    public Integer queryOrderSyncList(@RequestBody TJOrderMonitorVo vo) {
        QYAssert.notNull(vo,"参数不能为空");
        OrderStatisticsMonitorIDTO idto = new OrderStatisticsMonitorIDTO();
        SpringUtil.copyProperties(vo,idto);
        int pageNo = 1;
        idto.setPageNo(pageNo);//分页请求订单系统，查询订单
        idto.setPageSize(200);
        idto.setFilterOrderTypeList(SpringUtil.isNotEmpty(vo.getFilterOrderTypeList())?vo.getFilterOrderTypeList():new ArrayList<>());
        int retry = 0;
        PageInfo<OrderSyncODTO> orderResult = null;
        while(pageNo == 1 || (orderResult != null && orderResult.isHasNextPage())) {
            orderResult = orderClient.queryOrderSyncList(idto);
            if (orderResult != null && SpringUtil.isNotEmpty(orderResult.getList())) {
                orderStatisticsMonitorService.syncOrder(orderResult.getList());
            } else {
                if (retry++ > 6) {
                    break;
                }
            }
            idto.setPageNo(++pageNo);
        }
        return 1;
    }

    /**
     * 定时清理最新订单统计表及明细表数据
     * @return
     */
    @GetMapping("/removeTJOrderLatest")
    public Integer removeTJOrderLatest() {
        RLock lock = redissonClient.getLock("ORDER_LATEST_REMOVE_JOB");
        if (!lock.isLocked()) {
            boolean locked = lock.tryLock();
            if( locked ){
                try {
                    Date nowDate = DateUtil.getNowDate();
                    List<OrderLatest> orderList = orderStatisticsMonitorService.queryOrderLatestList(nowDate);
                    if(SpringUtil.isEmpty(orderList)){
                        log.info("定时清理最新订单表job，未查询到最新订单数据");
                        return 0;
                    }
                    orderList.stream().collect(Collectors.groupingBy(OrderLatest::getOrderTime)).forEach((orderTime,val)->{
                        try {
                            orderStatisticsMonitorService.removeTJOrderLatest(orderTime);
                        }catch (Exception e){
                            log.error("定时清理最新订单表job执行异常，订单日期={}",e,orderTime);
                        }
                    });
                }finally {
                    lock.unlock();
                }
            }

        }
        return 1;
    }

    /**
     * 订单同步定时补偿
     * 1. 如果统计系统总订单量<订单系统总订单量,根据差异数据把差异订单补偿到统计系统（create_time<=当前系统时间）
     * 2. 如果统计系统总正常订单量!=订单系统总正常订单量,则根据以下条件
     *    order_time='某天' and create_time!=update_time and create_time<=当前系统时间，把有过变更的订单重新同步
     * 3. 如果统计系统订单量=订单系统订单,但统计系统金额!=订单系统金额,则根据以下条件
     *    order_time='某天' and create_time!=update_time and create_time<=当前系统时间，把有过变更的订单重新同步
     * @param orderTimes
     * @return
     */
    @PostMapping("/compensateTjOrderSync")
    public Integer compensateTjOrderSync(@RequestBody(required = false) String[] orderTimes){
        List<Date> orderTimeList = orderStatisticsMonitorService.buildOrderTimeParam(orderTimes);
        for(Date orderTime : orderTimeList){ //循环遍历送货日期，分天处理
            try{
                log.info("\n统计系统订单同步定时补偿：开始执行订单同步，orderTime="+orderTime);
                //查询订单系统的订单数量、正常订单量、订单总金额
                OrderStatisticsMonitorIDTO orderVo = new OrderStatisticsMonitorIDTO();
                orderVo.setOrderTime(orderTime);
                OrderStatisticsODTO orderEntry = orderClient.queryOrderStatisticsInfo(orderVo);
                if(!checkOrderEntry(orderEntry)){
                    log.warn("\n订单同步定时补偿，根据指定的送货日期，在订单系统没有查询到订单：orderTime="+orderTime);
                    continue;
                }
                //查询统计系统的订单数量、正常订单量、订单总金额
                TJOrderMonitorVo tjVo = new TJOrderMonitorVo();
                tjVo.setOrderTime(orderTime);
                OrderStatisticsEntry tjOrderEntry = orderStatisticsMonitorService.queryOrderStatisticsInfo(tjVo);
                if(tjOrderEntry.getTotalOrderCount() == 0 || tjOrderEntry.getNormalOrderCount()==null || tjOrderEntry.getTotalOrderAmount() == null){
                    log.warn("\n订单同步定时补偿，送货日期="+orderTime+"，在统计系统订单总数量为0，需要补偿这一天的所有订单");
                    this.queryOrderSyncList(tjVo);
                }else if(tjOrderEntry.getTotalOrderCount().intValue() < orderEntry.getTotalOrderCount().intValue()){
                    //1.如果统计系统的订单总数量 < 订单系统的订单总数量，根据差异数据把差异订单补偿到统计系统
                    log.warn("\n订单同步定时补偿，送货日期="+orderTime+"，统计系统的订单总数量 < 订单系统的订单总数量，根据差异数据把差异订单补偿到统计系统");
                    List<String> list1 = orderClient.queryOrderDiffList(orderVo);
                    List<String> list2 = orderStatisticsMonitorService.queryOrderDiffList(tjVo);
                    List<String> diffOrderCodeList = orderStatisticsMonitorService.buildDiffOrderCodeList(list1,list2);
                    if(SpringUtil.isNotEmpty(diffOrderCodeList)){
                        //差异订单可能较大，分批次处理
                        List<List<String>> subDiffOrderCodeList = ListUtil.splitSubList(diffOrderCodeList, 200);
                        subDiffOrderCodeList.forEach(subDiffOrderCode->{
                            tjVo.setOrderCodeList(null);
                            tjVo.setOrderCodeList(subDiffOrderCode);
                            this.queryOrderSyncList(tjVo);
                        });
                    }
                }else if((tjOrderEntry.getTotalOrderCount().intValue() == orderEntry.getTotalOrderCount().intValue()) &&
                        (tjOrderEntry.getNormalOrderCount().compareTo(orderEntry.getNormalOrderCount()) != 0
                        || tjOrderEntry.getTotalOrderAmount().compareTo(orderEntry.getTotalOrderAmount()) != 0)){
                    //当统计系统的订单总数量 = 订单系统的订单总数量时，
                    //2.如果统计系统的正常订单量 不等于 订单系统的正常订单量，或者
                    //3.如果统计系统的订单总金额 不等于 订单系统的订单总金额，根据条件查询修改过的订单补偿到统计系统
                    String logStr = (tjOrderEntry.getNormalOrderCount().compareTo(orderEntry.getNormalOrderCount()) != 0)?"统计系统的正常订单量不等于订单系统的正常订单量":"统计系统的订单总金额不等于订单系统的订单总金额";
                    log.warn("\n订单同步定时补偿，送货日期="+orderTime+"，"+logStr+"，根据条件查询修改过的订单补偿到统计系统");
                    Calendar c = Calendar.getInstance();
                    tjVo.setSysTime(c.getTime());
                    this.queryOrderSyncList(tjVo);
                }
                log.info("\n====结束执行订单同步，orderTime="+orderTime);
            }catch (Exception e){
                log.error("统计系统订单同步定时补偿错误，orderTime：",orderTime,e);
            }
        }
        return 1;
    }

    /**
     * 验证订单信息统计
     * @param orderEntry
     * @return
     */
    private Boolean checkOrderEntry(OrderStatisticsODTO orderEntry){
        if(orderEntry == null || orderEntry.getTotalOrderCount().intValue()==0 || orderEntry.getNormalOrderCount()==null || orderEntry.getTotalOrderAmount()==null){
            return false;
        }
        return true;
    }


    /**
     * 定时清理最新订单统计表及明细表数据
     * @return
     */
    @GetMapping("/removeTJOrderSync")
    public Integer removeTJOrderSync() {
        RLock lock = redissonClient.getLock("ORDER_SYNC_REMOVE_JOB");
        if (!lock.isLocked()) {
            boolean locked = lock.tryLock();
            if( locked ){
                try {
                    //前6个月的日期
                    Date date = getDate();
                    if(null != date){
                        orderStatisticsMonitorService.removeTJOrderSync2(date);
                    }else{
                        log.info("定时清理t_tj_order_sync表job，日期为空");
                        return 0;
                    }
                }finally {
                    lock.unlock();
                }
            }
        }
        return 1;
    }

    /**
     * 查询当前日期的6个月之前的一天
     * @return
     */
    private Date getDate() {
        Calendar calendar = Calendar.getInstance();
        Date nowDate = DateUtil.getNowDate();
        calendar.setTime(nowDate);
        calendar.add(Calendar.MONTH, -6);
        Date date = calendar.getTime();
        return date;
    }
}

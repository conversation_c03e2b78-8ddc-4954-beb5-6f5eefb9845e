package com.pinshang.qingyun.orderreport.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2019/3/11 12:46
 */
@Data
public class FreshStoreProductShipmentsReqVo {
    @ApiModelProperty("工厂id")
    private Long factoryId;
    @ApiModelProperty("送货日期")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date orderDate;
    @ApiModelProperty("线路组")
    private Long lineGroupId;
    @ApiModelProperty("配送批次")
    private Integer deliveryBatch;
    /**
     * 查询标识
     */
    private transient Boolean latestFlag;

    @JsonIgnore
    private List<Long> factoryIds;
    @ApiModelProperty(value = "登录用户ID", hidden = true)
    private Long loginUserId;

    public void setFactoryIds(List<Long> factoryIds) {
        if(CollectionUtils.isNotEmpty(factoryIds)){
            this.factoryIds = factoryIds;
        }
    }
}

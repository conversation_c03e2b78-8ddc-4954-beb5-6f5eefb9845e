package com.pinshang.qingyun.orderreport.pdf;

import com.itextpdf.text.*;
import com.itextpdf.text.pdf.PdfPCell;
import com.itextpdf.text.pdf.PdfPTable;
import com.itextpdf.text.pdf.PdfWriter;
import com.pinshang.qingyun.base.enums.DictionaryEnums;
import com.pinshang.qingyun.common.dto.DictionaryIDTO;
import com.pinshang.qingyun.common.dto.DictionaryODTO;
import com.pinshang.qingyun.common.dto.DictionaryTreeODTO;
import com.pinshang.qingyun.common.service.DictionaryClient;
import com.pinshang.qingyun.orderreport.config.CustomerProperties;
import com.pinshang.qingyun.orderreport.util.ListUtils;
import com.pinshang.qingyun.orderreport.util.PdfUtils;
import com.pinshang.qingyun.orderreport.util.StrUtils;
import com.pinshang.qingyun.orderreport.vo.WorkshopShipmentsVo;
import org.apache.commons.lang3.StringUtils;
import org.joda.time.DateTime;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2019/3/1 10:22.
 */
@Component
public class WorkshopShipmentsPdfCreator {

    private static final Logger LOGGER = LoggerFactory.getLogger(WorkshopShipmentsPdfCreator.class);

    private static final Integer PAGE_ROW = 42;

    /** 商品名称+规格, 保留12(对应22个字节)个字,每行2个字共6行 */
    private static final int commodityNameLength = 22;
    /** 送货员姓名, 保留5个字(对应10个字节),不换行 */
    private static final int deliveryManNameLength = 6;


    /**
     * 多少商品变为横向打印
     * 默认是11号字体 A4纸的情况下, 14家客户换行
     * 设置成17 是因为 第一个格的 商品名称  和最后一个格的 合计
     * 17列是查询列表的总列数，打印时去掉线路一列，所以是14个商品。
     * 生产组发货单商品数量 小于14 是纵向打印，大于等于14 是横向打印
     */
    private static final Integer rotateDelimiter = 17;

    /**
     * 横向打印的时候,每页27条
     */
    private static final Integer horizontalPage = 27;

    private final CustomerProperties customerProperties;

    @Autowired
    private DictionaryClient dictionaryClient;

    @Autowired
    public WorkshopShipmentsPdfCreator(CustomerProperties customerProperties) {
        this.customerProperties = customerProperties;
    }

    public String createPdf(WorkshopShipmentsVo preview) {
        //是否横向打印,默认为否
        boolean horizontal = false;
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String printTime = dateFormat.format(new Date());
        String pdfSave = customerProperties.getAbsoluteSavePath();
        String savePath = customerProperties.getSavePath();
        Date date = new DateTime().plusDays(1).toDate();
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");
        String dataDir = format.format(date);
        File file = new File(pdfSave + "/" +savePath + "/" + dataDir);
        if (!file.exists()) {
            boolean mkdirs = file.mkdirs();
            if (!mkdirs) {
                LOGGER.error("创建用于保存pdf的目录时失败,请检查权限问题.");
                return null;
            }
        }

        String fileName = extractFileName(preview);
        try {

            Font chineseFont = PdfUtils.chineseFont(11);
            Font numberFont = PdfUtils.chineseFont(8);

            PdfPTable tableHeader = proceedTableHeader(preview, chineseFont);
            List<List<String>> tableDate = preview.getTableDate();
            Rectangle rect = new Rectangle(PageSize.A4);
            Document document;
            if (tableDate.get(0).size() >= rotateDelimiter) {
                horizontal = true;
                document = new Document(rect.rotate());
            } else {
                document = new Document(rect);
            }
            document.setMargins(15, 15, 15, 15);
            FileOutputStream outputStream = new FileOutputStream(file + "/" + fileName);
            PdfWriter.getInstance(document, outputStream);
            document.open();

            List<List<List<String>>> tableDataList;
            if (horizontal) {
                // 如果横向打印
                tableDataList = ListUtils.splitList(tableDate, horizontalPage);
            } else {
                tableDataList = ListUtils.splitList(tableDate, PAGE_ROW);
            }
            List<String> headerString = preview.getTableHeader();
            int page = 1;
            int totalPage = tableDataList.size();
            // 有多少列,因为第一列和第二列打印时不显示,所以-2；2019-6-17需求：打印新增车位列
            int colNumber = headerString.size() - 1;
            float[] cellWidth = new float[colNumber];
            float totalWidth = cellWidth[0] + cellWidth[1];
            cellWidth[0] = 35;
            cellWidth[1] = 50;

            if (colNumber == 3) {
                cellWidth[0] = 40;
                cellWidth[1] = 100;
                cellWidth[2] = 120;
                totalWidth = cellWidth[0] + cellWidth[1] + cellWidth[2];
            } else if (colNumber <= 5) {
                cellWidth[0] = 40;
                cellWidth[1] = 100;
                totalWidth = cellWidth[0] + cellWidth[1];
                for (int i = 2; i < colNumber; i++) {
                    cellWidth[i] = 120;
                    totalWidth += cellWidth[i];
                }
            } else if (colNumber == 6) {
                cellWidth[0] = 40;
                cellWidth[1] = 80;
                totalWidth = cellWidth[0] + cellWidth[1];
                for (int i = 2; i < colNumber; i++) {
                    cellWidth[i] = 100;
                    totalWidth += cellWidth[i];
                }
            } else {
                if (horizontal) {
                    cellWidth[0] = 28;
                    cellWidth[1] = 38;
                    totalWidth = cellWidth[0] + cellWidth[1];
                    //横向打印的时候,2列
                    for (int i = 2; i < colNumber; i++) {
                        cellWidth[i] = 40;
                        totalWidth += cellWidth[i];
                    }
                } else {
                    cellWidth[0] = 28;
                    cellWidth[1] = 38;
                    totalWidth = cellWidth[0] + cellWidth[1];
                    for (int i = 2; i < colNumber; i++) {
                        cellWidth[i] = 39;
                        totalWidth += cellWidth[i];
                    }
                }
            }
            for (List<List<String>> pageData : tableDataList) {
                document.add(tableHeader);
                PdfPTable table = new PdfPTable(cellWidth);
                // 100% 宽度
                table.setWidthPercentage(100);
                table.setHorizontalAlignment(Element.ALIGN_LEFT);
                table.setTotalWidth(totalWidth);
                table.setLockedWidth(true);
                for (int i = 0; i < headerString.size(); i++) {
                    if(i != 1){
                        String header = headerString.get(i);
                        header = header.replace("（", "(").replace("）", ")");
                        header = StrUtils.subStr(header, commodityNameLength);
                        PdfPCell cell = new PdfPCell(new Phrase(header, chineseFont));
                        table.addCell(cell);
                    }
                }
                for (List<String> row : pageData) {
                    // 前两列是 车位和线路名称, 因为预览的时候需要,打印的时候不需要,所以省略
                    for (int j = 0; j < row.size(); j++) {
                        if(j!=1){
                            String string = row.get(j);
                            if (j == 2) {
                                string = string.replace("（", "(").replace("）", ")");
                                string = StrUtils.subStr(string, deliveryManNameLength);
                                PdfPCell cell = new PdfPCell(new Phrase(string, chineseFont));
                                cell.setPadding(0);
                                cell.setNoWrap(true);
                                table.addCell(cell);
                            } else {
                                if (string.length() > 5) {
                                    table.addCell(new Phrase(string, numberFont));
                                } else {
                                    table.addCell(new Phrase(string, chineseFont));
                                }
                            }
                        }

                    }
                }
                if (page == totalPage) {
                    // 合计
                    List<String> tableBottom = preview.getTableBottom();
                    for (int i = 1; i < tableBottom.size(); i++) {
                        String bottom = tableBottom.get(i);
                        Phrase phrase;
                        if (bottom.length() > 4) {
                            phrase = new Phrase(bottom, numberFont);
                        } else {
                            phrase = new Phrase(bottom, chineseFont);
                        }
                        table.addCell(phrase);
                    }
                }
                // 分页的table
                PdfPTable pageTable = new PdfPTable(1);
                pageTable.setHorizontalAlignment(Element.ALIGN_LEFT);
                PdfPCell tableBottom = new PdfPCell(new Phrase("第 " + page + " 页, 共 " + totalPage + "页.                打印时间: " + printTime, chineseFont));
                tableBottom.setBorder(0);
                pageTable.addCell(tableBottom);
                page++;
                document.add(table);
                document.add(pageTable);
                if (page <= totalPage) {
                    document.newPage();
                }
            }
            document.close();
        } catch (Exception e) {
            //
            e.printStackTrace();
            LOGGER.error("生成PDF时异常:{}", e.getMessage());
            return null;
        }
        return String.format("%s%s%s",file.getAbsolutePath(),"/",fileName);
        //return savePath + "/" + dataDir + "/" + fileName;
    }

    /**
     *
     * 处理生成table的header
     * @param preview 预览数据
     * @param chineseFont 中文字体
     * @return 返回表头header
     */
    private PdfPTable proceedTableHeader(WorkshopShipmentsVo preview, Font chineseFont) throws IOException, DocumentException {

        float[] cellWidth = {200, 110 , 125 , 125};
        PdfPTable table = new PdfPTable(cellWidth.length);
        table.setWidthPercentage(100);
        table.setHorizontalAlignment(Element.ALIGN_LEFT);
        table.setTotalWidth(cellWidth);
        table.setLockedWidth(true);

        PdfPCell title;
        title = new PdfPCell(new Paragraph("", PdfUtils.chineseFont(18)));
        title.setBorder(0);
        title.setFixedHeight(27);
        table.addCell(title);

        title = new PdfPCell(new Paragraph("生产组发货单 ", PdfUtils.chineseFont(18)));
        title.setBorder(0);
        title.setColspan(2);
        title.setHorizontalAlignment(Element.ALIGN_LEFT);
        title.setVerticalAlignment(Element.ALIGN_LEFT);
        title.setFixedHeight(27);
        table.addCell(title);

        String orderModeTypeName = preview.getOrderModeTypeName();
        title = new PdfPCell(new Paragraph(StringUtils.isBlank(orderModeTypeName) ? "" : "（"+orderModeTypeName+"）",chineseFont));
        title.setBorder(0);
        title.setFixedHeight(27);
        table.addCell(title);

        PdfPCell cell ;
        String workshopName = preview.getWorkshopName();
        if (StringUtils.isBlank(workshopName)) {
            workshopName = "全部";
        }
        cell = new PdfPCell(new Phrase("生产组: " + workshopName, chineseFont));
        cell.setBorder(0);
        table.addCell(cell);
        String lineGroupName = preview.getLineGroupName();
        if (StringUtils.isBlank(lineGroupName)) {
            lineGroupName = "全部";
        }
        cell = new PdfPCell(new Phrase("线路组: " + lineGroupName, chineseFont));
        cell.setBorder(0);
        table.addCell(cell);

        cell = new PdfPCell(new Phrase("", chineseFont));
        cell.setBorder(0);
        table.addCell(cell);

        String deliveryHouse = preview.getDeliveryHouse();
        if (StringUtils.isBlank(deliveryHouse)) {
            deliveryHouse = "全部";
        }
        cell = new PdfPCell(new Phrase("发货仓库: " + deliveryHouse, chineseFont));
        cell.setBorder(0);
        table.addCell(cell);


        float[] cellWidth1 = { 200, 110 , 125 , 125};
        table.setTotalWidth(cellWidth1);
        table.setLockedWidth(true);

        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
        Date startOrderDate = preview.getStartOrderDate();
        Date endOrderDate = preview.getEndOrderDate();
        String start = dateFormat.format(startOrderDate);
        String end = dateFormat.format(endOrderDate);
        cell = new PdfPCell(new Phrase("送货日期: " + start + " 至 " +end, chineseFont));
        cell.setBorder(0);
        table.addCell(cell);

        String deliveryTime = preview.getDeliveryTime();
        if (StringUtils.isBlank(deliveryTime)) {
            deliveryTime = "全部";
        }
        cell = new PdfPCell(new Phrase("发货时间: " + deliveryTime, chineseFont));
        cell.setBorder(0);
        table.addCell(cell);

        String deliveryBatch = preview.getDeliveryBatch();

        cell = new PdfPCell(new Phrase("配送批次: " + deliveryBatch, chineseFont));
        cell.setBorder(0);
        table.addCell(cell);

        String storeType = preview.getStoreType();
        cell = new PdfPCell(new Phrase("客户类型: " + storeType, chineseFont));
        cell.setBorder(0);
        table.addCell(cell);

        return table;
    }

    /**
     * 抽取文件名
     * @param preview 预览数据
     * @return 返回文件名
     */
    private String extractFileName(WorkshopShipmentsVo preview) {
        Date startOrderDate = preview.getStartOrderDate();
        Date endOrderDate = preview.getEndOrderDate();
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
        String fileName;
        if (startOrderDate != null && endOrderDate != null) {
            String start = dateFormat.format(startOrderDate);
            String end = dateFormat.format(endOrderDate);
            fileName = start + "_" + end;
        } else {
            Date date = new DateTime().plusDays(1).toDate();
            fileName = dateFormat.format(date);
        }
        return fileName + "_" + UUID.randomUUID().toString().substring(0, 8) + ".pdf";
    }
}

package com.pinshang.qingyun.orderreport.mapper;

import com.pinshang.qingyun.orderreport.mapper.entry.ReplenishmentStatisticsEntry;
import com.pinshang.qingyun.orderreport.vo.ReplenishmentStatisticsVo;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface ReplenishmentStatisticsMapper {
    //补货数据汇总：查询最新订单表数据
    List<ReplenishmentStatisticsEntry> queryLatestList(@Param("vo") ReplenishmentStatisticsVo vo);

    //补货数据汇总：查询历史订单表数据
    List<ReplenishmentStatisticsEntry> queryList(@Param("vo") ReplenishmentStatisticsVo vo);

}

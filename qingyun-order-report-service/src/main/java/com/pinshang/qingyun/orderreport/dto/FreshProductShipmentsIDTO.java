package com.pinshang.qingyun.orderreport.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @Date 2019/3/11 11:54
 */
@Data
@ApiModel
public class FreshProductShipmentsIDTO {
    @ApiModelProperty("工厂id")
    private Long factoryId;
    @ApiModelProperty("送货日期")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date orderDate;
    @ApiModelProperty("线路组")
    private Long lineGroupId;
    @ApiModelProperty("配送批次")
    private Integer deliveryBatch;
    @ApiModelProperty(value = "登录用户ID", hidden = true)
    private Long loginUserId;
}

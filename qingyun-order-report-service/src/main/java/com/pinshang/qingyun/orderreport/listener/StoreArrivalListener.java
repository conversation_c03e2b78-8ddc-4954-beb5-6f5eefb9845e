package com.pinshang.qingyun.orderreport.listener;

import java.util.Arrays;
import java.util.List;

import lombok.extern.slf4j.Slf4j;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.stereotype.Component;

import com.alibaba.fastjson.JSON;
import com.pinshang.qingyun.base.annotations.OnlineSwitchWatcher;
import com.pinshang.qingyun.base.api.QYApplicationContext;
import com.pinshang.qingyun.kafka.base.BaseKafkaOnlineSwitchProcessor;
import com.pinshang.qingyun.kafka.base.KafkaMessageWrapper;
import com.pinshang.qingyun.kafka.base.KafkaTopicConstant;
import com.pinshang.qingyun.orderreport.dto.storearrival.StoreArrivalKafkaIDTO;
import com.pinshang.qingyun.orderreport.service.StoreArrivalService;

/**
 * 客户到货
 */
@Slf4j
@Component
@OnlineSwitchWatcher
public class StoreArrivalListener extends BaseKafkaOnlineSwitchProcessor {

	@Autowired
	protected StoreArrivalService storeArrivalService;

	@KafkaListener(id = "${application.name.switch}" + KafkaTopicConstant.STORE_ARRIVAL_TOPIC + "qingyunOrderReportSyncKafkaListenerContainerFactory", 
			topics = { "${application.name.switch}" + KafkaTopicConstant.STORE_ARRIVAL_TOPIC })
	public void listener(String message) {
		KafkaMessageWrapper wrapper = JSON.parseObject(message, KafkaMessageWrapper.class);
		String jsonStr = wrapper.getData().toString();
		if (StringUtils.isEmpty(jsonStr)) {
			log.error("\n客户到货消息，数据为空，message={}", message);
			return;
		}
		
		StoreArrivalKafkaIDTO idto = JSON.parseObject(jsonStr, StoreArrivalKafkaIDTO.class);
		storeArrivalService.saveStoreArrival(idto);
	}

	@Override
	public List<String> getKafkaIds() {
		return Arrays.asList(QYApplicationContext.applicationNameSwitch + KafkaTopicConstant.STORE_ARRIVAL_TOPIC + "qingyunOrderReportSyncKafkaListenerContainerFactory");
	}
}

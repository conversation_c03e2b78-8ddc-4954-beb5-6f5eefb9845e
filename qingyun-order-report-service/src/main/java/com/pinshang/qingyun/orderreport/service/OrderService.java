package com.pinshang.qingyun.orderreport.service;

import com.pinshang.qingyun.box.utils.SpringUtil;
import com.pinshang.qingyun.kafka.MessageOperationType;
import com.pinshang.qingyun.order.dto.order.OrderListInfoODTO;
import com.pinshang.qingyun.order.dto.order.OrderListOrderIdAndCommIDTO;
import com.pinshang.qingyun.order.service.OrderClient;
import com.pinshang.qingyun.orderreport.mapper.*;
import com.pinshang.qingyun.orderreport.model.Order;
import com.pinshang.qingyun.orderreport.model.OrderItem;
import com.pinshang.qingyun.orderreport.model.OrderItemLatest;
import com.pinshang.qingyun.orderreport.model.OrderLatest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.entity.Example;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2019/1/14 16:13.
 */
@Service
@Slf4j
public class OrderService {

    private final OrderMapper orderMapper;
    private final OrderItemMapper orderItemMapper;
    private final OrderLatestMapper orderLatestMapper;
    private final OrderItemLatestMapper orderItemLatestMapper;
    private final OperatorOrderStatisticalService operatorOrderStatisticalService;
    private final TJOrderMirrorService orderMirrorService;
    @Autowired
    private TjOrderSendKafkaService orderSendKafkaService;

    @Autowired
    private OrderClient orderClient;

    @Value("${pinshang.checkCombTypeIsNull:0}")
    private Integer checkCombTypeIsNull;

    public OrderService(OrderMapper orderMapper, OrderItemMapper orderItemMapper, OrderLatestMapper orderLatestMapper, OrderItemLatestMapper orderItemLatestMapper, OperatorOrderStatisticalService operatorOrderStatisticalService, TJOrderMirrorService orderMirrorService) {
        this.orderMapper = orderMapper;
        this.orderItemMapper = orderItemMapper;
        this.orderLatestMapper = orderLatestMapper;
        this.orderItemLatestMapper = orderItemLatestMapper;
        this.operatorOrderStatisticalService = operatorOrderStatisticalService;
        this.orderMirrorService = orderMirrorService;
    }

    /**
     * 处理kafka消息新增订单
     * @param order
     */
    @Transactional(rollbackFor = Exception.class)
    public void processInsertOrder(Order order) {

        // 操作员订单统计处理
        processOperatorOrderStatistical(order, MessageOperationType.INSERT);
        
        this.toInsertOrderInfo(order);

        //新增订单时，记录当时的基础数据：送货员、销售员、督导、大区经理、主任数据
        orderMirrorService.syncTjOrderMirror(Arrays.asList(order.getId()));
    }

    /***
     * 某些入口创建订单 发送统计消息（KafkaTopicConstant.STATISTICAL_ORDER_TOPIC）过来  防止 combType is null 导致统计问题
     * 当存在combType is null 时 查t_order_list 获取comb_type comb_commodityId
     * @param order
     */
    public void processCombTypeIsNull(Order order,String msgUuid) {

        if(this.checkCombTypeIsNull == null || this.checkCombTypeIsNull.intValue() == 0){
            return;
        }
        log.info("exec is method processCombTypeIsNull 处理combType is null 的情况 开关已打开：checkCombTypeIsNull={}",checkCombTypeIsNull);
        if (order == null) {
            return;
        }

        List<OrderItem> orderItemList = order.getOrderList();
        if (SpringUtil.isEmpty(orderItemList)) {
            return;
        }
        Set<Long> combTypeIsNullOrderListIdList = orderItemList.stream().filter(item -> item.getCombType() == null).map(OrderItem::getCommodityId).collect(Collectors.toSet());
        if(SpringUtil.isEmpty(combTypeIsNullOrderListIdList)){
            return;
        }

        log.warn("is method processCombTypeIsNull: 验证统计消息(KafkaTopicConstant.STATISTICAL_ORDER_TOPIC)数据： CombType组合类型存在空值\n" +
                "MQ消息UUID：{} \n 订单编码：{} \n 订单来源：{}", msgUuid, order.getOrderCode(), order.getOrderType());
        List<OrderListInfoODTO> orderList = orderClient.selectOrderListGiftByOrderIdAndCommodityIdList(new OrderListOrderIdAndCommIDTO(order.getId(), combTypeIsNullOrderListIdList));
        if (SpringUtil.isEmpty(orderList)) {
            return;
        }
        /***
         * t_order_list t_order_list_gift 数据量不一致  及小情况下 不会存在同一订单 对应的订单明细   订单明细id+商品id （id+commodityId） 冲突的情况
         */
        Map<String, OrderListInfoODTO> orderListMap = orderList.stream().collect(Collectors.toMap(k -> String.format("%s%s%s", k.getId(), "####", k.getCommodityId()), Function.identity(), (v1, v2) -> v2));
        orderItemList.stream().forEach(c -> {
            if (null == c.getCombType()) {
                String key = String.format("%s%s%s", c.getId(), "####", c.getCommodityId());
                OrderListInfoODTO orderListInfoODTO = orderListMap.get(key);
                if (orderListInfoODTO != null) {
                    c.setCombType(orderListInfoODTO.getCombType());
                    c.setCombCommodityId(orderListInfoODTO.getCombCommodityId());
                }
            }
        });

    }

    /**
     * 去  插入订单信息
     * 
     * @param order
     */
    private void toInsertOrderInfo(Order order) {
    	boolean duplicateKey = false;
    	try {
    		this.doInsertOrderInfo(order);
    	} catch (DuplicateKeyException e) {
    		log.error("\n同步订单信息 - 插入到统计库，主键冲突 order={}，error：", order, e);
    		duplicateKey = true;
    	} finally {
    		if (duplicateKey) {
    			// 1、删除订单相关记录
    			this.doDeleteOrderInfo(order);
    			
    			// 2、重新插入  订单相关记录
    			this.doInsertOrderInfo(order);
    		}
    	}
    }
    /**
     * 插入订单信息
     * 
     * @param order
     */
    private void doInsertOrderInfo(Order order) {
    	if (SpringUtil.isEmpty(order.getOrderList())) {
    		return;
    	}
        // 处理 t_tj_order 表
        orderMapper.insertSelective(order);
        List<OrderItem> orderList = order.getOrderList();
        orderItemMapper.batchInsert(orderList);

        // 处理latest表
        OrderLatest orderLatest = new OrderLatest();
        SpringUtil.copyProperties(order, orderLatest);
        orderLatestMapper.insertSelective(orderLatest);
        List<OrderItemLatest> itemLatests = orderList.stream().map(OrderItem::convert).collect(Collectors.toList());
        orderItemLatestMapper.batchInsert(itemLatests);
    }

    /**
     * 插入记录报主键冲突时，正反删除对应的 t_tj_order、t_tj_order_list、t_tj_order_latest、t_tj_order_latest_list
     *
     * @param order
     */
//    private void doDeleteOrderInfo(Order order) {
//        // 1、根据给定的 orderItemList，删除对应的 t_tj_order、t_tj_order_list、t_tj_order_latest、t_tj_order_latest_list
//        List<OrderItem> orderItemList = order.getOrderList();
//        if (SpringUtil.isNotEmpty(orderItemList)) {
//            List<Long> orderItemIdList = orderItemList.stream().map(OrderItem::getId).collect(Collectors.toList());
//            Example orderItemExample = new Example(OrderItem.class);
//            orderItemExample.createCriteria().andIn("id", orderItemIdList);
//            orderItemExample.selectProperties("id", "orderId");
//            List<OrderItem> existingOrderItemList = orderItemMapper.selectByExample(orderItemExample);
//            if (SpringUtil.isNotEmpty(existingOrderItemList)) {
//                Set<Long> orderIdList = existingOrderItemList.stream().map(OrderItem::getOrderId).collect(Collectors.toSet());
//                orderIdList.forEach(thisOrderId -> {
//                    orderMapper.deleteByPrimaryKey(thisOrderId);
//                    orderLatestMapper.deleteByPrimaryKey(thisOrderId);
//                });
//                existingOrderItemList.forEach(orderItem -> {
//                    orderItemMapper.deleteByPrimaryKey(orderItem.getId());
//                    orderItemLatestMapper.deleteByPrimaryKey(orderItem.getId());
//                });
//            }
//        }
//
//        // 2、根据给定的orderId， 删除对应的 t_tj_order、t_tj_order_list、t_tj_order_latest、t_tj_order_latest_list
//        Long orderId = order.getId();
//        orderMapper.deleteByPrimaryKey(orderId);
//        Example example = new Example(OrderItem.class);
//        example.createCriteria().andEqualTo("orderId", orderId);
//        orderItemMapper.deleteByExample(example);
//
//        orderLatestMapper.deleteByPrimaryKey(orderId);
//        example = new Example(OrderItemLatest.class);
//        example.createCriteria().andEqualTo("orderId", orderId);
//        orderItemLatestMapper.deleteByExample(example);
//    }
    /**
     * 插入记录报主键冲突时，正反删除对应的 t_tj_order、t_tj_order_list、t_tj_order_latest、t_tj_order_latest_list
     * 
     * @param order
     */
    private void doDeleteOrderInfo(Order order) {
		// 根据给定的orderId， 删除对应的 t_tj_order、t_tj_order_list、t_tj_order_latest、t_tj_order_latest_list
        Long orderId = order.getId();
		orderMapper.deleteByPrimaryKey(orderId);
		Example example = new Example(OrderItem.class);
		example.createCriteria().andEqualTo("orderId", orderId);
		orderItemMapper.deleteByExample(example);
		
		orderLatestMapper.deleteByPrimaryKey(orderId);
		example = new Example(OrderItemLatest.class);
		example.createCriteria().andEqualTo("orderId", orderId);
		orderItemLatestMapper.deleteByExample(example);
    }

    /**
     * 处理kafka消息更新订单
     * 优化“修改订单”的监听代码。
        修改订单时验证是否是”客服订单“(order_type=1)，如果是则判断是否是修改“送货日期“，如果是则重新统计此客服的订单数据
        修改订单时，验证统计查询系统是否有此订单，没有则立即把此订单同步到统计查询系统
     * @param order
     */
    @Transactional(rollbackFor = Exception.class)
    public void processUpdateOrder(Order order) {
        //验证统计查询系统是否有此订单
        Order tjOrder = orderMapper.selectByPrimaryKey(order.getId());
        if(tjOrder == null){
            //没有则立即把此订单同步到统计查询系统，且重新统计操作员订单统计数据
            this.processInsertOrder(order);
        }else{
            // 操作员订单统计
            processOperatorOrderStatistical(order, MessageOperationType.UPDATE);

            // 处理 t_tj_order 表
            orderMapper.updateByPrimaryKeySelective(order);

            // 处理 t_tj_order_latest表
            OrderLatest orderLatest = new OrderLatest();
            SpringUtil.copyProperties(order, orderLatest);
            orderLatestMapper.updateByPrimaryKeySelective(orderLatest);

            if (SpringUtil.isNotEmpty(order.getOrderList())) {
                Example ex = new Example(OrderItem.class);
                ex.createCriteria().andEqualTo("orderId", order.getId());
                orderItemMapper.deleteByExample(ex);
                List<OrderItem> orderList = order.getOrderList();
                orderItemMapper.batchInsert(orderList);

                Example latestEx = new Example(OrderItemLatest.class);
                latestEx.createCriteria().andEqualTo("orderId", orderLatest.getId());
                orderItemLatestMapper.deleteByExample(latestEx);
                List<OrderItemLatest> orderLatestList = orderList.stream().map(OrderItem::convert).collect(Collectors.toList());
                orderItemLatestMapper.batchInsert(orderLatestList);
            }
        }
    }

    /**
     * 处理kafka消息取消订单
     * 取消订单优化
     如果取消订单时，统计系统没有此订单则立即把此订单同步到统计查询系统
     验证是否是”客服订单“(order_type=1)，如果是则重新统计此客服的订单数据
     * @param order
     */
    @Transactional(rollbackFor = Exception.class)
    public void processCancelOrder(Order order) {
        //验证统计查询系统是否有此订单
        Order tjOrder = orderMapper.selectByPrimaryKey(order.getId());
        if(tjOrder == null){
            //没有则立即把此订单同步到统计查询系统，且重新统计操作员订单统计数据
            this.processInsertOrder(order);
        }else{
            // 操作员订单统计
            processOperatorOrderStatistical(order, MessageOperationType.CANCEL);

            // 处理order表
            orderMapper.updateByPrimaryKeySelective(order);

            // 处理latest表
            OrderLatest orderLatest = new OrderLatest();
            orderLatest.setId(order.getId());
            orderLatest.setOrderStatus(order.getOrderStatus());
            orderLatest.setUpdateTime(order.getUpdateTime());
            orderLatestMapper.updateByPrimaryKeySelective(orderLatest);
        }

    }

    /**
     * 删除指定用户的订单
     * @param storeIds
     */
    public void discardOrderByStoreIds(List<Long> storeIds) {
        if (SpringUtil.isEmpty(storeIds)) {
            return;
        }

        Example orderEx = new Example(Order.class);
        orderEx.createCriteria().andIn("storeId", storeIds).andEqualTo("orderStatus", 0);
        orderEx.selectProperties("id");
        List<Order> orders = orderMapper.selectByExample(orderEx);
        if (SpringUtil.isEmpty(orders)) {
            return;
        }

        List<Long> orderIds = orders.stream().map(Order::getId).collect(Collectors.toList());
        orderMapper.batchDeleteOrder(orderIds);
        // 注释不再使用的ES，不再需要发消息
        //orderSendKafkaService.sendMsgUpdateEsOrderStatus(orderIds);
    }

    /**
     * 操作员订单统计
     * @param order
     * @param type
     */
    public void processOperatorOrderStatistical(Order order, MessageOperationType type) {
        if (order.getOrderType() != 1) {
            // 不是PC下单
            return;
        }
        switch (type) {
            case INSERT:
                operatorOrderStatisticalService.processOperatorInsertOrder(order);
                break;
            case UPDATE:
                operatorOrderStatisticalService.processOperatorUpdateOrder(order);
                break;
            case CANCEL:
                operatorOrderStatisticalService.processOperatorCancelOrder(order);
                break;
            default:
                log.error("不是新增,也不是修改,也不是取消订单,未知操作.");
                break;
        }
    }
}

package com.pinshang.qingyun.orderreport.vo;

import com.pinshang.qingyun.box.utils.BeanUtil;
//import com.pinshang.qingyun.order.search.dto.DeliveryOrderDetailRespDTO;
import com.pinshang.qingyun.orderreport.mapper.entry.DeliveryOrderDetailEntry;
import com.pinshang.qingyun.orderreport.mapper.entry.DeliveryOrderGiftEntry;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2019/3/6 12:20
 */
@Data
public class DeliveryOrderDetailRespVo {
    /**
     * 订单客户信息
     */
    private DeliveryOrderDetailEntry detail;
    /**
     * 订单商品列表
     */
    private List<DeliveryOrderGiftEntry> orderList;
    /**
     * 订单赠品列表
     */
    private List<DeliveryOrderGiftEntry> giftList;
    /**
     * 订单配货列表
     */
    private List<DeliveryOrderGiftEntry> rationList;
    
    /**
     * 特惠商品列表
     */
    private List<DeliveryOrderGiftEntry> thList;

    public DeliveryOrderDetailRespVo() {
    }

    public DeliveryOrderDetailRespVo(DeliveryOrderDetailEntry detail, List<DeliveryOrderGiftEntry> orderList, List<DeliveryOrderGiftEntry> giftList, List<DeliveryOrderGiftEntry> rationList, List<DeliveryOrderGiftEntry> thList) {
        this.detail = detail;
        this.orderList = orderList;
        this.giftList = giftList;
        this.rationList = rationList;
        this.thList = thList;
    }

//    public static DeliveryOrderDetailRespVo covert(DeliveryOrderDetailRespDTO detailRespDTO){
//        DeliveryOrderDetailRespVo respVo = new DeliveryOrderDetailRespVo();
//
//        respVo.setDetail(BeanUtil.copyProperties(detailRespDTO.getDetail(), DeliveryOrderDetailEntry.class));
//        respVo.setGiftList(BeanUtil.copyProperties(detailRespDTO.getGiftList(), DeliveryOrderGiftEntry.class));
//        respVo.setOrderList(BeanUtil.copyProperties(detailRespDTO.getOrderList(), DeliveryOrderGiftEntry.class));
//        respVo.setRationList(BeanUtil.copyProperties(detailRespDTO.getRationList(), DeliveryOrderGiftEntry.class));
//
//        return respVo;
//    }
}

package com.pinshang.qingyun.orderreport.vo.ps;

import com.pinshang.qingyun.base.page.Pagination;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @Version 0.1.0
 * @Date 2021/1/26 11:27
 * @Copyright © 2019-2021 qm
 * @Description -- 类说明
 **/
@Data
public class QueryCommodityGroupLineVO extends Pagination {

    @ApiModelProperty(value = "线路id")
    private Long lineId;

    @ApiModelProperty(value = "线路组id")
    private Long lineGroupId;

    @ApiModelProperty(value = "商品组id")
    private Long commodityGroupId;

    @ApiModelProperty(value = "发货仓库id")
    private Long deliveryWarehouseId;

    @ApiModelProperty(value = "送货员id")
    private Long deliverymanId;

    @ApiModelProperty(value = "车牌号")
    private String licensePlate;

    @ApiModelProperty(value = "商品组线路状态:1-启用状态，0-停用状态")
    private Integer status;

    @ApiModelProperty(value = "手机号")
    private String deliverymanPhone;

}

package com.pinshang.qingyun.orderreport.service;

import com.alibaba.excel.EasyExcel;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.pinshang.qingyun.base.api.QYApplicationContext;
import com.pinshang.qingyun.base.configure.expand.QYAssert;
import com.pinshang.qingyun.base.constant.RedisKeyPrefixConst;
import com.pinshang.qingyun.base.util.ExcelUtil;
import com.pinshang.qingyun.box.utils.BeanCloneUtils;
import com.pinshang.qingyun.box.utils.BeanUtil;
import com.pinshang.qingyun.box.utils.DateUtil;
import com.pinshang.qingyun.box.utils.SpringUtil;
//import com.pinshang.qingyun.order.search.service.EsOrderDetailClient;
//import com.pinshang.qingyun.orderreport.dto.OrderDetailIDTO;
//import com.pinshang.qingyun.orderreport.dto.OrderDetailODTO;
import com.pinshang.qingyun.orderreport.mapper.OrderDetailMapper;
import com.pinshang.qingyun.orderreport.mapper.entry.OrderDetailEntry;
import com.pinshang.qingyun.orderreport.vo.LateOrderReqVo;
import com.pinshang.qingyun.orderreport.vo.LateOrderRespVo;
import com.pinshang.qingyun.orderreport.vo.OrderDetailReqVo;
import com.pinshang.qingyun.smm.dto.company.CompanyDropDownIDTO;
import com.pinshang.qingyun.smm.dto.company.CompanyDropDownODTO;
import com.pinshang.qingyun.smm.service.CompanyClient;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.redisson.api.RBucket;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2019/3/11 10:50
 */
@Service
@Slf4j
public class OrderDetailService {

    @Autowired
    private OrderDetailMapper orderDetailMapper;
    @Autowired
    private RedissonClient redissonClient;
//    @Autowired
//    private EsOrderDetailClient esOrderDetailClient;
    @Autowired
    private CompanyClient companyClient;

    public Boolean getEsOrderDetailRedisSwitch(){
        RBucket<Boolean> bucket = redissonClient.getBucket(QYApplicationContext.redisKeyProfile + RedisKeyPrefixConst.STATISTICS_ES_SWITCH);
        if (bucket.get()==null || !bucket.get()) {
            return Boolean.FALSE;
        }else{
            RBucket<Boolean> subSwitch = redissonClient.getBucket(QYApplicationContext.redisKeyProfile + RedisKeyPrefixConst.STATISTICS_ES_SWITCH_ORDER_DETAIL);
            if (subSwitch.get()==null || !subSwitch.get()) {
                return Boolean.FALSE;
            }
            return Boolean.TRUE;
        }
    }


    /**
     * 查询订单明细列表数据
     * @param vo
     * @return
     */
    public PageInfo<OrderDetailEntry> queryList(OrderDetailReqVo vo){
        if(this.getEsOrderDetailRedisSwitch()){
            QYAssert.isTrue(Boolean.FALSE,"此功能已经废弃");
//            PageInfo<OrderDetailEntry> result= this.queryListFromEs(vo);
//            List<OrderDetailEntry> dataList = result.getList();
//            /***
//             * 根据公司id调用接口获取公司信息,set公司信息
//             */
//            this.setCompanyName(dataList);
//            return result;
        }
        if(vo.getStartOrderDate() != null){
            vo.setLatestFlag(DateUtil.compareDate(DateUtil.getNowDate(), vo.getStartOrderDate()));
        }
        OrderDetailEntry headItem = orderDetailMapper.queryTotalAmountAndNum(vo);
        if(headItem == null){
            return null;
        }
        headItem.setCommoditySpec("合计:");
        PageInfo<OrderDetailEntry> result = PageHelper.startPage(vo.getPageNo(), vo.getPageSize()).doSelectPageInfo(() -> orderDetailMapper.queryList(vo));
        if(result != null && result.getList() != null && !result.getList().isEmpty()){
            List<OrderDetailEntry> dataList = result.getList();
            /***
             * 根据公司id调用接口获取公司信息,set公司信息
             */
            this.setCompanyName(dataList);
            dataList.add(0, headItem);
        }
        return result;
    }

    /***
     * 根据公司id调用接口获取公司信息 ,set公司信息
     * @param dataList
     */
    private void setCompanyName(List<OrderDetailEntry> dataList){
        if(dataList!=null && dataList.size()>0) {
            List<Long> compayIds = dataList.stream().map(OrderDetailEntry::getCompanyId).collect(Collectors.toList());
            CompanyDropDownIDTO companyDropDownIDTO = new CompanyDropDownIDTO();
            companyDropDownIDTO.setIds(compayIds);
            List<CompanyDropDownODTO> companyDropDownODTOList = companyClient.selectCompanyByIds(companyDropDownIDTO);
            if (companyDropDownODTOList != null && companyDropDownODTOList.size() > 0) {
                Map<Long, String> companyMap = companyDropDownODTOList.stream().collect(Collectors.toMap(CompanyDropDownODTO::getCompanyId, CompanyDropDownODTO::getCompanyName));
                dataList.forEach(o -> {
                    Long companyId = o.getCompanyId();
                    if (companyMap.containsKey(companyId)) {
                        o.setCompanyName(companyMap.get(companyId));
                    }
                });
            }
        }
    }

    /**
     * 分页查询ES
     * @param vo
     * @return
     */
//    public PageInfo<OrderDetailEntry> queryListFromEs(OrderDetailReqVo vo){
//        PageInfo<OrderDetailODTO> esPage = esOrderDetailClient.queryList(buildEsParam(vo));
//        if(esPage!=null && CollectionUtils.isNotEmpty(esPage.getList())){
//            return BeanUtil.copyProperties(esPage, OrderDetailEntry.class);
//        }
//        return new PageInfo<>();
//    }
//    private OrderDetailIDTO buildEsParam(OrderDetailReqVo vo){
//        OrderDetailIDTO orderDetailIDTO = new OrderDetailIDTO();
//        SpringUtil.copyProperties(vo,orderDetailIDTO);
//        orderDetailIDTO.setStartOrderDate(DateUtil.getDateFormate(vo.getStartOrderDate(),"yyyy-MM-dd"));
//        orderDetailIDTO.setEndOrderDate(DateUtil.getDateFormate(vo.getEndOrderDate(),"yyyy-MM-dd"));
//        return orderDetailIDTO;
//    }

    /**
     * 导出订单明细
     */
    public void exportOrderDetail(OrderDetailReqVo orderDetailReqVo, HttpServletResponse httpServletResponse) throws IOException {
        long queryStart = System.currentTimeMillis();
        PageInfo<OrderDetailEntry> orderDetailList = queryList(orderDetailReqVo);
        long queryEnd = System.currentTimeMillis();
        log.info("订单明细----导出查询时间=======" + ((queryStart - queryEnd)/1000));
        try {
            ExcelUtil.setFileNameAndHead(httpServletResponse,"订单明细查询" + System.currentTimeMillis());
            EasyExcel.write(httpServletResponse.getOutputStream(),OrderDetailEntry.class).autoCloseStream(Boolean.FALSE).sheet("订单明细查询").doWrite(orderDetailList == null ? null : orderDetailList.getList());;
        } catch (Exception e) {
            log.error("订单明细导出出错",e);
            ExcelUtil.setExceptionResponse(httpServletResponse);
        }
        long exportEnd = System.currentTimeMillis();
        log.info("订单明细----导出excel时间=======" + (queryEnd - exportEnd)/1000);
    }


    /**
     * 晚订货查询
     * @param lateOrderReqVo
     */
    public PageInfo<LateOrderRespVo> queryLateOrder(LateOrderReqVo lateOrderReqVo) {
        //return orderDetailMapper.queryLateOrder(lateOrderReqVo);
        return PageHelper.startPage(lateOrderReqVo.getPageNo(), lateOrderReqVo.getPageSize()).doSelectPageInfo(() -> orderDetailMapper.queryLateOrder(lateOrderReqVo));
    }

    /**
     * 导出晚订货查询
     */
    public void exportLateOrder(LateOrderReqVo lateOrderReqVo, HttpServletResponse httpServletResponse) throws IOException {
        lateOrderReqVo.setPageSize(Integer.MAX_VALUE);
        long queryStart = System.currentTimeMillis();
        PageInfo<LateOrderRespVo> lateOrderList = queryLateOrder(lateOrderReqVo);
        long queryEnd = System.currentTimeMillis();
        log.info("晚订货----导出查询时间=======" + ((queryStart - queryEnd)/1000));
        try {
            ExcelUtil.setFileNameAndHead(httpServletResponse,"晚订货" + System.currentTimeMillis());
            EasyExcel.write(httpServletResponse.getOutputStream(),LateOrderRespVo.class).autoCloseStream(Boolean.FALSE).sheet("晚订货").doWrite(lateOrderList.getList());;
        } catch (Exception e) {
            log.error("晚订货导出出错",e);
            ExcelUtil.setExceptionResponse(httpServletResponse);
        }
        long exportEnd = System.currentTimeMillis();
        log.info("晚订货----导出excel时间=======" + (queryEnd - exportEnd)/1000);
    }
}

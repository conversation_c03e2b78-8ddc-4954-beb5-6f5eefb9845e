package com.pinshang.qingyun.orderreport.controller;

import com.pinshang.qingyun.base.annotations.RepeatRetryServiceAnno;
import com.pinshang.qingyun.base.configure.expand.QYAssert;
import com.pinshang.qingyun.box.utils.SpringUtil;
import com.pinshang.qingyun.orderreport.config.CustomerProperties;
import com.pinshang.qingyun.orderreport.service.DeliveryListService;
import com.pinshang.qingyun.orderreport.service.PrintingAllowedCheckService;
import com.pinshang.qingyun.orderreport.util.PdfUtils;
import com.pinshang.qingyun.orderreport.vo.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiModelProperty;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.IOException;
import java.util.*;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping(value = "/statistical/deliveryList")
@Api(value = "送货清单api", tags = "deliveryList", description = "送货清单相关接口")

public class DeliveryListController {

    private final DeliveryListService deliveryListService;
    @Autowired
    private CustomerProperties customerProperties;
    @Autowired
    private PrintingAllowedCheckService printingAllowedCheckService;
    public DeliveryListController(DeliveryListService deliveryListService) {
        this.deliveryListService = deliveryListService;
    }

    /**
     * 送货清单列表查询
     *
     * @param params 参数
     * @return 返回JSON数据.
     */
    @RequestMapping(value = "/search",  method = RequestMethod.POST)
    @ApiModelProperty(value = "提货单", notes = "提货单")
    public List<DeliveryListRespVo> searchDeliveryList(@RequestBody DeliveryListReqVo params) {
        return deliveryListService.searchDeliveryList(params);
    }

    /**
     * 送货清单预览
     * @param param 预览参数
     * @return 返回预览参数
     */
    @RequestMapping(value = "/preview", method = RequestMethod.POST)
    public DeliveryListPreviewRespVo preview(@RequestBody DeliveryListPreviewReqVo param) {
        return deliveryListService.preview(param);
    }

    /**
     * 送货清单打印所选中的线路的送货清单
     * @param param 参数
     * @return 成功返回ok
     */
    @RequestMapping(value = "/print", method = RequestMethod.POST)
    public String print(@RequestBody DeliveryListPreviewReqVo param) {
        List<DeliveryListPreviewReqVo> params = new ArrayList<>();
        params.add(param);
        printingAllowedCheckService.isPrintingAllowed(params);
        return deliveryListService.printAll(params,param.getUserId());
    }

    /**
     * 送货清单, 打印所有选中的清单
     * @param params 参数
     * @return 成功返回ok
     */
    @RequestMapping(value = "/printAll/{userId}", method = RequestMethod.POST)
    @RepeatRetryServiceAnno(expireTime=10)
    public String printAll(@RequestBody List<DeliveryListPreviewReqVo> params, @PathVariable Long userId) {
        QYAssert.isTrue(SpringUtil.isNotEmpty(params),"请选择要打印的内容");
        printingAllowedCheckService.isPrintingAllowed(params);
        deliveryListService.asyncPrint(params, userId);
        return "";
    }
    @RequestMapping(value = "/exportZipOrPdf/{userId}",method = RequestMethod.POST)
    @ApiModelProperty(value = "送货清单Zip", notes = "送货清单Zip")
    public void  exportZipOrPdf(@RequestBody DeliveryListPreviewReqVo param, @PathVariable("userId") Long userId, HttpServletRequest request, HttpServletResponse response) throws IOException {
        List<DeliveryListPreviewReqVo> params = new ArrayList<>();
        params.add(param);
        printingAllowedCheckService.isPrintingAllowed(params);
        Map<String,String> strListPdf = deliveryListService.getStrListPdf(params,userId);
        /**
         * 单个生成pdf 多个生成zip
         */
        if(null != strListPdf && strListPdf.size() == 1){
            try {
                strListPdf.forEach((key,value)->{
                    File file = new File(key);
                    PdfUtils.exportPdf(response,file,value+".pdf");
                    return;
                });

            }catch (Exception exception){
                exception.printStackTrace();
            }

        }else {
            Map<String,File> fileList = new HashMap<>();

            Map<String, Integer> sumMap = new HashMap<>();
            for (String filePath : strListPdf.keySet()){
                File file = new File(filePath);
                String newFileName = strListPdf.get(filePath);
                if(fileList.containsKey(newFileName+".pdf")){
                    Integer i = 0;
                    if(sumMap.containsKey(newFileName)){
                        i = sumMap.get(newFileName);
                    }
                    sumMap.put(newFileName,i = i+1);
                    newFileName = newFileName +"("+(i)+")";

                }
                fileList.put(newFileName+".pdf",file);
            }


            String pdfSave = customerProperties.getAbsoluteSavePath();
            String savePath = customerProperties.getSavePath();

            String zipFilePath = pdfSave + savePath + "送货清单.zip";

            try {
                PdfUtils.exportZip(response,fileList,zipFilePath,"送货清单");
            }catch (Exception exception){
                exception.printStackTrace();
            }
        }

    }
    @RequestMapping(value = "/exportZip/{userId}",method = RequestMethod.POST)
    @ApiModelProperty(value = "送货清单Zip", notes = "送货清单Zip")
    public void  exportZip(@RequestBody List<DeliveryListPreviewReqVo> params, @PathVariable("userId") Long userId, HttpServletRequest request, HttpServletResponse response) throws IOException {
        if (SpringUtil.isEmpty(params)) {
            return;
        }
        printingAllowedCheckService.isPrintingAllowed(params);
        Map<String,String> strListPdf = deliveryListService.getStrListPdf(params,userId);

        Map<String,File> fileList = new HashMap<>();

        Map<String, Integer> sumMap = new HashMap<>();
        for (String filePath : strListPdf.keySet()){
            File file = new File(filePath);
            String newFileName = strListPdf.get(filePath);
            if(fileList.containsKey(newFileName+".pdf")){
                Integer i = 0;
                if(sumMap.containsKey(newFileName)){
                    i = sumMap.get(newFileName);
                }
                sumMap.put(newFileName,i = i+1);
                newFileName = newFileName +"("+(i)+")";

            }
            fileList.put(newFileName+".pdf",file);
        }

        String pdfSave = customerProperties.getAbsoluteSavePath();
        String savePath = customerProperties.getSavePath();

        String zipFilePath = pdfSave + savePath + "送货清单.zip";

        try {
            PdfUtils.exportZip(response,fileList,zipFilePath,"送货清单");
        }catch (Exception exception){
            exception.printStackTrace();
        }
    }
}
package com.pinshang.qingyun.orderreport.service;

import com.github.pagehelper.PageInfo;
import com.pinshang.qingyun.base.enums.SmsMessageTypeEnums;
import com.pinshang.qingyun.box.utils.*;
import com.pinshang.qingyun.common.dto.SmsMessageVo;
import com.pinshang.qingyun.order.dto.order.OrderReportJobIDTO;
import com.pinshang.qingyun.order.dto.orderStatistics.OrderStatisticsMonitorIDTO;
import com.pinshang.qingyun.order.dto.orderStatistics.OrderSyncODTO;
import com.pinshang.qingyun.order.dto.sync.SyncOrderListIDTO;
import com.pinshang.qingyun.order.dto.sync.SyncOrderListODTO;
import com.pinshang.qingyun.order.dto.sync.SyncOrderODTO;
import com.pinshang.qingyun.order.service.OrderClient;
import com.pinshang.qingyun.order.service.OrderStatisticsMonitorClient;
import com.pinshang.qingyun.orderreport.mapper.OrderItemLatestMapper;
import com.pinshang.qingyun.orderreport.mapper.OrderItemMapper;
import com.pinshang.qingyun.orderreport.mapper.OrderLatestMapper;
import com.pinshang.qingyun.orderreport.mapper.TJOrderMonitorMapper;
import com.pinshang.qingyun.orderreport.model.*;
import com.pinshang.qingyun.orderreport.vo.TJOrderMonitorVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.entity.Example;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2024/6/20 16:23
 */
@Slf4j
@Service
public class OrderReportJobService {

    @Value("${pinshang.threshold-value}")
    private Long thresholdValue;
    @Autowired
    private OrderStatisticsMonitorClient orderStatisticsMonitorClient;

    @Autowired
    private TJOrderMonitorService orderStatisticsMonitorService;

    @Autowired
    private OrderLatestMapper orderLatestMapper;
    @Autowired
    private OrderClient orderClient;

    @Autowired
    private WeChatSendMessageOrderReportService weChatSendMessageService;

    @Autowired
    private OrderItemMapper orderItemMapper;
    
    @Autowired
    private OrderItemLatestMapper orderItemLatestMapper;
    @Autowired
    private TJOrderMonitorMapper orderStatisticsMonitorMapper;

    /**
     * 定时任务 配置 HH:05:00 || HH:35:00
     * @param date
     * @TODO 添加  pinshang.threshold-value   配置
     */
    public void checkOrderSize(Date date){
        //将时间归整为 00:00 || 30:00
        Date time = orderTimeRoundDown(date);
        //获取指定时间前半小时
        Date endTime = handle60(time);
        Date  startTime= handle60(endTime);
        //查询订单系统
        OrderStatisticsMonitorIDTO orderIDTO = new OrderStatisticsMonitorIDTO();
        orderIDTO.setStartCreateTime(startTime);
        orderIDTO.setEndCreateTime(endTime);
        orderIDTO.setFilterOrderTypeList(Arrays.asList(14,15));
        List<String> list1 = orderStatisticsMonitorClient.queryOrderDiffList(orderIDTO);
        //查询统计系统
        TJOrderMonitorVo tjIDTO = new TJOrderMonitorVo();
        tjIDTO.setStartCreateTime(startTime);
        tjIDTO.setEndCreateTime(endTime);
        tjIDTO.setFilterOrderTypeList(Arrays.asList(14,15));
        List<Order> tjList = orderStatisticsMonitorMapper.queryOrderList(null,tjIDTO,false);
        //t_tj_order_latest
        List<Order> tjListLatest = orderStatisticsMonitorMapper.queryOrderList(null,tjIDTO,true);
        /*
            数据对比 如果两个系统订单数量差异数量大于配置阈值
            则发微信消息进行警告
         */
        //取绝对值和阈值进行比较
        int tjOrderCount = Math.abs(list1.size() - tjList.size());
        int tjOrderLatest = Math.abs(list1.size() - tjListLatest.size());

        if(tjOrderCount > thresholdValue || tjOrderLatest > thresholdValue){
            //发送微信告警
            String str = "订单系统同步统计系统,订单数量异常";
            SmsMessageVo vo = new SmsMessageVo();
            vo.setContent(str);
            vo.setMessageType(SmsMessageTypeEnums.REPORT_INFO_WARN);
            weChatSendMessageService.sendWeiXinMsg(vo);
            diffOrder(list1,tjList,tjListLatest,startTime,endTime);
        }
    }
    private void diffOrder( List<String> list1,List<Order> list2,List<Order> list3,Date startTime,Date endTime){
        List<String> syncList = new ArrayList<>();
        if(SpringUtil.isEmpty(list1)){
            return;
        }

        if(SpringUtil.isEmpty(list2)){
            syncOrder(startTime,endTime,list1);
            return;
        }else {
            List<String> collect1 = list2.stream().map(Order::getOrderCode).collect(Collectors.toList());
            List<String> collect = list1.stream().filter(orderCode -> !collect1.contains(orderCode)).collect(Collectors.toList());
            if(SpringUtil.isNotEmpty(collect)){
                syncList.addAll(collect);
            }
        }
        if(SpringUtil.isEmpty(list3)){
            syncOrder(startTime,endTime,list1);
            return;
        }else {
            List<String> collect1 = list3.stream().map(Order::getOrderCode).collect(Collectors.toList());
            List<String> collect = list1.stream().filter(orderCode -> !collect1.contains(orderCode)).collect(Collectors.toList());
            if(SpringUtil.isNotEmpty(collect)){
                syncList.addAll(collect);
            }
        }
        if(SpringUtil.isNotEmpty(syncList)){
            syncOrder(startTime,endTime,syncList);
        }
    }

    public void syncOrderByOrderCodeList(List<String> orderCodeList){
        OrderStatisticsMonitorIDTO idto = new OrderStatisticsMonitorIDTO();
        idto.setOrderCodeList(orderCodeList);
        int pageNo = 1;
        idto.setPageNo(pageNo);//分页请求订单系统，查询订单
        idto.setPageSize(200);
        int retry = 0;
        PageInfo<OrderSyncODTO> orderResult = null;
        while(pageNo == 1 || (orderResult != null && orderResult.isHasNextPage())) {
            orderResult = orderStatisticsMonitorClient.queryOrderSyncList(idto);
            if (orderResult != null && SpringUtil.isNotEmpty(orderResult.getList())) {
                orderStatisticsMonitorService.syncOrderItem(orderResult.getList());
            } else {
                if (retry++ > 6) {
                    break;
                }
            }
            idto.setPageNo(++pageNo);
        }
    }
    private void syncOrder(Date startTime,Date endTime,List<String> orderCodeList){
        OrderStatisticsMonitorIDTO idto = new OrderStatisticsMonitorIDTO();
//        idto.setStartCreateTime(startTime);
//        idto.setEndCreateTime(endTime);
        idto.setOrderCodeList(orderCodeList);
        int pageNo = 1;
        idto.setPageNo(pageNo);//分页请求订单系统，查询订单
        idto.setPageSize(200);
        int retry = 0;
        PageInfo<OrderSyncODTO> orderResult = null;
        while(pageNo == 1 || (orderResult != null && orderResult.isHasNextPage())) {
            orderResult = orderStatisticsMonitorClient.queryOrderSyncList(idto);
            if (orderResult != null && SpringUtil.isNotEmpty(orderResult.getList())) {
                orderStatisticsMonitorService.syncOrderOrLatest(orderResult.getList());
            } else {
                if (retry++ > 6) {
                    break;
                }
            }
            idto.setPageNo(++pageNo);
        }
    }

    public void checkOrderListSize(Date date){
        //将时间归整为 00:00 || 30:00
        Date time = orderTimeRoundDown(date);
        //获取指定时间前半小时
        Date endTime = handle60(time);
        Date  startTime= handle60(endTime);
        //查询订单系统
        OrderReportJobIDTO orderReportJobIDTO = new OrderReportJobIDTO();
        orderReportJobIDTO.setStartTime(startTime);
        orderReportJobIDTO.setEndTime(endTime);
        Long orderJobCount = orderClient.selectOrderListGiftJob(orderReportJobIDTO);

        //查询统计系统
        TJOrderMonitorVo tjOrderMonitorVo = new TJOrderMonitorVo();
        tjOrderMonitorVo.setStartTime(startTime);
        tjOrderMonitorVo.setEndTime(endTime);
        tjOrderMonitorVo.setFilterOrderTypeList(Arrays.asList(14,15));
        Long orderItemListCount = orderItemMapper.selectOrderItemListCountByOrderCreateTime(tjOrderMonitorVo);
        /*
            数据对比 如果两个系统订单数量差异数量大于配置阈值
            则发微信消息进行警告
         */
        if(Objects.nonNull(orderJobCount) && Objects.nonNull(orderItemListCount)  ){
            //取绝对值和阈值进行比较
            long tjOrderCount = Math.abs(orderJobCount - orderItemListCount);
            if(tjOrderCount > thresholdValue){
                //发送微信告警
                String str = "订单系统同步统计系统,订单明细数量异常";
                SmsMessageVo vo = new SmsMessageVo();
                vo.setContent(str);
                vo.setMessageType(SmsMessageTypeEnums.REPORT_INFO_WARN);
                weChatSendMessageService.sendWeiXinMsg(vo);
            }
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public void checkOrderListLatestSize(Date date){
        //将时间归整为 00:00 || 30:00
        Date time = orderTimeRoundDown(date);
        //获取指定时间前半小时
        Date endTime = handle60(time);
        Date  startTime= handle60(endTime);
        //查询统计系统
        TJOrderMonitorVo tjOrderMonitorVo = new TJOrderMonitorVo();
        tjOrderMonitorVo.setStartTime(startTime);
        tjOrderMonitorVo.setEndTime(endTime);
        tjOrderMonitorVo.setFilterOrderTypeList(Arrays.asList(14,15));
        Long orderItemListCount = orderItemMapper.selectOrderItemListCountByOrderCreateTime(tjOrderMonitorVo);

        Long orderItemLatestListCount = orderItemLatestMapper.selectOrderItemLatestListCountByOrderCreateTime(tjOrderMonitorVo);

        if(Objects.equals(orderItemListCount,orderItemLatestListCount)){
            return;
        }

        List<OrderItem> orderItemList = orderItemMapper.selectOrderItemListByOrderCreateTime(tjOrderMonitorVo);

        List<OrderItemLatest> orderItemLatestList = orderItemLatestMapper.selectOrderItemLatestListByOrderCreateTime(tjOrderMonitorVo);

        Map<Long, List<OrderItemLatest>> orderItemLatestMap = orderItemLatestList.stream().collect(Collectors.groupingBy(OrderItemLatest::getOrderId));

        List<Long> orderIdList = new ArrayList<>();
        List<OrderItem> addOrderItemList = new ArrayList<>();
        orderItemList.stream().collect(Collectors.groupingBy(OrderItem::getOrderId)).forEach((key,value)->{
            if(orderItemLatestMap.containsKey(key)){
                if(Objects.equals(orderItemLatestMap.get(key).size(), value.size())){

                }else {
                    orderIdList.add(key);

                    addOrderItemList.addAll(value);
                }
            }else {
                orderIdList.add(key);
                addOrderItemList.addAll(value);
            }
        });

        if(SpringUtil.isNotEmpty(orderIdList)){
            Example example = new Example(OrderItemLatest.class);
            example.createCriteria().andIn("orderId",orderIdList);
            orderItemLatestMapper.deleteByExample(example);
            List<OrderItemLatest> orderItemLatests = BeanCloneUtils.copyTo(addOrderItemList,OrderItemLatest.class);
            orderItemLatestMapper.insertList(orderItemLatests);

            //发送微信告警
            String str = "订单系统同步统计系统,订单数量异常-Latest";
            SmsMessageVo vo = new SmsMessageVo();
            vo.setContent(str);
            vo.setMessageType(SmsMessageTypeEnums.REPORT_INFO_WARN);
            weChatSendMessageService.sendWeiXinMsg(vo);
        }
    }

    public void repeatHaving1(){
        List<OrderItem> orderItemList = orderItemMapper.repeatHaving1();
        if(SpringUtil.isNotEmpty(orderItemList)){
            //发送微信告警
            String str = "订单系统同步统计系统,订单明细数据有重复";
            SmsMessageVo vo = new SmsMessageVo();
            vo.setContent(str);
            vo.setMessageType(SmsMessageTypeEnums.REPORT_INFO_WARN);
            weChatSendMessageService.sendWeiXinMsg(vo);
        }
    }

    /**
     *
     * 1、查询pinshang库订单
     * 2、查询tj库对应的 t_tj_order,t_tj_order_list,t_tj_order_latest,t_tj_order_list_latest
     *      将敏感字段抽取进行数据对比
     *  2.1 检查pinshang库订单tj库是否存在,如果存在进行同步
     *  2.2 对比pinshang库订单和tj库订单是否一致,如果不一致进行同步
     *  2.3 对比pinshang库订单明细tj库是否存在,如果存在进行同步
     *  2.4 对比pinshang库订单明细和tj库中是否一致,如果不一致进行同步
     * 3、调用订单补偿页面接口进行补偿
     * @param forwardAmount 提前X分钟
     * @param amount 查询几分钟的数据进行对比
     */
    public void syncOrderList(Integer forwardAmount,Integer amount){
        if(null == forwardAmount ){
            forwardAmount = 60;
        }
        if(null == amount || amount > 200){
            amount = 60;
        }
        //将时间归整为 00:00 || 30:00
//        Date time = orderTimeRoundDown(new Date());
        Date time = new Date();
        Date endTime = handleX(time,forwardAmount);
        Date  startTime= handleX(endTime,amount);

        String startTimeStr = DateTimeUtil.formatDate(startTime, DateTimeUtil.YYYY_MM_DD_HH_MM_SS);
        String endTimeStr = DateTimeUtil.formatDate(endTime, DateTimeUtil.YYYY_MM_DD_HH_MM_SS);
        SyncOrderListIDTO syncOrderListIDTO = new SyncOrderListIDTO();
        syncOrderListIDTO.setBTime(startTimeStr);
        syncOrderListIDTO.setETime(endTimeStr);
        List<SyncOrderODTO> syncOrderListODTOS = orderClient.syncOrderList(syncOrderListIDTO);
        if (SpringUtil.isEmpty(syncOrderListODTOS)) {
        	log.info("\n 订单监控告警-无需处理：主库订单更新时间{}到{}，未查询到订单信息", startTimeStr, endTimeStr);
        	return;
        }


        List<String> orderCodeS = syncOrderListODTOS.stream().map(SyncOrderODTO::getOrderCode).collect(Collectors.toList());

        List<Order> tjList = orderStatisticsMonitorMapper.queryOrderList(orderCodeS,null,false);
        //t_tj_order_latest
        List<Order> tjListLatest = orderStatisticsMonitorMapper.queryOrderList(orderCodeS,null,true);

        Map<Long, Order> tjListMap = tjList.stream().collect(Collectors.toMap(Order::getId, e -> e));
        Map<Long, Order> tjListLatestMap = tjListLatest.stream().collect(Collectors.toMap(Order::getId, e -> e));

        List<Long> orderIdList = tjListMap.keySet().stream().collect(Collectors.toList());
        List<OrderItem> orderItemList1 = orderItemMapper.selectOrderItemListByOrderIdList(orderIdList);
        Map<Long, List<OrderItem>> tjItemMap = orderItemList1.stream().collect(Collectors.groupingBy(OrderItem::getOrderId));

        List<OrderItemLatest> orderItemLatests = orderItemLatestMapper.selectOrderItemLatestListByOrderIdList(orderIdList);
        Map<Long, List<OrderItemLatest>> tjItemLatestMap = orderItemLatests.stream().collect(Collectors.groupingBy(OrderItemLatest::getOrderId));

        List<String> orderCodeList = new ArrayList<>();

        List<String> errorOrderStr = new ArrayList<>();
        Date nowDate = DateUtil.getNowDate();
        for (SyncOrderODTO syncOrderListODTO : syncOrderListODTOS) {
            Long orderId = syncOrderListODTO.getOrderId();
            String orderCode = syncOrderListODTO.getOrderCode();
            Date orderTime = syncOrderListODTO.getOrderTime();
            List<SyncOrderListODTO> syncOrderListODTOList = syncOrderListODTO.getSyncOrderListODTOList();
            if(!tjListMap.containsKey(orderId)){
                orderCodeList.add(orderCode);
                if(errorOrderStr.size() < 11){
                    errorOrderStr.add("订单编码不存在--"+orderCode);
                }
                continue;
            }

            Order tjOrder = tjListMap.get(orderId);
            String order = syncOrderListODTO.toSyncString();

            if(!Objects.equals(order,tjOrder.toSyncString())){
                orderCodeList.add(orderCode);
                if(errorOrderStr.size() < 11) {
                    errorOrderStr.add("订单数据不一致--order=" + order + ",tjOrder=" + tjOrder.toSyncString());
                }
                continue;
            }

            if(!tjItemMap.containsKey(orderId)){
                orderCodeList.add(orderCode);
                if(errorOrderStr.size() < 11) {
                    errorOrderStr.add("订单明细不存在--" + orderCode);
                }
                continue;
            }
            String orderListStr = getOrderList(syncOrderListODTOList);
            List<OrderItem> orderItemList = tjItemMap.get(orderId);
            if(!Objects.equals(orderListStr,getTjOrderList(orderItemList))){
                orderCodeList.add(orderCode);
                if(errorOrderStr.size() < 11) {
                    errorOrderStr.add("订单明细数据不一致--orderListStr=" + orderListStr + ",tjItem=" + getTjOrderList(orderItemList));
                }
                continue;
            }
            if(nowDate.compareTo(orderTime) <= 0){
                Order tjLatestOrder =tjListLatestMap.get(orderId);
                List<OrderItemLatest> orderItemLatestList = tjItemLatestMap.get(orderId);
                if(!tjListLatestMap.containsKey(orderId)){
                    orderCodeList.add(orderCode);
                    if(errorOrderStr.size() < 11){
                        errorOrderStr.add("订单编码不存在--"+orderCode);
                    }
                    continue;
                }
                if(!Objects.equals(order,tjLatestOrder.toSyncString())){
                    orderCodeList.add(orderCode);
                    if(errorOrderStr.size() < 11) {
                        errorOrderStr.add("订单数据不一致--order=" + order + ",tjLatestOrder=" + tjLatestOrder.toSyncString());
                    }
                    continue;
                }
                if(!tjItemLatestMap.containsKey(orderId)){
                    orderCodeList.add(orderCode);
                    if(errorOrderStr.size() < 11) {
                        errorOrderStr.add("订单明细不存在--" + orderCode);
                    }
                    continue;
                }
                if(!Objects.equals(orderListStr,getTjOrderListLatest(orderItemLatestList))){
                    orderCodeList.add(orderCode);
                    if(errorOrderStr.size() < 11) {
                        errorOrderStr.add("订单明细数据不一致--orderListStr=" + orderListStr + ",tjItemLatest=" + getTjOrderListLatest(orderItemLatestList));
                    }
                }
            }
        }
        if(SpringUtil.isNotEmpty(orderCodeList)){
            List<String> orderCodeSet = orderCodeList.stream().distinct().collect(Collectors.toList());
            log.error("订单同步异常数据详情,errorOrderStr={}",errorOrderStr);
            //发送微信告警
            String str = "订单系统同步统计系统,差异订单:"+orderCodeSet+"正在尝试同步";
            SmsMessageVo vo = new SmsMessageVo();
            vo.setContent(str);
            vo.setMessageType(SmsMessageTypeEnums.REPORT_INFO_WARN);
            weChatSendMessageService.sendWeiXinMsg(vo);
            try {
                syncOrderByOrderCodeList(orderCodeSet);
            }catch (Exception exception){
                //发送微信告警
                String exStr = "订单系统同步统计系统,订单补偿失败";
                SmsMessageVo exVo = new SmsMessageVo();
                exVo.setContent(exStr);
                exVo.setMessageType(SmsMessageTypeEnums.REPORT_INFO_WARN);
                weChatSendMessageService.sendWeiXinMsg(exVo);
            }
        }

    }

    private String getOrderList(List<SyncOrderListODTO> syncOrderListODTOList){
        StringBuilder stringBuilder = new StringBuilder();
        for (SyncOrderListODTO syncOrderListODTO : syncOrderListODTOList) {
            stringBuilder.append(syncOrderListODTO.toSyncString());
        }
        return stringBuilder.toString();
    }
    private String getTjOrderList(List<OrderItem> orderItemList){
        StringBuilder stringBuilder = new StringBuilder();
        for (OrderItem orderItem : orderItemList) {
            stringBuilder.append(orderItem.toSyncString());
        }
        return stringBuilder.toString();
    }
    private String getTjOrderListLatest(List<OrderItemLatest> orderItemLatests){
        StringBuilder stringBuilder = new StringBuilder();
        for (OrderItemLatest orderItemLatest : orderItemLatests) {
            stringBuilder.append(orderItemLatest.toSyncString());
        }
        return stringBuilder.toString();
    }
    /**
     * 将时间归整
     * 15:27 => 15:30
     * 15:34 => 15:30
     * @param date
     * @return
     */
    public static Date orderTimeRoundDown(Date date){
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        BigDecimal bigDecimal = new BigDecimal(date.getTime()- calendar.getTime().getTime()).divide(new BigDecimal(1000 * 60 * 30), 2, RoundingMode.HALF_UP).setScale(0, BigDecimal.ROUND_HALF_UP);
        BigDecimal multiply = bigDecimal.divide(new BigDecimal(2)).multiply(new BigDecimal(60));
        Calendar calendar1 = Calendar.getInstance();
        calendar1.setTime(date);
        calendar1.set(Calendar.HOUR_OF_DAY, 0);
        calendar1.set(Calendar.MINUTE, 0);
        calendar1.set(Calendar.SECOND, 0);
        calendar1.add(Calendar.MINUTE,multiply.intValue());
        return calendar1.getTime();
    }

    public static Date handle30(Date date){
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.add(Calendar.MINUTE, -30);
        return calendar.getTime();
    }

    public static Date handle60(Date date){
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.add(Calendar.MINUTE, -60);
        return calendar.getTime();
    }
    public static Date handleX(Date date, Integer integer){
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.add(Calendar.MINUTE, -integer);
        return calendar.getTime();
    }
}

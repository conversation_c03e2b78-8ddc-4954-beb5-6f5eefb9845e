package com.pinshang.qingyun.orderreport.mapper;

import com.pinshang.qingyun.orderreport.mapper.entry.OrderDetailEntry;
import com.pinshang.qingyun.orderreport.vo.LateOrderReqVo;
import com.pinshang.qingyun.orderreport.vo.LateOrderRespVo;
import com.pinshang.qingyun.orderreport.vo.OrderDetailReqVo;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2019/3/11 10:54
 */
@Repository
public interface OrderDetailMapper {
    List<OrderDetailEntry> queryList(@Param("reqVo")OrderDetailReqVo reqVo);
    OrderDetailEntry queryTotalAmountAndNum(@Param("reqVo")OrderDetailReqVo reqVo);

    List<LateOrderRespVo> queryLateOrder(LateOrderReqVo lateOrderReqVo);
}

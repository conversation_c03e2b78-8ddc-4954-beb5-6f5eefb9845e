package com.pinshang.qingyun.orderreport.controller;

import com.alibaba.excel.EasyExcel;
import com.github.pagehelper.PageInfo;
import com.pinshang.qingyun.base.annotations.RepeatRetryServiceAnno;
import com.pinshang.qingyun.base.api.TokenInfo;
import com.pinshang.qingyun.base.configure.expand.QYAssert;
import com.pinshang.qingyun.base.util.ExcelUtil;
import com.pinshang.qingyun.base.util.FastThreadLocalUtil;
import com.pinshang.qingyun.box.utils.*;
import com.pinshang.qingyun.common.dto.DictionaryIDTO;
import com.pinshang.qingyun.common.dto.DictionaryODTO;
import com.pinshang.qingyun.common.service.DictionaryClient;
import com.pinshang.qingyun.infrastructure.exportfile.cache.starter.FileCacheQuery;
import com.pinshang.qingyun.infrastructure.exportfile.cache.starter.FileCacheQueryParameter;
import com.pinshang.qingyun.orderreport.enums.ProductSaleStatisticsTypeEnums;
import com.pinshang.qingyun.orderreport.mapper.entry.CommoditySaleStatisticsMonitorEntry;
import com.pinshang.qingyun.orderreport.service.CommoditySaleStatisticsService;
import com.pinshang.qingyun.orderreport.service.CommodityService;
import com.pinshang.qingyun.orderreport.service.FactoryDeliveryStatisticsService;
import com.pinshang.qingyun.orderreport.util.DateUtils;
import com.pinshang.qingyun.orderreport.vo.CommoditySaleStatisticsItemRespVo;
import com.pinshang.qingyun.orderreport.vo.CommoditySaleStatisticsMakeUpReqVo;
import com.pinshang.qingyun.orderreport.vo.CommoditySaleStatisticsReqVo;
import com.pinshang.qingyun.orderreport.vo.CommoditySaleStatisticsRespVo;
import com.pinshang.qingyun.smm.dto.user.SelectUserFactoryIdListIDTO;
import com.pinshang.qingyun.smm.service.SMMUserClient;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.propertyeditors.CustomDateEditor;
import org.springframework.http.MediaType;
import org.springframework.web.bind.WebDataBinder;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.math.BigDecimal;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 产品销售汇总（日报/月报）
 */
@CrossOrigin
@RestController
@RequestMapping(value = "/statistical/commoditySaleStatistics")
@Slf4j
public class CommoditySaleStatisticsController {

    @Autowired
    private CommoditySaleStatisticsService commoditySaleStatisticsService;
    @Autowired
    private RedissonClient redissonClient;
    @Autowired
    private CommodityService commodityService;
    @Autowired
    private FactoryDeliveryStatisticsService factoryDeliveryStatisticsService;
    @Autowired
    private DictionaryClient dictionaryClient;
    @Autowired
    private SMMUserClient userClient;

    /**
     * 产品销售汇总监控--比较
     * @param vo
     * @return
     */
    //todo 产品销售汇总比较
    @RequestMapping(value = "/commoditySaleStatisticsCompareInfo", method = RequestMethod.POST)
    public CommoditySaleStatisticsMonitorEntry commoditySaleStatisticsCompareInfo(@RequestBody CommoditySaleStatisticsMakeUpReqVo vo){
        return  commoditySaleStatisticsService.commoditySaleStatisticsCompareInfo(vo);
    }

    /**
     * 手动补偿
     * 较第一版的区别：
     *  1.各业务分开汇总补偿；
     *  2.优化日期区间的循环；
     *  3.商品汇总补偿 先按天或按月删除原有数据;
     *  4.工厂发货汇总补偿 先按天删除原有数据;
     * @return
     */
    //todo 产品销售汇总监控补偿
    @PostMapping("/commoditySaleStatisticsMakeUp")
    @RepeatRetryServiceAnno
    public void commoditySaleStatisticsMakeUp(@RequestBody CommoditySaleStatisticsMakeUpReqVo commoditySaleStatisticsMakeUpReqVo) throws ParseException {
        RLock lock = redissonClient.getLock(this.getClass().getName());
        if(lock.tryLock()){
            try {
                int paramType = commoditySaleStatisticsMakeUpReqVo.getParamType();//1=日报；2=月报
                String orderTime = commoditySaleStatisticsMakeUpReqVo.getOrderTime();
                String startTime = commoditySaleStatisticsMakeUpReqVo.getStartDate();
                String endTime = commoditySaleStatisticsMakeUpReqVo.getEndDate();
                //组装date
                List<String> dateList = new ArrayList<>();
                if(StringUtils.isNotBlank(orderTime)){
                    dateList.add(orderTime);
                    startTime = orderTime;
                    endTime = orderTime;
                }else{
                    dateList = paramType==1?DateUtil.getBetweenDates(startTime,endTime):DateUtil.getBetweenMonths(startTime,endTime);
                }
                List<Integer> businessType = commoditySaleStatisticsMakeUpReqVo.getBusinessType();
                Boolean commodityStatsFlag = SpringUtil.isEmpty(businessType)?true:businessType.contains(ProductSaleStatisticsTypeEnums.COMMODITY_STATS.getCode());
                Boolean supervisorStatsFlag = SpringUtil.isEmpty(businessType)?true:businessType.contains(ProductSaleStatisticsTypeEnums.SUPERVISOR_STATS.getCode());
                Boolean factoryStatsFlag = SpringUtil.isEmpty(businessType)?true:businessType.contains(ProductSaleStatisticsTypeEnums.FACTORY_DELIVERY_STATS.getCode());

                for(String date : dateList){
                    try{
                        if(paramType == 1){
                            if(commodityStatsFlag){
                                /*** 补偿 产品销售汇总--日报  t_tj_commodity_sale_statistics、t_tj_commodity_sale_statistics_item */
                                commoditySaleStatisticsService.deleteDataByDay(commoditySaleStatisticsMakeUpReqVo.getCommodityId(),date);
                                this.loopPageByDay(commoditySaleStatisticsMakeUpReqVo,date);
                            }
                            if(supervisorStatsFlag){
                                /*** 补偿 日报表 督导明细  t_tj_commodity_sale_statistics_supervisor*/
                                commoditySaleStatisticsService.commoditySaleStatisticsSupervisorMakeUp(commoditySaleStatisticsMakeUpReqVo.getCommodityId(),date);
                            }
                            if(factoryStatsFlag){
                                /*** 手动补偿时先删除指定条件的记录，解决重复补偿 */
                                factoryDeliveryStatisticsService.deleteFactoryDeliveryStatistics(commoditySaleStatisticsMakeUpReqVo.getCommodityId(),date);
                                /*** 产品销售汇总--工厂送货汇总 */
                                factoryDeliveryStatisticsService.insertFactoryDeliveryStatistics(DateUtil.parseDate(date,"yyyy-MM-dd"),"t_tj_order","t_tj_order_list");
                            }
                        }else{
                            if(commodityStatsFlag) {
                                commoditySaleStatisticsService.deleteDataByMonth(commoditySaleStatisticsMakeUpReqVo.getCommodityId(),date);
                                this.loopPageByMonth(commoditySaleStatisticsMakeUpReqVo, date);
                            }
                            if(supervisorStatsFlag) {
                                /***督导月报表 */
                                commoditySaleStatisticsService.commoditySaleStatisticsMonthSupervisor(commoditySaleStatisticsMakeUpReqVo.getCommodityId(), date);
                            }
                        }
                    }catch (Exception e){
                        log.error("手动补偿出错,时间：{}",date,e);
                    }
                }
                /***自动补偿： 当以上天的数据变化了，那么对月报的数据也要更新一遍 */
                if(paramType == 1){
                    List<String> times = DateUtils.sameMonth(startTime,endTime);
                    for(String time: times){
                        try {
                            if(commodityStatsFlag) {
                                commoditySaleStatisticsService.deleteDataByMonth(commoditySaleStatisticsMakeUpReqVo.getCommodityId(),time);
                                this.loopPageByMonth(commoditySaleStatisticsMakeUpReqVo, time);
                            }
                            if(supervisorStatsFlag) {
                                //督导月报表
                                commoditySaleStatisticsService.commoditySaleStatisticsMonthSupervisor(commoditySaleStatisticsMakeUpReqVo.getCommodityId(), time);
                            }
                        }catch (Exception e){
                            log.error("手动补偿出错,时间：{}",time,e);
                        }
                    }
                }
            } finally {
                lock.unlock();
            }
        }
    }

    /**
     * 产品销售汇总数据查询
     * @param commoditySaleStatisticsReqVo
     * @return
     */
    //todo 产品销售汇总商品、月报
    @PostMapping("/queryCommoditySaleStatistics")
    @ApiModelProperty(value = "产品销售汇总(商品)", notes = "产品销售汇总(商品)")
    PageInfo<CommoditySaleStatisticsRespVo> queryCommoditySaleStatistics(@RequestBody CommoditySaleStatisticsReqVo commoditySaleStatisticsReqVo){
        if(commoditySaleStatisticsReqVo.getFactoryId() == null){
            List<Long> factoryIds = userClient.selectUserFactoryIdList(SelectUserFactoryIdListIDTO.onlyDb(commoditySaleStatisticsReqVo.getLoginUserId()));
            if(SpringUtil.isEmpty(factoryIds)){
                return new PageInfo<>();
            }
            commoditySaleStatisticsReqVo.setFactoryIds(factoryIds);
        }
        return commoditySaleStatisticsService.queryCommoditySaleStatistics(commoditySaleStatisticsReqVo);
    }

    @ApiOperation(value = "产品销售汇总数据（导出）", notes = "产品销售汇总数据（导出）")
    @RequestMapping(value = "/commoditySaleStatisticsExportData", method = RequestMethod.GET)
    @FileCacheQuery(bizCode = "COMMODITY_SALE_STATISTICS_E")
    public void commoditySaleStatisticsExportData(@FileCacheQueryParameter CommoditySaleStatisticsReqVo dto, HttpServletResponse response) throws IOException {
        long qS = System.currentTimeMillis();
        TokenInfo info = FastThreadLocalUtil.getQY();
        dto.setLoginUserId(info.getUserId());
        if(dto.getFactoryId() == null){
            List<Long> factoryIds = userClient.selectUserFactoryIdList(SelectUserFactoryIdListIDTO.onlyDb(dto.getLoginUserId()));
            if(SpringUtil.isEmpty(factoryIds)){
                return ;
            }
            dto.setFactoryIds(factoryIds);
        }
        //获取客户类型
        DictionaryIDTO dictionaryVo = new DictionaryIDTO();
        dictionaryVo.setDictionaryId(5043047439271865177L);
        List<com.pinshang.qingyun.common.dto.DictionaryODTO> dictionaryList = dictionaryClient.getDictionaryList(dictionaryVo);

        //获取汇总数据源
        dto.initExportPage();
        List<CommoditySaleStatisticsRespVo> commoditySaleStatisticsList =  commoditySaleStatisticsService.queryCommoditySaleStatistics(dto).getList();
        long qE = System.currentTimeMillis();
        log.info("产品销售汇总(商品/月)-导出--查询时间=" + ((qE - qS) / 1000));

        // 文件名
        String dayOrMonth = dto.getParamType()==1?"(日)":"(月)";

        String fileName = "产品销售汇总" + dayOrMonth + DateTimeUtil.formatDate(new Date(), "yyyyMMddHHmmss");

        List<List<String>> tableHeader = initTableHeader(dictionaryList);
        List<List<String>> tableData = initTableData(tableHeader.size(), dictionaryList, commoditySaleStatisticsList);
        try {
            com.pinshang.qingyun.base.util.ExcelUtil.setFileNameAndHead(response, fileName);
            EasyExcel.write(response.getOutputStream()).head(tableHeader).autoCloseStream(Boolean.FALSE).sheet("产品销售汇总")
                    .doWrite(tableData);

        }catch (Exception e){
            log.error("产品销售汇总(商品/月)导出错误", e);
            ExcelUtil.setExceptionResponse( response );
        }
        long eE = System.currentTimeMillis();
        log.info("产品销售汇总(商品/月)-导出--excel处理时间="+ ( (eE -qE) /1000 )  );
    }

    /**
     * 表头
     * @param dictionaryList
     * @return
     */
    private List<List<String>> initTableHeader(List<com.pinshang.qingyun.common.dto.DictionaryODTO> dictionaryList){
        List<List<String>> tableHeader = new ArrayList<>(dictionaryList.size()*2+11);
        //excel表头组装
        List<String> constHeader = Arrays.asList("工厂","生产组","商品编码","商品名称","计量单位","车间","大类","中类","小类","数量小计","金额小计");
        constHeader.forEach(header -> tableHeader.add(Arrays.asList(header, header)));

        if (CollectionUtils.isNotEmpty(dictionaryList)) {
            dictionaryList.forEach(item -> {
                tableHeader.add(Arrays.asList(item.getOptionName(), "数量"));
                tableHeader.add(Arrays.asList(item.getOptionName(), "金额"));
            });
        }

        tableHeader.add(Arrays.asList("商品税率","商品税率"));

        return tableHeader;
    }

    /**
     * 数据
     * @param cellLength
     * @param dictionaryList
     * @param saleStatisticsRespVos
     * @return
     */
    private List<List<String>> initTableData(int cellLength, List<com.pinshang.qingyun.common.dto.DictionaryODTO> dictionaryList, List<CommoditySaleStatisticsRespVo> saleStatisticsRespVos){
        if (CollectionUtils.isEmpty(saleStatisticsRespVos)){
            return Collections.EMPTY_LIST;
        }

        List<List<String>> tableData = new ArrayList<>(saleStatisticsRespVos.size()+1);
        String[] empty = new String[cellLength];
        List<String> firstRow = new ArrayList<>(Arrays.asList(empty));
        firstRow.set(8, "合计:");
        tableData.add(firstRow);

        //合计金额：key:cell下标 value:金额
        Map<Integer, BigDecimal> allAmount = new HashMap<>();
        Map<String,String> taxRateMap=commoditySaleStatisticsService.getCommdityTaxRateValue(commoditySaleStatisticsService.getTaxrateDictionaryId());
        for (int i =0; i < saleStatisticsRespVos.size(); i++){
            //总金额小计下标
            Integer sumIndex = 10;
            CommoditySaleStatisticsRespVo vo = saleStatisticsRespVos.get(i);
            if(StringUtils.isNotBlank(vo.getTaxRateId())){
                vo.setTaxRateValue(taxRateMap.get(vo.getTaxRateId().trim()));
            }
            List<String> data = new ArrayList<>(cellLength);
            data.add(vo.getFactoryName());
            data.add(vo.getWorkshopName());
            data.add(vo.getCommodityCode());
            data.add(vo.getCommodityName());
            data.add(vo.getCommodityUnitName());
            data.add(vo.getFlowshopName());
            data.add(vo.getCateName1());
            data.add(vo.getCateName2());
            data.add(vo.getCateName3());
            data.add(subZeroAndDot(String.valueOf(vo.getTotalQuantity())));
            data.add(subZeroAndDot(String.valueOf(vo.getTotalAmount())));
            if (allAmount.containsKey(sumIndex)) {
                allAmount.put(sumIndex, allAmount.get(sumIndex).add(vo.getTotalAmount()));
            } else {
                allAmount.put(sumIndex, vo.getTotalAmount());
            }
            data.addAll(Arrays.asList(new String[cellLength-11]));

            //根据StoreTypeId进行分组
            Map<Long, List<CommoditySaleStatisticsItemRespVo>> collect = vo.getCommoditySaleStatisticsItems().parallelStream().collect(Collectors.groupingBy(CommoditySaleStatisticsItemRespVo::getStoreTypeId));
            //根据客户类型set 金额、数量
            for (DictionaryODTO dictionaryODTO : dictionaryList){
                sumIndex += 2;
                List<CommoditySaleStatisticsItemRespVo> itemRespVoList = collect.get(Long.valueOf(dictionaryODTO.getId()));
                if (CollectionUtils.isNotEmpty(itemRespVoList)){
                    CommoditySaleStatisticsItemRespVo itemRespVos =  itemRespVoList.get(0);
                    data.set(sumIndex-1, subZeroAndDot(String.valueOf(itemRespVos.getTotalQuantity())));
                    data.set(sumIndex, subZeroAndDot(String.valueOf(itemRespVos.getTotalAmount())));

                    if (allAmount.containsKey(sumIndex)) {
                        allAmount.put(sumIndex, allAmount.get(sumIndex).add(itemRespVos.getTotalAmount()));
                    } else {
                        allAmount.put(sumIndex, itemRespVos.getTotalAmount());
                    }
                }
            }
            data.set(sumIndex+1,vo.getTaxRateValue());
            tableData.add(data);
        }

        //合计金额
        allAmount.keySet().forEach(k->firstRow.set(k, subZeroAndDot(String.valueOf(allAmount.get(k)))));

        return tableData;
    }

    /**
     * 去掉给定数字多余的0
     * @param s 给定的数字
     * @return 返回去除后的值
     */
    public static String subZeroAndDot(String s) {
        if (s.indexOf(".") > 0) {
            s = s.replaceAll("0+?$", "");//去掉多余的0
            s = s.replaceAll("[.]$", "");//如最后一位是.则去掉
        }
        return s;
    }


    /**
     * 商品汇总，每月定时任务
     * @return
     */
    //todo 商品每月汇总定时任务
    @GetMapping("/commoditySaleStatisticsMonth")
    public void commoditySaleStatisticsMonth() {
        RLock lock = redissonClient.getLock(this.getClass().getName());
        if(lock.tryLock()){
            try {
                SimpleDateFormat format = new SimpleDateFormat("yyyy-MM");
                /*** 获取前一个月 */
                String orderTime = format.format(CalendarTool.getPreviousDate(new Date()));

                /*** 商品月汇总报表 */
                this.loopPageByMonth(null,orderTime);

                /*** 督导月报表 */
                commoditySaleStatisticsService.commoditySaleStatisticsMonthSupervisor(null, orderTime);
            } catch (Exception e) {
                log.error("月报定时数据汇总出错",e);
            } finally {
                lock.unlock();
            }
        }
    }

    public void loopPageByDay(CommoditySaleStatisticsMakeUpReqVo vo,String orderTime) {
        List<List<Long>> subCommodityIdList = this.queryCommodityIdList(vo);
        subCommodityIdList.forEach(commodityIds->{
            try {
                /***
                 * 查询销售订单表数据 t_tj_order、t_tj_order_list
                 * 以公司、商品，客户类型分组
                 */
                List<CommoditySaleStatisticsItemRespVo> queryCommoditySaleItemData = commoditySaleStatisticsService.queryCommoditySaleItemData(commodityIds,orderTime);
                if(queryCommoditySaleItemData.size() > 0) {
                    /***
                     * 封装销售商品数据便于统计表t_tj_commodity_sale_statistics、以及添加t_tj_commodity_sale_statistics_item
                     */
                    commoditySaleStatisticsService.sumCommoditySaleStatisticsMakeUpByDay(queryCommoditySaleItemData, orderTime);
                }else{
                    log.info("商品日汇总补偿失败，补偿日期={}，原始数据表（t_tj_order、t_tj_order_list）无"+orderTime+"日期数据！",orderTime);
                }
            } catch (Exception e) {
                log.error("商品日汇总补偿异常：补偿日期={}，补偿商品批次={},error={}",orderTime,commodityIds,e);
            }
        });
    }

    public void loopPageByMonth(CommoditySaleStatisticsMakeUpReqVo vo,String orderTime) {
        List<List<Long>> subCommodityIdList = this.queryCommodityIdList(vo);
        subCommodityIdList.forEach(commodityIds->{
            try {
                commoditySaleStatisticsService.monthData(commodityIds,orderTime);
            } catch (Exception e) {
                log.error("商品月汇总补偿异常：补偿月份={}，补偿商品批次={}",orderTime,commodityIds);
            }
        });
    }

    private List<List<Long>> queryCommodityIdList(CommoditySaleStatisticsMakeUpReqVo vo){
        List<Long> commodityIds = new ArrayList<>();
        if(vo != null && vo.getCommodityId()!=null){
            commodityIds.add(vo.getCommodityId());
        }else{
            commodityIds.addAll(commodityService.queryCommodityIdList());
        }
        return SpringUtil.isNotEmpty(commodityIds) ? ListUtil.splitSubList(commodityIds,1000) : Collections.EMPTY_LIST;
    }


    /****
     * 修改统计订单
     * t_tj_order、t_tj_order_latest
     * 场景：
     * 修改客户的所属公司后，需将改该客户下的统计订单修改成对应所属公司
     * 修改订单的条件为：客户下的订单、订单送货时间>当前时间、订单所属公司不等于客户所属公司
     * @return
     */
    @ApiOperation(value = "根据客户修改统计订单对应的所属公司", notes = " 根据客户修改统计订单对应的所属公司", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @RequestMapping(value = "/modifyOrderCompany", method = RequestMethod.POST)
    public Integer modifyOrderCompanyByStore(@RequestParam("storeId") Long storeId,@RequestParam("companyId") Long companyId) {
        QYAssert.isTrue(storeId!=null,"客户不能为空!");
        QYAssert.isTrue(companyId!=null,"公司不能为空!");
        return commoditySaleStatisticsService.modifyOrderCompanyByStore(storeId,companyId);
    }

    @InitBinder
    public void initData(WebDataBinder wdb){
        wdb.registerCustomEditor(Date.class, new CustomDateEditor(new SimpleDateFormat("yyyy-MM-dd"), true));
    }

}
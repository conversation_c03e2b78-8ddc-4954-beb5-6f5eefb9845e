package com.pinshang.qingyun.orderreport.controller;

import com.alibaba.excel.EasyExcel;
import com.github.pagehelper.PageInfo;
import com.pinshang.qingyun.base.configure.expand.QYAssert;
import com.pinshang.qingyun.base.util.ExcelUtil;
import com.pinshang.qingyun.box.utils.CollectorsUtil;
import com.pinshang.qingyun.box.utils.JsonUtil;
import com.pinshang.qingyun.box.utils.SpringUtil;
import com.pinshang.qingyun.infrastructure.exportfile.cache.starter.FileCacheQuery;
import com.pinshang.qingyun.infrastructure.exportfile.cache.starter.FileCacheQueryParameter;
import com.pinshang.qingyun.orderreport.mapper.entry.FactoryDeliveryStatisticsByPageEntry;
import com.pinshang.qingyun.orderreport.mapper.entry.FactoryDeliveryStatisticsItemByPageEntry;
import com.pinshang.qingyun.orderreport.service.FactoryDeliveryStatisticsService;
import com.pinshang.qingyun.orderreport.vo.FactoryDeliveryStatisticsReqVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.propertyeditors.CustomDateEditor;
import org.springframework.web.bind.WebDataBinder;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 工厂送货
 */
@RestController
@RequestMapping(value = "/statistical/factoryDeliveryStatistics")
@Slf4j
@Api(value = "产品销售汇总-工厂送货", tags = "factoryDeliveryStatistics", description = "产品销售汇总-工厂送货")
public class FactoryDeliveryStatisticsController {

    @Autowired
    private FactoryDeliveryStatisticsService factoryDeliveryStatisticsService;

    @ApiOperation(value = "产品销售汇总-工厂送货", notes = "产品销售汇总-工厂送货", response = FactoryDeliveryStatisticsByPageEntry.class)
    @ApiImplicitParam(name = "vo", value = "", required = true, paramType = "body", dataType = "FactoryDeliveryStatisticsReqVo")
    @RequestMapping(value = "/queryFactoryDeliveryStatistics", method = RequestMethod.POST)
    public PageInfo<FactoryDeliveryStatisticsByPageEntry> queryFactoryDeliveryStatistics(@RequestBody FactoryDeliveryStatisticsReqVo vo){
        return factoryDeliveryStatisticsService.queryFactoryDeliveryStatistics(vo);
    }

    @ApiOperation(value = "产品销售汇总-工厂送货（导出）", notes = "产品销售汇总-工厂送货（导出）")
    @RequestMapping(value = "/factoryDeliveryStatisticsExportData", method = RequestMethod.GET)
    @FileCacheQuery(bizCode = "OR_FACTORY_DELIVERY_STA_E")
    public void factoryDeliveryStatisticsExportData(@FileCacheQueryParameter FactoryDeliveryStatisticsReqVo dto, HttpServletResponse response) throws IOException {
        //获取汇总数据源
        long qS = System.currentTimeMillis();
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");
        String fileName = "产品销售汇总-工厂送货-" + sdf.format(dto.getStartDate()) + "至" + sdf.format(dto.getEndDate());
        dto.setPageSize(Integer.MAX_VALUE);
        List<FactoryDeliveryStatisticsByPageEntry> resultList = factoryDeliveryStatisticsService.queryFactoryDeliveryStatistics(dto).getList();
        long qE = System.currentTimeMillis();
        log.error("产品销售汇总-工厂送货--查询时间=" + ((qE - qS) / 1000));
        try {
//            QYAssert.isTrue(SpringUtil.isNotEmpty(resultList),"未查询到产品销售汇总-工厂送货数据,入参："+ JsonUtil.java2json(dto));
            ExcelUtil.setFileNameAndHead( response,fileName);
            EasyExcel.write(response.getOutputStream()).head(createHead(resultList)).autoCloseStream(Boolean.FALSE).sheet("工厂送货").doWrite(dataList(resultList));
        } catch (Exception e) {
            ExcelUtil.setExceptionResponse( response );
            log.error("产品销售汇总-工厂送货--导出异常",e);
        }
        long eE = System.currentTimeMillis();
        log.error("产品销售汇总-工厂送货--导出--excel处理时间="+ ( (eE -qE) /1000 )  );
    }

    private List<List<String>> createHead(List<FactoryDeliveryStatisticsByPageEntry> odto){
        List<List<String>> list = new ArrayList<List<String>>();
        list.add(new ArrayList<String>(){{add("工厂");}});
        list.add(new ArrayList<String>(){{add("送货金额合计");}});
        if(odto!=null && odto.size()>0) {
            odto.get(0).getItems().forEach(item -> {
                list.add(new ArrayList<String>() {{
                    add(item.getStoreTypeName());
                }});
            });
        }
        list.add(new ArrayList<String>(){{add("线路");}});
        list.add(new ArrayList<String>(){{add("送货员");}});
        return list;
    }

    private List<List<Object>> dataList(List<FactoryDeliveryStatisticsByPageEntry> odtoList) {
        List<List<Object>> list = new ArrayList<List<Object>>();
        for (FactoryDeliveryStatisticsByPageEntry odto : odtoList) {
            List<Object> data = new ArrayList<Object>();
            data.add(odto.getFactoryName());
            data.add(subZeroAndDot(String.valueOf(odto.getTotalAmount())));
            List<FactoryDeliveryStatisticsItemByPageEntry> items = odto.getItems();
            items.forEach(item ->{
                data.add(subZeroAndDot(String.valueOf(item.getTotalAmount()==null?"":item.getTotalAmount())));
            });
            data.add(odto.getLineName());
            data.add(odto.getDeliverymanName());
            list.add(data);
        }
        return list;
    }

    /**
     * 去掉给定数字多余的0
     * @param s 给定的数字
     * @return 返回去除后的值
     */
    public static String subZeroAndDot(String s) {
        if (s.indexOf(".") > 0) {
            s = s.replaceAll("0+?$", "");//去掉多余的0
            s = s.replaceAll("[.]$", "");//如最后一位是.则去掉
        }
        return s;
    }

    @InitBinder
    public void initData(WebDataBinder wdb){
        wdb.registerCustomEditor(Date.class, new CustomDateEditor(new SimpleDateFormat("yyyy-MM-dd"), true));
    }
}
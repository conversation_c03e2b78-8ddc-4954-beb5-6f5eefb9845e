package com.pinshang.qingyun.orderreport.mapper;

import com.pinshang.qingyun.base.mybatis.MyMapper;
import com.pinshang.qingyun.orderreport.model.OrderSync;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;

/**
 * @description:
 * @author: hhf
 * @time: 2021/8/13 10:38
 */
@Mapper
@Repository
public interface OrderSyncMapper extends MyMapper<OrderSync> {

    /**
     * 查询当前日期之前的订单
     * @param nowDate
     * @return
     */
    List<OrderSync> queryOrderSyncList(@Param("orderTime") Date nowDate);
}

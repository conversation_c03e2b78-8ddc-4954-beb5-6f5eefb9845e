package com.pinshang.qingyun.orderreport.controller;

import com.pinshang.qingyun.base.api.TokenInfo;
import com.pinshang.qingyun.base.configure.expand.QYAssert;
import com.pinshang.qingyun.base.util.FastThreadLocalUtil;
import com.pinshang.qingyun.box.utils.DateUtil;
import com.pinshang.qingyun.box.utils.SpringUtil;
import com.pinshang.qingyun.orderreport.config.CustomerProperties;
import com.pinshang.qingyun.orderreport.mapper.entry.WorkshopShipmentsEntry;
import com.pinshang.qingyun.orderreport.mapper.entry.WorkshopShipmentsResultEntry;
import com.pinshang.qingyun.orderreport.service.PrintingAllowedCheckService;
import com.pinshang.qingyun.orderreport.service.WorkshopShipmentsService;
import com.pinshang.qingyun.orderreport.util.PdfUtils;
import com.pinshang.qingyun.orderreport.vo.ProductShipmentsRequestVo;
import com.pinshang.qingyun.orderreport.vo.ShipmentsVo;
import com.pinshang.qingyun.orderreport.vo.WorkshopShipmentsVo;
import com.pinshang.qingyun.orderreport.vo.WorkshopVo;
import com.pinshang.qingyun.print.service.PrinterTaskClient;
import com.pinshang.qingyun.smm.dto.user.SelectUserFactoryIdListIDTO;
import com.pinshang.qingyun.smm.service.SMMUserClient;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiModelProperty;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.util.*;

/**
 * 生产组发货单
 * <AUTHOR>
 * @date 2019/2/27 14:00.
 */
@RestController
@RequestMapping("/statistical/workshopShipments")
@Api(value = "生产组发货单api", tags = "workshopShipments", description = "生产组发货单相关接口")
public class WorkshopShipmentsController {
    private final String keyPattern = "directorCode:%s:%s:%s:%s:%s:%s";
    private final long  expire = 60L;
    @Resource
    private RedissonClient redissonClient;

    private final WorkshopShipmentsService workshopShipmentsService;
    @Autowired
    private SMMUserClient userClient;
    
    @Autowired
    private PrinterTaskClient printerTaskClient;

    @Autowired
    private CustomerProperties customerProperties;

    @Autowired
    private PrintingAllowedCheckService printingAllowedCheckService;

    public WorkshopShipmentsController(WorkshopShipmentsService workshopShipmentsService) {
        this.workshopShipmentsService = workshopShipmentsService;
    }

    /**
     * 生产组发货单和生产组外地发货单查询
     * @param requestVo 请求参数
     * @return
     */
    @RequestMapping(value = "/doSearch",method = RequestMethod.POST)
    @ApiModelProperty(value = "生产组发货单", notes = "生产组发货单")
    public List<WorkshopVo> doSearch(@RequestBody ProductShipmentsRequestVo requestVo) {
        if(requestVo.getFactoryId() == null){
            List<Long> factoryIds = userClient.selectUserFactoryIdList(SelectUserFactoryIdListIDTO.onlyDb(requestVo.getUserId()));
            if(SpringUtil.isEmpty(factoryIds)){
                return new ArrayList<>();
            }
            requestVo.setFactoryIds(factoryIds);
        }
        return workshopShipmentsService.doSearch(requestVo);
    }

    /**
     * 生产组发货单预览
     * @param requestVo 请求参数
     * @return
     */
    @RequestMapping(value = "/preview",method = RequestMethod.POST)
    public WorkshopShipmentsVo preview(@RequestBody ProductShipmentsRequestVo requestVo) {
        return workshopShipmentsService.preview(requestVo);
    }


    /**
     * 打印
     * @param requestVo 当前搜索条件
     * @return 返回JSON
     */
    @RequestMapping("print")
    public String print(@RequestBody ProductShipmentsRequestVo requestVo) {
        printingAllowedCheckService.isPrintingAllowed(requestVo);
        printerTaskClient.checkUserPrinter(requestVo.getUserId());
        workshopShipmentsService.print(requestVo);
        return "ok";
    }

    /**
     * 打印所有
     * @param requestVo 当前搜索条件
     * @return 返回JSON
     */
    @RequestMapping("printAll")
    public String print(@RequestBody List<ProductShipmentsRequestVo> requestVo) {
        QYAssert.isTrue(SpringUtil.isNotEmpty(requestVo),"请选择要打印的内容");
        printingAllowedCheckService.isPrintingAllowed(requestVo.get(0));
        printerTaskClient.checkUserPrinter(requestVo.get(0).getUserId());
        workshopShipmentsService.printAll(requestVo);
        return "ok";
    }

    @RequestMapping(value = "/exportZip",method = RequestMethod.POST)
    @ApiModelProperty(value = "生产组发货单Zip", notes = "生产组发货单Zip")
    public void  exportZip(@RequestBody List<ProductShipmentsRequestVo> requestVo, HttpServletRequest request, HttpServletResponse response) throws IOException {
        QYAssert.isTrue(SpringUtil.isNotEmpty(requestVo),"请选择要导出的内容");
        printingAllowedCheckService.isPrintingAllowed(requestVo.get(0));
        Map<String,String> strListPdf = workshopShipmentsService.getStrListPdf(requestVo);

        Map<String,File> fileList = new HashMap<>();
        strListPdf.forEach((key,value)->{
            File file = new File(key);
            fileList.put(value,file);
        });

        String pdfSave = customerProperties.getAbsoluteSavePath();
        String savePath = customerProperties.getSavePath();

        String zipFilePath = pdfSave + savePath + "生产组发货单.zip";

        try {
            PdfUtils.exportZip(response,fileList,zipFilePath,"生产组发货单");
        }catch (Exception exception){
            exception.printStackTrace();
        }
    }

    @RequestMapping(value = "/exportPdf",method = RequestMethod.POST)
    @ApiModelProperty(value = "生产组发货单pdf", notes = "生产组发货单pdf")
    public void  exportPdf(@RequestBody ProductShipmentsRequestVo requestVo, HttpServletRequest request, HttpServletResponse response) throws IOException {
        printingAllowedCheckService.isPrintingAllowed(requestVo);
        String strListPdf = workshopShipmentsService.getStrPdf(requestVo);

        File file = new File(strListPdf);
        try {
            PdfUtils.exportPdf(response,file,null);
        }catch (Exception exception){
            exception.printStackTrace();
        }
    }


    /**
     * 清美人APP查询生产组发货单
     * @param vo
     * @return
     */
    @RequestMapping(value = "/list",method = RequestMethod.POST)
    public List<WorkshopShipmentsEntry> list(@RequestBody ShipmentsVo vo){
        QYAssert.notNull(vo,"查找生产组发货单参数为空!");
        return workshopShipmentsService.findShipmentsList(vo);
//        String key = generateCacheKey(vo.getDirectorCode(), vo.getLineGroupId(), vo.getDeliveryTime(),
//                vo.getWarehouseId(), vo.getOrderDate()==null? LocalDate.now().plusDays(1).toString():vo.getOrderDate()
//                ,vo.getDeliveryBatch());
//        RLock lock = redissonClient.getLock("key:"+key);
//        lock.lock();
//        List<WorkshopShipmentsEntry> shipmentsList;
//        try {
//            RList<WorkshopShipmentsEntry> cacheList = redissonClient.getList(key);
//            if(cacheList !=null &&!cacheList.isEmpty()){
//                return  cacheList.readAll();
//            }
//            //QYAssert.isTrue(workshopShipmentsService.isDirector(vo.getDirectorCode()),"您不是生产组主任.");
//            shipmentsList = workshopShipmentsService.findShipmentsList(vo);
//            if (shipmentsList != null && !shipmentsList.isEmpty()) {
//                cacheList.addAllAsync(shipmentsList).thenRun(() -> cacheList.expireAsync(expire, TimeUnit.SECONDS));
//            }
//        } finally {
//            lock.unlock();
//        }
//        return shipmentsList;
    }

    /**
     * 清美人APP查询生产组发货单
     * @param vo
     * @return
     */
    @RequestMapping(value = "/listV1",method = RequestMethod.POST)
    public WorkshopShipmentsResultEntry listV1(@RequestBody ShipmentsVo vo) {
        QYAssert.isTrue(StringUtils.isNotBlank(vo.getOrderDate()),"送货时间不能为空，请输入！");
        TokenInfo tokenInfo = FastThreadLocalUtil.getQY();
        vo.setDirectorCode(tokenInfo.getEmployeeNumber());
        Date orderDate = DateUtil.parseDate(vo.getOrderDate(),"yyyy-MM-dd");
        return new WorkshopShipmentsResultEntry(
                printingAllowedCheckService.getPrintingAllowed(orderDate,orderDate,vo.getLineGroupId(),vo.getDeliveryTime()),
                workshopShipmentsService.findShipmentsList(vo));
    }

    private String generateCacheKey(Object ... directorCode){
        for (int i = 0; i < directorCode.length; i++) {
            Object o = directorCode[i];
            if(o == null || (o instanceof String && StringUtils.isBlank((String) o))){
                directorCode[i] = "-";
            }
        }
        return String.format(keyPattern,directorCode);
    }
}

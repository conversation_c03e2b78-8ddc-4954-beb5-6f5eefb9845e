package com.pinshang.qingyun.orderreport.vo.ps;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @Version 0.1.0
 * @Date 2021/1/27 18:52
 * @Copyright © 2019-2021 qm
 * @Description -- 类说明
 **/
@Data
public class MutexCommodityGroupStoreVO {
    private Long storeId;
    private String storeCode;
    private Long commodityGroupId;
    @ApiModelProperty(hidden = true, value = "true-客户有重复的商品组")
    private Boolean flag;
}

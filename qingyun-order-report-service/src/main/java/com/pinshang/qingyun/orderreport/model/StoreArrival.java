package com.pinshang.qingyun.orderreport.model;

import java.util.Date;

import javax.persistence.Entity;
import javax.persistence.Table;

import lombok.Data;
import lombok.NoArgsConstructor;

import com.pinshang.qingyun.base.po.BasePO;
import com.pinshang.qingyun.orderreport.dto.storearrival.StoreArrivalKafkaIDTO;

/**
 * 客户到货
 */
@Data
@NoArgsConstructor
@Entity
@Table(name = "t_tj_store_arrival")
public class StoreArrival extends BasePO {
	private String createName;
	private String updateName;

	// 送货日期：yyyy-MM-dd
	private Date orderTime;
	// 客户ID
	private Long storeId;
	// 客户类型ID
	private Long storeTypeId;
	// 线路ID
	private Long lineId;
	// 线路编码
	private String lineCode;
	// 线路名称
	private String lineName;
	// 送货员ID
	private Long deliverymanId;
	// 送货员名称
	private String deliverymanName;
	// 到货图片s
	private String arrivalPics;
	// 到货备注
	private String arrivalRemarks;
	
	public StoreArrival(Long storeId, Date orderTime) {
		this.orderTime = orderTime;
		this.storeId = storeId;
	}
	
	public static StoreArrival forInsert(Store store, StoreArrivalKafkaIDTO idto) {
		Long operateId = idto.getUserId();
		String operateName = idto.getUserName();
		Date operateTime = new Date();
		
		StoreArrival model = new StoreArrival();
		model.setCreateId(operateId);
		model.setCreateName(operateName);
		model.setCreateTime(operateTime);
		model.setUpdateId(operateId);
		model.setUpdateName(operateName);
		model.setUpdateTime(operateTime);
		
		model.orderTime = idto.getOrderTime();
		model.storeId = store.getId();
		model.storeTypeId = store.getStoreTypeId();
		model.lineId = store.getStoreLineId();
		model.lineCode = store.getStoreLineCode();
		model.lineName = store.getStoreLineName();
		model.deliverymanId = store.getDeliverymanId();
		model.deliverymanName = store.getDeliverymanName();
		model.arrivalPics = idto.getPics();
		model.arrivalRemarks = idto.getRemarks();
		
		return model;
	}
	
	public static void forUpdate(StoreArrival model, Store store, StoreArrivalKafkaIDTO idto) {
		Long operateId = idto.getUserId();
		String operateName = idto.getUserName();
		Date operateTime = new Date();
		
		model.setUpdateId(operateId);
		model.setUpdateName(operateName);
		model.setUpdateTime(operateTime);
		
		model.orderTime = idto.getOrderTime();
		model.storeId = store.getId();
		model.storeTypeId = store.getStoreTypeId();
		model.lineId = store.getStoreLineId();
		model.lineCode = store.getStoreLineCode();
		model.lineName = store.getStoreLineName();
		model.deliverymanId = store.getDeliverymanId();
		model.deliverymanName = store.getDeliverymanName();
		model.arrivalPics = idto.getPics();
		model.arrivalRemarks = idto.getRemarks();
	}
	
}

package com.pinshang.qingyun.orderreport.service;

import com.pinshang.qingyun.base.configure.expand.QYAssert;
import com.pinshang.qingyun.base.enums.ordertj.OrderTJPrintStatusEnum;
import com.pinshang.qingyun.base.enums.ordertj.OrderTjTypeEnum;
import com.pinshang.qingyun.box.utils.SpringUtil;
import com.pinshang.qingyun.orderreport.mapper.OrderLinePrintStatusMapper;
import com.pinshang.qingyun.orderreport.mapper.PrintStatusMapper;
import com.pinshang.qingyun.orderreport.model.OrderLinePrintStatus;
import com.pinshang.qingyun.orderreport.model.PrintStatus;
import com.pinshang.qingyun.orderreport.vo.DeliveryListPreviewReqVo;
import lombok.extern.slf4j.Slf4j;
import org.joda.time.DateTime;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.entity.Example;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR> weican on 2019-02-14.
 */
@Service
@Slf4j
public class PrintStatusService {
    @Autowired
    private PrintStatusMapper printStatusMapper;
    @Autowired
    private OrderLinePrintStatusMapper orderLinePrintStatusMapper;
    @Autowired
    private OrderLinePrintStatusService orderLinePrintStatusService;
    @Transactional(rollbackFor = Exception.class)
    public void saveOrUpdate(List<DeliveryListPreviewReqVo> params){
       if(SpringUtil.isNotEmpty(params)){
           DeliveryListPreviewReqVo first = params.get(0);
           QYAssert.notNull(first.getOrderDate(),"送货日期不能为空");
           //判断是否是今天及以后的送货日期，是=true,否=false
           boolean todayOrAfter = new DateTime(first.getOrderDate()).isAfter(new DateTime().withMillisOfDay(0));
           params.forEach(param->{
               checkParam(param);
               //当打印状态不是"全部都已打印"时，才处理数据，当是今天之前的数据时操作
               if(param.getPrintStatus().intValue() != OrderTJPrintStatusEnum.ALL_PRINTED.getCode()){
                   if(todayOrAfter){
                       PrintStatus db= findByOrderTimeAndLineId(param.getOrderDate(),param.getLineId());
                       if(db==null){
                           PrintStatus bean = new PrintStatus();
                           bean.setOrderTime(param.getOrderDate());
                           bean.setCreateTime(new Date());
                           bean.setLineId(param.getLineId());
                           bean.setPrintStatus(processPrintStatus(-1,param.getType()));
                           printStatusMapper.insertSelective(bean);
                       }else{
                           int processPrintStatus = processPrintStatus(db.getPrintStatus(),param.getType()) ;
                           if(processPrintStatus != db.getPrintStatus()){
                               db.setPrintStatus(processPrintStatus);
                               printStatusMapper.updateByPrimaryKey(db);
                           }
                       }
                   }else{
                       OrderLinePrintStatus db =  orderLinePrintStatusService.findByOrderTimeAndLineId(param.getOrderDate(),param.getLineId());
                       if(db!=null){
                           int processPrintStatus = processPrintStatus(db.getPrintStatus(),param.getType()) ;
                           if(processPrintStatus != db.getPrintStatus()){
                               db.setPrintStatus(processPrintStatus);
                               orderLinePrintStatusMapper.updateByPrimaryKey(db);
                           }
                       }else{
                           log.error("执行历史数据打印时，未在t_tj_order_line_print_status 表中找到" +
                                   " order_time={},lineId={}数据",param.getOrderDate(),param.getLineId());
                       }
                   }
               }

           });
       }
       }
    public PrintStatus findByOrderTimeAndLineId(Date orderTime,Long lineId){
        Example example = new Example(PrintStatus.class);
        example.createCriteria().andEqualTo("orderTime",orderTime).andEqualTo("lineId",lineId);
        List<PrintStatus> list = printStatusMapper.selectByExample(example);
        if(SpringUtil.isNotEmpty(list)){
            return list.get(0);
        }
        return null;
    }

    private void checkParam(DeliveryListPreviewReqVo param){
        QYAssert.notNull(param.getPrintStatus(),"打印状态不能为空");
        QYAssert.notNull(param.getType(),"业务类型不能为空");
        QYAssert.notNull(param.getOrderDate(),"送货日期不能为空");
        QYAssert.notNull(param.getLineId(),"线路不能为空");
    }
    private int processPrintStatus(int dbPrintStatus,int type){
        int printStatusOfType = type== OrderTjTypeEnum.DELIVERY_LIST.getCode() ?OrderTJPrintStatusEnum.DELIVERY_LIST_PRINTED.getCode():OrderTJPrintStatusEnum.BILL_OF_LEADING_PRINTED.getCode();
        // dbPrintStatus=-1 代表从未执行过打印
        if( dbPrintStatus== -1 || dbPrintStatus == OrderTJPrintStatusEnum.UN_PRINT.getCode() ) {
            return printStatusOfType;
        }
        // 如果相等代表重复执行打印,如果不相等则直接设置为全部已打印
        return dbPrintStatus == printStatusOfType?dbPrintStatus:OrderTJPrintStatusEnum.ALL_PRINTED.getCode();
    }

}

package com.pinshang.qingyun.orderreport.pdf;

import com.pinshang.qingyun.base.api.TokenInfo;
import com.pinshang.qingyun.base.configure.expand.QYAssert;
import com.pinshang.qingyun.base.enums.PrinterDataGroupTypeEnum;
import com.pinshang.qingyun.base.enums.PrinterTypeEnum;
import com.pinshang.qingyun.base.util.FastThreadLocalUtil;
import com.pinshang.qingyun.box.utils.DateUtil;
import com.pinshang.qingyun.orderreport.service.PrintTaskService;
import com.pinshang.qingyun.stream.plugin.core.MockMultipartFile;
import com.pinshang.qingyun.upload.dto.odto.FileUploadRestSingleODTO;
import com.pinshang.qingyun.upload.dto.odto.FileUploadResultODTO;
import com.pinshang.qingyun.upload.service.XsFileUploadClient;
import lombok.extern.slf4j.Slf4j;
import org.joda.time.DateTime;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.FileInputStream;
import java.io.InputStream;

/**
 * @author: chenqiang
 * @time: 22/6/14/014 11:40
 */
@Slf4j
@Service
public class PdfPrintService {
    /**
     * 上传pdf并打印
     *
     * @param: [xsFileUploadClient, printTaskService, filePath]
     * @author: teninone
     */
    public void uploadAndPrintPdf(XsFileUploadClient xsFileUploadClient,PrintTaskService printTaskService, String filePath, String customizedPath, PrinterDataGroupTypeEnum printerDataGroupTypeEnum) {
        try {
            final File file = new File(filePath);
            final InputStream inputStream = new FileInputStream(file);
            // "file"与"@RequestParam("datafile")注解里标明的名字保持一致
            final MultipartFile multipartFile = new MockMultipartFile("file", file.getName(), MediaType.MULTIPART_FORM_DATA_VALUE, inputStream);
            // 文件上传使用 XsFileUploadClient modeType=PS_PRINT,自定义路径=YC_RETURN_FACTORY
            final FileUploadRestSingleODTO fileUploadRestSingleODTO = xsFileUploadClient.restUpload4path(multipartFile, "PS_PRINT", customizedPath);
            log.info("xsFileUploadClient上传pdf-result ： {}", fileUploadRestSingleODTO);

            if (fileUploadRestSingleODTO == null) {
                QYAssert.isFalse("上传pdf异常!");
            }

            final FileUploadResultODTO data = fileUploadRestSingleODTO.getData();
            if (data == null) {
                QYAssert.isFalse("上传pdf异常!");
            }

            final String visitUrl = data.getVisitUrl();
            // 调用打印机服务
            this.savePrintTask(printTaskService, visitUrl,printerDataGroupTypeEnum);
        } catch (Exception e) {
            log.error("xsFileUploadClient上传pdf异常", e);
//            QYAssert.isFalse("上传pdf异常!");
            QYAssert.isFalse(e.getMessage());
        }
    }

    String getPngPath(PdfProperties pdfProperties) {
        String pdfSave = pdfProperties.getAbsoluteSavePath();
        String savePath = pdfProperties.getSavePath();
        String datePath = DateUtil.getDateFormate(new DateTime().plusDays(1).toDate(), "yyyy-MM-dd");

        return pdfSave + savePath + datePath;
    }

    /**
     * 调用并且进入打印机任务
     *
     * @param: [printTaskService, fileName]
     * @author: teninone
     */
    void savePrintTask(PrintTaskService printTaskService, String fileName, PrinterDataGroupTypeEnum printerDataGroupTypeEnum) {
//        try {
            printTaskService.savePrintTask(fileName, printerDataGroupTypeEnum, this.getUserId(), 1, PrinterTypeEnum.LASER.getCode());
//        } catch (Exception e) {
//            log.error("打印异常!", e);
//            QYAssert.isFalse("打印异常!");
//        }
    }

    private Long getUserId() {
        TokenInfo tokenInfo = FastThreadLocalUtil.getQY();
        if (tokenInfo == null) {
            return 1L;
        }

        return tokenInfo.getUserId();

    }

}

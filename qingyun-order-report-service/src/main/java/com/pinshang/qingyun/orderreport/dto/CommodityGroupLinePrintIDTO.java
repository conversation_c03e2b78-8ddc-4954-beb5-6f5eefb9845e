package com.pinshang.qingyun.orderreport.dto;

import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 商品组打印入参
 */
@Data
public class CommodityGroupLinePrintIDTO {

    @ApiModelProperty(value = "送货日期")
    private Date orderTime;

    @ApiModelProperty(value = "线路ID：打印预览和单个打印时只有一个值，批量打印时传多个")
    private Long lineId;

    @ApiModelProperty(value = "线路ID：打印预览和单个打印时只有一个值，批量打印时传多个")
    private List<Long> lineIdList;

    @JsonIgnore
    private Long userId;

    public List<Long> getLineIdList() {
        if(this.lineIdList==null || this.lineIdList.size()==0){
            this.lineIdList = new ArrayList<>();
            if(this.lineId!=null){
                this.lineIdList.add(this.lineId);
            }
        }
        return lineIdList;
    }
}

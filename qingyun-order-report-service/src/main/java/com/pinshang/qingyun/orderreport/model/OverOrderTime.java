package com.pinshang.qingyun.orderreport.model;

import lombok.Data;

import javax.persistence.Id;
import javax.persistence.Table;
import java.math.BigDecimal;
import java.util.Date;

@Data
@Table(name = "t_tj_over_order_time")
public class OverOrderTime {

    @Id
    private Long id;

    private String orderCode;

    private Date orderTime;

    private BigDecimal finalAmount;

    //订单下单日期(t_order表的 create_time)
    private Date orderCreateTime;

    private Long storeId;

    private Long lineGroupId;

    private String lineGroupName;

    //操作员(t_order表create_id)
    private Long operatorId;

    //操作员姓名
    private String operatorName;

    //督导ID(t_employee表主键)
    private Long supervisorId;

    private String supervisorName;

    private Date createTime;
}
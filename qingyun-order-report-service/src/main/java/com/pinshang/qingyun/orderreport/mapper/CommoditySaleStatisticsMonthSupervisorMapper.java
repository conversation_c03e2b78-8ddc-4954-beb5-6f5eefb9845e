package com.pinshang.qingyun.orderreport.mapper;

import com.pinshang.qingyun.base.mybatis.MyMapper;
import com.pinshang.qingyun.orderreport.model.CommoditySaleStatisticsMonthSupervisor;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface CommoditySaleStatisticsMonthSupervisorMapper extends MyMapper<CommoditySaleStatisticsMonthSupervisor> {
    /**
     *生成 产品销售汇总--月报表督导明细数据
     * @param startTime
     * @param endTime
     * @param commodityId
     * @return
     */
    List<CommoditySaleStatisticsMonthSupervisor> generateCommoditySaleStatisticsMonthSupervisor(@Param("startTime") String startTime,
                                                                                                @Param("endTime") String endTime,
                                                                                                @Param("commodityId") Long commodityId);
}

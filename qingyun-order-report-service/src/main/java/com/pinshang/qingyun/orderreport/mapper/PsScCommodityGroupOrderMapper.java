package com.pinshang.qingyun.orderreport.mapper;

import com.pinshang.qingyun.orderreport.dto.CommodityGroupLineTmpDetailDTO;
import com.pinshang.qingyun.orderreport.dto.CommodityGroupLineOrderIDTO;
import com.pinshang.qingyun.orderreport.dto.CommodityGroupLineTmpOrderItemDTO;
import com.pinshang.qingyun.orderreport.dto.CommodityGroupLineOrderODTO;
import io.swagger.models.auth.In;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;


@Repository
public interface PsScCommodityGroupOrderMapper {

    List<CommodityGroupLineOrderODTO> queryCommodityGroupOrderList(CommodityGroupLineOrderIDTO idto);

    CommodityGroupLineTmpDetailDTO queryLineDetailInfo(@Param("lineId")Long lineId);

    List<CommodityGroupLineTmpOrderItemDTO> queryLineOrderItemList(@Param("orderTime") Date orderTime, @Param("lineId")Long lineId, @Param("storeIdList")List<Long> storeIdList);

    List<Long> queryPrintStoreList(@Param("orderTime") Date orderTime,@Param("lineId")Long lineId);
}
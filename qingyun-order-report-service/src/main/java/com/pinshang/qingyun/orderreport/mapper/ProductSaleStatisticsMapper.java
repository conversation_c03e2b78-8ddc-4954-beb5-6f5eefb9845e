package com.pinshang.qingyun.orderreport.mapper;

import com.pinshang.qingyun.orderreport.mapper.entry.ProductSaleStatisticsCommodityInfoListEntry;
import com.pinshang.qingyun.orderreport.mapper.entry.ProductSaleStatisticsPurchaseTempEntry;
import com.pinshang.qingyun.orderreport.mapper.entry.ProductSaleStatisticsSumEntry;
import com.pinshang.qingyun.orderreport.mapper.entry.ProductSaleStatisticsTempEntry;
import com.pinshang.qingyun.orderreport.vo.ProductSaleStatisticsCommodityInfoListVo;
import com.pinshang.qingyun.orderreport.vo.ProductSaleStatisticsPurchaseQueryVo;
import com.pinshang.qingyun.orderreport.vo.ProductSaleStatisticsVo;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;

/**
 * 产品销售汇总
 */
@Repository
public interface ProductSaleStatisticsMapper {
    //查询最新订单表数据
    List<ProductSaleStatisticsTempEntry> queryLatestList(@Param("vo") ProductSaleStatisticsVo vo);

    //查询历史订单表数据
    List<ProductSaleStatisticsTempEntry> queryList(@Param("vo") ProductSaleStatisticsVo vo);

    ProductSaleStatisticsSumEntry querySum(@Param("vo") ProductSaleStatisticsVo vo);

    ProductSaleStatisticsSumEntry queryLatestSum(@Param("vo") ProductSaleStatisticsVo vo);

    //产品销售汇总--查询督导汇总表
    List<ProductSaleStatisticsTempEntry> queryListBySupervisorStatistics(@Param("vo") ProductSaleStatisticsVo vo);

    //产品销售汇总--查询商品汇总表
    List<ProductSaleStatisticsTempEntry> queryListByCommodityStatistics(@Param("vo") ProductSaleStatisticsVo vo);

    ProductSaleStatisticsSumEntry querySumBySupervisorStatistics(@Param("vo") ProductSaleStatisticsVo vo);

    ProductSaleStatisticsSumEntry querySumByCommodityStatistics(@Param("vo") ProductSaleStatisticsVo vo);

    /**
     * 产品销售汇总列表(半年)
     * @param vo
     * @return
     */
    List<ProductSaleStatisticsTempEntry> queryHalfYearList(@Param("vo") ProductSaleStatisticsVo vo);

    /**
     * 产品销售汇总合计(半年)
     * @param vo
     * @return
     */
    ProductSaleStatisticsSumEntry querySumHalfYear(@Param("vo") ProductSaleStatisticsVo vo);

    /***
     * 供 清美助手小程序 采购模块下 产品销售汇总使用
     * @param vo
     * @param isLatest
     * @return
     */
    List<ProductSaleStatisticsCommodityInfoListEntry> selectProductSaleStatisticsCommodityInfoListEntry(@Param("vo") ProductSaleStatisticsCommodityInfoListVo vo,
                                                                                                        @Param("isLatest") boolean isLatest,
                                                                                                        @Param("map") Map<String,List<Long>> map
                                                                                                        );

    /**
     * 根据订货日期查询订单
     */
    List<ProductSaleStatisticsPurchaseTempEntry> queryListForPurchaseByCreateTime(ProductSaleStatisticsPurchaseQueryVo vo);
}

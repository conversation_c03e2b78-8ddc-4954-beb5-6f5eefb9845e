package com.pinshang.qingyun.orderreport.service.ps;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.pinshang.qingyun.base.configure.expand.QYAssert;
import com.pinshang.qingyun.base.enums.PrinterDataGroupTypeEnum;
import com.pinshang.qingyun.base.enums.PrinterTypeEnum;
import com.pinshang.qingyun.box.utils.DateUtil;
import com.pinshang.qingyun.box.utils.SpringUtil;
import com.pinshang.qingyun.order.dto.shop.TjPreOrderIDTO;
import com.pinshang.qingyun.order.dto.shop.TjPreOrderODTO;
import com.pinshang.qingyun.order.service.ShopReceiveClient;
import com.pinshang.qingyun.orderreport.dto.*;
import com.pinshang.qingyun.orderreport.mapper.*;
import com.pinshang.qingyun.orderreport.pdf.PsScCommodityGroupLinePdfCreator;
import com.pinshang.qingyun.orderreport.service.PrintTaskService;
import com.pinshang.qingyun.orderreport.util.ListUtils;
import com.pinshang.qingyun.orderreport.util.NumberUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;


@Service
@Slf4j
public class PsScCommodityGroupOrderService {
    @Autowired
    private PsScCommodityGroupOrderMapper commodityGroupOrderMapper;
    @Autowired
    private PsScCommodityGroupLinePdfCreator pdfCreator;
    @Autowired
    private PrintTaskService printTaskService;
    private static final int xMaxStoreNum = 12;//横向打印最大客户数量
    private static final int yMaxStoreNum = 6;//纵向打印最大客户数量
    @Autowired
    private PsScCommodityGroupItemMapper groupItemMapper;
    @Autowired
    private PsScLineStoreMapper lineStoreMapper;
    @Autowired
    private ShopReceiveClient shopReceiveClient;


    /**
     * 商品组打印查询列表
     * @param vo
     * @return
     */
    public PageInfo<CommodityGroupLineOrderODTO> queryCommodityGroupOrderList(CommodityGroupLineOrderIDTO vo) {
        if(vo.getOrderTime()==null){
            vo.setOrderTime(DateUtil.getNowDate());
        }
        return PageHelper.startPage(vo.getPageNo(), vo.getPageSize()).doSelectPageInfo(() -> {
            List<CommodityGroupLineOrderODTO> list = commodityGroupOrderMapper.queryCommodityGroupOrderList(vo);
            list.forEach(item->{
                if(item.getStoreQuantity()==0 || item.getCommodityQuantity()==0){
                    item.setOrderStoreQuantity(0);
                    return;
                }
                List<Long> storeIds = lineStoreMapper.selectByLineId(item.getLineId());
                if(CollectionUtils.isEmpty(storeIds)){
                    item.setOrderStoreQuantity(0);
                    return;
                }
                List<Long> commodityIds = groupItemMapper.queryCommodityByGroupId(item.getCommodityGroupId());
                if(CollectionUtils.isEmpty(commodityIds)){
                    item.setOrderStoreQuantity(0);
                    return;
                }
                TjPreOrderIDTO idto = new TjPreOrderIDTO(vo.getOrderTime(),storeIds,commodityIds);
                List<TjPreOrderODTO> preOrderList = shopReceiveClient.queryTjPreOrderList(idto);
                if (CollectionUtils.isEmpty(preOrderList)){
                    item.setOrderStoreQuantity(0);
                    return;
                }
                Long num = preOrderList.stream().map(TjPreOrderODTO::getStoreId).distinct().count();
                item.setOrderStoreQuantity(num.intValue());
            });
        });
    }

    /**
     * 打印预览
     * @param lineId
     * @param orderTime
     * @return
     */
    public CommodityGroupLinePreviewODTO preview(Long lineId,Date orderTime) {
        if(orderTime==null){
            orderTime = DateUtil.getNowDate();
        }
        CommodityGroupLineTmpDetailDTO detailInfo = commodityGroupOrderMapper.queryLineDetailInfo(lineId);
        if(detailInfo==null || detailInfo.getIsCanPrint()==0 || detailInfo.getCommodityGroupId()==null){
            QYAssert.isFalse("线路不可打印！");
        }
        List<Long> storeIdList = lineStoreMapper.selectByLineId(lineId);
        CommodityGroupLinePreviewTmpVo tmpVo = this.processStoreCommodityData(orderTime,storeIdList,detailInfo.getCommodityGroupId());
        if(tmpVo==null || CollectionUtils.isEmpty(tmpVo.getTableHeader()) || CollectionUtils.isEmpty(tmpVo.getTableData())){
            QYAssert.isFalse("线路("+detailInfo.getLineName()+")没有要打印的订单","lineId={}",lineId);
        }
        CommodityGroupLinePreviewODTO previewODTO = CommodityGroupLinePreviewODTO.init(detailInfo,orderTime);
        SpringUtil.copyProperties(tmpVo,previewODTO);
        return previewODTO;
    }



    /**
     * 打印
     * @param vo
     * @return
     */
    public boolean print(CommodityGroupLinePrintIDTO vo) {
        QYAssert.notEmpty(vo.getLineIdList(),"请选择要打印的线路");
        vo.getLineIdList().forEach(lineId->{
            List<Long> storeIdList = lineStoreMapper.selectByLineId(lineId);
            if(CollectionUtils.isEmpty(storeIdList)){
                return;
            }
            List<CommodityGroupLinePreviewODTO> previewODTOList = new ArrayList<>();
            List<CommodityGroupLinePreviewODTO> previewODTOList2 = new ArrayList<>();
            if (storeIdList.size() <= yMaxStoreNum){
                CommodityGroupLinePreviewODTO printData = this.preview(lineId,vo.getOrderTime());
                if (printData == null || CollectionUtils.isEmpty(printData.getTableHeader()) || CollectionUtils.isEmpty(printData.getTableData())) {
                    return;
                }
                previewODTOList.add(printData);
            }else{
                CommodityGroupLineTmpDetailDTO lineDetailInfo = commodityGroupOrderMapper.queryLineDetailInfo(lineId);
                List<List<Long>> subStoreIdList = ListUtils.splitList(storeIdList,xMaxStoreNum);
                for (int i=0; i<subStoreIdList.size();i++){
                    CommodityGroupLinePreviewTmpVo tmpVo = this.processStoreCommodityData(vo.getOrderTime(),subStoreIdList.get(i),lineDetailInfo.getCommodityGroupId());
                    if(tmpVo==null || CollectionUtils.isEmpty(tmpVo.getTableHeader()) || CollectionUtils.isEmpty(tmpVo.getTableData())){
                        continue;
                    }
                    CommodityGroupLinePreviewODTO previewODTO = CommodityGroupLinePreviewODTO.init(lineDetailInfo,vo.getOrderTime());
                    SpringUtil.copyProperties(tmpVo,previewODTO);
                    if(subStoreIdList.get(i).size()>yMaxStoreNum){
                        previewODTOList.add(previewODTO);
                    }else{
                        previewODTOList2.add(previewODTO);
                    }
                }
            }
            if(CollectionUtils.isNotEmpty(previewODTOList)){
                String fileName = pdfCreator.batchCreatePDF(previewODTOList);
                savePrintTask(fileName,vo.getUserId());
            }
            if(CollectionUtils.isNotEmpty(previewODTOList2)){
                String fileName = pdfCreator.batchCreatePDF(previewODTOList2);
                savePrintTask(fileName,vo.getUserId());
            }
        });
        return true;
    }

//    private List<List<String>> getTableHeader(Map<Long,String> storeMap){
//        List<String> storeNameList = new ArrayList<>(storeMap.values());
//        List<String> title1 = new ArrayList<>();
//        title1.add("客户");
//        title1.addAll(storeNameList);
//        title1.add("汇总");
//
//        List<String> title2 = new ArrayList<>();
//        title2.add("商品名称");
//        title2.add("规格");
//        storeNameList.forEach(item->{
//            title2.add("预定");
//            title2.add("实发");
//        });
//        title2.add("预定量");
//
//        List<List<String>> tableHeader = new ArrayList<>();
//        tableHeader.add(title1);
//        tableHeader.add(title2);
//        return tableHeader;
//    }
//
//    private List<List<String>> getTableData(List<CommodityGroupOrderItemODTO> commodityList,Map<Long,String> storeMap){
//        List<List<String>> tableData = new ArrayList<>();
//        commodityList.forEach(item->{
//            List<String> row = new ArrayList<>();
//            row.add(item.getCommodityName());
//            row.add(item.getCommoditySpec());
//            storeMap.forEach((k,v)->{
//                if(item.getStoreQuantityMap().get(k)!=null){
//                    row.add(NumberUtils.subZeroAndDot(item.getStoreQuantityMap().get(k) + ""));
//                }else{
//                    row.add(null);
//                }
//                row.add(null);//实发
//            });
//            row.add(NumberUtils.subZeroAndDot(item.getTotalQuantity() + ""));
//            tableData.add(row);
//        });
//        return tableData;
//    }

    /**
     * 给定线路+客户，统计相应的商品，处理大量客户生成不同的PDF时，各PDF商品单独统计
     * @param orderTime
     * @param storeIdList
     * @param groupId
     * @return
     */
    private CommodityGroupLinePreviewTmpVo processStoreCommodityData(Date orderTime,List<Long> storeIdList,Long groupId) {
        if(orderTime==null){
            orderTime = DateUtil.getNowDate();
        }
        List<Long> commodityIds = groupItemMapper.queryCommodityByGroupId(groupId);
        if(CollectionUtils.isEmpty(storeIdList) || CollectionUtils.isEmpty(commodityIds)){
            return null;
        }
        TjPreOrderIDTO preOrderIDTO = new TjPreOrderIDTO(orderTime,storeIdList,commodityIds);
        List<TjPreOrderODTO> preOrderODTOList = shopReceiveClient.queryTjPreOrderList(preOrderIDTO);
        if(CollectionUtils.isEmpty(preOrderODTOList)){
            return null;
        }
        List<CommodityGroupLineTmpOrderItemDTO> orderItemList = preOrderODTOList.stream().map(CommodityGroupLineTmpOrderItemDTO::convert).collect(Collectors.toList());
        //List<CommodityGroupLineTmpOrderItemDTO> orderItemList = commodityGroupOrderMapper.queryLineOrderItemList(orderTime,lineId,storeIdList);
        if(CollectionUtils.isEmpty(orderItemList)){
            return null;
        }
        Map<Long,String> storeMap = new LinkedHashMap<>();
        List<CommodityGroupOrderItemODTO> commodityList = new ArrayList<>();
        orderItemList.stream().collect(Collectors.groupingBy(CommodityGroupLineTmpOrderItemDTO::getCommodityId, LinkedHashMap::new,Collectors.toList()))
                .forEach((k,v)->{
                    CommodityGroupOrderItemODTO commodityDTO = new CommodityGroupOrderItemODTO();
                    SpringUtil.copyProperties(v.get(0),commodityDTO);
                    Map<Long,BigDecimal> storeQuantityMap = new LinkedHashMap<>();
                    v.stream().collect(Collectors.groupingBy(CommodityGroupLineTmpOrderItemDTO::getStoreId, LinkedHashMap::new,Collectors.toList())).forEach((sk,sv)->{
                        BigDecimal orderQuantity = sv.stream().filter(item->item.getExpectQuantity()!=null).map(CommodityGroupLineTmpOrderItemDTO::getExpectQuantity).reduce(BigDecimal.ZERO, BigDecimal::add);
                        storeQuantityMap.put(sk,orderQuantity);
                        if(!storeMap.containsKey(sk)){
                            storeMap.put(sk,sv.get(0).getStoreName());
                        }
                    });
                    commodityDTO.setStoreQuantityMap(storeQuantityMap);
                    commodityList.add(commodityDTO);
                });
        if(storeMap.isEmpty() || CollectionUtils.isEmpty(commodityList)){
            return null;
        }
        CommodityGroupLinePreviewTmpVo tmpVo = new CommodityGroupLinePreviewTmpVo();
        tmpVo.setStoreMap(storeMap);
        tmpVo.setCommodityList(commodityList);
        return tmpVo;
    }

    private void savePrintTask(String fileName,Long userId){
//        try {
            printTaskService.savePrintTask(fileName, PrinterDataGroupTypeEnum.STATISTICAL_QUERY_DELIVERY,userId, 1, PrinterTypeEnum.LASER.getCode());
//        } catch (Exception e) {
//            log.error("商品组线路打印异常:{}", e);
//            return;
//        }
    }


    /**
     * 导出表头
     * @param tableHeaderList
     * @return
     */
    public List<List<String>> processExportHeader(List<List<String>> tableHeaderList){
        List<String> header = tableHeaderList.get(0);
        List<List<String>> headerList = new ArrayList<>(header.size()*2);
        for (int i = 0; i < header.size(); i++){
            if (i ==0) {
                headerList.add(Arrays.asList(header.get(i), header.get(i),"商品名称"));
                headerList.add(Arrays.asList(header.get(i), header.get(i),"规格"));
            }else if (i >0 && i < header.size()-1) {
                headerList.add(Arrays.asList(header.get(i), header.get(i),"预定"));
                headerList.add(Arrays.asList(header.get(i), header.get(i),"实发"));
            }else{
                headerList.add(Arrays.asList(header.get(i), header.get(i),"预定量"));
            }
        }
        return headerList;
    }

}

package com.pinshang.qingyun.orderreport.service;

import com.pinshang.qingyun.base.enums.PrinterDataGroupTypeEnum;
import com.pinshang.qingyun.common.dto.DictionaryODTO;
import com.pinshang.qingyun.common.service.DictionaryClient;
import com.pinshang.qingyun.orderreport.mapper.BillOfLeadingMapper;
import com.pinshang.qingyun.orderreport.mapper.StoreMapper;
import com.pinshang.qingyun.orderreport.mapper.entry.BillOfLeadingPreviewEntity;
import com.pinshang.qingyun.orderreport.pdf.BillOfLeadingPdfCreator;
import com.pinshang.qingyun.orderreport.util.NumberUtils;
import com.pinshang.qingyun.orderreport.vo.BillOfLadingPreviewRespVo;
import com.pinshang.qingyun.orderreport.vo.DeliveryListPreviewReqVo;
import com.pinshang.qingyun.orderreport.vo.DictionaryVo;
import lombok.extern.slf4j.Slf4j;
import org.joda.time.DateTime;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.NumberFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2019/1/28 14:58.
 */
@Service
@Slf4j
public class BillOfLeadingService {
    @Autowired
    private PrintStatusService printStatusService;
    private final BillOfLeadingMapper billOfLeadingMapper;
    private final StoreMapper storeMapper;
    private final BillOfLeadingPdfCreator billOfLeadingPdfCreator;
    private final PrintTaskService printTaskService;
    @Autowired
    private CommonPdfPrintService commonPdfPrintService;
    @Autowired
    private DictionaryClient dictionaryClient;

    public BillOfLeadingService(BillOfLeadingMapper billOfLeadingMapper, StoreMapper storeMapper, BillOfLeadingPdfCreator billOfLeadingPdfCreator, PrintTaskService printTaskService) {
        this.billOfLeadingMapper = billOfLeadingMapper;
        this.storeMapper = storeMapper;
        this.billOfLeadingPdfCreator = billOfLeadingPdfCreator;
        this.printTaskService = printTaskService;
    }

    public BillOfLadingPreviewRespVo preview(Long lineId, String orderDate,boolean storeTypeAll) {
        BillOfLadingPreviewRespVo result;
        if (lineId == null ) {
            return null;
        }
        Date date = null;
        try {
            date = new SimpleDateFormat("yyyy-MM-dd").parse(orderDate);
        } catch (ParseException e) {
            log.error("orderDate转换出错",e);
        }
        if (date == null) {
            date = new DateTime().plusDays(1).toDate();
        }
        boolean latestFlag = !new DateTime(date).isBefore(new DateTime().withMillisOfDay(0));
        result = billOfLeadingMapper.queryTitle(lineId);
        result.setOrderDate(date);

        List<String> tableHeader = new ArrayList<>();
        tableHeader.add("商品名称(规格)");
        // 找到所有店铺类型
        List<DictionaryVo> storeTypes = new ArrayList<>();
        if(storeTypeAll){
            storeTypes = storeMapper.selectAllStoreTypes();
        }else {
            List<DictionaryODTO> dictionaryODTOS = dictionaryClient.selectBillOfLadingStoreTypes();
            if(dictionaryODTOS!=null) {
                for (DictionaryODTO dictionaryODTO : dictionaryODTOS) {
                    storeTypes.add(new DictionaryVo(Long.valueOf(dictionaryODTO.getId()), dictionaryODTO.getOptionName()));
                }
            }
            //storeTypes =  storeMapper.selectBillOfLadingStoreTypes();
        }
        List<String> storeTypeNames = storeTypes.parallelStream().map(DictionaryVo::getName).collect(Collectors.toList());
        tableHeader.add("合计");
        tableHeader.addAll(storeTypeNames);
        result.setTableHeader(tableHeader);
        List<BillOfLeadingPreviewEntity> dbResult = billOfLeadingMapper.queryPreviewData(lineId, orderDate, latestFlag);
        Map<Long, List<BillOfLeadingPreviewEntity>> commodityMap = new LinkedHashMap<>();
        for (BillOfLeadingPreviewEntity entity : dbResult) {
            List<BillOfLeadingPreviewEntity> lists = commodityMap.get(entity.getCommodityId());
            if (lists == null) {
                lists = new ArrayList<>();
                lists.add(entity);
                commodityMap.put(entity.getCommodityId(), lists);
            } else {
                lists.add(entity);
            }
        }
        List<List<String>> tableData = new ArrayList<>();
        List<DictionaryVo> finalStoreTypes = storeTypes;
        commodityMap.forEach((key, value) -> {
            ///////////////////////////////////////////////////////////////////////////
            // 处理表格的每一行
            ///////////////////////////////////////////////////////////////////////////
            List<String> row = proceedTableRow(value, finalStoreTypes);
            tableData.add(row);
        });
        result.setTableData(tableData);
        return result;
    }

    /**
     * 处理表格的每一行数据
     * @param value 该商品对应的所有类型的客户购买的数量
     * @param storeTypes 所有的客户类型
     * @return 返回该行数据
     */
    private List<String> proceedTableRow(List<BillOfLeadingPreviewEntity> value, List<DictionaryVo> storeTypes) {
        NumberFormat numberFormat = NumberFormat.getNumberInstance();
        numberFormat.setMaximumFractionDigits(2);
        numberFormat.setRoundingMode(RoundingMode.HALF_UP);
        numberFormat.setGroupingUsed(false);
        List<String> row = new ArrayList<>();
        BillOfLeadingPreviewEntity entity1 = value.get(0);
        row.add(entity1.getCommodityNameSpec());
        BigDecimal totalNum = BigDecimal.ZERO;
        List<String> rowValue = new ArrayList<>();
        for (BillOfLeadingPreviewEntity entity : value) {
            totalNum = totalNum.add(entity.getCommodityNum());
        }
        for (DictionaryVo type : storeTypes) {
            // 是否有该类型的客户购买了商品
            boolean hasType = false;
            for (BillOfLeadingPreviewEntity entity : value) {
                if (type.getId().equals(entity.getStoreTypeId())) {
                    rowValue.add(NumberUtils.subZeroAndDot(entity.getCommodityNum() + ""));
                    hasType = true;
                    break;
                }
            }
            if (!hasType) {
                rowValue.add("");
            }
        }
        row.add(NumberUtils.subZeroAndDot(numberFormat.format(totalNum.stripTrailingZeros())));
        row.addAll(rowValue);
        return row;
    }

    private String print(DeliveryListPreviewReqVo param) {
        Long lineId = param.getLineId();
        Date orderDate = param.getOrderDate();
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");
        BillOfLadingPreviewRespVo dataList = preview(lineId, format.format(orderDate),false);
        if (dataList == null || dataList.getTableData() == null || dataList.getTableData().size() == 0) {
            return "fail";
        }
        String result = billOfLeadingPdfCreator.create(dataList);
//        try {
            //order_report_TJ_BILL_OF_LEADING 提货单
            commonPdfPrintService.uploadAndPrintPdf(result,"order_report_TJ_BILL_OF_LEADING",PrinterDataGroupTypeEnum.STATISTICAL_QUERY_DELIVERY,param.getUserId());
           // printTaskService.savePrintTask(result, PrinterDataGroupTypeEnum.STATISTICAL_QUERY_DELIVERY, param.getUserId(), 1, PrinterTypeEnum.LASER.getCode());
//        } catch (Exception e) {
//            return "fail";
//        }
        return "ok";
    }

    public String printAll(List<DeliveryListPreviewReqVo> params, Long userId) {
        for (DeliveryListPreviewReqVo previewVo : params) {
            previewVo.setUserId(userId);
            print(previewVo);
        }
        printStatusService.saveOrUpdate(params);
        return "ok";
    }
}

package com.pinshang.qingyun.orderreport.controller.ps;

import com.github.pagehelper.PageInfo;
import com.pinshang.qingyun.base.api.TokenInfo;
import com.pinshang.qingyun.base.configure.expand.QYAssert;
import com.pinshang.qingyun.base.util.FastThreadLocalUtil;
import com.pinshang.qingyun.orderreport.service.ps.PsScCommodityGroupService;
import com.pinshang.qingyun.orderreport.vo.ps.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR>
 * @Version 0.1.0
 * @Date 2021/1/18 10:39
 * @Copyright © 2019-2021 qm
 * @Description -- 类说明
 **/
@RestController
@RequestMapping(value = "/ps/commodityGroup")
@Api(value = "配送-水产-商品组api", tags = "commodityGroup", description = "配送-水产-商品组相关接口")
public class PsScCommodityGroupController {

    @Autowired
    private PsScCommodityGroupService psScCommodityGroupService;

    @ApiOperation(value = "商品组列表", notes = "商品组列表")
    @PostMapping("/queryCommodityGroupList")
    public PageInfo<PsScCommodityGroupVO> queryCommodityGroupList(@RequestBody QueryCommodityGroupVO dto) {
        return psScCommodityGroupService.queryCommodityGroupList(dto);
    }

    @ApiOperation(value = "商品组下的商品列表", notes = "商品组下的商品列表")
    @PostMapping("/queryCommodityGroupCommodityList")
    public PageInfo<PsScCommodityGroupCommodityVO> queryCommodityGroupCommodityList(@RequestBody QueryCommodityGroupCommodityVO dto) {
        QYAssert.notNull(dto.getCommodityGroupId(), "商品组id不能为空！");
        return psScCommodityGroupService.queryCommodityGroupCommodityList(dto);
    }

    @ApiOperation(value = "商品组批量添加商品", notes = "商品组批量增加商品，商品code使用回车键间隔")
    @PostMapping("/addCommodityGroupCommodity")
    public PsResult addCommodityGroupCommodity(@RequestBody AddCommodityGroupCommodityVO dto) {
        TokenInfo tokenInfo = FastThreadLocalUtil.getQY();
        QYAssert.notNull(tokenInfo, "登录过期！");
        dto.setCreateId(tokenInfo.getUserId());
        return psScCommodityGroupService.addCommodityGroupCommodity(dto);
    }

    @ApiOperation(value = "删除商品组下指定的商品", notes = "删除商品组下的商品")
    @PostMapping("/delCommodityGroupCommodity")
    public Boolean delCommodityGroupCommodity(@RequestBody DelCommodityGroupCommodityVO dto) {
        TokenInfo tokenInfo = FastThreadLocalUtil.getQY();
        QYAssert.notNull(tokenInfo, "登录过期！");
        dto.setCreateId(tokenInfo.getUserId());
        return psScCommodityGroupService.delCommodityGroupCommodity(dto);
    }

    @ApiOperation(value = "新增商品组", notes = "新增商品组")
    @PostMapping("/addCommodityGroup")
    public Boolean addCommodityGroup(@RequestBody AddCommodityGroupVO dto) {
        TokenInfo tokenInfo = FastThreadLocalUtil.getQY();
        QYAssert.notNull(tokenInfo, "登录过期！");
        dto.setCreateId(tokenInfo.getUserId());
        return psScCommodityGroupService.addCommodityGroup(dto);
    }

    @ApiOperation(value = "修改商品组名称", notes = "修改商品组名称")
    @PostMapping("/modCommodityGroup")
    public Boolean modCommodityGroup(@RequestBody ModCommodityGroupVO dto) {
        TokenInfo tokenInfo = FastThreadLocalUtil.getQY();
        QYAssert.notNull(tokenInfo, "登录过期！");
        dto.setCreateId(tokenInfo.getUserId());
        return psScCommodityGroupService.modCommodityGroup(dto);
    }

    @ApiOperation(value = "启用/停用商品组，后台自动判断", notes = "停用/启用商品组")
    @PostMapping("/onOffCommodityGroup")
    public Boolean onOffCommodityGroup(@RequestBody OnOffCommodityGroupVO dto) {
        TokenInfo tokenInfo = FastThreadLocalUtil.getQY();
        QYAssert.notNull(tokenInfo, "登录过期！");
        dto.setCreateId(tokenInfo.getUserId());
        return psScCommodityGroupService.onOffCommodityGroup(dto);
    }

}

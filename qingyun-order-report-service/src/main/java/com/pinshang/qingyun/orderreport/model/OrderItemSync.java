package com.pinshang.qingyun.orderreport.model;

import com.pinshang.qingyun.base.po.BaseIDPO;
import lombok.Data;

import javax.persistence.Table;
import java.math.BigDecimal;

/**
 * @description:
 * @author: hhf
 * @time: 2021/8/13 10:44
 */
@Data
@Table(name = "t_tj_order_list_sync")
public class OrderItemSync extends BaseIDPO {

    /** 订单id **/
    private Long orderId;
    /** 商品id **/
    private Long commodityId;
    /** 商品数量 **/
    private BigDecimal commodityNum;
    /** 单价 **/
    private BigDecimal commodityPrice;

    /** 促销前的源始金额 **/
    private BigDecimal totalPrice;
    /** 类型 **/
    private Integer type;
    /** 备注 **/
    private String remark;

    /**
     * 商品类型-1-非组合 2-组合  3-组合子品
     */

    private Integer combType;
    /**
     * 属于组合商品id
     */
    private Long combCommodityId;
}

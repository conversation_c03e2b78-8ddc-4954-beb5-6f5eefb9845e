package com.pinshang.qingyun.orderreport.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.pinshang.qingyun.orderreport.enums.OrderModeTypeEnums;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2019/1/23 13:42.
 */
@Data
public class DeliveryListPreviewReqVo {

    private Long orderId ;
    private Long lineId;
    @JsonFormat(pattern="yyyy-MM-dd",timezone = "GMT+8")
    private Date orderDate;
    private Long printDeliveryBatchId;
    private String deliveryManId;
    private String printDeliveryBatchName;
    private Long userId;
    private Integer deliveryBatch;
    /**
     * 页面传入打印状态，根据 printStatus 和 type 值 判断 是否需要更新数据库，记录打印状态
     */
    private Integer printStatus;
    /**
     *  0=送货清单
     *  1=提货单
     */
    private Integer type;

    /*** 补货数据 0-订单、 1-补货订单、 2-查所有0和1 ()  （此字段没值也是查所有0和1 兼容清美组手低版本） */
    private Integer orderModeType;

    public String getOrderModeTypeName() {

        if (this.orderModeType == null) {
            orderModeType = 2;
        }
        return OrderModeTypeEnums.getName(orderModeType);
    }
}

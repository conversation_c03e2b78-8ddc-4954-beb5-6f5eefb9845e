package com.pinshang.qingyun.orderreport.controller;

import com.alibaba.excel.EasyExcel;
import com.pinshang.qingyun.base.util.ExcelUtil;
import com.pinshang.qingyun.infrastructure.exportfile.cache.starter.FileCacheQuery;
import com.pinshang.qingyun.infrastructure.exportfile.cache.starter.FileCacheQueryParameter;
import com.pinshang.qingyun.orderreport.mapper.entry.LogisticsDeliveryOrderAmountStatisticsTableEntry;
import com.pinshang.qingyun.orderreport.service.LogisticsDeliveryOrderAmountStatisticsService;
import com.pinshang.qingyun.orderreport.vo.LogisticsDeliveryOrderAmountStatisticsVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.joda.time.LocalDate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * @description: 物流配送订单金额统计
 * @author: hhf
 * @time: 2022/6/15/015 14:04
 */
@RestController
@RequestMapping("/statistical/logisticsDeliveryOrderAmount")
@Api(value = "logisticsDeliveryOrderAmount", tags = "物流配送订单金额统计", description = "物流配送订单金额统计")
@Slf4j
public class LogisticsDeliveryOrderAmountStatisticsController {

    @Autowired
    private LogisticsDeliveryOrderAmountStatisticsService service;

    @PostMapping("/queryList")
    @ApiModelProperty(value = "物流配送订单金额统计", notes = "物流配送订单金额统计")
    public LogisticsDeliveryOrderAmountStatisticsTableEntry queryList(@RequestBody LogisticsDeliveryOrderAmountStatisticsVo vo){
        return service.queryList(vo);
    }

    @ApiOperation(value = "导出线路组送货金额汇总", notes = "导出线路组送货金额汇总")
    @RequestMapping(value = "/export", method = RequestMethod.GET)
    @FileCacheQuery(bizCode = "OR_LOGISTICS_DELIVERY_ORDER_E")
    public void export(@FileCacheQueryParameter LogisticsDeliveryOrderAmountStatisticsVo vo, HttpServletResponse response) throws IOException {
        long qS = System.currentTimeMillis();
        vo.initExportPage();
        LogisticsDeliveryOrderAmountStatisticsTableEntry tableEntry = queryList(vo);
        long qE = System.currentTimeMillis();
        log.info("线路组送货金额汇总-导出--查询时间=" + ((qE - qS) / 1000));
        //处理数据并导出
        dealDataToExport(response, tableEntry.getTableHeader(),tableEntry.getTableData());
        long eE = System.currentTimeMillis();
        log.info("线路组送货金额汇总-导出--excel处理时间="+ ( (eE -qE) /1000 )  );
    }

    /**
     * 处理数据并导出
     * @param response
     * @param tableHeader
     * @param tableDataList
     * @throws IOException
     */
    public void dealDataToExport(HttpServletResponse response, List<String> tableHeader, List<List<String>> tableDataList) throws IOException {
        //tableHeader
        List<List<String>> headerList = new ArrayList<>(tableHeader.size()*1);
        for (int i = 0; i < tableHeader.size(); i++){
            headerList.add(Arrays.asList(tableHeader.get(i),tableHeader.get(i)));
        }
        try {
            ExcelUtil.setFileNameAndHead(response, "线路组送货金额汇总" + LocalDate.now().toString("yyyyMMdd"));
            EasyExcel.write(response.getOutputStream()).head(headerList).autoCloseStream(Boolean.FALSE).sheet("线路组送货金额汇总")
                    .doWrite(tableDataList);

        }catch (Exception e){
            log.error("线路组送货金额汇总导出错误", e);
            ExcelUtil.setExceptionResponse( response );
        }
    }

}

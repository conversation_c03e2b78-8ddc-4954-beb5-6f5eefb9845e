package com.pinshang.qingyun.orderreport.controller.web;

import com.pinshang.qingyun.order.dto.orderStatistics.OrderStatisticsMonitorIDTO;
import com.pinshang.qingyun.order.dto.orderStatistics.OrderStatisticsODTO;
import com.pinshang.qingyun.order.service.OrderStatisticsMonitorClient;
import com.pinshang.qingyun.orderreport.service.OrderReportJobService;
import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Calendar;
import java.util.Date;

/**
 * <AUTHOR>
 * @Date 2024/6/20 14:12
 */
@Slf4j
@RestController
@RequestMapping("/orderReportJob/web")
@Api(value = "统计查询服务-Web", description = "OrderReportJobController")
public class OrderReportJobController {

    @Autowired
    private OrderReportJobService orderReportJobService;

    @GetMapping(value = "/checkOrderSize")
    public void checkOrderSize(){
        orderReportJobService.checkOrderSize(new Date());
    }

    @GetMapping(value = "/checkOrderListSize")
    public void checkOrderListSize(){
        orderReportJobService.checkOrderListSize(new Date());
    }


    @GetMapping(value = "/checkOrderListLatestSize")
    public void checkOrderListLatestSize(){
        orderReportJobService.checkOrderListLatestSize(new Date());
    }

    @GetMapping(value = "/repeatHaving1")
    public void repeatHaving1(){
        orderReportJobService.repeatHaving1();
    }

    @PostMapping(value = "/syncOrderList")
    public void syncOrderList(@RequestParam("forwardAmount") Integer forwardAmount,@RequestParam("amount") Integer amount){
        orderReportJobService.syncOrderList(forwardAmount,amount);
    }
}

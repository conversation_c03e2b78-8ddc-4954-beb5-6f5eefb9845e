package com.pinshang.qingyun.orderreport.mapper;

import com.pinshang.qingyun.orderreport.dto.DeliveryNoteIDTO;
import com.pinshang.qingyun.orderreport.dto.KeyValueODTO;
import com.pinshang.qingyun.orderreport.dto.deliverynote.DeliverymanStoreInfoODTO;
import com.pinshang.qingyun.orderreport.dto.deliverynote.SelectDeliverymanStoreInfoListIDTO;
import com.pinshang.qingyun.orderreport.mapper.entry.DeliveryNoteOrderEntry;

import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * @author: chen<PERSON>ang
 * @time: 2021/9/17 14:08
 */
@Repository
public interface DeliveryNoteMapper {

    /**
     * 查询当前送货员的送货单
     * @param deliveryNoteIDTO
     * @return
     */
    List<DeliveryNoteOrderEntry> selectDeliveryNoteOrderList(DeliveryNoteIDTO deliveryNoteIDTO);
    
    /**
     * 查询  送货员-客户信息 列表
     * 
     * @param idto
     * @return
     */
    public List<DeliverymanStoreInfoODTO> selectDeliverymanStoreInfoList(SelectDeliverymanStoreInfoListIDTO idto);
    
    /**
     * 查询  客户订单数量  列表
     * 
     * @param orderTime
     * @param storeIdList
     * @return
     */
    public List<KeyValueODTO> selectStoreOrderQuantityList(@Param("orderTime")String orderTime, @Param("storeIdList")List<Long> storeIdList);
    
}

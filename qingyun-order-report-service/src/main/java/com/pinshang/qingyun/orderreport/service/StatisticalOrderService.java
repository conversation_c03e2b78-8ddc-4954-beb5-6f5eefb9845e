package com.pinshang.qingyun.orderreport.service;

import com.pinshang.qingyun.box.utils.SpringUtil;
import com.pinshang.qingyun.common.dto.DictionaryODTO;
import com.pinshang.qingyun.common.service.DictionaryClient;
import com.pinshang.qingyun.orderreport.mapper.StoreMapper;
import com.pinshang.qingyun.orderreport.model.Store;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Example;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2019/3/6 16:39.
 */
@Service
public class StatisticalOrderService {

    private static final String TEST_STORE_OPTION_CODE = "TEST_STORE";

    private final DictionaryClient dictionaryClient;
    private final StoreMapper storeMapper;
    private final OrderService orderService;

    public StatisticalOrderService(DictionaryClient dictionaryClient, StoreMapper storeMapper, OrderService orderService) {
        this.dictionaryClient = dictionaryClient;
        this.storeMapper = storeMapper;
        this.orderService = orderService;
    }

    public boolean discardTestUserOrder() {
        List<DictionaryODTO> dictionary = dictionaryClient.querySubDictionaryByParentOptionCode(TEST_STORE_OPTION_CODE);
        if (SpringUtil.isEmpty(dictionary)) {
            return true;
        }
        List<String> storeCodes = dictionary.stream().map(DictionaryODTO::getOptionValue).collect(Collectors.toList());
        Example storeEx = new Example(Store.class);
        storeEx.createCriteria().andIn("storeCode", storeCodes);
        storeEx.selectProperties("id");
        List<Store> stores = storeMapper.selectByExample(storeEx);
        if (SpringUtil.isEmpty(stores)) {
            return true;
        }
        List<Long> storeIds = stores.stream().map(Store::getId).collect(Collectors.toList());

        orderService.discardOrderByStoreIds(storeIds);
        return true;
    }
}

package com.pinshang.qingyun.orderreport.config;

/**
 * <AUTHOR>
 * @Version 0.1.0
 * @Date 2021/1/22 10:26
 * @Copyright © 2019-2021 qm
 * @Description -- 类说明
 **/
public class OrderReportConstant {

    /**
     * 新增水产配送-商品组锁
     */
    public static final String ADD_COMMODITY_GROUP_SC_LOCK = "ADD_COMMODITY_GROUP_SC_LOCK";

    /**
     * 修改商品组名称锁
     */
    public static final String MOD_COMMODITY_GROUP_SC_LOCK = "MOD_COMMODITY_GROUP_SC_LOCK";

    /**
     * 水产配送-商品组编号
     */
    public static final String PS_SC_COMMODITY_GROUP = "PS_SC_COMMODITY_GROUP";

    /**
     * 删除商品组-商品锁
     */
    public static final String DEL_COMMODITY_GROUP_COMMODITY_LOCK = "DEL_COMMODITY_GROUP_COMMODITY_LOCK";

    /**
     * 添加商品组-商品锁
     */
    public static final String ADD_COMMODITY_GROUP_SC_COMMODITY_LOCK = "ADD_COMMODITY_GROUP_SC_COMMODITY_LOCK";




    /**
     * 添加商品组线路-线路锁
     */
    public static final String ADD_COMMODITY_GROUP_SC_LINE_LOCK = "ADD_COMMODITY_GROUP_SC_LINE_LOCK";

    /**
     * 修改商品组线路-线路锁
     */
    public static final String MOD_COMMODITY_GROUP_SC_LINE_LOCK = "MOD_COMMODITY_GROUP_SC_LINE_LOCK";

    /**
     * 添加商品组线路-客户锁
     */
    public static final String ADD_COMMODITY_GROUP_SC_LINE_STORE_LOCK = "ADD_COMMODITY_GROUP_SC_LINE_STORE_LOCK";

    /**
     * 删除商品组线路-客户锁
     */
    public static final String DEL_COMMODITY_GROUP_SC_LINE_STORE_LOCK = "DEL_COMMODITY_GROUP_SC_LINE_STORE_LOCK";

    /**
     * 水产配送商品组线路编号
     */
    public static final String PS_SC_COMMODITY_LINE = "PS_SC_COMMODITY_LINE";





    /**
     * 锁释放时间
     */
    public static final Long LOCK_EXPIRE_TIME = 360L;
}

<?xml version="1.0" encoding="UTF-8" ?>

<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.pinshang.qingyun.orderreport.mapper.OrderMirrorMapper">

    <!--<insert id="batchInsert">-->
        <!--INSERT INTO t_tj_order_mirror(-->
            <!--order_id,order_code,deliveryman_id,deliveryman_name,salesman_id,salesman_name,-->
            <!--supervisor_id,supervisor_name,region_manager_id,region_manager_name,office_director_id,office_director_name-->
        <!--)-->
        <!--SELECT-->
            <!--o.id AS orderId,o.order_code,s.deliveryman_id,s.deliveryman_name,s.salesman_id,s.salesman_name,-->
            <!--s.supervisor_id,s.supervisor_name,s.region_manager_id,s.region_manager_name,s.office_director_id,s.office_director_name-->
        <!--FROM t_tj_order o-->
        <!--LEFT JOIN t_store s ON o.store_id = s.id-->
        <!--WHERE o.id IN-->
        <!--<foreach collection="orderIds" item="orderId" separator="," open="(" close=")">-->
            <!--${orderId}-->
        <!--</foreach>-->
    <!--</insert>-->

    <update id="batchUpdate" parameterType="java.util.List">
        <foreach collection="list" item="item" index="index" open="" close="" separator=";">
            UPDATE t_tj_order_mirror
            SET
                deliveryman_id = #{item.deliverymanId},deliveryman_name = #{item.deliverymanName},
                salesman_id = #{item.salesmanId},salesman_name = #{item.salesmanName},
                supervisor_id = #{item.supervisorId},supervisor_name = #{item.supervisorName},
                region_manager_id = #{item.regionManagerId},region_manager_name = #{item.regionManagerName},
                office_director_id = #{item.officeDirectorId},office_director_name = #{item.officeDirectorName}
            WHERE
                order_id =  #{item.orderId}
        </foreach>
    </update>

    <select id="queryOrderMirrorEntry" resultType="com.pinshang.qingyun.orderreport.model.OrderMirror">
        SELECT
            o.id AS orderId,o.order_code,s.deliveryman_id,s.deliveryman_name,s.salesman_id,s.salesman_name,
            s.supervisor_id,s.supervisor_name,s.region_manager_id,s.region_manager_name,s.office_director_id,s.office_director_name
        FROM t_tj_order o
        LEFT JOIN t_store s ON o.store_id = s.id
        WHERE o.id IN
        <foreach collection="orderIds" item="orderId" separator="," open="(" close=")">
            ${orderId}
        </foreach>
    </select>

</mapper>
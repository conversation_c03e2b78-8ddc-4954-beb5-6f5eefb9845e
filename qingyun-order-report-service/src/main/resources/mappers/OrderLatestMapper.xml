<?xml version="1.0" encoding="UTF-8" ?>

<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.pinshang.qingyun.orderreport.mapper.OrderLatestMapper">

    <insert id="batchInsert" parameterType="com.pinshang.qingyun.orderreport.model.OrderLatest">
        INSERT INTO t_tj_order_latest
        ( id, company_id,order_code, store_id, order_time, mode_type,
        order_type, total_amount, final_amount,order_amount,freight_amount,
        order_status,delivery_batch,settle_status,change_price_status,print_num,
        print_type,create_time,create_id,update_time,update_id
        )
        VALUES
        <foreach collection="list" separator="," item="item">
            ( #{item.id},#{item.companyId}, #{item.orderCode}, #{item.storeId}, #{item.orderTime}, #{item.modeType},
            #{item.orderType}, #{item.totalAmount}, #{item.finalAmount},#{item.orderAmount},#{item.freightAmount},
            #{item.orderStatus}, #{item.deliveryBatch}, #{item.settleStatus}, #{item.changePriceStatus}, #{item.printNum},
            #{item.printType}, #{item.createTime}, #{item.createId}, #{item.updateTime}, #{item.updateId}
            )
        </foreach>
    </insert>

    <select id="queryLatestAndStoreTypeData" resultType="com.pinshang.qingyun.orderreport.mapper.entry.LatestStoreTypeDetailEntry">
        SELECT
            ll.commodity_id,SUM(ll.commodity_num) AS commodityNum,SUM(ll.total_price) AS totalPrice,o.company_id
        FROM
            t_tj_order_list_latest ll
        INNER JOIN t_tj_order_latest o ON ll.order_id = o.id AND o.order_time = #{orderTime} AND o.order_status=0
        WHERE ll.comb_type IN (1,3)
        GROUP BY o.company_id,ll.commodity_id
    </select>

    <select id="queryOrderLatestByStoreIds" resultType="com.pinshang.qingyun.orderreport.model.Order">
        SELECT
            o.id,o.store_id
        FROM t_tj_order_latest o
        WHERE
            o.store_id IN
            <foreach collection="storeIds" item="storeId" separator="," open="(" close=")">
                ${storeId}
            </foreach>
        AND  o.order_time <![CDATA[ > ]]> NOW()
    </select>

    <select id="queryOrderLatestList" resultType="com.pinshang.qingyun.orderreport.model.OrderLatest">
        SELECT o.id,o.order_time
        FROM t_tj_order_latest o
        WHERE o.order_time <![CDATA[ < ]]> #{nowDate}
        AND o.order_status = 0
    </select>

    <select id="queryLatestCommoditySaleTopN" parameterType="com.pinshang.qingyun.orderreport.dto.LatestCommoditySaleStatisticsIDTO" resultType="com.pinshang.qingyun.orderreport.dto.LatestCommoditySaleStatisticsODTO">
        SELECT
            ll.commodity_id,
            sum(ll.total_price) as totalPrice,
            c.commodity_name,
            c.commodity_third_kind_name,
            c.commodity_unit_name
        FROM
            t_tj_order_list_latest ll
        INNER JOIN t_tj_order_latest o ON ll.order_id = o.id AND o.order_time = #{orderTime} AND o.order_status=0
        LEFT JOIN t_commodity c on ll.commodity_id = c.id
        <where>
            <if test="factoryIds != null and factoryIds.size>0">
                c.commodity_factory_id in
                <foreach collection="factoryIds" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
        </where>
        group by ll.commodity_id
        order by ll.total_price desc, ll.commodity_id
        <if test="size != null">
        limit #{size}
        </if>
    </select>

</mapper>
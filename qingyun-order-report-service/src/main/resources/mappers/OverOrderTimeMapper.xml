<?xml version="1.0" encoding="UTF-8" ?>

<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.pinshang.qingyun.orderreport.mapper.OverOrderTimeMapper">

    <select id="queryOverOrderList" resultType="com.pinshang.qingyun.orderreport.mapper.entry.OverOrderTimeEntry">
        SELECT
            o.order_code,
            o.order_time,
            o.final_amount,
            o.create_time AS orderCreateTime,
            o.mode_type,
            o.create_id AS operatorId,
            u.employee_name AS operatorName,
            s.id AS storeId,
            s.store_line_group_id AS lineGroupId,
            s.store_line_group_name AS lineGroupName,
            s.supervisor_id AS supervisorId,
            s.supervisor_name AS supervisorName
        FROM t_tj_order_latest o
        INNER JOIN t_store s ON o.store_id = s.id
        LEFT JOIN t_employee_user u ON o.create_id = u.user_id
        WHERE o.order_time = #{orderTime} and o.order_status = 0
        GROUP BY o.order_time,o.store_id
        HAVING count(1)=1 AND o.mode_type = 1
        order by o.order_time,o.store_id
    </select>

    <insert id="batchInsert" parameterType="com.pinshang.qingyun.orderreport.mapper.entry.OverOrderTimeEntry">
        INSERT INTO t_tj_over_order_time(
            order_code, order_time, final_amount, order_create_time, operator_id,operator_name,
            store_id, line_group_id, line_group_name, supervisor_id, supervisor_name
        )
        VALUES
        <foreach collection="list" separator="," item="item">
            ( #{item.orderCode}, #{item.orderTime}, #{item.finalAmount}, #{item.orderCreateTime}, #{item.operatorId},#{item.operatorName},
              #{item.storeId}, #{item.lineGroupId}, #{item.lineGroupName},#{item.supervisorId},#{item.supervisorName}
            )
        </foreach>
    </insert>

</mapper>
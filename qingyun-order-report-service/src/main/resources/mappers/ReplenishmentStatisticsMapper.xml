<?xml version="1.0" encoding="UTF-8" ?>

<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.pinshang.qingyun.orderreport.mapper.ReplenishmentStatisticsMapper">

    <sql id="getCondition">
        <where>
            <if test="vo.startDate !=null and vo.endDate != null">
                AND o.order_time BETWEEN #{vo.startDate} AND #{vo.endDate}
            </if>
            <if test="vo.deliveryManId != null">
                AND s.deliveryman_id = #{vo.deliveryManId}
            </if>
            <if test="vo.deliveryWarehouseId != null">
                AND s.delivery_warehouse_id = #{vo.deliveryWarehouseId}
            </if>
            <if test="vo.commodityWorkshopId != null">
                AND c.commodity_workshop_id= #{vo.commodityWorkshopId}
            </if>
            <if test="vo.commodityFlowshopId != null">
                AND c.commodity_flowshop_id= #{vo.commodityFlowshopId}
            </if>
            <if test="vo.commodityFactoryId != null">
                AND c.commodity_factory_id= #{vo.commodityFactoryId}
            </if>
            AND  o.mode_type = 1
            AND  o.order_status = 0
        </where>
    </sql>

    <!--<select id="queryList" resultType="com.pinshang.qingyun.orderreport.mapper.entry.ReplenishmentStatisticsEntry">
       <![CDATA[ SELECT
            item.commodity_id,
            c.commodity_name,
            c.commodity_spec,
            c.commodity_code,
            c.commodity_factory_id,
            c.commodity_workshop_id,
            c.commodity_flowshop_id,
            sum(item.commodity_num) AS totalCommodityNum
            ]]>
        FROM t_tj_order o
        INNER JOIN t_tj_order_list item ON o.id = item.order_id
        INNER JOIN t_commodity c ON item.commodity_id = c.id
        INNER JOIN t_store s ON o.store_id = s.id
        <include refid="getCondition"/>
        GROUP BY item.commodity_id
    </select>-->



    <select id="queryList" resultType="com.pinshang.qingyun.orderreport.mapper.entry.ReplenishmentStatisticsEntry">
        SELECT item.commodity_id,c.commodity_name,c.commodity_factory_name,c.commodity_workshop_name,c.commodity_flowshop_name,c.commodity_spec,c.commodity_code,c.commodity_factory_id,c.commodity_workshop_id,c.commodity_flowshop_id,sum(item.commodity_num) AS totalCommodityNum
        FROM
        ( select c.id,c.commodity_name,c.commodity_factory_name,c.commodity_workshop_name,c.commodity_flowshop_name,c.commodity_spec,c.commodity_code,c.commodity_factory_id,c.commodity_workshop_id,c.commodity_flowshop_id from t_commodity c
          <where>
              <if test="vo.commodityWorkshopId != null">
                  AND c.commodity_workshop_id= #{vo.commodityWorkshopId}
              </if>
              <if test="vo.commodityFlowshopId != null">
                  AND c.commodity_flowshop_id= #{vo.commodityFlowshopId}
              </if>
              <if test="vo.commodityFactoryId != null">
                  AND c.commodity_factory_id= #{vo.commodityFactoryId}
              </if>
          </where>
          limit 10000000000000
        )  c
        inner JOIN
        (
        SELECT item.commodity_id,item.commodity_num
        from t_tj_order o
        INNER JOIN t_tj_order_list item ON o.id = item.order_id
        INNER JOIN t_store s ON o.store_id = s.id
        WHERE o.mode_type = 1 AND o.order_status = 0
        <if test="vo.startDate !=null and vo.endDate != null">
            AND o.order_time BETWEEN #{vo.startDate} AND #{vo.endDate}
        </if>
        <if test="vo.deliveryManId != null">
            AND s.deliveryman_id = #{vo.deliveryManId}
        </if>
        <if test="vo.deliveryWarehouseId != null">
            AND s.delivery_warehouse_id = #{vo.deliveryWarehouseId}
        </if>
            AND item.comb_type IN (1,3)

         limit 10000000000000
        ) item ON item.commodity_id = c.id
        GROUP BY item.commodity_id
        ORDER BY c.commodity_factory_id,c.commodity_workshop_name,c.commodity_code
    </select>

    <!--<select id="queryLatestList" resultType="com.pinshang.qingyun.orderreport.mapper.entry.ReplenishmentStatisticsEntry">
        <![CDATA[ SELECT
            item.commodity_id,
            c.commodity_name,
            c.commodity_spec,
            c.commodity_code,
            c.commodity_factory_id,
            c.commodity_workshop_id,
            c.commodity_flowshop_id,
            sum(item.commodity_num) AS totalCommodityNum
            ]]>
        FROM t_tj_order_latest o
        INNER JOIN t_tj_order_list_latest item ON o.id = item.order_id
        INNER JOIN t_commodity c ON item.commodity_id = c.id
        INNER JOIN t_store s ON o.store_id = s.id
        <include refid="getCondition"/>
        GROUP BY item.commodity_id
    </select>-->


    <select id="queryLatestList" resultType="com.pinshang.qingyun.orderreport.mapper.entry.ReplenishmentStatisticsEntry">
        SELECT item.commodity_id,concat(c.commodity_name,'(',c.commodity_spec,')') commodityNameSpec,c.commodity_name,c.commodity_factory_name,c.commodity_workshop_name,c.commodity_flowshop_name,c.commodity_spec,c.commodity_code,c.commodity_factory_id,c.commodity_workshop_id,c.commodity_flowshop_id,sum(item.commodity_num) AS totalCommodityNum
        FROM
        ( select c.id,c.commodity_name,c.commodity_factory_name,c.commodity_workshop_name,c.commodity_flowshop_name,c.commodity_spec,c.commodity_code,c.commodity_factory_id,c.commodity_workshop_id,c.commodity_flowshop_id from t_commodity c
        <where>
            <if test="vo.commodityWorkshopId != null">
                AND c.commodity_workshop_id= #{vo.commodityWorkshopId}
            </if>
            <if test="vo.commodityFlowshopId != null">
                AND c.commodity_flowshop_id= #{vo.commodityFlowshopId}
            </if>
            <if test="vo.commodityFactoryId != null">
                AND c.commodity_factory_id= #{vo.commodityFactoryId}
            </if>
        </where>
        limit 10000000000000
        )  c
        inner JOIN
        (
        SELECT item.commodity_id,item.commodity_num
        from t_tj_order_latest o
        INNER JOIN t_tj_order_list_latest item ON o.id = item.order_id
        INNER JOIN t_store s ON o.store_id = s.id
        WHERE o.mode_type = 1 AND o.order_status = 0
        <if test="vo.startDate !=null and vo.endDate != null">
            AND o.order_time BETWEEN #{vo.startDate} AND #{vo.endDate}
        </if>
        <if test="vo.deliveryManId != null">
            AND s.deliveryman_id = #{vo.deliveryManId}
        </if>
        <if test="vo.deliveryWarehouseId != null">
            AND s.delivery_warehouse_id = #{vo.deliveryWarehouseId}
        </if>
        AND item.comb_type IN (1,3)

        limit 10000000000000
        ) item ON item.commodity_id = c.id
        GROUP BY item.commodity_id
        ORDER BY c.commodity_factory_id,c.commodity_workshop_name,c.commodity_code
    </select>

</mapper>
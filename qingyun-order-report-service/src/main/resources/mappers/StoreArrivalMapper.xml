<?xml version="1.0" encoding="UTF-8" ?>

<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.pinshang.qingyun.orderreport.mapper.StoreArrivalMapper">

	<!-- 查询 客户到货信息 列表 -->
    <select id="selectStoreArrivalInfoList" 
    	resultType="com.pinshang.qingyun.orderreport.dto.storearrival.StoreArrivalInfoODTO"
    	parameterType="com.pinshang.qingyun.orderreport.dto.storearrival.SelectStoreArrivalInfoPageIDTO">
    	SELECT
			sa.order_time,
			sa.line_code,
			sa.line_name,
			sa.deliveryman_name,
			sa.store_id,
			sa.store_type_id,
			sa.create_time AS arrivalTime,
			sa.arrival_pics,
			sa.arrival_remarks
		FROM t_tj_store_arrival sa
		<where>
		<if test="beginOrderTime != null and beginOrderTime != ''">
			AND sa.order_time >= #{beginOrderTime}
		</if>
		<if test="endOrderTime != null and endOrderTime != ''">
			<![CDATA[
			AND sa.order_time <= #{endOrderTime}
			 ]]>
		</if>
		<if test="lineId != null">
			AND sa.line_id = #{lineId}
		</if>
		<if test="storeId != null">
			AND sa.store_id = #{storeId}
		</if>
		<if test="deliverymanId != null">
			AND sa.deliveryman_id = #{deliverymanId}
		</if>
		<if test="storeTypeId != null">
			AND sa.store_type_id = #{storeTypeId}
		</if>
		</where>
		ORDER BY sa.id DESC
    </select>
    
</mapper>
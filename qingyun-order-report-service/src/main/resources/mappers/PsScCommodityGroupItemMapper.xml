<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pinshang.qingyun.orderreport.mapper.PsScCommodityGroupItemMapper">
  <resultMap id="BaseResultMap" type="com.pinshang.qingyun.orderreport.model.PsScCommodityGroupItem">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="commodity_group_id" jdbcType="BIGINT" property="commodityGroupId" />
    <result column="commodity_id" jdbcType="BIGINT" property="commodityId" />
  </resultMap>
  <sql id="Base_Column_List">
    id, commodity_group_id, commodity_id
  </sql>

    <!--通过商品id获取商品组id-->
  <select id="selectByCommodityGroupId" resultType="java.lang.Long">
    select
        commodity_group_id
    from t_ps_sc_commodity_group_item
    where commodity_id = #{commodity_id}
  </select>

  <select id="selectInExistenceByCommodityCodeList" resultType="com.pinshang.qingyun.orderreport.vo.ps.CommodityGroupCommodityVO">
    select
        distinct
        com.id AS commodityId,
        com.commodity_code
    from t_commodity com
    where com.commodity_code in
        <foreach collection="commodityCodeList" item="item" open="(" separator="," close=")">
          #{item}
        </foreach>
        and com.id not in (select commodity_id from t_ps_sc_commodity_group_item)
  </select>

    <select id="queryCommodityByGroupId" resultType="java.lang.Long">
        select
            commodity_id
        from t_ps_sc_commodity_group_item
        where commodity_group_id = #{groupId}
    </select>

</mapper>
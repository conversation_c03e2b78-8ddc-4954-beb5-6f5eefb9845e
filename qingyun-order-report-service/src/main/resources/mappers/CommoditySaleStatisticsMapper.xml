<?xml version="1.0" encoding="UTF-8" ?>

<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.pinshang.qingyun.orderreport.mapper.CommoditySaleStatisticsMapper">

    <insert id="batchInsert" parameterType="com.pinshang.qingyun.orderreport.model.CommoditySaleStatistics" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO t_tj_commodity_sale_statistics
        (order_time,company_id, commodity_id, total_quantity, total_amount)
        VALUES
        <foreach collection="list" separator="," item="item">
            (#{item.orderTime},#{item.companyId}, #{item.commodityId}, #{item.totalQuantity}, #{item.totalAmount})
        </foreach>
    </insert>

    <select id="queryCommoditySaleStatisticsByMonthData" resultType="com.pinshang.qingyun.orderreport.model.CommoditySaleStatistics">
        SELECT
            ss.commodity_id,
            ss.company_id,
            DATE_FORMAT(ss.order_time,'%Y-%m') AS orderTimeStr,
            SUM(ss.total_quantity) AS totalQuantity,
            SUM(ss.total_amount) AS totalAmount
        FROM
            t_tj_commodity_sale_statistics ss
        WHERE
            ss.order_time >= #{startTime} AND
            <![CDATA[ ss.order_time <= #{endTime} AND ]]>
            ss.commodity_id IN
            <foreach collection="list" item="item" index="index" open="(" separator="," close=")">
                #{item}
            </foreach>
        GROUP BY
            ss.company_id,ss.commodity_id
        ORDER BY
            ss.commodity_id ASC
    </select>



    <select id="queryCommoditySaleStatisticsAndItemLatestData" resultType="com.pinshang.qingyun.orderreport.mapper.entry.CommoditySaleStatisticsAndItemLatestEntry">
        SELECT  s.id AS refer_id,s.commodity_id,l.order_id,l.commodity_num,l.total_price
        FROM t_tj_order_list_latest l
        INNER JOIN t_tj_order_latest o ON l.order_id = o.id AND o.order_time = #{orderTime} AND o.order_status=0
        INNER JOIN t_tj_commodity_sale_statistics s ON l.commodity_id = s.commodity_id AND s.order_time = o.order_time
    </select>

    <select id="queryCommoditySaleData" resultType="com.pinshang.qingyun.orderreport.vo.CommoditySaleStatisticsRespVo">
        SELECT
            tc.commodity_factory_id AS factoryId,
            tc.commodity_factory_name AS factoryName,
            tc.commodity_flowshop_id AS flowshopId,
            tc.commodity_flowshop_name AS flowshopName,
            tc.commodity_workshop_id AS workshopId,
            tc.commodity_workshop_name AS workshopName,
            tc.commodity_first_kind_id AS cateId1,
            tc.commodity_first_kind_name AS cateName1,
            tc.commodity_second_kind_id AS cateId2,
            tc.commodity_second_kind_name AS cateName2,
            tc.commodity_third_kind_id AS cateId3,
            tc.commodity_third_kind_name AS cateName3,
            ss.commodity_id,
            tc.commodity_code,
            CONCAT(tc.commodity_name,'(',tc.commodity_spec,')') AS commodityName,
            tc.commodity_unit_name,
            SUM(item.total_quantity) AS totalQuantity,
            SUM(item.total_amount) AS totalAmount,
            ss.company_id,
            tc.tax_rate_id
        FROM
        <choose>
            <when test="vo.paramType != null and vo.paramType == 1">
                t_tj_commodity_sale_statistics ss
                INNER JOIN t_tj_commodity_sale_statistics_item item ON  ss.id = item.refer_id
            </when>
            <otherwise>
                t_tj_commodity_sale_statistics_month ss
                INNER JOIN t_tj_commodity_sale_statistics_month_item item ON ss.id = item.refer_id
            </otherwise>
        </choose>
        LEFT JOIN t_commodity tc ON ss.commodity_id = tc.id
        <where>
            <choose>
                <when test="vo.paramType != null and vo.paramType == 1">
                    <if test="null != vo.startDate and null != vo.endDate ">
                        <![CDATA[ AND ss.order_time >= #{vo.startDate} AND ss.order_time <= #{vo.endDate} ]]>
                    </if>
                </when>
                <otherwise>
                    <if test="null != vo.startDateStr and null != vo.endDateStr ">
                        <![CDATA[ AND ss.sale_month >= #{vo.startDateStr} AND ss.sale_month <= #{vo.endDateStr} ]]>
                    </if>
                </otherwise>
            </choose>
            <if test="null != vo.factoryId">
                AND tc.commodity_factory_id = #{vo.factoryId}
            </if>
            <if test="vo.factoryIds != null and vo.factoryIds.size > 0">
                AND tc.commodity_factory_id IN
                <foreach collection="vo.factoryIds" item="item" open="(" close=")" separator="," index="index">
                    #{item}
                </foreach>
            </if>
            <if test="null != vo.flowshopId">
                AND tc.commodity_flowshop_id = #{vo.flowshopId}
            </if>
            <if test="null != vo.workshopId">
                AND tc.commodity_workshop_id = #{vo.workshopId}
            </if>
            <if test="null != vo.cateId1">
                AND tc.commodity_first_kind_id = #{vo.cateId1}
            </if>
            <if test="null != vo.commodityId">
                AND ss.commodity_id = #{vo.commodityId}
            </if>
            <if test="vo.companyId !=null">
                AND ss.company_id = #{vo.companyId}
            </if>
            <if test="vo.filterBusinessType != null">
                <choose>
                    <when test="vo.filterBusinessType == 14">
                        AND item.store_type_id NOT IN(9131178242860605247)
                    </when>
                    <when test="vo.filterBusinessType == 15">
                        AND item.store_type_id NOT IN(9131178242860605246,9131178242860605245)
                    </when>
                </choose>
            </if>
        </where>
        <choose>
            <when test="vo.companyId !=null">
                GROUP BY ss.company_id,ss.commodity_id
            </when>
            <otherwise>
                GROUP BY ss.commodity_id
            </otherwise>
        </choose>
        ORDER BY tc.commodity_factory_id ASC, CONVERT(tc.commodity_workshop_name USING gbk) ASC, tc.commodity_code ASC
    </select>

    <select id="queryOrderCommodityStatistics" resultType="com.pinshang.qingyun.orderreport.mapper.entry.CommoditySaleStatisticsMonitorEntry">
        SELECT sum(ol.commodity_num) AS totalQuantity,sum(ol.total_price) AS totalAmount
        FROM t_tj_order oo
        INNER JOIN t_tj_order_list ol ON oo.id = ol.order_id
        <where>
            <if test="vo.commodityId != null">
                AND ol.commodity_id = #{vo.commodityId}
            </if>
            <if test="vo.orderTime != null and vo.orderTime != '' ">
                AND oo.order_time = date(#{vo.orderTime})
            </if>
            <if test="vo.startDate !=null and vo.startDate != '' and vo.endDate !=null and vo.endDate != ''  ">
                AND oo.order_time BETWEEN date(#{vo.startDate}) AND date(#{vo.endDate})
            </if>
              AND oo.order_status = 0
              AND ol.comb_type IN (1,3)
        </where>
    </select>

    <select id="queryCommodityStatisticsBase" resultType="com.pinshang.qingyun.orderreport.mapper.entry.CommoditySaleStatisticsMonitorEntry">
        SELECT sum(css.total_quantity) AS totalQuantity,sum(css.total_amount) AS totalAmount
        FROM ${tableName} css
        <where>
            <if test="vo.commodityId != null">
                AND css.commodity_id = #{vo.commodityId}
            </if>
            <if test="vo.orderTime != null and vo.orderTime != '' ">
                <if test="vo.paramType == 1">
                    AND css.order_time = date(#{vo.orderTime})
                </if>
            </if>
            <if test="vo.startDate !=null and vo.startDate != '' and vo.endDate !=null and vo.endDate != ''  ">
                AND css.order_time BETWEEN date(#{vo.startDate}) AND date(#{vo.endDate})
            </if>
        </where>
    </select>
    <select id="queryCommodityStatisticsFactory" resultType="com.pinshang.qingyun.orderreport.mapper.entry.CommoditySaleStatisticsMonitorEntry">
        SELECT sum(css.total_quantity) AS totalQuantity,sum(css.total_amount) AS totalAmount
        FROM t_tj_factory_delivery_statistics css
        <if test="vo.commodityId != null">
            INNER JOIN t_commodity c ON css.factory_id = c.commodity_factory_id
        </if>
        <where>
            <if test="vo.commodityId != null">
                AND c.id = #{vo.commodityId}
            </if>
            <if test="vo.orderTime != null and vo.orderTime != '' ">
                <if test="vo.paramType == 1">
                    AND css.order_time = date(#{vo.orderTime})
                </if>
            </if>
            <if test="vo.startDate !=null and vo.startDate != '' and vo.endDate !=null and vo.endDate != ''  ">
                AND css.order_time BETWEEN date(#{vo.startDate}) AND date(#{vo.endDate})
            </if>
        </where>
    </select>

</mapper>
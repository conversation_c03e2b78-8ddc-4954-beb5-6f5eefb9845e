<?xml version="1.0" encoding="UTF-8" ?>

<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.pinshang.qingyun.orderreport.mapper.ProductStatisticsListMapper">

    <sql id="getCondition">
        <where>
            <if test="vo.commodityId != null">
                AND item.commodity_id = #{vo.commodityId}
            </if>
            <if test="vo.orderTime != null">
                AND o.order_time = #{vo.orderTime}
            </if>
            <if test="vo.storeSettId != null">
                AND s.store_sett_id = #{vo.storeSettId}
            </if>
            <if test="vo.deliveryManId != null">
                AND s.deliveryman_id = #{vo.deliveryManId}
            </if>
            <if test="vo.deliveryWarehouseId != null">
                AND s.delivery_warehouse_id = #{vo.deliveryWarehouseId}
            </if>
            <if test="vo.lineGroupId != null">
                AND s.store_line_group_id = #{vo.lineGroupId}
            </if>
            <if test="vo.storeId != null">
                AND s.id = #{vo.storeId}
            </if>
            AND  o.order_status = 0
            <if test="vo.companyId !=null ">
              AND  o.company_id = #{vo.companyId}
            </if>
            AND item.comb_type IN (1,2)
        </where>
    </sql>

    <select id="queryList" resultType="com.pinshang.qingyun.orderreport.mapper.entry.ProductStatisticsListEntry">
        SELECT
            s.store_code,
            s.store_name,
            c.commodity_name,
            c.commodity_spec,
            item.commodity_price,
            sum(item.commodity_num) AS commodityNum,
            sum(item.total_price) AS totalPrice
        FROM t_tj_order o
        INNER JOIN t_tj_order_list item ON o.id = item.order_id
        INNER JOIN t_commodity c ON item.commodity_id = c.id
        INNER JOIN t_store s ON o.store_id = s.id
        <include refid="getCondition"/>
        GROUP BY s.id
    </select>

    <select id="queryLatestList" resultType="com.pinshang.qingyun.orderreport.mapper.entry.ProductStatisticsListEntry">
        SELECT
            s.store_code,
            s.store_name,
            c.commodity_name,
            c.commodity_spec,
            item.commodity_price,
            sum(item.commodity_num) AS commodityNum,
            sum(item.total_price) AS totalPrice
        FROM t_tj_order_latest o
        INNER JOIN t_tj_order_list_latest item ON o.id = item.order_id
        INNER JOIN t_commodity c ON item.commodity_id = c.id
        INNER JOIN t_store s ON o.store_id = s.id
        <include refid="getCondition"/>
        GROUP BY s.id
    </select>

    <select id="querySum" resultType="com.pinshang.qingyun.orderreport.mapper.entry.ProductStatisticsListSumEntry">
        SELECT
            sum(item.commodity_num) AS totalCommodityNum,
            sum(item.total_price) AS totalCommodityPrice
        FROM t_tj_order o
        INNER JOIN t_tj_order_list item ON o.id = item.order_id
        INNER JOIN t_store s ON o.store_id = s.id
        <include refid="getCondition"/>
    </select>

    <select id="queryLatestSum" resultType="com.pinshang.qingyun.orderreport.mapper.entry.ProductStatisticsListSumEntry">
        SELECT
            sum(item.commodity_num) AS totalCommodityNum,
            sum(item.total_price) AS totalCommodityPrice
        FROM t_tj_order_latest o
        INNER JOIN t_tj_order_list_latest item ON o.id = item.order_id
        INNER JOIN t_store s ON o.store_id = s.id
        <include refid="getCondition"/>
    </select>

</mapper>
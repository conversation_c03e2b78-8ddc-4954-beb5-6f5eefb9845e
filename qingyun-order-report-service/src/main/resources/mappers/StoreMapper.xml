<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.pinshang.qingyun.orderreport.mapper.StoreMapper">

    <select id="query4FreshStoreProductShipments" resultType="com.pinshang.qingyun.orderreport.model.Store">
        SELECT s.id, CASE  WHEN LENGTH(trim(s.store_short_name))>0 THEN s.store_short_name ELSE s.store_name END storeName
        FROM t_store s
        INNER JOIN
        <choose>
        <when test="vo.latestFlag != null and vo.latestFlag == true">
            t_tj_order_latest
        </when>
        <otherwise>
            t_tj_order
        </otherwise>
        </choose>
        o ON o.store_id = s.id
        INNER JOIN
        <choose>
        <when test="vo.latestFlag != null and vo.latestFlag == true">
            t_tj_order_list_latest
        </when>
        <otherwise>
            t_tj_order_list
        </otherwise>
        </choose>
        olg ON o.id=olg.order_id
        <if test="vo.factoryId != null">
            INNER JOIN t_commodity c ON olg.commodity_id=c.id
        </if>
        WHERE o.order_status = 0
        <if test="vo.lineGroupId != null">
            AND s.store_line_group_id = #{vo.lineGroupId}
        </if>
        <if test="vo.orderDate != null">
            AND o.order_time =  #{vo.orderDate}
        </if>
        <if test="vo.factoryId != null">
            AND c.commodity_factory_id = #{vo.factoryId}
        </if>
        <if test="vo.deliveryBatch != null">
            and o.delivery_batch = #{vo.deliveryBatch}
        </if>
        GROUP BY s.id
        ORDER BY s.carport_name ASC,s.store_code ASC
    </select>

    <select id="queryFreshProductShipmentsStore" resultType="com.pinshang.qingyun.orderreport.model.Store">
        SELECT s.id, CASE  WHEN LENGTH(trim(s.store_short_name))>0 THEN s.store_short_name ELSE s.store_name END storeName
        FROM t_store s
        <where>
        <if test="storeIds != null and storeIds.size() > 0">
            AND s.id IN
            <foreach collection="storeIds" separator="," item="item" open="(" close=")">
                #{item}
            </foreach>
        </if>
        </where>
        ORDER BY s.carport_name ASC,s.store_code ASC
    </select>

</mapper>
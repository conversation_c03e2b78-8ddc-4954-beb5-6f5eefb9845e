<?xml version="1.0" encoding="UTF-8" ?>

<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.pinshang.qingyun.orderreport.mapper.OrderItemMapper">

    <insert id="batchInsert" parameterType="com.pinshang.qingyun.orderreport.model.OrderItem">
        INSERT INTO t_tj_order_list(order_id, commodity_id, commodity_num, total_price, commodity_price, type, remark,comb_type,comb_commodity_id)
        VALUES
               <foreach collection="list" separator="," item="item">
                   (#{item.orderId}, #{item.commodityId}, #{item.commodityNum}, #{item.totalPrice}, #{item.commodityPrice}, #{item.type}, #{item.remark},#{item.combType},#{item.combCommodityId})
               </foreach>
    </insert>

    <select id="findGiftByOrderIdAndType" resultType="com.pinshang.qingyun.orderreport.mapper.entry.DeliveryOrderGiftEntry">
        SELECT
        i.id AS id,
        i.order_id AS orderId,
        i.commodity_id AS commodityId,
        i.commodity_num AS commodityNum,
        i.commodity_price AS commodityPrice,
        i.total_price AS totalPrice,
        i.type AS type,
        i.remark AS remark,
        CONCAT(c.commodity_unit_name,'|',c.commodity_name) AS commodityName,
        c.commodity_code AS commodityCode,
        c.commodity_spec AS commoditySpec
        FROM
        t_tj_order_list i
        LEFT JOIN t_commodity c ON i.commodity_id = c.id
        <where>
            AND i.order_id = #{orderId}
            AND i.type = #{type}
            ANd i.comb_type IN (1,2)
        </where>
    </select>
    <select id="selectOrderItemListCountByOrderCreateTime" resultType="java.lang.Long">
        SELECT
            count(1)
        FROM
            t_tj_order_list ol
            LEFT JOIN t_tj_order o ON o.id = ol.order_id
        <where>
            <if test="null != filterOrderTypeList and filterOrderTypeList.size > 0">
                AND o.order_type NOT IN
                <foreach collection="filterOrderTypeList" item="orderType" open="(" close=")" separator=",">
                    #{orderType}
                </foreach>
            </if>
            <if test="null != startTime and null != endTime">
                and o.create_time BETWEEN #{startTime} AND #{endTime}
            </if>
        </where>
    </select>
    <select id="selectOrderItemListByOrderCreateTime" resultType="com.pinshang.qingyun.orderreport.model.OrderItem">
        SELECT
            ol.*
        FROM
        t_tj_order_list ol
        LEFT JOIN t_tj_order o ON o.id = ol.order_id
        <where>
            <if test="null != filterOrderTypeList and filterOrderTypeList.size > 0">
                AND o.order_type NOT IN
                <foreach collection="filterOrderTypeList" item="orderType" open="(" close=")" separator=",">
                    #{orderType}
                </foreach>
            </if>
            <if test="null != startTime and null != endTime">
                and o.create_time BETWEEN #{startTime} AND #{endTime}
            </if>
        </where>
    </select>
    <select id="selectOrderItemListByOrderIdList" resultType="com.pinshang.qingyun.orderreport.model.OrderItem">
        SELECT
        ol.*
        FROM
        t_tj_order_list ol
        LEFT JOIN t_tj_order o ON o.id = ol.order_id
        <where>
            <if test="null != orderIdList and orderIdList.size > 0">
                AND o.id IN
                <foreach collection="orderIdList" item="orderId" open="(" close=")" separator=",">
                    #{orderId}
                </foreach>
            </if>
        </where>
        ORDER BY ol.id ASC
    </select>
    <select id="repeatHaving1" resultType="com.pinshang.qingyun.orderreport.model.OrderItem">
        select
            oll.*
        from t_tj_order o
        INNER JOIN t_tj_order_list oll on oll.order_id = o.id
        where o.create_time BETWEEN CONCAT(DATE_FORMAT(now(),'%Y-%m-%d'),' 00:00:00') and CONCAT(DATE_FORMAT(now(),'%Y-%m-%d'),' 23:59:59')
        AND oll.type != 2
        AND o.order_type NOT IN(14,15)
        GROUP BY oll.order_id,oll.commodity_id,oll.commodity_num,oll.total_price,oll.type,oll.comb_type
        HAVING count(oll.commodity_id)>1
    </select>
</mapper>
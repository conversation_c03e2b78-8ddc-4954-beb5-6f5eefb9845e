<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pinshang.qingyun.orderreport.mapper.PsScCommodityGroupOrderMapper">

    <select id="queryCommodityGroupOrderList"  resultType="com.pinshang.qingyun.orderreport.dto.CommodityGroupLineOrderODTO"
        parameterType="com.pinshang.qingyun.orderreport.dto.CommodityGroupLineOrderIDTO">
        SELECT
            tl.id AS lineId,
            tl.line_code,
            tl.line_name,
            tl.line_group_id,
            tl.line_group_name,
            tl.`status` AS lineStatus,
            tl.store_quantity,
            tl.commodity_group_id,
            tg.group_name AS commodityGroupName,
            tg.`status` AS commodityGroupStatus,
            tg.commodity_quantity,
            tu.employee_name AS deliverymanName
        FROM t_ps_sc_line tl
        INNER JOIN t_ps_sc_commodity_group tg ON tl.commodity_group_id = tg.id
        INNER JOIN t_employee_user tu ON tl.deliveryman_id = tu.employee_id
        <where>
            <if test="lineGroupId != null">
                AND tl.line_group_id = #{lineGroupId}
            </if>
            <if test="deliverymanId != null">
                AND tl.deliveryman_id = #{deliverymanId}
            </if>
        </where>
        ORDER BY  tl.`status` DESC,tg.`status` DESC,tl.store_quantity DESC,tg.commodity_quantity DESC, tl.update_time DESC
    </select>

    <select id="queryLineDetailInfo"  resultType="com.pinshang.qingyun.orderreport.dto.CommodityGroupLineTmpDetailDTO">
        SELECT
            tl.id AS lineId,
            tl.line_code,
            tl.line_name,
            tl.line_group_id,
            tl.line_group_name,
            tl.`status` AS lineStatus,
            tl.store_quantity,
            tl.commodity_group_id,
            tg.group_name AS commodityGroupName,
            tg.`status` AS commodityGroupStatus,
            tg.commodity_quantity,
            tu.employee_name AS deliverymanName,
            tl.license_plate,
            tu.employee_phone AS phone
        FROM t_ps_sc_line tl
        INNER JOIN t_ps_sc_commodity_group tg ON tl.commodity_group_id = tg.id
        INNER JOIN t_employee_user tu ON tl.deliveryman_id = tu.employee_id
       WHERE tl.id = #{lineId}
    </select>

    <select id="queryLineOrderItemList"  resultType="com.pinshang.qingyun.orderreport.dto.CommodityGroupLineTmpOrderItemDTO">
        SELECT
           o.id as orderId,o.store_id,IF(LENGTH(s.store_short_name)>0,s.store_short_name,s.store_name) AS storeName,
           ol.commodity_id,c.commodity_name,c.commodity_spec,ol.commodity_num AS expectQuantity
        FROM
            t_tj_order_latest o
        INNER JOIN t_tj_order_list_latest ol ON o.id = ol.order_id
        INNER JOIN t_store s ON o.store_id=s.id
        INNER JOIN t_commodity c ON ol.commodity_id=c.id
        INNER JOIN t_ps_sc_line_store ls ON o.store_id = ls.store_id AND ls.line_id = #{lineId}
        INNER JOIN t_ps_sc_line l ON ls.line_id = l.id
        INNER JOIN t_ps_sc_commodity_group_item gi ON c.id = gi.commodity_id AND gi.commodity_group_id = l.commodity_group_id
        WHERE o.order_time = #{orderTime}
        <if test="storeIdList != null and storeIdList.size() > 0">
            AND o.store_id IN
            <foreach collection="storeIdList" separator="," item="storeId" open="(" close=")">
                #{storeId}
            </foreach>
        </if>
        ORDER BY LENGTH(c.commodity_code) ASC, c.commodity_code ASC,o.id ASC
    </select>

    <select id="queryPrintStoreList"  resultType="java.lang.Long">
        SELECT
            DISTINCT o.store_id
        FROM
            t_tj_order_latest o
        INNER JOIN t_tj_order_list_latest ol ON o.id = ol.order_id
        INNER JOIN t_store s ON o.store_id=s.id
        INNER JOIN t_commodity c ON ol.commodity_id=c.id
        INNER JOIN t_ps_sc_line_store ls ON o.store_id = ls.store_id AND ls.line_id = #{lineId}
        INNER JOIN t_ps_sc_line l ON ls.line_id = l.id
        INNER JOIN t_ps_sc_commodity_group_item gi ON ol.commodity_id = gi.commodity_id AND gi.commodity_group_id = l.commodity_group_id
        WHERE o.order_time = #{orderTime}
    </select>

</mapper>
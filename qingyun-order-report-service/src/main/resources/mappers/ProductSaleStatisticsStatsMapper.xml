<?xml version="1.0" encoding="UTF-8" ?>

<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.pinshang.qingyun.orderreport.mapper.ProductSaleStatisticsMapper">

    <sql id="getStoreCondition">
        <if test="vo.supervisorId != null">
            AND css.supervisor_id = #{vo.supervisorId}
        </if>
        <if test="vo.storeTypeId != null">
            AND css.store_type_id = #{vo.storeTypeId}
        </if>
    </sql>
    <sql id="getCommodityCondition">
        <if test="vo.commodityId != null">
            AND c.id = #{vo.commodityId}
        </if>
        <if test="vo.commodityIdList != null and vo.commodityIdList.size() > 0">
            AND c.id IN
            <foreach collection="vo.commodityIdList" separator="," item="item" open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="vo.factoryId != null">
            AND c.commodity_factory_id = #{vo.factoryId}
        </if>
        <if test="vo.factoryIds != null and vo.factoryIds.size > 0">
            AND c.commodity_factory_id IN
            <foreach collection="vo.factoryIds" item="item" open="(" close=")" separator="," index="index">
                #{item}
            </foreach>
        </if>
        <if test="vo.workshopId != null">
            AND c.commodity_workshop_id = #{vo.workshopId}
        </if>
        <if test="vo.cateId1 != null">
            AND c.commodity_first_kind_id = #{vo.cateId1}
        </if>
        <if test="vo.cateId2 != null">
            AND c.commodity_second_kind_id = #{vo.cateId2}
        </if>
        <if test="vo.cateId3 != null">
            AND c.commodity_third_kind_id = #{vo.cateId3}
        </if>
    </sql>

    <sql id="getSupervisorStatsJoin">
        (
        <trim suffixOverrides="UNION ALL">
            <foreach item="dateVo" collection="vo.dataTypeList">
                <if test="dateVo.dateType==1">
                    SELECT  ol.commodity_id,css.store_type_id,
                    SUM(ol.commodity_num) AS total_quantity, SUM(ol.total_price) AS total_amount,o.company_id,c.tax_rate_id
                    FROM t_tj_order_latest o
                    INNER JOIN t_tj_order_list_latest ol ON o.id = ol.order_id
                    INNER JOIN t_store css ON o.store_id = css.id <include refid="getStoreCondition"/>
                    INNER JOIN t_commodity c ON ol.commodity_id = c.id <include refid="getCommodityCondition"/>
                    WHERE o.order_time BETWEEN #{dateVo.startDate} AND #{dateVo.endDate} AND  o.order_status = 0
                    <if test="vo.companyId!=null">
                        AND o.company_id= #{vo.companyId}
                    </if>
                    AND ol.comb_type IN (1,3)
                    <choose>
                        <when test="vo.companyId!=null">
                            GROUP BY o.company_id, ol.commodity_id ,css.store_type_id
                        </when>
                        <otherwise>
                            GROUP BY ol.commodity_id ,css.store_type_id
                        </otherwise>
                    </choose>
                </if>
                <if test="dateVo.dateType==2">
                    SELECT css.commodity_id,css.store_type_id,css.total_quantity,css.total_amount,css.company_id,c.tax_rate_id
                    FROM t_tj_commodity_sale_statistics_supervisor css
                    INNER JOIN t_commodity c ON css.commodity_id = c.id <include refid="getCommodityCondition"/>
                    WHERE css.order_time BETWEEN #{dateVo.startDate} AND #{dateVo.endDate}
                    <include refid="getStoreCondition"/>
                    <if test="vo.companyId!=null">
                        AND css.company_id= #{vo.companyId}
                    </if>
                </if>
                <if test="dateVo.dateType==3">
                    SELECT css.commodity_id,css.store_type_id,css.total_quantity,css.total_amount,css.company_id,c.tax_rate_id
                    FROM t_tj_commodity_sale_statistics_month_supervisor css
                    INNER JOIN t_commodity c ON css.commodity_id = c.id <include refid="getCommodityCondition"/>
                    WHERE css.sale_month = #{dateVo.saleMonth}
                    <include refid="getStoreCondition"/>
                    <if test="vo.companyId!=null">
                        AND css.company_id= #{vo.companyId}
                    </if>
                </if>
                UNION ALL
            </foreach>
        </trim>
        )item
        INNER JOIN t_commodity c ON item.commodity_id = c.id <include refid="getCommodityCondition"/>
    </sql>

    <sql id="getCommodityStatsCondition">
        (
            <trim suffixOverrides="UNION ALL">
                <foreach item="dateVo" collection="vo.dataTypeList">
                    <if test="dateVo.dateType==1">
                        SELECT  ol.commodity_id,css.store_type_id,
                                SUM(ol.commodity_num) AS total_quantity, SUM(ol.total_price) AS total_amount,o.company_id,c.tax_rate_id
                        FROM t_tj_order_latest o
                        INNER JOIN t_tj_order_list_latest ol ON o.id = ol.order_id
                        INNER JOIN t_store css ON o.store_id=css.id <include refid="getStoreCondition"/>
                        INNER JOIN t_commodity c ON ol.commodity_id = c.id <include refid="getCommodityCondition"/>
                        WHERE o.order_time BETWEEN #{dateVo.startDate} AND #{dateVo.endDate} AND  o.order_status = 0
                        <if test="vo.companyId!=null">
                            AND o.company_id= #{vo.companyId}
                        </if>
                        AND ol.comb_type IN (1,3)
                        <choose>
                            <when test="vo.companyId!=null">
                                GROUP BY o.company_id, ol.commodity_id ,css.store_type_id
                            </when>
                            <otherwise>
                                GROUP BY ol.commodity_id ,css.store_type_id
                            </otherwise>
                        </choose>
                    </if>
                    <if test="dateVo.dateType==2">
                        SELECT  css.commodity_id,item.store_type_id,item.total_quantity,item.total_amount,css.company_id,c.tax_rate_id
                        FROM t_tj_commodity_sale_statistics css
                        INNER JOIN t_tj_commodity_sale_statistics_item item ON css.id = item.refer_id
                        INNER JOIN t_commodity c ON css.commodity_id = c.id <include refid="getCommodityCondition"/>
                        WHERE css.order_time BETWEEN #{dateVo.startDate} AND #{dateVo.endDate}
                        <if test="vo.storeTypeId != null">
                            AND item.store_type_id = #{vo.storeTypeId}
                        </if>
                        <if test="vo.companyId!=null">
                            AND css.company_id= #{vo.companyId}
                        </if>
                    </if>
                    <if test="dateVo.dateType==3">
                        SELECT  css.commodity_id,item.store_type_id,item.total_quantity,item.total_amount,css.company_id,c.tax_rate_id
                        FROM t_tj_commodity_sale_statistics_month css
                        INNER JOIN t_tj_commodity_sale_statistics_month_item item ON css.id = item.refer_id
                        INNER JOIN t_commodity c ON css.commodity_id = c.id <include refid="getCommodityCondition"/>
                        WHERE css.sale_month = #{dateVo.saleMonth}
                        <if test="vo.storeTypeId != null">
                            AND item.store_type_id = #{vo.storeTypeId}
                        </if>
                        <if test="vo.companyId!=null">
                            AND css.company_id= #{vo.companyId}
                        </if>
                    </if>
                    UNION ALL
                </foreach>
            </trim>
        )item
        INNER JOIN t_commodity c ON item.commodity_id = c.id <include refid="getCommodityCondition"/>
    </sql>

    <select id="queryListBySupervisorStatistics" resultType="com.pinshang.qingyun.orderreport.mapper.entry.ProductSaleStatisticsTempEntry">
        SELECT
            c.commodity_factory_name AS factoryName,
            c.commodity_workshop_name AS workshopName,
            c.commodity_code AS commodityCode,
            CONCAT(c.commodity_name,'(',c.commodity_spec,')') AS commodityName,
            SUM(item.total_quantity) AS commodityNum,
            SUM(item.total_amount) AS totalPrice,
            item.store_type_id as storeTypeId,
            item.company_id,
            c.tax_rate_id,
            c.id AS commodityId
        FROM
        <include refid="getSupervisorStatsJoin"/>
        <choose>
            <when test="vo.companyId!=null">
                GROUP BY item.company_id, item.commodity_id,item.store_type_id
            </when>
            <otherwise>
                GROUP BY item.commodity_id,item.store_type_id
            </otherwise>
        </choose>
        ORDER BY c.commodity_factory_id ASC,CONVERT(c.commodity_workshop_name USING gbk) ASC,c.commodity_code asc
    </select>

    <select id="queryListByCommodityStatistics" resultType="com.pinshang.qingyun.orderreport.mapper.entry.ProductSaleStatisticsTempEntry">
        SELECT
            c.commodity_factory_name AS factoryName,
            c.commodity_workshop_name AS workshopName,
            c.commodity_code AS commodityCode,
            CONCAT(c.commodity_name,'(',c.commodity_spec,')') AS commodityName,
            SUM(item.total_quantity) AS commodityNum,
            SUM(item.total_amount) AS totalPrice,
            item.store_type_id as storeTypeId,
            item.company_id,
            c.tax_rate_id,
            c.id AS commodityId
        FROM <include refid="getCommodityStatsCondition"/>
        <choose>
            <when test="vo.companyId!=null">
                GROUP BY item.company_id,item.commodity_id,item.store_type_id
            </when>
            <otherwise>
                GROUP BY item.commodity_id,item.store_type_id
            </otherwise>
        </choose>
        ORDER BY c.commodity_factory_id ASC,CONVERT(c.commodity_workshop_name USING gbk) ASC,c.commodity_code asc
    </select>

    <select id="querySumBySupervisorStatistics" resultType="com.pinshang.qingyun.orderreport.mapper.entry.ProductSaleStatisticsSumEntry">
        SELECT
            sum(item.total_amount) AS totalCommodityPrice
        FROM
        <include refid="getSupervisorStatsJoin"/>
    </select>

    <select id="querySumByCommodityStatistics" resultType="com.pinshang.qingyun.orderreport.mapper.entry.ProductSaleStatisticsSumEntry">
        SELECT
            SUM(item.total_amount) AS totalCommodityPrice
        FROM <include refid="getCommodityStatsCondition"/>
    </select>

</mapper>
<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pinshang.qingyun.orderreport.mapper.PsScOperationLogMapper">


  <resultMap id="BaseResultMap" type="com.pinshang.qingyun.orderreport.model.PsScOperationLog">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="business_type" jdbcType="TINYINT" property="businessType" />
    <result column="business_name" jdbcType="VARCHAR" property="businessName" />
    <result column="operation_table" jdbcType="VARCHAR" property="operationTable" />
    <result column="web_code" jdbcType="VARCHAR" property="webCode" />
    <result column="web_name" jdbcType="VARCHAR" property="webName" />
    <result column="operation_column" jdbcType="VARCHAR" property="operationColumn" />
    <result column="operation_column_name" jdbcType="VARCHAR" property="operationColumnName" />
    <result column="log_type" jdbcType="TINYINT" property="logType" />
    <result column="operation_type_name" jdbcType="VARCHAR" property="operationTypeName" />
    <result column="operation_old" jdbcType="VARCHAR" property="operationOld" />
    <result column="operation_new" jdbcType="VARCHAR" property="operationNew" />
    <result column="create_id" jdbcType="BIGINT" property="createId" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
  </resultMap>

  <sql id="Base_Column_List">
    id, business_type, business_name, operation_table, web_code, web_name, operation_column,
    operation_column_name, log_type, operation_type_name, operation_old, operation_new, create_id,
    create_time
  </sql>

  <select id="queryOperationList" resultType="com.pinshang.qingyun.orderreport.vo.ps.PsScOperationLogVO">
    select
      log.business_name,
      log.web_code,
      log.web_name,
      log.operation_column_name,
      log.operation_type_name,
      log.operation_old,
      log.operation_new,
      log.create_time,
      em.employee_name as operator
    from t_ps_sc_operation_log log
    left join t_employee_user em on log.create_id = em.user_id
    where
        business_type = #{businessTypeId}
        and log.create_time between #{operationStartTime} and #{operationEndTime}
      <if test="operatorId != null ">
        and log.create_id = #{operatorId}
      </if>
      <if test="operationType != null ">
        and log.operation_type = #{operationType}
      </if>
      order by log.create_time desc, log.id desc
  </select>

</mapper>

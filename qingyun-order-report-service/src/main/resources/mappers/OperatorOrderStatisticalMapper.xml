<?xml version="1.0" encoding="UTF-8" ?>

<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.pinshang.qingyun.orderreport.mapper.OperatorOrderStatisticalMapper">

    <select id="queryList" resultType="com.pinshang.qingyun.orderreport.vo.OperatorOrderRespVo">
        SELECT
                SUM(work.item_quantity) AS categoryQuantity,
                SUM(work.order_quantity) AS orderQuantity,
               eu.employee_name AS operatorName
        FROM
            t_tj_workload work INNER JOIN t_employee_user eu ON work.operator_id=eu.user_id
        <where>
            <if test="orderDate != null">
                AND work.order_time= #{orderDate}
            </if>
            <if test="startDate != null and endDate != null">
                AND work.order_time BETWEEN #{startDate} AND #{endDate}
            </if>
            <if test="operatorId != null">
                AND work.operator_id = #{operatorId}
            </if>
        </where>
        GROUP BY work.operator_id
        ORDER BY work.operator_id
    </select>

    <insert id="insertOrUpdateOperatorOrder" parameterType="com.pinshang.qingyun.orderreport.model.Workload">
        INSERT INTO t_tj_workload (
                operator_id,
                order_time,
                order_quantity,
                item_quantity
                )
        VALUES
               (#{operatorId}, #{orderTime}, 1, #{itemQuantity}) ON DUPLICATE KEY UPDATE order_quantity = order_quantity + 1, item_quantity = item_quantity + #{itemQuantity}
    </insert>

    <update id="updateItemQuantity" parameterType="com.pinshang.qingyun.orderreport.model.Workload">
        UPDATE t_tj_workload SET item_quantity = item_quantity - #{itemQuantity},order_quantity = order_quantity - #{orderQuantity} WHERE order_time = #{orderTime} AND operator_id = #{operatorId}
    </update>

    <select id="queryOperatorStatisticsEntry" parameterType="com.pinshang.qingyun.orderreport.vo.OperatorOrderMonitorVo"
            resultType="com.pinshang.qingyun.orderreport.mapper.entry.OperatorStatisticsEntry">
        SELECT
            o.create_id AS operatorId,
            o.order_time AS orderTime,
            count(DISTINCT o.id) AS orderQuantity,
            count(ol.id) AS itemQuantity
        FROM
        <if test="latestFlag">
            t_tj_order_latest o
            INNER JOIN t_tj_order_list_latest ol ON o.id = ol.order_id
        </if>
        <if test="!latestFlag">
            t_tj_order o
            INNER JOIN t_tj_order_list ol ON o.id = ol.order_id
        </if>
        <where>
            <if test="orderTime != null">
                AND o.order_time= #{orderTime}
            </if>
            <if test="startTime != null and endTime != null">
                AND o.order_time BETWEEN #{startTime} AND #{endTime}
            </if>
            AND o.create_id = #{operatorId}
            AND o.order_type = 1
            AND o.order_status = 0
        </where>
        GROUP BY o.order_time
    </select>

    <insert id="batchInsert" parameterType="com.pinshang.qingyun.orderreport.mapper.entry.OperatorStatisticsEntry">
        INSERT INTO t_tj_workload( operator_id, order_time, order_quantity, item_quantity )
        VALUES
        <foreach collection="list" separator="," item="item">
            ( #{item.operatorId}, #{item.orderTime}, #{item.orderQuantity}, #{item.itemQuantity} )
        </foreach>
    </insert>

    <delete id="deleteOperatorStatistics" parameterType="com.pinshang.qingyun.orderreport.vo.OperatorOrderMonitorVo">
        DELETE FROM t_tj_workload
        WHERE operator_id = #{operatorId}
        <if test="orderTime != null">
            AND order_time= #{orderTime}
        </if>
        <if test="startTime != null and endTime != null">
            AND order_time BETWEEN #{startTime} AND #{endTime}
        </if>
    </delete>
</mapper>
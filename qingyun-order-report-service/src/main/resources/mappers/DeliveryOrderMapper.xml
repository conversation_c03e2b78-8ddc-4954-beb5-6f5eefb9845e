<?xml version="1.0" encoding="UTF-8" ?>

<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.pinshang.qingyun.orderreport.mapper.DeliveryOrderMapper">

    <select id="queryList" resultType="com.pinshang.qingyun.orderreport.mapper.entry.DeliveryOrderEntry">
        SELECT
        o.id AS id,
        o.order_code AS orderCode,
        o.final_amount AS finalAmount,
        o.order_time AS deliveryDate,
        s.store_name AS storeName,
        s.store_code AS storeCode,
        s.store_delivery_address AS deliveryAddress,
        s.store_sett_name AS settlementCustomer,
        s.store_type_name AS storeTypeName,
        s.receive_time receiveTime,
        om.deliveryman_name AS deliveryManName,
        om.supervisor_name AS supervisorName,
        om.office_director_name AS officeDirectorName,
        s.store_line_group_name AS lineGroupName,
         
		(CASE o.order_type 
    		WHEN 2 THEN s.store_name -- 清美生鲜App
        	WHEN 8 THEN s.store_name -- 清美鲜达App
        	WHEN 9 THEN s.store_name -- 清美批发App
        	ELSE us.employee_name END) AS operatorName,
        	
        s.team_leader_name AS teamLeaderName
        FROM
        <choose>
            <when test="vo.latestFlag != null and vo.latestFlag == true">
              t_tj_order_latest o
            </when>
            <otherwise>
              t_tj_order o
            </otherwise>
        </choose>
        LEFT JOIN t_store s ON s.id = o.store_id
        LEFT JOIN t_tj_order_mirror om ON o.id = om.order_id
        LEFT JOIN t_employee_user us ON us.user_id = o.create_id
        <where>
            o.order_status = 0
            <if test="vo.storeCode != null and vo.storeCode != ''">
                AND (s.store_code LIKE CONCAT('%',#{vo.storeCode},'%')
                OR s.store_name LIKE CONCAT('%',#{vo.storeCode},'%') )
            </if>
             <if test="vo.storeCodeList != null and vo.storeCodeList.size > 0">
	            AND s.store_code IN 
	            <foreach item="item" index="index" collection="vo.storeCodeList" open="(" separator="," close=")">
	                #{item}
	            </foreach>
             </if>
            <if test="vo.storeTypeId != null">
                AND s.store_type_id = #{vo.storeTypeId}
            </if>
            <!-- <if test="vo.settlementCustomerId != null">
                AND s.store_sett_id = #{vo.settlementCustomerId}
            </if> -->
            <if test="vo.settlementCustomerCode != null and vo.settlementCustomerCode !=''">
                AND (s.store_sett_code LIKE CONCAT('%',#{vo.settlementCustomerCode},'%')
                OR s.store_sett_name LIKE CONCAT('%',#{vo.settlementCustomerCode},'%') )
            </if>
            <if test="vo.receiveTime != null and vo.receiveTime !=''">
                AND s.receive_time LIKE CONCAT('%',#{vo.receiveTime},'%')
            </if>
            <if test="vo.deliveryManId !=null">
                AND om.deliveryman_id = #{vo.deliveryManId}
            </if>
            <if test="vo.lineGroupId != null">
                AND s.store_line_group_id = #{vo.lineGroupId}
            </if>
            <if test="vo.startOrderDate != null">
                <![CDATA[AND o.order_time >= #{vo.startOrderDate}]]>
            </if>
            <if test="vo.endOrderDate != null">
                <![CDATA[AND o.order_time <= #{vo.endOrderDate}]]>
            </if>
            <if test="vo.storeChannelId != null">
                AND s.store_channel_id = #{vo.storeChannelId}
            </if>
            <if test="vo.orderCode != null and vo.orderCode != ''">
                AND  o.order_code LIKE CONCAT('%',#{vo.orderCode},'%')
            </if>
            <if test="vo.supervisorId != null">
                AND om.supervisor_id = #{vo.supervisorId}
            </if>
            <if test="vo.officeDirectorId != null">
                AND om.office_director_id = #{vo.officeDirectorId}
            </if>
            <if test="vo.operatorId != null">
                AND o.create_id = #{vo.operatorId}
            </if>
            <if test="vo.deliveryBatch != null">
                AND o.delivery_batch = #{vo.deliveryBatch}
            </if>
             <if test="vo.commodityId != null">
             	AND EXISTS (
             		SELECT 1 FROM 
             		<choose>
		            <when test="vo.latestFlag != null and vo.latestFlag == true">
		          		t_tj_order_list_latest ol 
		            </when>
		            <otherwise>
		        		t_tj_order_list ol 
		            </otherwise>
		        	</choose>
					WHERE ol .order_id = o.id 
					AND ol .commodity_id = #{vo.commodityId}
					AND ol.comb_type IN (1,2)
                 )
            </if>
            <choose>
                <when test="vo.orderModeType ==0">
                    AND o.mode_type !=1
                </when>
                <when test="vo.orderModeType ==1">
                    AND o.mode_type = 1
                    AND o.order_type = 1
                </when>
            </choose>
            <if test="vo.storePhone!= null and vo.storePhone!= '' ">
                AND s.linkman_mobile = #{vo.storePhone}
            </if>
        </where>
        <if test="vo.sortRule != null">
            <if test="vo.sortRule == 1">
                ORDER BY o.order_time desc,o.create_time DESC
            </if>
            <if test="vo.sortRule == 2">
                order by s.store_sett_code asc,s.store_code asc,o.order_time asc
            </if>
        </if>

    </select>

    <select id="queryDetail" resultType="com.pinshang.qingyun.orderreport.mapper.entry.DeliveryOrderDetailEntry">
        SELECT
            o.id AS id,
            o.order_code AS orderCode,
            o.final_amount AS finalAmount,
            o.order_time AS deliveryDate,
            o.update_time AS updateTime,
            o.create_time AS createTime,
            o.order_remark AS orderRemark,
            s.store_name AS storeName,
            s.store_code AS storeCode,
            s.store_delivery_address AS deliveryAddress,
            s.receive_time receiveTime,
            s.linkman_mobile AS linkmanMobile,
            '' AS storeDestribe,
            om.deliveryman_name AS deliveryManName,
            om.supervisor_name AS supervisorName,
            om.salesman_name AS salesmanName,
          	(CASE o.order_type
	    		WHEN 2 THEN s.store_name -- 清美生鲜App
	        	WHEN 8 THEN s.store_name -- 清美鲜达App
	        	WHEN 9 THEN s.store_name -- 清美批发App
	        	ELSE us.employee_name END) AS operatorName,
        	
            s.store_district_name AS areaName,
            s.store_sett_name AS settlementCustomer,
            s.store_type_name AS storeTypeName,
            o.freight_amount,
            o.order_type,
            o.business_type,
            o.delivery_time_range
        FROM
            t_tj_order o
        LEFT JOIN t_store s ON s.id = o.store_id
        LEFT JOIN t_tj_order_mirror om ON o.id = om.order_id
        LEFT JOIN t_employee_user us ON us.user_id = o.create_id
        WHERE
	      o.id = #{id}
    </select>

    <select id="query4Print" resultType="com.pinshang.qingyun.orderreport.mapper.entry.DeliveryOrderPrintEntry">
        SELECT
            o.id AS id,
            o.store_id AS storeId,
            o.order_code AS orderNo,
            o.order_time AS orderTime,
            o.final_amount AS payMoney,
            o.freight_amount AS deliveryFees,
            o.print_num AS printNumber,
            o.order_remark AS remark,
            
            (CASE o.order_type
	    		WHEN 2 THEN s.store_name -- 清美生鲜App
	        	WHEN 8 THEN s.store_name -- 清美鲜达App
	        	WHEN 9 THEN s.store_name -- 清美批发App
	        	ELSE us.employee_name END) AS createName,
            
            s.store_code AS storeCode,
            s.store_name AS storeName,
            s.store_line_group_name AS lineGroupName,
            s.store_delivery_address AS shopAddress,
            s.linkman_mobile AS mobile,
            IFNULL(s.is_test_report, 1) AS testReport,
            s.show_price_retail AS showPrice,
            s.receive_time receiveTime,
            e.employee_code AS deliverymanCode,
            e.employee_phone AS deliverymanMobile,
            om.deliveryman_name AS deliverymanName,
            om.region_manager_name AS regionalManager,
            rs.employee_phone AS regionalManagerMobile,
            om.supervisor_name AS superintend,
            os.employee_phone AS superintendMobile
        FROM
            t_tj_order o
        LEFT JOIN t_store s ON s.id = o.store_id
        LEFT JOIN t_tj_order_mirror om ON o.id = om.order_id
        LEFT JOIN t_employee_user rs ON rs.employee_id = om.region_manager_id
        LEFT JOIN t_employee_user e ON e.employee_id = om.deliveryman_id
        LEFT JOIN t_employee_user os ON os.employee_id = om.supervisor_id
        LEFT JOIN t_employee_user us ON us.user_id = o.create_id
        WHERE
            o.id IN
            <foreach item="uid" index="index" collection="ids" open="(" separator="," close=")">
                #{uid}
            </foreach>
        ORDER BY FIELD(o.id,
        <foreach item="uid" index="index" collection="ids" separator=",">
            #{uid}
        </foreach>
        )
    </select>

    <select id="queryPrintItems" resultType="com.pinshang.qingyun.orderreport.mapper.entry.DeliveryOrderItemPrintEntry">
        SELECT
            olg.id,
            olg.commodity_num as number,
            olg.commodity_price as unitPrice,
            olg.total_price as money,
            olg.remark as productRemark,
            CONCAT(c.commodity_unit_name,'|',c.commodity_name,'(',c.commodity_spec,')') as productName,
            c.commodity_package_name as madeDate
        FROM
        t_tj_order_list olg
        LEFT JOIN t_commodity c ON c.id = olg.commodity_id
        WHERE  olg.order_id = #{orderId}
        AND olg.comb_type IN (1,2)
        order by olg.id
    </select>
</mapper>
<?xml version="1.0" encoding="UTF-8" ?>

<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.pinshang.qingyun.orderreport.mapper.FactoryDeliveryStatisticsItemMapper">

    <insert id="batchInsert" parameterType="com.pinshang.qingyun.orderreport.model.FactoryDeliveryStatisticsItem">
        INSERT INTO t_tj_factory_delivery_statistics_item
        (refer_id, store_type_id, total_quantity, total_amount)
        VALUES
        <foreach collection="list" separator="," item="item">
            (#{item.referId}, #{item.storeTypeId}, #{item.totalQuantity}, #{item.totalAmount})
        </foreach>
    </insert>

</mapper>
<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.pinshang.qingyun.orderreport.mapper.TJOrderMonitorMapper" >

    <sql id="getCondition">
        <where>
            <if test="list != null">
                AND o.order_code IN
                <foreach collection="list" item="orderCode" open="(" close=")" separator=",">
                    #{orderCode}
                </foreach>
            </if>
            <if test="vo.orderTime != null">
                AND o.order_time = date(#{vo.orderTime})
            </if>
            <if test="vo.startTime !=null and vo.endTime !=null ">
                AND o.order_time BETWEEN date(#{vo.startTime}) AND date(#{vo.endTime})
            </if>
        </where>
    </sql>
    <sql id="getConditionCreate">
        <where>
            <if test="null != vo">
                <if test="null != vo.filterOrderTypeList and vo.filterOrderTypeList.size > 0">
                    AND o.order_type NOT IN
                    <foreach collection="vo.filterOrderTypeList" item="orderType" open="(" close=")" separator=",">
                        #{orderType}
                    </foreach>
                </if>
                <if test="vo.orderTime != null">
                    AND o.order_time = date(#{vo.orderTime})
                </if>
                <if test="vo.startTime !=null and vo.endTime !=null ">
                    AND o.order_time BETWEEN date(#{vo.startTime}) AND date(#{vo.endTime})
                </if>
                <if test="vo.startCreateTime != null and vo.endCreateTime != null ">
                    AND o.create_time BETWEEN #{vo.startCreateTime} AND #{vo.endCreateTime}
                </if>
                <if test="null != vo.startUpdateTime and null != vo.endUpdateTime">
                    AND o.update_time BETWEEN #{vo.startUpdateTime} AND #{vo.endUpdateTime}
                </if>
            </if>
            <if test="list != null">
                AND o.order_code IN
                <foreach collection="list" item="orderCode" open="(" close=")" separator=",">
                    #{orderCode}
                </foreach>
            </if>
        </where>
    </sql>
    <!-- 查询订单统计信息，用于订单比较 -->
    <select id="queryOrderStatisticsInfo" resultType="com.pinshang.qingyun.orderreport.mapper.entry.OrderStatisticsEntry">
        SELECT
            COUNT(o.id) AS totalOrderCount,
            SUM(IF(o.order_status=0,1,0)) AS normalOrderCount,
            SUM(o.final_amount) AS totalOrderAmount
        FROM t_tj_order o
        <include refid="getCondition"/>
    </select>

    <!-- 查询订单统计信息，用于订单比较 -->
    <select id="queryOrderStatisticsInfoLatest" resultType="com.pinshang.qingyun.orderreport.mapper.entry.OrderStatisticsEntry">
        SELECT
            COUNT(o.id) AS totalOrderCount,
            SUM(IF(o.order_status=0,1,0)) AS normalOrderCount,
            SUM(o.final_amount) AS totalOrderAmount
        FROM t_tj_order_latest o
        <include refid="getCondition"/>
    </select>

    <!-- 查询订单编号，用于比较差异订单 -->
    <select id="queryOrderList" resultType="com.pinshang.qingyun.orderreport.model.Order">
        SELECT
            o.id,o.order_code,
            o.order_time,o.order_type,o.total_amount,o.final_amount,
            o.order_amount,o.freight_amount,
            o.logistics_center_id,o.business_type
        FROM
        <if test="latestFlag">
            t_tj_order_latest o
        </if>
        <if test="!latestFlag">
            t_tj_order o
        </if>
        <include refid="getConditionCreate"/>
    </select>

</mapper>
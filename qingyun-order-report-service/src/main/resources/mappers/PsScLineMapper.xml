<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pinshang.qingyun.orderreport.mapper.PsScLineMapper">
  <resultMap id="BaseResultMap" type="com.pinshang.qingyun.orderreport.model.PsScLine">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="line_group_id" jdbcType="BIGINT" property="lineGroupId" />
    <result column="line_group_name" jdbcType="VARCHAR" property="lineGroupName" />
    <result column="line_code" jdbcType="VARCHAR" property="lineCode" />
    <result column="line_name" jdbcType="VARCHAR" property="lineName" />
    <result column="commodity_group_id" jdbcType="BIGINT" property="commodityGroupId" />
    <result column="delivery_warehouse_id" jdbcType="BIGINT" property="deliveryWarehouseId" />
    <result column="delivery_warehouse_name" jdbcType="VARCHAR" property="deliveryWarehouseName" />
    <result column="deliveryman_id" jdbcType="BIGINT" property="deliverymanId" />
    <result column="carport_name" jdbcType="VARCHAR" property="carportName" />
    <result column="license_plate" jdbcType="VARCHAR" property="licensePlate" />
    <result column="store_quantity" jdbcType="BIGINT" property="storeQuantity" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="create_id" jdbcType="BIGINT" property="createId" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_id" jdbcType="BIGINT" property="updateId" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    id, line_group_id, line_group_name, line_code, line_name, commodity_group_id, delivery_warehouse_id,delivery_warehouse_name,
    deliveryman_id, carport_name, license_plate, store_quantity, status, create_id, create_time, update_id,
    update_time
  </sql>
  <select id="selectByGroupLineName" resultMap="BaseResultMap">
    select 
        <include refid="Base_Column_List" />
    from t_ps_sc_line
    where line_name = #{lineName}
  </select>

  <select id="mutexCommodityGroup2Store" resultType="com.pinshang.qingyun.orderreport.vo.ps.MutexCommodityGroupVO">
    select
        distinct
        line.commodity_group_id,
        com.group_name as commodityGroupName
    from t_ps_sc_line line
    left join
        (select
                line_id
        from t_ps_sc_line_store
        where line_id != #{lineId} and store_id in ( select store_id from t_ps_sc_line_store where line_id = #{lineId} )
        ) AS store
    on line.id = store.line_id
    left join t_ps_sc_commodity_group com on line.commodity_group_id = com.id
    where 1 = 1
      <if test="commodityGroupId != null and  commodityGroupId > 0 ">
          and line.commodity_group_id = #{commodityGroupId}
      </if>
  </select>

  <select id="queryCommodityGroupSelectList1" resultType="com.pinshang.qingyun.orderreport.vo.ps.PsScCommodityGroupSelectVO">
    select
        com.id as commodityGroupId,
        com.group_code as commodityGroupCode,
        com.group_name as commodityGroupName
    from t_ps_sc_commodity_group com
    where com.id not in
      ( select scline.commodity_group_id
      from t_ps_sc_line scline
      inner join
        (select
            distinct
            store1.line_id
        from t_ps_sc_line_store store1
        inner join t_ps_sc_line_store store2 on store1.store_id = store2.store_id
        where store2.line_id = #{lineId}
        ) as store on scline.id = store.line_id
--       group by scline.commodity_group_id having sum(scline.commodity_group_id) > 1
      )
    order by com.id;

  </select>

  <select id="queryCommodityGroupSelectList2" resultType="com.pinshang.qingyun.orderreport.vo.ps.PsScCommodityGroupSelectVO">
    select
        com.id as commodityGroupId,
        com.group_code as commodityGroupCode,
        com.group_name as commodityGroupName
    from t_ps_sc_commodity_group com
    where 1=1
      <if test="lineId != null and  lineId == -1 ">
          and com.status = 1
      </if>
    order by com.id desc
  </select>

  <select id="mutexStore2CommodityGroup" resultType="com.pinshang.qingyun.orderreport.vo.ps.MutexCommodityGroupStoreVO">
    select
      distinct
      store.id as storeId,
      store.store_code,
      ( case when inls.commodity_group_id is null then false else true end ) as flag
    from t_store store
    left join
    (select line.commodity_group_id, lstore.line_id, lstore.store_id
        from t_ps_sc_line_store lstore
        left join t_ps_sc_line line on lstore.line_id = line.id
        where lstore.store_id in
            <foreach collection="storeIdsList" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            and line.commodity_group_id = #{commodityGroupId}
    ) as inls on store.id = inls.store_id
    where store.id in
      <foreach collection="storeIdsList" item="item" open="(" separator="," close=")">
          #{item}
      </foreach>
  </select>

  <select id="queryCommodityGroupLineStoreList" resultType="com.pinshang.qingyun.orderreport.vo.ps.PsScLineStoreVO">
    select
      line.id,
      store.store_code,
      store.store_name,
      store.store_type_name,
      store.linkman_mobile,
      store.store_delivery_address
    from t_ps_sc_line_store line
    left join t_store store on line.store_id = store.id
    where line.line_id = #{lineId}
      <if test="storeId != null and  storeId > 0 ">
          and line.store_id = #{storeId}
      </if>
      <if test="storeTypeId != null and  storeTypeId > 0 ">
          and store.store_type_id = #{storeTypeId}
      </if>
      order by line.id desc
  </select>

  <select id="queryCommodityGroupLineAllList" resultType="com.pinshang.qingyun.orderreport.vo.ps.PsScLineInfoVO">
    select
      line.id AS lineId,
      line.line_code,
      line.line_name
    from t_ps_sc_line line
      order by line.id desc
  </select>

  <select id="queryCommodityGroupLineList" resultType="com.pinshang.qingyun.orderreport.vo.ps.PsScLineVO">
    SELECT
      line.id,
      line.line_group_id,
      line.line_group_name,
      line.line_code,
      line.line_name,
      line.commodity_group_id,
      com.group_name as commodityGroupName,
      line.delivery_warehouse_id,
      line.delivery_warehouse_name,
      em.employee_code as deliverymanCode,
      em.employee_name as deliverymanName,
      em.employee_phone as deliverymanPhone,
      line.carport_name,
      line.license_plate,
      line.store_quantity,
      line.status
    FROM t_ps_sc_line line
        LEFT JOIN t_ps_sc_commodity_group com ON line.commodity_group_id = com.id
        LEFT JOIN t_employee_user em ON line.deliveryman_id = em.employee_id
    WHERE 1 = 1
<!--      <if test="lineCodeOrName != null and  lineCodeOrName != '' ">
        AND  (  line.`line_name` LIKE CONCAT('%',#{lineCodeOrName},'%')
            OR  line.`line_code` = #{lineCodeOrName}
        )
      </if>-->
      <if test="lineId != null and  lineId > 0 ">
        AND line.`id` = #{lineId}
      </if>
      <if test="lineGroupId != null and  lineGroupId > 0 ">
          AND line.`line_group_id` = #{lineGroupId}
      </if>
      <if test="commodityGroupId != null and  commodityGroupId > 0 ">
          AND line.`commodity_group_id` = #{commodityGroupId}
      </if>
      <if test="status != null">
          AND line.`status` = #{status}
      </if>
      <if test="deliveryWarehouseId != null and  deliveryWarehouseId > 0 ">
          AND line.`delivery_warehouse_id` = #{deliveryWarehouseId}
      </if>
      <if test="deliverymanId != null and  deliverymanId > 0 ">
          AND line.`deliveryman_id` = #{deliverymanId}
      </if>
      <if test="licensePlate != null and  licensePlate != '' ">
          AND line.`license_plate` = #{licensePlate}
      </if>
      <if test="deliverymanPhone != null and  deliverymanPhone != '' ">
          AND em.`employee_phone` = #{deliverymanPhone}
      </if>
      ORDER BY line.create_time DESC
  </select>

  <update id="modCommodityGroupLine" >
		UPDATE t_ps_sc_line
      <set>
            `update_id` = #{updateId},
            `update_time` = NOW(),
          <if test="lineName != null and  lineName != '' ">
            `line_name` = #{lineName},
          </if>
          <if test="carportName != null and  carportName != '' ">
            `carport_name` = #{carportName},
          </if>
          <if test="licensePlate != null and  licensePlate != '' ">
            `license_plate` = #{licensePlate},
          </if>
          <if test="lineGroupId != null and  lineGroupId > 0 and lineGroupName != null ">
            `line_group_id` = #{lineGroupId},
            `line_group_name` = #{lineGroupName},
          </if>
          <if test="commodityGroupId != null and  commodityGroupId > 0 ">
            `commodity_group_id` = #{commodityGroupId},
          </if>
          <if test="status != null">
            `status` = #{status},
          </if>
          <if test="deliveryWarehouseId != null and  deliveryWarehouseId > 0 and deliveryWarehouseName != null ">
            `delivery_warehouse_id` = #{deliveryWarehouseId},
            `delivery_warehouse_name` = #{deliveryWarehouseName},
          </if>
          <if test="deliverymanId != null and  deliverymanId > 0 ">
            `deliveryman_id` = #{deliverymanId},
          </if>
      </set>
		WHERE
			`id` = #{id}
	</update>


</mapper>
<?xml version="1.0" encoding="UTF-8" ?>

<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.pinshang.qingyun.orderreport.mapper.OrderItemLatestMapper">

    <insert id="batchInsert" parameterType="com.pinshang.qingyun.orderreport.model.OrderItemLatest">
        INSERT INTO t_tj_order_list_latest(order_id, commodity_id, commodity_num, total_price, commodity_price, type, remark,comb_type,comb_commodity_id)
        VALUES
               <foreach collection="list" separator="," item="item">
                   (#{item.orderId}, #{item.commodityId}, #{item.commodityNum}, #{item.totalPrice}, #{item.commodityPrice}, #{item.type}, #{item.remark},#{item.combType},#{item.combCommodityId})
               </foreach>
    </insert>

    <delete id="deleteOrderLatestItemByOrderTime">
        DELETE ll FROM t_tj_order_list_latest ll
        INNER JOIN t_tj_order_latest o ON ll.order_id = o.id
        WHERE o.order_time = #{orderTime}
    </delete>

    <select id="selectOrderItemLatestListCountByOrderCreateTime" resultType="java.lang.Long">
        SELECT
            count(1)
        FROM
            t_tj_order_list_latest ol
            LEFT JOIN t_tj_order o ON o.id = ol.order_id
        <where>
            <if test="null != filterOrderTypeList and filterOrderTypeList.size > 0">
                AND o.order_type NOT IN
                <foreach collection="filterOrderTypeList" item="orderType" open="(" close=")" separator=",">
                    #{orderType}
                </foreach>
            </if>
            <if test="null != startTime and null != endTime">
                and o.create_time BETWEEN #{startTime} AND #{endTime}
            </if>
        </where>
    </select>

    <select id="selectOrderItemLatestListByOrderCreateTime" resultType="com.pinshang.qingyun.orderreport.model.OrderItemLatest">
        SELECT
            oll.*
        FROM
        t_tj_order_list_latest oll
        LEFT JOIN t_tj_order o ON o.id = oll.order_id
        <where>
            <if test="null != filterOrderTypeList and filterOrderTypeList.size > 0">
                AND o.order_type NOT IN
                <foreach collection="filterOrderTypeList" item="orderType" open="(" close=")" separator=",">
                    #{orderType}
                </foreach>
            </if>
            <if test="null != startTime and null != endTime">
                and o.create_time BETWEEN #{startTime} AND #{endTime}
            </if>
        </where>
        ORDER BY oll.id ASC
    </select>
    <select id="selectOrderItemLatestListByOrderIdList" resultType="com.pinshang.qingyun.orderreport.model.OrderItemLatest">
        SELECT
        oll.*
        FROM
        t_tj_order_list_latest oll
        LEFT JOIN t_tj_order o ON o.id = oll.order_id
        <where>
            <if test="null != orderIdList and orderIdList.size > 0">
                AND o.id IN
                <foreach collection="orderIdList" item="orderId" open="(" close=")" separator=",">
                    #{orderId}
                </foreach>
            </if>
        </where>
        ORDER BY oll.id ASC;
    </select>
    <delete id="deleteOrderLatestItemByOrderId">
        DELETE FROM t_tj_order_list_latest WHERE order_id IN
        <foreach collection="orderIdList" item="id" open="(" close=")" separator=",">
            #{id}
        </foreach>
    </delete>
</mapper>
<?xml version="1.0" encoding="UTF-8" ?>

<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.pinshang.qingyun.orderreport.mapper.OrderDetailMapper">

    <select id="queryList" resultType="com.pinshang.qingyun.orderreport.mapper.entry.OrderDetailEntry">
        SELECT
        o.order_time AS orderTime,
        o.order_code AS orderCode,
        s.store_code AS storeCode,
        s.store_name AS storeName,
        s.store_company_name as departmentName,
        s.office_director_name as officeDirectorName,
        s.region_manager_name as regionManagerName,
        s.supervisor_name as supervisorName,
        c.commodity_code AS commodityCode,
        c.commodity_name AS commodityName,
        c.commodity_first_kind_name AS cateName ,
        c.commodity_spec AS commoditySpec,
        ol.commodity_price AS commodityPrice,
        ol.commodity_num AS orderGoodsNum,
        ol.total_price AS commodityAmount ,
        ol.remark AS commodityRemark,
        
        (CASE o.order_type 
        	WHEN 2 THEN s.store_name -- 清美生鲜App
        	WHEN 8 THEN s.store_name -- 清美鲜达App
        	WHEN 9 THEN s.store_name -- 清美批发App
        	ELSE u.employee_name END) AS operator,
        
        o.create_time as orderCreateTime,
        o.update_time as orderUpdateTime,
        o.company_id
        FROM
        <choose>
            <when test="reqVo.latestFlag != null and reqVo.latestFlag == true">
                t_tj_order_latest o
            </when>
            <otherwise>
                t_tj_order o
            </otherwise>
        </choose>
        LEFT JOIN t_employee_user u ON u.user_id = o.update_id
        INNER JOIN
        <choose>
            <when test="reqVo.latestFlag != null and reqVo.latestFlag == true">
                t_tj_order_list_latest
            </when>
            <otherwise>
                t_tj_order_list
            </otherwise>
        </choose> ol ON o.id = ol.order_id
        INNER JOIN t_commodity c ON ol.commodity_id = c.id
        INNER JOIN t_store s ON o.store_id = s.id
        <include refid="queryCondition" />
        ORDER BY o.order_time DESC,s.store_code,c.commodity_code
    </select>

    <select id="queryTotalAmountAndNum" resultType="com.pinshang.qingyun.orderreport.mapper.entry.OrderDetailEntry">
        SELECT
            SUM(ol.commodity_num) AS orderGoodsNum,
            SUM(ol.total_price) AS commodityAmount
            FROM
        <choose>
            <when test="reqVo.latestFlag != null and reqVo.latestFlag == true">
                t_tj_order_latest o
            </when>
            <otherwise>
                t_tj_order o
            </otherwise>
        </choose>
        LEFT JOIN t_employee_user u ON u.user_id = o.update_id
        INNER JOIN
        <choose>
            <when test="reqVo.latestFlag != null and reqVo.latestFlag == true">
                t_tj_order_list_latest
            </when>
            <otherwise>
                t_tj_order_list
            </otherwise>
        </choose> ol ON o.id = ol.order_id
        INNER JOIN t_commodity c ON ol.commodity_id = c.id
        INNER JOIN t_store s ON o.store_id = s.id
        <include refid="queryCondition" />
    </select>
    <sql id="queryCondition">
        <where>
            o.order_status = 0
            <if test="reqVo.orderCode != null and reqVo.orderCode != ''">
                AND  o.order_code = #{reqVo.orderCode}
            </if>
            <if test="reqVo.commodityId != null ">
                AND  c.id = #{reqVo.commodityId}
            </if>
            <if test="reqVo.settlementIdList != null and reqVo.settlementIdList.size() > 0 ">
                AND  s.store_sett_id in
                <foreach collection="reqVo.settlementIdList" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="reqVo.storeCode != null and reqVo.storeCode != ''">
                AND  s.store_code = #{reqVo.storeCode}
            </if>
            <if test="reqVo.categoryId != null">
                AND (c.commodity_first_kind_id = #{reqVo.categoryId} OR c.commodity_second_kind_id = #{reqVo.categoryId}  OR c.commodity_third_kind_id = #{reqVo.categoryId})
            </if>
            <if test="reqVo.startOrderDate != null">
                <![CDATA[AND o.order_time >= #{reqVo.startOrderDate}]]>
            </if>
            <if test="reqVo.endOrderDate != null">
                <![CDATA[AND o.order_time <= #{reqVo.endOrderDate}]]>
            </if>
            <if test="reqVo.operatorId != null and reqVo.operatorId!= ''">
                AND u.user_id = #{reqVo.operatorId}
            </if>
            <if test="reqVo.storeTypeId != null">
                AND  s.store_type_id = #{reqVo.storeTypeId}
            </if>
            <if test="reqVo.departmentId != null">
                AND  s.store_company_id = #{reqVo.departmentId}
            </if>
            <if test="reqVo.storeChannelId != null">
                AND  s.store_channel_id = #{reqVo.storeChannelId}
            </if>
            <if test="reqVo.officeDirectorIdList != null and reqVo.officeDirectorIdList.size()> 0 ">
                AND  s.office_director_id in
                <foreach collection="reqVo.officeDirectorIdList" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="reqVo.supervisorIdList != null and reqVo.supervisorIdList.size()> 0 ">
                AND  s.supervisor_id in
                <foreach collection="reqVo.supervisorIdList" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="reqVo.regionManagerIdList != null and reqVo.regionManagerIdList.size()>0 ">
                AND  s.region_manager_id in
                <foreach collection="reqVo.regionManagerIdList" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="reqVo.companyId != null">
                AND o.company_id= #{reqVo.companyId}
            </if>
            <if test="reqVo.filterBusinessType != null">
                <choose>
                    <when test="reqVo.filterBusinessType == 14">
                        AND s.store_type_id NOT IN(9131178242860605247)
                    </when>
                    <when test="reqVo.filterBusinessType == 15">
                        AND s.store_type_id NOT IN(9131178242860605246,9131178242860605245)
                    </when>
                </choose>
            </if>
            AND ol.comb_type IN (1,2)
        </where>
    </sql>

    <select id="queryLateOrder" parameterType="com.pinshang.qingyun.orderreport.vo.LateOrderReqVo" resultType="com.pinshang.qingyun.orderreport.vo.LateOrderRespVo">
        SELECT
               o.order_create_time AS createTime,
               o.order_time AS orderTime,
               s.store_code,
               s.store_name,
               o.final_amount AS orderAmount,
               o.supervisor_name AS supervisionName,
               o.operator_name AS operatorName,
               o.order_code,
               o.line_group_name
               FROM
        t_tj_over_order_time o INNER JOIN t_store s ON o.store_id = s.id
        <where>
            <if test="orderStartDate!=null and orderEndDate!=null">
                AND o.order_time BETWEEN #{orderStartDate} and #{orderEndDate}
            </if>
            <if test="supervisionId != null">
                AND o.supervisor_id = #{supervisionId}
            </if>
        </where>
        ORDER BY o.supervisor_id ASC,
        o.order_create_time DESC
    </select>
</mapper>
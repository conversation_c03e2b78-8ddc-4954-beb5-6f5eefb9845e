<?xml version="1.0" encoding="UTF-8" ?>

<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.pinshang.qingyun.orderreport.mapper.CommoditySaleStatisticsMonthSupervisorMapper">
    <select id="generateCommoditySaleStatisticsMonthSupervisor" resultType="com.pinshang.qingyun.orderreport.model.CommoditySaleStatisticsMonthSupervisor">
        SELECT
        company_id,
        store_type_id,
        supervisor_id,
        commodity_id,
        DATE_FORMAT(order_time,'%Y-%m') AS saleMonth,
        SUM(total_quantity) AS totalQuantity,
        SUM(total_amount) AS totalAmount
        FROM
        t_tj_commodity_sale_statistics_supervisor
        <where>
            <![CDATA[ order_time >= #{startTime} AND order_time <= #{endTime} ]]>

            <if test="commodityId !=null">
                AND commodity_id =#{commodityId}
            </if>

        </where>
        GROUP BY
        company_id,commodity_id,store_type_id,supervisor_id
    </select>
</mapper>
<?xml version="1.0" encoding="UTF-8" ?>

<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.pinshang.qingyun.orderreport.mapper.CommoditySaleStatisticsMonthMapper">

    <insert id="batchInsert" parameterType="List" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO t_tj_commodity_sale_statistics_month
        (sale_month,company_id,commodity_id, total_quantity, total_amount)
        VALUES
        <foreach collection="list" item="item" index="index" separator=",">
            (#{item.saleMonth},#{item.companyId} ,#{item.commodityId}, #{item.totalQuantity}, #{item.totalAmount})
        </foreach>
    </insert>


    <select id="queryCommodityStatisticsBaseMonth" resultType="com.pinshang.qingyun.orderreport.mapper.entry.CommoditySaleStatisticsMonitorEntry">
        SELECT sum(csm.total_quantity) AS totalQuantity,sum(csm.total_amount) AS totalAmount
        FROM ${tableName} csm
        <where>
            <if test="vo.orderTime != null and vo.orderTime != '' ">
                AND csm.sale_month = #{vo.orderTime}
            </if>
            <if test="vo.commodityId != null">
                AND csm.commodity_id = #{vo.commodityId}
            </if>
        </where>
    </select>

</mapper>
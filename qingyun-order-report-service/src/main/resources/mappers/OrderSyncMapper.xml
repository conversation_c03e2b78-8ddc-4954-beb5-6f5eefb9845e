<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.pinshang.qingyun.orderreport.mapper.OrderSyncMapper">

    <!--查询当前日期之前的订单-->
    <select id="queryOrderSyncList" resultType="com.pinshang.qingyun.orderreport.model.OrderSync">
        SELECT o.id ,o.order_time as orderTime
        FROM t_tj_order_sync o
        WHERE o.order_time <![CDATA[ < ]]> #{orderTime}
    </select>

</mapper>
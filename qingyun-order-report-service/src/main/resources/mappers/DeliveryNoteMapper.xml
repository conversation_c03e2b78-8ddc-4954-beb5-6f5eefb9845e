<?xml version="1.0" encoding="UTF-8" ?>

<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.pinshang.qingyun.orderreport.mapper.DeliveryNoteMapper">

    <select id="selectDeliveryNoteOrderList" parameterType="com.pinshang.qingyun.orderreport.dto.DeliveryNoteIDTO"
            resultType="com.pinshang.qingyun.orderreport.mapper.entry.DeliveryNoteOrderEntry">
        SELECT
        s.id AS storeId,
        s.store_name AS storeName,
        s.store_code AS storeCode,
        s.store_line_name as lineName,
        s.store_delivery_address AS deliveryAddress,
        IFNULL(s.store_linkman,'') AS storeLinkman,
        s.linkman_mobile AS linkmanMobile,
        s.delivery_time AS deliveryTime
        FROM
        t_tj_order_latest o
        INNER JOIN t_store s ON o.store_id = s.id
        <where>
            <if test="null != orderTime and orderTime != '' ">
                AND o.order_time = #{orderTime} -- 时间
            </if>
            <if test="null != storeLineId">
                AND s.store_line_id = #{storeLineId}  -- 线路
            </if>
            <if test="null != storeName and storeName != '' ">
                AND s.store_name LIKE CONCAT('%',#{storeName},'%') -- 客户名称
            </if>
            <if test="null != deliveryAddress and deliveryAddress != '' ">
                AND s.store_delivery_address LIKE CONCAT('%',#{deliveryAddress},'%')
            </if>
            <if test="null != userId">
                AND  s.deliveryman_id = #{userId} -- 隐藏条件 送货员
            </if>
            AND o.order_status = 0
        </where>
        GROUP BY s.store_code
        ORDER BY s.store_line_name ASC,s.print_delivery_batch ASC,s.id ASC;
    </select>
    
    <!-- 查询  送货员-客户信息  列表 -->
    <select id="selectDeliverymanStoreInfoList" 
    	resultType="com.pinshang.qingyun.orderreport.dto.deliverynote.DeliverymanStoreInfoODTO"
    	parameterType="com.pinshang.qingyun.orderreport.dto.deliverynote.SelectDeliverymanStoreInfoListIDTO">
    	SELECT
			s.id AS storeId,
			s.store_code,
			s.store_name,
			s.store_type_name,
			s.store_line_name,
			s.store_linkman,
			s.linkman_mobile,
			s.store_delivery_address
		FROM t_store s 
		LEFT JOIN t_distribution_line dl ON dl.id = s.store_line_id
		<where>
		AND dl.line_status = 1 	-- 线路-启用
		AND s.store_status = 0	-- 客户-启用
		AND s.deliveryman_id = #{deliverymanId}
		<if test="null != storeLineId">
			AND s.store_line_id = #{storeLineId}
		</if>
		</where>
		ORDER BY s.store_line_name ASC, s.store_code
    </select>
    
    <!-- 查询  客户订单数量  列表 -->
    <select id="selectStoreOrderQuantityList" 
    	resultType="com.pinshang.qingyun.orderreport.dto.KeyValueODTO">
    	SELECT
			o.store_id AS keyLong,
			COUNT(o.id) AS valueInt
		FROM t_tj_order_latest o
		WHERE o.order_time = #{orderTime}
		AND o.store_id IN 
		<foreach collection="storeIdList" index="index" item="id" open="(" separator="," close=")">
			#{id}
		</foreach>
		GROUP BY o.store_id
    </select>
    
</mapper>
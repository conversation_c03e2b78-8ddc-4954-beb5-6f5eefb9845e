<?xml version="1.0" encoding="UTF-8" ?>

<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.pinshang.qingyun.orderreport.mapper.CommoditySaleStatisticsSupervisorMapper">
    <select id="generateCommoditySaleStatisticsSupervisor" resultType="com.pinshang.qingyun.orderreport.model.CommoditySaleStatisticsSupervisor">
        SELECT
            o.order_time,
            o.company_id,
            l.commodity_id,
            ss.supervisor_id,
            ss.store_type_id,
            sum(l.commodity_num) AS total_quantity,
            sum(l.total_price) AS total_amount
        FROM t_tj_order_latest o
        INNER JOIN t_tj_order_list_latest l ON l.order_id = o.id AND o.order_time =#{orderTime} AND o.order_status=0
        INNER JOIN t_store ss ON o.store_id = ss.id
        where l.comb_type IN (1,3)
        GROUP BY o.company_id,l.commodity_id,ss.store_type_id,ss.supervisor_id

    </select>


    <select id="generateCommoditySaleStatisticsSupervisorMakeup" resultType="com.pinshang.qingyun.orderreport.model.CommoditySaleStatisticsSupervisor">
        SELECT
            o.company_id,
            o.order_time,
            l.commodity_id,
            ss.supervisor_id,
            ss.store_type_id,
            sum(l.commodity_num) AS total_quantity,
            sum(l.total_price) AS total_amount
        FROM t_tj_order o
        INNER JOIN t_tj_order_list l ON l.order_id = o.id AND o.order_time =#{orderTime} AND o.order_status=0
        INNER JOIN t_store ss ON o.store_id = ss.id
        <where>
            <if test="commodityId != null">
                l.commodity_id =#{commodityId}
            </if>
            AND l.comb_type IN (1,3)
        </where>
        GROUP BY o.company_id,l.commodity_id,ss.store_type_id,ss.supervisor_id

    </select>

    <select id="queryCommoditySaleDataBySupervisor" resultType="com.pinshang.qingyun.orderreport.vo.CommoditySaleStatisticsRespVo">
        SELECT
            tc.commodity_factory_id AS factoryId,
            tc.commodity_factory_name AS factoryName,
            tc.commodity_flowshop_id AS flowshopId,
            tc.commodity_flowshop_name AS flowshopName,
            tc.commodity_workshop_id AS workshopId,
            tc.commodity_workshop_name AS workshopName,
            tc.commodity_first_kind_id AS cateId1,
            tc.commodity_first_kind_name AS cateName1,
            tc.commodity_second_kind_id AS cateId2,
            tc.commodity_second_kind_name AS cateName2,
            tc.commodity_third_kind_id AS cateId3,
            tc.commodity_third_kind_name AS cateName3,
            tc.commodity_code,
            su.commodity_id,
            CONCAT(tc.commodity_name,'(',tc.commodity_spec,')') AS commodityName,
            tc.commodity_unit_name,
            SUM(su.total_quantity) AS totalQuantity,
            SUM(su.total_amount) AS totalAmount,
            su.company_id,
            tc.tax_rate_id
        FROM
        <choose>
            <when test="vo.paramType != null and vo.paramType == 1">
                t_tj_commodity_sale_statistics_supervisor su
            </when>
            <otherwise>
                t_tj_commodity_sale_statistics_month_supervisor su
            </otherwise>
        </choose>

        LEFT JOIN t_commodity tc ON su.commodity_id = tc.id
        <where>
            <choose>
                <when test="vo.paramType != null and vo.paramType == 1">
                    <if test="null != vo.startDate and null != vo.endDate ">
                        <![CDATA[ AND su.order_time >= #{vo.startDate} AND su.order_time <= #{vo.endDate} ]]>
                    </if>
                </when>
                <otherwise>
                    <if test="null != vo.startDateStr and null != vo.endDateStr ">
                        <![CDATA[ AND su.sale_month >= #{vo.startDateStr} AND su.sale_month <= #{vo.endDateStr} ]]>
                    </if>
                </otherwise>
            </choose>
            <if test="null != vo.supervisorId">
                and su.supervisor_id = #{vo.supervisorId}
            </if>

            <if test="null != vo.factoryId">
                AND tc.commodity_factory_id = #{vo.factoryId}
            </if>
            <if test="vo.factoryIds != null and vo.factoryIds.size > 0">
                AND tc.commodity_factory_id IN
                <foreach collection="vo.factoryIds" item="item" open="(" close=")" separator="," index="index">
                    #{item}
                </foreach>
            </if>
            <if test="null != vo.flowshopId">
                AND tc.commodity_flowshop_id = #{vo.flowshopId}
            </if>
            <if test="null != vo.workshopId">
                AND tc.commodity_workshop_id = #{vo.workshopId}
            </if>
            <if test="null != vo.cateId1">
                AND tc.commodity_first_kind_id = #{vo.cateId1}
            </if>
            <if test="null != vo.commodityId">
                AND su.commodity_id = #{vo.commodityId}
            </if>

            <if test="null != vo.companyId">
                AND su.company_id = #{vo.companyId}
            </if>
        </where>
        <choose>
            <when test="vo.companyId !=null">
                GROUP BY su.company_id,su.commodity_id, su.supervisor_id
            </when>
            <otherwise>
                GROUP BY su.commodity_id, su.supervisor_id
            </otherwise>
        </choose>
        ORDER BY tc.commodity_factory_id ASC, CONVERT(tc.commodity_workshop_name USING gbk) ASC, tc.commodity_code ASC

    </select>
    
    <select id="queryCommoditySaleItemDataBySupervisor" resultType="com.pinshang.qingyun.orderreport.vo.CommoditySaleStatisticsItemRespVo">
        SELECT
            su.commodity_id,
            su.store_type_id,
            SUM(su.total_quantity) AS totalQuantity,
            SUM(su.total_amount) AS totalAmount,
            su.company_id
        FROM
        <choose>
            <when test="vo.paramType != null and vo.paramType == 1">
                t_tj_commodity_sale_statistics_supervisor su
            </when>
            <otherwise>
                t_tj_commodity_sale_statistics_month_supervisor su
            </otherwise>
        </choose>

        <where>
            <choose>
                <when test="vo.paramType != null and vo.paramType == 1">
                    <if test="null != vo.startDate and null != vo.endDate ">
                        <![CDATA[ AND su.order_time >= #{vo.startDate} AND su.order_time <= #{vo.endDate} ]]>
                    </if>
                </when>
                <otherwise>
                    <if test="null != vo.startDateStr and null != vo.endDateStr ">
                        <![CDATA[ AND su.sale_month >= #{vo.startDateStr} AND su.sale_month <= #{vo.endDateStr} ]]>
                    </if>
                </otherwise>
            </choose>
            <if test="null != vo.supervisorId">
                and su.supervisor_id = #{vo.supervisorId}
            </if>
            <if test="null != vo.companyId">
                AND su.company_id = #{vo.companyId}
            </if>

            and su.commodity_id IN
            <foreach collection="vo.commodityIds" item="item" index="index" open="(" separator="," close=")">
                #{item}
            </foreach>
        </where>
        <choose>
            <when test="null != vo.companyId">
                GROUP BY su.company_id,su.commodity_id, su.supervisor_id
            </when>
            <otherwise>
                GROUP BY su.commodity_id, su.supervisor_id
            </otherwise>
        </choose>
    </select>
</mapper>
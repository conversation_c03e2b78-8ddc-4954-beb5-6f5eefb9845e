<?xml version="1.0" encoding="UTF-8" ?>

<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.pinshang.qingyun.orderreport.mapper.DeliveryListMapper">

    <select id="searchLatestList" parameterType="com.pinshang.qingyun.orderreport.vo.DeliveryListReqVo" resultType="com.pinshang.qingyun.orderreport.vo.DeliveryListRespVo">
        SELECT
            s.store_line_id as lineId,
            s.store_line_name as lineName,
            s.deliveryman_id as deliveryManId,
            s.deliveryman_name as deliveryManName,
            s.team_leader_name as team<PERSON><PERSON>er<PERSON>ame,
           IFNULL(ps.print_status,0) as printStatus,
           #{orderModeType} as orderModeType
        FROM
            t_tj_order_latest o
            INNER JOIN t_store s ON o.store_id = s.id
            LEFT JOIN t_tj_print_status ps ON ps.line_id = s.store_line_id AND ps.order_time= o.order_time
        <where>
            <if test="orderDate != null">
                AND o.order_time = #{orderDate}
            </if>
            <if test="deliveryManId != null">
                AND s.deliveryman_id = #{deliveryManId}
            </if>
            <if test="lineGroupId != null">
                AND s.store_line_group_id = #{lineGroupId}
            </if>

            <if test=" deliveryTime!='' and deliveryTime != null">
                AND s.delivery_time = #{deliveryTime}
            </if>
            <if test=" teamLeaderId != null ">
                AND s.team_leader_id = #{teamLeaderId}
            </if>
            <if test="deliveryBatch != null">
                AND o.delivery_batch = #{deliveryBatch}
            </if>
            AND o.order_status = 0
            <choose>
                <when test="orderModeType!=null and orderModeType ==0">
                    AND o.mode_type !=1
                </when>
                <when test="orderModeType!=null and orderModeType ==1">
                    AND o.mode_type = 1
                    AND o.order_type = 1
                </when>
            </choose>
        </where>
        GROUP BY s.store_line_code
        ORDER BY s.team_leader_id
    </select>
    <select id="searchList" parameterType="com.pinshang.qingyun.orderreport.vo.DeliveryListReqVo" resultType="com.pinshang.qingyun.orderreport.vo.DeliveryListRespVo">
        SELECT
        ps.line_id as lineId,
        ps.line_name as lineName,
        ps.deliveryman_id as deliveryManId,
        ps.deliveryman_name as deliveryManName,
        ps.team_leader_name as teamLeaderName,
        ps.print_status as printStatus,
        #{orderModeType} as orderModeType
        FROM   t_tj_order_line_print_status ps
        <where>
            <if test="orderDate != null">
                AND ps.order_time = #{orderDate}
            </if>
            <if test="deliveryManId != null">
                AND ps.deliveryman_id = #{deliveryManId}
            </if>
            <if test="lineGroupId != null">
                AND ps.line_group_id = #{lineGroupId}
            </if>

            <if test="deliveryTime!='' and deliveryTime != null">
                AND ps.delivery_time = #{deliveryTime}
            </if>
            <if test=" teamLeaderId != null ">
                AND ps.team_leader_id = #{teamLeaderId}
            </if>
            <if test="deliveryBatch != null or (orderModeType!=null and orderModeType!=2)">
                and  exists
                (  select  1 from t_tj_order t inner join t_store s on  <if test="orderDate != null"> t.order_time=#{orderDate} and </if>  t.store_id = s.id
                   where    t.order_time= ps.order_time and s.store_line_id= ps.line_id
                   <if test="deliveryBatch != null">
                       AND t.delivery_batch=#{deliveryBatch}
                   </if>

                <choose>
                    <when test="orderModeType ==0">
                        AND t.mode_type !=1
                    </when>
                    <when test="orderModeType ==1">
                        AND t.mode_type = 1
                        AND t.order_type = 1
                    </when>
                </choose>
                )
            </if>

        </where>
        ORDER BY ps.team_leader_id
    </select>

    <select id="queryStoreNameByLineId" resultType="com.pinshang.qingyun.orderreport.vo.StoreVo">
        SELECT
        s.id AS id,
        s.store_name AS storeName,
        s.print_delivery_batch AS printDeliveryBatchId,
        o.id AS orderId,
        o.delivery_batch AS deliveryBatch
        FROM
        t_store s
        <if test="latestFlag">
            JOIN t_tj_order_latest o ON o.store_id = s.id
        </if>
        <if test="!latestFlag">
            JOIN t_tj_order o ON o.store_id = s.id
        </if>
        <where>
            <if test="deliveryBatch != null ">
                AND o.delivery_batch = #{deliveryBatch}
            </if>
            AND s.store_line_id = #{lineId}
            AND o.order_time = #{orderDate}
            AND o.order_status = 0
            <choose>
                <when test="orderModeType ==0">
                    AND o.mode_type !=1
                </when>
                <when test="orderModeType ==1">
                    AND o.mode_type = 1
                    AND o.order_type = 1
                </when>
            </choose>
        </where>
        GROUP BY s.id
        ORDER BY
        s.print_delivery_queue
    </select>

    <select id="searchPreviewTitle" parameterType="java.lang.Long" resultType="com.pinshang.qingyun.orderreport.vo.DeliveryListPreviewRespVo">
        SELECT
               s.store_line_id as id,
               s.store_line_code AS lineCode,
               s.store_line_name AS lineName,
               s.delivery_warehouse_name AS warehouse,
               s.store_line_group_name AS lineGroupName,
               s.deliveryman_id AS deliveryManId,
               s.deliveryman_name AS deliveryManName
        FROM
             t_store s
        WHERE
                s.store_line_id = #{lineId}
        LIMIT 1
    </select>

    <select id="queryStoreBuyGoodsByOrderDateAndLineGroupId" resultType="com.pinshang.qingyun.orderreport.vo.OrderListVo">
        SELECT
            o.order_code AS orderCode,
            ol.order_id AS orderId,
            ol.commodity_id AS goodsId,
            ol.commodity_num AS goodsNumber,
            c.commodity_name AS goodsName,
            c.commodity_spec AS specification,
            s.store_type_name  AS storeTypeName,
            s.id AS storeId
        FROM
        <if test="latestFlag">
            t_tj_order_list_latest ol
            JOIN t_tj_order_latest o ON ol.order_id = o.id
        </if>
        <if test="!latestFlag">
            t_tj_order_list ol
            JOIN t_tj_order o ON ol.order_id = o.id
        </if>
        JOIN t_commodity c ON c.id=ol.commodity_id
        JOIN t_store s ON o.store_id = s.id
        WHERE
        o.order_time = #{orderDate}
        AND s.store_line_id = #{lineId}
        <if test="printBatchId != null">
            AND s.print_delivery_batch = #{printBatchId}
        </if>
        <if test="deliveryBatch != null">
            AND o.delivery_batch = #{deliveryBatch}
        </if>
        AND o.order_status = 0
        AND ol.comb_type IN (1,3)
        <choose>
            <when test="orderModeType ==0">
                AND o.mode_type !=1
            </when>
            <when test="orderModeType ==1">
                AND o.mode_type = 1
                AND o.order_type = 1
            </when>
        </choose>
        GROUP BY ol.commodity_id
        ORDER BY c.is_summary,c.commodity_third_kind_id,c.commodity_code
    </select>

    <select id="findGoodsByStoreId" resultType="com.pinshang.qingyun.orderreport.vo.StoreBuyGoods">
        SELECT
        ol.commodity_id AS goodsId,
        ol.commodity_num AS goodsNumber
        FROM
        <if test="latestFlag">
            t_tj_order_latest o
            LEFT JOIN t_tj_order_list_latest ol ON o.id = ol.order_id
        </if>
        <if test="!latestFlag">
            t_tj_order o
            LEFT JOIN t_tj_order_list ol ON o.id = ol.order_id
        </if>
        WHERE
        o.store_id = #{storeId}
        <if test="deliveryBatch != null">
            AND o.delivery_batch = #{deliveryBatch}
        </if>
        AND o.order_time = #{orderDate}
        AND o.order_status = 0
        AND ol.comb_type IN (1,3)
        <choose>
            <when test="orderModeType!=null and orderModeType ==0">
                AND o.mode_type !=1
            </when>
            <when test="orderModeType!=null and orderModeType ==1">
                AND o.mode_type = 1
                AND o.order_type = 1
            </when>
        </choose>
    </select>

</mapper>
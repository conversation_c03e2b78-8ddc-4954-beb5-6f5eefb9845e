<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.pinshang.qingyun.orderreport.mapper.FreshStoreProductShipmentsMapper">

    <select id="queryList" resultType="com.pinshang.qingyun.orderreport.mapper.entry.FreshStoreProductShipmentsEntry">
        SELECT
        ol.commodity_id AS commodityId,
        concat(c.commodity_name,'(',c.commodity_spec,')') AS commodityName,
        c.commodity_code AS commodityCode,
        c.commodity_unit_name AS unit,
        o.store_id AS storeId,
        c.commodity_workshop_name AS workshopName,
        c.commodity_factory_name AS factoryName,
        c.commodity_flowshop_name AS flowshopName,
        SUM(ol.commodity_num) AS commodityTotal
        FROM
        <choose>
            <when test="vo.latestFlag != null and vo.latestFlag == true">
                t_tj_order_latest
            </when>
            <otherwise>
                t_tj_order
            </otherwise>
        </choose> o
        JOIN
        <choose>
            <when test="vo.latestFlag != null and vo.latestFlag == true">
                t_tj_order_list_latest
            </when>
            <otherwise>
                t_tj_order_list
            </otherwise>
        </choose> ol ON o.id = ol.order_id
        JOIN t_commodity c ON ol.commodity_id=c.id
        JOIN t_store s ON o.store_id=s.id
        <where>
            <if test="vo.orderDate != null">
                AND o.order_time = #{vo.orderDate}
            </if>
            <if test="vo.deliveryBatch != null">
                AND o.delivery_batch = #{vo.deliveryBatch}
            </if>
            <if test="vo.factoryId != null">
                AND c.commodity_factory_id = #{vo.factoryId}
            </if>
            <if test="vo.factoryIds != null and vo.factoryIds.size > 0">
                AND c.commodity_factory_id IN
                <foreach collection="vo.factoryIds" item="item" open="(" close=")" separator="," index="index">
                    #{item}
                </foreach>
            </if>
            <if test="vo.lineGroupId != null">
                AND s.store_line_group_id = #{vo.lineGroupId}
            </if>
            AND o.order_status = 0
            AND ol.comb_type IN (1,3)
        </where>
        GROUP BY o.store_id,ol.commodity_id
        ORDER BY c.commodity_factory_code,CONVERT(c.commodity_workshop_name USING gbk),c.commodity_code
    </select>

</mapper>
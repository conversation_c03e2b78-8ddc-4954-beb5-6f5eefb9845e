<?xml version="1.0" encoding="UTF-8" ?>

<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.pinshang.qingyun.orderreport.mapper.XdaStoreOrderAmountMapper">

    <select id="exportXdaStoreOrderAmount" resultType="com.pinshang.qingyun.orderreport.mapper.entry.XdaStoreOrderAmountEntry">
        SELECT
            t.store_code AS storeCode,
            t.store_name AS storeName,
            '鲜达' AS storeType,
            t.supervisor_name AS supervisorName, -- 督导
            t.office_director_name AS officeDirectorName, -- '主任'
            t.region_manager_name AS regionManagerName, -- '大区经理'
            t.store_company_name AS storeCompanyName, -- '部门',
            t.orderAmount AS orderAmount, -- '订单总金额',
            t.orderFilterAmount AS orderFilterAmount -- '豆面制品订单金额'
        FROM
            (
            SELECT
                s.id,
                s.store_code,
                s.store_name,
                s.supervisor_id,
                s.supervisor_name,
                s.office_director_id,
                s.office_director_name,
                s.region_manager_id,
                s.region_manager_name,
                s.store_company_id,
                s.store_company_name,
                SUM(l.total_price) AS orderAmount,
                SUM(
                    CASE
                    WHEN com.commodity_first_kind_id = 996135984224522756 THEN
                        IFNULL(l.total_price, 0)
                    WHEN com.commodity_first_kind_id = 996135984224522757 THEN
                        IFNULL(l.total_price, 0)
                    ELSE
                        0
                    END
                ) AS orderFilterAmount
            FROM
                t_store s -- INNER JOIN t_store_company c ON c.store_id = s.id
            INNER JOIN t_tj_order o ON o.store_id = s.id
            INNER JOIN t_tj_order_list l ON l.order_id = o.id
            INNER JOIN t_commodity com ON com.id = l.commodity_id
            WHERE
                o.order_time BETWEEN #{bTime} AND #{eTime}
            AND o.order_status = 0
            AND s.store_type_id = 9131078242860604325
            GROUP BY
                s.id
        ) AS t
    </select>
</mapper>
<?xml version="1.0" encoding="UTF-8" ?>

<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.pinshang.qingyun.orderreport.mapper.EmployeeUserMapper">

    <select id="queryEmployeeUser"  resultType="com.pinshang.qingyun.orderreport.model.EmployeeUser">
        SELECT user_id,employee_name
        FROM t_employee_user
        <where>
            <if test="userId != null">
                AND user_id = #{userId}
            </if>
        </where>
    </select>

    <select id="queryEmployeeUserByEmployeeId"  resultType="String">
        SELECT employee_name
        FROM t_employee_user
        WHERE employee_id = #{employeeId}
    </select>
</mapper>
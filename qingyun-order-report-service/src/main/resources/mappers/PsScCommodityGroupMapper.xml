<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pinshang.qingyun.orderreport.mapper.PsScCommodityGroupMapper">
  <resultMap id="BaseResultMap" type="com.pinshang.qingyun.orderreport.model.PsScCommodityGroup">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="group_name" jdbcType="VARCHAR" property="groupName" />
    <result column="group_code" jdbcType="VARCHAR" property="groupCode" />
    <result column="commodity_quantity" jdbcType="BIGINT" property="commodityQuantity" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="create_id" jdbcType="BIGINT" property="createId" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_id" jdbcType="BIGINT" property="updateId" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    id, group_name, group_code, commodity_quantity, `status`, create_id, create_time, 
    update_id, update_time
  </sql>
  <select id="queryCommodityGroupList"  resultType="com.pinshang.qingyun.orderreport.vo.ps.PsScCommodityGroupVO">
    SELECT
           com.id,
           com.group_name,
           com.group_code,
           com.commodity_quantity,
           com.status,
           com.create_id,
           em.employee_name AS createName,
           com.create_time
    FROM t_ps_sc_commodity_group com
        LEFT JOIN t_employee_user em ON com.create_id = em.user_id
    WHERE 1=1
        <if test="commodityGroupId != null">
          AND id = #{commodityGroupId}
        </if>
        <!--<if test="groupName != null and  groupName != '' ">
          AND  (  com.`group_name` LIKE CONCAT('%',#{groupName},'%')
              OR  com.`group_code` LIKE CONCAT('%',#{groupName},'%')
              )
        </if>-->
        <if test="status != null">
          AND com.status = #{status}
        </if>
        ORDER BY com.create_time DESC
  </select>

  <select id="queryCommodityGroupCommodityList"  resultType="com.pinshang.qingyun.orderreport.vo.ps.PsScCommodityGroupCommodityVO">
    SELECT
        item.id,
        item.commodity_group_id,
        item.commodity_id,
        com.commodity_code,
        com.commodity_name,
        com.commodity_spec,
        com.commodity_third_kind_name,
        com.commodity_unit_name,
        (CASE WHEN com.commodity_state = 1 THEN '可售' ELSE '不可售' END ) AS commodityState,
        com.commodity_package_name AS commodityPackageSpec
    FROM t_ps_sc_commodity_group_item item
        INNER JOIN t_commodity com ON item.commodity_id = com.id
    WHERE
            item.commodity_group_id = #{commodityGroupId}
        <if test="commodityId != null ">
          AND item.commodity_id = #{commodityId}
        </if>
        <if test="commodityKindId != null">
          AND ( com.`commodity_first_kind_id` = #{commodityKindId}
            OR  com.`commodity_second_kind_id` = #{commodityKindId}
            OR  com.`commodity_third_kind_id`= #{commodityKindId}
            )
        </if>
        ORDER BY item.commodity_id
  </select>

  <select id="queryCommodityGroupByLineId" resultType="com.pinshang.qingyun.orderreport.model.PsScCommodityGroup">
    select
     cg.id, cg.group_name, cg.group_code
    from t_ps_sc_line line
    inner join t_ps_sc_commodity_group cg on line.commodity_group_id = cg.id
    where line.id = #{lineId}
  </select>


  <select id="selectByGroupName" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from t_ps_sc_commodity_group
    where group_name = #{groupName}
  </select>

  <update id="updateGroupNameByPrimaryKey" >
    update t_ps_sc_commodity_group
    set
        group_name = #{groupName},
    where id = #{groupId}
  </update>

  <delete id="delByCommodityId" >
     delete from t_ps_sc_commodity_group_item
     where commodity_id = #{commodityId}
        and commodity_group_id = #{commodityGroupId}
  </delete>

</mapper>
<?xml version="1.0" encoding="UTF-8" ?>

<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.pinshang.qingyun.orderreport.mapper.CommoditySaleStatisticsItemMapper">

    <insert id="batchInsert" parameterType="com.pinshang.qingyun.orderreport.model.CommoditySaleStatisticsItem">
        INSERT INTO t_tj_commodity_sale_statistics_item
        (refer_id, store_type_id, total_quantity, total_amount)
        VALUES
        <foreach collection="list" separator="," item="item">
            (#{item.referId}, #{item.storeTypeId}, #{item.totalQuantity}, #{item.totalAmount})
        </foreach>
    </insert>

    <select id="queryCommoditySaleItemData" resultType="com.pinshang.qingyun.orderreport.vo.CommoditySaleStatisticsItemRespVo">
        SELECT
        si.store_type_id,SUM(si.total_quantity) AS totalQuantity,SUM(si.total_amount) AS totalAmount,ss.commodity_id,ss.company_id
        FROM
        <choose>
            <when test="vo.paramType != null and vo.paramType == 1">
                 t_tj_commodity_sale_statistics ss
                LEFT JOIN t_tj_commodity_sale_statistics_item si ON ss.id = si.refer_id
            </when>
            <otherwise>
                 t_tj_commodity_sale_statistics_month ss
                LEFT JOIN t_tj_commodity_sale_statistics_month_item si ON ss.id = si.refer_id
            </otherwise>
        </choose>
        <where>
            <choose>
                <when test="vo.paramType != null and vo.paramType == 1">
                    <if test="null != vo.startDate and null != vo.endDate ">
                        <![CDATA[ AND ss.order_time >= #{vo.startDate} AND ss.order_time <= #{vo.endDate} ]]>
                    </if>
                </when>
                <otherwise>
                    <if test="null != vo.startDateStr and null != vo.endDateStr ">
                        <![CDATA[ AND ss.sale_month >= #{vo.startDateStr} AND ss.sale_month <= #{vo.endDateStr} ]]>
                    </if>
                </otherwise>
            </choose>
            <if test="vo.companyId !=null">
                AND ss.company_id = #{vo.companyId}
            </if>
            AND ss.commodity_id IN
            <foreach collection="vo.commodityIds" item="item" index="index" open="(" separator="," close=")">
                #{item}
            </foreach>
        </where>

        <choose>
            <when test="vo.companyId !=null">
                GROUP BY ss.company_id,ss.commodity_id,si.store_type_id
            </when>
            <otherwise>
                GROUP BY ss.commodity_id,si.store_type_id
            </otherwise>
        </choose>

    </select>

    <select id="queryCommoditySaleItemByMonthData" resultType="com.pinshang.qingyun.orderreport.vo.CommoditySaleStatisticsItemRespVo">
        SELECT
            si.store_type_id,SUM(si.total_quantity) AS totalQuantity,SUM(si.total_amount) AS totalAmount,ss.commodity_id,ss.company_id
        FROM
            t_tj_commodity_sale_statistics_item si
            INNER JOIN t_tj_commodity_sale_statistics ss ON si.refer_id = ss.id
        WHERE
            ss.order_time >= #{startTime} AND
            <![CDATA[ ss.order_time <= #{endTime} ]]> AND
            ss.commodity_id IN
            <foreach collection="list" item="item" index="index" open="(" separator="," close=")">
                #{item}
            </foreach>
        GROUP BY
        ss.company_id,ss.commodity_id,si.store_type_id
    </select>




    <select id="queryCommoditySaleItemForInsert" resultType="com.pinshang.qingyun.orderreport.model.CommoditySaleStatisticsItem">
        SELECT  s.id AS refer_id,ss.store_type_id,sum(l.commodity_num) AS total_quantity,sum(l.total_price) AS total_amount
        FROM t_tj_order_latest o
        INNER JOIN t_tj_order_list_latest l ON l.order_id = o.id AND o.order_time = #{orderTime} AND o.order_status=0
        INNER JOIN t_store ss ON o.store_id = ss.id
        INNER JOIN t_tj_commodity_sale_statistics s ON l.commodity_id = s.commodity_id AND s.order_time = o.order_time AND s.company_id=o.company_id
        WHERE l.comb_type IN (1,3)
        GROUP BY o.company_id,l.commodity_id,ss.store_type_id
    </select>
</mapper>
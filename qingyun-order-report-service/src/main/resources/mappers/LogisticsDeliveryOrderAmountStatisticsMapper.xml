<?xml version="1.0" encoding="UTF-8" ?>

<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.pinshang.qingyun.orderreport.mapper.LogisticsDeliveryOrderAmountStatisticsMapper">

    <!--LatestList-->
    <select id="queryLatestList" resultType="com.pinshang.qingyun.orderreport.mapper.entry.LogisticsDeliveryOrderAmountStatisticsTempEntry">
        SELECT
            o.order_time,
            s.store_line_group_id,
            s.store_line_group_name,
            s.store_type_id,
            s.store_type_name,
            SUM(o.order_amount) AS orderAmount
        FROM
            t_tj_order_latest o
        INNER JOIN t_store s ON o.store_id = s.id
        <where>
            AND o.order_status = 0
            <if test="null != orderTime and orderTime != '' ">
                and o.order_time =  #{orderTime}
            </if>
            <if test="null != storeLineGroupId">
                and s.store_line_group_id =  #{storeLineGroupId}
            </if>
        </where>
        GROUP BY
            s.store_type_id,
            s.store_line_group_id;
    </select>


    <!--List-->
    <select id="queryList" resultType="com.pinshang.qingyun.orderreport.mapper.entry.LogisticsDeliveryOrderAmountStatisticsTempEntry">
        SELECT
            o.order_time,
            s.store_line_group_id,
            s.store_line_group_name,
            s.store_type_id,
            s.store_type_name,
            SUM(o.order_amount) AS orderAmount
        FROM
            t_tj_order o
            INNER JOIN t_store s ON o.store_id = s.id
        <where>
            AND o.order_status = 0
            <if test="null != orderTime and orderTime != '' ">
                and o.order_time =  #{orderTime}
            </if>
            <if test="null != storeLineGroupId">
                and s.store_line_group_id =  #{storeLineGroupId}
            </if>
        </where>
        GROUP BY
            s.store_type_id,
            s.store_line_group_id;
    </select>
</mapper>
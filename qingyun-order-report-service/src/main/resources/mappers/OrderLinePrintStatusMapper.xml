<?xml version="1.0" encoding="UTF-8" ?>

<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.pinshang.qingyun.orderreport.mapper.OrderLinePrintStatusMapper">

    <insert id="batchInsert" parameterType="com.pinshang.qingyun.orderreport.model.OrderLinePrintStatus">
        INSERT INTO t_tj_order_line_print_status(
            order_time, line_id, line_code,line_name,print_status,
            line_group_id, line_group_name, delivery_time_id,delivery_time,
            team_leader_id,team_leader_name,deliveryman_id,deliveryman_name
        )
        VALUES
        <foreach collection="list" separator="," item="item">
            ( #{item.orderTime}, #{item.lineId}, #{item.lineCode}, #{item.lineName}, #{item.printStatus},
              #{item.lineGroupId}, #{item.lineGroupName}, #{item.deliveryTimeId},#{item.deliveryTime},
              #{item.teamLeaderId}, #{item.teamLeaderName}, #{item.deliverymanId}, #{item.deliverymanName}
            )
        </foreach>
    </insert>

    <select id="queryOrderPrintStatus" resultType="com.pinshang.qingyun.orderreport.model.OrderLinePrintStatus">
        SELECT
            o.order_time AS orderTime,
            s.store_line_id AS lineId,
            s.store_line_code AS lineCode,
            s.store_line_name AS lineName,
            IFNULL(ps.print_status, 0) AS printStatus,
            s.store_line_group_id AS lineGroupId,
            s.store_line_group_name AS lineGroupName,
            s.delivery_time_id,
            s.delivery_time,
            s.team_leader_id,
            s.team_leader_name,
            s.deliveryman_id,
            s.deliveryman_name
        FROM
            t_tj_order_latest o
        INNER JOIN t_store s ON o.store_id = s.id
        LEFT JOIN t_tj_print_status ps ON o.order_time = ps.order_time AND s.store_line_id = ps.line_id
        LEFT JOIN t_tj_order_line_print_status ops ON o.order_time = ops.order_time AND s.store_line_id = ops.line_id
        WHERE o.order_time = #{orderTime}
        AND o.order_status = 0
        AND ops.id is NULL
        GROUP BY s.store_line_id,o.order_time
    </select>

</mapper>
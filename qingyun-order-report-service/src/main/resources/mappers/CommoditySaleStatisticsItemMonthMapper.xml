<?xml version="1.0" encoding="UTF-8" ?>

<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.pinshang.qingyun.orderreport.mapper.CommoditySaleStatisticsMonthItemMapper">

    <insert id="batchInsert" parameterType="List" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO t_tj_commodity_sale_statistics_month_item
            (refer_id, store_type_id, total_quantity, total_amount)
        VALUES
        <foreach collection="list" item="item" index="index" separator=",">
            (#{item.referId}, #{item.storeTypeId}, #{item.totalQuantity}, #{item.totalAmount})
        </foreach>
    </insert>

</mapper>
<?xml version="1.0" encoding="UTF-8" ?>

<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.pinshang.qingyun.orderreport.mapper.WorkshopShipmentsMapper">
    <select id="searchListByParams" resultType="com.pinshang.qingyun.orderreport.vo.WorkshopVo" parameterType="java.util.Map">
        select distinct 
				c.commodity_workshop_id AS id,
				c.commodity_workshop_name AS workshopName
		from t_commodity c
		where exists(
			select 1 from (
			select
			distinct commodity_id
			from 
				<if test="!latestFlag">
					t_tj_order o
					inner JOIN t_tj_order_list ol ON o.id = ol.order_id
				</if>
				<if test="latestFlag">
					t_tj_order_latest o
					inner JOIN t_tj_order_list_latest ol ON o.id = ol.order_id
				</if>
				<if test="lineGroupId != null or warehouseId != null or deliveryTime != null or deliveryTime != ''">
				inner join t_store s ON o.store_id = s.id
				</if>
				<where>
					<if test="orderDate != null">
						AND o.order_time = #{orderDate}
					</if>
					<if test="startDate != null">
						AND o.order_time BETWEEN #{startDate} AND #{endDate}
					</if>
					<if test="deliveryBatch != null">
						AND o.delivery_batch = #{deliveryBatch}
					</if>
					AND o.order_status = 0
					
					<if test="lineGroupId != null">
						AND s.store_line_group_id = #{lineGroupId}
					</if>
					<if test="warehouseId != null">
						AND s.delivery_warehouse_id = #{warehouseId}
					</if>
					<if test="deliveryTime != null">
						AND s.delivery_time = #{deliveryTime}
					</if>
					<if test="storeTypeId != null and (lineGroupId != null or warehouseId != null or deliveryTime != null or deliveryTime != '')">
						AND s.store_type_id = #{storeTypeId}
					</if>
                    AND ol.comb_type IN (1,3)
                    <choose>
                        <when test="orderModeType!=null and orderModeType ==0">
                            AND o.mode_type !=1
                        </when>
                        <when test="orderModeType!=null and orderModeType ==1">
                            AND o.mode_type = 1
                            AND o.order_type = 1
                        </when>
                    </choose>
                    AND s.store_type_id NOT IN(9131178242860605246,9131178242860605245)
				</where>
				) cid
				where cid.commodity_id=c.id
			) <!-- exists -->
			
			<if test="factoryId != null">
				AND c.commodity_factory_id = #{factoryId}
			</if>
			<if test="factoryIds != null and factoryIds.size > 0">
				AND c.commodity_factory_id IN
				<foreach collection="factoryIds" item="item" open="(" close=")" separator="," index="index">
					#{item}
				</foreach>
			</if>
			ORDER BY convert(c.commodity_workshop_name USING gbk)
    </select>

    <select id="queryOrdersByOrderDateAndWorkshopId" resultType="com.pinshang.qingyun.orderreport.vo.OrderListVo">
        SELECT
            o.order_code AS orderCode,
            ol.order_id AS orderId,
            ol.commodity_id AS goodsId,
            ol.commodity_num AS goodsNumber,
            c.commodity_name AS goodsName,
            c.commodity_spec AS specification,
            s.id AS storeId,
            s.store_line_name AS lineName,
            s.store_line_code AS linecode,
            s.store_line_group_id AS lineGroupId,
            s.store_line_group_name AS lineGroupName,
            s.carport_name AS carportName,
            c.commodity_unit_name AS unit,
            s.deliveryman_name AS deliveryManName
        FROM
        <if test="!latestFlag">
            t_tj_order_list ol
            JOIN t_tj_order o ON ol.order_id = o.id
        </if>
        <if test="latestFlag">
            t_tj_order_list_latest ol
            JOIN t_tj_order_latest o ON ol.order_id = o.id
        </if>
        JOIN t_commodity c ON c.id = ol.commodity_id
        JOIN t_store s ON o.store_id = s.id
        <where>
            <if test="orderDate != null">
                o.order_time = #{orderDate}
            </if>
            <if test="startDate != null and endDate != null">
                o.order_time BETWEEN #{startDate} AND #{endDate}
            </if>
            <if test="workshopId != null">
                AND c.commodity_workshop_id = #{workshopId}
            </if>
            <if test="lineGroupId != null">
                AND s.store_line_group_id = #{lineGroupId}
            </if>
            <if test="warehouseId != null">
                AND s.delivery_warehouse_id = #{warehouseId}
            </if>
            <if test="deliveryTime != null">
                AND s.delivery_time = #{deliveryTime}
            </if>
            <if test="storeTypeId != null">
                AND s.store_type_id = #{storeTypeId}
            </if>
            <if test="deliveryBatch != null">
                AND o.delivery_batch = #{deliveryBatch}
            </if>
            AND o.order_status = 0
            AND ol.comb_type IN (1,3)
            <choose>
                <when test="orderModeType!=null and orderModeType ==0">
                    AND o.mode_type !=1
                </when>
                <when test="orderModeType!=null and orderModeType ==1">
                    AND o.mode_type = 1
                    AND o.order_type = 1
                </when>
            </choose>
            AND s.store_type_id NOT IN(9131178242860605246,9131178242860605245)
        </where>
        ORDER BY c.commodity_code
    </select>

    <select id="queryListForApp" resultType="com.pinshang.qingyun.orderreport.mapper.entry.NonlocalShipmentsTempEntry">
        SELECT
            CONCAT(c.commodity_name,'(',c.commodity_spec,')') AS commodityName,
            c.commodity_code,
            c.commodity_unit_name,
            c.commodity_workshop_name AS workshopName,
            ol.commodity_num AS quantity,
            s.store_line_group_id AS lineGroupId,
            convert(s.store_line_group_name USING gbk) AS lineGroupName
        FROM
        <if test="!latestFlag">
            t_tj_order_list ol
            JOIN t_tj_order o ON ol.order_id = o.id
        </if>
        <if test="latestFlag">
            t_tj_order_list_latest ol
            JOIN t_tj_order_latest o ON ol.order_id = o.id
        </if>
        JOIN t_commodity c ON c.id = ol.commodity_id
        JOIN t_store s ON o.store_id = s.id
        <where>
            <if test="vo.orderDate != null">
                o.order_time = #{vo.orderDate}
            </if>
            <if test="vo.deliveryTime != null and vo.deliveryTime != ''">
                AND s.delivery_time = #{vo.deliveryTime}
            </if>
            <if test="vo.warehouseId != null">
                AND s.delivery_warehouse_id = #{vo.warehouseId}
            </if>
            <if test="vo.lineGroupId != null">
                AND s.store_line_group_id = #{vo.lineGroupId}
            </if>
            <if test="vo.deliveryBatch != null">
                AND o.delivery_batch = #{vo.deliveryBatch}
            </if>
            <if test="vo.directorCode != null and vo.directorCode != ''">
                AND c.commodity_workshop_director = #{vo.directorCode}
            </if>
            AND o.order_status = 0
            AND ol.comb_type IN (1,3)
            <choose>
                <when test="vo.orderModeType!=null and vo.orderModeType ==0">
                    AND o.mode_type !=1
                </when>
                <when test="vo.orderModeType!=null and vo.orderModeType ==1">
                    AND o.mode_type = 1
                    AND o.order_type = 1
                </when>
            </choose>
            AND s.store_type_id NOT IN(9131178242860605246,9131178242860605245)
        </where>
        ORDER BY c.commodity_code,convert(s.store_line_group_name USING gbk)
    </select>

    <select id="findShipmentsList" resultType="com.pinshang.qingyun.orderreport.mapper.entry.WorkshopShipmentsTempEntry">
        SELECT
            c.commodity_code,
            CONCAT(c.commodity_name,'(',c.commodity_spec,')') AS commodityName,
            c.commodity_unit_name AS commodityUnitName,
            c.commodity_workshop_name AS workshopName,
            SUM(ol.commodity_num) AS itemQuantity,
            s.carport_name AS carportName,
            s.deliveryman_name AS deliverymanName
        FROM
        <if test="!latestFlag">
            t_tj_order_list ol
            JOIN t_tj_order o ON ol.order_id = o.id
        </if>
        <if test="latestFlag">
            t_tj_order_list_latest ol
            JOIN t_tj_order_latest o ON ol.order_id = o.id
        </if>
        INNER JOIN t_commodity c ON ol.commodity_id = c.id
        INNER JOIN t_store s ON o.store_id= s.id
        <where>
            o.order_time = #{orderDate}
            <if test="lineGroupId != null and lineGroupId > 0">
                AND s.store_line_group_id = #{lineGroupId}
            </if>
            <if test="warehouseId != null and warehouseId >0">
                AND s.delivery_warehouse_id = #{warehouseId}
            </if>
            <if test="directorCode != null and directorCode != ''">
                AND c.commodity_workshop_director = #{directorCode}
            </if>
            <if test="deliveryTime !=null and deliveryTime!=''">
                AND s.delivery_time = #{deliveryTime}
            </if>
            <if test="deliveryBatch !=null">
                AND o.delivery_batch = #{deliveryBatch}
            </if>
            AND o.order_status=0
            AND ol.comb_type IN (1,3)
            <choose>
                <when test="orderModeType!=null and orderModeType ==0">
                    AND o.mode_type !=1
                </when>
                <when test="orderModeType!=null and orderModeType ==1">
                    AND o.mode_type = 1
                    AND o.order_type = 1
                </when>
            </choose>
            AND s.store_type_id NOT IN(9131178242860605246,9131178242860605245)
        </where>
        GROUP BY ol.commodity_id,s.store_line_id
        ORDER BY c.commodity_workshop_id,c.commodity_code,s.carport_name ASC
    </select>
</mapper>
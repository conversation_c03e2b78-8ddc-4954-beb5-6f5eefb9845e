<?xml version="1.0" encoding="UTF-8" ?>

<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.pinshang.qingyun.orderreport.mapper.OrderMapper">

    <insert id="batchInsert" parameterType="com.pinshang.qingyun.orderreport.model.Order">
        INSERT INTO t_tj_order
        ( id, company_id,order_code, store_id, order_time, mode_type,
          order_type, total_amount, final_amount,order_amount,freight_amount,
          order_status,delivery_batch,settle_status,change_price_status,print_num,
          print_type,create_time,create_id,update_time,update_id,
          logistics_center_id,business_type,delivery_time_range
        )
        VALUES
        <foreach collection="list" separator="," item="item">
            ( #{item.id},#{item.companyId}, #{item.orderCode}, #{item.storeId}, #{item.orderTime}, #{item.modeType},
              #{item.orderType}, #{item.totalAmount}, #{item.finalAmount},#{item.orderAmount},#{item.freightAmount},
              #{item.orderStatus}, #{item.deliveryBatch}, #{item.settleStatus}, #{item.changePriceStatus}, #{item.printNum},
              #{item.printType}, #{item.createTime}, #{item.createId}, #{item.updateTime}, #{item.updateId},
              #{item.logisticsCenterId},#{item.businessType},#{item.deliveryTimeRange}
            )
        </foreach>
    </insert>

    <select id="batchUpdate" resultType="int" parameterType="com.pinshang.qingyun.orderreport.model.Order">
        <foreach collection="orderList" item="item" index="index" separator=";" open="" close="">
            UPDATE t_tj_order
            SET
            logistics_center_id = #{item.logisticsCenterId},
            business_id = #{item.businessId}
            WHERE
            id = #{item.id}
        </foreach>
    </select>

    <delete id="batchDeleteOrder" parameterType="java.lang.String">
        UPDATE t_tj_order SET order_status = 2 WHERE id IN
        <foreach collection="list" item="i" separator="," open="(" close=")">
            #{i}
        </foreach>;
        UPDATE t_tj_order_latest SET order_status = 2 WHERE id IN
        <foreach collection="list" item="i" separator="," open="(" close=")">
            #{i}
        </foreach>
    </delete>

    <select id="queryCommoditySaleItemData" resultType="com.pinshang.qingyun.orderreport.vo.CommoditySaleStatisticsItemRespVo">
        SELECT
            s.store_type_id, ol.commodity_id, SUM(ol.commodity_num) AS totalQuantity, SUM(ol.total_price) AS totalAmount,o.company_id
        FROM
            t_tj_order o
            INNER JOIN t_store s ON s.id = o.store_id
            INNER JOIN t_tj_order_list ol ON ol.order_id = o.id
        WHERE
            o.order_time = #{orderTime} AND
            o.order_status = 0 AND
            ol.commodity_id IN
            <foreach collection="list" item="item" index="index" open="(" separator="," close=")">
                #{item}
            </foreach>
            AND ol.comb_type IN (1,3)
        GROUP BY
           o.company_id, ol.commodity_id,s.store_type_id
        order by null
    </select>

    <select id="queryStoreOrderStatistics" resultType="com.pinshang.qingyun.orderreport.mapper.entry.StoreOrderListEntry">
        SELECT s.id as storeId,s.store_code,s.store_name,sum(o.final_amount) AS orderAmount
        FROM t_tj_order o
        INNER JOIN t_tj_order_mirror m ON o.id = m.order_id
        INNER JOIN t_store s ON o.store_id = s.id
        WHERE o.order_time = #{vo.orderTime}
        AND o.order_status = 0
        AND m.supervisor_id = #{vo.supervisorId}
        GROUP BY s.id
        ORDER BY orderAmount DESC
    </select>

    <select id="queryStoreOrderSum" resultType="com.pinshang.qingyun.orderreport.mapper.entry.StoreOrderStatisticsEntry">
        SELECT
            count(DISTINCT o.store_id) as totalStoreQuantity,
            sum(o.final_amount) AS totalOrderAmount
        FROM t_tj_order o
        INNER JOIN t_tj_order_mirror m ON o.id = m.order_id
        WHERE o.order_time = #{vo.orderTime}
        AND o.order_status = 0
        AND m.supervisor_id = #{vo.supervisorId}
    </select>

    <!--根据订单日期统计商品数量-->
    <select id="selectOrderCountCommodityQuantityByOrderTime" resultType="com.pinshang.qingyun.orderreport.dto.CommodityQuantityODTO">
        SELECT
            ttol.commodity_id AS commodityId,
            SUM(ttol.commodity_num) AS commodityQuantity
        FROM
            t_tj_order tto
            LEFT JOIN t_tj_order_list ttol ON ttol.order_id = tto.id
        <where>
            <if test="null != orderTime and orderTime != ''">
               AND tto.order_time = #{orderTime}
            </if>
            <if test="null != commodityIdList and commodityIdList.size > 0 ">
                AND ttol.commodity_id IN
                <foreach collection="commodityIdList" item="item" index="index" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            GROUP BY ttol.commodity_id
        </where>
    </select>

    <!--根据订单日期统计商品数量-->
    <select id="selectOrderCountCommodityQuantityByOrderTimeV2" resultType="com.pinshang.qingyun.orderreport.dto.CommodityQuantityODTO">
        SELECT
        ttol.commodity_id AS commodityId,
        tto.order_time AS orderTime,
        SUM(ttol.commodity_num) AS commodityQuantity
        FROM
        t_tj_order tto
        LEFT JOIN t_tj_order_list ttol ON ttol.order_id = tto.id
        <where>
            <if test="null != orderTimeList and orderTimeList.size > 0 ">
                AND tto.order_time  IN
                <foreach collection="orderTimeList" item="orderTime" index="index" open="(" separator="," close=")">
                    #{orderTime}
                </foreach>
            </if>
            <if test="null != commodityIdList and commodityIdList.size > 0 ">
                AND ttol.commodity_id IN
                <foreach collection="commodityIdList" item="item" index="index" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            GROUP BY tto.order_time, ttol.commodity_id
        </where>
    </select>

</mapper>
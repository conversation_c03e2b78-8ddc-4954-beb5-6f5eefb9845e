<?xml version="1.0" encoding="UTF-8" ?>

<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.pinshang.qingyun.orderreport.mapper.ProductShipmentsMapper">
    <select id="selectShipmentsTableBody" resultType="com.pinshang.qingyun.orderreport.mapper.entry.ProductShipmentsEntry" parameterType="java.util.Map">
        SELECT
            ol.commodity_id AS commodityId,
            concat(c.commodity_name,'(',c.commodity_spec,')') AS commodityName,
            c.commodity_code AS commodityCode,
            c.bar_code as barCode,
            c.commodity_unit_name AS unit,
            o.store_id AS storeId,
            s.store_type_id AS storeTypeId,
            c.commodity_workshop_name AS workshopName,
            c.commodity_workshop_id AS workshopId,
            SUM(ol.commodity_num) AS storeTypeTotal
        FROM
        <if test="!latestFlag">
            t_tj_order_list ol
            JOIN t_tj_order o ON ol.order_id = o.id
        </if>
        <if test="latestFlag">
            t_tj_order_list_latest ol
            JOIN t_tj_order_latest o ON ol.order_id = o.id
        </if>
        JOIN t_store s ON o.store_id=s.id
        JOIN t_commodity c ON ol.commodity_id=c.id
        <where>
            <if test="orderDate != null">
                AND o.order_time = #{orderDate}
            </if>
            <if test="startDate != null">
                AND o.order_time BETWEEN #{startDate} AND #{endDate}
            </if>
            <if test="deliveryBatch != null">
                AND o.delivery_batch = #{deliveryBatch}
            </if>
            <if test="factoryId != null">
                AND c.commodity_factory_id = #{factoryId}
            </if>
            <if test="factoryIds != null and factoryIds.size > 0">
                AND c.commodity_factory_id IN
                <foreach collection="factoryIds" item="item" open="(" close=")" separator="," index="index">
                    #{item}
                </foreach>
            </if>
            <if test="lineGroupId != null">
                AND s.store_line_group_id = #{lineGroupId}
            </if>
            <if test="warehouseId != null">
                AND s.delivery_warehouse_id = #{warehouseId}
            </if>
            <if test="deliveryTime != null">
                AND s.delivery_time = #{deliveryTime}
            </if>
            AND o.order_status = 0
            AND ol.comb_type IN (1,3)
            <choose>
                <when test="orderModeType!=null and orderModeType ==0">
                    AND o.mode_type !=1
                </when>
                <when test="orderModeType!=null and orderModeType ==1">
                    AND o.mode_type = 1
                    AND o.order_type = 1
                </when>
            </choose>
        </where>
        GROUP BY s.store_type_id,ol.commodity_id
        ORDER BY c.commodity_factory_code asc,CONVERT(c.commodity_workshop_name USING gbk) asc,c.commodity_code asc
        <!--  ORDER BY c.commodity_workshop_id,c.id  -->
    </select>
</mapper>
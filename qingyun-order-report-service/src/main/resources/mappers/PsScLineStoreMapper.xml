<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pinshang.qingyun.orderreport.mapper.PsScLineStoreMapper">
  <resultMap id="BaseResultMap" type="com.pinshang.qingyun.orderreport.model.PsScLineStore">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="line_id" jdbcType="BIGINT" property="lineId" />
    <result column="store_id" jdbcType="BIGINT" property="storeId" />
  </resultMap>
  <sql id="Base_Column_List">
    id, line_id, store_id
  </sql>

  <select id="selectNotLinkByStoreCodeList" resultType="com.pinshang.qingyun.orderreport.vo.ps.CommodityGroupLineStoreVO">
    select
        distinct
        store.id AS storeId,
        store.store_code
    from t_store store
    where
        store.store_code in
      <foreach collection="storeCodeList" item="item" open="(" separator="," close=")">
        #{item}
      </foreach>
        and store.id not in (select store_id from t_ps_sc_line_store where line_id = #{lineId})
  </select>

  <!--商品组与客户互斥逻辑(筛选当前商品组在不同线路下不重复的客户)-->
  <select id="selectNotRepeatStoreListInCommodityGroup" resultType="com.pinshang.qingyun.orderreport.vo.ps.CommodityGroupLineStoreVO">
    select
        distinct
        store.storeId AS storeId,
        store.storeCode
    from
    (<trim suffixOverrides="union">
      <foreach collection="storeList" item="item" >
        select #{item.storeId} AS storeId, #{item.storeCode} AS storeCode
        union
      </foreach>
    </trim>
    ) AS store
    where
        store.storeId not in
      (
        select
            distinct
            IFNULL(store.store_id, -1)
        from t_ps_sc_line line
        LEFT JOIN t_ps_sc_line_store store on line.id= store.line_id
        where line.commodity_group_id = #{commodityGroupId}
      )

  </select>

  <delete id="delCommodityGroupLineStore">
    delete from t_ps_sc_line_store
    where line_id = #{lineId}
          and store_id = #{storeId}
  </delete>

  <select id="selectByLineId" resultType="java.lang.Long">
    select
        store_id
    from t_ps_sc_line_store
    where line_id = #{lineId}
  </select>
</mapper>
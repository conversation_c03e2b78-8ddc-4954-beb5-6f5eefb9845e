<?xml version="1.0" encoding="UTF-8" ?>

<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.pinshang.qingyun.orderreport.mapper.BillOfLeadingMapper">

    <select id="queryTitle" resultType="com.pinshang.qingyun.orderreport.vo.BillOfLadingPreviewRespVo">
        SELECT
               s.store_line_code AS lineCode,
               s.store_line_name AS lineName,
               s.delivery_warehouse_name AS warehouse,
               s.store_line_group_name AS lineGroup,
               s.deliveryman_name AS deliveryManName
        FROM
             t_store s
        WHERE
                s.store_line_id = #{lineId}
        GROUP BY s.store_line_id
    </select>

    <select id="queryPreviewData" resultType="com.pinshang.qingyun.orderreport.mapper.entry.BillOfLeadingPreviewEntity">
        SELECT
           s.store_type_id AS storeTypeId,
           ol.commodity_id AS commodityId,
           CONCAT(c.commodity_name,'(',c.commodity_spec,')') AS commodityNameSpec,
           SUM(ol.commodity_num) AS commodityNum
        FROM
             <if test="latestFlag">
                 t_tj_order_latest o
                 JOIN t_tj_order_list_latest ol ON o.id = ol.order_id
             </if>
             <if test="!latestFlag">
                 t_tj_order o
                 JOIN t_tj_order_list ol ON o.id = ol.order_id
             </if>
             JOIN t_store s ON o.store_id = s.id
             JOIN t_commodity c ON ol.commodity_id = c.id
        WHERE o.order_time = #{orderDate}
          AND s.store_line_id = #{lineId}
          AND o.order_status = 0
          AND ol.comb_type IN (1,3)
        GROUP BY
             s.store_type_id,
             ol.commodity_id
        ORDER BY
             c.is_summary,
             c.commodity_third_kind_id,
             c.commodity_code
    </select>
</mapper>
<?xml version="1.0" encoding="UTF-8" ?>

<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.pinshang.qingyun.orderreport.mapper.ProductSaleStatisticsMapper">

    <sql id="getCondition">
        <where>
            <if test="vo.startDate !=null and vo.endDate != null">
                AND o.order_time BETWEEN #{vo.startDate} AND #{vo.endDate}
            </if>
            <if test="vo.storeId != null">
                AND s.id = #{vo.storeId}
            </if>
            <if test="vo.storeIds != null and vo.storeIds.size() > 0">
                AND s.id IN
                <foreach collection="vo.storeIds" separator="," item="item" open="(" close=")">
                    #{item}
                </foreach>
            </if>
            <if test="vo.commodityId != null">
                AND c.id = #{vo.commodityId}
            </if>
            <if test="vo.commodityIdList != null and vo.commodityIdList.size() > 0">
                AND c.id IN
                <foreach collection="vo.commodityIdList" separator="," item="item" open="(" close=")">
                    #{item}
                </foreach>
            </if>
            <if test="vo.storeSettId != null">
                AND s.store_sett_id = #{vo.storeSettId}
            </if>
            <if test="vo.storeSettIds != null and vo.storeSettIds.size()>0 ">
                AND s.store_sett_id IN
                <foreach collection="vo.storeSettIds" separator="," item="item" open="(" close=")">
                    #{item}
                </foreach>
            </if>
            <if test="vo.factoryId != null">
                AND c.commodity_factory_id = #{vo.factoryId}
            </if>
            <if test="vo.factoryIds != null and vo.factoryIds.size > 0">
                AND c.commodity_factory_id IN
                <foreach collection="vo.factoryIds" item="item" open="(" close=")" separator="," index="index">
                    #{item}
                </foreach>
            </if>
            <if test="vo.workshopId != null">
                AND c.commodity_workshop_id = #{vo.workshopId}
            </if>
            <if test="vo.storeCompanyId != null">
                AND s.store_company_id = #{vo.storeCompanyId}
            </if>
            <if test="vo.storeTypeId != null">
                AND s.store_type_id = #{vo.storeTypeId}
            </if>
            <if test="vo.supervisorId != null">
                AND s.supervisor_id = #{vo.supervisorId}
            </if>
            <if test="vo.regionManagerId != null">
                AND s.region_manager_id = #{vo.regionManagerId}
            </if>
            <if test="vo.lineGroupId != null">
                AND s.store_line_group_id = #{vo.lineGroupId}
            </if>
            <if test="vo.deliveryManId != null">
                AND s.deliveryman_id = #{vo.deliveryManId}
            </if>
            <if test="vo.cateId1 != null">
                AND c.commodity_first_kind_id = #{vo.cateId1}
            </if>
            <if test="vo.cateId2 != null">
                AND c.commodity_second_kind_id = #{vo.cateId2}
            </if>
            <if test="vo.cateId3 != null">
                AND c.commodity_third_kind_id = #{vo.cateId3}
            </if>

            <if test="vo.companyId!=null">
                AND o.company_id = #{vo.companyId}
            </if>
            <if test="vo.filterBusinessType != null">
                <choose>
                    <when test="vo.filterBusinessType == 14">
                        AND s.store_type_id NOT IN(9131178242860605247)
                    </when>
                    <when test="vo.filterBusinessType == 15">
                        AND s.store_type_id NOT IN(9131178242860605246,9131178242860605245)
                    </when>
                </choose>
            </if>
            AND  o.order_status = 0
            AND ol.comb_type IN (1,3)
        </where>
    </sql>

    <select id="queryList" resultType="com.pinshang.qingyun.orderreport.mapper.entry.ProductSaleStatisticsTempEntry">
        SELECT
            c.commodity_factory_name AS factoryName,
            c.commodity_workshop_name AS workshopName,
            c.commodity_code AS commodityCode,
            CONCAT(c.commodity_name,'(',c.commodity_spec,')') AS commodityName,
            SUM(ol.commodity_num) AS commodityNum,
            SUM(ol.total_price) AS totalPrice,
            s.store_type_id as storeTypeId,
            o.company_id,
            c.tax_rate_id,
            c.id AS commodityId,
            o.order_time AS orderTime
        FROM
            t_tj_order o
        INNER JOIN t_tj_order_list ol ON o.id = ol.order_id
        INNER JOIN t_store s ON o.store_id=s.id
        INNER JOIN t_commodity c ON ol.commodity_id=c.id
        <include refid="getCondition"/>
        <choose>
            <when test="vo.companyId!=null">
                GROUP BY o.company_id,ol.commodity_id ,s.store_type_id
            </when>
            <otherwise>
                GROUP BY ol.commodity_id ,s.store_type_id
            </otherwise>
        </choose>
        ORDER BY c.commodity_factory_id ASC,CONVERT(c.commodity_workshop_name USING gbk) ASC,c.commodity_code asc
    </select>

    <select id="queryLatestList" resultType="com.pinshang.qingyun.orderreport.mapper.entry.ProductSaleStatisticsTempEntry">
        SELECT
            c.commodity_factory_name AS factoryName,
            c.commodity_workshop_name AS workshopName,
            c.commodity_code AS commodityCode,
            CONCAT(c.commodity_name,'(',c.commodity_spec,')') AS commodityName,
            SUM(ol.commodity_num) AS commodityNum,
            SUM(ol.total_price) AS totalPrice,
            s.store_type_id as storeTypeId,
            o.company_id,
            c.tax_rate_id,
            c.id AS commodityId,
            o.order_time AS orderTime
        FROM
            t_tj_order_latest o
        INNER JOIN t_tj_order_list_latest ol ON o.id = ol.order_id
        INNER JOIN t_store s ON o.store_id=s.id
        INNER JOIN t_commodity c ON ol.commodity_id=c.id
        <include refid="getCondition"/>
        <choose>
            <when test="vo.companyId!=null">
                GROUP BY o.company_id,ol.commodity_id ,s.store_type_id
            </when>
            <otherwise>
                GROUP BY ol.commodity_id ,s.store_type_id
            </otherwise>
        </choose>
        ORDER BY c.commodity_factory_id ASC,CONVERT(c.commodity_workshop_name USING gbk) ASC,c.commodity_code asc
    </select>

    <select id="querySum" resultType="com.pinshang.qingyun.orderreport.mapper.entry.ProductSaleStatisticsSumEntry">
        SELECT
            sum(ol.total_price) AS totalCommodityPrice
        FROM
            t_tj_order o
        INNER JOIN t_tj_order_list ol ON o.id = ol.order_id
        INNER JOIN t_store s ON o.store_id=s.id
        INNER JOIN t_commodity c ON ol.commodity_id=c.id
        <include refid="getCondition"/>
    </select>

    <select id="queryLatestSum" resultType="com.pinshang.qingyun.orderreport.mapper.entry.ProductSaleStatisticsSumEntry">
        SELECT
            sum(ol.total_price) AS totalCommodityPrice
        FROM
            t_tj_order_latest o
        INNER JOIN t_tj_order_list_latest ol ON o.id = ol.order_id
        INNER JOIN t_store s ON o.store_id=s.id
        INNER JOIN t_commodity c ON ol.commodity_id=c.id
        <include refid="getCondition"/>
    </select>


    <!--产品销售汇总列表(半年)-->
    <select id="queryHalfYearList" resultType="com.pinshang.qingyun.orderreport.mapper.entry.ProductSaleStatisticsTempEntry">
        SELECT
            c.commodity_factory_name AS factoryName,
            c.commodity_workshop_name AS workshopName,
            c.commodity_code AS commodityCode,
            CONCAT(c.commodity_name,'(',c.commodity_spec,')') AS commodityName,
            SUM(ol.commodity_num) AS commodityNum,
            SUM(ol.total_price) AS totalPrice,
            s.store_type_id as storeTypeId,
            o.company_id
        FROM
            t_tj_order_sync o
        INNER JOIN t_tj_order_list_sync ol ON o.id = ol.order_id
        INNER JOIN t_store s ON o.store_id=s.id
        INNER JOIN t_commodity c ON ol.commodity_id=c.id
        <include refid="getCondition"/>
        <choose>
            <when test="vo.companyId!=null">
                GROUP BY o.company_id,ol.commodity_id ,s.store_type_id
            </when>
            <otherwise>
                GROUP BY ol.commodity_id ,s.store_type_id
            </otherwise>
        </choose>
        ORDER BY c.commodity_factory_id ASC,CONVERT(c.commodity_workshop_name USING gbk) ASC,c.commodity_code asc
    </select>

    <!--产品销售汇总合计(半年)-->
    <select id="querySumHalfYear" resultType="com.pinshang.qingyun.orderreport.mapper.entry.ProductSaleStatisticsSumEntry">
        SELECT
            sum(ol.total_price) AS totalCommodityPrice
        FROM
            t_tj_order_sync o
        INNER JOIN t_tj_order_list_sync ol ON o.id = ol.order_id
        INNER JOIN t_store s ON o.store_id=s.id
        INNER JOIN t_commodity c ON ol.commodity_id=c.id
        <include refid="getCondition"/>
    </select>

    <select id="selectProductSaleStatisticsCommodityInfoListEntry" resultType="com.pinshang.qingyun.orderreport.mapper.entry.ProductSaleStatisticsCommodityInfoListEntry">
            SELECT
            c.id AS commodityId,
            c.commodity_code,
            c.commodity_name,
            c.commodity_spec,
            c.commodity_unit_name,
            <choose>
            <when test="isLatest">
                SUM(ol.commodity_num) AS commodityQuantity,
            </when>
            <otherwise>
                SUM(o.total_quantity) AS commodityQuantity,
            </otherwise>
            </choose>
            CONCAT_WS('/',c.commodity_first_kind_name,c.commodity_second_kind_name,c.commodity_third_kind_name) AS categoryName,
            CONCAT_WS('/',c.commodity_first_kind_id,c.commodity_second_kind_id,c.commodity_third_kind_id) AS categoryIds
            FROM
            <choose>
                <when test="isLatest">
                    t_tj_order_latest o
                    INNER JOIN t_tj_order_list_latest ol ON o.id = ol.order_id
                    INNER JOIN t_commodity c ON ol.commodity_id=c.id
                    WHERE  o.order_time = DATE_FORMAT(#{vo.deliveryDate},'%Y-%m-%d' )
                    AND o.order_status = 0
                    AND ol.comb_type IN (1,3)
                </when>
                <otherwise>
                    t_tj_commodity_sale_statistics o
                    INNER JOIN t_commodity c ON o.commodity_id=c.id
                    WHERE o.order_time = DATE_FORMAT(#{vo.deliveryDate},'%Y-%m-%d' )
                </otherwise>
            </choose>
            <if test="vo.concatCodeOrName!=null and vo.concatCodeOrName!=''">
                AND (c.commodity_code LIKE CONCAT('%',#{vo.concatCodeOrName},'%') OR c.commodity_name LIKE CONCAT('%',#{vo.concatCodeOrName},'%'))
            </if>
            <if test="map!=null and map.size>0">
                AND (
                <foreach collection="map.keys" item="k" separator="or">
                     <if test="k == 'firstCategory'">
                         c.commodity_first_kind_id IN
                         <foreach collection="map[k]" item="id" separator="," open="(" close=")">
                             #{id}
                         </foreach>
                     </if>

                    <if test="k == 'secondCategory'">
                        c.commodity_second_kind_id IN
                        <foreach collection="map[k]" item="id" separator="," open="(" close=")">
                            #{id}
                        </foreach>
                    </if>

                    <if test="k == 'thirdCategory'">
                        c.commodity_third_kind_id IN
                        <foreach collection="map[k]" item="id" separator="," open="(" close=")">
                            #{id}
                        </foreach>
                    </if>

                </foreach>
                )
            </if>
            GROUP BY c.id
            ORDER BY c.commodity_first_kind_name,commodity_second_kind_name,commodity_third_kind_name ASC,c.commodity_name ASC
    </select>

    <select id="queryListForPurchaseByCreateTime"
            resultType="com.pinshang.qingyun.orderreport.mapper.entry.ProductSaleStatisticsPurchaseTempEntry">
        SELECT
        c.id AS commodityId,
        o.order_time AS orderTime,
        SUM(ol.commodity_num) AS commodityNum,
        SUM(ol.total_price) AS totalPrice,
        o.create_time AS createTime
        FROM
        t_tj_order_latest o
        INNER JOIN t_tj_order_list_latest ol ON o.id = ol.order_id
        INNER JOIN t_commodity c ON ol.commodity_id=c.id
        <where> o.order_status = 0 AND ol.comb_type IN (1,3)
            <if test="startDate !=null and endDate != null">
                AND o.order_time BETWEEN #{startDate} AND #{endDate}
            </if>
            <if test="startCreateTime != null and endCreateTime != null">
                AND o.create_time BETWEEN #{startCreateTime} AND #{endCreateTime}
            </if>
            <if test="commodityId != null">
                AND c.id = #{commodityId}
            </if>
        </where>
        GROUP BY c.id, DATE(o.create_time)
    </select>
</mapper>
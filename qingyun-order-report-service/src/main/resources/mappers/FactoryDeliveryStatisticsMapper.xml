<?xml version="1.0" encoding="UTF-8" ?>

<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.pinshang.qingyun.orderreport.mapper.FactoryDeliveryStatisticsMapper">

    <resultMap id = "queryFactoryDeliveryMainMap" type="com.pinshang.qingyun.orderreport.mapper.entry.FactoryDeliveryStatisticsByPageEntry">
        <result property="id" column="id" />
        <result property="lineName" column="line_name" />
        <result property="deliverymanName" column="deliveryman_name" />
        <result property="factoryName" column="factory_name" />
        <result property="totalAmount" column="total_amount" />
        <collection property="items" column="{id=id}" javaType="ArrayList"
                    ofType="com.pinshang.qingyun.orderreport.mapper.entry.FactoryDeliveryStatisticsItemByPageEntry" select="queryFactoryDeliveryItem"/>
    </resultMap>

    <resultMap id="queryFactoryDeliveryItemMap" type="com.pinshang.qingyun.orderreport.mapper.entry.FactoryDeliveryStatisticsItemByPageEntry" >
        <result property="storeTypeId" column="store_type_id"/>
        <result property="totalQuantity" column="total_quantity"/>
        <result property="totalAmount" column="total_amount"/>
    </resultMap>

    <select id="queryFactoryDeliveryMain" resultMap="queryFactoryDeliveryMainMap">
        SELECT
            GROUP_CONCAT(id) AS id,
            line_name,
            deliveryman_name,
            factory_name,
            SUM(total_amount) AS totalAmount
        FROM t_tj_factory_delivery_statistics
        WHERE
            1=1
            <if test="factoryId !=null">
                and factory_id =#{factoryId}
            </if>
            <if test="lineGroupId !=null">
                and line_group_id =#{lineGroupId}
            </if>
            <if test="deliverymanId !=null">
                and deliveryman_id =#{deliverymanId}
            </if>
            <if test="null != startDate and null != endDate ">
                <![CDATA[ AND order_time >= #{startDate} AND order_time <= #{endDate} ]]>
            </if>
        GROUP BY line_id,deliveryman_id,factory_id
        ORDER BY factory_id DESC
    </select>
    <!-- 1对多查询 -->
    <select id="queryFactoryDeliveryItem" parameterType="java.util.Map"  resultMap="queryFactoryDeliveryItemMap">
        SELECT
            i.store_type_id,
            SUM(i.total_quantity) AS totalQuantity,
            SUM(i.total_amount) AS totalAmount
        FROM
            t_tj_factory_delivery_statistics_item i JOIN
            mysql.help_topic b ON b.help_topic_id <![CDATA[ < ]]> LENGTH(#{id})-LENGTH(REPLACE(#{id},',',''))+1
        WHERE
            i.refer_id IN (SUBSTRING_INDEX(SUBSTRING_INDEX(#{id},',',help_topic_id+1),',',-1))
        GROUP BY i.store_type_id;
    </select>
    <select id="queryFactoryDeliveryStatistics" resultType="com.pinshang.qingyun.orderreport.mapper.entry.FactoryDeliveryStatisticsEntry">
        SELECT
            ss.store_type_id,
            ss.store_line_id AS lineId,
            ss.store_line_name AS lineName,
            ss.store_line_group_id AS lineGroupId,
            ss.deliveryman_id AS deliverymanId,
            ss.deliveryman_name AS deliverymanName,
            c.commodity_factory_id AS factoryId,
            c.commodity_factory_name AS factoryName,
            SUM(l.commodity_num) AS totalQuantity,
            SUM(l.total_price) AS totalAmount
        FROM ${mainTable} o
        INNER JOIN ${itemTable} l ON l.order_id = o.id AND o.order_time = #{orderTime} AND o.order_status=0
        INNER JOIN t_store ss ON o.store_id = ss.id
        INNER JOIN t_commodity c ON c.id = l.commodity_id
        WHERE l.comb_type IN (1,3)
        GROUP BY ss.store_type_id,ss.store_line_id,ss.deliveryman_id,c.commodity_factory_id;
    </select>

    <insert id="batchInsert" parameterType="com.pinshang.qingyun.orderreport.model.FactoryDeliveryStatistics" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO t_tj_factory_delivery_statistics
        (order_time, factory_id, factory_name, line_id, line_name, line_group_id, deliveryman_id, deliveryman_name, total_quantity, total_amount)
        VALUES
        <foreach collection="list" separator="," item="item">
            (#{item.orderTime}, #{item.factoryId}, #{item.factoryName}, #{item.lineId}, #{item.lineName},
            #{item.lineGroupId}, #{item.deliverymanId}, #{item.deliverymanName}, #{item.totalQuantity}, #{item.totalAmount})
        </foreach>
    </insert>

    <select id="queryFactoryDeliveryIdList" resultType="java.lang.Long">
        SELECT css.id
        FROM t_tj_factory_delivery_statistics css
        <if test="commodityId != null">
            INNER JOIN t_commodity c ON css.factory_id = c.commodity_factory_id
        </if>
        <where>
            <if test="commodityId != null">
                AND c.id = #{commodityId}
            </if>
            <if test="orderTime != null and orderTime != '' ">
                AND css.order_time = date(#{orderTime})
            </if>
        </where>
    </select>

</mapper>
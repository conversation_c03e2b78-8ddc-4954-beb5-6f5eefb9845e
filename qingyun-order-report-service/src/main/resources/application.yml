#security:
#  ignored: /chk.html,/robots.txt
mybatis:
    type-aliases-package: com.pinshang.qingyun.orderreport.model
    mapper-locations: classpath*:mappers/*.xml
    config-location: classpath:conf/mybatis-typehandler.xml
mapper:
    mappers:
        - com.pinshang.qingyun.base.mybatis.MyMapper
    not-empty: false
    identity: MYSQL
    enum-as-simple-type: true

pagehelper:
    helperDialect: mysql
    reasonable: false
    supportMethodsArguments: true
    params: count=countSql
spring:
  session:
    store-type=none
  freemarker:
    request-context-attribute: rc
    expose-request-attributes: true
    expose-session-attributes: true
    expose-spring-macro-helpers: true
  jackson:
    time-zone: GMT+8
    serialization:
      write-dates-as-timestamps: true
feign:
  httpclient:
    enabled: true
  hystrix:
    enabled: true
eureka:
  client:
    healthcheck:
      enabled: true
  instance:
    metadata-map:
      instanceVersion: V_4.4.0
      appProfile: ${spring.profiles.active}
      appSwitch: ${application.name.switch}
      appCode: ${pinshang.application-name}

management:
  endpoints:
    web:
      exposure:
        include: '*'

tramy:
  absoluteSavePath: /opt/pdf/
  savePath: /statistical/



package com.pinshang.qingyun.orderreport.service;

import com.pinshang.qingyun.box.utils.DateTimeUtil;
import com.pinshang.qingyun.box.utils.DateUtil;
import com.pinshang.qingyun.orderreport.mapper.entry.ProductSaleStatisticsSumEntry;
import com.pinshang.qingyun.orderreport.vo.ProductSaleStatisticsVo;
import com.pinshang.qingyun.test.AbstractJunitBase;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Arrays;
import java.util.Date;

/**
 * @ClassName ProductSaleStatisticsServiceTest
 * @Description TODO
 * <AUTHOR>
 * @Date 2019/10/29 16:10
 **/
public class ProductSaleStatisticsServiceTest extends AbstractJunitBase {
    @Autowired
    private ProductSaleStatisticsService productSaleStatisticsService;
    @Test
    public void queryList(){
        ProductSaleStatisticsVo vo = new ProductSaleStatisticsVo();
        vo.setStartDate(DateUtil.getDate("2019","10","8"));
        vo.setEndDate( DateUtil.getDate("2019","10","29"));
        vo.setStoreIds(Arrays.asList());
        vo.setStoreSettIds(Arrays.asList());
        productSaleStatisticsService.queryList(vo) ;
        System.out.println("");
    }
    @Test
    public void querySum(){
        ProductSaleStatisticsVo vo = new ProductSaleStatisticsVo();
        vo.setStartDate(DateUtil.getDate("2019","10","8"));
        vo.setEndDate( DateUtil.getDate("2019","10","29"));
        vo.setStoreIds(Arrays.asList(11L));
        vo.setStoreSettIds(Arrays.asList(222L));
        ProductSaleStatisticsSumEntry sum = productSaleStatisticsService.querySum(vo) ;
        System.out.println(sum.getTotalCommodityPrice());
    }
}

package com.pinshang.qingyun.orderreport.service;

import com.pinshang.qingyun.box.utils.DateUtil;
import com.pinshang.qingyun.orderreport.pdf.WorkshopShipmentsPdfCreator;
import com.pinshang.qingyun.orderreport.vo.ProductShipmentsRequestVo;
import com.pinshang.qingyun.orderreport.vo.WorkshopShipmentsVo;
import com.pinshang.qingyun.test.AbstractJunitBase;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.annotation.Rollback;


public class WorkshopShipmentsPDFTest extends AbstractJunitBase {

    @Autowired
    private WorkshopShipmentsService shipmentsService;
    @Autowired
    private WorkshopShipmentsPdfCreator workshopShipmentsPdfCreator;

    @Test
    @Rollback(false)
    public void createPDF() {
        ProductShipmentsRequestVo vo = new ProductShipmentsRequestVo();
        vo.setStartOrderDate(DateUtil.parseDate("2019-05-09","yyyy-MM-dd"));
        vo.setEndOrderDate(DateUtil.parseDate("2019-05-09","yyyy-MM-dd"));
        //vo.setDeliveryTime("11:30");
        vo.setWorkshopId(2L);
        WorkshopShipmentsVo preview = shipmentsService.preview(vo);
        workshopShipmentsPdfCreator.createPdf(preview);
    }

}

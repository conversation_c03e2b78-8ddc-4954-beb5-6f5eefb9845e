package com.pinshang.qingyun.orderreport.service;

import com.pinshang.qingyun.orderreport.mapper.BillOfLeadingMapper;
import com.pinshang.qingyun.orderreport.mapper.DeliveryListMapper;
import com.pinshang.qingyun.orderreport.vo.DeliveryListReqVo;
import com.pinshang.qingyun.orderreport.vo.DeliveryListRespVo;
import com.pinshang.qingyun.test.AbstractJunitBase;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import javax.annotation.Resource;
import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2019/1/30 11:19.
 */
public class BillOfLeadingMappeTest extends AbstractJunitBase {

    @Resource
    private BillOfLeadingMapper billOfLeadingMapper;

    @Resource
    private DeliveryListMapper deliveryListMapper;
    @Autowired
    private DeliveryListService deliveryListService;

    @Test
    public void method1() {
        DateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
        DeliveryListReqVo params = new DeliveryListReqVo();
        try {
            params.setOrderDate((dateFormat.parse("2019-01-26")));
        } catch (ParseException e) {
            e.printStackTrace();
        }
        params.setDeliveryTime("");
//        params.setDeliveryBatch("");
        List<DeliveryListRespVo> list =  deliveryListService.searchDeliveryList(params);
        System.out.println(list);
    }

    @Test
    public void method() {
        billOfLeadingMapper.queryPreviewData(46L, "2019-01-01", false);
        System.out.println("ok");
    }
}

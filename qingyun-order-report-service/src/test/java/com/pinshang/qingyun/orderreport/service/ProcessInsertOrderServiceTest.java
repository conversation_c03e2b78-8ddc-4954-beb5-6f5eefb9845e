package com.pinshang.qingyun.orderreport.service;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import lombok.extern.slf4j.Slf4j;

import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.cloud.netflix.eureka.serviceregistry.EurekaAutoServiceRegistration;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.test.annotation.Rollback;

import com.pinshang.qingyun.orderreport.mapper.OrderItemMapper;
import com.pinshang.qingyun.orderreport.mapper.OrderMapper;
import com.pinshang.qingyun.orderreport.model.Order;
import com.pinshang.qingyun.orderreport.model.OrderItem;
import com.pinshang.qingyun.test.AbstractJunitBase;

@Slf4j
public class ProcessInsertOrderServiceTest extends AbstractJunitBase {
	
	@MockBean
	private EurekaAutoServiceRegistration eurekaAutoServiceRegistration;

    @Autowired
    private OrderService orderService;
    @Autowired
    private OrderMapper orderMapper;
    @Autowired
    private OrderItemMapper orderItemMapper;
    @Autowired
    private KafkaTemplate<String, String> kafkaTemplate;
    @Value("${application.name.switch}")
    private String applicationNameSwitch;
    @Autowired
    private TjOrderSendKafkaService orderSendKafkaService;

    @Test
    @Rollback(false)
    public void processInsertOrder() {
        Order order = new Order();
        order.setId(219569L);
        order.setOrderCode("1547608171269787305");
        order.setModeType(0);
        order.setCreateId(372L);
        order.setCreateTime(new Date(1547608171000L));
        order.setFinalAmount(BigDecimal.valueOf(10.5));
        order.setOrderAmount(BigDecimal.valueOf(10.5));
        order.setOrderStatus(0);
        order.setOrderTime(new Date(1547654400000L));
        order.setOrderType(1);
        order.setPrintNum(1);
        order.setPrintType(3);
        order.setSettleStatus(0);
        order.setStoreId(752908786325622016L);
        order.setUpdateId(372L);
        order.setUpdateTime(new Date(1547608171000L));
        List<OrderItem> orderList = new ArrayList<>();
        OrderItem orderItem1 = new OrderItem();
        orderItem1.setId(5318315L);
        orderItem1.setCommodityId(227000091358522304L);
        orderItem1.setCommodityNum(BigDecimal.ONE);
        orderItem1.setCommodityPrice(BigDecimal.valueOf(3.5));
        orderItem1.setOrderId(219569L);
        orderItem1.setTotalPrice(BigDecimal.valueOf(3.5));
        orderItem1.setType(1);
        orderList.add(orderItem1);
        OrderItem orderItem2 = new OrderItem();
        orderItem2.setId(5318316L);
        orderItem2.setCommodityId(970288549453778176L);
        orderItem2.setCommodityNum(BigDecimal.valueOf(2));
        orderItem2.setCommodityPrice(BigDecimal.valueOf(3.5));
        orderItem2.setOrderId(219569L);
        orderItem2.setTotalPrice(BigDecimal.valueOf(7));
        orderItem2.setType(1);
        orderList.add(orderItem2);
        order.setOrderList(orderList);
        orderService.processInsertOrder(order);
    }

}
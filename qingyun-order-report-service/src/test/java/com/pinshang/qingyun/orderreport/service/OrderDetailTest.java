package com.pinshang.qingyun.orderreport.service;

import com.github.pagehelper.PageInfo;
import com.pinshang.qingyun.orderreport.vo.LateOrderReqVo;
import com.pinshang.qingyun.orderreport.vo.LateOrderRespVo;
import com.pinshang.qingyun.test.AbstractJunitBase;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.text.ParseException;

/**
 * <AUTHOR>
 * @date 2019/3/18 10:35.
 */
public class OrderDetailTest extends AbstractJunitBase {
    @Autowired
    private OrderDetailService orderDetailService;

    @Test
    public void queryLateOrderTest() throws ParseException {
        LateOrderReqVo req = new LateOrderReqVo();
        req.setOrderStartDate("2019-03-18");
        req.setOrderEndDate("2019-03-18");
        PageInfo<LateOrderRespVo> pageInfo =  orderDetailService.queryLateOrder(req);
        System.out.println( pageInfo );
    }
}

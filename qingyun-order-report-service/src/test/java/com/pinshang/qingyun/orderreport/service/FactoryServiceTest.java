package com.pinshang.qingyun.orderreport.service;

import com.pinshang.qingyun.kafka.MessageOperationType;
import com.pinshang.qingyun.orderreport.model.Order;
import com.pinshang.qingyun.orderreport.model.OrderItem;
import com.pinshang.qingyun.orderreport.vo.DeliveryListPreviewReqVo;
import com.pinshang.qingyun.test.AbstractJunitBase;
import org.joda.time.DateTime;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.annotation.Rollback;

import java.math.BigDecimal;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2019/1/16 11:15.
 */
public class FactoryServiceTest extends AbstractJunitBase {

    @Autowired
    private FactoryDeliveryStatisticsService factoryDeliveryStatisticsService;

    @Test
    @Rollback(false)
    public void processInsertOrder() throws ParseException {
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");//注意月份是MM
        Date date = simpleDateFormat.parse("2019-09-15");
        factoryDeliveryStatisticsService.insertFactoryDeliveryStatistics(date,"t_tj_order_latest","t_tj_order_list_latest");
    }
}
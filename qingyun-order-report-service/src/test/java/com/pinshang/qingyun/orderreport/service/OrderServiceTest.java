package com.pinshang.qingyun.orderreport.service;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.pinshang.qingyun.box.utils.DateUtil;
import com.pinshang.qingyun.box.utils.IDGenerator;
import com.pinshang.qingyun.kafka.MessageOperationType;
import com.pinshang.qingyun.kafka.base.KafkaMessageWrapper;
import com.pinshang.qingyun.kafka.base.KafkaTopicConstant;
import com.pinshang.qingyun.kafka.enums.KafkaMessageOperationTypeEnum;
import com.pinshang.qingyun.kafka.enums.KafkaMessageTypeEnum;
import com.pinshang.qingyun.order.dto.commodity.CreateOrderIDTO;
import com.pinshang.qingyun.order.service.OrderClient;
import com.pinshang.qingyun.orderreport.mapper.OrderItemMapper;
import com.pinshang.qingyun.orderreport.mapper.OrderMapper;
import com.pinshang.qingyun.orderreport.model.Order;
import com.pinshang.qingyun.orderreport.model.OrderItem;
import com.pinshang.qingyun.orderreport.vo.DeliveryListPreviewReqVo;
import com.pinshang.qingyun.test.AbstractJunitBase;
import lombok.extern.slf4j.Slf4j;
import org.joda.time.DateTime;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.test.annotation.Rollback;

import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.ArrayBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @date 2019/1/16 11:15.
 */
@Slf4j
public class OrderServiceTest extends AbstractJunitBase {

    @Autowired
    private OrderService orderService;
    @Autowired
    private OrderMapper orderMapper;
    @Autowired
    private OrderItemMapper orderItemMapper;
    @Autowired
    private KafkaTemplate<String, String> kafkaTemplate;
    @Value("${application.name.switch}")
    private String applicationNameSwitch;
    @Autowired
    private TjOrderSendKafkaService orderSendKafkaService;


    public static void main(String[] args) {
        DeliveryListPreviewReqVo first = new DeliveryListPreviewReqVo();
        Calendar ca =Calendar.getInstance();
        ca.set(2019,1,14);
        first.setOrderDate(ca.getTime());
       boolean b =  new DateTime(first.getOrderDate()).isAfter(new DateTime().withMillisOfDay(0));
        System.out.println(b);
    }
    @Test
    @Rollback(false)
    public void processInsertOrder() {
        Order order = new Order();
        order.setId(219569L);
        order.setOrderCode("1547608171269787305");
        order.setModeType(0);
        order.setCreateId(372L);
        order.setCreateTime(new Date(1547608171000L));
        order.setFinalAmount(BigDecimal.valueOf(10.5));
        order.setOrderAmount(BigDecimal.valueOf(10.5));
        order.setOrderStatus(0);
        order.setOrderTime(new Date(1547654400000L));
        order.setOrderType(1);
        order.setPrintNum(1);
        order.setPrintType(3);
        order.setSettleStatus(0);
        order.setStoreId(752908786325622016L);
        order.setUpdateId(372L);
        order.setUpdateTime(new Date(1547608171000L));
        List<OrderItem> orderList = new ArrayList<>();
        OrderItem orderItem1 = new OrderItem();
        orderItem1.setId(5318315L);
        orderItem1.setCommodityId(227000091358522304L);
        orderItem1.setCommodityNum(BigDecimal.ONE);
        orderItem1.setCommodityPrice(BigDecimal.valueOf(3.5));
        orderItem1.setOrderId(219569L);
        orderItem1.setTotalPrice(BigDecimal.valueOf(3.5));
        orderItem1.setType(1);
        orderList.add(orderItem1);
        OrderItem orderItem2 = new OrderItem();
        orderItem2.setId(5318316L);
        orderItem2.setCommodityId(970288549453778176L);
        orderItem2.setCommodityNum(BigDecimal.valueOf(2));
        orderItem2.setCommodityPrice(BigDecimal.valueOf(3.5));
        orderItem2.setOrderId(219569L);
        orderItem2.setTotalPrice(BigDecimal.valueOf(7));
        orderItem2.setType(1);
        orderList.add(orderItem2);
        order.setOrderList(orderList);
        orderService.processInsertOrder(order);
    }

    @Test
    public void processUpdateOrder() {
    }

    @Test
    public void processCancelOrder() {
    }

    @Test
    @Rollback(false)
    public void cancleOrderTest() {
        List<Long> ids = new ArrayList<>();
        ids.add(752908786325622016L);
        orderService.discardOrderByStoreIds(ids);
    }

    @Test
    @Rollback(false)
    public void processOperatorOrderStatisticalTest() {
        Order order = new Order();
        order.setId(16804772L);
        order.setOrderCode("1547608171269787305");
        order.setModeType(0);
        order.setCreateId(372L);
        order.setCreateTime(new Date(1547608171000L));
        order.setFinalAmount(BigDecimal.valueOf(10.5));
        order.setOrderAmount(BigDecimal.valueOf(10.5));
        order.setOrderStatus(0);
        order.setOrderTime(new Date(1547654400000L));
        order.setOrderType(1);
        order.setPrintNum(1);
        order.setPrintType(3);
        order.setSettleStatus(0);
        order.setStoreId(752908786325622016L);
        order.setUpdateId(372L);
        order.setUpdateTime(new Date(1547608171000L));
        List<OrderItem> orderList = new ArrayList<>();
        OrderItem orderItem1 = new OrderItem();
        orderItem1.setId(5318315L);
        orderItem1.setCommodityId(227000091358522304L);
        orderItem1.setCommodityNum(BigDecimal.ONE);
        orderItem1.setCommodityPrice(BigDecimal.valueOf(3.5));
        orderItem1.setOrderId(219569L);
        orderItem1.setTotalPrice(BigDecimal.valueOf(3.5));
        orderItem1.setType(1);
        orderList.add(orderItem1);
        OrderItem orderItem2 = new OrderItem();
        orderItem2.setId(5318316L);
        orderItem2.setCommodityId(970288549453778176L);
        orderItem2.setCommodityNum(BigDecimal.valueOf(2));
        orderItem2.setCommodityPrice(BigDecimal.valueOf(3.5));
        orderItem2.setOrderId(219569L);
        orderItem2.setTotalPrice(BigDecimal.valueOf(7));
        orderItem2.setType(1);
        orderList.add(orderItem2);
        order.setOrderList(orderList);
        orderService.processOperatorOrderStatistical(order, MessageOperationType.CANCEL);
    }

    @Test
    @Rollback(false)
    public void batchCreateOrder(){
        ThreadPoolExecutor threadPool = new ThreadPoolExecutor(10, 10, 3,
                TimeUnit.MINUTES, new ArrayBlockingQueue<Runnable>(200)
        );
        for(int i=0;i<3;i++){
            try {
                //创建任务并提交到线程池中
                threadPool.execute(()-> {
                    try{
                        Order order = new Order();
                        order.setOrderCode(IDGenerator.newOrderCode());
                        order.setStoreId(752908786325622016L);
                        order.setOrderTime(DateUtil.getNowDate());
                        order.setFinalAmount(BigDecimal.valueOf(10.5));
                        order.setOrderAmount(BigDecimal.valueOf(10.5));
                        order.setOrderStatus(0);
                        order.setModeType(0);
                        order.setOrderType(1);
                        order.setPrintNum(1);
                        order.setPrintType(3);
                        order.setSettleStatus(0);
                        order.setCreateId(372L);
                        order.setCreateTime(new Date());
                        order.setUpdateId(372L);
                        order.setUpdateTime(new Date());
                        orderMapper.insertSelective(order);
                        Long orderId = order.getId();
                        List<OrderItem> orderList = new ArrayList<>();
                        OrderItem orderItem1 = new OrderItem();
                        orderItem1.setCommodityId(227000091358522304L);
                        orderItem1.setCommodityNum(BigDecimal.ONE);
                        orderItem1.setCommodityPrice(BigDecimal.valueOf(3.5));
                        orderItem1.setOrderId(orderId);
                        orderItem1.setTotalPrice(BigDecimal.valueOf(3.5));
                        orderItem1.setType(1);
                        orderItemMapper.insertSelective(orderItem1);
                        orderList.add(orderItem1);
                        OrderItem orderItem2 = new OrderItem();
                        orderItem2.setCommodityId(970288549453778176L);
                        orderItem2.setCommodityNum(BigDecimal.valueOf(2));
                        orderItem2.setCommodityPrice(BigDecimal.valueOf(3.5));
                        orderItem2.setOrderId(orderId);
                        orderItem2.setTotalPrice(BigDecimal.valueOf(7));
                        orderItem2.setType(1);
                        orderItemMapper.insertSelective(orderItem2);
                        orderList.add(orderItem2);
                        order.setOrderList(orderList);

                        orderSendKafkaService.sendEsOrderMsg(order,KafkaMessageOperationTypeEnum.INSERT);
                    }catch(Exception e){
                        log.error("生成订单异常",e);
                    }
                });

                Thread.sleep(10);
            } catch (Exception e) {
                log.error("建议订货量并且加入临时购物车异常: ",e);
            }
        }

        // 等待所有线程执行完毕当前任务。
        threadPool.shutdown();
    }


}
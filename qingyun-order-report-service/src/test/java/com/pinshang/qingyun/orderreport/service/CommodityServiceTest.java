package com.pinshang.qingyun.orderreport.service;

import com.pinshang.qingyun.test.AbstractJunitBase;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.annotation.Rollback;

import java.util.ArrayList;
import java.util.Map;
import java.util.Set;
import java.util.TreeSet;


public class CommodityServiceTest extends AbstractJunitBase {

    @Autowired
    private CommodityService commodityService;

    @Test
    @Rollback(false)
    public void Test() {
        Set<String> commodityCodeList = new TreeSet<>();
        commodityCodeList.add("000049");
        commodityCodeList.add("000171");
        commodityCodeList.add("000185");
        Map<String, String> cateNameMap = commodityService.queryCommodityCateName(new ArrayList<>(commodityCodeList));
        System.out.println("====="+cateNameMap);
    }
}
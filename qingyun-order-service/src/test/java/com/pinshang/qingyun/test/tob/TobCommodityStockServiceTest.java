package com.pinshang.qingyun.test.tob;

import com.alibaba.fastjson.JSON;
import com.pinshang.qingyun.order.service.TobCommodityStockService;
import com.pinshang.qingyun.order.vo.tob.TobCommodityStockKafkaVo;
import com.pinshang.qingyun.test.AbstractJunitBase;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.annotation.Rollback;

public class TobCommodityStockServiceTest extends AbstractJunitBase {
    @Autowired
    private TobCommodityStockService tobCommodityStockService;


    @Test
    @Rollback(false)
    public void updateTobCommodityStock() {
        String msg = "{\n" +
                "\t\"type\": 15,\n" +
                "\t\"commodityId\": 468788174651305600,\n" +
                "\t\"warehouseId\": 10198,\n" +
                "\t\"stockType\": 1,\n" +
                "\t\"stockStatus\": 0,\n" +
                "\t\"logisticsCenterId\": 10086,\n" +
                "\t\"currentTime\": 1754472485218,\n" +
                "\t\"kafkaKey\": \"10198_468788174651305600_15\"\n" +
                "}";
        TobCommodityStockKafkaVo vo = JSON.parseObject(msg, TobCommodityStockKafkaVo.class);
        tobCommodityStockService.updateTobCommodityStock(vo, vo.getCommodityId());
    }

}

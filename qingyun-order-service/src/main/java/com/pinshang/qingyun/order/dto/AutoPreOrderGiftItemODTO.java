package com.pinshang.qingyun.order.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;


/**
 * 加货申请通过后，价格，赠品，更新和订单一样
 * @Author: liu<PERSON>hen
 * @DateTime: 2022/5/13 16:11
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class AutoPreOrderGiftItemODTO {
    private Long commodityId;
    private BigDecimal stockQuantity;
    private BigDecimal price;
    /**
     * 类型：1.订单商品，2，赠品，3配货商品
     */
    private Integer type;
}

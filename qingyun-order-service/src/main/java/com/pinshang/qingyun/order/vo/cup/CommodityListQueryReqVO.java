package com.pinshang.qingyun.order.vo.cup;

import com.pinshang.qingyun.base.page.Pagination;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class CommodityListQueryReqVO extends Pagination {

    @ApiModelProperty("客户id")
    private Long storeId;

    @ApiModelProperty("商品编码")
    private String code;

    @ApiModelProperty("商品编码集合")
    private String[] codes;

    @ApiModelProperty("送货日期")
    private String orderTime;

    @ApiModelProperty("已经选择的商品编码集合")
    private String[] selectedCodes;

//    @ApiModelProperty("添加商品方式 1：商品编码 2：商品条码")
    @ApiModelProperty("添加商品方式 1：商品条码 2：商品编码")
    private Integer addCommodityType = 1;

    @ApiModelProperty("是否计划订单 0：否 1：是")
    private Integer planOrderType;
}

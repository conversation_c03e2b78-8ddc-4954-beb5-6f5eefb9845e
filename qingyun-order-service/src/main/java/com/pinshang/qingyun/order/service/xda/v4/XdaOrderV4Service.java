package com.pinshang.qingyun.order.service.xda.v4;

import com.alibaba.fastjson2.JSON;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Maps;
import com.pinshang.qingyun.base.api.ApiErrorCodeEnum;
import com.pinshang.qingyun.base.api.XdaTokenInfo;
import com.pinshang.qingyun.base.configure.expand.BizLogicException;
import com.pinshang.qingyun.base.configure.expand.QYAssert;
import com.pinshang.qingyun.base.constant.QingyunConstant;
import com.pinshang.qingyun.base.constant.XdaAppVersionConstant;
import com.pinshang.qingyun.base.enums.*;
import com.pinshang.qingyun.base.enums.order.OrderTypeEnum;
import com.pinshang.qingyun.base.enums.order.ProductTypeEnum;
import com.pinshang.qingyun.base.enums.xda.XdaOrderProcessStatusEunm;
import com.pinshang.qingyun.base.enums.xda.XdaStoreTypeEnum;
import com.pinshang.qingyun.base.util.FastThreadLocalUtil;
import com.pinshang.qingyun.box.utils.*;
import com.pinshang.qingyun.kafka.enums.KafkaMessageOperationTypeEnum;
import com.pinshang.qingyun.marketing.dto.mtCoupon.MtOrderCouponODTO;
import com.pinshang.qingyun.marketing.service.MtPromotionClient;
import com.pinshang.qingyun.order.bo.StoreDeductionBO;
import com.pinshang.qingyun.order.bo.StoreRechargeBO;
import com.pinshang.qingyun.order.bo.StoreRechargeRubricationBO;
import com.pinshang.qingyun.order.constant.PreOrderTipConstant;
import com.pinshang.qingyun.order.dto.giftLimit.GiftLimitQuantitySaveIDTO;
import com.pinshang.qingyun.order.dto.shopcart.v3.CommodityPromotionV3ODTO;
import com.pinshang.qingyun.order.dto.shopcart.v4.*;
import com.pinshang.qingyun.order.dto.xda.TdaOrderSyncTmsIDTO;
import com.pinshang.qingyun.order.dto.xda.XdaQueryOrder4AppParam;
import com.pinshang.qingyun.order.dto.xda.tda.LogisticsDeliveryMessage;
import com.pinshang.qingyun.order.dto.xda.v2.XdaOrderItemAppV2ODTO;
import com.pinshang.qingyun.order.dto.xda.v3.XdaOrderItemAppV3ODTO;
import com.pinshang.qingyun.order.dto.xda.v3.XdaPreOrderItemGroupV3ODTO;
import com.pinshang.qingyun.order.dto.xda.v3.XdaPreOrderItemV3ODTO;
import com.pinshang.qingyun.order.dto.xda.v4.*;
import com.pinshang.qingyun.order.enums.CombTypeEnum;
import com.pinshang.qingyun.order.enums.ProductTypeEnums;
import com.pinshang.qingyun.order.enums.StockLevelEnums;
import com.pinshang.qingyun.order.enums.XdaPayTypeEnum;
import com.pinshang.qingyun.order.mapper.*;
import com.pinshang.qingyun.order.mapper.entry.order.OrderRealDeliveryFinishEntry;
import com.pinshang.qingyun.order.mapper.entry.order.V2.OrderItemV2Entry;
import com.pinshang.qingyun.order.mapper.entry.payType.PayTypeEntry;
import com.pinshang.qingyun.order.mapper.entry.recharge.XdaPayBillEntry;
import com.pinshang.qingyun.order.model.order.*;
import com.pinshang.qingyun.order.model.shop.Shop;
import com.pinshang.qingyun.order.service.*;
import com.pinshang.qingyun.order.service.giftLimit.GiftLimitService;
import com.pinshang.qingyun.order.service.order.AppOrderLogService;
import com.pinshang.qingyun.order.service.recharge.RechargeService;
import com.pinshang.qingyun.order.service.xda.CouponDayStatisticsService;
import com.pinshang.qingyun.order.service.xda.OrderLimitQuantityService;
import com.pinshang.qingyun.order.vo.BStockLackVO;
import com.pinshang.qingyun.order.vo.splitOrder.SplitOrderKafkaVo;
import com.pinshang.qingyun.price.dto.commodity.CommodityListRequestIDTO;
import com.pinshang.qingyun.price.dto.commodity.CommodityResultODTO;
import com.pinshang.qingyun.price.service.ProductPriceModelClient;
import com.pinshang.qingyun.product.dto.commodity.CommodityItemODTO;
import com.pinshang.qingyun.storage.dto.tob.CommodityInventoryDetailIDTO;
import com.pinshang.qingyun.storage.dto.tob.CommodityInventoryODTO;
import com.pinshang.qingyun.store.dto.customer.StoreSelectODTO;
import com.pinshang.qingyun.store.dto.storeSettlement.PaymentParentChildStoreSettlementODTO;
import com.pinshang.qingyun.store.dto.xda.QueryXdaUserAccountDTO;
import com.pinshang.qingyun.store.dto.xda.XdaUserAccountDTO;
import com.pinshang.qingyun.store.service.StoreManageClient;
import com.pinshang.qingyun.store.service.XdaStoreUserClient;
import com.pinshang.qingyun.tms.dto.transport.SelectWaybillAndRouteInfoIDTO;
import com.pinshang.qingyun.tms.dto.transport.WaybillAndRouteInfoODTO;
import com.pinshang.qingyun.tms.dto.waybill.SelectWaybillStatusForOrderIDTO;
import com.pinshang.qingyun.tms.dto.waybill.WaybillStatusForOrderODTO;
import com.pinshang.qingyun.tms.service.TransportClient;
import com.pinshang.qingyun.tms.service.WaybillClient;
import com.pinshang.qingyun.xda.product.dto.commodityText.SelectXdaCommodityInfoListIDTO;
import com.pinshang.qingyun.xda.product.dto.commodityText.XdaCommodityInfoODTO;
import com.pinshang.qingyun.xda.product.dto.front.XdaOrderTargetSetV2ODTO;
import com.pinshang.qingyun.xda.product.dto.front.XdaShoppingCartV2IDTO;
import com.pinshang.qingyun.xda.product.dto.shoppingCart.XdaShoppingCartV3IDTO;
import com.pinshang.qingyun.xda.product.dto.shoppingCart.XdaShoppingCartV3ODTO;
import com.pinshang.qingyun.xda.product.service.XdaCommodityFrontClient;
import com.pinshang.qingyun.xda.product.service.XdaCommodityTextClient;
import com.pinshang.qingyun.xda.product.service.XdaShoppingCartController;
import feign.codec.DecodeException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionSynchronization;
import org.springframework.transaction.support.TransactionSynchronizationAdapter;
import org.springframework.transaction.support.TransactionSynchronizationManager;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static java.util.stream.Collectors.toList;

/**
 * <AUTHOR>
 * @Date 2024/3/4 9:57
 */
@Service
@Slf4j
public class XdaOrderV4Service {
    // 订单相关
    @Resource
    private OrderMapper orderMapper;
    @Resource
    private OrderService orderService;
    @Resource
    private OrderListMapper orderListMapper;
    @Resource
    private OrderAsyncKafkaService orderAsyncKafkaService;
    @Resource
    private OrderHistoryService orderHistoryService;
    @Resource
    private OrderListGiftMapper orderListGiftMapper;
    @Resource
    private SubOrderMapper subOrderMapper;
    @Resource
    private SubOrderItemMapper subOrderItemMapper;
    // 商品相关
    @Resource
    private XdaCommodityTextClient xdaCommodityTextClient;
    @Resource
    private XdaShoppingCartController xdaShoppingCartController;
    // 客户相关
    @Resource
    private StoreManageClient storeManageClient;
    @Resource
    private RechargeService rechargeService;
    @Resource
    private XdaStoreUserClient xdaStoreUserClient;
    @Resource
    private StoreMapper storeMapper;
    @Resource
    private StoreSettlementMapper storeSettlementMapper;
    //购物车相关
    @Resource
    private XdaShoppingCartMapper xdaShoppingCartMapper;
    @Resource
    private XdaCommodityFrontClient xdaCommodityFrontClient;
    @Resource
    private StoreService storeService;
    @Resource
    private StoreDurationMapper storeDurationMapper;
    @Resource
    private CommodityFreezeGroupMapper commodityFreezeGroupMapper;
    @Resource
    private XdaShoppingCartV4Service xdaShoppingCartV4Service;
    @Resource
    private MtPromotionClient mtPromotionClient;
    @Autowired
    private OrderSalesPromotionMapper orderSalesPromotionMapper;
    @Autowired
    private BStockShortService bStockShortService;
    @Autowired
    private ToBService toBService;
    @Autowired
    private XfOrderMapper xfOrderMapper;
    @Autowired
    private XdaPreOrderMapper xdaPreOrderMapper;
    @Autowired
    private XdaPreOrderService xdaPreOrderService;
    @Autowired
    private TdaOrderService tdaOrderService;
    @Autowired
    private ProductPriceModelClient productPriceModelClient;
    @Autowired
    private WaybillClient waybillClient;
    @Lazy
    @Autowired
    private XfOrderService xfOrderService;
    @Autowired
    private RedissonClient redissonClient;
    @Autowired
    private OrderLimitQuantityService orderLimitQuantityService;
    @Autowired
    private StoreRechargeService storeRechargeService;
    @Autowired
    private OrderMtCouponService mtCouponService;
    @Autowired
    private CouponDayStatisticsService couponDayStatisticsService;
    @Autowired
    private SplitOrderSendKfkService splitOrderSendKfkService;
    @Autowired
    private XdaSaveOrderCheckService xdaSaveOrderCheckService;
    @Autowired
    private GiftLimitService giftLimitService;
    @Autowired
    private AppOrderLogService appOrderLogService;
    @Autowired
    private ShopService shopService;
    @Autowired
    private TransportClient transportClient;

    public XdaPreOrderV4ODTO preOrderView(XdaPreOrderV4IDTO xdaPreOrderV4IDTO){
        XdaPreOrderV4ODTO xdaPreOrderV4ODTO = new XdaPreOrderV4ODTO();

        // 判断是否有待支付预订单
        XdaPreOrder xdaPreOrder = xdaPreOrderService.queryNoPayXdaPreOrder(xdaPreOrderV4IDTO.getStoreId());
        if(xdaPreOrder != null){
            Boolean oldVersion = StringUtils.isNotBlank(FastThreadLocalUtil.getXDA().getAppVersion()) && FastThreadLocalUtil.getXDA().getAppVersion().compareToIgnoreCase(XdaAppVersionConstant.TDA_VERSION) < 0;
            if(oldVersion){
                QYAssert.isFalse(PreOrderTipConstant.OLD_PRE_ORDER_TIPS);
            }else {
                //QYAssert.isFalse("已有1个待支付订单，可到订单列表继续支付");
                xdaPreOrderV4ODTO.setXdaPreOrder(YesOrNoEnums.YES.getCode());
                return xdaPreOrderV4ODTO;
            }
        }
        // 通达校验截单时间
        try {
            tdaOrderService.checkDeliveryTimeRange(xdaPreOrderV4IDTO.getStoreId(), xdaPreOrderV4IDTO.getDeliverytimerange(), xdaPreOrderV4IDTO.getDeliverybatch(), true);
        } catch (BizLogicException e) {
            // 业务逻辑异常统一处理
            throw new BizLogicException(ApiErrorCodeEnum.XDA_PRE_ORDER_V4_WARN.getRemark(), ApiErrorCodeEnum.XDA_PRE_ORDER_V4_WARN);
        }


        Long storeId = xdaPreOrderV4IDTO.getStoreId();
        // 客户基本信息
        StoreSelectODTO selectODTO =storeManageClient.getStoreData(storeId);
        QYAssert.notNull(selectODTO, "客户不存在");
        // 客户账户类型
        PaymentParentChildStoreSettlementODTO xdaUserAccountDTO = storeService.queryStoreCollectPriceByStoreId(storeId, true);
        QYAssert.notNull(xdaUserAccountDTO, "客户账户不存在");
        Date orderTime = xdaPreOrderV4IDTO.getOrderTime();

        // 获取购物车信息
        BuildXdaShoppingCartV4 buildXdaShoppingCartV4 = new BuildXdaShoppingCartV4(storeId, xdaPreOrderV4IDTO.getOrderTime(), toBService, xdaShoppingCartMapper, orderMapper, orderListMapper, xdaCommodityFrontClient, xdaShoppingCartController, storeService, storeDurationMapper, commodityFreezeGroupMapper, mtPromotionClient, xdaPreOrderV4IDTO.getDeliverytimerange(), xdaPreOrderV4IDTO.getDeliverybatch(), redissonClient, xdaPreOrderV4IDTO.getCouponUserId());
        ShoppingCartV4ODTO shoppingCartV4ODTO = buildXdaShoppingCartV4.shopCartList();

        if(!shoppingCartV4ODTO.getCanSettlement()){
            throw new BizLogicException(ApiErrorCodeEnum.XDA_PRE_ORDER_V4_WARN.getRemark() , ApiErrorCodeEnum.XDA_PRE_ORDER_V4_WARN);
        }
        // 校验商品金额，品种数量以及商品数量
        //是否指定用券,1-是  0-否
        BigDecimal summation = shoppingCartV4ODTO.getSummation().setScale(2, BigDecimal.ROUND_HALF_UP);

        // 当未指定用券时，才校验金额
        boolean isAmountValid;
        if (Objects.equals(xdaPreOrderV4IDTO.getUseCoupon(), 1)) {
            //指定用券，不校验
            isAmountValid = true;
        } else {
            isAmountValid = summation.compareTo(xdaPreOrderV4IDTO.getOrderAmount()) == 0;
        }

        if (null == shoppingCartV4ODTO.getVarietySum()
                || !shoppingCartV4ODTO.getVarietySum().equals(xdaPreOrderV4IDTO.getVarietySum())
                || !isAmountValid
                || null == shoppingCartV4ODTO.getCommodityNum()
                || shoppingCartV4ODTO.getCommodityNum().compareTo(xdaPreOrderV4IDTO.getCommodityNum()) != 0) {
            throw new BizLogicException(ApiErrorCodeEnum.XDA_PRE_ORDER_V4_WARN.getRemark(), ApiErrorCodeEnum.XDA_PRE_ORDER_V4_WARN);
        }

        //校验旧版本APP（小于V1.6.2） 超出总限量或客户限购的情况
        if (StringUtils.isNotBlank(xdaPreOrderV4IDTO.getAppVersion())
                && xdaPreOrderV4IDTO.getAppVersion().compareToIgnoreCase(XdaAppVersionConstant.TDA_VERSION_162) < 0) {
            shoppingCartV4ODTO.getNormalGroup().getCommodities()
                    .stream()
                    .filter(x -> Objects.equals(x.getIsExceedLimit(), Boolean.TRUE))
                    .findAny()
                    .ifPresent(y -> QYAssert.isTrue(false, "超出特价商品限值，请下载最新版本APP后再继续下单"));
        }

        // 校验赠品是否存在缺货情况 && 兼容老版本
        List<ShoppingCartCommodityV4ODTO> giftExceedLimitCommodities = getGiftExceedLimitCommodities(shoppingCartV4ODTO);
        if (CollectionUtils.isNotEmpty(giftExceedLimitCommodities)
                && !Objects.equals(xdaPreOrderV4IDTO.getForceStatus(), YesOrNoEnums.YES.getCode())
                && xdaPreOrderV4IDTO.getAppVersion().compareToIgnoreCase(XdaAppVersionConstant.XDA_VERSION_171) >= 0) {
            // 显示赠品无货确认弹框
            xdaPreOrderV4ODTO.setIsGiftExceedLimit(true);
            xdaPreOrderV4ODTO.setGiftExceedLimitCommodities(giftExceedLimitCommodities);
            return xdaPreOrderV4ODTO;
        }

        //如果赠品存在缺货 且客户强制提交选择了是（继续结算）,则给出提示“部分赠品数量不足，请注意核对商品数量”
        if(CollectionUtils.isNotEmpty(giftExceedLimitCommodities) &&  Objects.equals(xdaPreOrderV4IDTO.getForceStatus(), YesOrNoEnums.YES.getCode())){
            xdaPreOrderV4ODTO.setNotEnoughGiftItemTips("部分赠品数量不足，请注意核对商品数量");
        }

        xdaPreOrderV4ODTO.covertStoreBaseInfo(selectODTO);
        xdaPreOrderV4ODTO.setOrderTime(orderTime);
        xdaPreOrderV4ODTO.setDeliveryTimeRange(xdaPreOrderV4IDTO.getDeliverytimerange());
//        xdaPreOrderV4ODTO.setVarietySum(shoppingCartV4ODTO.getVarietySum());
        xdaPreOrderV4ODTO.setTotalOriginPrice(shoppingCartV4ODTO.getOriginalAmount());
        xdaPreOrderV4ODTO.setDiscountTotal(shoppingCartV4ODTO.getOriginalAmount().subtract(shoppingCartV4ODTO.getSummation()));
        xdaPreOrderV4ODTO.setActuallyPaid(shoppingCartV4ODTO.getSummation());
        xdaPreOrderV4ODTO.setActivityDiscount(shoppingCartV4ODTO.getOriginalAmount().subtract(shoppingCartV4ODTO.getSummation()));

        if (Objects.nonNull(shoppingCartV4ODTO.getDiscountDetail())) {
            DiscountDetailODTO discountDetail = shoppingCartV4ODTO.getDiscountDetail();
            //券优惠
            xdaPreOrderV4ODTO.setCouponAmount(discountDetail.getCouponAmount());
            //活动优惠
            xdaPreOrderV4ODTO.setActivityDiscount(discountDetail.getActivityDiscount());
            //优惠合计
            xdaPreOrderV4ODTO.setDiscountTotal(discountDetail.getDiscountTotal());
        }

        /**
         * 获取购物车中的所有商品基本信息
         */
        List<Long> commodityIdList = getShoppingCartCommodityIdList(shoppingCartV4ODTO);
        //根据赠品的过滤结果重新设置品种合计数量
        xdaPreOrderV4ODTO.setVarietySum(shoppingCartV4ODTO.getVarietySum());
        SelectXdaCommodityInfoListIDTO infoListIDTO = new SelectXdaCommodityInfoListIDTO();
        infoListIDTO.setCommodityIdList(commodityIdList);
        List<XdaCommodityInfoODTO> xdaCommodityInfoODTOList = xdaCommodityTextClient.selectXdaCommodityInfoList(infoListIDTO);

        List<XdaOrderItemAppV4ODTO> commodities = new ArrayList<>();
        xdaCommodityInfoODTOList.forEach(item->{
            commodities.add(XdaOrderItemAppV4ODTO.covert(item));
        });
        xdaPreOrderV4ODTO.setCommodities(commodities);

        xdaPreOrderV4ODTO.setStoreType(XdaStoreTypeEnum.PRE_PAY.getCode() == xdaUserAccountDTO.getCollectStatus() ? XdaStoreTypeEnum.PRE_PAY : XdaStoreTypeEnum.POST_PAY);
        xdaPreOrderV4ODTO.setCurrentAmount(xdaUserAccountDTO.getCollectPrice());

        // 最小充值金额
        if(XdaStoreTypeEnum.PRE_PAY.equals(xdaPreOrderV4ODTO.getStoreType())){
            String minRechargeMoney = rechargeService.queryRechargeMoney(xdaPreOrderV4IDTO.getAppCode());
            xdaPreOrderV4ODTO.setMinRechargeMoney(minRechargeMoney);
        }

        // 支付方式
        PayTypeEntry payTypeEntry = rechargeService.queryValidPayMethodAndTip();
        if(null != payTypeEntry && SpringUtil.isNotEmpty(payTypeEntry.getPayTypeList())){
            xdaPreOrderV4ODTO.setPayTypeList(payTypeEntry.getPayTypeList());
        }
        //券已选数量
        xdaPreOrderV4ODTO.setCouponSelectedNumber(shoppingCartV4ODTO.getCouponId() == null ? 0 : 1);
        //券可用数量
        xdaPreOrderV4ODTO.setCouponAvailableNumber(shoppingCartV4ODTO.getCouponAvailableNumber());
        Long couponUserId = shoppingCartV4ODTO.getCouponUserId();
        if(Objects.equals(xdaPreOrderV4IDTO.getCouponUserId(), -1L)){
            couponUserId = -1L;
        }
        xdaPreOrderV4ODTO.setCouponUserId(couponUserId);

        // 计算配送费
        BigDecimal freightAmount = xdaSaveOrderCheckService.calculateFreightAmount(storeId, orderTime, shoppingCartV4ODTO);
        xdaPreOrderV4ODTO.setFreightAmount(freightAmount);
        xdaPreOrderV4ODTO.setActuallyPaid(xdaPreOrderV4ODTO.getActuallyPaid().add(freightAmount));

        if(DateUtil.get4yMd(new Date()).equals(DateUtil.get4yMd(orderTime))) {
            xdaPreOrderV4ODTO.setTodayTips("友情提示：送货日期为今天，仅支持自提，且下单后不支持取消。");
        }
        return xdaPreOrderV4ODTO;
    }

    /**
     * 预览订单 -商品详细信息
     * @param xdaPreOrderV4IDTO
     * @return
     */
    public XdaPreOrderItemV4ODTO preOrderViewCommodityListV4(XdaPreOrderV4IDTO xdaPreOrderV4IDTO){
        XdaPreOrderItemV4ODTO xdaPreOrderItemV4ODTO = XdaPreOrderItemV4ODTO.initXdaPreOrderItemV4ODTO();

        Long storeId = xdaPreOrderV4IDTO.getStoreId();
        Date orderTime = xdaPreOrderV4IDTO.getOrderTime();
        BuildXdaShoppingCartV4 buildXdaShoppingCartV4 = new BuildXdaShoppingCartV4(storeId,orderTime,toBService, xdaShoppingCartMapper, orderMapper, orderListMapper, xdaCommodityFrontClient,xdaShoppingCartController, storeService, storeDurationMapper,commodityFreezeGroupMapper,mtPromotionClient,xdaPreOrderV4IDTO.getDeliverytimerange(),xdaPreOrderV4IDTO.getDeliverybatch(),redissonClient, xdaPreOrderV4IDTO.getCouponUserId());
        ShoppingCartV4ODTO shoppingCartV4ODTO = buildXdaShoppingCartV4.shopCartList();
        if(!shoppingCartV4ODTO.getCanSettlement()){
            throw new BizLogicException(shoppingCartV4ODTO.getWarnMessage() , ApiErrorCodeEnum.XDA_PRE_ORDER_WARN);
        }
        // 校验商品金额，品种数量以及商品数量
        BigDecimal summation = shoppingCartV4ODTO.getSummation().setScale(2, BigDecimal.ROUND_HALF_UP);

        //实付金额=订单总金额+运费
        // 计算配送费
        BigDecimal freightAmount = xdaSaveOrderCheckService.calculateFreightAmount(storeId, orderTime, shoppingCartV4ODTO);
        BigDecimal actualPaid = summation.add(freightAmount);

        if(null == shoppingCartV4ODTO.getVarietySum() || !shoppingCartV4ODTO.getVarietySum().equals(shoppingCartV4ODTO.getVarietySum()) || actualPaid.compareTo(xdaPreOrderV4IDTO.getOrderAmount()) != 0
                || null == shoppingCartV4ODTO.getCommodityNum() || shoppingCartV4ODTO.getCommodityNum().compareTo(xdaPreOrderV4IDTO.getCommodityNum())!=0
        ){
            throw new BizLogicException(ApiErrorCodeEnum.XDA_PRE_ORDER_WARN.getRemark() , ApiErrorCodeEnum.XDA_PRE_ORDER_WARN);
        }

        if(SpringUtil.isNotEmpty(shoppingCartV4ODTO.getNormalGroup().getCommodities())){
            List<XdaOrderItemAppV4ODTO> normalGroup = BeanCloneUtils.copyTo(shoppingCartV4ODTO.getNormalGroup().getCommodities(),XdaOrderItemAppV4ODTO.class);
            Map<Long, ShoppingCartCommodityV4ODTO> shoppingCartCommodityV4ODTOMap = shoppingCartV4ODTO.getNormalGroup().getCommodities().stream().collect(Collectors.toMap(ShoppingCartCommodityV4ODTO::getCommodityId, e -> e));
            normalGroup.forEach(item->{
                if(shoppingCartCommodityV4ODTOMap.containsKey(item.getCommodityId())){
                    ShoppingCartCommodityV4ODTO shoppingCartCommodityV4ODTO = shoppingCartCommodityV4ODTOMap.get(item.getCommodityId());
                    item.setOriginalPrice(shoppingCartCommodityV4ODTO.getCommodityPrice());
                }
            });
            xdaPreOrderItemV4ODTO.setNormalGroup(normalGroup);
        }
        if(SpringUtil.isNotEmpty(shoppingCartV4ODTO.getThGroups().getCommodities())){
            List<ShoppingCartCommodityV4ODTO> collect = shoppingCartV4ODTO.getThGroups().getCommodities().stream().filter(item -> !item.getIsInvalidate()).collect(Collectors.toList());
            List<XdaOrderItemAppV4ODTO> thGroup = BeanCloneUtils.copyTo(collect,XdaOrderItemAppV4ODTO.class);
            if(SpringUtil.isNotEmpty(thGroup)){
                Map<Long, ShoppingCartCommodityV4ODTO> shoppingCartCommodityV4ODTOMap = shoppingCartV4ODTO.getThGroups().getCommodities().stream().collect(Collectors.toMap(ShoppingCartCommodityV4ODTO::getCommodityId, e -> e));
                thGroup.forEach(item->{
                    if(shoppingCartCommodityV4ODTOMap.containsKey(item.getCommodityId())){
                        ShoppingCartCommodityV4ODTO shoppingCartCommodityV4ODTO = shoppingCartCommodityV4ODTOMap.get(item.getCommodityId());
                        item.setOriginalPrice(shoppingCartCommodityV4ODTO.getCommodityPrice());
                    }
                });
                xdaPreOrderItemV4ODTO.setThGroup(thGroup);
            }
        }
        if(SpringUtil.isNotEmpty(shoppingCartV4ODTO.getPromotionGroup())){
            shoppingCartV4ODTO.getPromotionGroup().forEach(item->{
                XdaPreOrderItemGroupV4ODTO xdaPreOrderItemGroupV4ODTO = new XdaPreOrderItemGroupV4ODTO();
                List<XdaOrderItemAppV4ODTO> promotion = BeanCloneUtils.copyTo(item.getCommodities(), XdaOrderItemAppV4ODTO.class);
                Map<Long, ShoppingCartCommodityV4ODTO> shoppingCartCommodityV4ODTOMap = item.getCommodities().stream().filter(commodityItem-> !commodityItem.getIsGift()).collect(Collectors.toMap(ShoppingCartCommodityV4ODTO::getCommodityId, e -> e));
                promotion.forEach(promotionKey->{
                    if(shoppingCartCommodityV4ODTOMap.containsKey(promotionKey.getCommodityId())){
                        promotionKey.setOriginalPrice(shoppingCartCommodityV4ODTOMap.get(promotionKey.getCommodityId()).getCommodityPrice());
                    }
                });
                if(null != item.getFullStatus() && item.getFullStatus() == 0 && SpringUtil.isNotEmpty(promotion)){
                    promotion.forEach(promotionItem->{
                        promotionItem.setDeliveryPrice(null);
                    });
                    xdaPreOrderItemV4ODTO.getNormalGroup().addAll(promotion);
                }else {

                    //是否是买赠促销活动，否则为梯度满折
                    boolean isGiftSalesPromotion = Objects.equals(SalesPromotionStatusEnums.GIFT.getCode(), item.getCode());
                    if(isGiftSalesPromotion){
                        //将赠品为0的移除
                        promotion.removeIf(
                                promotionItem -> promotionItem.getIsGift() && promotionItem.getQuantity().compareTo(BigDecimal.ZERO) == 0
                        );
                        Optional<XdaOrderItemAppV4ODTO> giftItemOptional = promotion.stream().filter(promotionItem -> promotionItem.getIsGift() && promotionItem.getQuantity().compareTo(BigDecimal.ZERO) > 0).findAny();
                        if(!giftItemOptional.isPresent() && Objects.equals(SalesPromotionStatusEnums.GIFT.getCode(),item.getCode())){

                            xdaPreOrderItemV4ODTO.getNormalGroup().addAll(promotion);
                            return;
                        }
                    }

                    xdaPreOrderItemGroupV4ODTO.setPromotionId(item.getPromotionId());
                    xdaPreOrderItemGroupV4ODTO.setFullStatus(item.getFullStatus());
                    xdaPreOrderItemGroupV4ODTO.setCode(item.getCode());
                    CommodityPromotionV4ODTO commodityPromotionV4ODTO = item.getCommodityPromotionV4ODTO();
                    String[] result = commodityPromotionV4ODTO.getNextTips().split(";");
                    String tips = result[0] + (isGiftSalesPromotion ?"（赠完即止）":"");
                    commodityPromotionV4ODTO.setTips(tips);
                    commodityPromotionV4ODTO.setNextTips(tips);
                    xdaPreOrderItemGroupV4ODTO.setCommodityPromotionV3ODTO(commodityPromotionV4ODTO);
                    xdaPreOrderItemGroupV4ODTO.setNormalGroup(promotion);
                    xdaPreOrderItemV4ODTO.getPromotionGroups().add(xdaPreOrderItemGroupV4ODTO);
                }
            });
        }
        // 注意: 商品原价 & 包装规格 自测时需要验证
        return xdaPreOrderItemV4ODTO;
    }
    /**
     * 创建订单
     * @param xdaCreatePrePayOrderV4IDTO
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public XdaSaveOrderODTO creatOrder(XdaCreatePrePayOrderV4IDTO xdaCreatePrePayOrderV4IDTO){

        XdaPreOrder xdaPreOrder = xdaPreOrderService.queryNoPayXdaPreOrder(xdaCreatePrePayOrderV4IDTO.getStoreId());
        if(xdaPreOrder != null){
            Boolean oldVersion = StringUtils.isNotBlank(FastThreadLocalUtil.getXDA().getAppVersion()) && FastThreadLocalUtil.getXDA().getAppVersion().compareToIgnoreCase(XdaAppVersionConstant.TDA_VERSION) < 0;
            if(oldVersion){
                QYAssert.isFalse(PreOrderTipConstant.OLD_PRE_ORDER_TIPS);
            }else {
                QYAssert.isFalse(PreOrderTipConstant.NEW_PRE_ORDER_TIPS);
            }
        }

        Long storeId = xdaCreatePrePayOrderV4IDTO.getStoreId();
        XdaSaveOrderODTO xdaSaveOrderODTO = new XdaSaveOrderODTO();
        Long orderId = null;
        String orderCode = null;
        Long couponUserId = null;
        Integer businessType = null;
        try {
            xdaSaveOrderODTO.setIsSuccess(Boolean.TRUE);
            Date orderTime = xdaCreatePrePayOrderV4IDTO.getOrderTime();

            // 1.获取购物车数据
            ShoppingCartV4ODTO shoppingCartV4ODTO = getShoppingCart(storeId, orderTime, xdaCreatePrePayOrderV4IDTO.getDeliverytimerange(), xdaCreatePrePayOrderV4IDTO.getDeliverybatch(), xdaCreatePrePayOrderV4IDTO.getCouponUserId());

            // 2.保存订单统一校验,并保存订单
            Order order = xdaSaveOrderCheckService.xdaCreateOrderCheckAndSaveOrder(xdaCreatePrePayOrderV4IDTO, false, true, shoppingCartV4ODTO, xdaCreatePrePayOrderV4IDTO.getLogisticscenterid());
            orderId = order.getId();
            businessType = order.getBusinessType();
            orderCode = order.getOrderCode();
            xdaSaveOrderODTO.setOrderId(order.getId());
            if(StringUtils.isNotBlank(order.getErrorMsg())) {
                // 调用解冻库存
                toBService.warehouseUnfreezeInventory(orderId, xdaCreatePrePayOrderV4IDTO.getStoreId(),order.getBusinessType());
                xdaSaveOrderODTO.setIsSuccess(Boolean.FALSE);
                xdaSaveOrderODTO.setErrorMsg(order.getErrorMsg());
                return xdaSaveOrderODTO;
            }

            //3.保存促销方案
            savePromotion(shoppingCartV4ODTO,order.getId());

            // 4.优惠券核销
            couponUserId = shoppingCartV4ODTO.getCouponUserId();
            mtCouponService.useCoupon(couponUserId, order.getStoreId(), order.getOrderCode());

            // 5.扣款 预付款用户保存付款单
            if (XdaStoreTypeEnum.PRE_PAY.equals(xdaCreatePrePayOrderV4IDTO.getStoreType())) {
                saveOrderDeductions(order, storeId, XdaPayTypeEnum.DEDUCTION, "", null);
            }

            // 6.清空购物车
            xdaShoppingCartV4Service.clearV4(storeId,null);

            // 7、记录订单日志
            orderHistoryService.insertOrderHistoryOnCreateOrder(order);

            //8、B端买赠记录赠品总量已赠数量
            updateGiftTotalLimitQuantity(shoppingCartV4ODTO,orderId);

            // 9 记录设备号
            appOrderLogService.saveAppOrderLog(orderId,
                    order.getStoreId(),
                    order.getOrderTime(),
                    xdaCreatePrePayOrderV4IDTO.getDeviceid(),
                    xdaCreatePrePayOrderV4IDTO.getSystemtype(),
                    xdaCreatePrePayOrderV4IDTO.getSystemversion());

            // 10.事务提交后 发送kafka消息
            Long finalOrderId = orderId;
            TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronization() {
                @Override
                public void afterCommit() {
                    // 1.发送结算消息
                    orderAsyncKafkaService.sendKafkaSaveOrderMessage(order);

                    // 2.通达给物流发送消息
                    Boolean isTdaStore = tdaOrderService.isTdaOrBCountry(order.getStoreId());
                    if(isTdaStore){
                        TdaOrderSyncTmsIDTO tdaOrderSyncTmsIDTO = TdaOrderSyncTmsIDTO.forOrder(order.getId(), XdaOrderProcessStatusEunm.WAITING_PICK.getCode());
                        tdaOrderService.tdaOrderSyncToTms(tdaOrderSyncTmsIDTO);
                    }

                    // 3.发送拆单消息
                    sendSplitOrderMsg(order.getId(), orderTime, xdaCreatePrePayOrderV4IDTO.getUserId(),order.getCommWarehouseMap());

                    // 4.维护B端特价限购记录表
                    orderLimitQuantityService.saveOrderLimitQuantity(finalOrderId, OperateTypeEnums.新增.getCode(),false);

                    // 5.查询订单优惠券信息，实时维护优惠券统计表
                    couponDayStatisticsService.saveOrUpdateCouponDayStatistics(finalOrderId, OperateTypeEnums.新增.getCode());
                }
            });

        }catch (Exception exception){
            //冻结成功，如果出现异常，也能获取到orderId，用于解冻
            log.error("订单创建异常调用解冻 orderId = {}", orderId);
            toBService.warehouseUnfreezeInventory(orderId, xdaCreatePrePayOrderV4IDTO.getStoreId(),businessType);

            try {
                //返还券
                mtCouponService.refundCoupon(orderCode, couponUserId, storeId);
            }catch (Exception e){
                log.warn("鲜达保存订单失败,返还券失败 orderCode  {} couponUserId {}", orderCode, couponUserId, e);
            }

            if(exception instanceof BizLogicException || exception instanceof DecodeException){
                throw exception;
            }else {
                throw new BizLogicException("订单创建失败");
            }

        }
        return xdaSaveOrderODTO;
    }

    private void updateGiftTotalLimitQuantity(ShoppingCartV4ODTO shoppingCartV4ODTO,Long orderId){

        List<ShoppingCartGroupV4ODTO> promotionGroupList = shoppingCartV4ODTO.getPromotionGroup();
        if(SpringUtil.isNotEmpty(promotionGroupList)){
            List<GiftLimitQuantitySaveIDTO> saveIDTOList = promotionGroupList.stream().flatMap(promotionGroup ->

                    promotionGroup.getCommodities().stream().filter(shoppingCartCommodityV4ODTO -> shoppingCartCommodityV4ODTO.getIsGift() && shoppingCartCommodityV4ODTO.getQuantity().compareTo(BigDecimal.ZERO) > 0).map(
                            commodity -> {
                                Long commodityId = commodity.getCommodityId();
                                BigDecimal totalQuantity = commodity.getQuantity();
                                Long giftModelId = Long.parseLong(promotionGroup.getPromotionId());
                                GiftLimitQuantitySaveIDTO saveIDTO = new GiftLimitQuantitySaveIDTO();
                                saveIDTO.setOrderId(orderId);
                                saveIDTO.setTotalQuantity(totalQuantity);
                                saveIDTO.setGiftPrommotionId(giftModelId);
                                saveIDTO.setCommodityId(commodityId);
                                return saveIDTO;
                            }
                    )).collect(toList());

            if(SpringUtil.isNotEmpty(saveIDTOList)){
                giftLimitService.saveGiftLimitQuantity(saveIDTOList);
            }
        }
    }


    /**
     * 发送拆单消息
     * @param orderId
     * @param orderTime
     * @param userId
     */
    private void sendSplitOrderMsg(Long orderId, Date orderTime, Long userId, Map<Long, Long> commWarehouseMap) {
        SplitOrderKafkaVo splitOrderKafkaVo = new SplitOrderKafkaVo();
        splitOrderKafkaVo.setOrderId(orderId);
        splitOrderKafkaVo.setOrderTime(orderTime);
        splitOrderKafkaVo.setType(KafkaMessageOperationTypeEnum.INSERT);
        splitOrderKafkaVo.setCreateId(userId);
        splitOrderKafkaVo.setEnterpriseId(78L);
        splitOrderKafkaVo.setCommWarehouseMap(commWarehouseMap);
        //splitOrderService.execute(splitOrderKafkaVo, splitOrderService, weChatSendMessageService);
        splitOrderSendKfkService.sendSplitOrderKfkMsg(splitOrderKafkaVo);
    }

    /**
     * 订单取消
     * @param orderCancelV4IDTO
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public Integer cancelOrderV4(OrderCancelV4IDTO orderCancelV4IDTO){
        Long orderId = orderCancelV4IDTO.getOrderId();
        Long storeId = orderCancelV4IDTO.getStoreId();
        Order order = orderMapper.selectByPrimaryKey(orderId);
        QYAssert.isTrue(null != order , "订单不存在!");
        QYAssert.isTrue(order.getCreateId().equals(storeId), "操作失败，只能取消自己创建的订单！");

        Boolean isTdaStore = DeliveryOrderTypeEnums.TD_SALE.getCode().equals(order.getBusinessType());
        Boolean isCountryStore = DeliveryOrderTypeEnums.B_COUNTRY.getCode().equals(order.getBusinessType());

        // 校验是否可以取消
        checkCancelOrderValidity(order,true);

        Map<String,Object> paramMap = new HashMap<String, Object>(2);
        paramMap.put("orderId", order.getId());
        paramMap.put("orderStatus", 2);
        paramMap.put("cancelReasonId", orderCancelV4IDTO.getReasonOptionId());
        if(isTdaStore||isCountryStore){
            paramMap.put("processStatus", XdaOrderProcessStatusEunm.CANCEL.getCode());
        }
        Integer rowNumber = orderMapper.updateOrderStatusByParameter(paramMap);
        QYAssert.isTrue(rowNumber > 0 , "订单取消失败!");
        // 取消子订单
        int subOrderRows = subOrderMapper.cancelSubOrder(order.getId());
        QYAssert.isTrue(subOrderRows > 0 , "子订单取消失败!");
        // 客户账户类型
        QueryXdaUserAccountDTO queryXdaUserAccountDTO = new QueryXdaUserAccountDTO();
        queryXdaUserAccountDTO.setStoreId(storeId);
        XdaUserAccountDTO xdaUserAccountDTO = xdaStoreUserClient.queryUserAccountInfo(queryXdaUserAccountDTO);
        QYAssert.notNull(xdaUserAccountDTO, "客户账户不存在");

        // 通达取消订单校验
        cancelOrderProcessStatusCheck(order);

        //商品库存解冻
        if(!toBService.warehouseUnfreezeInventory(order.getId(), storeId,order.getBusinessType())){
            QYAssert.isFalse("订单取消失败!");
        }

        // 返还优惠券
        mtCouponService.refundCoupon(orderId, order.getStoreId());

        // 回款
        if(XdaStoreTypeEnum.PRE_PAY.equals(xdaUserAccountDTO.getStoreType())){
            saveOrderDeductions(order, storeId, XdaPayTypeEnum.PAY_BACK, "线上订单取消",StoreBillTypeEnums.ONLINE_CANCEL.getCode());
        }

        //订单取消回退买赠总数量
        giftLimitService.deleteGiftLimitQuantity(Arrays.asList(order.getId()));

        // 发送取消订单消息给统计查询
        log.info("start send kafka msg: orderId={} ", orderId);
        TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronizationAdapter() {
            @Override
            public void afterCommit() {
                orderAsyncKafkaService.sendKafkaCancelOrder(order);

                if(isTdaStore||isCountryStore){
                    // 发送消息，通知物流
                    TdaOrderSyncTmsIDTO tdaOrderSyncTmsIDTO = TdaOrderSyncTmsIDTO.forOrder(order.getId(), XdaOrderProcessStatusEunm.CANCEL.getCode());
                    tdaOrderService.tdaOrderSyncToTms(tdaOrderSyncTmsIDTO);
                }

                // 维护B端特价限购记录表
                orderLimitQuantityService.saveOrderLimitQuantity(order.getId(), OperateTypeEnums.删除.getCode(),  false);

                // 查询订单优惠券信息，实时维护优惠券统计表
                couponDayStatisticsService.saveOrUpdateCouponDayStatistics(order.getId(), OperateTypeEnums.删除.getCode());
            }
        });

        // 记录订单日志
        orderHistoryService.insertOrderHistoryOnCancelOrder(order);
        return rowNumber;
    }


    /**
     * 已支付订单满足如下条件：可取消
     * 3、通达流程状态为：待拣货、出库中、待揽收
     * 4、B端全国流程状态为：待拣货
     * 5、非通达、非B端全国流程状态为：待发货
     */
    public void cancelOrderProcessStatusCheck(Order order) {
        boolean checkStatus = isProcessStatusCheck(order);

        // 待拣货的订单取消成功后，需要通知给大仓 ，大仓需要释放冻结的库存，同时也需要通知物流
        // 出库中、待揽收的订单，取消成功后，消息通知给物流进行拦截
        if (checkStatus) {
            boolean isTdaStore = DeliveryOrderTypeEnums.TD_SALE.getCode().equals(order.getBusinessType());
            boolean isCountryStore = DeliveryOrderTypeEnums.B_COUNTRY.getCode().equals(order.getBusinessType());
            boolean isBigShop = BusinessTypeEnums.BIGSHOP_SALE.getCode().equals(order.getBusinessType());
            if (isTdaStore || isCountryStore || isBigShop) {
                SelectWaybillStatusForOrderIDTO statusForOrderIDTO = SelectWaybillStatusForOrderIDTO.forCancelOrder(order.getId());
                WaybillStatusForOrderODTO waybillStatusForOrderODTO = waybillClient.selectWaybillStatusForOrder(statusForOrderIDTO);
                if(!waybillStatusForOrderODTO.getAllow()){
                    QYAssert.isFalse(waybillStatusForOrderODTO.getErrorMsg());
                }
            }
        }else {
            QYAssert.isFalse("订单状态异常，不能取消!");
        }

    }

    /**
     * 订单复制
     * @param orderId 订单id
     * @return 是否成功
     */
    @Transactional(rollbackFor = Exception.class)
    public Integer copyOrderByIdXdaAppV4(Long orderId,String orderDate){
        QYAssert.isTrue(null !=orderId , "orderId is not null !");
        QYAssert.isTrue(null !=orderDate , "orderDate is not null !");
        XdaTokenInfo tokenInfo = FastThreadLocalUtil.getXDA();
        Long storeId =  tokenInfo.getStoreId();
        List<OrderItemV2Entry> commList = this.orderMapper.queryOrderItemCopyV2(orderId);
        QYAssert.isTrue(SpringUtil.isNotEmpty(commList), "该订单有误,无法复制!");
        xdaShoppingCartV4Service.clearV4(storeId,null);
        List<Long> commodityIdList = commList.stream().filter(item -> item.getCommodityType().equals(ProductTypeEnums.TH.getCode())).map(OrderItemV2Entry::getCommodityId).collect(Collectors.toList());
        List<Long> xdaSpecialsCommodityList = new ArrayList<>();
        if(SpringUtil.isNotEmpty(commodityIdList)){
            xdaSpecialsCommodityList = xdaCommodityFrontClient.selectXdaSpecialsCommoditySet(commodityIdList);
        }
        //查询是否有特惠方案
        XdaShoppingCartV2IDTO xdaShoppingCartV2IDTO = new XdaShoppingCartV2IDTO();
        xdaShoppingCartV2IDTO.setStoreId(storeId);
        xdaShoppingCartV2IDTO.setOrderTime(DateUtil.parseDate(orderDate,"yyyy-MM-dd"));
        BigDecimal orderTargetByStoreIdAndOrderTime = xdaCommodityFrontClient.findOrderTargetByStoreIdAndOrderTime(xdaShoppingCartV2IDTO);

        List<XdaShoppingCart> xdaShoppingCartList = new ArrayList<>();
        List<Long> finalXdaSpecialsCommodityList = xdaSpecialsCommodityList;
        commList.forEach(item -> {
            XdaShoppingCart xdaShoppingCart = new XdaShoppingCart();
            if(item.getCommodityType().equals(1)){
                xdaShoppingCart.setStoreId(storeId);
                xdaShoppingCart.setCommodityId(item.getCommodityId());
                xdaShoppingCart.setCommodityType(item.getCommodityType());
                xdaShoppingCart.setQuantity(item.getCommodityNum());
                xdaShoppingCartList.add(xdaShoppingCart);
            }else if(item.getCommodityType().equals(ProductTypeEnums.TH.getCode()) && finalXdaSpecialsCommodityList.contains(item.getCommodityId()) && null != orderTargetByStoreIdAndOrderTime){
                xdaShoppingCart.setStoreId(storeId);
                xdaShoppingCart.setCommodityId(item.getCommodityId());
                xdaShoppingCart.setCommodityType(2);
                xdaShoppingCart.setQuantity(item.getCommodityNum());
                xdaShoppingCartList.add(xdaShoppingCart);
            }
        });

        List<XdaShoppingCart> resultList = new ArrayList<>();
        // 称重品合并数量
        Map<String, List<XdaShoppingCart>> map = xdaShoppingCartList.stream().collect(Collectors.groupingBy(item -> {
            return item.getCommodityType() + "" + item.getCommodityId();
        }));

        if(!map.isEmpty()){
            resultList = new ArrayList<>();
            for (Map.Entry<String, List<XdaShoppingCart>> entry : map.entrySet()) {
                List<XdaShoppingCart> itemList = entry.getValue();
                XdaShoppingCart xdaShoppingCart = itemList.get(0);
                BigDecimal quantity = itemList.stream().map(XdaShoppingCart::getQuantity).reduce(BigDecimal.ZERO, BigDecimal::add);
                xdaShoppingCart.setQuantity(quantity);
                resultList.add(xdaShoppingCart);
            }
        }

        return xdaShoppingCartMapper.insertList(resultList);
    }


    /**
     * 查询当前客户订单列表
     * @param param
     * @return
     */
    public PageInfo<XdaOrderAppV4ODTO> queryOrderListByPageV4(XdaQueryOrderAppParamV4 param, String appVersion){
        PageInfo<XdaOrderAppV4ODTO> page = PageHelper.startPage(param.getPageNo(), param.getPageSize()).doSelectPageInfo(() -> {
            orderMapper.queryOrderXdaAppV4(param);
        });
        List<XdaOrderAppV4ODTO> xdaOrder4AppODTOS = page.getList();
        Boolean orderListNotEmpty = CollectionUtils.isNotEmpty(xdaOrder4AppODTOS);

        // 新版本订单列表返回未支付的预订单
        if(StringUtils.isNotBlank(appVersion) && appVersion.compareToIgnoreCase(XdaAppVersionConstant.TDA_VERSION) >= 0 && param.getPageNo() == 1){
            // 查询是否有待支付的预订单，有的话放在第一个
            Integer count = xdaPreOrderMapper.countXdaNoPayPreOrder(param.getStoreId());
            if(count > 0){
                // 返回给app的时间+2秒,防止主从同步或者job刷新原因导致app刷列表订单状态没有改变
                XdaOrderAppV4ODTO xdaOrderAppV4ODTO = xdaPreOrderMapper.queryXdaNoPayPreOrder(param.getStoreId());
                if(xdaOrderAppV4ODTO != null) {
                    xdaOrderAppV4ODTO.setCurrentCountdown(xfOrderService.orderListCountdown(xdaOrderAppV4ODTO.getOrderCreateTime()));
                    xdaOrderAppV4ODTO.setXdaPreOrder(YesOrNoEnums.YES.getCode());

                    // 现付订单置为成功
                    XfOrder xfOrder = new XfOrder();
                    xfOrder.setBillCode(xdaOrderAppV4ODTO.getBillCode());
                    XfOrder xf = xfOrderMapper.selectOne(xfOrder);
                    if(xf != null){
                        xdaOrderAppV4ODTO.setXfPayAmount(xf.getPayAmount());
                        xdaOrderAppV4ODTO.setAccountPayAmount(xdaOrderAppV4ODTO.getSummation().subtract(xf.getPayAmount()));
                    }

                    List<XdaOrderItemAppV4ODTO> odtos = xdaPreOrderMapper.queryXdaPreOrderItemXdaAppV4(Collections.singletonList(xdaOrderAppV4ODTO.getOrderId()));
                    Set<Long> commodityIds = odtos.stream().map(XdaOrderItemAppV4ODTO :: getCommodityId).collect(Collectors.toSet());
                    SelectXdaCommodityInfoListIDTO infoListIDTO = new SelectXdaCommodityInfoListIDTO();
                    infoListIDTO.setCommodityIdList(new ArrayList<>(commodityIds));
                    List<XdaCommodityInfoODTO> xdaCommodityInfoODTOList = xdaCommodityTextClient.selectXdaCommodityInfoList(infoListIDTO);
                    QYAssert.notEmpty(xdaCommodityInfoODTOList, "商品基本信息为空");
                    Map<Long,XdaCommodityInfoODTO> xdaCommodityInfoODTOMap = xdaCommodityInfoODTOList.stream().collect(Collectors.toMap(XdaCommodityInfoODTO::getCommodityId, Function.identity()));

                    odtos.forEach(commodity->{
                        if(xdaOrderAppV4ODTO.getCommodities() == null){
                            xdaOrderAppV4ODTO.setCommodities(new ArrayList<>());
                        }
                        XdaCommodityInfoODTO odto = xdaCommodityInfoODTOMap.get(commodity.getCommodityId());
                        if(odto == null) {return;}
                        commodity.setCommodityName(odto.getCommodityAppName());
                        commodity.setCommodityUnitName(odto.getCommodityUnitName());
                        commodity.setCommoditySpec(odto.getCommoditySpec());
                        commodity.setImageUrl(odto.getDefaultPicUrl());
                        xdaOrderAppV4ODTO.getCommodities().add(commodity);
                    });
                    xdaOrder4AppODTOS.add(0, xdaOrderAppV4ODTO);
                }
            }
        }

        if(xdaOrder4AppODTOS.isEmpty()){
            return page;
        }

        // 如果只有一条预订单就不走此方法
        if(orderListNotEmpty){
            covertCommodityInfo(xdaOrder4AppODTOS);
        }

        xdaOrder4AppODTOS.forEach(item->{
            if(CollectionUtils.isNotEmpty(item.getCommodities())){
                List<XdaOrderItemAppV4ODTO> uniqueByCommodityId = item.getCommodities().stream().collect(
                        Collectors.collectingAndThen(
                                Collectors.toCollection(() -> new TreeSet<>(Comparator.comparing(XdaOrderItemAppV4ODTO::getCommodityId))), ArrayList::new));
                item.setCommodities(uniqueByCommodityId);
                if (!Objects.equals(item.getXdaPreOrder(), 1)) {
                    // 现付单取消逻辑 由 app端处理
                    boolean canCancelOrder = checkCancelOrderValidity(BeanCloneUtils.copyTo(item, Order.class), false);
                    item.setCanCancelOrder(canCancelOrder);
                }
            }
        });
        page.setList(xdaOrder4AppODTOS);
        return page;
    }
    /**
     * 鲜达 查询订单详情
     * @param orderCode
     * @return
     */
    public XdaOrderAppV4ODTO queryOrderDetailByCodeV4(String orderCode,Long storeId){
        XdaQueryOrder4AppParam param = new XdaQueryOrder4AppParam();
        param.setOrderCode(orderCode);
        List<XdaOrderAppV4ODTO> xdaOrderAppODTOS = orderMapper.queryOrderDetailByCodeV4(param);

        QYAssert.notEmpty(xdaOrderAppODTOS, "订单不存在");
        XdaOrderAppV4ODTO xdaOrderAppV4ODTO = covertCommodityInfo(xdaOrderAppODTOS).get(0);
        // 客户基本信息
        StoreSelectODTO selectODTO =storeManageClient.getStoreData(storeId);
        QYAssert.notNull(selectODTO, "客户不存在");
        xdaOrderAppV4ODTO.covertStoreBaseInfo(selectODTO);
        // 客户账户类型
        QueryXdaUserAccountDTO queryXdaUserAccountDTO = new QueryXdaUserAccountDTO();
        queryXdaUserAccountDTO.setStoreId(storeId);
        XdaUserAccountDTO xdaUserAccountDTO = xdaStoreUserClient.queryUserAccountInfo(queryXdaUserAccountDTO);
        QYAssert.notNull(xdaUserAccountDTO, "客户账户不存在");
        xdaOrderAppV4ODTO.setStoreType(xdaUserAccountDTO.getStoreType());
        //如果商品金额(原价总计) 为空则表示为历史订单特殊处理
        BigDecimal couponAmount = Objects.isNull(xdaOrderAppV4ODTO.getCouponAmount()) ? BigDecimal.ZERO : xdaOrderAppV4ODTO.getCouponAmount();
        if(null == xdaOrderAppV4ODTO.getTotalOriginPrice()){
            Long orderId = Long.valueOf(xdaOrderAppV4ODTO.getOrderId());
            List<XdaOrderItemAppV2ODTO> xdaOrderItemAppV2ODTOS = orderListMapper.queryOrderCommodityDetailById(orderId);
            BigDecimal totalOriginPrice = xdaOrderItemAppV2ODTOS.stream().collect(CollectorsUtil.summingBigDecimalMax(
                    item -> {
                        if(null != item.getCommodityNum() && null != item.getCommodityPrice()){
                            return item.getCommodityNum().multiply(item.getCommodityPrice().setScale(2, BigDecimal.ROUND_HALF_UP));
                        }
                        return BigDecimal.ZERO;
                    }));
            xdaOrderAppV4ODTO.setTotalOriginPrice(totalOriginPrice);
            couponAmount = xdaOrderItemAppV2ODTOS.stream().map(XdaOrderItemAppV2ODTO::getCouponDiscountAmount).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add);
            xdaOrderAppV4ODTO.setCouponAmount(couponAmount);

        }
        xdaOrderAppV4ODTO.setActivityDiscount(xdaOrderAppV4ODTO.getTotalOriginPrice().subtract(xdaOrderAppV4ODTO.getSummation()).subtract(couponAmount).setScale(2,BigDecimal.ROUND_HALF_UP));
        xdaOrderAppV4ODTO.setDiscountTotal(xdaOrderAppV4ODTO.getTotalOriginPrice().subtract(xdaOrderAppV4ODTO.getSummation()).setScale(2,BigDecimal.ROUND_HALF_UP));
        List<XdaOrderItemAppV4ODTO> uniqueByCommodityId = xdaOrderAppV4ODTO.getCommodities().stream().collect(
                Collectors.collectingAndThen(
                        Collectors.toCollection(() -> new TreeSet<>(Comparator.comparing(XdaOrderItemAppV4ODTO::getCommodityId))), ArrayList::new));
        xdaOrderAppV4ODTO.setCommodities(uniqueByCommodityId);

        // 有运费，实付金额要加上运费
        BigDecimal freightAmount = xdaOrderAppV4ODTO.getFreightAmount();
        if (null != freightAmount && freightAmount.compareTo(BigDecimal.ZERO) > 0) {
            xdaOrderAppV4ODTO.setSummation(xdaOrderAppV4ODTO.getSummation().add(freightAmount));
        }

        // 订单是否可以被取消（app端展示取消订单按钮的判断依据）
        boolean canCancelOrder = checkCancelOrderValidity(BeanCloneUtils.copyTo(xdaOrderAppV4ODTO, Order.class), false);
        xdaOrderAppV4ODTO.setCanCancelOrder(canCancelOrder);
        return xdaOrderAppV4ODTO;

    }

    /**
     * 根据订单编码  查询订单商品详情
     * 1、查询订单商品
     * 2、查询商品详细信息
     * 3、组装订单商品详情
     * 4、处理实付、实发  如果全部子单部分出库或无出库,实发实付赋值为null
     * @param orderCode
     * @return
     */
    public XdaPreOrderItemV3ODTO queryXdaOrderDetailV4(String orderCode){
        XdaPreOrderItemV3ODTO xdaPreOrderItemV3ODTO = new XdaPreOrderItemV3ODTO();
        Order order = orderMapper.findOrderInfo(orderCode);
        QYAssert.notNull(order, "订单不存在");
        List<XdaOrderItemAppV3ODTO> xdaOrderAppODTOS = orderListMapper.queryOrderCommodityDetailV3ById(order.getId());
        QYAssert.notEmpty(xdaOrderAppODTOS, "订单不存在");
        //查询商品详情
        List<Long> commodityIdList = xdaOrderAppODTOS.stream().map(XdaOrderItemAppV3ODTO::getCommodityId).distinct().collect(Collectors.toList());

        SelectXdaCommodityInfoListIDTO infoListIDTO = new SelectXdaCommodityInfoListIDTO();
        infoListIDTO.setCommodityIdList(new ArrayList<>(commodityIdList));
        List<XdaCommodityInfoODTO> xdaCommodityInfoODTOList = xdaCommodityTextClient.selectXdaCommodityInfoList(infoListIDTO);
        Map<Long, XdaCommodityInfoODTO> commodityInfoMap = xdaCommodityInfoODTOList.stream().collect(Collectors.toMap(XdaCommodityInfoODTO::getCommodityId, e -> e));

        /**
         * 判断子单出库次数
         */
        List<OrderRealDeliveryFinishEntry> deliveryFinishEntryList = subOrderMapper.findOrderRealDeliveryFinishList(Arrays.asList(order.getId()));
        Map<Long,OrderRealDeliveryFinishEntry> deliveryFinishEntryMap = SpringUtil.isNotEmpty(deliveryFinishEntryList) ? deliveryFinishEntryList.stream().collect(
                Collectors.toMap(OrderRealDeliveryFinishEntry::getOrderId, Function.identity())) : new HashMap<>();
        OrderRealDeliveryFinishEntry deliveryFinishEntry = deliveryFinishEntryMap.get(order.getId());
        Boolean isAllOutbound = Boolean.TRUE;
        if(deliveryFinishEntry == null || deliveryFinishEntry.getSubNum().compareTo(deliveryFinishEntry.getRealFinishNum())!=0){
            isAllOutbound = Boolean.FALSE;
        }

        //商品新增组装
        Boolean finalIsAllOutbound = isAllOutbound;
        xdaOrderAppODTOS.forEach(item->{
            // 1期特价限购版本处理
            if(item.getPricePromotionId() != null && item.getPricePromotionId() > 0){
                item.setSpecialPrice(item.getCommodityPrice());
            }
            Long commodityId = item.getCommodityId();
            if(!finalIsAllOutbound){
                item.setRealDeliveryQuantity(null);
            }
            if(commodityInfoMap.containsKey(commodityId)){
                XdaCommodityInfoODTO xdaCommodityInfoODTO = commodityInfoMap.get(commodityId);
                item.setCommodityName(xdaCommodityInfoODTO.getCommodityAppName());
                item.setCommodityUnitName(xdaCommodityInfoODTO.getCommodityUnitName());
                item.setImageUrl(xdaCommodityInfoODTO.getDefaultPicUrl());
                item.setCommoditySpec(xdaCommodityInfoODTO.getCommoditySpec());
                item.setCommodityPackageSpec(xdaCommodityInfoODTO.getCommodityPackageSpec());
                item.setSalesBoxCapacity(xdaCommodityInfoODTO.getSalesBoxCapacity());
            }
            if(null != item.getSpecialPrice()){
                item.setIsSpecialPrice(1);
            }
            if(item.getCommodityType().equals(ProductTypeEnums.TH.getCode())){
                item.setThPrice(item.getCommodityPrice());
                item.setIsThPrice(1);
            }
            if(null != item.getGiftModelId()){
                item.setDeliveryPrice(item.getCommodityPrice());
            }
            //如果商品金额(原价总计) 为空则表示为历史订单特殊处理
            if(null == item.getOriginalPrice() && null != item.getCommodityPrice()){
                item.setOriginalPrice(item.getCommodityPrice());
            }
            item.setRealPrice(item.getTotalPrice());

        });
        /**
         * 订单商品根据商品类型进行分组
         */
        Map<Integer, List<XdaOrderItemAppV3ODTO>> orderCommodityListByCommodityTypeMap = xdaOrderAppODTOS.stream().collect(Collectors.groupingBy(XdaOrderItemAppV3ODTO::getCommodityType));
        List<XdaOrderItemAppV3ODTO> normalGroupCommodity = orderCommodityListByCommodityTypeMap.get(ProductTypeEnums.PRODUCT.getCode());
        List<XdaOrderItemAppV3ODTO> giftGroupCommodity = orderCommodityListByCommodityTypeMap.get(ProductTypeEnums.GIFT.getCode());
        //查询赠品原价，数据库里赠品存的是0
        Map<Long, CommodityResultODTO> giftPriceMap = new HashMap<>();
        if (SpringUtil.isNotEmpty(giftGroupCommodity)) {
            List<Long> giftCommodityIdList = giftGroupCommodity.stream().map(XdaOrderItemAppV3ODTO::getCommodityId).collect(Collectors.toList());
            CommodityListRequestIDTO idto = new CommodityListRequestIDTO();
            idto.setStoreId(String.valueOf(order.getStoreId()));
            idto.setCommodityIdListAll(giftCommodityIdList);
            List<CommodityResultODTO> storeCommodityList = productPriceModelClient.findStoreCommodityList(idto);
            giftPriceMap = storeCommodityList.stream().collect(Collectors.toMap(x -> Long.valueOf(x.getCommodityId()), Function.identity()));
        }

        //订单明细商品查询 需要自己保存促销数据用于展示 OrderSalesPromotionMapper
        List<XdaOrderItemAppV3ODTO> normalCommodityList = normalGroupCommodity.stream().filter(item -> null == item.getGiftModelId()).collect(Collectors.toList());
        xdaPreOrderItemV3ODTO.setNormalGroup(normalCommodityList);
        List<XdaOrderItemAppV3ODTO> promotionCommodityList = normalGroupCommodity.stream().filter(item -> null != item.getGiftModelId()).collect(Collectors.toList());

        List<String> giftModelIdList = promotionCommodityList.stream().map(XdaOrderItemAppV3ODTO::getGiftModelId).collect(Collectors.toList());
        if(SpringUtil.isNotEmpty(giftModelIdList)){
            //查询促销信息
            Example itemEx =new Example(OrderSalesPromotion.class);
            itemEx.createCriteria().andEqualTo("orderId", order.getId()).andIn("promotionId",giftModelIdList);
            List<OrderSalesPromotion> orderSalesPromotionList = orderSalesPromotionMapper.selectByExample(itemEx);

            Map<Integer, List<XdaOrderItemAppV3ODTO>> commodityTypeMap = promotionCommodityList.stream().collect(Collectors.groupingBy(XdaOrderItemAppV3ODTO::getCommodityType));
            List<XdaPreOrderItemGroupV3ODTO> xdaPreOrderItemGroupV3ODTOList = new ArrayList<>();
            Map<Long, CommodityResultODTO> finalGiftPriceMap = giftPriceMap;
            orderSalesPromotionList.forEach(item->{
                XdaPreOrderItemGroupV3ODTO xdaPreOrderItemGroupV3ODTO = new XdaPreOrderItemGroupV3ODTO();
                CommodityPromotionV3ODTO commodityPromotionV3ODTO = new CommodityPromotionV3ODTO();
                String tips = item.getTips() + (SalesPromotionStatusEnums.GIFT.getCode() == item.getPromotionType().intValue()?"（赠完即止）":"");
                commodityPromotionV3ODTO.setNextTips(tips);
                commodityPromotionV3ODTO.setTips(tips);
                commodityPromotionV3ODTO.setPromotionType(item.getPromotionType());
                String name = "";
                List<String> remarkList = new ArrayList<>();
                if(null != item.getPromotionType()){
                    if (SalesPromotionStatusEnums.GIFT.getCode() == item.getPromotionType().intValue()) {
                        name =  "买赠";
                        remarkList = CommodityPromotionV3ODTO.GIFT_REMARK_LIST;

                    } else if (SalesPromotionStatusEnums.GRADIENT_DISCOUNT.getCode() == item.getPromotionType().intValue()) {
                        name = "梯度满折";
                        remarkList = CommodityPromotionV3ODTO.GRADIENT_DISCOUNT_REMARK_LIST;

                    }
                }
                List<String> list = Arrays.asList(item.getPromotionRules().split(","));
                commodityPromotionV3ODTO.setPromotionTypeName(name);
                commodityPromotionV3ODTO.setRuleList(list);
                commodityPromotionV3ODTO.setRemarkList(remarkList);
                xdaPreOrderItemGroupV3ODTO.setCommodityPromotionV3ODTO(commodityPromotionV3ODTO);
                List<XdaOrderItemAppV3ODTO> normalGroup = new ArrayList<>();
                List<XdaOrderItemAppV3ODTO> collect = promotionCommodityList.stream().filter(xdaItem -> xdaItem.getGiftModelId().equals(item.getPromotionId()+"")).collect(Collectors.toList());
                if(SpringUtil.isNotEmpty(collect)){
                    if(SalesPromotionStatusEnums.GRADIENT_DISCOUNT.getCode() != item.getPromotionType().intValue()){
                        for (XdaOrderItemAppV3ODTO xdaOrderItemAppV3ODTO : collect) {
                            xdaOrderItemAppV3ODTO.setDeliveryPrice(null);
                        }
                    }else {
                        for (XdaOrderItemAppV3ODTO xdaOrderItemAppV3ODTO : collect) {
                            if(xdaOrderItemAppV3ODTO.getCommodityUnitName().trim().equals("kg") && null != xdaOrderItemAppV3ODTO.getDeliveryPrice()){
                                BigDecimal divide = xdaOrderItemAppV3ODTO.getCommodityPrice().divide(BigDecimal.valueOf(2).setScale(2, BigDecimal.ROUND_HALF_UP));
                                xdaOrderItemAppV3ODTO.setDeliveryPrice(divide);
                            }
                        }
                    }
                    normalGroup.addAll(collect);
                }
                if(SpringUtil.isNotEmpty(giftGroupCommodity)){
                    List<XdaOrderItemAppV3ODTO> giftCollect = giftGroupCommodity.stream().filter(xdaItem -> xdaItem.getGiftModelId().equals(item.getPromotionId()+"")).collect(Collectors.toList());
                    if(SpringUtil.isNotEmpty(giftCollect)){
                        for (XdaOrderItemAppV3ODTO xdaOrderItemAppV3ODTO : giftCollect) {
                            xdaOrderItemAppV3ODTO.setIsGift(Boolean.TRUE);
                            CommodityResultODTO odto = finalGiftPriceMap.get(xdaOrderItemAppV3ODTO.getCommodityId());
                            if (Objects.nonNull(odto)) {
                                xdaOrderItemAppV3ODTO.setOriginalPrice(odto.getCommodityPrice());
                            }
                        }
                        normalGroup.addAll(giftCollect);
                    }
                }

                if(SalesPromotionStatusEnums.GIFT.getCode() == item.getPromotionType().intValue()){
                    //移除赠品为0得（步骤可能多余）
                    normalGroup.removeIf(
                            normalGroupItem -> normalGroupItem.getIsGift() && normalGroupItem.getQuantity().compareTo(BigDecimal.ZERO) == 0
                    );
                    Optional<XdaOrderItemAppV3ODTO> giftItemOptional = normalGroup.stream().filter(
                            normalGroupItem -> normalGroupItem.getIsGift() && normalGroupItem.getQuantity().compareTo(BigDecimal.ZERO) > 0
                    ).findAny();
                    if(giftItemOptional.isPresent()){
                        xdaPreOrderItemGroupV3ODTO.setNormalGroup(normalGroup);
                        xdaPreOrderItemGroupV3ODTOList.add(xdaPreOrderItemGroupV3ODTO);
                    }else{
                        xdaPreOrderItemV3ODTO.getNormalGroup().addAll(normalGroup);
                    }
                }else{
                    xdaPreOrderItemGroupV3ODTO.setNormalGroup(normalGroup);
                    xdaPreOrderItemGroupV3ODTOList.add(xdaPreOrderItemGroupV3ODTO);
                }


            });
            xdaPreOrderItemV3ODTO.setPromotionGroups(xdaPreOrderItemGroupV3ODTOList);
        }
        List<XdaOrderItemAppV3ODTO> thGroupsCommodity = orderCommodityListByCommodityTypeMap.get(ProductTypeEnums.TH.getCode());
        xdaPreOrderItemV3ODTO.setThGroup(thGroupsCommodity);

        //处理老的促销方案中的赠 不同方案可能赠送同一个商品 需要分组统计
        if(SpringUtil.isNotEmpty(giftGroupCommodity)){
            List<Long> longList = giftGroupCommodity.stream().map(XdaOrderItemAppV3ODTO::getCommodityId).collect(Collectors.toList());
            XdaShoppingCartV3IDTO xdaShoppingCartV3IDTO = new XdaShoppingCartV3IDTO();
            xdaShoppingCartV3IDTO.setCommodityIdList(longList);
            xdaShoppingCartV3IDTO.setOrderTime(order.getOrderTime());
            xdaShoppingCartV3IDTO.setStoreId(order.getStoreId());
            xdaShoppingCartV3IDTO.setIsShoppingCart(Boolean.FALSE);
            List<XdaShoppingCartV3ODTO> validCommodityGift = xdaShoppingCartController.getValidCommodityGift(xdaShoppingCartV3IDTO);
            Map<Long, XdaShoppingCartV3ODTO> xdaShoppingCartV3ODTOMap = validCommodityGift.stream().collect(Collectors.toMap(XdaShoppingCartV3ODTO::getCommodityId, e -> e));
            if(SpringUtil.isEmpty(xdaPreOrderItemV3ODTO.getPromotionGroups())){
                Map<Long, List<XdaOrderItemAppV3ODTO>> oldGiftCommodityIdList = giftGroupCommodity.stream().collect(Collectors.groupingBy(XdaOrderItemAppV3ODTO::getCommodityId));
                if(SpringUtil.isNotEmpty(oldGiftCommodityIdList)){
                    oldGiftCommodityIdList.forEach((key,value)->{
                        XdaOrderItemAppV3ODTO xdaOrderItemAppV3ODTO = value.get(0);
                        if(xdaShoppingCartV3ODTOMap.containsKey(key)){
                            XdaShoppingCartV3ODTO xdaCommodityInfoODTO = xdaShoppingCartV3ODTOMap.get(key);
                            xdaOrderItemAppV3ODTO.setCommodityName(xdaCommodityInfoODTO.getCommodityName());
                            xdaOrderItemAppV3ODTO.setCommodityUnitName(xdaCommodityInfoODTO.getCommodityUnitName());
                            xdaOrderItemAppV3ODTO.setImageUrl(xdaCommodityInfoODTO.getImageUrl());
                            xdaOrderItemAppV3ODTO.setSalesBoxCapacity(xdaCommodityInfoODTO.getSalesBoxCapacity());
                        }
                        BigDecimal bigDecimal = value.stream().map(XdaOrderItemAppV3ODTO::getQuantity).reduce(BigDecimal.ZERO, (p, q) -> p.add(q)).setScale(2, BigDecimal.ROUND_HALF_UP);
                        xdaOrderItemAppV3ODTO.setQuantity(bigDecimal);
                        xdaPreOrderItemV3ODTO.getNormalGroup().add(xdaOrderItemAppV3ODTO);
                    });
                }
                return xdaPreOrderItemV3ODTO;
            }
            Map<Long, List<XdaOrderItemAppV3ODTO>> giftCommodityIdList = xdaPreOrderItemV3ODTO.getPromotionGroups().stream().flatMap(group -> group.getNormalGroup().stream()).filter(item -> item.getIsGift()).collect(Collectors.groupingBy(XdaOrderItemAppV3ODTO::getCommodityId));
            if(SpringUtil.isNotEmpty(giftCommodityIdList)){
                Map<Long, List<XdaOrderItemAppV3ODTO>> oldGiftCommodityIdList = giftGroupCommodity.stream().filter(gift -> !giftCommodityIdList.containsKey(gift.getCommodityId())).collect(Collectors.groupingBy(XdaOrderItemAppV3ODTO::getCommodityId));
                if(SpringUtil.isNotEmpty(oldGiftCommodityIdList)){
                    oldGiftCommodityIdList.forEach((key,value)->{
                        XdaOrderItemAppV3ODTO xdaOrderItemAppV3ODTO = value.get(0);
                        if(xdaShoppingCartV3ODTOMap.containsKey(key)){
                            XdaShoppingCartV3ODTO xdaCommodityInfoODTO = xdaShoppingCartV3ODTOMap.get(key);
                            xdaOrderItemAppV3ODTO.setCommodityName(xdaCommodityInfoODTO.getCommodityName());
                            xdaOrderItemAppV3ODTO.setCommodityUnitName(xdaCommodityInfoODTO.getCommodityUnitName());
                            xdaOrderItemAppV3ODTO.setImageUrl(xdaCommodityInfoODTO.getImageUrl());
                            xdaOrderItemAppV3ODTO.setSalesBoxCapacity(xdaCommodityInfoODTO.getSalesBoxCapacity());
                        }
                        BigDecimal bigDecimal = value.stream().map(XdaOrderItemAppV3ODTO::getQuantity).reduce(BigDecimal.ZERO, (p, q) -> p.add(q)).setScale(2, BigDecimal.ROUND_HALF_UP);
                        xdaOrderItemAppV3ODTO.setQuantity(bigDecimal);
                        xdaPreOrderItemV3ODTO.getNormalGroup().add(xdaOrderItemAppV3ODTO);
                    });
                }
            }
        }
        return xdaPreOrderItemV3ODTO;
    }
    /**
     * 订单商品
     * @param xdaOrderAppV4ODTOS
     * @return
     */
    private List<XdaOrderAppV4ODTO> covertCommodityInfo(List<XdaOrderAppV4ODTO> xdaOrderAppV4ODTOS){
        Map<Long, XdaOrderAppV4ODTO> orderMap = xdaOrderAppV4ODTOS.stream().collect(Collectors.toMap(XdaOrderAppV4ODTO::getOrderId, Function.identity()));
        List<XdaOrderItemAppV4ODTO> odtos = orderListMapper.queryOrderItemXdaAppV4(new ArrayList<>(orderMap.keySet()) );
        Set<Long> commodityIds = odtos.stream().map(XdaOrderItemAppV4ODTO :: getCommodityId).collect(Collectors.toSet());
        SelectXdaCommodityInfoListIDTO infoListIDTO = new SelectXdaCommodityInfoListIDTO();
        infoListIDTO.setCommodityIdList(new ArrayList<>(commodityIds));
        List<XdaCommodityInfoODTO> xdaCommodityInfoODTOList = xdaCommodityTextClient.selectXdaCommodityInfoList(infoListIDTO);
        QYAssert.notEmpty(xdaCommodityInfoODTOList, "商品基本信息为空");
        Map<Long,XdaCommodityInfoODTO> xdaCommodityInfoODTOMap = xdaCommodityInfoODTOList.stream().collect(Collectors.toMap(XdaCommodityInfoODTO::getCommodityId, Function.identity()));
        /***
         * APP 我的订单列表实发金额显示问题：背景由于同一个订单多个仓库发货完成时间点不同，导致实发金额会变化 希望能整单完成拣货（出货）才能计算实发金额，才能生成送货单
         *
         * 查询订单拆单数量（不包括直送类型：t_sub_order表logistics_model=0，直送类型大仓不发送发货消息）及实际大仓发货数
         * 实际大仓发货数=根据t_sub_order表字段writeback_real_qty_flag该字段等于1 时从而知道子订单在大仓发货并发送发货消息：topic=PICK_UPDATE_SUB_ORDER_QUANTITY_TOPIC 来判断拆分的子单是否都发货
         */
        List<OrderRealDeliveryFinishEntry> deliveryFinishEntryList=subOrderMapper.findOrderRealDeliveryFinishList(new ArrayList<>(orderMap.keySet()));
        Map<Long,OrderRealDeliveryFinishEntry> deliveryFinishEntryMap = SpringUtil.isNotEmpty(deliveryFinishEntryList) ? deliveryFinishEntryList.stream().collect(
                Collectors.toMap(OrderRealDeliveryFinishEntry::getOrderId, Function.identity())) : new HashMap<>();
        odtos.forEach(commodity->{
            XdaOrderAppV4ODTO order = orderMap.get(commodity.getOrderId());
            OrderRealDeliveryFinishEntry deliveryFinishEntry=deliveryFinishEntryMap.get(commodity.getOrderId());
            /*** 订单拆单数量不等于实际发货数 实发金额赋值null*/
            if(deliveryFinishEntry!=null && deliveryFinishEntry.getSubNum().compareTo(deliveryFinishEntry.getRealFinishNum())!=0){
                order.setRealAmount(null);
            }
            if(order.getCommodities() == null){
                order.setCommodities(new ArrayList<>());
            }
            XdaCommodityInfoODTO odto = xdaCommodityInfoODTOMap.get(commodity.getCommodityId());
            if(odto == null) {return;}
            commodity.setCommodityName(odto.getCommodityAppName());
            commodity.setCommodityUnitName(odto.getCommodityUnitName());
            commodity.setCommoditySpec(odto.getCommoditySpec());
            commodity.setImageUrl(odto.getDefaultPicUrl());
            order.getCommodities().add(commodity);
        });
        return xdaOrderAppV4ODTOS;
    }


    /**
     * 获取购物车内的所有商品id
     * @param shoppingCartV4ODTO
     * @return
     */
    public List<Long> getShoppingCartCommodityIdList(ShoppingCartV4ODTO shoppingCartV4ODTO){

        List<Long> commodityIdList = new ArrayList<>();
        List<Long> commodityIds;
        if(SpringUtil.isNotEmpty(commodityIds = shoppingCartV4ODTO.getNormalGroup().getCommodities().stream().map(ShoppingCartCommodityV4ODTO::getCommodityId).collect(Collectors.toList()))){
            commodityIdList.addAll(commodityIds);
        }
        if(SpringUtil.isNotEmpty(shoppingCartV4ODTO.getPromotionGroup())){
            List<ShoppingCartGroupV4ODTO> promotionGroup = shoppingCartV4ODTO.getPromotionGroup();
            //移除购物车中买赠组中赠品为0的商品
            promotionGroup.forEach(
                    item -> {
                        if(SpringUtil.isNotEmpty(item.getCommodities())){
                            item.getCommodities().removeIf(subItem-> BooleanUtils.isTrue(subItem.getIsGift()) && Objects.equals(subItem.getQuantity(),BigDecimal.ZERO));
                        }
                    }
            );
            promotionGroup.forEach(item->{
                List<Long> commodityId;
                if(SpringUtil.isNotEmpty(commodityId = item.getCommodityIdList())){
                    commodityIdList.addAll(commodityId);
                }

                if(SpringUtil.isNotEmpty(commodityId = item.getGiftGroupCommodityIdList())){
                    commodityIdList.addAll(commodityId);
                }

                //赠品不为0的图片才进行展示
//                if(SpringUtil.isNotEmpty(commodityId = item.getGiftGroup()
//                        .stream()
//                        .filter(giftItem -> BigDecimal.ZERO.compareTo(giftItem.getQuantity()) < 0)
//                        .map(ShoppingCartCommodityV4ODTO::getCommodityId).collect(Collectors.toList()))){
//                    commodityIdList.addAll(commodityId);
//                }
            });
        }
        if(SpringUtil.isNotEmpty(shoppingCartV4ODTO.getThGroups().getCommodityIdList())){
            List<ShoppingCartCommodityV4ODTO> commodities = shoppingCartV4ODTO.getThGroups().getCommodities();
            commodities.forEach(item->{
                if(!item.getIsInvalidate()){
                    commodityIdList.add(item.getCommodityId());
                }
            });
        }

        return commodityIdList;
    }
    /**
     * 获取购物车有效商品
     * 1、获取购物车商品
     * 2、删除特惠商品中失效的商品
     * 3、普通商品组 & 买赠商品组 & 满折商品组 独立失效分组不需要删除,用的时候需要获取有效商品组
     * @param storeId 客户id
     * @param orderTime 送货日期
     * @return 购物车商品数据
     */
    public ShoppingCartV4ODTO getShoppingCart(Long storeId,Date orderTime,String deliveryTimeRange, Integer deliveryBatch,Long couponUserId){
        // 重新刷新购物车数据
        BuildXdaShoppingCartV4 buildXdaShoppingCartV4 = new BuildXdaShoppingCartV4(storeId, orderTime, toBService,xdaShoppingCartMapper, orderMapper, orderListMapper, xdaCommodityFrontClient, xdaShoppingCartController, storeService, storeDurationMapper, commodityFreezeGroupMapper, mtPromotionClient,deliveryTimeRange,deliveryBatch,redissonClient,couponUserId);
        ShoppingCartV4ODTO shoppingCartV4ODTO = buildXdaShoppingCartV4.shopCartList();
        if(null != shoppingCartV4ODTO && shoppingCartV4ODTO.getCanSettlement()){
            // 特惠商品删除失效商品
            if(null != shoppingCartV4ODTO.getThGroups() && SpringUtil.isNotEmpty(shoppingCartV4ODTO.getThGroups().getCommodities())){
                shoppingCartV4ODTO.getThGroups().getCommodities().removeIf(item-> item.getIsInvalidate());
            }
        }
        return shoppingCartV4ODTO;
    }


    /**
     * 保存拆单明细
     * @param orderClassifyMap
     * @param order
     */
    private void saveSubOrder(Map<String, List<CreOrderItemV4DTO>> orderClassifyMap, Order order){
        for (Map.Entry<String, List<CreOrderItemV4DTO>> entry : orderClassifyMap.entrySet()){
            if("classifyKey".equals(entry.getKey())){
                continue;
            }
            List<CreOrderItemV4DTO> itemList = entry.getValue();
            Integer logisticsModel = itemList.get(0).getLogisticsModel();
            Long warehouseId = itemList.get(0).getWarehouseId();
            Long supplierId = itemList.get(0).getSupplierId();
            SubOrder subOrder = new SubOrder();
            subOrder.setOrderId(order.getId());
            subOrder.setOrderTime(order.getOrderTime());
            subOrder.setStatus(SubOrderStatusEnums.SUB_ORDER_UNDELIVERY.getCode());
            subOrder.setLogisticsModel(logisticsModel);
            subOrder.setVarietyTotal(itemList.size());
            subOrder.setCreateId(order.getCreateId());
            subOrder.setCreateTime(new Date());
            subOrder.setEnterpriseId(QingyunConstant.ENTERPRISE_ID);
            subOrder.setWarehouseId(warehouseId);
            subOrder.setSupplierId(supplierId);

            List<SubOrderItem> items = new ArrayList<SubOrderItem>(itemList.size());
            BigDecimal totalPriceForSubOrder = BigDecimal.ZERO;
            for(CreOrderItemV4DTO item : itemList){
                SubOrderItem subOrderItem = new SubOrderItem();
                subOrderItem.setCommodityId(item.getCommodityId());
                subOrderItem.setCreateId(order.getCreateId());
                subOrderItem.setUpdateTime(new Date());
                subOrderItem.setCreateTime(new Date());
                subOrderItem.setPrice(item.salePrice());
                if(null != item.getConditionId() && null != item.getDeliveryPrice()){
                    //为了兼容一期 必须保存kg 为单位的价格  只有参加满折&单位为kg的才会有预估到手价
                    BigDecimal deliveryPrice = item.getDeliveryPrice();
                    if(null != item.getIsWeight() && item.getIsWeight().equals(1)){
                        deliveryPrice = item.getDeliveryPrice().multiply(new BigDecimal("2")
                                .setScale(2, BigDecimal.ROUND_HALF_UP));
                    }
                    subOrderItem.setPrice(deliveryPrice);
                }
                subOrderItem.setQuantity(item.getQuantity());
                BigDecimal totalPrice = subOrderItem.getPrice().multiply(item.getQuantity()).setScale(2, BigDecimal.ROUND_HALF_UP);
                if(null != item.getSumPrice()){
                    totalPrice = item.getSumPrice().setScale(2,BigDecimal.ROUND_HALF_UP);
                }
                subOrderItem.setTotalPrice(totalPrice);

                totalPriceForSubOrder = totalPriceForSubOrder.add(totalPrice).setScale(2, BigDecimal.ROUND_HALF_UP);
                items.add(subOrderItem);
            }
            subOrder.setSubOrderCode(IDGenerator.newOrderCode());
            subOrder.setTotalPrice(totalPriceForSubOrder);
            subOrderMapper.insertSelective(subOrder);
            for(SubOrderItem item : items){
                item.setSubOrderId(subOrder.getId());
            }
            subOrderItemMapper.insertList(items);
        }
    }

    public void savePromotion(ShoppingCartV4ODTO shoppingCartV4ODTO,Long orderId){
        if(SpringUtil.isNotEmpty(shoppingCartV4ODTO.getPromotionGroup())){
            List<OrderSalesPromotion> orderSalesPromotionList = new ArrayList<>();
            for (ShoppingCartGroupV4ODTO shoppingCartGroupV4ODTO : shoppingCartV4ODTO.getPromotionGroup()) {
                if(null==shoppingCartGroupV4ODTO.getFullStatus() || shoppingCartGroupV4ODTO.getFullStatus()== 0){
                    continue;
                }
                OrderSalesPromotion orderSalesPromotion = new OrderSalesPromotion();
                CommodityPromotionV4ODTO commodityPromotionV4ODTO = shoppingCartGroupV4ODTO.getCommodityPromotionV4ODTO();
                orderSalesPromotion.setOrderId(orderId);
                orderSalesPromotion.setPromotionId(Long.valueOf(shoppingCartGroupV4ODTO.getPromotionId()));
                orderSalesPromotion.setPromotionName(commodityPromotionV4ODTO.getPromotionName());
                orderSalesPromotion.setPromotionType(commodityPromotionV4ODTO.getPromotionType());
                String[] result = commodityPromotionV4ODTO.getNextTips().split(";");
                orderSalesPromotion.setTips(result[0]);
                orderSalesPromotion.setPromotionRules(commodityPromotionV4ODTO.getRuleList().stream().collect(Collectors.joining(",")));
                orderSalesPromotionList.add(orderSalesPromotion);
            }
            if(SpringUtil.isNotEmpty(orderSalesPromotionList)){
                orderSalesPromotionMapper.insertList(orderSalesPromotionList);
            }
        }
    }

    /**
     * 订单扣款/回款
     */
    public void saveOrderDeductions(Order order, Long storeId, XdaPayTypeEnum typeEnum, String remark,Integer billType) {
        if (Objects.isNull(storeId)) {
            return;
        }
        //校验账户
        Example ex = new Example(StoreSettlement.class);
        ex.createCriteria().andEqualTo("storeId", storeId);
        List<StoreSettlement> ssList = this.storeSettlementMapper.selectByExample(ex);
        if (CollectionUtils.isEmpty(ssList) || !ssList.get(0).getCollectStatus()) {
            //客户账户表无数据或者账户为非预售，不处理
            return;
        }

        //订单金额，合并批发后，需加上运费
        BigDecimal orderAmount = order.getOrderAmount();
        orderAmount = orderAmount.add(order.getFreightAmount());

        //处理扣款/回款
        if (XdaPayTypeEnum.DEDUCTION.equals(typeEnum)) {
            //扣款
            StoreDeductionBO deductionBO = StoreDeductionBO.builder()
                    .orderCode(order.getOrderCode())
                    .orderId(order.getId())
                    .orderAmount(orderAmount)
                    .storeId(storeId)
                    .tradeCode(order.getOrderCode())
                    .tradeTime(new Date())
                    .orderTime(order.getOrderTime())
                    .billType(StoreBillTypeEnums.APP_XD_DEDUCTION.getCode())
                    .remark("<--鲜达app扣款：" + order.getOrderCode() + " -->")
                    .userId(storeId)
                    .build();
            storeRechargeService.storeDeduction(deductionBO);
        } else {
            //回款（充值）
            Example xfEx = new Example(XfOrder.class);
            xfEx.createCriteria().andEqualTo("orderId", order.getId());
            XfOrder xfOrder = xfOrderMapper.selectOneByExample(xfEx);
            /**
             * 1.现付部分原路返还
             * 2.红冲
             * 3.充值剩下的余额
             */
            if (Objects.nonNull(xfOrder)) {
                //现付款 订单取消
                BigDecimal amount = xfOrder.getPayAmount();
                XdaPayBillEntry xdaPay = new XdaPayBillEntry();
                xdaPay.setBillCode(xfOrder.getBillCode());
                String refundOrderCode = xfOrder.getBillCode() + "999";
                xdaPay.setPayAmount(amount);
                xdaPay.setStoreId(xfOrder.getStoreId());
                xdaPay.setCreateId(xfOrder.getCreateId());
                //原路返还
                rechargeService.reversePay(xdaPay);

                String billRemark = "<--" + remark + ":充值流水号" + xfOrder.getBillCode() + ":订单号" + order.getOrderCode() + " -->";
                Integer xfBillType = null;
                if (Objects.equals(billType, StoreBillTypeEnums.ONLINE_CANCEL.getCode())) {
                    xfBillType = StoreBillTypeEnums.ONLINE_CANCEL_RECHARGE.getCode();
                }
                if (Objects.equals(billType, StoreBillTypeEnums.ONLINE_ZERO_CANCEL.getCode())) {
                    //有现付 加上充值流水号
                    xfBillType = StoreBillTypeEnums.ONLINE_STK_QUANTITY_ZERO_CANCEL.getCode();
                }
                if (Objects.equals(billType, StoreBillTypeEnums.ONLINE_ORDER_DISPATCHING_ERROR.getCode())) {
                    //有现付 加上充值流水号
                    xfBillType = StoreBillTypeEnums.ONLINE_ORDER_DISPATCHING_ERROR_DEPOSIT.getCode();
                }
                StoreRechargeRubricationBO storeRechargeRubricationBO = StoreRechargeRubricationBO
                        .builder()
                        .orderCode(order.getOrderCode())
                        .orderId(order.getId())
                        .tradeCode(order.getOrderCode())
                        .tradeTime(new Date())
                        .thirdPartyTradeCode(refundOrderCode)
                        .money(amount)
                        .storeId(storeId)
                        .billType(xfBillType)
                        .remark(billRemark)
                        .build();
                // 红冲
                storeRechargeService.redInkAdjustment(storeRechargeRubricationBO);
                //剩余金额返还账户
                orderAmount = orderAmount.subtract(amount);
            }
            //余额部分充值
            if (orderAmount.compareTo(BigDecimal.ZERO) != 0) {
                String billRemark = "<--" + remark + ":订单号" + order.getOrderCode() + " -->";
                Long userId = storeId;
                if (Objects.equals(billType, StoreBillTypeEnums.ONLINE_CANCEL_RECHARGE.getCode())
                        || Objects.equals(billType, StoreBillTypeEnums.ONLINE_ORDER_DISPATCHING_ERROR.getCode())
                        || Objects.equals(billType, StoreBillTypeEnums.ONLINE_ORDER_DISPATCHING_ERROR_DEPOSIT.getCode())
                        || Objects.equals(billType, StoreBillTypeEnums.ONLINE_STK_QUANTITY_ZERO_CANCEL.getCode())
                ) {
                    userId = -1L;
                }
                StoreRechargeBO rechargeBO = StoreRechargeBO.builder()
                        .orderCode(order.getOrderCode())
                        .tradeCode(order.getOrderCode())
                        .money(orderAmount.doubleValue())
                        .storeId(storeId)
                        .tradeTime(new Date())
                        .receiptDate(new Date())
                        .billType(billType)
                        .remark(billRemark)
                        .userId(userId)
                        .build();
                storeRechargeService.storeRecharge(rechargeBO);
            }
        }
    }

    /**
     * 保存订单
     * @return
     */
    public Order saveOrder(SaveOrderDTO saveOrderDTO){
        Order freezeOrder = saveOrderDTO.getFreezeOrder();
        List<CreOrderItemV4DTO> creOrderItemDTOList = saveOrderDTO.getCreOrderItemDTOList();
        XdaCreatePrePayOrderV4IDTO xdaCreatePrePayOrderV4IDTO = saveOrderDTO.getXdaCreatePrePayOrderV4IDTO();
        Integer businessType = saveOrderDTO.getBusinessType();
        TdaDeliveryTimeRangeODTO tdaDeliveryTimeRangeODTO = saveOrderDTO.getTdaDeliveryTimeRangeODTO();
        BigDecimal freightAmount = saveOrderDTO.getFreightAmount();
        BigDecimal amount = saveOrderDTO.getAmount();
        Date orderDurationTime = saveOrderDTO.getOrderDurationTime();
        Boolean isSaveOrder = saveOrderDTO.getIsSaveOrder();
        /* 初始化鲜达订单信息 */
        Order order = Order.initForXdaOrderV4(xdaCreatePrePayOrderV4IDTO, amount, freightAmount, orderDurationTime, storeMapper.getStoreCompanyByStoreId(xdaCreatePrePayOrderV4IDTO.getStoreId()));

        // 通达/顺丰 订单设置流程状态为待拣货、物流中心、业务类型、送货时间段
        if (Objects.equals(businessType, BusinessTypeEnums.B_COUNTRY.getCode()) || Objects.equals(businessType, BusinessTypeEnums.TD_SALE.getCode())) {
            tdaOrderSetting(tdaDeliveryTimeRangeODTO, order);
        }

        if (StringUtils.isNotBlank(saveOrderDTO.getLogisticsCarrierCode())) {
            order.setLogisticsCarrierCode(saveOrderDTO.getLogisticsCarrierCode());
        }

        order.setDeliveryBatch(order.getDeliveryBatch() == null ? CloudDeliveryBatchTypeEnum.ZERO_BATCH.getCode() : order.getDeliveryBatch());
        orderMapper.insertSelective(order);
        freezeOrder.setId(order.getId());
        freezeOrder.setOrderCode(order.getOrderCode());

        //把订单明细转换成查询大仓库存的orderList,不包含组合品
        List<CommodityInventoryDetailIDTO> orderCommodityList = convert2OrderCommodityList(creOrderItemDTOList);

        //查询库存
        List<CommodityInventoryODTO> commodityInventoryODTOS = toBService.queryCommodityInventory(order.getOrderTime(), toBService.mergeOrderItemsByLevel(orderCommodityList),order.getStoreId(),order.getDeliveryTimeRange(),order.getDeliveryBatch());
        Map<Long, Integer> commodityStockTypeMap = commodityInventoryODTOS.stream().collect(Collectors.toMap(CommodityInventoryODTO::getCommodityId, CommodityInventoryODTO::getStockType, (v1, v2) -> v1));

        //设置赠品最大下单数量
        this.handleGiftMaxOrderQuantity(creOrderItemDTOList, commodityInventoryODTOS);

        try {
            //冻结库存,不包含组合品。其中一个商品库存不足。大仓就直接报错
            List<CommodityInventoryODTO> inventoryODTOList = toBService.saveXdaOrderWarehouseFreezeInventory(order, creOrderItemDTOList, commodityStockTypeMap, tdaDeliveryTimeRangeODTO);
            Map<Long, Long> commWarehouseMap = Maps.newHashMap();
            if (CollectionUtils.isNotEmpty(orderCommodityList)) {
                for (CommodityInventoryODTO inventoryODTO : inventoryODTOList) {
                    commWarehouseMap.put(inventoryODTO.getCommodityId(), inventoryODTO.getWarehouseId());
                }
            }
            order.setCommWarehouseMap(commWarehouseMap);

        } catch (Exception e) {
            log.warn("冻结B端库存失败,入参:{}", JSON.toJSONString(order), e);

            // 1.删除订单
            order.setErrorMsg(ApiErrorCodeEnum.XDA_PRE_ORDER_V4_WARN.getRemark());
            orderMapper.deleteByPrimaryKey(order.getId());

            // 2.调用释放冻结方法
            try {
                toBService.warehouseUnfreezeInventory(order.getId(), order.getStoreId(),businessType);
            } catch (Exception e2) {
                log.warn("冻结失败，调用释放冻结方法异常 orderId {}", order.getId(), e2);
            }

            // 3.记录库存不足的商品
            freezeFailSaveShortStockLog(creOrderItemDTOList, order, orderCommodityList);

            return order;
        }

        // 过滤商品数量为0的商品
        creOrderItemDTOList.removeIf(item -> item.getQuantity().compareTo(BigDecimal.ZERO) == 0);

        // 获取鲜达订货目标id,特惠商品保存
        XdaOrderTargetSetV2ODTO xdaOrderTargetIdByStoreIdAndOrderTime = getXdaOrderTargetSetV2ODTO(xdaCreatePrePayOrderV4IDTO);

        // 组装orderList
        List<OrderList> orderLists = assembleOrderLists(creOrderItemDTOList, xdaOrderTargetIdByStoreIdAndOrderTime, order);

        //分摊组合子商品金额
        allocateCombChildItemAmount(orderLists);

        // 预订单不调用保存明细方法，预订单用order.getOrderList 去保存预订单明细
        // 组装给统计库发送消息 saveOrderList, id必须是order_gift_list.id
        List<OrderList> saveOrderList = saveOrderListAndOrderGiftList(Objects.equals(BusinessTypeEnums.TD_SALE.getCode(), businessType), isSaveOrder, orderLists);

        // 给统计查询发消息所用和预订单保存预订单明细
        order.setOrderList(saveOrderList);

        if(isSaveOrder){
            orderService.crateOrderMirror(order);
        }

        //订单中总的促销后的金额
        updateOrderPromotionAmount(orderLists, order);
        return order;
    }

    /**
     * 更新订单促销后金额和总金额
     * @param orderLists
     * @param order
     */
    private void updateOrderPromotionAmount(List<OrderList> orderLists, Order order) {
        BigDecimal totalPricesOfAllItems = orderLists.stream().filter(x -> !Objects.equals(x.getCombType(), 3)).map(OrderList::getTotalPrice).reduce(BigDecimal.ZERO, BigDecimal::add);
        BigDecimal totalOriginalPricesOfAllItems = orderLists.stream().filter(x -> !Objects.equals(x.getCombType(), 3)).map(OrderList::getOriginalTotalPrice).reduce(BigDecimal.ZERO, BigDecimal::add);
        order.setPromotionAmount(totalOriginalPricesOfAllItems.subtract(totalPricesOfAllItems));
        order.setTotalAmount(totalOriginalPricesOfAllItems);
        orderMapper.updateByPrimaryKeySelective(order);
    }

    private @NotNull List<OrderList> saveOrderListAndOrderGiftList(Boolean isTdaStore, Boolean isSaveOrder, List<OrderList> orderLists) {
        // 给统计查询发消息的list，id必须是order_gift_list.id
        List<OrderList> kfkOrderList = new ArrayList<>();
        // 通达销售，称重品拆行
        if(isTdaStore){
            List<OrderList> orderListAll = new ArrayList<>();
            for(OrderList orderList : orderLists){
                if(orderList.getIsWeight() != null && orderList.getIsWeight().equals(YesOrNoEnums.YES.getCode())){
                    // 通达称重品拆行
                    List<OrderList> splitOrderList = tdaWeightSplit(orderList, orderList.getCommodityPackageSpec());
                    orderListAll.addAll(splitOrderList);
                }else {
                    orderListAll.add(orderList);
                }
            }

            //批量插入订单明细
            if(isSaveOrder){
                // 保存orderList和orderGiftList
                saveOrderListAndOrderGiftList(orderListAll, kfkOrderList);
            } else {
                kfkOrderList.addAll(orderListAll);
            }
        }else {
            //批量插入订单明细
            if(isSaveOrder){
                // 保存orderList和orderGiftList
                saveOrderListAndOrderGiftList(orderLists, kfkOrderList);
            } else {
                kfkOrderList.addAll(orderLists);
            }
        }
        return kfkOrderList;
    }

    /**
     * 鲜达订单组装orderList信息
     * @param creOrderItemDTOList
     * @param xdaOrderTargetIdByStoreIdAndOrderTime
     * @param order
     * @return
     */
    private  List<OrderList> assembleOrderLists(List<CreOrderItemV4DTO> creOrderItemDTOList, XdaOrderTargetSetV2ODTO xdaOrderTargetIdByStoreIdAndOrderTime, Order order) {
        List<OrderList> orderLists = new ArrayList<OrderList>();
        creOrderItemDTOList.forEach(item->{
            OrderList orderList = new OrderList();
            orderList.setCommodityId(item.getCommodityId());
            orderList.setCommodityNum(item.saleQuantity());
            orderList.setType(item.getType().getCode());
            //支付金额
            orderList.setCommodityPrice(item.salePrice());
            if (ProductTypeEnums.PRODUCT.equals(item.getType())) {
                orderList.setRemark("订单商品");
            }else if(ProductTypeEnums.TH.equals(item.getType())){
                orderList.setRemark("订单商品");
                if(null != xdaOrderTargetIdByStoreIdAndOrderTime){
                    orderList.setSpecialsId(xdaOrderTargetIdByStoreIdAndOrderTime.getId());
                }
                //支付金额
                orderList.setCommodityPrice(item.getThPrice());
            }
            if(null != item.getConditionId() && null != item.getDeliveryPrice()){
                //为了兼容一期 必须保存kg 为单位的价格  只有参加满折&单位为kg的才会有预估到手价
                BigDecimal deliveryPrice = item.getDeliveryPrice();
                if(null != item.getIsWeight() && item.getIsWeight().equals(1)){
                    deliveryPrice = item.getDeliveryPrice().multiply(new BigDecimal("2")
                            .setScale(2, BigDecimal.ROUND_HALF_UP));
                }
                orderList.setCommodityPrice(deliveryPrice);
            }
            orderList.setOrderId(order.getId());
            //设置特价
            if(item.getIsSpecialPrice().equals(1)){
                orderList.setSpecialPrice(item.getSpecialPrice());
                orderList.setPricePromotionId(item.getPricePromotionId());
            }
            //设置原价
            orderList.setOriginalPrice(item.getCommodityPrice());
            //促销后的商品总金额
            orderList.setTotalPrice(orderList.getCommodityPrice().multiply(orderList.getCommodityNum()).setScale(2,BigDecimal.ROUND_HALF_UP));
            if(null != item.getSumPrice()){
                orderList.setTotalPrice(item.getSumPrice().setScale(2,BigDecimal.ROUND_HALF_UP));
            }
            //商品原价总金额
            orderList.setOriginalTotalPrice(orderList.getOriginalPrice().multiply(orderList.getCommodityNum()).setScale(2,BigDecimal.ROUND_HALF_UP));
            orderList.setGiftModelId(item.getConditionId());
            //商品类型
            orderList.setCombType(item.getCombType());
            //金额占比（组合子品才有）
            orderList.setProportion(item.getProportion());
            //组合商品id
            if (Objects.nonNull(item.getCombCommodityId())) {
                //有组合商品id，说明有子商品列表
                orderList.setCombCommodityId(item.getCombCommodityId());
                //orderList.setPricePromotionId(null);
                orderList.setSpecialPrice(null);
            }
            orderList.setIsWeight(item.getIsWeight());
            orderList.setCommodityPackageSpec(item.getCommodityPackageSpec());
            //优惠券
            orderList.setCouponDiscountAmount(item.getCouponDiscountAmount());
            BigDecimal couponDiscountAmount = Objects.isNull(orderList.getCouponDiscountAmount()) ? BigDecimal.ZERO : orderList.getCouponDiscountAmount();
            orderList.setTotalPrice(orderList.getTotalPrice().subtract(couponDiscountAmount));
            orderList.setCommodityPrice(orderList.getTotalPrice().divide(orderList.getCommodityNum(), 2, RoundingMode.HALF_UP));
            orderList.setCouponId(item.getCouponId());
            orderList.setCouponUserId(item.getCouponUserId());
            orderLists.add(orderList);
        });
        return orderLists;
    }

    private XdaOrderTargetSetV2ODTO getXdaOrderTargetSetV2ODTO(XdaCreatePrePayOrderV4IDTO xdaCreatePrePayOrderV4IDTO) {
        XdaShoppingCartV2IDTO xdaShoppingCartV2IDTO = new XdaShoppingCartV2IDTO();
        xdaShoppingCartV2IDTO.setStoreId(xdaCreatePrePayOrderV4IDTO.getStoreId());
        xdaShoppingCartV2IDTO.setOrderTime(xdaCreatePrePayOrderV4IDTO.getOrderTime());
        XdaOrderTargetSetV2ODTO xdaOrderTargetIdByStoreIdAndOrderTime = xdaCommodityFrontClient.findXdaOrderTargetIdByStoreIdAndOrderTime(xdaShoppingCartV2IDTO);
        return xdaOrderTargetIdByStoreIdAndOrderTime;
    }

    /**
     * 保存订单冻结失败，记录库存不足的商品
     * @param creOrderItemDTOList
     * @param order
     * @param orderCommodityList
     */
    private void freezeFailSaveShortStockLog(List<CreOrderItemV4DTO> creOrderItemDTOList, Order order, List<CommodityInventoryDetailIDTO> orderCommodityList) {
        // 冻结出错，重新查询实时库存
        List<CommodityInventoryODTO> commodityInventoryList = toBService.queryCommodityInventory(order.getOrderTime(), toBService.mergeOrderItemsByLevel(orderCommodityList), order.getStoreId(), order.getDeliveryTimeRange(), order.getDeliveryBatch());
        Map<Long, CommodityInventoryODTO> inventoryODTOMap = commodityInventoryList.stream().collect(Collectors.toMap(CommodityInventoryODTO::getCommodityId, Function.identity()));

        List<CreOrderItemV4DTO> combOrderItems = creOrderItemDTOList.stream().filter(x -> Objects.equals(x.getCombType(), CombTypeEnum.COMB.getCode())).collect(Collectors.toList());
        //查询组合商品的库存依据，库存不足时需要设置库存依据
        List<CommodityInventoryDetailIDTO> combOrderCommodityList = combOrderItems
                .stream()
                .map(item -> {
                    CommodityInventoryDetailIDTO idto = new CommodityInventoryDetailIDTO();
                    idto.setCommodityId(item.getCommodityId());
                    idto.setQuantity(item.getQuantity());
                    idto.setLevel(0);
                    return idto;
                }).collect(Collectors.toList());
        List<CommodityInventoryODTO> combCommodityInventoryList = toBService.queryCommodityInventory(order.getOrderTime(), toBService.mergeOrderItemsByLevel(combOrderCommodityList), order.getStoreId(), order.getDeliveryTimeRange(), order.getDeliveryBatch());
        Map<Long, CommodityInventoryODTO> combInventoryMap = combCommodityInventoryList.stream().collect(Collectors.toMap(CommodityInventoryODTO::getCommodityId, Function.identity()));

        //组装map，key为组合商品，value为子商品列表
        Map<CreOrderItemV4DTO, List<CommodityItemODTO>> map = getCommodityItemListMap(creOrderItemDTOList, combOrderItems);

        List<BStockLackVO> voList = new ArrayList<>();

        // 处理组合商品库存不足
        handleCompositeProductShortage(map, inventoryODTOMap, order, combInventoryMap, voList);

        // 处理普通商品库存不足
        handleNotCompositeProductShortage(creOrderItemDTOList, inventoryODTOMap, order, voList);

        if (SpringUtil.isNotEmpty(voList)) {
            bStockShortService.stockShort(voList);
        }
    }

    /**
     * 通达订单设置待拣货、物流中心、业务类型、送货时间段
     * @param tdaDeliveryTimeRangeODTO
     * @param order
     */
    private void tdaOrderSetting(TdaDeliveryTimeRangeODTO tdaDeliveryTimeRangeODTO, Order order) {
        order.setProcessStatus(XdaOrderProcessStatusEunm.WAITING_PICK.getCode());
        if(tdaDeliveryTimeRangeODTO != null) {
            order.setDeliveryBatch(tdaDeliveryTimeRangeODTO.getDeliveryBatch());
            order.setLogisticsCenterId(tdaDeliveryTimeRangeODTO.getLogisticsCenterId());
            order.setLogisticsCenter(tdaDeliveryTimeRangeODTO.getLogisticsCenterName());
            order.setBusinessType(tdaDeliveryTimeRangeODTO.getBusinessType());
            order.setDeliveryTimeRange(tdaDeliveryTimeRangeODTO.getDeliveryTimeRange());
        }
    }

    private void saveOrderListAndOrderGiftList(List<OrderList> orderList, List<OrderList> kfkOrderList) {
        orderList.forEach(item -> {
            orderListMapper.insert(item);

            OrderListGift gift = BeanCloneUtils.copyTo(item, OrderListGift.class);
            //确保giftList插入时生成数据库自增的ID
            gift.setId(null);
            orderListGiftMapper.insert(gift);

            // 给统计查询发消息，id必须是order_gift_list.id
            item.setId(gift.getId());
            kfkOrderList.add(item);
        });
    }

    /**
     * 通达称重品拆行
     * @param orderList
     */
    public List<OrderList> tdaWeightSplit(OrderList orderList, BigDecimal commodityPackageSpec){
        List<OrderList> splitOrderList = new ArrayList<>();
        Integer number = orderList.getCommodityNum().divide(commodityPackageSpec,0,BigDecimal.ROUND_UP).intValue();
        // 称重品价格分摊问题,向下保留2位小数。最后一个称重品金额可能会多点
        BigDecimal oneTotalPrice = orderList.getTotalPrice().divide(new BigDecimal(number), 2, BigDecimal.ROUND_DOWN);
        BigDecimal originalTotalPrice = orderList.getOriginalPrice().multiply(orderList.getCommodityNum()).setScale(2, BigDecimal.ROUND_HALF_UP);
        BigDecimal oneOriginalTotalPrice = originalTotalPrice.divide(new BigDecimal(number), 2, BigDecimal.ROUND_DOWN);
        BigDecimal couponDiscountAmount = orderList.getCouponDiscountAmount();
        BigDecimal oneCouponDiscountAmount = null;
        if (Objects.nonNull(couponDiscountAmount)){
            oneCouponDiscountAmount = couponDiscountAmount.divide(new BigDecimal(number), 2, RoundingMode.DOWN);
        }
        for(int i = 0 ; i < number ; i++){
            OrderList saveOrderList = BeanCloneUtils.copyTo(orderList, OrderList.class);
            saveOrderList.setCommodityNum(commodityPackageSpec);
            saveOrderList.setCouponId(orderList.getCouponId());
            saveOrderList.setCouponUserId(orderList.getCouponUserId());
            if(i < number - 1){
                saveOrderList.setTotalPrice(oneTotalPrice);
                saveOrderList.setOriginalTotalPrice(oneOriginalTotalPrice);
                if (Objects.nonNull(couponDiscountAmount)){
                    saveOrderList.setCouponDiscountAmount(oneCouponDiscountAmount);
                }
            }else {
                saveOrderList.setTotalPrice(orderList.getTotalPrice().subtract(oneTotalPrice.multiply(new BigDecimal(number - 1))));
                saveOrderList.setOriginalTotalPrice(originalTotalPrice.subtract(oneOriginalTotalPrice.multiply(new BigDecimal(number - 1))));
                if (Objects.nonNull(couponDiscountAmount)) {
                    saveOrderList.setCouponDiscountAmount(couponDiscountAmount.subtract(oneCouponDiscountAmount.multiply(new BigDecimal(number - 1))));
                }
            }
            splitOrderList.add(saveOrderList);
        }
        return splitOrderList;
    }

    /**
     * 分摊组合子商品金额
     *
     * @param orderLists 入库的订单明细
     */
    private void allocateCombChildItemAmount(List<OrderList> orderLists) {
        //过滤出组合商品
        Map<String, OrderList> combOrderListMap = orderLists.stream()
                .filter(y -> Objects.equals(y.getCombType(), CombTypeEnum.COMB.getCode()))
                //根据组合商品id+订单号+订单类型 映射
                //特价限购需求后加上了 特价id（同一个商品可以享受部分特价，部分原价）
                .collect(Collectors.toMap(x -> x.getCommodityId() + "_" + x.getOrderId() + "_" + x.getType() + "_" + x.getPricePromotionId(), Function.identity()));
        //组合子品分组
        Map<String, List<OrderList>> combOrderMap = orderLists
                .stream()
                .filter(y -> Objects.equals(y.getCombType(), CombTypeEnum.COMB_CHILD.getCode()))
                //根据组合商品id+订单号+订单类型 分组
                .collect(Collectors.groupingBy(x -> x.getCombCommodityId() + "_" + x.getOrderId() + "_" + x.getType() + "_" + x.getPricePromotionId()));

        for (Map.Entry<String, List<OrderList>> entry : combOrderMap.entrySet()) {
            List<OrderList> combChildOrderList = entry.getValue();
            BigDecimal totalPromotionAmount = BigDecimal.ZERO;
            for (int i = 0, combChildOrderListSize = combChildOrderList.size(); i < combChildOrderListSize; i++) {
                OrderList combChildOrder = combChildOrderList.get(i);
                OrderList combOrder = combOrderListMap.get(entry.getKey());
                if (Objects.isNull(combOrder)) {
                    continue;
                }
                //原价
                combChildOrder.setOriginalPrice(combChildOrder.getOriginalPrice());
                combChildOrder.setOriginalTotalPrice(combChildOrder.getOriginalPrice().multiply(combChildOrder.getCommodityNum()));

                //组合品促销后金额
                BigDecimal totalPrice = combOrder.getTotalPrice();

                // 计算子商品促销后金额
                BigDecimal comChildtotalPrice = BigDecimal.ZERO;
                if (i == combChildOrderList.size() - 1) {
                    // 最后一笔订单按固定金额减免
                    comChildtotalPrice = totalPrice.subtract(totalPromotionAmount);
                } else {
                    // 其他订单按比例减免
                    if (Objects.nonNull(combChildOrder.getProportion())) {
                        comChildtotalPrice = totalPrice.multiply(combChildOrder.getProportion()).setScale(2, RoundingMode.HALF_UP);
                    }
                }
                combChildOrder.setTotalPrice(comChildtotalPrice);
                //子商品促销后单价，combChildOrder.getCommodityNum()已经是下单数量了
                BigDecimal commodityPrice = comChildtotalPrice.divide(combChildOrder.getCommodityNum(), 6, RoundingMode.HALF_UP);
                combChildOrder.setCommodityPrice(commodityPrice);
                totalPromotionAmount = totalPromotionAmount.add(comChildtotalPrice);
                //设置子商品订单其他字段
                combChildOrder.setSpecialsId(null);
                combChildOrder.setGiftModelId(null);
                combChildOrder.setSpecialPrice(null);
                combChildOrder.setPromotionId(null);
            }
        }
    }


    /**
     * 设置赠品最大下单数量
     */
    private void handleGiftMaxOrderQuantity(List<CreOrderItemV4DTO> creOrderItemDTOList, List<CommodityInventoryODTO> commodityInventoryODTOS) {
        //将大仓返回的库存信息按 类型分组
        Map<Integer, Map<Long, CommodityInventoryODTO>> resultMap = commodityInventoryODTOS.stream()
                .collect(Collectors.groupingBy(
                        CommodityInventoryODTO::getLevel,
                        Collectors.toMap(CommodityInventoryODTO::getCommodityId, Function.identity())));
        //赠品库存信息
        Map<Long, CommodityInventoryODTO> giftMap = resultMap.get(ProductTypeEnum.GIFT.getCode());

        //遍历赠品订单 设置赠品最大下单数量
        if (MapUtils.isNotEmpty(giftMap)) {
            List<CreOrderItemV4DTO> giftList = creOrderItemDTOList.stream().filter(x -> x.getType().getCode().equals(ProductTypeEnums.GIFT.getCode())).collect(Collectors.toList());
            for (CreOrderItemV4DTO itemV4DTO : giftList) {
                Long commodityId = itemV4DTO.getCommodityId();
                CommodityInventoryODTO commodityInventoryODTO = giftMap.get(commodityId);
                if (Objects.nonNull(commodityInventoryODTO)) {
                    BigDecimal giftQuantity = commodityInventoryODTO.getInventoryQuantity();
                    if (itemV4DTO.getQuantity().compareTo(giftQuantity) < 0) {
                        commodityInventoryODTO.setInventoryQuantity(giftQuantity.subtract(itemV4DTO.getQuantity()));
                        itemV4DTO.setQuantity(giftQuantity.min(itemV4DTO.getQuantity()));
                    } else {
                        itemV4DTO.setQuantity(giftQuantity);
                    }
                }
            }
        }

        creOrderItemDTOList.removeIf(item -> item.getType().getCode().equals(ProductTypeEnums.GIFT.getCode()) && item.getQuantity().compareTo(BigDecimal.ZERO) <= 0);
    }

    /**
     * 处理非组合商品库存不足
     */
    private  void handleNotCompositeProductShortage(List<CreOrderItemV4DTO> creOrderItemDTOList, Map<Long, CommodityInventoryODTO> inventoryODTOMap, Order order, List<BStockLackVO> voList) {
        for (Map.Entry<Long, CommodityInventoryODTO> entry : inventoryODTOMap.entrySet()) {
            Long commodityId = entry.getKey();
            CommodityInventoryODTO inventoryODTO = entry.getValue();
            for (CreOrderItemV4DTO dto : creOrderItemDTOList) {
                BigDecimal quantity = dto.getQuantity();
                if (Objects.equals(commodityId, dto.getCommodityId())) {
                    if (quantity.compareTo(inventoryODTO.getInventoryQuantity()) > 0) {
                        BStockLackVO bStockLackVO = new BStockLackVO();
                        bStockLackVO.setStoreId(order.getStoreId());
                        bStockLackVO.setCommodityId(inventoryODTO.getCommodityId());
                        bStockLackVO.setStockQuantity(inventoryODTO.getInventoryQuantity());
                        bStockLackVO.setNeedQuantity(quantity);
                        bStockLackVO.setStockType(inventoryODTO.getStockType());
                        bStockLackVO.setOrderType(OrderTypeEnum.XDA_APP_ORDER.getCode());
                        bStockLackVO.setCreateId(order.getStoreId());
                        voList.add(bStockLackVO);
                    }
                }
            }
        }
    }

    /**
     * 处理组合商品库存不足
     *
     * @param map              key为组合商品，value为子商品列表
     * @param inventoryODTOMap 大仓返回的库存
     * @param order            订单信息
     * @param combInventoryMap 组合商品的库存依据
     * @param voList           库存不足消息
     */
    private void handleCompositeProductShortage(Map<CreOrderItemV4DTO, List<CommodityItemODTO>> map, Map<Long, CommodityInventoryODTO> inventoryODTOMap,
                                                Order order, Map<Long, CommodityInventoryODTO> combInventoryMap, List<BStockLackVO> voList) {
        for (Map.Entry<CreOrderItemV4DTO, List<CommodityItemODTO>> entry : map.entrySet()) {
            CreOrderItemV4DTO itemV4DTO = entry.getKey();
            List<CommodityItemODTO> odtoList = entry.getValue();
            BigDecimal minStock = toBService.calculateMinStock(odtoList, inventoryODTOMap);
            if (itemV4DTO.getQuantity().compareTo(minStock) < 0) {
                //库存充足
                for (CommodityItemODTO commodityItemODTO : odtoList) {
                    Long commodityItemId = Long.valueOf(commodityItemODTO.getCommodityItemId());
                    CommodityInventoryODTO inventoryODTO = inventoryODTOMap.get(commodityItemId);
                    if (Objects.nonNull(inventoryODTO)){
                        //子商品下单数量
                        BigDecimal itemOrderNum = commodityItemODTO.getCommodityNum().multiply(itemV4DTO.getQuantity());
                        //剩余库存
                        BigDecimal inventoryLeft = inventoryODTO.getInventoryQuantity().subtract(itemOrderNum);
                        inventoryODTO.setInventoryQuantity(inventoryLeft);
                    }
                }
            }else {
                //库存不足
                BStockLackVO bStockLackVO = new BStockLackVO();
                bStockLackVO.setStoreId(order.getStoreId());
                bStockLackVO.setCommodityId(itemV4DTO.getCommodityId());
                bStockLackVO.setStockQuantity(minStock);
                bStockLackVO.setNeedQuantity(itemV4DTO.getQuantity());
                CommodityInventoryODTO commodityInventoryODTO = combInventoryMap.get(itemV4DTO.getCommodityId());
                if (Objects.nonNull(commodityInventoryODTO)){
                    bStockLackVO.setStockType(commodityInventoryODTO.getStockType());
                }
                bStockLackVO.setOrderType(OrderTypeEnum.XDA_APP_ORDER.getCode());
                bStockLackVO.setCreateId(order.getStoreId());
                voList.add(bStockLackVO);
            }
        }
    }

    /**
     * 组装map，key为组合商品，value为子商品列表
     */
    private static Map<CreOrderItemV4DTO, List<CommodityItemODTO>> getCommodityItemListMap(List<CreOrderItemV4DTO> creOrderItemDTOList, List<CreOrderItemV4DTO> combOrderItems) {
        Map<CreOrderItemV4DTO, List<CommodityItemODTO>> map = new HashMap<>();
        for (CreOrderItemV4DTO combOrderItem : combOrderItems) {
            List<CommodityItemODTO> itemODTOList = creOrderItemDTOList
                    .stream()
                    //过滤出组合商品的子商品订单
                    .filter(x -> Objects.equals(x.getCombType(), CombTypeEnum.COMB_CHILD.getCode()) && Objects.equals(x.getCombCommodityId(), combOrderItem.getCommodityId()))
                    .map(y -> {
                        CommodityItemODTO commodityItemODTO = new CommodityItemODTO();
                        commodityItemODTO.setCommodityItemId(String.valueOf(y.getCommodityId()));
                        commodityItemODTO.setCommodityNum(y.getQuantity().divide(combOrderItem.getQuantity()));
                        return commodityItemODTO;
                    })
                    .collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(itemODTOList)){
                map.put(combOrderItem, itemODTOList);
            }
        }
        return map;
    }

    /**
     * 把订单明细转换成查询大仓库存的orderList
     */
    private static List<CommodityInventoryDetailIDTO> convert2OrderCommodityList(List<CreOrderItemV4DTO> creOrderItemDTOList) {
        if (CollectionUtils.isEmpty(creOrderItemDTOList)) {
            return Collections.emptyList();
        }
        return creOrderItemDTOList
                .stream()
                //不查询组合商品的库存
                .filter(x -> !Objects.equals(x.getCombType(), CombTypeEnum.COMB.getCode()))
                .map(item -> {
                    CommodityInventoryDetailIDTO idto = new CommodityInventoryDetailIDTO();
                    idto.setCommodityId(item.getCommodityId());
                    idto.setQuantity(item.getQuantity());
                    idto.setLevel(ProductTypeEnum.NORMAL.getCode());
                    if (Objects.equals(item.getType().getCode(), ProductTypeEnums.PRODUCT.getCode())) {
                        if (Objects.equals(item.getCombType(), CombTypeEnum.NOT_COMB.getCode())) {
                            //非组合
                            idto.setLevel(StockLevelEnums.NORMAL.getCode());
                        } else if (Objects.equals(item.getCombType(), CombTypeEnum.COMB_CHILD.getCode())) {
                            //组合子品
                            idto.setLevel(StockLevelEnums.NORMAL_COMB.getCode());
                        }
                    } else if (item.getType().getCode().equals(ProductTypeEnums.GIFT.getCode())) {
                        if (Objects.equals(item.getCombType(), CombTypeEnum.NOT_COMB.getCode())) {
                            idto.setLevel(StockLevelEnums.GIFT.getCode());
                        } else if (Objects.equals(item.getCombType(), CombTypeEnum.COMB_CHILD.getCode())) {
                            idto.setLevel(StockLevelEnums.GIFT_COMB.getCode());
                        }
                    } else if (item.getType().getCode().equals(ProductTypeEnums.TH.getCode())) {
                        if (Objects.equals(item.getCombType(), CombTypeEnum.NOT_COMB.getCode())) {
                            idto.setLevel(StockLevelEnums.TH.getCode());
                        } else if (Objects.equals(item.getCombType(), CombTypeEnum.COMB_CHILD.getCode())) {
                            idto.setLevel(StockLevelEnums.TH_COMB.getCode());
                        }
                    }
                    return idto;
                }).collect(Collectors.toList());
    }


    /**
     * 通达销售 --> 更新订单状态
     * @param orderId
     * @param processStatus
     */
    @Transactional(rollbackFor = Exception.class)
    public void updateOrderStatus(Long orderId, Integer processStatus, LogisticsDeliveryMessage dto) {
        log.info("更新通达订单状态，订单id：{}，状态：{}", orderId, processStatus);

        QYAssert.isTrue(orderId != null, "订单id不能为空");
        Order order = orderMapper.selectByPrimaryKey(orderId);
        QYAssert.isTrue(order != null, "订单不存在" + orderId);

        // 大仓会多次执行发货，更新一次出库中就不再更新状态了。
        if(processStatus.equals(XdaOrderProcessStatusEunm.OUT_STOCKING.getCode())
                && !Integer.valueOf(XdaOrderProcessStatusEunm.WAITING_PICK.getCode()).equals(order.getProcessStatus())){
            log.warn("大仓会多次执行发货，更新一次出库中就不再更新状态了. processStatus {} ", order.getProcessStatus());
            return;
        }

        order.setProcessStatus(processStatus);
        order.setUpdateTime(new Date());

        // 配送完成状态
        if(processStatus.equals(XdaOrderProcessStatusEunm.DELIVERY_COMPLETED.getCode())){
            order.setRealOrderTime(new Date());
            order.setSettleOrderTime(new Date());
            order.setRealSubOrderDoneStatus(YesOrNoEnums.YES.getCode());
        }

        if(dto.getDriverId() != null && dto.getDriverId() > 0){
            order.setDriverId(dto.getDriverId());
        }

        // 订单实发全是0,订单取消
        if(processStatus.equals(XdaOrderProcessStatusEunm.CANCEL.getCode())){
            // 如果客户在门店表，比如大店。就不需要取消订单
            List<Shop> shopList =  shopService.getShopByStoreIdList(Collections.singletonList(order.getStoreId()));
            if(CollectionUtils.isEmpty(shopList)) {
                order.setOrderStatus(2);

                // 记录订单日志
                orderHistoryService.insertSystemOrderHistoryOnCancelOrder(order);

                //商品库存解冻
                if(!toBService.warehouseUnfreezeInventory(order.getId(), order.getStoreId(),dto.getBusinessType())){
                    QYAssert.isFalse("库存解冻,订单取消失败!");
                }

                TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronizationAdapter() {
                    @Override
                    public void afterCommit() {
                        orderAsyncKafkaService.sendKafkaCancelOrder(order);
                    }
                });
            }
        }
        orderMapper.updateByPrimaryKeySelective(order);
    }

    /**
     * 订单页优惠券列表
     */
    public XdaOrderCouponODTO xdaOrderCouponList(XdaPreOrderV4IDTO xdaPreOrderV4IDTO) {
        // 通达校验截单时间
        tdaOrderService.checkDeliveryTimeRange(xdaPreOrderV4IDTO.getStoreId(), xdaPreOrderV4IDTO.getDeliverytimerange(), xdaPreOrderV4IDTO.getDeliverybatch(), true);

        Long storeId = xdaPreOrderV4IDTO.getStoreId();
        // 客户基本信息
        StoreSelectODTO selectODTO = storeManageClient.getStoreData(storeId);
        QYAssert.notNull(selectODTO, "客户不存在");
        // 客户账户类型
        QueryXdaUserAccountDTO queryXdaUserAccountDTO = new QueryXdaUserAccountDTO();
        queryXdaUserAccountDTO.setStoreId(storeId);
        XdaUserAccountDTO xdaUserAccountDTO = xdaStoreUserClient.queryUserAccountInfo(queryXdaUserAccountDTO);
        QYAssert.notNull(xdaUserAccountDTO, "客户账户不存在");
        BuildXdaShoppingCartV4 buildXdaShoppingCartV4 = new BuildXdaShoppingCartV4(storeId, xdaPreOrderV4IDTO.getOrderTime(), toBService, xdaShoppingCartMapper, orderMapper, orderListMapper, xdaCommodityFrontClient, xdaShoppingCartController, storeService, storeDurationMapper, commodityFreezeGroupMapper, mtPromotionClient, xdaPreOrderV4IDTO.getDeliverytimerange(), xdaPreOrderV4IDTO.getDeliverybatch(), redissonClient, xdaPreOrderV4IDTO.getCouponUserId());
        ShoppingCartV4ODTO shoppingCart = buildXdaShoppingCartV4.buildCommodityInfo(buildXdaShoppingCartV4.queryShoppingCart(storeId, null, null));
        buildXdaShoppingCartV4.processXdaCoupon(shoppingCart);
        //获取券列表
        MtOrderCouponODTO mtOrderCouponODTO = mtCouponService.orderCouponList(storeId, xdaPreOrderV4IDTO.getCouponUserId(), shoppingCart);
        XdaOrderCouponODTO xdaOrderCouponODTO = Objects.isNull(mtOrderCouponODTO) ?  XdaOrderCouponODTO.initOrderCoupon() : BeanCloneUtils.copyTo(mtOrderCouponODTO, XdaOrderCouponODTO.class);
        log.info("订单优惠券列表:{}", JSON.toJSONString(xdaOrderCouponODTO));
        return xdaOrderCouponODTO;
    }

    /**
     * 获取存在缺货的赠品
     */
    private List<ShoppingCartCommodityV4ODTO> getGiftExceedLimitCommodities(ShoppingCartV4ODTO shoppingCartV4ODTO) {
        return shoppingCartV4ODTO.getPromotionGroup().stream()
                .flatMap(group -> group.getCommodities().stream())
                .filter(cartCommodity -> BooleanUtils.isTrue(cartCommodity.getIsGiftExceedLimit()))
                .collect(Collectors.toList());
    }

    public WaybillAndRouteInfoODTO queryLogisticsByOrderId(Long orderId) {
        SelectWaybillAndRouteInfoIDTO infoIDTO = new SelectWaybillAndRouteInfoIDTO();
        infoIDTO.setOrderId(orderId);
        infoIDTO.setQueryType(2);
        return transportClient.selectWaybillAndRouteInfo(infoIDTO);
    }

    /**
     * 已支付订单满足如下条件：可取消
     * <p>
     * 0、订单类型是鲜达app
     * 1、未超过截单时间
     * 2、订单状态为：正常
     * 3、通达流程状态为：待拣货、出库中、待揽收
     * 4、B端全国流程状态为：待拣货
     * 5、非通达、非B端全国流程状态为：待发货
     * </P>
     *
     * @param order           订单
     * @param assertOnFailure 是否抛异常
     * @return 校验结果
     */
    public boolean checkCancelOrderValidity(Order order, boolean assertOnFailure) {
        if (Objects.isNull(order)) {
            return false;
        }

        // 非鲜达（客服/生鲜）订单不展示取消按钮
        boolean isXdaOrder = Objects.equals(order.getOrderType(), OrderTypeEnum.XDA_APP_ORDER.getCode());
        if (!isXdaOrder && assertOnFailure) {
            QYAssert.isFalse("无法取消非鲜达app订单!");
        }

        // 校验截单时间
        boolean durationTimeCheck = DateUtil.compareDate(new Date(), order.getOrderDurationTime());
        if (!durationTimeCheck && assertOnFailure) {
            QYAssert.isFalse("订单已过截单时间无法取消!");
        }

        // 校验订单状态
        boolean orderStatusCheck = order.getOrderStatus() == 0;
        if (!orderStatusCheck && assertOnFailure) {
            QYAssert.isFalse("当前状态不可以取消订单");
        }

        // 校验流程状态
        boolean processStatusCheck = isProcessStatusCheck(order);

        if (!processStatusCheck && assertOnFailure) {
            QYAssert.isFalse("当前状态不可以取消订单");
        }

        // 如果不需要抛出异常，直接返回校验结果
        if (!assertOnFailure) {
            return isXdaOrder && durationTimeCheck && orderStatusCheck && processStatusCheck;
        }

        return false;
    }

    private boolean isProcessStatusCheck(Order order) {
        boolean processStatusCheck;
        boolean isTdaStore = DeliveryOrderTypeEnums.TD_SALE.getCode().equals(order.getBusinessType());
        boolean isCountryStore = DeliveryOrderTypeEnums.B_COUNTRY.getCode().equals(order.getBusinessType());
        boolean isBigShop = BusinessTypeEnums.BIGSHOP_SALE.getCode().equals(order.getBusinessType());
        if (isCountryStore) {
            processStatusCheck = Integer.valueOf(XdaOrderProcessStatusEunm.WAITING_PICK.getCode()).equals(order.getProcessStatus());
        } else if (isTdaStore || isBigShop) {
            processStatusCheck = Integer.valueOf(XdaOrderProcessStatusEunm.WAITING_PICK.getCode()).equals(order.getProcessStatus())
                    || Integer.valueOf(XdaOrderProcessStatusEunm.OUT_STOCKING.getCode()).equals(order.getProcessStatus())
                    || Integer.valueOf(XdaOrderProcessStatusEunm.WAITING_COLLECT.getCode()).equals(order.getProcessStatus());
        } else {
            processStatusCheck = Integer.valueOf(XdaOrderProcessStatusEunm.WAITING_SHIP.getCode()).equals(order.getProcessStatus());
        }
        return processStatusCheck;
    }

}

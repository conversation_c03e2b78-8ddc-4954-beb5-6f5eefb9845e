package com.pinshang.qingyun.order.dto.bigShop;

import com.pinshang.qingyun.base.enums.xd.StorageAreaEnum;
import com.pinshang.qingyun.order.vo.order.SaleReturnOrderPicVO;
import com.pinshang.qingyun.renderer.annotation.FieldRender;
import com.pinshang.qingyun.renderer.constant.RenderFieldHelper;
import com.pinshang.qingyun.renderer.enums.FieldTypeEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * @Author: sk
 * @Date: 2024/10/11
 */
@Data
public class DdReceiveDocCommodityODTO {

    private String commodityId;
    private String commodityCode;
    private String commodityName;

    @ApiModelProperty("主条形码")
    private String barCode;
    private String barCodes;
    @ApiModelProperty("条形码list")
    private List<String> barCodeList;

    private Long commodityUnitId; // 单位id
    @ApiModelProperty("规格")
    private String commoditySpec;

    @ApiModelProperty("单位")
    @FieldRender(fieldType = FieldTypeEnum.DICTIONARY,fieldName = RenderFieldHelper.Dictionary.optionName,keyName = "commodityUnitId")
    private String unit;

    @ApiModelProperty("是否称重0-不称量,1-称重")
    private Integer isWeight;


    @ApiModelProperty("订货数量")
    private BigDecimal quantity;
    @ApiModelProperty("发货数量")
    private BigDecimal realDeliveryQuantity;
    @ApiModelProperty("实收数量")
    private BigDecimal realReceiveQuantity;

    @ApiModelProperty("1, 排面区  2, 拣货区 3, 仓储区")
    private Integer storageArea;
    private String storageAreaName;

    @ApiModelProperty("货位ID")
    private Long goodsAllocationId;

    @ApiModelProperty("货位号")
    private String goodsAllocationCode;

    @ApiModelProperty("是否PDA收货 1=是  0 =否")
    private Integer pdaStatus = 0;

    @ApiModelProperty("pda收货人id")
    private Long receiveUserId;
    @ApiModelProperty("pda收货人名称")
    @FieldRender(fieldType = FieldTypeEnum.USER,fieldName = RenderFieldHelper.User.realName,keyName = "receiveUserId")
    private String receiveUserName;
    @ApiModelProperty("pda收货时间")
    private String receiveTime;
    @ApiModelProperty("pda收货备注")
    private String remark;

    @ApiModelProperty("图片list")
    private List<SaleReturnOrderPicVO> picList;

    @ApiModelProperty("视频list")
    private List<SaleReturnOrderPicVO> videoList;

    public String getStorageAreaName() {
        StorageAreaEnum storageAreaEnum = StorageAreaEnum.getTypeEnumByCode(storageArea);
        return null == storageAreaEnum ? null : storageAreaEnum.getName();
    }
}

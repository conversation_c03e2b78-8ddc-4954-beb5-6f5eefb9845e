package com.pinshang.qingyun.order.model.promotionStk;

import java.math.BigDecimal;

import javax.persistence.Entity;
import javax.persistence.Table;
import javax.persistence.Transient;

import com.pinshang.qingyun.base.po.BaseIDPO;

/**
 * 配货方案商品相应的设定表
 **/
@Entity
@Table(name = "t_promotion_stk_comm")
public class PromotionStkComm extends BaseIDPO {
	/**优惠ID **/
    private Long promotionId;
	/**配货订单限制表ID **/
    private Long stkCodeId;
	/**商品Id **/
    private Long commId;
	/**数量 **/
    private Long number;
	/**限量数量 **/
    private Long limes;
	/**创建用户Id **/
    private Long createUserId;

    @Transient
    private BigDecimal commodityPrice;
    
    @Transient
    private String productCode;

    @Transient
    private BigDecimal originalPrice;

    @Transient
    private Integer changePriceStatus;

    public Integer getChangePriceStatus() {
        return changePriceStatus;
    }

    public void setChangePriceStatus(Integer changePriceStatus) {
        this.changePriceStatus = changePriceStatus;
    }

    public BigDecimal getOriginalPrice() {
        return originalPrice;
    }

    public void setOriginalPrice(BigDecimal originalPrice) {
        this.originalPrice = originalPrice;
    }

    public Long getPromotionId(){
        return promotionId;
    }
    public void setPromotionId(Long promotionId){
        this.promotionId = promotionId;
    }
    public Long getStkCodeId(){
        return stkCodeId;
    }
    public void setStkCodeId(Long stkCodeId){
        this.stkCodeId = stkCodeId;
    }
    public Long getCommId(){
        return commId;
    }
    public void setCommId(Long commId){
        this.commId = commId;
    }
    
    public Long getNumber() {
		return number;
	}
	public void setNumber(Long number) {
		this.number = number;
	}
	public Long getLimes(){
        return limes;
    }
    public void setLimes(Long limes){
        this.limes = limes;
    }
    public Long getCreateUserId(){
        return createUserId;
    }
    public void setCreateUserId(Long createUserId){
        this.createUserId = createUserId;
    }
	public BigDecimal getCommodityPrice() {
		return commodityPrice;
	}
	public void setCommodityPrice(BigDecimal commodityPrice) {
		this.commodityPrice = commodityPrice;
	}
	public String getProductCode() {
		return productCode;
	}
	public void setProductCode(String productCode) {
		this.productCode = productCode;
	}
}
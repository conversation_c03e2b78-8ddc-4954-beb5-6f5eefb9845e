package com.pinshang.qingyun.order.dto.xforder;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.pinshang.qingyun.base.enums.TerminalSourceTypeEnum;
import com.pinshang.qingyun.base.enums.xd.XdPayTypeEnum;
import com.pinshang.qingyun.base.enums.xda.XdaStoreTypeEnum;
import com.pinshang.qingyun.order.dto.xda.v4.XdaCreatePrePayOrderV4IDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 创建 现付单
 */
@Data
@NoArgsConstructor
public class CreateXfOrderIDTO extends XdaCreatePrePayOrderV4IDTO {
	@JsonIgnore
	@ApiModelProperty(position = 0, required = true, value = "客户ID", hidden = true)
	private Long storeId;
	@JsonIgnore
	@ApiModelProperty(position = 0, required = true, value = "客户用户ID", hidden = true)
	private Long userId;
	@JsonIgnore
	@ApiModelProperty(position = 0, required = true, value = "appCode", hidden = true)
	private String appCode;

	// ==========充值参数-起始，参见RechargeVo==========
	@ApiModelProperty(position = 21, required = true, value = "充值参数-充值金额")
	private BigDecimal payAmount;
	@ApiModelProperty(position = 22, required = true, value = "充值参数-支付方式：1＝支付宝，2＝微信，3=小程序，5=云闪付支付")
	private XdPayTypeEnum payType;
	@ApiModelProperty(position = 23, required = true, value = "充值参数-微信支付时需要")
	private String clientIp;
	@ApiModelProperty(position = 24, required = true, value = "充值参数-如果为小程序下单,需传入jsCode")
	private String jsCode;
	@ApiModelProperty(position = 25, required = true, value = "充值参数-订单号")
	private String billCode;
	// ==========充值参数-结束，参见RechargeVo==========

	// ==========订单参数-起始，参见XdaCreatePrePayOrderV3IDTO==========
	@ApiModelProperty(position = 32, required = true, value = "订单参数-订单金额")
	private BigDecimal orderAmount;
	@ApiModelProperty(position = 33, required = true, value = "订单参数-订单日期")
	private Date orderTime;
	@ApiModelProperty(position = 34, required = true, value = "订单参数-品种合计")
	private Integer varietySum;
	@ApiModelProperty(position = 34, required = true, value = "订单参数-商品数量合计")
	private BigDecimal commodityNum;
	@JsonIgnore
	@ApiModelProperty(position = 35, required = true, value = "订单参数-客户类型", hidden = true)
	private XdaStoreTypeEnum storeType;

	@ApiModelProperty(position = 36, required = true, value = "订单参数-客户类型", hidden = true)
	private String appVersion;
//	@JsonIgnore
//	@ApiModelProperty(position = 36, required = true, value = "订单参数-订单截止时间", hidden = true)
//	private String storeEndTime;
	@JsonIgnore
	@ApiModelProperty(position = 37, required = true, value = "订单参数-typeEnum", hidden = true)
	private TerminalSourceTypeEnum typeEnum;
	// ==========订单参数-结束，参见XdaCreatePrePayOrderV3IDTO==========

	@ApiModelProperty("订单id")
	private Long orderId;

	@ApiModelProperty("用户优惠券id\n" +
			"--传 null 或空值： 表示系统推荐最优优惠券。\n" +
			"--传 -1： 表示用户不使用优惠券。\n" +
			"--传 -2： 表示老版本不使用优惠券。\n" +
			"--传实际的 couponUserId 值： 表示指定使用该优惠券。")
	private Long couponUserId;
}

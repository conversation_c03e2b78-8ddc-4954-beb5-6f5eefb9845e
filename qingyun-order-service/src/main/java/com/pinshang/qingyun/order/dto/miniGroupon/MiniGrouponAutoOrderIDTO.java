package com.pinshang.qingyun.order.dto.miniGroupon;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@Data
@ApiModel
public class MiniGrouponAutoOrderIDTO {
    @ApiModelProperty("门店Id")
    private Long shopId;
    @ApiModelProperty("商品Id")
    private Long commodityId;
    @ApiModelProperty("订单金额")
    private BigDecimal orderAmount;
    @ApiModelProperty("特价订单金额")
    private BigDecimal promotionAmount;
    @ApiModelProperty("数量")
    private BigDecimal quantity;
    @ApiModelProperty("单价")
    private BigDecimal originPrice;
    @ApiModelProperty("促销单价")
    private BigDecimal promotionPrice;
    @ApiModelProperty("截单时间 yyyy-MM-dd HH:mm:ss")
    private String cutTime;
    @ApiModelProperty("提货时间")
    private String orderTime;
    @ApiModelProperty("mini团购订单Id")
    private Long miniGrouponOrderId;
    @ApiModelProperty(value = "配送模式",hidden = true)
    private Integer logisticsModel;
    @ApiModelProperty(value = "默认供应商id",hidden = true)
    private Long supplierId;
    @ApiModelProperty(value = "默认仓库id",hidden = true)
    private Long warehouseId;

    public BigDecimal getOrderAmount() {
        if(this.getQuantity() != null && this.getOriginPrice() !=null){
            this.setOrderAmount(this.getQuantity().multiply(this.getOriginPrice()));
        }
        return orderAmount;
    }

    public BigDecimal getPromotionAmount() {
        if(this.getQuantity() != null ){
            if(this.getPromotionPrice() != null){
                this.setPromotionPrice(this.getQuantity().multiply(this.getPromotionPrice()));
            }else if(this.getOriginPrice() != null){
                this.setPromotionPrice(this.getQuantity().multiply(this.getOriginPrice()));
            }
        }
        return promotionAmount;
    }

    public String getClassifyKey(){
        boolean flag = this.logisticsModel == null || this.warehouseId == null
                || (this.logisticsModel == 0 && this.supplierId == null);
        if(flag){
            return "classifyKey";
        }else {
            return this.logisticsModel+"_"+this.warehouseId+"_"+this.supplierId;
        }
    }

}

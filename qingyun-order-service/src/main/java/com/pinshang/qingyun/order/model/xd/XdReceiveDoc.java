package com.pinshang.qingyun.order.model.xd;

import com.pinshang.qingyun.base.po.BaseSimplePO;

import javax.persistence.Entity;
import javax.persistence.Table;
import java.util.Date;

/**
 * @Author: sk
 * @Date: 2020/3/17
 */
@Entity
@Table(name="t_xd_receive_doc")
public class XdReceiveDoc extends BaseSimplePO {

    /** 前置仓id **/
    private Long shopId;

    /** 单据号 **/
    private String docCode;

    /** 送货日期 **/
    private Date deliveryTime;

    /** 单据状态：0 待收货 1 已关闭 **/
    private Integer docStatus;

    public Long getShopId() {
        return shopId;
    }

    public void setShopId(Long shopId) {
        this.shopId = shopId;
    }

    public String getDocCode() {
        return docCode;
    }

    public void setDocCode(String docCode) {
        this.docCode = docCode;
    }

    public Date getDeliveryTime() {
        return deliveryTime;
    }

    public void setDeliveryTime(Date deliveryTime) {
        this.deliveryTime = deliveryTime;
    }

    public Integer getDocStatus() {
        return docStatus;
    }

    public void setDocStatus(Integer docStatus) {
        this.docStatus = docStatus;
    }
}

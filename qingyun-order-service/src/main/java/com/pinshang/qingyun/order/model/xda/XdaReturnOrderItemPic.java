package com.pinshang.qingyun.order.model.xda;


import com.pinshang.qingyun.base.po.BaseIDPO;
import lombok.Data;

import javax.persistence.*;
import java.util.Date;


/**
 * <AUTHOR>
 */
@Data
@Entity
@Table(name = "t_xda_return_order_item_pic")
public class XdaReturnOrderItemPic  extends BaseIDPO {

    /**
     * 退货单明细id
     */
    private Long returnOrderItemId;

    /**
     * 用户实拍商品图URL
     */
    private String imgPicUrl;

    /**
     * 备注
     */
    private String remark;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;
}

package com.pinshang.qingyun.order.job;

import com.pinshang.qingyun.order.mapper.PurchaseOrderMapper;
import com.pinshang.qingyun.storage.service.CommoditySupplierClient;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.quartz.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.quartz.SchedulerFactoryBean;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2017/6/19.
 */
@Component
@Slf4j
public class GeneratePurchaseOrderTasks {


    @Autowired
    PurchaseOrderMapper purchaseOrderMapper;

    @Autowired
    private CommoditySupplierClient commoditySupplierClient;

//    private final Executor executor = Executors.newFixedThreadPool(8, new ThreadFactory() {
//        @Override
//        public Thread newThread(Runnable r) {
//            Thread t = new Thread(r);
//            return t;
//        }
//    });


    @Autowired
    private SchedulerFactoryBean schedulerFactoryBean;

    //@Scheduled(cron ="0 0/10 * * * ?")
    public void scheduleJobs(){
        List<String> supplierEndTimeList = commoditySupplierClient.getSupplierEndTime();

        if(null!=supplierEndTimeList){
            supplierEndTimeList.forEach(
                    endTime->{
                        Scheduler scheduler = schedulerFactoryBean.getScheduler();
                        try {
                            startJob(scheduler, endTime);
                        } catch (SchedulerException e) {
                            e.printStackTrace();
                        }
                    }
            );

        }

    }

    private void startJob(Scheduler scheduler, String endTime) throws SchedulerException {
    	if(StringUtils.isBlank(endTime)){
    		return;
    	}
        JobDataMap jobDataMap = new JobDataMap();
//        jobDataMap.put("fixedThreadPoolExecutor", executor);
//        jobDataMap.put("subOrder2DeliveryOrderService", subOrder2DeliveryOrderService);
        jobDataMap.put("supplierEndTime", endTime);
        String jobName = "job" + endTime;
        String triggerName = "trigger" + endTime;
        String groupName = "group1";
        JobKey jobKey = JobKey.jobKey(jobName, groupName);
        if (null != jobKey) {
            scheduler.deleteJob(jobKey);
        }
        String cronExp = buildCronExpression(endTime);
        JobDetail jobDetail = JobBuilder.newJob(GeneratePurchaseOrderJob.class).withIdentity(jobName, groupName).usingJobData(jobDataMap).build();
        CronScheduleBuilder scheduleBuilder = CronScheduleBuilder.cronSchedule(cronExp);
        TriggerKey triggerKey = TriggerKey.triggerKey(jobName, groupName);
        CronTrigger cronTrigger = (CronTrigger) scheduler.getTrigger(triggerKey);
        if (null != cronTrigger) {
            cronTrigger = cronTrigger.getTriggerBuilder().withIdentity(triggerKey)
                    .withSchedule(scheduleBuilder).build();
        } else {
            cronTrigger = TriggerBuilder.newTrigger().withIdentity(triggerName, groupName).withSchedule(scheduleBuilder).build();
        }
        scheduler.scheduleJob(jobDetail, cronTrigger);
    }




    private String buildCronExpression(String endTime) {

//        String cronExpression =  "0 0/1 * * * ? *";
        String cronExpression = "";
        String[] times = endTime.split(":");
        if ("25".equals(times[1]) || "55".equals(times[1])) {
            Integer minute =  Integer.valueOf(times[1])+2;
            cronExpression = "0 " + minute.toString() + " " + times[0] + " * * ? *";
        }else {
            cronExpression = "0 " + times[1] + " " + times[0] + " * * ? *";
        }
        log.info("generator time cronExpression [{}]", cronExpression);
        return cronExpression;
    }
}

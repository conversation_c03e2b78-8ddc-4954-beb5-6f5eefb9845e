package com.pinshang.qingyun.order.dto.xda;

import com.pinshang.qingyun.base.enums.TerminalSourceTypeEnum;
import com.pinshang.qingyun.base.enums.xda.XdaStoreTypeEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
@ApiModel
public class CreatePrePayOrderIDTO {
    @ApiModelProperty(hidden = true)
    private Long storeId;
    @ApiModelProperty("备注")
    private String remark;
    @ApiModelProperty("订单金额 ")
    private BigDecimal orderAmount;
    @ApiModelProperty("加密后支付密码，预售客户必穿")
    private String payPassword;
    @ApiModelProperty("订货日期")
    private Date orderTime;
    @ApiModelProperty(value = "appCode", hidden = true)
    private String appCode;
    @ApiModelProperty(value = "客户类型", hidden = true)
    private XdaStoreTypeEnum storeType;
    @ApiModelProperty(value = "订单截止时间", hidden = true)
    private String storeEndTime;
    @ApiModelProperty("品种合计")
    private Integer varietySum;
    @ApiModelProperty(value = "userId", hidden = true)
    private Long userId;
    @ApiModelProperty(value = "typeEnum", hidden = true)
    private TerminalSourceTypeEnum typeEnum;

    public void convert(Long storeId, String appCode, Long userId, TerminalSourceTypeEnum typeEnum){
        this.storeId = storeId;
        this.appCode = appCode;
        this.userId = userId;
        this.typeEnum = typeEnum;
    }

}

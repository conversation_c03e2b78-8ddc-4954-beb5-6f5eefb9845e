//package com.pinshang.qingyun.order.util.email;
//
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.boot.context.properties.EnableConfigurationProperties;
//import org.springframework.context.annotation.Bean;
//
//@EnableConfigurationProperties(value = {EmailConfigProperties.class})
//public class EmailServiceDefinition {
//
//	@Autowired
//	private EmailConfigProperties emailConfigProperties;
//
//	@Bean
//	public EmailService emailServiceDefinition(){
//		EmailService email = new EmailService();
//		email.setHostName(emailConfigProperties.getHostName());
//		email.setUserName(emailConfigProperties.getUserName());
//		email.setUserPassword(emailConfigProperties.getUserPassword());
//		return email;
//	}
//
//}

/*
 * tramy.cn Inc.
 * Copyright (c) 2021-2024 All Rights Reserved.
 */
package com.pinshang.qingyun.order.dto.xda.tda;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <p>
 * 退货单详情
 * </p>
 *
 * <AUTHOR> shenyang
 * @version : 1.0.0
 * @history : modify history
 * <author>              <time>              <version>              <desc>
 * @since : 2024/05/11 10:09
 */
@Data
public class ReturnOrderItemODTO {
    /**
     * id序号
     */
    @ApiModelProperty("id")
    private Long id;

    /**
     * 退货单id
     */
    @ApiModelProperty("退货单id")
    private Long returnOrderId;

    /**
     * 商品id
     */
    @ApiModelProperty("商品id")
    private String commodityId;

    /**
     * 促销后单价
     */
    @ApiModelProperty("促销后单价")
    private BigDecimal commodityPrice;

    /**
     * 商品编码
     */
    @ApiModelProperty("商品编码")
    private String commodityCode;

    /**
     * 商品名称
     */
    @ApiModelProperty("商品名称")
    private String commodityName;

    /**
     * 商品app名称
     */
    @ApiModelProperty("商品app名称")
    private String commodityAppName;

    /**
     * 条码
     */
    @ApiModelProperty("条码")
    private String barCode;

    /**
     * 计量单位
     */
    @ApiModelProperty("计量单位")
    private String commodityUnitName;

    /**
     * 包装规格
     */
    @ApiModelProperty("包装规格")
    private BigDecimal commodityPackageSpec;

    /**
     * 申请份数
     */
    @ApiModelProperty("申请份数")
    private Integer applyNumber;

    /**
     * 申请商品金额
     */
    @ApiModelProperty("申请商品金额")
    private BigDecimal applyMoney;

    /**
     * 申请商品数量
     */
    @ApiModelProperty("申请商品数量")
    private BigDecimal applyQuantity;

    /**
     * 审核商品金额
     */
    @ApiModelProperty("审核商品金额")
    private BigDecimal checkMoney;

    /**
     * 审核商品数量
     */
    @ApiModelProperty("审核商品数量")
    private BigDecimal checkQuantity;

    /**
     * 审核商品份数
     */
    @ApiModelProperty("审核商品份数")
    private Integer checkNumber;

    /**
     * 确认份数
     */
    private Integer confirmNumber;

    /**
     * 退货原因类型-字典类型
     */
    @ApiModelProperty("退货原因类型-字典类型")
    private Long returnReasonType;

    /**
     * 退货订单类型：1-退货，2-少货，3-多货
     */
    @ApiModelProperty("退货订单类型：1-退货，2-少货，3-多货")
    private Integer returnOrderType;

    /**
     * 退货订单类型：1-退货，2-少货，3-多货
     */
    @ApiModelProperty("退货订单类型：1-退货，2-少货，3-多货")
    private String returnOrderTypeName;
    /**
     * 退货原因
     */
    @ApiModelProperty("退货原因")
    private String returnReason;

    /**
     * 责任方
     */
    @ApiModelProperty("责任方")
    private String responsibleParty;

    /**
     * 审核备注
     */
    @ApiModelProperty("审核备注")
    private String remark;

    /**
     * 是否已取消：1-已取消、0-正常（全部取消按钮会设置，单个取消走下面的删除逻辑）
     */
    @ApiModelProperty("是否已取消：1-已取消、0-正常")
    private Integer status;

    /**
     * 是否删除：1-已删除、0-正常（判断可用，需要保证没取消，也没删除）
     */
    @ApiModelProperty("是否已删除：1-已删除、0-正常")
    private Integer delFlag;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;
    private Long checkUserId;
    private BigDecimal commodityOrderQuantity;
    /**
     * 是否称重 0=否 1=是
     */
    private Integer isWeight;
    @ApiModelProperty("图片列表")
    private List<ReturnOrderItemPicODTO> returnOrderItemPicList;

    /**
     * 投诉内容
     */
    @ApiModelProperty("投诉内容")
    private String complaintContent;
}

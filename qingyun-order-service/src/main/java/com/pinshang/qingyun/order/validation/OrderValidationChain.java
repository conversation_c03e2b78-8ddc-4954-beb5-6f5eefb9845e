package com.pinshang.qingyun.order.validation;

import com.pinshang.qingyun.order.dto.cup.OrderRequestDto;
import org.apache.commons.compress.utils.Lists;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public class OrderValidationChain {

    private final List<OrderValidationHandler> validators;

    public OrderValidationChain(DeliveryTimeValidator tdaOrderValidator,
                                PlanOrderValidator planOrderValidator,
                                BCountryOrderValidator bCountryOrderValidator){
        this.validators = Lists.newArrayList();
        this.validators.add(tdaOrderValidator);
        this.validators.add(planOrderValidator);
        this.validators.add(bCountryOrderValidator);

        for(int i = 0;i < validators.size() - 1;i++){
            validators.get(i).setNextHandler(validators.get(i + 1));
        }
    }

    public OrderValidationResult validate(OrderRequestDto orderRequestDto) {
        if (validators.isEmpty()) {
            return new OrderValidationResult(true, "No validators configured");
        }
        return validators.get(0).validate(orderRequestDto);
    }



}

package com.pinshang.qingyun.order.vo.order;

import com.pinshang.qingyun.base.configure.expand.QYAssert;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class CountCommoditySubmitVo {

    @ApiModelProperty("商品Id")
    private Long commodityId;

    @ApiModelProperty("调整数量")
    private BigDecimal adjustNum;

    @ApiModelProperty("1=增加  2=减去 3=更新")
    private Integer type;

    public void check() {
        QYAssert.isTrue(null != commodityId, "商品ID不能为空");
        QYAssert.isTrue(null != adjustNum, "调整数量不能为空");
        QYAssert.isTrue(null != type, "调整类型不能为空");
    }
}

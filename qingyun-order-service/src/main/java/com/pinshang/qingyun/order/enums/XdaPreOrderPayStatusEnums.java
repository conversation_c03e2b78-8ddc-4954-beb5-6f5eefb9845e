package com.pinshang.qingyun.order.enums;

import com.pinshang.qingyun.box.utils.EnumUtils;

/**
 * @Author: sk
 * @Date: 2024/5/13
 * 支付状态  0 待支付   1 已经支付  2 已经取消
 */
public enum XdaPreOrderPayStatusEnums {

    INIT(0, "待支付"),
    SUCCESS(1, "已经支付"),
    CANCEL(2, "已经取消"),
    ;
    private Integer code;
    private String name;

    XdaPreOrderPayStatusEnums(Integer code, String name) {
        this.code = code;
        this.name = name;
    }

    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public static XdaPreOrderPayStatusEnums get(Integer code) {
        return EnumUtils.fromEnumProperty(XdaPreOrderPayStatusEnums.class, "code",
                code);
    }


}

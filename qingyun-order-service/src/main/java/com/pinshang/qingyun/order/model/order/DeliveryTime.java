package com.pinshang.qingyun.order.model.order;


import lombok.Data;

import javax.persistence.Entity;
import javax.persistence.Table;
import javax.persistence.Transient;
import java.util.Date;

@Entity
@Table(name = "t_delivery_time")
@Data
public class DeliveryTime {

	private Long id;

	private String beginTime;

	private String endTime;

	private String lineGroupId;

	private Date updateTime;

	private Date CreateTime;

	private Long createUserId;

	private Integer status;

	private String remark;

	private String coverTime;

	@Transient
	private Boolean coverFlag = false;//true=job在零点后执行coverTime在零点前的线路组

}

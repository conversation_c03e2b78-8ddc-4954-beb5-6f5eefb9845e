package com.pinshang.qingyun.order.service.order;/**
 * @Author: sk
 * @Date: 2025/8/1
 */

import com.pinshang.qingyun.base.configure.expand.QYAssert;
import com.pinshang.qingyun.base.enums.xda.XdaOrderProcessStatusEunm;
import com.pinshang.qingyun.box.utils.JsonUtil;
import com.pinshang.qingyun.order.dto.order.UpdateOrderIDTO;
import com.pinshang.qingyun.order.enums.OperationTypeEnum;
import com.pinshang.qingyun.order.mapper.OrderMapper;
import com.pinshang.qingyun.order.model.order.Order;
import com.pinshang.qingyun.order.service.OrderAsyncKafkaService;
import com.pinshang.qingyun.order.service.OrderService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionSynchronization;
import org.springframework.transaction.support.TransactionSynchronizationManager;
import tk.mybatis.mapper.entity.Example;

import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025年08月01日 下午12:38
 */
@Slf4j
@Service
public class OrderStatusChangeService {

    @Autowired
    private OrderMapper orderMapper;
    @Autowired
    private OrderService orderService;
    @Autowired
    private OrderAsyncKafkaService orderAsyncKafkaService;

    /**
     * 1. 计划销售订单，顺丰通知清美入库后，大仓修改订单状态为已完成
     * 2. B端全国订单，订单进大仓后，大仓修改订单状态为出库中
     * 3. B端全国订单，顺丰审核失败，大仓发起取消订单后，修改订单状态为已取消
     * @param updateOrderIDTO
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public Boolean updateOrderStatus(UpdateOrderIDTO updateOrderIDTO) {
        log.info("B端全国版订单状态变化,入参: {}", JsonUtil.java2json(updateOrderIDTO));

        QYAssert.isTrue(CollectionUtils.isNotEmpty(updateOrderIDTO.getOrderIdList()), "订单id不能为空!");
        QYAssert.isTrue(updateOrderIDTO.getFlag() != null, "订单标识不能为空!");
        QYAssert.isTrue(updateOrderIDTO.getUserId() != null, "操作人id不能为空!");
        QYAssert.isTrue(StringUtils.isNotBlank(updateOrderIDTO.getUserName()), "操作人名称不能为空!");

        Example orderEx = new Example(Order.class);
        orderEx.createCriteria().andIn("id", updateOrderIDTO.getOrderIdList());
        List<Order> orderList = orderMapper.selectByExample(orderEx);
        QYAssert.isTrue(CollectionUtils.isNotEmpty(orderList), "订单不存在,无法操作!");

        List<Order> filterOrderList = orderList.stream().filter(p -> p.getOrderStatus().intValue() != 2).collect(Collectors.toList());
        if(CollectionUtils.isEmpty(filterOrderList)) {
            log.warn("订单已被取消,无法操作! orderIdList {}", updateOrderIDTO.getOrderIdList());
            return Boolean.TRUE;
        }
        List<Long> orderIdList = filterOrderList.stream().map(item -> item.getId()).collect(Collectors.toList());


        Integer processStatus = null;
        Integer orderStatus = null;
        switch (updateOrderIDTO.getFlag()) {
            case 1:
                processStatus = XdaOrderProcessStatusEunm.DELIVERY_COMPLETED.getCode();
                break;
            case 2:
                processStatus = XdaOrderProcessStatusEunm.OUT_STOCKING.getCode();
                break;
            case 3:
                orderStatus = 2;
                break;
        }


        Example filterOrderEx = new Example(Order.class);
        filterOrderEx.createCriteria().andIn("id", orderIdList);

        Order order = new Order();
        if(processStatus != null) {
            order.setProcessStatus(processStatus);
        }

        if(orderStatus != null) {
            order.setOrderStatus(orderStatus);
        }

        order.setUpdateId(updateOrderIDTO.getUserId());
        order.setUpdateTime(new Date());
        orderMapper.updateByExampleSelective(order, filterOrderEx);

        if(updateOrderIDTO.getFlag() == 3) {
            Integer finalOrderStatus = orderStatus;
            filterOrderList.forEach(item -> {
                // 增加取消日志
                orderService.inserOrderHistory(item.getId(), Long.valueOf(item.getOrderCode()), updateOrderIDTO.getUserId(), updateOrderIDTO.getUserName(), OperationTypeEnum.ORDER_CANCEL.getCode(), item.getOrderTime(), null, null);

                // 事务完成后取消的订单给统计库发消息
                TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronization() {
                    @Override
                    public void afterCommit() {
                        // 如果是取消订单
                        if(finalOrderStatus != null && finalOrderStatus.intValue() == 2) {
                            // 给统计查询发消息
                            orderAsyncKafkaService.sendKafkaCancelOrder(order);

                            // 更新预付款余额，订单回款记录对账明细
                            orderService.orderRefund(order);
                        }
                    }
                });
            });
        }
        return Boolean.TRUE;
    }
}

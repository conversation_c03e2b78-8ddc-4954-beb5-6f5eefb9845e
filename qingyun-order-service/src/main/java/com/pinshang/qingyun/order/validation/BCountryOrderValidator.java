package com.pinshang.qingyun.order.validation;

import com.pinshang.qingyun.base.configure.expand.QYAssert;
import com.pinshang.qingyun.base.enums.BusinessTypeEnums;
import com.pinshang.qingyun.box.utils.DateUtil;
import com.pinshang.qingyun.common.dto.DictionaryODTO;
import com.pinshang.qingyun.common.service.DictionaryClient;
import com.pinshang.qingyun.order.dto.cup.OrderRequestDto;
import com.pinshang.qingyun.order.dto.xda.v4.TdaDeliveryTimeRangeODTO;
import com.pinshang.qingyun.order.mapper.StoreMapper;
import com.pinshang.qingyun.order.model.store.Store;
import com.pinshang.qingyun.order.service.tms.OrderTmsService;
import com.pinshang.qingyun.order.util.OrderTimeUtil;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.Objects;
import java.util.Optional;

@Component
@RequiredArgsConstructor
public class BCountryOrderValidator extends OrderValidationHandler{

    private final OrderTmsService orderTmsService;

    private final StoreMapper storeMapper;

    private final DictionaryClient dictionaryClient;
    @Override
    public OrderValidationResult validate(OrderRequestDto orderRequestDto) {

        if(!Objects.equals(orderRequestDto.getPlanOrderType(),1)){

            if(Objects.equals(BusinessTypeEnums.PLAN_SALE.getCode(),orderRequestDto.getBusinessType())){
                QYAssert.isFalse("计划销售类型的客户，请在计划订单中操作！");
            }
            if(Objects.equals(BusinessTypeEnums.B_COUNTRY.getCode(),orderRequestDto.getBusinessType())){
                //客户编码校验,若输入的客户业务类型为"计划销售"，报错
                validStoreBusinessType(orderRequestDto);
                //若客户业务类型为“B端全国”，则送货日期不可为当日，报错提示“B端全国业务类型的客户不支持当天下单！”
                String orderTime = orderRequestDto.getOrderTime();
                if(OrderTimeUtil.isTheSameDay(new Date(), DateUtil.parseDate(orderTime,"yyyy-MM-dd"))){
                    QYAssert.isFalse("B端全国业务类型的客户不支持当天下单！");
                }

                //送货地址校验
                validLogisticsCarrier(orderRequestDto);
            }

        }
       return validateNext(orderRequestDto);
    }

    private void validLogisticsCarrier(OrderRequestDto orderRequestDto) {
        DictionaryODTO sfProductCode = dictionaryClient.getDictionaryByCode("sfProductCode");
        String logisticsCarrierCode = Optional.ofNullable(sfProductCode).map(DictionaryODTO::getOptionValue).orElse(null);
        orderRequestDto.setLogisticsCarrierCode(logisticsCarrierCode);
        TdaDeliveryTimeRangeODTO tdaDeliveryTimeRangeODTO = orderRequestDto.getTdaDeliveryTimeRangeODTO();
        orderTmsService.checkTransportFlow(orderRequestDto.getStoreId(),BusinessTypeEnums.B_COUNTRY.getCode(),
                tdaDeliveryTimeRangeODTO.getDeliveryBatch(),tdaDeliveryTimeRangeODTO.getLogisticsCenterId(), logisticsCarrierCode);
    }

    private void validStoreBusinessType(OrderRequestDto orderRequestDto) {
        Long storeId = orderRequestDto.getStoreId();
        Store store = storeMapper.selectByPrimaryKey(storeId);
        Integer businessType = store.getBusinessType();
        QYAssert.isTrue(!Objects.equals(businessType, BusinessTypeEnums.PLAN_SALE.getCode()),"计划销售类型的客户，请在计划订单中操作！");
    }
}

package com.pinshang.qingyun.order.vo.shop;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2017/6/9.
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class QuerySubOrderVo {

   private Integer logisticsModel;

   @ApiModelProperty("订单id")
   private Long subOrderId;

   @ApiModelProperty("订单编号")
   private String subOrderCode;

   private Long storeId;

   private Integer shopType;

   private Long shopId;

}

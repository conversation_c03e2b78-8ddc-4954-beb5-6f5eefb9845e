package com.pinshang.qingyun.order.vo.order;


/**
 * 订单日志-商品添加类型
 *
 * <AUTHOR>
 *
 * @date 2017年4月6日
 */
public enum OrderHistoryAddTypeEnums {
	COMMON(1, "普通"),
	MANUAL_RATION(2, "手工配货"),
	ZERO_OUT_STOCK(3, "大仓0出库")
	;
	private Integer code;
	private String name;

	public Integer getCode() {
		return code;
	}

	OrderHistoryAddTypeEnums(Integer code, String name) {
		this.code = code;
		this.name=name;
	}
	
	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public void setCode(Integer code) {
		this.code = code;
	}

	public static String getName(Integer code) {
		if(code == null) {
			return "";
		}

		for (OrderHistoryAddTypeEnums at : OrderHistoryAddTypeEnums.values()) {
			if (code == at.getCode()) {
				return at.name;
			}
		}
		return null;
	}
	
}

package com.pinshang.qingyun.order.model.store;

import javax.persistence.Entity;
import javax.persistence.Table;

import com.pinshang.qingyun.base.po.BaseTimePO;

@Entity
@Table(name = "t_store_area")
public class StoreArea extends BaseTimePO {
	/**父节点ID **/
    private Long parentId;
	/**区域名称 **/
    private String areaName;
	/**展示排序 **/
    private Integer showOrder;
	/**1正常 0关闭 **/
    private Integer areaStatus;
	/**区域级别 **/
    private Integer areaLevel;
	/**叶子节点=1,非叶子节点=0 **/
    private Integer isLeaf;
    /**同步用**/
    private String areaCode;

    public Long getParentId(){
        return parentId;
    }
    public void setParentId(Long parentId){
        this.parentId = parentId;
    }
    public String getAreaName(){
        return areaName;
    }
    public void setAreaName(String areaName){
        this.areaName = areaName;
    }
    public Integer getShowOrder(){
        return showOrder;
    }
    public void setShowOrder(Integer showOrder){
        this.showOrder = showOrder;
    }
    public Integer getAreaStatus(){
        return areaStatus;
    }
    public void setAreaStatus(Integer areaStatus){
        this.areaStatus = areaStatus;
    }
    public Integer getAreaLevel(){
        return areaLevel;
    }
    public void setAreaLevel(Integer areaLevel){
        this.areaLevel = areaLevel;
    }
    public Integer getIsLeaf(){
        return isLeaf;
    }
    public void setIsLeaf(Integer isLeaf){
        this.isLeaf = isLeaf;
    }

    public String getAreaCode() {
        return areaCode;
    }

    public void setAreaCode(String areaCode) {
        this.areaCode = areaCode;
    }
}
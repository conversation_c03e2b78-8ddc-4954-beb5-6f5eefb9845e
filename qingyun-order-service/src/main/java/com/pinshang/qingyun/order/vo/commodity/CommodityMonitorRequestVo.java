package com.pinshang.qingyun.order.vo.commodity;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * <AUTHOR>
 * @Date 2018/10/11 10:39
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class CommodityMonitorRequestVo {
    /**
     * 产品代码
     */
    private String commodityCode;
    /**
     * 订单发货时间
     */
    private Date orderTime;
    /**
     * 下单时间
     */
    private Date createTime;
    /**
     * 企业id
     */
    private Long enterpriseId;
}

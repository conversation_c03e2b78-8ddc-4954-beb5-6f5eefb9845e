package com.pinshang.qingyun.order.dto.tob;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * @Description：提货卡下单数量和份数
 * @Author：ZhangHui
 * @Package：com.pinshang.qingyun.order.dto.tob
 * @Date: 2024/4/15
 */
@Data
@ApiModel(value = "TobOrderTakeQuantityAndNumber", description = "B端提货卡数量和份数数据返回参数")
public class TobOrderTakeQuantityAndNumberODTO {

    @ApiModelProperty("商品ID")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long commodityId;

    @ApiModelProperty("送货日期")
    private String orderTime;

    @ApiModelProperty("实时提货卡下单数量")
    private BigDecimal realTakeQuantity;

    @ApiModelProperty("实时提货卡下单份数")
    private Integer realTakeNumber;
}


package com.pinshang.qingyun.order.dto.shopcart.v2;

import com.pinshang.qingyun.box.utils.SpringUtil;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;


@Data
@ApiModel
@NoArgsConstructor
@AllArgsConstructor
public class ShoppingCartGroupV2ODTO {

    @ApiModelProperty("购物车组内商品列表")
    private List<ShoppingCartCommodityV2ODTO> commodities;

    @ApiModelProperty("是否失效 0、否 1、是")
    private Boolean isInvalidate = Boolean.FALSE;

    @ApiModelProperty("冷冻商品组告警提示")
    private String freezeWarnTips = "";

    public static ShoppingCartGroupV2ODTO initialization(){
        ShoppingCartGroupV2ODTO shoppingCartGroupODTO = new ShoppingCartGroupV2ODTO();
        shoppingCartGroupODTO.setCommodities(Collections.EMPTY_LIST);
        shoppingCartGroupODTO.setFreezeWarnTips("");
        return shoppingCartGroupODTO;
    }

    /**
     * 获取组内商品信息
     * @return
     */
    public List<Long> getCommodityIdList(){
        if(SpringUtil.isNotEmpty(commodities)){
            return commodities.stream().map(ShoppingCartCommodityV2ODTO::getCommodityId).collect(Collectors.toList());
        }
        return null;
    }
}

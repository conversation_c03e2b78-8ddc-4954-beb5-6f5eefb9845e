package com.pinshang.qingyun.order.dto;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * @Author: sk
 * @Date: 2023/4/13
 */
@Data
public class GrouponCommodtyOrderDTO extends MsShopOrderSettingBaseDTO{
    private Long orderId;
    private Long grouponId;
    private String grouponCode;
    private Date grouponStartTime;
    private Date grouponEndTime;
    private String orderTime;
    private Long shopId;
    private Long commodityId;
    private BigDecimal quantity;
    private BigDecimal price;
    private BigDecimal groupPrice;
    private String orderRemark;
    private Integer logisticsModel;
    private Long supplierId;
    private Long warehouseId;
    private String deleveryTimeRange;
    private Long enterpriseId;
    private Long userId;
    private String deliveryBatch;
    private Long storeId;
    private String createName;

    /** 团购类型 1超值预售 2次日团 */
    private Integer groupType;
}

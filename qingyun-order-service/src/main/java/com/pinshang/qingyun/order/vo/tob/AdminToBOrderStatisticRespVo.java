package com.pinshang.qingyun.order.vo.tob;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * @Description：
 * @Author：ZhangHui
 * @Package：com.pinshang.qingyun.order.vo.tob
 * @Date: 2024/4/7
 */
@Data
@ApiModel(value = "AdminToBOrderStatisticRespVo", description = "b端下单统计返回参数")
public class AdminToBOrderStatisticRespVo {


    @ApiModelProperty("送货日期")
    private String orderTime;

    @ApiModelProperty("商品名称")
    private String commodityName;

    @ApiModelProperty("商品编码")
    private String commodityCode;

    @ApiModelProperty("下单数量汇总")
    private BigDecimal orderQuantity;

    @ApiModelProperty("下单份数汇总")
    private Integer orderNumber;

    @ApiModelProperty("创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date createTime;

    @ApiModelProperty("修改时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date updateTime;








}

package com.pinshang.qingyun.order.dto.xda;

import com.pinshang.qingyun.base.enums.OrderSourceTypeEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 待确认订单预览
 * <AUTHOR>
 */
@Data
@ApiModel
public class XdaPreOrderDTO {
    @ApiModelProperty(value = "客户id", hidden = true)
    private Long storeId;
    @ApiModelProperty(value = "appCode", hidden = true)
    private String appCode;
    @ApiModelProperty("订单金额")
    private BigDecimal orderAmount;
    @ApiModelProperty("订货日期")
    private Date orderTime;
    @ApiModelProperty("品种合计")
    private Integer varietySum;


}

package com.pinshang.qingyun.order.model.groupon;

import com.pinshang.qingyun.base.po.BaseSimplePO;

import javax.persistence.Entity;
import javax.persistence.Table;

/**
 * 
 **/
@Entity
@Table(name = "t_groupon_order_log")
public class GrouponOrderLogModel extends BaseSimplePO {
    private Integer grouponType; // 团购类型 1 清美团购   2 云超团购
    private Long grouponId;

    public Long getGrouponId() {
        return grouponId;
    }

    public void setGrouponId(Long grouponId) {
        this.grouponId = grouponId;
    }

    public Integer getGrouponType() {
        return grouponType;
    }

    public void setGrouponType(Integer grouponType) {
        this.grouponType = grouponType;
    }
}
package com.pinshang.qingyun.order.dto.pf;

import io.swagger.annotations.ApiModelProperty;

import java.math.BigDecimal;

import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 送货单项
 */
@Data
@NoArgsConstructor
public class PfOrderItemInfoODTO {
	@ApiModelProperty(position = 0, value = "订单ID", required = true, hidden = true)
	private Long orderId;
	@ApiModelProperty(position = 0, value = "商品ID", required = true, hidden = true)
	private Long commodityId;
	
	@ApiModelProperty(position = 11, value = "商品名称", required = true)
	private String commodityName;
	@ApiModelProperty(position = 11, value = "商品规格", required = true)
	private String commoditySpec;
	@ApiModelProperty(position = 12, value = "单位", required = true)
	private String commodityUnitName;
	@ApiModelProperty(position = 12, value = "单价", required = true)
	private BigDecimal commodityPrice;
	@ApiModelProperty(position = 13, value = "数量", required = true)
	private BigDecimal commodityQuantity;
	@ApiModelProperty(position = 14, value = "金额", required = true)
	private BigDecimal commodityAmount;
	@ApiModelProperty(position = 15, value = "实发数量", required = true)
	private BigDecimal realCommodityQuantity;
	@ApiModelProperty(position = 16, value = "实发金额", required = true)
	private BigDecimal realCommodityAmount;
	@ApiModelProperty(position = 17, value = "包装类型：01-生产当天日期、02-见包装", required = true)
	private String commodityPackageKind;
	@ApiModelProperty(position = 18, value = "备注")
	private String remark;
	
	public String getCommodityName() {
		return commodityName + "(" + commoditySpec + ")";
	}
	
}

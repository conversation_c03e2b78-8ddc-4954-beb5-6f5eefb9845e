package com.pinshang.qingyun.order.enums;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;

/**
 * 发起下单方枚举
 */
public enum OrderLaunchTypeEnum implements BaseEnum{

    CUSTOMER_SERVICE(1,"客服下单"),
    SHOP(2,"门店订货下单");

    private Integer code;
    private String desc;

    OrderLaunchTypeEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    @Override
    public Integer convert() {
        return this.code;
    }
    @JsonValue
    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }
    @JsonCreator
    public static OrderLaunchTypeEnum getEnumByCode(Integer code){
        if(code == null){
            return null;
        }
        for(OrderLaunchTypeEnum e : OrderLaunchTypeEnum.values()){
            if(e.getCode().intValue() == code){
                return e;
            }
        }
        return null;
    }
}

package com.pinshang.qingyun.order.vo.shop;

import com.pinshang.qingyun.base.page.Pagination;
import com.pinshang.qingyun.order.mapper.entry.stall.StallEntry;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;


/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2017/6/14.
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class AuditQueryVo extends StallEntry {

    private String beginDate;

    private String endDate;

    private Integer logisticsModel;

    private Integer status;

    private String supplierId;

    private String subOrderCode;

    private String shopStr;

    private String enterpriseId;

    private List<Integer> orderStatus;
    
    private String supplierStr;

    private List<Long> supplyIdList;
}

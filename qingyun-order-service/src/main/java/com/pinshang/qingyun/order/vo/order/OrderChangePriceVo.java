package com.pinshang.qingyun.order.vo.order;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class OrderChangePriceVo {
    private Long subOrderId;

    /** yyyy-MM-dd */
    private Date orderTime;

    /** 是否可变价 :1是 0否 */
    private Integer changePriceStatus;
}

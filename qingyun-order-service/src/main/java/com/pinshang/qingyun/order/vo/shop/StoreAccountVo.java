package com.pinshang.qingyun.order.vo.shop;

import com.pinshang.qingyun.base.page.Pagination;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.NonNull;

/**
 *
 * <AUTHOR>
 * @Date 2018/4/8 17:14
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class StoreAccountVo extends Pagination {

    private static final long serialVersionUID = -2001027524564547744L;
    /**
     * 模糊查询code
     */
    private String storeCode;

    /**
     * 员工code
     */
    private String employeeCode;
    /**  雇员表ID**/
    private Long employeeId;
}

package com.pinshang.qingyun.order.vo.order;

import com.pinshang.qingyun.base.page.Pagination;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class OrderReplenishmentListVo extends Pagination{

    @ApiModelProperty(position = 1,value = "门店id")
    private String shopId;

    @ApiModelProperty(position = 2,value = "开始时间")
    private String beginDate;

    @ApiModelProperty(position = 3,value = "结束时间")
    private String endDate;

    @ApiModelProperty(position = 4,value = "预订单号")
    private String subOrderCode;

    private String enterpriseId;

    @ApiModelProperty(position = 5,value = "是否补货1:补货")
    private Integer isReplenishment;//是否补货

    @ApiModelProperty(position = 6,value = "创建人编码/名称")
    private String createUser;

    @ApiModelProperty(position = 7,value = "订单号")
    private String orderCode;
}

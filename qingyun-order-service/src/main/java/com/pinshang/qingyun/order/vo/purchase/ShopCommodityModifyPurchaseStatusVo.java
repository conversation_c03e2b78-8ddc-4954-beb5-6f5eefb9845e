package com.pinshang.qingyun.order.vo.purchase;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class ShopCommodityModifyPurchaseStatusVo {
    //门店id
    private List<Long> shopId;

    //商品编码
    private String commodityCodes;

    //原因
    private String reason;

    //是否可采状态
    private Integer purchaseStatus;

    private Long userId;
}

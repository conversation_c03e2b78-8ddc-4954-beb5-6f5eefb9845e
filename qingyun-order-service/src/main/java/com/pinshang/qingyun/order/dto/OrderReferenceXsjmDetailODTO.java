package com.pinshang.qingyun.order.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.List;

/**
 * @Author: sk
 * @Date: 2025/2/26
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class OrderReferenceXsjmDetailODTO {

    private String commodityId;

    @ApiModelProperty("商品编码")
    private String commodityCode;

    @ApiModelProperty("商品名称")
    private String commodityName;

    @ApiModelProperty("条形码")
    private String barCode;

    @ApiModelProperty("单位")
    private String commodityUnit;

    @ApiModelProperty("包装规格")
    private BigDecimal commodityPackageSpec;

    @ApiModelProperty("规格型号")
    private String commoditySpec;

    @ApiModelProperty("收货价")
    private BigDecimal receivePrice;

    @ApiModelProperty("销售箱装量(起订倍数)")
    private BigDecimal salesBoxCapacity;


    @ApiModelProperty("进销损明细")
    private List<ReferenceDetailODTO> referenceDetailList;

    private List list1;

    public static class ReferenceDetailODTO{

        @ApiModelProperty("日期")
        private String orderDate;

        @ApiModelProperty("订货数量")
        private BigDecimal orderQuantity = BigDecimal.ZERO;

        @ApiModelProperty("实收数量")
        private BigDecimal realReceiveQuantity  = BigDecimal.ZERO;

        @ApiModelProperty("12点前销量")
        private BigDecimal saleQuantityBefore12 = BigDecimal.ZERO;

        @ApiModelProperty("19点前销量")
        private BigDecimal saleQuantityBefore19 = BigDecimal.ZERO;

        @ApiModelProperty("19点后销量")
        private BigDecimal saleQuantityAfter19 = BigDecimal.ZERO;

        @ApiModelProperty("损耗数量")
        private BigDecimal breakageQuantity = BigDecimal.ZERO;

        @ApiModelProperty("订货价")
        private BigDecimal orderPrice = BigDecimal.ZERO;

        @ApiModelProperty("零售价")
        private BigDecimal commodityPrice = BigDecimal.ZERO;

        @ApiModelProperty("订货总额")
        private BigDecimal orderAmount = BigDecimal.ZERO;

        @ApiModelProperty("毛利率")
        private String grossProfitMargin;

        @ApiModelProperty("19点前销售额")
        private BigDecimal saleAmountBefore19 = BigDecimal.ZERO;

        @ApiModelProperty("19点后销售额")
        private BigDecimal saleAmountAfter19 = BigDecimal.ZERO;

        @ApiModelProperty("最后一单销售时间")
        private String lastSaleTime;

        public String getOrderDate() {
            return orderDate;
        }

        public void setOrderDate(String orderDate) {
            this.orderDate = orderDate;
        }

        public BigDecimal getOrderQuantity() {
            return orderQuantity;
        }

        public void setOrderQuantity(BigDecimal orderQuantity) {
            this.orderQuantity = orderQuantity;
        }

        public BigDecimal getRealReceiveQuantity() {
            return realReceiveQuantity;
        }

        public void setRealReceiveQuantity(BigDecimal realReceiveQuantity) {
            this.realReceiveQuantity = realReceiveQuantity;
        }

        public BigDecimal getSaleQuantityBefore12() {
            return saleQuantityBefore12;
        }

        public void setSaleQuantityBefore12(BigDecimal saleQuantityBefore12) {
            this.saleQuantityBefore12 = saleQuantityBefore12;
        }

        public BigDecimal getSaleQuantityBefore19() {
            return saleQuantityBefore19;
        }

        public void setSaleQuantityBefore19(BigDecimal saleQuantityBefore19) {
            this.saleQuantityBefore19 = saleQuantityBefore19;
        }

        public BigDecimal getSaleQuantityAfter19() {
            return saleQuantityAfter19;
        }

        public void setSaleQuantityAfter19(BigDecimal saleQuantityAfter19) {
            this.saleQuantityAfter19 = saleQuantityAfter19;
        }

        public BigDecimal getBreakageQuantity() {
            return breakageQuantity;
        }

        public void setBreakageQuantity(BigDecimal breakageQuantity) {
            this.breakageQuantity = breakageQuantity;
        }

        public BigDecimal getOrderPrice() {
            return orderPrice;
        }

        public void setOrderPrice(BigDecimal orderPrice) {
            this.orderPrice = orderPrice;
        }

        public BigDecimal getCommodityPrice() {
            return commodityPrice;
        }

        public void setCommodityPrice(BigDecimal commodityPrice) {
            this.commodityPrice = commodityPrice;
        }

        public BigDecimal getOrderAmount() {
            return orderAmount;
        }

        public void setOrderAmount(BigDecimal orderAmount) {
            this.orderAmount = orderAmount;
        }

        public String getGrossProfitMargin() {
            return grossProfitMargin;
        }

        public void setGrossProfitMargin(String grossProfitMargin) {
            this.grossProfitMargin = grossProfitMargin;
        }

        public BigDecimal getSaleAmountBefore19() {
            return saleAmountBefore19;
        }

        public void setSaleAmountBefore19(BigDecimal saleAmountBefore19) {
            this.saleAmountBefore19 = saleAmountBefore19;
        }

        public BigDecimal getSaleAmountAfter19() {
            return saleAmountAfter19;
        }

        public void setSaleAmountAfter19(BigDecimal saleAmountAfter19) {
            this.saleAmountAfter19 = saleAmountAfter19;
        }

        public String getLastSaleTime() {
            return lastSaleTime;
        }

        public void setLastSaleTime(String lastSaleTime) {
            this.lastSaleTime = lastSaleTime;
        }
    }
}

package com.pinshang.qingyun.order.job;

import com.pinshang.qingyun.base.spring.SpringBeanFinder;
import com.pinshang.qingyun.order.service.XDShoppingCartService;
import lombok.extern.slf4j.Slf4j;
import org.quartz.Job;
import org.quartz.JobDataMap;
import org.quartz.JobExecutionContext;
import org.springframework.stereotype.Component;


@Component
@Slf4j
public class GenerateCreateXdOrderJob implements Job{


    @Override
    public void execute(JobExecutionContext jobExecutionContext){
    	
        JobDataMap jobDataMap = jobExecutionContext.getJobDetail().getJobDataMap();

        String endTime = jobDataMap.getString("endTime");

        XDShoppingCartService xDShoppingCartService = (XDShoppingCartService) SpringBeanFinder.getBean("xdShoppingCartService");

        try {
            xDShoppingCartService.autoCreateXDOrder(endTime);
        } catch (Throwable throwable) {
            log.error("GenerateCreateXdOrderJob",throwable);
        }
        log.info("鲜道自动提交订单时间endTime------------------------------------" +endTime);


    }

}

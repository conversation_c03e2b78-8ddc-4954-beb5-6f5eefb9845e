package com.pinshang.qingyun.order.dto.shopcart.v4;

import com.pinshang.qingyun.base.enums.SalesPromotionStatusEnums;
import com.pinshang.qingyun.box.utils.CollectorsUtil;
import com.pinshang.qingyun.box.utils.SpringUtil;
import com.pinshang.qingyun.marketing.dto.app.CartGroupODTO;
import com.pinshang.qingyun.order.dto.shopcart.v3.CommodityPromotionV3ODTO;
import com.pinshang.qingyun.order.dto.shopcart.v3.ShoppingCartCommodityV3ODTO;
import com.pinshang.qingyun.order.dto.shopcart.v3.ShoppingCartGroupV3ODTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2024/3/4 16:05
 */
@Data
public class ShoppingCartGroupV4ODTO {
    @ApiModelProperty(value = "买赠分组ID")
    private String promotionId;

    @ApiModelProperty("促销类型")
    private Integer code;


    private CommodityPromotionV4ODTO commodityPromotionV4ODTO;

    @ApiModelProperty(" 是否满足促销条件，0=未满足 去凑单，1=满足，再逛逛 第三层促销")
    private Integer fullStatus;

    @ApiModelProperty("商品组")
    private List<ShoppingCartCommodityV4ODTO> commodities;

    @ApiModelProperty("赠品组")
    private List<ShoppingCartCommodityV4ODTO> giftGroup;

    @ApiModelProperty("普通商品失效商品组")
    private List<ShoppingCartCommodityV4ODTO> invalidateGroup;

    @ApiModelProperty("当前分组是否全部失效 0、否 1、是")
    private Boolean isInvalidate = Boolean.FALSE;

    @ApiModelProperty("组告警提示")
    private String freezeWarnTips = "";

    public static ShoppingCartGroupV4ODTO initialization(){
        ShoppingCartGroupV4ODTO shoppingCartGroupODTO = new ShoppingCartGroupV4ODTO();
        shoppingCartGroupODTO.setCommodities(Collections.EMPTY_LIST);
        shoppingCartGroupODTO.setGiftGroup(Collections.EMPTY_LIST);
        shoppingCartGroupODTO.setFreezeWarnTips("");
        return shoppingCartGroupODTO;
    }
    public static ShoppingCartGroupV4ODTO initializationCartPromotionId(CartGroupODTO cartGroupODTO){
        ShoppingCartGroupV4ODTO shoppingCartGroupODTO = initialization();
        shoppingCartGroupODTO.setPromotionId(cartGroupODTO.getPromotionId().toString());
        String name = "";
        List<String> remarkList = new ArrayList<>();
        if(null != cartGroupODTO.getPromotionType()){
            if (SalesPromotionStatusEnums.GIFT.getCode() == cartGroupODTO.getPromotionType().intValue()) {
                name =  "买赠";
                remarkList = CommodityPromotionV4ODTO.GIFT_REMARK_LIST;

            } else if (SalesPromotionStatusEnums.GRADIENT_DISCOUNT.getCode() == cartGroupODTO.getPromotionType().intValue()) {
                name = "梯度满折";
                remarkList = CommodityPromotionV4ODTO.GRADIENT_DISCOUNT_REMARK_LIST;
            }
        }
        CommodityPromotionV4ODTO commodityPromotionV4ODTO = new CommodityPromotionV4ODTO();
        commodityPromotionV4ODTO.setPromotionTypeName(name);
        commodityPromotionV4ODTO.setPromotionType(cartGroupODTO.getPromotionType());
        commodityPromotionV4ODTO.setPromotionName(cartGroupODTO.getPromotionName());
        commodityPromotionV4ODTO.setRuleList(cartGroupODTO.getPromotionRules());
        commodityPromotionV4ODTO.setRemarkList(remarkList);

        String tips = "";
        if(null != cartGroupODTO.getTips())
            tips = cartGroupODTO.getTips();

        if(null != cartGroupODTO.getNextTips())
            tips = tips + ";" + cartGroupODTO.getNextTips();
        commodityPromotionV4ODTO.setTips(cartGroupODTO.getTips());
        commodityPromotionV4ODTO.setNextTips(tips);
        shoppingCartGroupODTO.setCommodityPromotionV4ODTO(commodityPromotionV4ODTO);
        shoppingCartGroupODTO.setCode(cartGroupODTO.getPromotionType());
        shoppingCartGroupODTO.setFullStatus(cartGroupODTO.getFullStatus());
        return shoppingCartGroupODTO;
    }
    /**
     * 获取组内商品信息
     * @return
     */
    public List<Long> getCommodityIdList(){
        if(SpringUtil.isNotEmpty(commodities)){
            return commodities.stream().map(ShoppingCartCommodityV4ODTO::getCommodityId).collect(Collectors.toList());
        }
        return null;
    }
    public List<Long> getGiftGroupCommodityIdList(){
        if(SpringUtil.isNotEmpty(giftGroup)){
            return giftGroup.stream().map(ShoppingCartCommodityV4ODTO::getCommodityId).collect(Collectors.toList());
        }
        return null;
    }
    public Map<Long, BigDecimal> getCommodityIdAndQuantityList(){
        if(SpringUtil.isNotEmpty(commodities)){
            return commodities.stream().collect(Collectors.groupingBy(ShoppingCartCommodityV4ODTO::getCommodityId, CollectorsUtil.summingBigDecimalMax(ShoppingCartCommodityV4ODTO::getQuantity)));
        }
        return new HashMap<>();
    }
    public Map<Long, BigDecimal> getGiftGroupCommodityIdAndQuantityMap(){
        if(SpringUtil.isNotEmpty(giftGroup)){
            return giftGroup.stream().collect(Collectors.groupingBy(ShoppingCartCommodityV4ODTO::getCommodityId, CollectorsUtil.summingBigDecimalMax(ShoppingCartCommodityV4ODTO::getQuantity)));
        }
        return new HashMap<>();
    }
}

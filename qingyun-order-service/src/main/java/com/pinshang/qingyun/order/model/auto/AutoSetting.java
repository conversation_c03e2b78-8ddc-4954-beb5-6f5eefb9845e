package com.pinshang.qingyun.order.model.auto;

import com.pinshang.qingyun.base.po.BaseIDPO;
import lombok.Data;

import javax.persistence.Entity;
import javax.persistence.Table;
import java.util.Date;

/**
 * <AUTHOR>
 */
@Entity
@Data
@Table(name="t_md_auto_setting")
public class AutoSetting extends BaseIDPO  {

    private Long shopId;

    private Integer status;

    /**
     * 总部规划品项数
     */
    private Integer headItems;

    /**
     * 已设品项数
     */
    private Integer setItems;

    /**
     * 上个月销售品项
     */
    private Integer lastMonthSale;

    /**
     * 上个月销售品项详情
     */
    private String lastMonthSaleInfo;

    private Long createId;

    private Date createTime;

    private Long updateId;

    private Date updateTime;
}

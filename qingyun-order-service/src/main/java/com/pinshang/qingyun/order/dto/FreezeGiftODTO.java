package com.pinshang.qingyun.order.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * @Author: sk
 * @Date: 2024/3/8
 * 赠品冻结，临时dto
 */
@Data
public class FreezeGiftODTO {
    @ApiModelProperty("商品ID")
    private Long commodityId;
    @ApiModelProperty("订单数量（根据商品包装规格转换）")
    private BigDecimal orderQuantity;
    @ApiModelProperty("库存依据类型:1=依据大仓, 2=不限量订货,3=限量供应。如果传的和查询的不一样，解冻报错")
    private Integer stockType;
    private Integer type;// 商品类型


    @ApiModelProperty("当前商品数量")
    private BigDecimal quantity;
    @ApiModelProperty("可用库存数量")
    private BigDecimal inventoryQuantity;
}

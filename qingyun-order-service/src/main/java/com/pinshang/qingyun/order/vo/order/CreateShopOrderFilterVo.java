package com.pinshang.qingyun.order.vo.order;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * summary.
 * <p>
 * detailed description
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2019-02-15
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class CreateShopOrderFilterVo extends CreateOrderFilterVo {

    /**
     * 创建订单结果
     */
    private Object object;

    public CreateShopOrderFilterVo() {
        super();
    }


    public CreateShopOrderFilterVo(Boolean createOrderResult, List<FilterTipVo> filterTips, List<BStockShortResponseVO> shortStockList, Object object) {
        super(createOrderResult, filterTips, shortStockList);
        this.object = object;
    }

    /**
     * 创建成功结果
     */

    public static CreateShopOrderFilterVo success() {
        return new CreateShopOrderFilterVo(true, null, null, null);
    }

    /**
     * 创建库存不足的成功结果
     */

    public static CreateShopOrderFilterVo success(List<BStockShortResponseVO> shortStockList) {
        return new CreateShopOrderFilterVo(true, null, shortStockList, null);
    }


    /**
     * 创建订货参考的失败结果
     */

    public static CreateShopOrderFilterVo fail(Object object) {
        return new CreateShopOrderFilterVo(false, null, null, object);
    }
}

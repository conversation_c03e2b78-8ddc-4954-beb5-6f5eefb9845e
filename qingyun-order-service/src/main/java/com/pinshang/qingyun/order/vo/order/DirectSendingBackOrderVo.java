package com.pinshang.qingyun.order.vo.order;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import lombok.ToString;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 补货单VO
 */
@Data
@ToString
@AllArgsConstructor
@RequiredArgsConstructor
public class DirectSendingBackOrderVo {
	private Long storeId;
	private Long shopId;
	private Long enterpriseId;
	private Long userId;
	private Long supplierId;
	private Boolean isInternal;

	List<OrderItemDto> items;
}

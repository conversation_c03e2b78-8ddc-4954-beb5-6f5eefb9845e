package com.pinshang.qingyun.order.model.order;

import java.math.BigDecimal;

import javax.persistence.Entity;
import javax.persistence.Table;

import com.pinshang.qingyun.base.po.BasePO;
/*
 *门店直送预订单明细表
 */
@Entity
@Table(name="t_md_preorder_item")
public class PreOrderItem  extends BasePO{
	private Long preorderId;
	private Long commodityId;
	private BigDecimal requireQuantity;
	private BigDecimal price;
	private BigDecimal totalPrice;
	private BigDecimal realReceiveQuantity;
	private Integer realReceiveNumber;
	private Integer status;
	private String rejectReason;
	private Boolean isAdd;
	//类型: 1-订单商品,3-配货商品,4-配比商品,2-赠品
	private Integer type;

	// 收货数量(代销香烟专用)
	private BigDecimal receiveQuantity;

	// 审核数量(代销香烟专用)
	private BigDecimal checkQuantity;

	public BigDecimal getReceiveQuantity() {
		return receiveQuantity;
	}

	public void setReceiveQuantity(BigDecimal receiveQuantity) {
		this.receiveQuantity = receiveQuantity;
	}

	public BigDecimal getCheckQuantity() {
		return checkQuantity;
	}

	public void setCheckQuantity(BigDecimal checkQuantity) {
		this.checkQuantity = checkQuantity;
	}

	public Integer getRealReceiveNumber() {
		return realReceiveNumber;
	}

	public void setRealReceiveNumber(Integer realReceiveNumber) {
		this.realReceiveNumber = realReceiveNumber;
	}

	public Long getPreorderId() {
		return preorderId;
	}
	public void setPreorderId(Long preorderId) {
		this.preorderId = preorderId;
	}
	public Long getCommodityId() {
		return commodityId;
	}
	public void setCommodityId(Long commodityId) {
		this.commodityId = commodityId;
	}
	public BigDecimal getRequireQuantity() {
		return requireQuantity;
	}
	public void setRequireQuantity(BigDecimal requireQuantity) {
		this.requireQuantity = requireQuantity;
	}
	public BigDecimal getPrice() {
		return price;
	}
	public void setPrice(BigDecimal price) {
		this.price = price;
	}
	public BigDecimal getTotalPrice() {
		return totalPrice;
	}
	public void setTotalPrice(BigDecimal totalPrice) {
		this.totalPrice = totalPrice;
	}
	public BigDecimal getRealReceiveQuantity() {
		return realReceiveQuantity;
	}
	public void setRealReceiveQuantity(BigDecimal realReceiveQuantity) {
		this.realReceiveQuantity = realReceiveQuantity;
	}
	public Integer getStatus() {
		return status;
	}
	public void setStatus(Integer status) {
		this.status = status;
	}
	public String getRejectReason() {
		return rejectReason;
	}
	public void setRejectReason(String rejectReason) {
		this.rejectReason = rejectReason;
	}
	public Boolean getIsAdd() {
		return isAdd;
	}
	public void setIsAdd(Boolean isAdd) {
		this.isAdd = isAdd;
	}
	public Integer getType() {
		return type;
	}
	public void setType(Integer type) {
		this.type = type;
	}
}

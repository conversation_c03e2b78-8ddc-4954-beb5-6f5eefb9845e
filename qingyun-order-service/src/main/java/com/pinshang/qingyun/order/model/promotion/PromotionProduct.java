package com.pinshang.qingyun.order.model.promotion;

import com.pinshang.qingyun.base.po.BaseIDPO;
import lombok.Data;

import javax.persistence.Entity;
import javax.persistence.Table;
import javax.persistence.Transient;
import java.util.Date;

/**
 * 商品特价方案关联的所有商品信息
 */
@Entity
@Table(name = "t_promotion_product")
@Data
public class PromotionProduct extends BaseIDPO {
	/**
     * t_promotion表中的主键ID,即方案ID
     */
    private Long promotionId;
    /**
     * 商品code
     */
    private String productCode;
    /**
     * 特价价格
     */
    private Double price;

    /** 总限量（0表示不限量） */
    private Integer totalLimitNumber;
    /**
     * 客户限购（0表示不限购）
     */
    private Integer limitNumber;

    /**
     * 创建时间
     */
    private Date createTime;
    /**
     * 更新时间
     */
    private Date updateTime;
    /**
     * 状态,0表示正常 1表示不再使用
     */
    private Integer status;
    /**
     * 促销开始时间
     */
    @Transient
    private Date startTime;
    /**
     * 促销结束时间
     */
    @Transient
    private Date endTime;

}

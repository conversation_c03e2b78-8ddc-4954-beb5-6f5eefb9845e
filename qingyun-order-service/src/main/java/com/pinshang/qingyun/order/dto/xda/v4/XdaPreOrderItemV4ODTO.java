package com.pinshang.qingyun.order.dto.xda.v4;

import com.pinshang.qingyun.order.dto.xda.v3.XdaPreOrderItemV3ODTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2024/3/7 16:33
 */
@Data
public class XdaPreOrderItemV4ODTO {
    @ApiModelProperty("普通商品组")
    private List<XdaOrderItemAppV4ODTO> normalGroup;

    @ApiModelProperty("促销商品组")
    private List<XdaPreOrderItemGroupV4ODTO> promotionGroups;

    @ApiModelProperty("特惠商品组")
    private List<XdaOrderItemAppV4ODTO> thGroup;

//    @ApiModelProperty("失效商品组")
//    private List<XdaOrderItemAppV3ODTO> invalidateGroup;

    public static XdaPreOrderItemV4ODTO initXdaPreOrderItemV4ODTO(){
        XdaPreOrderItemV4ODTO xdaPreOrderItemV4ODTO = new XdaPreOrderItemV4ODTO();
        xdaPreOrderItemV4ODTO.setNormalGroup(new ArrayList<>());
        xdaPreOrderItemV4ODTO.setThGroup(new ArrayList<>());
        xdaPreOrderItemV4ODTO.setPromotionGroups(new ArrayList<>());
//        xdaPreOrderItemV2ODTO.setInvalidateGroup(new XdaPreOrderItemGroupV3ODTO());
        return xdaPreOrderItemV4ODTO;
    }
}

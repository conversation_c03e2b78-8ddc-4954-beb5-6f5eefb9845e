package com.pinshang.qingyun.order.model.order;

import com.pinshang.qingyun.base.po.BaseEnterpriseSimplePO;
import lombok.Data;

import javax.persistence.Entity;
import javax.persistence.Table;
/**
 * 客户结账信息表
 **/
@Entity
@Table(name = "t_store_settlement")
@Data
public class StoreSettlement extends BaseEnterpriseSimplePO {
	
	/** 结账客户ID **/
	private Long storeId;
	/** 结账客户ID **/
	private Long settlementCustomerId;
	/** 价格方案ID **/
	private Long productPriceModelId;
	/** 账期类型 **/
	private Long paymentDaysType;
	/** 结账备注 **/
	private String remark;
	/** 结账员ID **/
	private Long settleEmpId;
	/** 是否开票 **/
	private Boolean invoiceStatus;
	/** 发票title **/
	private String invoiceTitle;
	/** 是否存在押金（0：没有押金，1：有押金） **/
	private Boolean depositStatus;
	/** 质量押金 **/
	private Long depositQuality;
	/** 货款押金 **/
	private Long depositGoods;
	/** 冰箱押金 **/
	private Long depositRefrigerator;
	/** 折扣类型（0:按价格下浮，1：按品类下浮，2：按单品下浮） **/
	private Long salesType;
	/** 结账类型(1：固定下浮，2：销量下浮，3：品类下浮，4：单品下浮) **/
	private Long selType;
	/** 结账类型(0：启用 1：不启用) **/
	private Long setStatus;
	/** 是否预收 **/
	private Double collectPrice;

	private Boolean collectStatus;
	/** 创建人 **/
	private Long createId;
	/** 固定下浮 的比率值 **/
	private Double setSale;
	/**
	 * 调货 折扣率
	 */
	private Double settleCalcSale;



}
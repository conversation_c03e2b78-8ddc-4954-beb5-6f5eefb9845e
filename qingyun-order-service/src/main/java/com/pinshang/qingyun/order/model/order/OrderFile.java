package com.pinshang.qingyun.order.model.order;

import java.util.Date;

import javax.persistence.Entity;
import javax.persistence.Table;

import lombok.Data;
import lombok.NoArgsConstructor;

import com.pinshang.qingyun.base.po.BaseSimplePO;

/**
 * 订单文件
 */
@Data
@Entity
@NoArgsConstructor
@Table(name = "t_order_file")
public class OrderFile extends BaseSimplePO {
	// 订单ID
	private Long orderId;
	// 文件Url
	private String fileUrl;
	// 文件大小（字节）
	private Integer fileSize;
	
	public OrderFile (Long orderId) {
		this.orderId = orderId;
	}
	
	public static OrderFile forInsert(Long orderId, String fileUrl, Long fileSize) {
		OrderFile model = new OrderFile();
		model.setOrderId(orderId);
		model.setFileUrl(fileUrl);
		model.setFileSize(null == fileSize? 0: fileSize.intValue());
		model.setCreateId(0L);
		model.setCreateTime(new Date());
		return model;
	}
	
}

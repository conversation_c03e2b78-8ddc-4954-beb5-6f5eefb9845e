package com.pinshang.qingyun.order.model.order;

import com.pinshang.qingyun.base.po.BaseEnterprisePO;

import javax.persistence.Entity;
import javax.persistence.Table;
import java.math.BigDecimal;

@Entity
@Table(name="t_sale_return_order")
public class SaleReturn extends BaseEnterprisePO{
    private Long storeId;

    private String orderCode;

    private Integer status;

    private Integer logisticsModel;

    private Long supplierId;

    private Long warehouseId;

    private BigDecimal returnAmount;

    private Integer totalQuantity;

    private String remark;
    private Long consignmentId; // 代销商id
    /** 档口id */
    private Long stallId;

    public Long getStallId() {
        return stallId;
    }

    public void setStallId(Long stallId) {
        this.stallId = stallId;
    }

    public SaleReturn() {
    }

    public SaleReturn(Long enterpriseId, Long storeId, Long createId, Integer status, Integer logisticsModel) {
        super.setEnterpriseId(enterpriseId);
        this.storeId = storeId;
        super.setCreateId(createId);
        this.status = status;
        this.logisticsModel = logisticsModel;
    }

    public SaleReturn(Long id, Long updateId, Integer status, BigDecimal returnAmount) {
        super.setId(id);
        super.setUpdateId(updateId);
        this.status = status;
        this.returnAmount = returnAmount;
    }

    public Long getStoreId() {
        return storeId;
    }

    public void setStoreId(Long storeId) {
        this.storeId = storeId;
    }

    public String getOrderCode() {
        return orderCode;
    }

    public void setOrderCode(String orderCode) {
        this.orderCode = orderCode;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Integer getLogisticsModel() {
        return logisticsModel;
    }

    public void setLogisticsModel(Integer logisticsModel) {
        this.logisticsModel = logisticsModel;
    }

    public Long getSupplierId() {
        return supplierId;
    }

    public void setSupplierId(Long supplierId) {
        this.supplierId = supplierId;
    }

    public Long getWarehouseId() {
        return warehouseId;
    }

    public void setWarehouseId(Long warehouseId) {
        this.warehouseId = warehouseId;
    }

    public BigDecimal getReturnAmount() {
        return returnAmount;
    }

    public void setReturnAmount(BigDecimal returnAmount) {
        this.returnAmount = returnAmount;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public Integer getTotalQuantity() {
        return totalQuantity;
    }

    public void setTotalQuantity(Integer totalQuantity) {
        this.totalQuantity = totalQuantity;
    }

    public Long getConsignmentId() {
        return consignmentId;
    }

    public void setConsignmentId(Long consignmentId) {
        this.consignmentId = consignmentId;
    }
}
package com.pinshang.qingyun.order.dto.miniGroupon;

import com.pinshang.qingyun.order.model.order.SubOrderItem;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@ApiModel
@NoArgsConstructor
public class MiniGrouponAutoOrderItemODTO {
    private Long miniOrderId;
    private Long orderId;
    private Long subOrderId;
    private Long subOrderItemId;

    public MiniGrouponAutoOrderItemODTO(SubOrderItem item,Long orderId){
        this.miniOrderId = item.getMiniOrderId();
        this.orderId = orderId;
        this.subOrderId = item.getSubOrderId();
        this.subOrderItemId = item.getId();
    }
}

package com.pinshang.qingyun.order.model.recharge;

import com.pinshang.qingyun.base.enums.XSPayBillStatusEnums;
import com.pinshang.qingyun.base.enums.xd.XdPayTypeEnum;
import com.pinshang.qingyun.base.po.BasePO;
import com.pinshang.qingyun.kafka.enums.KafkaMessageOperationTypeEnum;
import lombok.*;

import javax.persistence.Entity;
import javax.persistence.Table;
import java.math.BigDecimal;
import java.util.Date;

@EqualsAndHashCode(callSuper = true)
@Table(name="t_xda_pay_bill")
@Entity
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class XDAPayBill extends BasePO {


    /** 客户ID */
    private Long storeId;

    /** 付款流水编码(充值单号） */
    private String billCode;

    /** 关联的类型，1＝充值 */
    private Integer referType;

    /** 充值类型=t_order_bill.id */
    private Long referId;

    /** 支付金额 */
    private BigDecimal payAmount;
    /**
     * 支付方式：1＝支付宝，2＝微信, 3=小程序, 5=云闪付
     */
    private Integer payType;
    /**
     * 交易流水状态
     * 1＝待支付
     * 2＝支付完成
     * 3＝支付取消（目前定时业务会在查询支付平台”未支付“时，改为”支付取消“，退款也有支付取消）
     * 4＝支付失败（待定，实践中于考虑）,5=支付关闭(定时补偿时当支付平台返回交易关闭时改为此状态 )
     * 6=支付平台支付出错(支付平台内部系统处理出错,服务不可用等,是支付平台针对此交易他们的系统发生了未知的问题)
     * 9=系统交易不存在(有支付流水，没有订单或退货单)
     */
    private Integer billStatus;

    /** 云闪付存储时，保存tn号 **/
    private String tn;

    /** 外部的交易流水号（支付宝、微信或云闪付返回的支付单号） **/
    private String tradeBillCode;

    /** 支付宝待支付单json(字符串) **/
    private String prepayJsonAlipay;

    /** 微信待支付单json(字符串) **/
    private String prepayJsonWechat;

    /** 小程序支付单json **/
    private String prepayJsonMini;

    /** 云闪付支付单json **/
    private String prepayJsonUnion;

    /** 支付时间(记录是支付平台返回的支付时间,主要用于收银报表对账) **/
    private Date payTime;



    public  XDAPayBill getInstance(Long userId, KafkaMessageOperationTypeEnum dataType){
        Date date = new Date();
        if(KafkaMessageOperationTypeEnum.INSERT.equals(dataType)){
            setCreateId(userId);
            setCreateTime(date);
        }
        setUpdateId(userId);
        setUpdateTime(date);
        return this ;
    }

    public XDAPayBill setStoreId(Long storeId) {
        this.storeId = storeId;
        return this;
    }

    public XDAPayBill setBillCode(String billCode) {
        this.billCode = billCode;
        return this;
    }

    public XDAPayBill setReferType(Integer referType) {
        this.referType = referType;
        return this;
    }

    public XDAPayBill setReferId(Long referId) {
        this.referId = referId;
        return this;
    }

    public XDAPayBill setPayAmount(BigDecimal payAmount) {
        this.payAmount = payAmount;
        return this;
    }

    public void setPayType(Integer payType) {
        this.payType = payType;
    }

    public XDAPayBill setPayType(XdPayTypeEnum payType) {
        this.payType = payType.getCode();
        return this;
    }

    public void setBillStatus(Integer billStatus) {
        this.billStatus = billStatus;
    }

    public XDAPayBill setBillStatus(XSPayBillStatusEnums billStatus) {
        this.billStatus = billStatus.getCode();
        return this;
    }

    public XDAPayBill setTn(String tn) {
        this.tn = tn;
        return this;
    }

    public XDAPayBill setTradeBillCode(String tradeBillCode) {
        this.tradeBillCode = tradeBillCode;
        return this;
    }

    public XDAPayBill setPrepayJsonAlipay(String prepayJsonAlipay) {
        this.prepayJsonAlipay = prepayJsonAlipay;
        return this;
    }

    public XDAPayBill setPrepayJsonWechat(String prepayJsonWechat) {
        this.prepayJsonWechat = prepayJsonWechat;
        return this;
    }

    public XDAPayBill setPrepayJsonMini(String prepayJsonMini) {
        this.prepayJsonMini = prepayJsonMini;
        return this;
    }

    public XDAPayBill setPrepayJsonUnion(String prepayJsonUnion) {
        this.prepayJsonUnion = prepayJsonUnion;
        return this;
    }

    public XDAPayBill setPayTime(Date payTime) {
        this.payTime = payTime;
        return this;
    }

}
package com.pinshang.qingyun.order.dto.tob;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * @Description：
 * @Author：ZhangHui
 * @Package：com.pinshang.qingyun.order.dto.tob
 * @Date: 2024/4/10
 */
@Data
@ApiModel(value = "ToBStockRepairIDTO", description = "库存修复入参")
public class ToBStockRepairIDTO {

    @ApiModelProperty("商品id")
    private Long commodityId;

    @ApiModelProperty("默认仓库")
    private Long warehouseId;

    @ApiModelProperty("依据类型：1=依据大仓, 2=不限量订货,3=限量供应")
    private Integer stockType;

    @ApiModelProperty("限量值(stock_type=3 时才有值)")
    private Integer limitNumber;

    @ApiModelProperty("生效方式: 1=限总量，2=每天循环限量")
    private Integer effectType;

    @ApiModelProperty("是否有库存；0：无；1：有")
    private Integer stockStatus;

    @ApiModelProperty("创建时间")
    private Date createTime;

    @ApiModelProperty("更新时间")
    private Date updateTime;

    private Integer tdaStockStatus;

}

package com.pinshang.qingyun.order.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 *  查询待收货/收货中的客户
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class PrintOrderODTO {

    @ApiModelProperty("门店编码")
    private String shopCode;
    @ApiModelProperty("门店名称")
    private String shopName;

    @ApiModelProperty("送货日期")
    private String orderTime;

    @ApiModelProperty("打印人")
    private String printer;
    @ApiModelProperty("打印时间")
    private String printDate;


    @ApiModelProperty("订单号列表")
    private List<String> orderCodeList;

    @ApiModelProperty("商品明细")
    private List<PrintOrderItemODTO> itemList;

}

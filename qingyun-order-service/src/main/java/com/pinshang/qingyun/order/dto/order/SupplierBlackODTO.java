package com.pinshang.qingyun.order.dto.order;

import com.pinshang.qingyun.renderer.annotation.FieldRender;
import com.pinshang.qingyun.renderer.constant.RenderFieldHelper;
import com.pinshang.qingyun.renderer.enums.FieldTypeEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Author: sk
 * @Date: 2023/3/31
 */
@Data
public class SupplierBlackODTO {

    @ApiModelProperty("供应商id")
    private String supplierId;

    @ApiModelProperty("供应商code")
    @FieldRender(fieldType = FieldTypeEnum.SUPPLIER,fieldName = RenderFieldHelper.Supplier.supplierCode,keyName = "supplierId")
    private String supplierCode;

    @ApiModelProperty("供应商名称")
    @FieldRender(fieldType = FieldTypeEnum.SUPPLIER,fieldName = RenderFieldHelper.Supplier.supplierName,keyName = "supplierId")
    private String supplierName;


    private Integer type;

    private Long createId;

    private String createName;

    private String createTime;

}

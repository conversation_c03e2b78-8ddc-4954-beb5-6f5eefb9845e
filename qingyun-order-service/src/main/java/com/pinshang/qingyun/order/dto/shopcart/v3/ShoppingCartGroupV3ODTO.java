package com.pinshang.qingyun.order.dto.shopcart.v3;

import com.pinshang.qingyun.base.enums.SalesPromotionStatusEnums;
import com.pinshang.qingyun.box.utils.SpringUtil;
import com.pinshang.qingyun.marketing.dto.app.CartGroupODTO;
import com.pinshang.qingyun.marketing.dto.app.CommodityDetailODTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

@Data
@ApiModel
@NoArgsConstructor
@AllArgsConstructor
public class ShoppingCartGroupV3ODTO {
    @ApiModelProperty(value = "买赠分组ID")
    private String promotionId;

    @ApiModelProperty("促销类型")
    private Integer code;


    private CommodityPromotionV3ODTO commodityPromotionV3ODTO;

    @ApiModelProperty(" 是否满足促销条件，0=未满足 去凑单，1=满足，再逛逛 第三层促销")
    private Integer fullStatus;

    @ApiModelProperty("商品组")
    private List<ShoppingCartCommodityV3ODTO> commodities;

    @ApiModelProperty("赠品组")
    private List<ShoppingCartCommodityV3ODTO> giftGroup;

    @ApiModelProperty("普通商品失效商品组")
    private List<ShoppingCartCommodityV3ODTO> invalidateGroup;

    @ApiModelProperty("当前分组是否全部失效 0、否 1、是")
    private Boolean isInvalidate = Boolean.FALSE;

    @ApiModelProperty("组告警提示")
    private String freezeWarnTips = "";

    public static ShoppingCartGroupV3ODTO initialization(){
        ShoppingCartGroupV3ODTO shoppingCartGroupODTO = new ShoppingCartGroupV3ODTO();
        shoppingCartGroupODTO.setCommodities(Collections.EMPTY_LIST);
        shoppingCartGroupODTO.setGiftGroup(Collections.EMPTY_LIST);
        shoppingCartGroupODTO.setFreezeWarnTips("");
        return shoppingCartGroupODTO;
    }
    public static ShoppingCartGroupV3ODTO initializationCartPromotionId(CartGroupODTO cartGroupODTO){
        ShoppingCartGroupV3ODTO shoppingCartGroupODTO = initialization();
        shoppingCartGroupODTO.setPromotionId(cartGroupODTO.getPromotionId().toString());
        String name = "";
        List<String> remarkList = new ArrayList<>();
        if(null != cartGroupODTO.getPromotionType()){
            if (SalesPromotionStatusEnums.GIFT.getCode() == cartGroupODTO.getPromotionType().intValue()) {
                name =  "买赠";
                remarkList = CommodityPromotionV3ODTO.GIFT_REMARK_LIST;

            } else if (SalesPromotionStatusEnums.GRADIENT_DISCOUNT.getCode() == cartGroupODTO.getPromotionType().intValue()) {
                name = "梯度满折";
                remarkList = CommodityPromotionV3ODTO.GRADIENT_DISCOUNT_REMARK_LIST;
            }
        }
        CommodityPromotionV3ODTO commodityPromotionV3ODTO = new CommodityPromotionV3ODTO();
        commodityPromotionV3ODTO.setPromotionTypeName(name);
        commodityPromotionV3ODTO.setPromotionType(cartGroupODTO.getPromotionType());
        commodityPromotionV3ODTO.setPromotionName(cartGroupODTO.getPromotionName());
        commodityPromotionV3ODTO.setRuleList(cartGroupODTO.getPromotionRules());
        commodityPromotionV3ODTO.setRemarkList(remarkList);

        String tips = "";
        if(null != cartGroupODTO.getTips())
            tips = cartGroupODTO.getTips();

        if(null != cartGroupODTO.getNextTips())
            tips = tips + ";" + cartGroupODTO.getNextTips();
        commodityPromotionV3ODTO.setTips(cartGroupODTO.getTips());
        commodityPromotionV3ODTO.setNextTips(tips);
        shoppingCartGroupODTO.setCommodityPromotionV3ODTO(commodityPromotionV3ODTO);
        shoppingCartGroupODTO.setCode(cartGroupODTO.getPromotionType());
        shoppingCartGroupODTO.setFullStatus(cartGroupODTO.getFullStatus());
        return shoppingCartGroupODTO;
    }
    /**
     * 获取组内商品信息
     * @return
     */
    public List<Long> getCommodityIdList(){
        if(SpringUtil.isNotEmpty(commodities)){
            return commodities.stream().map(ShoppingCartCommodityV3ODTO::getCommodityId).collect(Collectors.toList());
        }
        return null;
    }
    public List<Long> getGiftGroupCommodityIdList(){
        if(SpringUtil.isNotEmpty(giftGroup)){
            return giftGroup.stream().map(ShoppingCartCommodityV3ODTO::getCommodityId).collect(Collectors.toList());
        }
        return null;
    }
}

package com.pinshang.qingyun.order.vo.order;

import com.pinshang.qingyun.base.page.Pagination;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class SaleReturnOrderReqVo extends Pagination {
    /**退货日期 开始日期*/
    private Date returnOrderStartDate;
    /**退货日期 结束日期*/
    private Date returnOrderEndDate;
    /**退货单号*/
    private String returnOrderCode;
    /**退货单状态 0＝取消，1＝待入库(待确认)，2＝已入库（已确认）,3＝入库中*/
    private Integer status;
    /**客户 名称/编码/助记码 模糊搜索条件*/
    private String storeFuzzy;
    /**仓库ID*/
    private Long warehouseId;

    // 退货原因
    private Boolean ifReturnShort = false;
    private Integer returnReason;
}

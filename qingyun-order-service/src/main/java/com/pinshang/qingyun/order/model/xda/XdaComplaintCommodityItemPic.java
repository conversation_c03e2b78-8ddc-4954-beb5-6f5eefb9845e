package com.pinshang.qingyun.order.model.xda;

import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;

@Entity
@Table(name="t_complaint_commodity_list_pic")
@Data
@NoArgsConstructor
public class XdaComplaintCommodityItemPic {
  @Id
  @Column(name = "uid")
  private String id;
  private String complaintCommodityListUid;
  private String imgPicUrl;

  public XdaComplaintCommodityItemPic(String id, String complaintCommodityListUid, String imgPicUrl) {
    this.id = id;
    this.complaintCommodityListUid = complaintCommodityListUid;
    this.imgPicUrl = imgPicUrl;
  }
}

package com.pinshang.qingyun.order.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * @Author: sk
 * @Date: 2022/9/2
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class CloudOrderAllocationSaveIDTO extends MsShopOrderSettingBaseDTO{

    private Long shopId;
    private Long storeId;

    private String orderTime;

    /** 商品id */
    private Long commodityId;
    private BigDecimal quantity;
    private BigDecimal price;

    private Integer logisticsModel;
    private Long supplierId;
    private Long warehouseId;
    private String deleveryTimeRange;
    private Long enterpriseId;
    private Long userId;
    private String deliveryBatch;
    private String createName;

}

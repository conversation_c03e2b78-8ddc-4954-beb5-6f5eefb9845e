package com.pinshang.qingyun.order.model.bigShop;

import com.pinshang.qingyun.base.po.BaseSimplePO;
import lombok.Data;
import lombok.ToString;

import javax.persistence.Entity;
import javax.persistence.Table;
import java.math.BigDecimal;

/**
 * <p>
 * 大店收货登记表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-14
 */
@Data
@ToString
@Entity
@Table(name = "t_dd_receive_doc_record")
public class DdReceiveDocRecord extends BaseSimplePO {

    /**
     *单据号
     */
    private Long docId;

    /**
     *商品id
     */
    private Long commodityId;

    /**
     *库区 1排面区 2拣货区 3存储区
     */
    private Integer storageArea;

    /**
     *货位号id
     */
    private Long goodsAllocationId;

    /**
     *实际收货数量
     */
    private BigDecimal realReceiveQuantity;

    /**
     *备注
     */
    private String remark;


}

package com.pinshang.qingyun.order.dto.bigShop;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.List;

/**
 * 收货登记单个商品DTO
 * @Author: shenyang
 * @Date: 2025-08-14
 */
@Data
public class DdReceiveDocRecordIDTO {

    @ApiModelProperty(value = "门店ID", required = true)
    private Long shopId;

    @ApiModelProperty(value = "档口ID", required = true)
    private Long stallId;

    @ApiModelProperty(value = "单据ID", required = true)
    private Long docId;

    @ApiModelProperty(value = "商品ID", required = true)
    private Long commodityId;

    @ApiModelProperty(value = "实收数量", required = true)
    private BigDecimal realReceiveQuantity;

    @ApiModelProperty(value = "库区ID 1-排面区 2-拣货区 3-存储区", required = true)
    private Integer storageArea;

    @ApiModelProperty(value = "货位ID")
    private Long goodsAllocationId;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "图片信息列表")
    private List<DdReceiveDocPicIDTO> picList;

    /**
     * 图片信息DTO
     */
    @Data
    public static class DdReceiveDocPicIDTO {
        
        @ApiModelProperty(value = "图片链接", required = true)
        @NotNull(message = "图片链接不能为空")
        private String picUrl;

        @ApiModelProperty(value = "图片类型 1-普通图片 2-长图 3-视频", required = true)
        @NotNull(message = "图片类型不能为空")
        private Integer picType;
    }
}

package com.pinshang.qingyun.order.dto.tms;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;

/**
 * 查询【源单-用于运单】
 */
@Data
@NoArgsConstructor
public class SelectRefOrderForWaybillIDTO {
	@ApiModelProperty(position = 10, required = true, value = "源单类型：11-订单、21-退货单")
	private Integer refType;
	@JsonSerialize(using = ToStringSerializer.class)
	@ApiModelProperty(position = 11, required = true, value = "源单ID")
	private Long refOrderId;
}

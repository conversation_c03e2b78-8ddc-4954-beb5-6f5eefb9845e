package com.pinshang.qingyun.order.vo.orderStatistics;

import com.pinshang.qingyun.base.page.Pagination;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @ClassName OrderCompanyCommodityStatisticsVo
 * @Description TODO
 * <AUTHOR>
 * @Date 2021/3/26 14:57
 **/
@Data
@NoArgsConstructor
@AllArgsConstructor
public class OrderCompanyCommodityStatisticsVo extends Pagination {
    /** 公司ID */
    private Long companyId;
    private String startOrderTime;
    private String endOrderTime;
    private Long commodityId;
    private Long factoryId;
    private Integer showType;
}

package com.pinshang.qingyun.order.model.common;

import javax.persistence.Entity;
import javax.persistence.Table;

import lombok.Data;
import lombok.NoArgsConstructor;

import com.pinshang.qingyun.base.po.BaseIDPO;

/**
 * 公司		—— 从SMM库同步过来的
 */
@Data
@Entity
@NoArgsConstructor
@Table(name = "t_company")
public class Company extends BaseIDPO {
	// 公司编码
	private String companyCode;
	// 公司名称
	private String companyName;
}

package com.pinshang.qingyun.order.model.order;

import com.pinshang.qingyun.base.po.BaseIDPO;
import com.pinshang.qingyun.base.po.BasePO;
import lombok.Data;

import javax.persistence.Table;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2023/05/25/0025 14:00
 */
@Data
@Table(name="t_take_card_order_bill")
public class TakeCardOrderBill extends BasePO {

    //提货卡号
    private String cardNo;
    //卡序列号
    private String cardSn;
    //客户id
    private Long storeId;
    //门店code
    private String shopCode;
    private Integer status;
    private String remark;
    //门店名称
    private String shopName;
    //预约提货时间：yyyy-MM-dd
    private String appointDate;
    //预约商品ID
    private Long commodityId;
    //产品代码
    private String commodityCode;
    //产品名称
    private String commodityName;
    //实发数量
    private BigDecimal realQuantity;
    //单价
    private BigDecimal commodityPrice;
    //实发总金额
    private BigDecimal realTotalPrice;


}

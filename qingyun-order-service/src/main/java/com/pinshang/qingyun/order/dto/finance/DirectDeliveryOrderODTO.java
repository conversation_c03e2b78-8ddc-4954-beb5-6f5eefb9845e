package com.pinshang.qingyun.order.dto.finance;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2025/7/2 10:30
 */
@Data
@NoArgsConstructor
public class DirectDeliveryOrderODTO {

    @ApiModelProperty(value ="1-出库、2-入库")
    private Integer inoutType;

    @ApiModelProperty(value = "业务类型:1-直送订单 2-直送订单退货")
    private Integer businessType;

    @ApiModelProperty(value = "业务单号")
    private String businessCode;

    @ApiModelProperty(value = "业务日期：yyyy-MM-dd")
    private String businessDate;

    @ApiModelProperty(value = "业务时间：yyyy-MM-dd HH:mm:ss")
    private Date businessTime;

    @ApiModelProperty(value = "业务明细ID")
    private Long businessOrderItemId;

    @ApiModelProperty("商品id")
    private Long commodityId;

    @ApiModelProperty("数量")
    private BigDecimal quantity;

    @ApiModelProperty("金额")
    private BigDecimal amount;

    @ApiModelProperty("客户")
    private Long storeId;

    @ApiModelProperty("档口")
    private Long stallId;

    private BigDecimal price;


    public BigDecimal getAmount() {
        if(price != null && quantity != null){
            return price.multiply(new BigDecimal(quantity.toString()));
        }
        return amount;
    }

    public Integer getInoutType() {
        if(businessType != null){
            if(businessType == 1){
                return 1;
            } else if (businessType == 2) {
                return 2;
            }
        }
        return inoutType;
    }
}

package com.pinshang.qingyun.order.vo.order;

import com.pinshang.qingyun.base.enums.StoreTypeEnums;
import com.pinshang.qingyun.base.page.Pagination;
import com.pinshang.qingyun.order.constant.DictionaryCodeConstant;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;
import java.util.List;
import java.util.Map;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class SubOrderVo extends Pagination {
	/**
	 *
	 */
	private static final long serialVersionUID = -5920644605352450524L;
	private Long id;
	//订单编号
	private String orderCode;
	//仓库
	private Long warehouseId;

	private Long enterpriseId;

	private List<Long> orderIds;
	// 客户类型
	private Long storeTypeId;
	private Long userId;
	// 批次时间段start (已无用)
	private String startTime;
	// 批次时间段end　(已无用)
	private String endTime;
	/** 批次查询字段  **/
	private Integer deliveryBatch;
	//送货日期
	private Date orderTime;

	private Long lineGroupId;
    @ApiModelProperty("发货时间")
    private String deliveryTime;

    //鲜达客户类型
    private Long xdaStoreTypeId;

	@ApiModelProperty("业务类型：0=B端销售，1=调拨，4=线上销售，10=通达销售")
	private Integer businessType;

	@ApiModelProperty("区域（物流中心id，通达时有效）")
	private Long logisticsCenterId;

    private List<Long> directStoreTypeList;
    private List<Long> tobStoreTypeList;
    private List<Long> tobWarehouseList;

    public void init(Map<String,List<Long>> dictionaryMap){
        this.setDirectStoreTypeList(dictionaryMap.get(DictionaryCodeConstant.STORETYPE_DIRECT_LIST));
        this.setTobStoreTypeList(dictionaryMap.get(DictionaryCodeConstant.STORETYPE_ToB_LIST));
        this.setTobWarehouseList(dictionaryMap.get(DictionaryCodeConstant.WAREHOUSE_ToB_LIST));
    }

    public Long getXdaStoreTypeId() {
        return StoreTypeEnums.XDA.getId();
    }
}

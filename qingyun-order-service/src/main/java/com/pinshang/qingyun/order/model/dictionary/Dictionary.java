package com.pinshang.qingyun.order.model.dictionary;

import com.pinshang.qingyun.base.po.BaseIDPO;

import javax.persistence.Entity;
import javax.persistence.Table;
import javax.persistence.Transient;

/**
 * 
 **/
@Entity
@Table(name = "t_dictionary")
public class Dictionary extends BaseIDPO {
    public static final long ROOT_DICTIONARY_ID = 0L;
    public static final long ROOT_PARENT_DICTIONARY_ID = -1L;
    private Long dictionaryId;
    private String optionName;
    private String optionCode;
    private String optionValue;
    private String memo;
    private Integer optionState;
    private String optionFullName;
    private String optionDefaultCode;
    private Integer optionLevel;
    private Integer optionClass;

    @Transient
    private String idStr;
    
    @Transient
    private String  optionStateName;
    @Transient
    private String  ids;
    /**是否是叶子节点**/
    @Transient
    private Integer isLeaf;
    
    
    public String getIdStr() {
		return getId()==null?null:getId().toString();
	}
	public void setIdStr(String idStr) {
		this.idStr = idStr;
	}
	public Integer getIsLeaf() {
        if(this.getOptionLevel()==4) {
            return 1;
        } else {
            return 0;
        }
    }
    public Long getDictionaryId() {
        return dictionaryId;
    }

    public void setDictionaryId(Long dictionaryId) {
        this.dictionaryId = dictionaryId;
    }
    public String getOptionName(){
        return optionName;
    }
    public void setOptionName(String optionName){
        this.optionName = optionName;
    }
    public String getOptionCode(){
        return optionCode;
    }
    public void setOptionCode(String optionCode){
        this.optionCode = optionCode;
    }
    public String getOptionValue(){
        return optionValue;
    }
    public void setOptionValue(String optionValue){
        this.optionValue = optionValue;
    }
    public String getMemo(){
        return memo;
    }
    public void setMemo(String memo){
        this.memo = memo;
    }
    public Integer getOptionState(){
        return optionState;
    }
    public void setOptionState(Integer optionState){
        this.optionState = optionState;
    }
    public String getOptionFullName(){
        return optionFullName;
    }
    public void setOptionFullName(String optionFullName){
        this.optionFullName = optionFullName;
    }
    public String getOptionDefaultCode(){
        return optionDefaultCode;
    }
    public void setOptionDefaultCode(String optionDefaultCode){
        this.optionDefaultCode = optionDefaultCode;
    }
    public Integer getOptionLevel(){
        return optionLevel;
    }
    public void setOptionLevel(Integer optionLevel){
        this.optionLevel = optionLevel;
    }
    public Integer getOptionClass(){
        return optionClass;
    }
    public void setOptionClass(Integer optionClass){
        this.optionClass = optionClass;
    }

    public String getIds() {
        return ids;
    }

    public void setIds(String ids) {
        this.ids = ids;
    }

    public void setOptionStateName(String optionStateName) {
        this.optionStateName = optionStateName;
    }
}
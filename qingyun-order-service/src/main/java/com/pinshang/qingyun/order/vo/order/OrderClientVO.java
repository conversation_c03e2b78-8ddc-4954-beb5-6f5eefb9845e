package com.pinshang.qingyun.order.vo.order;

import com.pinshang.qingyun.order.model.order.SubOrder;
import com.pinshang.qingyun.order.model.order.SubOrderItem;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * @auther dy
 * @date 2023/12/19  14:41
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
public class OrderClientVO {
    private Long id;
    /**
     * 订单号
     **/
    private String orderCode;
    /**
     * 商铺id
     **/
    private Long storeId;
    /**
     * 订单时间
     **/
    private Date orderTime;
    /**
     * 订单类型：（0：普通订单，1：补货单）
     **/
    private Integer modeType;
    /**
     * 订单类型
     **/
    private Integer orderType;
    /**
     * 打印份数
     **/
    private Integer printNum;
    /**
     * 打印类型(1：本地,2：送货员,3：不打印)
     **/
    private Integer printType;
    /**
     * 应付金额（参与促销活动之前的源始金额）
     **/
    private BigDecimal totalAmount;
    /**
     * 订单金额
     **/
    private BigDecimal orderAmount;
    /**
     * 订单最终金额
     **/
    private BigDecimal finalAmount;
    /**
     * 订单运费金额（免运费为0）
     **/
    private BigDecimal freightAmount;
    /**
     * 备注
     **/
    private String orderRemark;
    /**
     * 更新者
     **/
    private Long updateId;
    /**
     * 订单状态(0正常,1删除)
     **/
    private Integer orderStatus;
    /**
     * 创建者
     **/
    private Long createId;
    /**
     * 创建者姓名
     **/
    private Integer deliveryBatch;
    private String deliveryBatchRemark;
    /**
     * 订单是否允许修改价格
     */
    private Integer changePriceStatus;
    private Integer syncStatus;
    private Integer settleStatus;
    /**
     * 流程状态(鲜达新加)  7=待发货    11=出库中　15=配送中　　19=配送完成
     */
    private Integer processStatus;
    /**
     * 订单截止时间(鲜达新加),根据当前客户截止时间与订单中的最晚商品截止时间得来;订单取消与是否进大仓时使用
     */
    private Date orderDurationTime;

    /**
     * 实发总金额
     **/
    private BigDecimal realTotalPrice;
    /**
     * 实发日期
     **/
    private Date realOrderTime;
    /**
     * 所属公司ID
     **/
    private Long companyId;
    private Long consignmentId; // 代销商户id
    /**
     * 订单所有子单是否出库完成（不包括直送类型） 等于1-完成
     **/
    private Integer realSubOrderDoneStatus;
    private Long enterpriseId;
    private Date createTime;
    private Date updateTime;
    private Integer presaleStatus;
    private List<SubOrder> subOrders;
    private List<SubOrderItem> subOrderItems;
    private Long cacelReasonId;
    private Long logisticsCenterId;
    private String logisticsCenter;
    private Integer businessType;
    private String deliveryTimeRange;
    private Long stallId;
}

package com.pinshang.qingyun.order.dto.sync;

import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @Date 2024/7/26 13:57
 */
@Data
public class SyncOrderListODTO {
    private Long orderId;

    private Long commodityId;

    /**商品数量*/
    private BigDecimal commodityNum;

    /**实发数量*/
    private BigDecimal realQuantity;

    private BigDecimal totalPrice;

    private BigDecimal commodityPrice;

    /**1-订单商品,2-赠品,3-配货商品,5-特惠商品*/
    private Integer type;

    /**实发总金额*/
    private BigDecimal realTotalPrice;

    /**原价*/
    private BigDecimal originalPrice;

    /**原金额(原价*数量)*/
    private BigDecimal originalTotalPrice;

    /**商品类型-1-非组合 2-组合  3-组合子品*/
    private Integer combType;

    /**属于组合商品id*/
    private Long combCommodityId;

    /**鲜达B端特价id*/
    private Long pricePromotionId;
}

package com.pinshang.qingyun.order.enums;

import java.util.EnumSet;

import com.pinshang.qingyun.box.utils.EnumUtils;

public enum GiftModelConditionTypeEnum {

	//条件类型:1.赠送一次,2.累计赠送
	GIVE_ONE(1, "赠送一次"),
	CUMULATIVE_GIVE(2,"累计赠送");

	private Integer code;
	private String remark;
	
	private GiftModelConditionTypeEnum(Integer code, String remark) {
		this.setCode(code);
		this.setRemark(remark);
	}

	public static GiftModelConditionTypeEnum fromName(Integer code) {
		return EnumUtils.fromEnumProperty(GiftModelConditionTypeEnum.class, "code", code);
	}

	public static EnumSet<GiftModelConditionTypeEnum> allList() {
		EnumSet<GiftModelConditionTypeEnum> adverPositionList = EnumSet.allOf(GiftModelConditionTypeEnum.class);
		return adverPositionList;
	}

	public Integer getCode() {
		return code;
	}

	public void setCode(Integer code) {
		this.code = code;
	}

	public String getRemark() {
		return remark;
	}

	public void setRemark(String remark) {
		this.remark = remark;
	}
	
	
}

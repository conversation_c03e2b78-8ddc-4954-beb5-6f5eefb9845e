package com.pinshang.qingyun.order.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 自动日结参数
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class CompensateSettleDailyIDTO {
    @ApiModelProperty(value = "日结日期,yyyy-MM-dd,不传默认当天")
    private String dailyDate;

    @ApiModelProperty(value = "门店id")
    private Long shopId;
}

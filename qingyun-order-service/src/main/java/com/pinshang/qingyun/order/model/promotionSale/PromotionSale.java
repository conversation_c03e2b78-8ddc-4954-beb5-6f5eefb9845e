package com.pinshang.qingyun.order.model.promotionSale;

import java.util.Date;

import javax.persistence.Entity;
import javax.persistence.Table;
import javax.persistence.Transient;

import com.pinshang.qingyun.base.po.BaseEnterprisePO;
/**
 * 配比方案表
 **/
@Entity
@Table(name = "t_promotion_sale")
public class PromotionSale extends BaseEnterprisePO {
	/**方案名称 **/
    private String name;
	/**开始时间 **/
    private Date startTime;
	/**结束时间 **/
    private Date endTime;
	/**描述 **/
    private String remark;
	/** 配比优惠类型（0：订单，1：商品） **/
    private Integer saleType;
	/**订单满多少钱使用 **/
    private Double orderReach;
	/**订单比率（小于0为商品配比条件，大于等于0为订单配比条件） **/
    private Long orderRate;
	/**0：开启 1：关闭 **/
    private Integer status;
	/**创建者 **/
    private Long createUserId;
    /** 创建用户名 **/
    @Transient
    private String createUserName;
    
    public String getName(){
        return name;
    }
    public void setName(String name){
        this.name = name;
    }
    public Date getStartTime(){
        return startTime;
    }
    public void setStartTime(Date startTime){
        this.startTime = startTime;
    }
    public Date getEndTime(){
        return endTime;
    }
    public void setEndTime(Date endTime){
        this.endTime = endTime;
    }
    public String getRemark(){
        return remark;
    }
    public void setRemark(String remark){
        this.remark = remark;
    }
    public Double getOrderReach(){
        return orderReach;
    }
    public void setOrderReach(Double orderReach){
        this.orderReach = orderReach;
    }
    public Long getOrderRate(){
        return orderRate;
    }
    public void setOrderRate(Long orderRate){
        this.orderRate = orderRate;
    }
    public Integer getStatus(){
        return status;
    }
    public void setStatus(Integer status){
        this.status = status;
    }
    public Long getCreateUserId(){
        return createUserId;
    }
    public void setCreateUserId(Long createUserId){
        this.createUserId = createUserId;
    }
	public String getCreateUserName() {
		return createUserName;
	}
	public void setCreateUserName(String createUserName) {
		this.createUserName = createUserName;
	}
	
	public Integer getSaleType() {
		return saleType;
	}
	public void setSaleType(Integer saleType) {
		this.saleType = saleType;
	}
    
}
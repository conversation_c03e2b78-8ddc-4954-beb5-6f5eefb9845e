package com.pinshang.qingyun.order.model.order;

import com.pinshang.qingyun.base.po.BasePO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Table;
import javax.persistence.Transient;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * @Author: sk
 * @Date: 2024/5/9
 */
@Data
@Table(name = "t_xda_pre_order")
public class XdaPreOrder extends BasePO {

    /**订单号 **/
    private String orderCode;

    /**商铺id **/
    private Long storeId;

    /**订单时间 **/
    private Date orderTime;

    /** 支付状态  0 待支付   1 已经支付  2 已经取消**/
    private Integer payStatus;

    /**订单类型 **/
    private Integer orderType;

    /**订单金额 **/
    private BigDecimal orderAmount;
    /** 优惠金额合计 */
    private BigDecimal promotionAmount;

    /**备注 **/
    private String orderRemark;

    /** 订单截止时间(鲜达新加),根据当前客户截止时间与订单中的最晚商品截止时间得来;订单取消与是否进大仓时使用 */
    private Date orderDurationTime;

    /**
     * 所属公司ID
     **/
    private Long companyId;

    /**订单运费金额（免运费为0）**/
    private BigDecimal freightAmount;


    private Integer deliveryBatch; // 配送批次
    private String deliveryTimeRange; // 送货时间段
    private Long logisticsCenterId;
    private String logisticsCenter; // 物流中心
    private Integer businessType; // 业务类型：10-通达销售

    @ApiModelProperty("支付单号")
    private String billCode;

    @Transient
    private String  errorMsg;
    @Transient
    private List<XdaPreOrderItem> orderList;

    /**
     * 物流承运商code
     */
    private String logisticsCarrierCode;

}

package com.pinshang.qingyun.order.vo.order;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/*
 * subOrder及item新增vo
 */
public class SubOrderAndItemVo {
	//订单id
	private Long orderId;
	//订单code
	private String orderCode;
	//物流模式
	private Integer logisticsModel;
	//商品品类数
	private BigDecimal varietyTotal;
	//供应商id
	private Long supplierId;
	//仓库id
	private Long warehouseId;
	//createId
	private Long createId;
	//enterpriseId
	private Long enterpriseId;
	//total_price
	private BigDecimal totalPrice;
	//orderTime
	private Date orderTime;
	//采购单id(直通自动生成采购单)
	private Long purchaseOrderId;

	// 店铺id
	private Long storeId;

	/**
	 * 是否预售:0=否，1=是
	 */
	private Integer presaleStatus;

	/**
	 * 库存依据  1=依据大仓, 2=不限量订货, 3=限量供应
	 */
	private Integer stockType;

	public Integer getPresaleStatus() {
		return presaleStatus;
	}

	public void setPresaleStatus(Integer presaleStatus) {
		this.presaleStatus = presaleStatus;
	}

	public Integer getStockType() {
		return stockType;
	}

	public void setStockType(Integer stockType) {
		this.stockType = stockType;
	}

	public Long getStoreId() {
		return storeId;
	}

	public void setStoreId(Long storeId) {
		this.storeId = storeId;
	}

	private List<SubOrderItemVo> items;
	public Long getOrderId() {
		return orderId;
	}
	public void setOrderId(Long orderId) {
		this.orderId = orderId;
	}
	public Integer getLogisticsModel() {
		return logisticsModel;
	}
	public void setLogisticsModel(Integer logisticsModel) {
		this.logisticsModel = logisticsModel;
	}
	public BigDecimal getVarietyTotal() {
		return varietyTotal;
	}
	public void setVarietyTotal(BigDecimal varietyTotal) {
		this.varietyTotal = varietyTotal;
	}
	public Long getSupplierId() {
		return supplierId;
	}
	public void setSupplierId(Long supplierId) {
		this.supplierId = supplierId;
	}
	public Long getWarehouseId() {
		return warehouseId;
	}
	public void setWarehouseId(Long warehouseId) {
		this.warehouseId = warehouseId;
	}
	public Long getCreateId() {
		return createId;
	}
	public void setCreateId(Long createId) {
		this.createId = createId;
	}
	public Long getEnterpriseId() {
		return enterpriseId;
	}
	public void setEnterpriseId(Long enterpriseId) {
		this.enterpriseId = enterpriseId;
	}
	public BigDecimal getTotalPrice() {
		return totalPrice;
	}
	public void setTotalPrice(BigDecimal totalPrice) {
		this.totalPrice = totalPrice;
	}
	public Date getOrderTime() {
		return orderTime;
	}
	public void setOrderTime(Date orderTime) {
		this.orderTime = orderTime;
	}
	public Long getPurchaseOrderId() {
		return purchaseOrderId;
	}
	public void setPurchaseOrderId(Long purchaseOrderId) {
		this.purchaseOrderId = purchaseOrderId;
	}
	
	public static class SubOrderItemVo{
		private Long subOrderId;
		private Long commodityId;
		private BigDecimal quantity;
		private BigDecimal price;
		private BigDecimal totalPrice;
		private BigDecimal realReceiveQuantity;
		private BigDecimal realDeliveryQuantity;
		private String rejectReason;
		//是否可变价——特价不可变价，其余从 订货设置 取
		private Integer changePriceStatus;
		/**
		 * 原材料比例
		 */
		private Integer sourceRatio;
		/**
		 * 转成品比例
		 */
		private Integer targetRatio;

		/**
		 * 组合商品转换的最小单位商品ID
		 */
		private Long targetCommodityId;

		/**
		 * 组合商品转换状态：0=无转换，1=有转换
		 */
		private Integer convertStatus;

		/**
		 * 组合商品目标数量 : 向上取整 ( quantity / source_ratio * target_ratio)
		 */
		private BigDecimal targetQuantity;
		private Integer type;

		public Integer getType() {
			return type;
		}

		public void setType(Integer type) {
			this.type = type;
		}

		public Integer getSourceRatio() {
			return sourceRatio;
		}

		public void setSourceRatio(Integer sourceRatio) {
			this.sourceRatio = sourceRatio;
		}

		public Integer getTargetRatio() {
			return targetRatio;
		}

		public void setTargetRatio(Integer targetRatio) {
			this.targetRatio = targetRatio;
		}

		public Long getTargetCommodityId() {
			return targetCommodityId;
		}

		public void setTargetCommodityId(Long targetCommodityId) {
			this.targetCommodityId = targetCommodityId;
		}

		public Integer getConvertStatus() {
			return convertStatus;
		}

		public void setConvertStatus(Integer convertStatus) {
			this.convertStatus = convertStatus;
		}

		public BigDecimal getTargetQuantity() {
			return targetQuantity;
		}

		public void setTargetQuantity(BigDecimal targetQuantity) {
			this.targetQuantity = targetQuantity;
		}

		public Long getSubOrderId() {
			return subOrderId;
		}
		public void setSubOrderId(Long subOrderId) {
			this.subOrderId = subOrderId;
		}
		public Long getCommodityId() {
			return commodityId;
		}
		public void setCommodityId(Long commodityId) {
			this.commodityId = commodityId;
		}
		public BigDecimal getQuantity() {
			return quantity;
		}
		public void setQuantity(BigDecimal quantity) {
			this.quantity = quantity;
		}
		public BigDecimal getPrice() {
			return price;
		}
		public void setPrice(BigDecimal price) {
			this.price = price;
		}
		public BigDecimal getTotalPrice() {
			return totalPrice;
		}
		public void setTotalPrice(BigDecimal totalPrice) {
			this.totalPrice = totalPrice;
		}
		public BigDecimal getRealReceiveQuantity() {
			return realReceiveQuantity;
		}
		public void setRealReceiveQuantity(BigDecimal realReceiveQuantity) {
			this.realReceiveQuantity = realReceiveQuantity;
		}
		public BigDecimal getRealDeliveryQuantity() {
			return realDeliveryQuantity;
		}
		public void setRealDeliveryQuantity(BigDecimal realDeliveryQuantity) {
			this.realDeliveryQuantity = realDeliveryQuantity;
		}
		public String getRejectReason() {
			return rejectReason;
		}
		public void setRejectReason(String rejectReason) {
			this.rejectReason = rejectReason;
		}

		public Integer getChangePriceStatus() {
			return changePriceStatus;
		}

		public void setChangePriceStatus(Integer changePriceStatus) {
			this.changePriceStatus = changePriceStatus;
		}
	}

	public List<SubOrderItemVo> getItems() {
		return items;
	}
	public void setItems(List<SubOrderItemVo> items) {
		this.items = items;
	}
	public String getOrderCode() {
		return orderCode;
	}
	public void setOrderCode(String orderCode) {
		this.orderCode = orderCode;
	}
}

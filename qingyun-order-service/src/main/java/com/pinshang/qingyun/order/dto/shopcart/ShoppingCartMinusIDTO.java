package com.pinshang.qingyun.order.dto.shopcart;

import com.pinshang.qingyun.base.enums.YesOrNoEnums;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @Date 2019/11/18 13:45
 */
@Data
@ApiModel
@NoArgsConstructor
@AllArgsConstructor
public class ShoppingCartMinusIDTO {
    @ApiModelProperty(value = "商品id")
    private Long commodityId;
    @ApiModelProperty(hidden = true)
    private Long storeId;
    @ApiModelProperty("送货日期")
    private Date orderTime;
    @ApiModelProperty("是否是购物车页面请求")
    private YesOrNoEnums shopCartPage;
    @ApiModelProperty("商品数量")
    private BigDecimal quantity;
}

package com.pinshang.qingyun.order.aspect;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.pinshang.qingyun.infrastructure.common.utils.IpUtil;
import com.pinshang.qingyun.order.dto.OrderBillLogSaveIDTO;
import com.pinshang.qingyun.order.service.OrderBillLogService;
import com.pinshang.qingyun.order.service.WeChatSendMessageService;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;

/**
 * 调用结算服务时记录日志，异常时告警
 *
 * <AUTHOR>
 */
@Aspect
@Component
public class OrderBillLogAspect {

    @Autowired
    private OrderBillLogService logService;

    @Autowired
    private WeChatSendMessageService weChatSendMessageService;

    @Pointcut("@annotation(orderBillLog)")
    public void logPointcut(OrderBillLog orderBillLog) {
    }

    @Around("logPointcut(orderBillLog)")
    public Object logAround(ProceedingJoinPoint joinPoint, OrderBillLog orderBillLog) throws Throwable {
        Object result;
        String ipAddress = IpUtil.getRealIp();
        String description = orderBillLog.value();
        Date operationDate = new Date();
        String requestParams = getRequestParams(joinPoint);
        Long storeId = extractStoreId(requestParams);
        try {
            result = joinPoint.proceed();
            String response = result != null ? JSON.toJSONString(result) : null;
            //记录正常日志
            saveLog(ipAddress, description, operationDate, requestParams, response, storeId);
        } catch (Throwable throwable) {
            //微信告警
            weChatSendMessageService.sendWeChatMessage("客户" + storeId + "订单" + description + "异常:" + throwable.getMessage());
            //记录异常日志
            saveLog(ipAddress, description, operationDate, requestParams, throwable.getMessage(), storeId);
            throw throwable;
        }

        return result;
    }

    private String getRequestParams(ProceedingJoinPoint joinPoint) {
        Object[] args = joinPoint.getArgs();
        try {
            return JSON.toJSONString(args);
        } catch (Exception e) {
            return "Failed to parse request parameters";
        }
    }

    /**
     * 尝试获取参数中的storeId
     */
    private Long extractStoreId(String requestParams) {
        try {
            JSONArray jsonArray = JSON.parseArray(requestParams);
            Long storeId = null;
            for (Object jsonObject : jsonArray) {
                if (jsonObject instanceof JSONObject) {
                    JSONObject js = (JSONObject) jsonObject;
                    storeId = js.getLong("storeId");
                    break;
                }
            }
            return storeId;
        } catch (Exception e) {
            return null;
        }
    }

    private void saveLog(String ipAddress, String description, Date operationDate, String requestParams, String response, Long storeId) {
        OrderBillLogSaveIDTO orderLog = OrderBillLogSaveIDTO.builder()
                .host(ipAddress)
                .methodType(description)
                .param(requestParams)
                .createTime(operationDate)
                .storeId(storeId)
                .response(response)
                .build();
        logService.saveLogAsync(orderLog);
    }
}

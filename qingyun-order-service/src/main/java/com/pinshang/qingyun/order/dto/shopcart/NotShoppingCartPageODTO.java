package com.pinshang.qingyun.order.dto.shopcart;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @Date 2019/11/18 10:46
 */
@Data
@ApiModel
@NoArgsConstructor
@AllArgsConstructor
public class NotShoppingCartPageODTO {
    @ApiModelProperty(value = "商品id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long commodityId;
    @ApiModelProperty(value = "数量")
    private BigDecimal quantity;

    @ApiModelProperty(hidden = true)
    private Long id;
    @ApiModelProperty(hidden = true)
    private BigDecimal oldQuantity;

    @ApiModelProperty("是否可继续添加-->用于是否置灰购物车内加号按钮的判断")
    private Boolean ableAdd;
    @ApiModelProperty("商品库存缺少修改提示")
    private String stockWarningTips;

}

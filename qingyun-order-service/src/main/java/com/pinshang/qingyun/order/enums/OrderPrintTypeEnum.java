package com.pinshang.qingyun.order.enums;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;

/**
 *
 * <AUTHOR>
 * @Date 2018/4/8 16:26
 */
public enum  OrderPrintTypeEnum implements BaseEnum{

    LOCAL(1,"本地"),
    DELIVERYMAN(2,"送货员"),
    NOPRInteger(3,"不打印");

    private Integer code;
    private String desc;

    OrderPrintTypeEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    @Override
    public Integer convert() {
        return this.code;
    }
    @JsonValue
    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }
    @JsonCreator
    public static OrderPrintTypeEnum getEnumByCode(Integer code){
        if(code == null){
            return null;
        }
        for(OrderPrintTypeEnum e : OrderPrintTypeEnum.values()){
            if(e.getCode().intValue() == code){
                return e;
            }
        }
        return null;
    }
}

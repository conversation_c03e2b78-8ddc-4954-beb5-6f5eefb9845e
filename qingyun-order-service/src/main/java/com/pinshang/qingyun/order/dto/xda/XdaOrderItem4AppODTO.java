package com.pinshang.qingyun.order.dto.xda;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.pinshang.qingyun.base.configure.codec.DecimalSerializerKeep2;
import com.pinshang.qingyun.order.enums.ProductTypeEnums;
import com.pinshang.qingyun.xda.product.dto.commodityText.XdaCommodityInfoODTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;


/**
 * <AUTHOR>
 * @Date 2019/11/20 9:50
 */
@Data
@ApiModel
public class XdaOrderItem4AppODTO {
    @ApiModelProperty(value = "orderId", hidden = true)
    private Long orderId;
    @ApiModelProperty("商品名称")
    private String commodityName;
    @ApiModelProperty("商品id")
    private Long commodityId;
    @ApiModelProperty("单位")
    private String commodityUnit;
    @ApiModelProperty("销售单价")
    @JsonSerialize(using = DecimalSerializerKeep2.class)
    private BigDecimal commodityPrice;
    @ApiModelProperty("商品数量")
    private BigDecimal commodityNum;
    @ApiModelProperty("商品url")
    private String imageUrl;
    @ApiModelProperty("实发数量")
    private BigDecimal realDeliveryQuantity;
    @ApiModelProperty("商品类型")
    private Integer type;
    @ApiModelProperty("商品规格")
    private String commoditySpec;

    public void covert(XdaCommodityInfoODTO odto){
        this.commodityName = odto.getCommodityAppName();
        this.commodityUnit = odto.getCommodityUnitName();
        this.commoditySpec = odto.getCommoditySpec();
        this.imageUrl = odto.getDefaultPicUrl();
    }

}

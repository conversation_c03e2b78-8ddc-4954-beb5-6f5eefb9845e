package com.pinshang.qingyun.order.dto.tob;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * @Description：
 * @Author：ZhangHui
 * @Package：com.pinshang.qingyun.order.dto.tob
 * @Date: 2024/4/12
 */
@Data
@ApiModel(value = "ToBQueryOrderStatisticsODTO", description = "查询订单数据返回参数")
public class ToBQueryOrderStatisticsODTO {

    @ApiModelProperty("商品id")
    private Long commodityId;

    @ApiModelProperty("送货日期")
    private String orderTime;

    @ApiModelProperty("下单数量汇总（不包括取消订单）")
    private BigDecimal orderQuantity;

    @ApiModelProperty("下单份数汇总（不包括取消订单）")
    private Integer orderNumber;


}

package com.pinshang.qingyun.order.dto.report;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.pinshang.qingyun.renderer.annotation.FieldRender;
import com.pinshang.qingyun.renderer.constant.RenderFieldHelper;
import com.pinshang.qingyun.renderer.enums.FieldTypeEnum;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @ClassName ShortDeliveryReportNoHqStallODto
 * @Description TODO
 * <AUTHOR>
 * @Date 2024/12/19 17:21
 * @Version 1.0
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ShortDeliveryReportNoHqStallODto extends ShortDeliveryReportNoHqODto{
    @ExcelIgnore
    private Long stallId;

	@ExcelProperty(value ="档口名称",index = 24)
	@FieldRender(fieldType = FieldTypeEnum.STALL, fieldName = RenderFieldHelper.Stall.stallName, keyName = "stallId")
	private String stallName;
}

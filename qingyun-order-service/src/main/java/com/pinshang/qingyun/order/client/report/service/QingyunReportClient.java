package com.pinshang.qingyun.order.client.report.service;

import com.pinshang.qingyun.base.configure.FeignClientConfiguration;
import com.pinshang.qingyun.base.constant.ApplicationNameConstant;
import com.pinshang.qingyun.order.client.report.hystrix.QingyunReportClientHystrix;
import com.pinshang.qingyun.order.mapper.entry.commodity.CommodityOrderSalesEntry;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import java.util.List;


@FeignClient(value = ApplicationNameConstant.QINGYUN_REPORT_SERVICE, fallbackFactory = QingyunReportClientHystrix.class, configuration = FeignClientConfiguration.class)
public interface QingyunReportClient {


    /**
     * pda 手持订货查询 月均销量
     * @return
     */
    @RequestMapping(value = "/shopCommodityTax/findMonthSalesQuanty", method = RequestMethod.GET)
	List<CommodityOrderSalesEntry> findMonthSalesQuanty(@RequestParam(value = "commodityId",required = false) Long commodityId,@RequestParam(value = "storeId",required = false)Long storeId,@RequestParam(value = "inBegin",required = false)String inBegin,@RequestParam(value = "yesterday",required = false)String yesterday,@RequestParam(value = "flag",required = false)int flag);

}

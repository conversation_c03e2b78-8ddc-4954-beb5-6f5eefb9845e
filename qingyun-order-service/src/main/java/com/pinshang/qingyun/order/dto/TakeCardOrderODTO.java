package com.pinshang.qingyun.order.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Author: sk
 * @Date: 2020/11/23
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class TakeCardOrderODTO extends MsShopOrderSettingBaseDTO{

    /** 提货预约ID */
    private Long takeAppointmentId;


    private Long shopId;
    private Long storeId;

    //private String productId;
    //private BigDecimal productNum;

    private String orderTime;
    private Long enterpriseId;
    //物流模式
    private Integer logisticsModel;
    //供应商id
    private Long supplierId;
    private Long warehouseId;
    private String deliveryBatch;
    private String deleveryTimeRange;
    private Long userId;
    private String createName;

}

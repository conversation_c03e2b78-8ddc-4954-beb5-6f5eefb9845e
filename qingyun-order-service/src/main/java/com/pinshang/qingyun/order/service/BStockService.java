package com.pinshang.qingyun.order.service;

import com.alibaba.fastjson.JSON;
import com.pinshang.qingyun.base.configure.expand.QYAssert;
import com.pinshang.qingyun.base.enums.IogisticsModelEnums;
import com.pinshang.qingyun.base.enums.order.OrderTypeEnum;
import com.pinshang.qingyun.base.enums.order.ProductTypeEnum;
import com.pinshang.qingyun.box.utils.BeanCloneUtils;
import com.pinshang.qingyun.box.utils.DateTimeUtil;
import com.pinshang.qingyun.box.utils.DateUtil;
import com.pinshang.qingyun.order.manage.service.AutoOrderAgainClient;
import com.pinshang.qingyun.order.mapper.CommodityMapper;
import com.pinshang.qingyun.order.mapper.StoreMapper;
import com.pinshang.qingyun.order.mapper.entry.MdShopOrderSettingEntry;
import com.pinshang.qingyun.order.mapper.entry.commodity.CommodityBasicEntry;
import com.pinshang.qingyun.order.model.store.Store;
import com.pinshang.qingyun.order.service.auto.ShopAutoOrderService;
import com.pinshang.qingyun.order.vo.BStockLackVO;
import com.pinshang.qingyun.order.vo.commodity.CommodityVO;
import com.pinshang.qingyun.order.vo.order.BStockShortResponseVO;
import com.pinshang.qingyun.storage.dto.tob.*;
import com.pinshang.qingyun.storage.service.tob.ToBClient;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.ArrayStack;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @Author: sk
 * @Date: 2024/3/5
 */
@Slf4j
@Service
public class BStockService {

    @Autowired
    private ToBClient toBClient;
    @Autowired
    private StoreMapper storeMapper;
    @Lazy
    @Autowired
    private MdShopOrderSettingService mdShopOrderSettingService;
    @Autowired
    private CommodityMapper commodityMapper;
    @Autowired
    private BStockShortService bStockShortService;
    @Lazy
    @Autowired
    private ShopAutoOrderService shopAutoOrderService;
    @Autowired
    private AutoOrderAgainClient autoOrderAgainClient;


    /**
     * 获取B端库存依据 map（默认不传物流中心）
     *
     * @return
     */
    public Map<Long, CommodityInventoryODTO> getbStockMap(Date orderTime, Map<Long, BigDecimal> orderQuantityMap) {
        return getbStockMap(orderTime, orderQuantityMap, null);
    }

    /**
     * 获取B端库存依据 map（通达/全国B端客户）
     * @return
     */
    public Map<Long, CommodityInventoryODTO> getbStockMap(Date orderTime, Map<Long, BigDecimal> orderQuantityMap, Long logisticsCenterId) {
        List<CommodityInventoryDetailIDTO> orderCommodityList = new ArrayList<>();
        orderQuantityMap.forEach((k,v)->{
            CommodityInventoryDetailIDTO commodityInventoryDetailIDTO = new CommodityInventoryDetailIDTO();
            commodityInventoryDetailIDTO.setCommodityId(k);
            commodityInventoryDetailIDTO.setQuantity(v);
            commodityInventoryDetailIDTO.setLevel(ProductTypeEnum.NORMAL.getCode());
            orderCommodityList.add(commodityInventoryDetailIDTO);
        });
        QYAssert.isTrue(CollectionUtils.isNotEmpty(orderCommodityList), "查询库存依据,商品不能为空!");

        List<CommodityInventoryODTO> toBStockList = getCommodityInventory(orderTime, orderCommodityList,logisticsCenterId);
        Map<Long, CommodityInventoryODTO> toBStockMap;
        if(CollectionUtils.isNotEmpty(toBStockList)){
            toBStockMap = toBStockList.stream().collect(Collectors.toMap(CommodityInventoryODTO::getCommodityId, Function.identity()));
        } else {
            toBStockMap = new HashMap<>();
        }
        return toBStockMap;
    }

    private List<CommodityInventoryODTO> getCommodityInventory(Date orderTime, List<CommodityInventoryDetailIDTO> orderCommodityList, Long logisticsCenterId) {
        CommodityInventoryIDTO commodityInventoryIDTO = new CommodityInventoryIDTO();
        commodityInventoryIDTO.setOrderTime(orderTime != null ? orderTime : DateUtil.parseDate(DateTimeUtil.defaultDeliveryDate(), "yyyy-MM-dd"));
        commodityInventoryIDTO.setOrderCommodityList(orderCommodityList);
        commodityInventoryIDTO.setLogisticsCenterId(logisticsCenterId);
        return toBClient.queryCommodityWithBomInventory(commodityInventoryIDTO);
    }



    /**
     * 校验B端库存
     * @return
     */
    public Map<String, Object> checkBStockWithGift(Date orderTime, Long storeId, Integer orderType, List<CommodityInventoryDetailIDTO> orderCommodityList,
                                                   String remark, Long createId) {
        Map<String, Object> resultMap = new HashMap<>();
        CommodityInventoryIDTO commodityInventoryIDTO = new CommodityInventoryIDTO();
        commodityInventoryIDTO.setOrderTime(orderTime != null ? orderTime : DateUtil.parseDate(DateTimeUtil.defaultDeliveryDate(), "yyyy-MM-dd"));
        commodityInventoryIDTO.setOrderCommodityList(orderCommodityList);
        List<CommodityInventoryODTO> toBStockList = toBClient.queryCommodityWithBomInventory(commodityInventoryIDTO);
        Map<Long, CommodityInventoryODTO> toBStockMap = new HashMap<>();
        toBStockList.forEach((k) -> {
            toBStockMap.put(k.getCommodityId(), k);
        });
        resultMap.put("toBStockMap", toBStockMap);

        List<CommodityInventoryDetailIDTO> giftList = orderCommodityList.stream().filter(p -> ProductTypeEnum.GIFT.getCode().equals(p.getLevel())).collect(Collectors.toList());
        Map<Long, BigDecimal> giftMap = giftList.stream().collect(Collectors.toMap(CommodityInventoryDetailIDTO::getCommodityId,CommodityInventoryDetailIDTO::getQuantity,(key1 , key2)-> key2));


        List<BStockShortResponseVO> responseVOList = new ArrayList<>();
        Set<String> commodityIdList = new HashSet<>();
        orderCommodityList.forEach((k) -> {
            commodityIdList.add(k.getCommodityId() + "");
        });

        // 查询门店订货通用设置
        List<MdShopOrderSettingEntry> settingEntries = mdShopOrderSettingService.queryMdShopOrderSettingListByIds(storeId, new ArrayList<>(commodityIdList));
        if(CollectionUtils.isNotEmpty(settingEntries)){
            Map<Long, MdShopOrderSettingEntry> settingEntriesMap = settingEntries.stream().collect(Collectors.toMap(MdShopOrderSettingEntry::getCommodityId, Function.identity()));

            // 排除直送的
            for(CommodityInventoryODTO entry : toBStockList){
                MdShopOrderSettingEntry orderSettingEntry = settingEntriesMap.get(entry.getCommodityId());
                // 只判断配送、直通
                if(orderSettingEntry != null && (orderSettingEntry.getLogisticsModel() == IogisticsModelEnums.DISPATCHING.getCode()
                        || orderSettingEntry.getLogisticsModel() == IogisticsModelEnums.DIRECT_CONNECTION.getCode())){
                    // 只判断赠品的库存
                    if(ProductTypeEnum.GIFT.getCode().equals(entry.getLevel()) && !entry.getHaveInventory()){
                        BStockShortResponseVO responseVO = new BStockShortResponseVO();
                        responseVO.setCommodityId(entry.getCommodityId() + "");
                        responseVO.setQuantity(giftMap.get(entry.getCommodityId()));
                        responseVO.setInventoryQuantity(entry.getInventoryQuantity());
                        responseVO.setStockType(entry.getStockType());
                        responseVOList.add(responseVO);
                    }
                }
            }
        }

        if(CollectionUtils.isNotEmpty(responseVOList) && orderType != null){
            List<BStockLackVO> bStockLackList = getBStockLackList(storeId, orderType, remark, responseVOList, createId);
            bStockShortService.stockShort(bStockLackList);
        }

        resultMap.put("shortResponseList", responseVOList);
        return resultMap;
    }

    /**
     * 校验B端库存
     * @return
     */
    public List<BStockShortResponseVO> checkBStock(Long storeId, Integer orderType, Date orderTime, Map<Long, BigDecimal> orderQuantityMap,
                                                   String remark, Map<Long, CommodityInventoryODTO> toBStockMap, Long createId) {
        if(toBStockMap == null){
            toBStockMap = getbStockMap(orderTime, orderQuantityMap);
        }

        List<BStockShortResponseVO> responseVOList = new ArrayList<>();

        List<String> commodityIdList = new ArrayList();
        orderQuantityMap.forEach((key,value) -> {
            commodityIdList.add(key + "");
        });

        // 查询门店订货通用设置
        List<MdShopOrderSettingEntry> settingEntries = mdShopOrderSettingService.queryMdShopOrderSettingListByIds(storeId, commodityIdList);
        if(CollectionUtils.isNotEmpty(settingEntries)){
            Map<Long, MdShopOrderSettingEntry> settingEntriesMap = settingEntries.stream().collect(Collectors.toMap(MdShopOrderSettingEntry::getCommodityId, Function.identity()));

            // 排除直送的
            for(Map.Entry<Long, BigDecimal> entry : orderQuantityMap.entrySet()){
                Long commodityId = entry.getKey();
                MdShopOrderSettingEntry orderSettingEntry = settingEntriesMap.get(commodityId);
                // 只判断配送、直通
                if(orderSettingEntry != null && (orderSettingEntry.getLogisticsModel() == IogisticsModelEnums.DISPATCHING.getCode()
                                                || orderSettingEntry.getLogisticsModel() == IogisticsModelEnums.DIRECT_CONNECTION.getCode())){

                    CommodityInventoryODTO inventoryODTO = toBStockMap.get(entry.getKey());
                    if(inventoryODTO != null && !inventoryODTO.getHaveInventory()){
                        BStockShortResponseVO responseVO = new BStockShortResponseVO();
                        responseVO.setCommodityId(entry.getKey() + "");
                        responseVO.setQuantity(entry.getValue());
                        responseVO.setInventoryQuantity(inventoryODTO.getInventoryQuantity());
                        responseVO.setStockType(inventoryODTO.getStockType());
                        responseVOList.add(responseVO);
                    }
                }
            }
        }

        if(CollectionUtils.isNotEmpty(responseVOList) && orderType != null){
            List<BStockLackVO> bStockLackList = getBStockLackList(storeId, orderType, remark, responseVOList, createId);
            bStockShortService.stockShort(bStockLackList);
        }
        return responseVOList;
    }

    /**
     * 提交订单或者自动下单，赠品不足记录库存不足
     */
    public void giftShort(Long storeId, Integer orderType, String remark, List<BStockShortResponseVO> responseVOList, Long createId){
        if(CollectionUtils.isNotEmpty(responseVOList)){
            List<BStockLackVO> bStockLackList = getBStockLackList(storeId, orderType, remark, responseVOList, createId);
            bStockShortService.stockShort(bStockLackList);
        }
    }

    private List<BStockLackVO> getBStockLackList(Long storeId, Integer orderType, String remark, List<BStockShortResponseVO> responseVOList,Long createId) {
        List<BStockLackVO> bStockLackList = new ArrayStack();
        for(BStockShortResponseVO responseVO : responseVOList){
            BStockLackVO lackVO = BeanCloneUtils.copyTo(responseVO, BStockLackVO.class);
            lackVO.setStoreId(storeId);
            lackVO.setCommodityId(Long.valueOf(responseVO.getCommodityId()));
            lackVO.setStockQuantity(responseVO.getInventoryQuantity());
            lackVO.setNeedQuantity(responseVO.getQuantity());
            lackVO.setOrderType(orderType);
            lackVO.setRemark(remark);
            lackVO.setCreateId(createId);
            bStockLackList.add(lackVO);
        }
        return bStockLackList;
    }

    /**
     * 冻结B端库存
     * @return
     */
    public List<CommodityInventoryODTO> warehouseFreezeInventory(WarehouseFreezeInventoryIDTO idto, Long createId, String autoStr){

        if(StringUtils.isBlank(idto.getStoreCode()) || StringUtils.isBlank(idto.getStoreName())){
            Store store = storeMapper.selectByPrimaryKey(idto.getStoreId());
            idto.setStoreCode(store.getStoreCode());
            idto.setStoreName(store.getStoreName());
        }

        // 如果冻结份数为空，则重新赋值
        List<ToBOrderDetailIDTO> filterList = idto.getOrderCommodityDetailList().stream().filter(p -> p.getOrderNumber() == null).collect(Collectors.toList());
        if(CollectionUtils.isNotEmpty(filterList)){
            List<Long> commodityIdList = idto.getOrderCommodityDetailList().stream().map(item -> item.getCommodityId()).collect(Collectors.toList());
            CommodityVO vo = new CommodityVO();
            vo.setCommodityIdList(commodityIdList);
            List<CommodityBasicEntry> basicEntryList = commodityMapper.findCommodityBasicListByParam(vo);
            Map<String, CommodityBasicEntry> commMap = basicEntryList.stream().collect(Collectors.toMap(CommodityBasicEntry::getCommodityId, Function.identity()));
            idto.getOrderCommodityDetailList().forEach(item -> {
                CommodityBasicEntry commodityBasicEntry = commMap.get(item.getCommodityId() + "");
                if(item.getOrderNumber() == null &&  commodityBasicEntry != null){
                    BigDecimal commodityPackageSpec = commodityBasicEntry.getCommodityPackageSpec();
                    item.setOrderNumber(item.getOrderQuantity().divide(commodityPackageSpec, 0, BigDecimal.ROUND_UP).intValue());
                }
            });
        }

        List<CommodityInventoryODTO> freezeResponseList;
        try {
            freezeResponseList = toBClient.warehouseFreezeInventory(idto);
        }catch (Exception e){
            // 自动订货的如果报错就当前截单时间重新异步跑一次
            if(OrderTypeEnum.SHOP_AUTO_ORDER.getCode().equals(idto.getSourceType())){
                shopAutoOrderService.againAutoOrderAsync(autoStr);
            }
            // 重新查询实时库存
            Map<Long, BigDecimal> orderQuantityMap = new HashMap<>();
            idto.getOrderCommodityDetailList().forEach(item -> {
                orderQuantityMap.put(item.getCommodityId(), item.getOrderQuantity());
            });
            Map<Long, CommodityInventoryODTO> toBStockMap = getbStockMap(idto.getOrderTime(), orderQuantityMap, idto.getLogisticsCenterId());
            List<BStockLackVO> bStockLackList = getFreezeLackList(idto, toBStockMap, createId);
            if(CollectionUtils.isNotEmpty(bStockLackList)){
                this.stockShortAsync(bStockLackList);
            }

            // 冻结失败，调用释放冻结方法
            try{
                warehouseUnfreezeInventory(idto.getOrderId(), idto.getType());
            }catch (Exception e2){
                log.warn("冻结失败，调用释放冻结方法异常 orderId {}", idto.getOrderId(),  e2);
            }
            log.warn("冻结B端库存失败,入参:{}", JSON.toJSONString(idto));
            throw e;
        }

       return freezeResponseList;
    }

    @Async
    public void stockShortAsync(List<BStockLackVO> voList) {
        bStockShortService.stockShort(voList);
    }

    private List<BStockLackVO> getFreezeLackList(WarehouseFreezeInventoryIDTO idto, Map<Long, CommodityInventoryODTO> toBStockMap, Long createId) {
        List<BStockLackVO> bStockLackList = new ArrayStack();
        for(ToBOrderDetailIDTO toBOrderDetailIDTO : idto.getOrderCommodityDetailList()){
            CommodityInventoryODTO commodityInventoryODTO = toBStockMap.get(toBOrderDetailIDTO.getCommodityId());
            if(commodityInventoryODTO != null && !commodityInventoryODTO.getHaveInventory()){
                BStockLackVO lackVO = BeanCloneUtils.copyTo(toBOrderDetailIDTO, BStockLackVO.class);
                lackVO.setStoreId(idto.getStoreId());
                lackVO.setCommodityId(toBOrderDetailIDTO.getCommodityId());
                lackVO.setStockQuantity(commodityInventoryODTO.getInventoryQuantity());
                lackVO.setNeedQuantity(toBOrderDetailIDTO.getOrderQuantity());
                lackVO.setOrderType(idto.getSourceType());
                lackVO.setRemark("冻结失败");
                lackVO.setCreateId(createId);
                bStockLackList.add(lackVO);
            }
        }
        return bStockLackList;
    }

    /**
     * 解冻B端库存
     */
    public Boolean warehouseUnfreezeInventory(Long orderId, Integer type){
        WarehouseUnfreezeInventoryIDTO unfreezeInventoryIDTO = new WarehouseUnfreezeInventoryIDTO();
        unfreezeInventoryIDTO.setOrderId(orderId);
        unfreezeInventoryIDTO.setType(type);
        toBClient.warehouseUnfreezeInventory(unfreezeInventoryIDTO);
        return Boolean.TRUE;
    }

    /**
     * 冻结转换(由加货单冻结，转换成B端冻结)
     * @return
     */
    public Boolean cOrderToBOrderDealInventory(COrderToBOrderDealInventoryIDTO dto){

        if(StringUtils.isBlank(dto.getStoreCode()) || StringUtils.isBlank(dto.getStoreName())){
            Store store = storeMapper.selectByPrimaryKey(dto.getStoreId());
            dto.setStoreCode(store.getStoreCode());
            dto.setStoreName(store.getStoreName());
        }
        toBClient.cOrderToBOrderDealInventory(dto);
        return Boolean.TRUE;
    }
}

package com.pinshang.qingyun.order.validation;

import com.pinshang.qingyun.base.enums.BusinessTypeEnums;
import com.pinshang.qingyun.order.dto.cup.OrderRequestDto;
import com.pinshang.qingyun.order.dto.xda.v4.TdaDeliveryTimeRangeODTO;
import com.pinshang.qingyun.order.service.StoreService;
import com.pinshang.qingyun.order.service.xda.v4.TdaOrderService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.Objects;

@Component
@RequiredArgsConstructor
public class DeliveryTimeValidator extends OrderValidationHandler{

    private final TdaOrderService tdaOrderService;

    private final StoreService storeService;
    @Override
    public OrderValidationResult validate(OrderRequestDto orderRequestDto) {

        //如果不是订单导入，则按照上送的客户的id，送货时间段，配送批次校验
        //如果是订单导入，则没有上送送货时间段、配送批次，需要拿到最早送货时间段对应的配送批次
        if(!Objects.equals(orderRequestDto.getIfImportSave(),1)){
            TdaDeliveryTimeRangeODTO tdaDeliveryTimeRangeODTO = tdaOrderService.checkCupDeliveryTimeRange(orderRequestDto.getStoreId()
                    , orderRequestDto.getDeliveryTimeRange()
                    , orderRequestDto.getDeliveryBatch());

            orderRequestDto.setTdaDeliveryTimeRangeODTO(tdaDeliveryTimeRangeODTO);
        }else{
            Long storeId = orderRequestDto.getStoreId();
            Integer storeBussinessType = storeService.getStoreBussinessType(storeId);
            if(Objects.equals(storeBussinessType, BusinessTypeEnums.B_COUNTRY.getCode())){
                TdaDeliveryTimeRangeODTO tdaDeliveryTimeRangeODTO = tdaOrderService.queryBCountryDeliveryTimeRangeListByStoreId(storeId);
                orderRequestDto.setTdaDeliveryTimeRangeODTO(tdaDeliveryTimeRangeODTO);
            }
        }

        return validateNext(orderRequestDto);
    }
}

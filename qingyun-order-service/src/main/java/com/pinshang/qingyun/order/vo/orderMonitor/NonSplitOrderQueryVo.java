package com.pinshang.qingyun.order.vo.orderMonitor;

import com.pinshang.qingyun.base.page.Pagination;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * Created by weican on 2017-10-15.
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class NonSplitOrderQueryVo extends Pagination {
    /** 单个订单ID折单**/
    private Long orderId;
    /** 指定送货日期订单折单**/
    private Date orderTime;
    /** 订单编码 **/
    private String orderCode;
    /** 客户编码 **/
    private String storeCode;
    // 订单状态(0正常,1删除,2 取消)
    private Integer orderStatus;
}

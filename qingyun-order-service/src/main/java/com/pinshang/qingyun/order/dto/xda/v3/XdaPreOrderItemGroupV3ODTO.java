package com.pinshang.qingyun.order.dto.xda.v3;

import com.pinshang.qingyun.base.enums.SalesPromotionStatusEnums;
import com.pinshang.qingyun.marketing.dto.app.PreOrderGroupODTO;
import com.pinshang.qingyun.order.dto.shopcart.v3.CommodityPromotionV3ODTO;
import com.pinshang.qingyun.order.dto.shopcart.v3.ShoppingCartGroupV3ODTO;
import com.pinshang.qingyun.order.dto.xda.v2.XdaOrderItemAppV2ODTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2023/12/21 14:34
 */
@Data
public class XdaPreOrderItemGroupV3ODTO {
    private String promotionId;

    @ApiModelProperty("促销类型")
    private Integer code;

    @ApiModelProperty(" 是否满足促销条件，0=未满足 去凑单，1=满足，再逛逛 第三层促销")
    private Integer fullStatus;

    private CommodityPromotionV3ODTO commodityPromotionV3ODTO;

    @ApiModelProperty("普通商品组")
    private List<XdaOrderItemAppV3ODTO> normalGroup;

    @ApiModelProperty("赠送商品组")
    private List<XdaOrderItemAppV3ODTO> giftGroup;

//    @ApiModelProperty("失效商品组")
//    private List<XdaOrderItemAppV3ODTO> invalidateGroup;

    public static void initCommodityPromotionV3ODTO(PreOrderGroupODTO preOrderGroupODTO,XdaPreOrderItemGroupV3ODTO xdaPreOrderItemGroupV3ODTO) {
        CommodityPromotionV3ODTO commodityPromotion = new CommodityPromotionV3ODTO();
        xdaPreOrderItemGroupV3ODTO.setPromotionId(preOrderGroupODTO.getPromotionId().toString());
        xdaPreOrderItemGroupV3ODTO.setCode(preOrderGroupODTO.getCode());
        xdaPreOrderItemGroupV3ODTO.setFullStatus(preOrderGroupODTO.getFullStatus());
        String name = "";
        List<String> remarkList = new ArrayList<>();
        if(null != preOrderGroupODTO.getPromotionType()){
            if (SalesPromotionStatusEnums.GIFT.getCode() == preOrderGroupODTO.getPromotionType().intValue()) {
                name =  "买赠";
                remarkList = CommodityPromotionV3ODTO.GIFT_REMARK_LIST;

            } else if (SalesPromotionStatusEnums.GRADIENT_DISCOUNT.getCode() == preOrderGroupODTO.getPromotionType().intValue()) {
                name = "梯度满折";
                remarkList = CommodityPromotionV3ODTO.GRADIENT_DISCOUNT_REMARK_LIST;

            }
        }
        CommodityPromotionV3ODTO commodityPromotionV3ODTO = new CommodityPromotionV3ODTO();
        commodityPromotionV3ODTO.setPromotionTypeName(name);
        commodityPromotionV3ODTO.setPromotionType(preOrderGroupODTO.getPromotionType());
        commodityPromotionV3ODTO.setPromotionName(preOrderGroupODTO.getPromotionName());
        commodityPromotionV3ODTO.setRuleList(preOrderGroupODTO.getPromotionRules());
        commodityPromotionV3ODTO.setRemarkList(remarkList);

        String tips = "";
        if(null != preOrderGroupODTO.getTips())
            tips = preOrderGroupODTO.getTips();

        if(null != preOrderGroupODTO.getNextTips())
            tips = tips + "," + preOrderGroupODTO.getNextTips();

        commodityPromotionV3ODTO.setNextTips(tips);
        xdaPreOrderItemGroupV3ODTO.setCommodityPromotionV3ODTO(commodityPromotion);
    }
}

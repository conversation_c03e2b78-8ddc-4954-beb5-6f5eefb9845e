package com.pinshang.qingyun.order.vo.tob;


import com.pinshang.qingyun.page.Pagination;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Description：
 * @Author：ZhangHui
 * @Package：com.pinshang.qingyun.order.vo.tob
 * @Date: 2024/4/7
 */
@Data
@ApiModel(value = "AdminToBOrderStatisticReqVo", description = "b端下单统计查询参数")
public class AdminToBOrderStatisticReqVo extends Pagination {

    @ApiModelProperty("送货开始日期")
    private String startOrderTime;

    @ApiModelProperty("送货结束日期")
    private String endOrderTime;

    @ApiModelProperty(value = "商品ID")
    private String commodityId;

}

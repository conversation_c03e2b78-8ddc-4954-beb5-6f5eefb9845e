package com.pinshang.qingyun.order.model.bigShop;

import com.pinshang.qingyun.base.po.BaseSimplePO;

import javax.persistence.Entity;
import javax.persistence.Table;

/**
 * @Author: sk
 * @Date: 2024/10/11
 */
@Entity
@Table(name="t_dd_receive_doc_pic")
public class DdReceiveDocPic extends BaseSimplePO {

    /** 单据号ID */
    private Long docId;

    /** 商品ID */
    private Long commodityId;

    /** 收货方式 1=PDA收货  2=PC收货 */
    private Integer receiveType;

    /** 类型：1-普通图片、2-长图、3-视频 */
    private Integer picType;

    /** 图片地址 */
    private String picUrl;

    public Long getDocId() {
        return docId;
    }

    public void setDocId(Long docId) {
        this.docId = docId;
    }

    public Long getCommodityId() {
        return commodityId;
    }

    public void setCommodityId(Long commodityId) {
        this.commodityId = commodityId;
    }

    public Integer getPicType() {
        return picType;
    }

    public void setPicType(Integer picType) {
        this.picType = picType;
    }

    public String getPicUrl() {
        return picUrl;
    }

    public void setPicUrl(String picUrl) {
        this.picUrl = picUrl;
    }
    public Integer getReceiveType() {
        return receiveType;
    }

    public void setReceiveType(Integer receiveType) {
        this.receiveType = receiveType;
    }
}

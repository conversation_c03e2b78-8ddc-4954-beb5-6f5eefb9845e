package com.pinshang.qingyun.order.dto;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.pinshang.qingyun.renderer.annotation.FieldRender;
import com.pinshang.qingyun.renderer.constant.RenderFieldHelper;
import com.pinshang.qingyun.renderer.enums.FieldTypeEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * @ClassName ShopCountStockDetailItemODTO
 * <AUTHOR>
 * @Date 2022/12/14 13:35
 * @Description ShopCountStockDetailItemODTO
 * @Version 1.0
 */
@Data
public class ShopCountStockDetailItemODTO {
    @ApiModelProperty("商品id")
    @JsonSerialize(using = ToStringSerializer.class)
    @ExcelIgnore
    private Long commodityId;

    @ApiModelProperty("商品编码")
    @ExcelProperty(value = "商品编码")
    private String commodityCode;

    @ApiModelProperty("商品条码")
    @ExcelProperty(value = "商品条码")
    private String barCode;

    @ApiModelProperty("商品名称")
    @ExcelProperty(value = "商品名称")
    private String commodityName;

    @ApiModelProperty("规格")
    @ExcelProperty(value = "规格")
    private String commoditySpec;

    @ApiModelProperty(value = "商品计量单位")
    @FieldRender(fieldType = FieldTypeEnum.DICTIONARY,fieldName = RenderFieldHelper.Dictionary.optionName,keyName = "commodityUnitId")
    @ExcelProperty(value = "计量单位")
    private String commodityUnit;

    @ExcelIgnore
    @ApiModelProperty(value = "商品计量单位id")
    private Long commodityUnitId;

    @ApiModelProperty("包装规格")
    @ExcelProperty(value = "包装规格")
    private BigDecimal commodityPackageSpec;

    @ApiModelProperty("是否称重")
    @ExcelProperty(value = "是否称重")
    private String isWeight;

    @ApiModelProperty("订货商品")
    @ExcelProperty(value = "实发数量")
    private BigDecimal varietyTotal;

    @ApiModelProperty("已点商品")
    @ExcelProperty(value = "已点数量")
    private BigDecimal countNum;

    @ApiModelProperty(value = "差异数量", notes = "已点数量-实发数量")
    @ExcelProperty(value = "差异数量")
    private BigDecimal diffQuantity;

    @ApiModelProperty("操作人id")
    @JsonSerialize(using = ToStringSerializer.class)
    @ExcelIgnore
    private Long updateId;

    @ApiModelProperty("操作人")
    @FieldRender(fieldType = FieldTypeEnum.USER,fieldName = RenderFieldHelper.User.realName,keyName = "updateId")
    @ExcelProperty(value = "操作人")
    private String updateUserName;

    @ApiModelProperty("操作时间")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
    @ExcelProperty(value = "操作时间")
    private Date updateUserTime;

}

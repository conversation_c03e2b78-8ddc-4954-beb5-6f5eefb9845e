package com.pinshang.qingyun.order.dto;

import com.pinshang.qingyun.base.page.Pagination;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @Author: sk
 * @Date: 2020/3/17
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class XdReceiveDocIDTO  extends Pagination {

    @ApiModelProperty("单据id")
    private Long docId;

    @ApiModelProperty("商品编码、条形码、名称")
    private String commodityKey;

    @ApiModelProperty("货位id")
    private Long shelfId;



    private String deliveryTime;
    private Long commodityId;
    private Long shopId;

    private List<Long> commodityIdList;
}

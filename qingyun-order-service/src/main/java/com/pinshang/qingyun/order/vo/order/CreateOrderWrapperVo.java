package com.pinshang.qingyun.order.vo.order;

import com.pinshang.qingyun.order.model.order.Order;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 新建订单包装对象
 * <AUTHOR>
 * @date 2019/4/11 17:39.
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class CreateOrderWrapperVo {
    private List<FilterTipVo> filterTipVos;
    private Order kafkaOrder;
}

package com.pinshang.qingyun.order.model.distribution;

import com.pinshang.qingyun.base.po.BaseTimePO;
import com.pinshang.qingyun.order.model.store.Store;

import javax.persistence.Entity;
import javax.persistence.Table;
import javax.persistence.Transient;
import java.util.List;

/**
 * 运输线路
 **/
@Entity
@Table(name = "t_distribution_line")
public class DistributionLine extends BaseTimePO {
	/**线路编码 **/
    private String lineCode;
	/**线路名称 **/
    private String lineName;
	/**送货员id **/
    private Long deliveryManId;
    @Transient
    private String deliveryManIdStr;
	/**送货员 **/
    private String deliveryMan;
    /**发货时间id **/
    private Long deliveryTimeId;
	/**发货时间 **/
    private String deliveryTime;
    /**发货仓库Id**/
    private Long deliveryWarehouseId;
	/**发货仓库 **/
    private String deliveryWarehouse;
	/**车位id **/
    private Long carportId;
    /**车位名称**/
    private String carportName;
	/**创建人 **/
    private Long createId;
    /**线路状态：0停用，1启用**/
    private Integer lineStatus;

    /** 新立成中线路和送货员一对多,新清美中一对一.同步的时候就出现问题,为了解决这种同步问题 **/
    private String lineOldCode;

    /**状态显示**/
    @Transient
    private String lineStatusName;
    /**手机号**/
    @Transient
    private String mobilePhoneNumber;
    @Transient
    private List<Store> storeList;
    /**覆盖客户数**/
    @Transient
    private Integer storeCount;
    /**送货员编号(编辑之前的)，同步新立成时用**/
    @Transient
    private String deliveryManCode;
    /**老烽火对应的线路送货员编号**/
    @Transient
    private String freehomeDeliveryManCode;

    /**客户ID**/
    @Transient
    private Long storeId;

    public Long getStoreId() {
        return storeId;
    }

    public void setStoreId(Long storeId) {
        this.storeId = storeId;
    }

    public String getLineOldCode() {
        return lineOldCode;
    }

    public void setLineOldCode(String lineOldCode) {
        this.lineOldCode = lineOldCode;
    }

    public String getDeliveryManCode() {
        return deliveryManCode;
    }

    public void setDeliveryManCode(String deliveryManCode) {
        this.deliveryManCode = deliveryManCode;
    }

    public Integer getStoreCount() {
		if(null != storeCount){
			return storeCount;
		}
		return 0;
	}
	public void setStoreCount(Integer storeCount) {
		this.storeCount = storeCount;
	}
	public String getLineCode(){
        return lineCode;
    }
    public void setLineCode(String lineCode){
        this.lineCode = lineCode;
    }
    public String getLineName(){
        return lineName;
    }
    public void setLineName(String lineName){
        this.lineName = lineName;
    }
    public Long getDeliveryManId(){
        return deliveryManId;
    }
    public void setDeliveryManId(Long deliveryManId){
        this.deliveryManId = deliveryManId;
    }
    public String getDeliveryMan(){
        return deliveryMan;
    }
    public void setDeliveryMan(String deliveryMan){
        this.deliveryMan = deliveryMan;
    }
    public Long getDeliveryWarehouseId(){
        return deliveryWarehouseId;
    }
    public void setDeliveryWarehouseId(Long deliveryWarehouseId){
        this.deliveryWarehouseId = deliveryWarehouseId;
    }
    public String getDeliveryWarehouse(){
        return deliveryWarehouse;
    }
    public void setDeliveryWarehouse(String deliveryWarehouse){
        this.deliveryWarehouse = deliveryWarehouse;
    }
    public Long getCarportId(){
        return carportId;
    }
    public void setCarportId(Long carportId){
        this.carportId = carportId;
    }
    public String getCarportName(){
        return carportName;
    }
    public void setCarportName(String carportName){
        this.carportName = carportName;
    }
    public Long getCreateId(){
        return createId;
    }
    public void setCreateId(Long createId){
        this.createId = createId;
    }
	public Integer getLineStatus() {
		return lineStatus;
	}
	public void setLineStatus(Integer lineStatus) {
		this.lineStatus = lineStatus;
	}
	
	public String getDeliveryManIdStr() {
		return deliveryManId!=null?deliveryManId.toString():null;
	}

	public void setDeliveryManIdStr(String deliveryManIdStr) {
		this.deliveryManIdStr = deliveryManIdStr;
	}

	public void setLineStatusName(String lineStatusName) {
		this.lineStatusName = lineStatusName;
	}
	public Long getDeliveryTimeId() {
		return deliveryTimeId;
	}
	public void setDeliveryTimeId(Long deliveryTimeId) {
		this.deliveryTimeId = deliveryTimeId;
	}
	public String getDeliveryTime() {
		return deliveryTime;
	}
	public void setDeliveryTime(String deliveryTime) {
		this.deliveryTime = deliveryTime;
	}
	public String getMobilePhoneNumber() {
        if(null != mobilePhoneNumber){
            if("0".equals(mobilePhoneNumber)){
                return "";
            }
        }
        return mobilePhoneNumber;
	}
	public void setMobilePhoneNumber(String mobilePhoneNumber) {
		this.mobilePhoneNumber = mobilePhoneNumber;
	}
	public List<Store> getStoreList() {
		return storeList;
	}
	public void setStoreList(List<Store> storeList) {
		this.storeList = storeList;
	}

    public String getFreehomeDeliveryManCode() {
        return freehomeDeliveryManCode;
    }

    public void setFreehomeDeliveryManCode(String freehomeDeliveryManCode) {
        this.freehomeDeliveryManCode = freehomeDeliveryManCode;
    }
}
/*
 * tramy.cn Inc.
 * Copyright (c) 2021-2024 All Rights Reserved.
 */
package com.pinshang.qingyun.order.bo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 充值实体类
 * </p>
 *
 * <AUTHOR> shenyang
 * @version : 1.0.0
 * @history : modify history
 * <author>              <time>              <version>              <desc>
 * @since : 2024/07/18 14:04
 */
@Builder
@Data
public class StoreRechargeBO implements Serializable {
    private static final long serialVersionUID = -1L;
    private Long storeId;
    private Double money;
    private Long bank;
    private Date bankDate;
    private String remark;
    @ApiModelProperty("收款类型Id:从数据字典code[receiptType]获取")
    private Long receiptType;
    @ApiModelProperty("付款方式Id:从数据字典code[paymentMethod]获取")
    private Long paymentMethod;
    @ApiModelProperty("收款日期")
    private Date receiptDate;
    @ApiModelProperty("系统内部交易code")
    private String tradeCode;
    @ApiModelProperty("第三方交易code")
    private String thirdPartyTradeCode;
    @ApiModelProperty("")
    private Date tradeTime;
    @ApiModelProperty("交易类型：com.pinshang.qingyun.base.enums.StoreBillTypeEnums")
    private Integer billType;
    @ApiModelProperty("订单编码")
    private String orderCode;
    @ApiModelProperty("销售主体公司Id=t_company.id （销售||网络公司Id）")
    private Long salesCompanyId;
    @ApiModelProperty("销售主体公司简称（备注不一定是公司全称,销售||网络）")
    private String salesCompanyName;
    private Long userId;
}
package com.pinshang.qingyun.order.dto.shopcart.v2;

import com.pinshang.qingyun.base.enums.YesOrNoEnums;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @Date 2019/11/18 13:45
 */
@Data
@ApiModel
@NoArgsConstructor
@AllArgsConstructor
public class ShoppingCartMinusV2IDTO {
    @ApiModelProperty(value = "商品类型 默认1、普通商品 2、特惠商品")
    private Integer commodityType;

    @ApiModelProperty(value = "商品id")
    private Long commodityId;

    @ApiModelProperty(hidden = true)
    private Long storeId;

    @ApiModelProperty("送货日期")
    private Date orderTime;

    @ApiModelProperty("是否是购物车页面请求")
    private YesOrNoEnums shopCartPage;

    @ApiModelProperty("商品数量")
    private BigDecimal quantity;
}

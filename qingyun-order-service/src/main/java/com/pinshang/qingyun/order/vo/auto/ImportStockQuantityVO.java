package com.pinshang.qingyun.order.vo.auto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ImportStockQuantityVO {

    private Long commodityId;

    private String commodityCode;

    private String commodityName;

    private BigDecimal stockQuantity;

    /**
     * 物流配送模式0=直送，1＝配送，2＝直通
     */
    private Integer logisticsModel;
}

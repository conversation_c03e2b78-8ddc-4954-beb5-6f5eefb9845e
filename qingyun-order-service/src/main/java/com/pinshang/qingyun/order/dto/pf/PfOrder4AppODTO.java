package com.pinshang.qingyun.order.dto.pf;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.pinshang.qingyun.base.configure.codec.DecimalSerializerKeep2;

import com.pinshang.qingyun.base.configure.codec.LongToStringKeep3;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;


/**
 * <AUTHOR>
 * @Date 2019/11/20 9:50
 */
@Data
@ApiModel
public class PfOrder4AppODTO {
    @ApiModelProperty(value = "orderId", hidden = true)
    @JsonSerialize(using = LongToStringKeep3.class)
    private Long orderId;
    @ApiModelProperty("订单号")
    private String orderCode;
    @ApiModelProperty("实付总金额")
    @JsonSerialize(using = DecimalSerializerKeep2.class)
    private BigDecimal summation;
    @ApiModelProperty(" 实发金额")
    @JsonSerialize(using = DecimalSerializerKeep2.class)
    private BigDecimal realAmount;
    @ApiModelProperty("2=取消 7=待发货11=出库中15=配送中19=配送完成")
    private Integer processStatus;
    @ApiModelProperty("订单状态(0正常,1删除,2取消)")
    private Integer orderStatus;
    @ApiModelProperty("送货日期")
    private Date orderTime;
    @ApiModelProperty("订单创建日期")
    private Date orderCreateTime;
    @ApiModelProperty("品种合计")
    private Integer varietySum;
    @ApiModelProperty("商品列表")
    private List<PfOrderItem4AppODTO> commodities;

    @ApiModelProperty("订单运费金额（免运费为0）")
    private BigDecimal freightAmount;
    
    @ApiModelProperty("商品小计=实付总金额")
    @Deprecated
    public BigDecimal getCommoditySubTotal() {
    	return summation;
//    	BigDecimal total = BigDecimal.ZERO;
//    	for (PfOrderItem4AppODTO c : commodities) {
//    		BigDecimal comNum = c.getCommodityNum() != null ? c.getCommodityNum() : BigDecimal.ZERO;
//    		BigDecimal comPrice = c.getCommodityPrice() != null ? c.getCommodityPrice() : BigDecimal.ZERO;
//    		
//    		total = total.add(comNum.multiply(comPrice).setScale(2, RoundingMode.HALF_UP));
//    	}
//    	return total;
    }

    @ApiModelProperty("（批发订单）合计")
    public BigDecimal getPfOrderTotalAmount() {
    	BigDecimal total = BigDecimal.ZERO;
    	BigDecimal commoditySubTotal = this.summation != null ? this.summation : BigDecimal.ZERO;
    	BigDecimal freightAmount = this.freightAmount != null ? this.freightAmount : BigDecimal.ZERO;
    	return total.add(commoditySubTotal).add(freightAmount);
    }
}

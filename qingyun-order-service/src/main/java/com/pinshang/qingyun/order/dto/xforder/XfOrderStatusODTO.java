package com.pinshang.qingyun.order.dto.xforder;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import com.pinshang.qingyun.order.model.order.XfOrder;

/**
 * 现付单状态
 */
@Data
@NoArgsConstructor
public class XfOrderStatusODTO {
	@ApiModelProperty(position = 11, required = true, value = "付款流水编码(充值单号），XDAPayBill.billCode")
	private String billCode;
	@ApiModelProperty(position = 12, required = true, value = "现付单状态：0-待处理、1-下单成功、2-下单失败	—— 参见枚举XfOrder.XfOrderStatusEnums")
	private Integer status;
	@ApiModelProperty(position = 13, required = true, value = "错误信息")
	private String errorMsg;
	
	public static XfOrderStatusODTO init(XfOrder model) {
		XfOrderStatusODTO o = new XfOrderStatusODTO();
		if (null == model) {
			o.setStatus(XfOrder.XfOrderStatusEnums.FAILURE.getCode());
			o.errorMsg = "单据不存在!";
			return o;
		}
		
		o.billCode = model.getBillCode();
		o.status = model.getStatus();
		o.errorMsg = model.getErrorMsg();
		
		if (XfOrder.XfOrderStatusEnums.INIT.getCode().equals(model.getStatus())) {
			o.errorMsg = "订单正在处理中...";
		} else if (XfOrder.XfOrderStatusEnums.SUCCESS.getCode().equals(model.getStatus())) {
			o.errorMsg = "下单成功";
		}
		return o;
	}
	
}

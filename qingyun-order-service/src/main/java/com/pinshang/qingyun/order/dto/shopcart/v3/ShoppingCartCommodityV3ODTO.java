package com.pinshang.qingyun.order.dto.shopcart.v3;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.pinshang.qingyun.base.configure.codec.DecimalSerializerKeep2;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

@Data
@ApiModel
@NoArgsConstructor
@AllArgsConstructor
public class ShoppingCartCommodityV3ODTO implements Serializable {
    @ApiModelProperty("购物车明细id")
    private Long shoppingCartId;
    @ApiModelProperty("前台二级品类ID")
    private Long xdaSecondCategoryId;
    @ApiModelProperty("商品名称")
    private String commodityName;
    @ApiModelProperty("商品id")
    private Long commodityId;
    @ApiModelProperty("商品编码")
    private String commodityCode;
    @ApiModelProperty("商品ID字符串")
    private String commodityIdStr;
    @ApiModelProperty("销售箱规")
    private BigDecimal salesBoxCapacity;
    @ApiModelProperty("规格")
    private String commoditySpec;
    @ApiModelProperty("计量单位")
    private String commodityUnitName;
    @ApiModelProperty("原价")
    @JsonSerialize(using = DecimalSerializerKeep2.class)
    private BigDecimal commodityPrice;
    @ApiModelProperty(value ="是否有特价：0=无特价，1=普通特价")
    private Integer isSpecialPrice;
    @ApiModelProperty("特价")
    @JsonSerialize(using = DecimalSerializerKeep2.class)
    private BigDecimal specialPrice;

    @ApiModelProperty("预估到手价 -参加满折商品展示")
    @JsonSerialize(using = DecimalSerializerKeep2.class)
    private BigDecimal deliveryPrice;
    @ApiModelProperty( value = "特价限量",position = 11)
    private BigDecimal specialPriceLimit;
    @ApiModelProperty("商品数量")
    private BigDecimal quantity;
    @ApiModelProperty(value = "商品份数", hidden = true)
    private BigDecimal num;
    @ApiModelProperty("商品url")
    private String imageUrl;
    @ApiModelProperty("是否可继续添加-->用于是否置灰购物车内加号按钮的判断")
    private Boolean ableAdd;
    @ApiModelProperty("商品库存缺少修改提示")
    private String stockWarningTips;
    @ApiModelProperty("箱规不合法提示")
    private String boxWarningTips;
    @ApiModelProperty("是否速冻：0-否、1-是")
    private Integer isQuickFreeze;
    @ApiModelProperty("是否可订货")
    private Boolean isCanOrder;
    @ApiModelProperty("是否有限量：0=无，1=有")
    private Integer isLimit;
    @ApiModelProperty("商品限量值")
    private BigDecimal limitNumber;
    @ApiModelProperty("赠品限量值")
    private BigDecimal giftLimitNumber;
    @ApiModelProperty("是否凑整：0-否、1-是")
    private Integer isFreezeRoundingGroup;

    @ApiModelProperty(value ="凑整倍数")
    private Integer isFreezeRoundingGroupMultiple;

    @ApiModelProperty(value = "是否特惠：0=无，1=有", position = 17)
    private Integer isThPrice;

    @ApiModelProperty(value = "特惠商品限量值", position = 18)
    private BigDecimal thLimitNumber;

    @ApiModelProperty(value = "特惠价格", position = 19)
    @JsonSerialize(using = DecimalSerializerKeep2.class)
    private BigDecimal thPrice;

    private BigDecimal thFullPrice;

    private BigDecimal sumPrice;

    @ApiModelProperty(value ="最早可订货时间,订单使用", hidden = true)
    private Date beginDeliveryTime;
    @ApiModelProperty(value ="最晚可订货时间,订单使用", hidden = true)
    private Date endDeliveryTime;
    @ApiModelProperty(value = "库存限量开始时间",hidden = true)
    private Date limitStartTime;
    @ApiModelProperty(value = "库存限量结束时间",hidden = true)
    private Date limitEndTime;

    @ApiModelProperty("是否失效 0、否 1、是")
    private Boolean isInvalidate = Boolean.FALSE;

    @ApiModelProperty("是否为赠品 0、否 1、是")
    private Boolean isGift = Boolean.FALSE;

    private BigDecimal toDayLimitNumber;

    public BigDecimal getSpecialPrice(){
        return this.isSpecialPrice == 0 ? this.commodityPrice : this.specialPrice;
    }
    public BigDecimal getCommodityPrice(){
        if(null == this.commodityPrice){
            this.commodityPrice = BigDecimal.ZERO;
        }
        return commodityPrice;
    }
}

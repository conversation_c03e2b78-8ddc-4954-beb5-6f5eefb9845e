package com.pinshang.qingyun.order.model.commodity;

import lombok.NoArgsConstructor;

import javax.persistence.Entity;
import javax.persistence.Table;
import java.util.Date;

@Entity
@NoArgsConstructor
@Table(name="t_xs_shop_commodity_purchase_status")
public class CommodityPurchaseStatus {

    private Long id;
    //门店id
    private Long shopId;
    //商品id
    private Long commodityId;
    //商品是否可采状态
    private Integer commodityPurchaseStatus;
    //创建人
    private Long createId;
    //创建时间
    private Date createTime;
    //修改人
    private Long updateId;
    //修改时间
    private Date updateTime;

    /**门店id拼接商品id**/
    private String shopCommodity;

    public CommodityPurchaseStatus(Integer commodityPurchaseStatus,Long updateId,Date updateTime){
        this.commodityPurchaseStatus = commodityPurchaseStatus;
        this.updateId = updateId;
        this.updateTime = updateTime;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getShopId() {
        return shopId;
    }

    public void setShopId(Long shopId) {
        this.shopId = shopId;
    }

    public Long getCommodityId() {
        return commodityId;
    }

    public void setCommodityId(Long commodityId) {
        this.commodityId = commodityId;
    }

    public Integer getCommodityPurchaseStatus() {
        return commodityPurchaseStatus;
    }

    public void setCommodityPurchaseStatus(Integer commodityPurchaseStatus) {
        this.commodityPurchaseStatus = commodityPurchaseStatus;
    }

    public Long getCreateId() {
        return createId;
    }

    public void setCreateId(Long createId) {
        this.createId = createId;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Long getUpdateId() {
        return updateId;
    }

    public void setUpdateId(Long updateId) {
        this.updateId = updateId;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public String getShopCommodity() {
        return shopCommodity;
    }

    public void setShopCommodity(String shopCommodity) {
        this.shopCommodity = shopCommodity;
    }
}

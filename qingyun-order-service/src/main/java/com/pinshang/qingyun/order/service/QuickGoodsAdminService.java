package com.pinshang.qingyun.order.service;

import com.pinshang.qingyun.base.configure.expand.QYAssert;
import com.pinshang.qingyun.base.enums.IogisticsModelEnums;
import com.pinshang.qingyun.base.enums.shop.ShopTypeEnums;
import com.pinshang.qingyun.box.utils.BeanCloneUtils;
import com.pinshang.qingyun.order.dto.XDCommodityODTO;
import com.pinshang.qingyun.order.mapper.CommodityMapper;
import com.pinshang.qingyun.order.mapper.QuickGoodsMapper;
import com.pinshang.qingyun.order.mapper.ShopMapper;
import com.pinshang.qingyun.order.mapper.ShoppingCartMapper;
import com.pinshang.qingyun.order.mapper.entry.MdShopOrderSettingEntry;
import com.pinshang.qingyun.order.mapper.entry.order.CommodityInfoEntry;
import com.pinshang.qingyun.order.mapper.entry.order.DeliveryBatchEntry;
import com.pinshang.qingyun.order.model.order.QuickGoods;
import com.pinshang.qingyun.order.model.store.Store;
import com.pinshang.qingyun.order.vo.order.QuickGoodsVo;
import com.pinshang.qingyun.shop.dto.bigShop.StallCommodityQueryIDTO;
import com.pinshang.qingyun.shop.dto.bigShop.StallODTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @Author: sk
 * @Date: 2023/12/14
 */
@Slf4j
@Service
public class QuickGoodsAdminService {

    @Autowired
    private QuickGoodsMapper quickGoodsMapper;

    @Autowired
    private ShopMapper shopMapper;

    @Autowired
    private ShoppingCartMapper shoppingCartMapper;

    @Autowired
    private MdShopOrderSettingService mdShopOrderSettingService;

    @Autowired
    private CommodityMapper commodityMapper;
    @Lazy
    @Autowired
    private QuickGoodsService quickGoodsService;
    @Autowired
    private ConsignmentSupplierService consignmentSupplierService;

    @Transactional(rollbackFor = Exception.class)
    public Boolean saveQuickGoodsAdmin(QuickGoodsVo vo, Integer shopType, Map<String, XDCommodityODTO> commodityMap, List<DeliveryBatchEntry> deliveryBatchList,
                                       Map<String, StallODTO> stallCodeMap , Boolean isBigShop, Map<String, String> stallCommodityMap){
        Boolean isXd = shopType.equals(ShopTypeEnums.XD.getCode());
        Boolean isJm = shopType.equals(ShopTypeEnums.XSJM.getCode());
        List<String> commodityIdStrList = new ArrayList<>();
        List<Long> commodityIdList = new ArrayList<>();
        List<XDCommodityODTO> commodityList = new ArrayList<>();
        for(QuickGoodsVo.QuickGoodsItemVo dto : vo.getList()){
            XDCommodityODTO commodityODTO = BeanCloneUtils.copyTo(commodityMap.get(dto.getCommodityCode()), XDCommodityODTO.class);
            commodityIdList.add(Long.valueOf(commodityODTO.getCommodityId()));
            commodityIdStrList.add(commodityODTO.getCommodityId());
            commodityODTO.setStallCode(dto.getStallCode());
            commodityList.add(commodityODTO);
        }

        // 获取商品价格、状态
        List<CommodityInfoEntry> commodityInfoEntries = quickGoodsService.queryCommodityInfoByStoreId(commodityList, commodityIdList, vo.getStoreId(), vo.getShopId());
        Map<String, List<CommodityInfoEntry>> entryMap = new HashMap<>(commodityInfoEntries.size());
        if(isBigShop) {
             entryMap = commodityInfoEntries.stream().collect(Collectors.groupingBy(item -> {
                return item.getStallCode() + "_" + item.getCommodityCode();
            }));
        }else {
            entryMap = commodityInfoEntries.stream().collect(Collectors.groupingBy(item -> {
                return item.getCommodityCode();
            }));
        }

        List<QuickGoods> models = new ArrayList<>();
        if(null !=vo && null !=vo.getList() && !vo.getList().isEmpty()){
            List<MdShopOrderSettingEntry>  settingList = mdShopOrderSettingService.queryMdShopOrderSettingListByIds(vo.getStoreId(), commodityIdStrList);
            Map<Long, MdShopOrderSettingEntry> settingMap = settingList.stream().collect(Collectors.toMap(MdShopOrderSettingEntry::getCommodityId, Function.identity()));

            Date d =new Date();
            for(QuickGoodsVo.QuickGoodsItemVo dto:vo.getList()){
                StringBuilder remark = new StringBuilder();
                QuickGoods g = new QuickGoods();
                g.setPass(true);

                List<CommodityInfoEntry> list = new ArrayList<>();
                if(isBigShop) {
                    list = entryMap.get(dto.getStallCode() + "_" + dto.getCommodityCode());
                }else {
                    list = entryMap.get(dto.getCommodityCode());
                }
                if(CollectionUtils.isEmpty(list)) {
                    continue;
                }

                if(isBigShop) {
                    String key = vo.getShopId() + "" + dto.getStallCode() + "" + dto.getCommodityCode();
                    if(!stallCommodityMap.containsKey(key)){
                        remark.append("当前档口无此商品");
                        g.setPass(Boolean.FALSE);
                    }
                }


                CommodityInfoEntry t = list.get(0);
                MdShopOrderSettingEntry mdShopOrderSettingEntry = settingMap.get(t.getId());
                if(mdShopOrderSettingEntry == null){
                    remark.append("门店订货通用设置不存在");
                    g.setPass(Boolean.FALSE);
                }else {
                    // 采用门店订货通用设置里面的供应商仓库信息
                    g.setLogisticsModel(mdShopOrderSettingEntry.getLogisticsModel());
                    t.setLogisticsModel(g.getLogisticsModel());
                    t.setSupplierId(Long.valueOf(mdShopOrderSettingEntry.getSupplierId()));
                    t.setSupplierStartTime(mdShopOrderSettingEntry.getDefaultSupplierBeginTime());
                    t.setSupplierEndTime(mdShopOrderSettingEntry.getDefaultSupplierEndTime());
                    if(mdShopOrderSettingEntry.getWarehouseId() != null) {
                        t.setWarehouseId(Long.valueOf(mdShopOrderSettingEntry.getWarehouseId()));
                        t.setWorkBeginTime(mdShopOrderSettingEntry.getDefaultWarehouseBeginTime());
                        t.setWorkEndTime(mdShopOrderSettingEntry.getDefaultWarehouseEndTime());
                    }else {
                        // 仓库为空，更新仓库信息
                        mdShopOrderSettingService.updateShopOrderSettingWarehouse(t.getId());
                    }

                }

                if(vo.getIfAdmin()){
                    g.setOrderTime(dto.getOrderTime());
                    g.setDeliveryBatch(dto.getDeliveryBatch());
                    if(dto.getConsignmentCommodity() != null && dto.getConsignmentCommodity()){
                        // 总部代理订货可以导入直送商品(代销香烟)
                    }else {
                        if(mdShopOrderSettingEntry != null && mdShopOrderSettingEntry.getLogisticsModel().equals(IogisticsModelEnums.DIRECT_SENDING.getCode())){
                            remark.append("直送商品");
                            g.setPass(Boolean.FALSE);
                        }
                    }

                }

                // 鲜食加盟的不允许导入直送商品
                if(isJm){
                    if(mdShopOrderSettingEntry != null && mdShopOrderSettingEntry.getLogisticsModel().equals(IogisticsModelEnums.DIRECT_SENDING.getCode())){
                        remark.append("直送商品");
                        g.setPass(Boolean.FALSE);
                    }
                }

                g.setCommodityId(t.getId());
                g.setStoreId(vo.getStoreId());
                // 数量先保留两位小数
                dto.setQuantity(dto.getQuantity().setScale(2, BigDecimal.ROUND_HALF_UP));
                if(isXd){
                    int num = dto.getQuantity().intValue();

                    // 代理订货不校验小数
                    if(!vo.getIfAdmin()){
                        if(BigDecimal.valueOf(num).compareTo(dto.getQuantity()) < 0){
                            remark.append("不是整数");
                            g.setPass(Boolean.FALSE);
                        }
                    }

                    BigDecimal commodityPackageSpec = BigDecimal.ONE;
                    XDCommodityODTO xdCommodityPackageSpec = commodityMap.get(dto.getCommodityCode());
                    if(xdCommodityPackageSpec != null && xdCommodityPackageSpec.getCommodityPackageSpec() != null){
                        commodityPackageSpec = commodityMap.get(dto.getCommodityCode()).getCommodityPackageSpec();
                    }
                    // 代理快速订货，按照数量导入
                    if(vo.getIfAdmin()){
                        g.setQuantity(dto.getQuantity());
                    }else {
                        g.setQuantity(dto.getQuantity().multiply(commodityPackageSpec));
                    }

                }else {
                    g.setQuantity(dto.getQuantity());
                }
                g.setSupplierId(t.getSupplierId());
                g.setWarehouseId(t.getWarehouseId());
                g.setCreateTime(d);
                //检查客户价格方案->检查物流模式->检查供应商供货时间->检查仓库或供应商;

                XDCommodityODTO fpe = commodityMap.get(dto.getCommodityCode());
                // FrozenProductEntry fpe = this.quickGoodsMapper.findFrozenProductListByProductCode(dto.getCommodityCode());
                if(null != fpe){
                    if(null != fpe.getSalesBoxCapacity() && fpe.getSalesBoxCapacity().compareTo(BigDecimal.ZERO) > 0){
                        log.info(dto.getCommodityCode()+"-"+g.getQuantity());
                        BigDecimal salesBoxCapacity = fpe.getSalesBoxCapacity();
                        if(isXd || isBigShop){
                            salesBoxCapacity = fpe.getXdSalesBoxCapacity();
                        }
                        if(null == salesBoxCapacity || salesBoxCapacity.compareTo(BigDecimal.ZERO) == 0){
                            remark.append("箱规不存在或者为0");
                            g.setPass(Boolean.FALSE);
                        }else {
                            if(g.getQuantity().divideAndRemainder(salesBoxCapacity)[1].compareTo(BigDecimal.ZERO) != 0){
                                remark.append("不是箱规的倍数");
                                g.setPass(Boolean.FALSE);
                            }
                        }
                    }
                }
                // 代理订货，不判断商品的可售状态（即停售的商品，也能下单）
                if(!vo.getIfAdmin()){
                    if(null != t.getCommodityStatus() && t.getCommodityStatus().intValue() == 0){
                        remark.append("该商品已被禁用");
                        g.setPass(Boolean.FALSE);
                    }
                }

                if(null ==t.getCommodityId()){
                    remark.append("不在价格方案中,");
                    g.setPass(Boolean.FALSE);
                }
                //检查物流模式
                if(null ==t.getLogisticsModel()){
                    remark.append("没有设置商品物流模式,");
                    g.setPass(Boolean.FALSE);
                }
                if (null == t.getCommodityPurchaseStatus() || t.getCommodityPurchaseStatus() == 0) {
                    remark.append("该商品不可采,");
                    g.setPass(Boolean.FALSE);
                }
                if (null == t.getPrice() || BigDecimal.ZERO.equals(t.getPrice())) {
                    remark.append("不在价格方案中,");
                    g.setPass(Boolean.FALSE);
                }

                //检查仓库或供应商;
                if(null !=t.getLogisticsModel()){
                    if(t.getLogisticsModel().intValue()== IogisticsModelEnums.DIRECT_CONNECTION.getCode()){
                        if(null ==t.getSupplierId()){
                            g.setPass(Boolean.FALSE);
                            remark.append("默认供应商未设置");
                        }
                        if(null ==t.getWarehouseId()){
                            g.setPass(Boolean.FALSE);
                            remark.append("默认仓库未设置");
                        }
                    }else if(t.getLogisticsModel().intValue()== IogisticsModelEnums.DIRECT_SENDING.getCode()){
                        if(null ==t.getSupplierId()){
                            g.setPass(Boolean.FALSE);
                            remark.append("默认供应商未设置");
                        }
                    }else if(t.getLogisticsModel().intValue()== IogisticsModelEnums.DISPATCHING.getCode()){
                        if(null ==t.getSupplierId()){
                            g.setPass(Boolean.FALSE);
                            remark.append("默认供应商未设置");
                        }
                        if(null ==t.getWarehouseId()){
                            g.setPass(Boolean.FALSE);
                            remark.append("默认仓库未设置");
                        }
                    }
                }

                g.setRemark(remark.toString());
                g.setCreateId(vo.getCreateId());
                g.setConsignmentId(-1L);
                StallODTO stallODTO = stallCodeMap.get(dto.getStallCode());
                g.setStallId(stallODTO != null ? stallODTO.getId() : -1L);
                models.add(g);
            }
            quickGoodsMapper.insertListAdmin(models);
        }
        return true;
    }
}

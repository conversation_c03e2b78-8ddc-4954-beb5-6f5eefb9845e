package com.pinshang.qingyun.order.dto.sync;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2024/7/26 13:57
 */
@Data
public class SyncOrderODTO {
    private Long orderId;

    private String orderCode;

    /**订单时间*/
    private Date orderTime;

    /**订单类型*/
    private Integer orderType;

    /**应付金额（参与促销活动之前的源始金额）*/
    private BigDecimal totalAmount;

    /**最终金额*/
    private BigDecimal finalAmount;

    /**订单金额*/
    private BigDecimal orderAmount;

    /**订单运费金额（免运费为0）*/
    private BigDecimal freightAmount;

    private List<SyncOrderListODTO> syncOrderListODTOList;
}

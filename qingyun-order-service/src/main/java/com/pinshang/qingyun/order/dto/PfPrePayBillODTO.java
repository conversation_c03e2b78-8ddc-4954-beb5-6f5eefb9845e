package com.pinshang.qingyun.order.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Map;

/**
 * 待支付单
 * <AUTHOR>
 * @Date 2019/11/24 20:19
 */
@Data
@ApiModel
@NoArgsConstructor
@AllArgsConstructor
public class PfPrePayBillODTO {

    @ApiModelProperty("支付单id")
    private String payBillId;

    private String orderCode;
    @ApiModelProperty("支付宝预支付订单")
    private String alipayPrePayBill;
    @ApiModelProperty("微信预支付订单")
    private Map<String, String> wechatPrePayBill;
    @ApiModelProperty("云闪付")
    private Map<String, String> unionPayPrePayBill;
    @ApiModelProperty("聚合支付: 支付宝预支付订单")
    private Map<String, String> unionAlipayPrePayBill;
    @ApiModelProperty("聚合支付: 微信预支付订单")
    private Map<String, String> unionWechatPrePayBill;
    @ApiModelProperty("聚合支付: 云闪付与支付订单")
    private Map<String, String> unionPayAggregationPrePayBill;
}

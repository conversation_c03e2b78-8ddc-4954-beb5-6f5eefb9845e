package com.pinshang.qingyun.order.vo.recharge;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.pinshang.qingyun.base.configure.expand.QYAssert;
import com.pinshang.qingyun.box.utils.SpringUtil;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class PfRechargeVo {

    @ApiModelProperty("充值金额")
    private BigDecimal payAmount;

    @ApiModelProperty("支付方式：1＝支付宝，2＝微信，4=云闪付支付，7-聚合支付宝 ，8-聚合微信，11-云闪付聚合支付")
    private Integer payType;

    @ApiModelProperty("微信支付时需要")
    private String clientIp;

    @ApiModelProperty("如果为小程序下单,需传入jsCode")
    private String jsCode;

    /** 客户id **/
    @JsonIgnore
    private Long storeId;
    @JsonIgnore
    private Long userId;

    /**
     * 验证充值金额必须大于等于配置金额
     * 验证充值金额必须是正整数
     * @param confPayAmount
     */
    public void checkPayAmount(String confPayAmount){
        QYAssert.notNull( this.payAmount,"请输入充值金额");
        QYAssert.isTrue(SpringUtil.hasTextAndNotNull( confPayAmount ),"请配置最小充值金额");
        int confPayAmountInt =  Integer.valueOf( confPayAmount );
        int payAmountInt = this.payAmount.intValue();
        QYAssert.isTrue(payAmountInt>= confPayAmountInt ,"充值金额必须大于等于"+confPayAmountInt);
        QYAssert.isTrue(this.payAmount.compareTo( new BigDecimal( payAmountInt))==0 ,"充值金额必须是正整数");
    }

}

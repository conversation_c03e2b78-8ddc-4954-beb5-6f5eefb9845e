package com.pinshang.qingyun.order.model.order;

import com.pinshang.qingyun.base.po.BaseEnterprisePO;
import lombok.Data;

import javax.persistence.Table;
import java.util.Date;

/**
 * @description t_consignment_sale_return_order
 * <AUTHOR>
 * @date 2024-06-04
 */
@Data
@Table(name="t_consignment_sale_return_order")
public class ConsignmentSaleReturnOrder extends BaseEnterprisePO {

    /**
     * 代销供应商ID
     */
    private Long supplierId;

    /**
     * 客户ID
     */
    private Long storeId;

    /**
     * 退货单号
     */
    private String orderCode;

    /**
     * 0＝取消，1＝待确认，2＝待审核，3＝已驳回，4＝已完成
     */
    private Integer status;

    /**
     * remark
     */
    private String remark;

    /**
     * 确认人
     */
    private Long confirmId;

    /**
     * 确认时间
     */
    private Date confirmTime;

    /**
     * 审核人
     */
    private Long checkUserId;

    /**
     * 审核时间
     */
    private Date checkTime;


}
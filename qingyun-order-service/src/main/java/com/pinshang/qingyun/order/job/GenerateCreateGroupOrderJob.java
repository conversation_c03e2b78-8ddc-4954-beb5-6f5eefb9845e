/*
package com.pinshang.qingyun.order.job;

import com.pinshang.qingyun.order.service.GrouponOrderService;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.concurrent.TimeUnit;

*/
/**
 * @Author: sk
 * @Date: 2020/12/18
 *//*

@Component
@Slf4j
public class GenerateCreateGroupOrderJob {

    @Autowired
    private RedissonClient redissonClient;

    @Autowired
    private GrouponOrderService grouponOrderService;

    @Value("${config.autoCreateOrder:0}")
    private int autoCreateOrder;

    */
/**
     * 每5分钟执行一次
     * 自动提交团购结束日期为当天并且已经结束的团购单(团购数量 purchased_number > 0 )
     *//*

    @Scheduled(cron ="0 0/5 * * * ?")
    public Boolean scheduleJobs() throws Throwable{
        // 为了区分环境,0不种植任务  1 种植任务
        if(autoCreateOrder == 0){
            return false;
        }

        String cacheKey = "group_order_lock";
        RLock lock = redissonClient.getLock(cacheKey);

        lock.lock(2L, TimeUnit.MINUTES);
        try {
            log.info("开始----------------社区团提交订单+锁-------" + new Date());
            grouponOrderService.createGrouponOrder();

        } finally {
            log.info("结束----------------社区团提交订单释放锁-------" + new Date());
            lock.unlock();
        }
        return true;
    }
}
*/

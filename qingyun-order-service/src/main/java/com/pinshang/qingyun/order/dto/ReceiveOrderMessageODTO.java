package com.pinshang.qingyun.order.dto;

import com.pinshang.qingyun.box.utils.DateUtil;
import com.pinshang.qingyun.order.enums.ReceiveOrderBusinessTypeEnums;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * @Author: sk
 * @Date: 2021/6/21
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ReceiveOrderMessageODTO {
    @ApiModelProperty("门店id")
    private Long shopId;
    @ApiModelProperty("门店编码")
    private String shopCode;
    @ApiModelProperty("门店名称")
    private String shopName;

    @ApiModelProperty("职员姓名")
    private String employeeName;
    @ApiModelProperty("职员电话")
    private String employeePhone;

    @ApiModelProperty("业务类型 1 自动收货后  2 手动收货前")
    private Integer businessType;

    @ApiModelProperty("状态 1 成功  0 失败")
    private Integer status;

    @ApiModelProperty("发送时间")
    private Date createTime;

    @ApiModelProperty("发送内容")
    private String content;

    public String getBusinessTypeName(){
        if(businessType != null){
            return ReceiveOrderBusinessTypeEnums.getName(businessType);
        }else {
            return "";
        }
    }

    public String getCreateTimeStr(){
        if(createTime != null){
            return DateUtil.getDateFormate(createTime,"yyyy-MM-dd HH:mm:ss");
        }else {
            return "";
        }
    }
}

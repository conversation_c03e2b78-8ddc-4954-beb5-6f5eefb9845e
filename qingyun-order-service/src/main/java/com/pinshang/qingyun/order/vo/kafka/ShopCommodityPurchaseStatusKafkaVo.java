package com.pinshang.qingyun.order.vo.kafka;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@NoArgsConstructor
public class ShopCommodityPurchaseStatusKafkaVo {

    private List<Long> shopIdList;

    private List<Long> commodityIdList;

    private Integer purchaseStatus;

    public ShopCommodityPurchaseStatusKafkaVo(List<Long> shopIdList,List<Long> commodityIdList,Integer purchaseStatus){
        this.shopIdList = shopIdList;
        this.commodityIdList = commodityIdList;
        this.purchaseStatus = purchaseStatus;
    }
}

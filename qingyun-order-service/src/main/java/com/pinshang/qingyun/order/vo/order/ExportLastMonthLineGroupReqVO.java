package com.pinshang.qingyun.order.vo.order;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2021/05/18 10:33
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ExportLastMonthLineGroupReqVO {
    @JsonFormat(pattern = "yyyy-MM-dd")
    private String startOrderTime;
    @JsonFormat(pattern = "yyyy-MM-dd")
    private String endOrderTime;
}

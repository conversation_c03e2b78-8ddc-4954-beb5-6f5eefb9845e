package com.pinshang.qingyun.order.dto;

import com.pinshang.qingyun.renderer.annotation.FieldRender;
import com.pinshang.qingyun.renderer.constant.RenderFieldHelper;
import com.pinshang.qingyun.renderer.enums.FieldTypeEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * @Author: sk
 * @Date: 2021/12/28
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class GroupOrderODTO  {


    @ApiModelProperty("团购活动编码")
    private String grouponCode;
    @ApiModelProperty("团购时间")
    private String grouponTime;

    @ApiModelProperty("门店编码")
    private String shopCode;
    @ApiModelProperty("门店名称")
    private String shopName;


    @ApiModelProperty("商品id")
    private String commodityId;
    @ApiModelProperty("商品编码")
    private String commodityCode;
    @ApiModelProperty("商品名称")
    private String commodityName;
    @ApiModelProperty("规格")
    private String commoditySpec; // 型号规格
    private String barCode;
    private String barCodes;
    private Long commodityUnitId; // 单位id
    @FieldRender(fieldType = FieldTypeEnum.DICTIONARY,fieldName = RenderFieldHelper.Dictionary.optionName,keyName = "commodityUnitId")
    private String commodityUnitName;

    @ApiModelProperty("团购数量")
    private BigDecimal grouponQuantity;
    @ApiModelProperty("团购金额")
    private BigDecimal grouponAmount;
    @ApiModelProperty("订单数量")
    private BigDecimal orderQuantity;
    @ApiModelProperty("订单金额")
    private BigDecimal orderAmount;
}

package com.pinshang.qingyun.order.vo.shop;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;
import java.util.List;

/**
 * 商品组配送统计已生成采购单的预订单
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class TjPreOrderVO {

    @ApiModelProperty("送货日期")
    private Date orderTime;

    @ApiModelProperty("客户ID")
    private List<Long> storeIds;

    @ApiModelProperty("商品ID")
    private List<Long> commodityIds;
}

/*
 * tramy.cn Inc.
 * Copyright (c) 2021-2024 All Rights Reserved.
 */
package com.pinshang.qingyun.order.dto.xda.tda;

import com.pinshang.qingyun.base.page.Pagination;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <p>
 * 退货单查询
 * </p>
 *
 * <AUTHOR> shenyang
 * @version : 1.0.0
 * @history : modify history
 * <author>              <time>              <version>              <desc>
 * @since : 2024/05/11 10:09
 */
@Data
public class ReturnOrderSearchIDTO extends Pagination {
    /**
     * 退货订单类型：1-退货，2-差异（少货/多货）
     */
    @ApiModelProperty(value = "退货订单类型：1-退货，2-差异（少货/多货）")
    private Integer returnOrderType;

    /**
     * 创建日期
     */
    @ApiModelProperty(value = "创建日期开始时间")
    private String createBeginTime;

    /**
     * 创建日期
     */
    @ApiModelProperty(value = "创建日期结束时间")
    private String createEndTime;

    /**
     * 送货日期
     */
    @ApiModelProperty(value = "送货日期开始时间")
    private String deliveryBeginTime;

    /**
     * 送货日期
     */
    @ApiModelProperty(value = "送货日期结束时间")
    private String deliveryEndTime;

    /**
     * 退货单状态：1-待审核、2-待取货、3-取货失败、4-待大仓确认、5-已取消、6-已完成、7-审核不通过
     */
    @ApiModelProperty(value = "退货单状态：1-待审核、2-待取货、3-取货失败、4-待大仓确认、5-已取消、6-已完成、7-审核不通过")
    private Integer status;

    /**
     * 退货人手机号
     */
    @ApiModelProperty(value = "退货人手机号")
    private String phone;

    /**
     * 退货类型：1-客户退货，2-订单取消，3-配送失败
     */
    @ApiModelProperty(value = "退货类型：1-客户退货，2-订单取消，3-配送失败")
    private Integer returnType;

    /**
     * 退货单序列号，前端展示用
     */
    @ApiModelProperty(value = "退货单序列号，前端展示用")
    private String returnOrderSeq;

    /**
     * 来源单号，即相关的订单编号
     */
    @ApiModelProperty(value = "来源单号，即相关的订单编号")
    private String sourceOrderCode;

    /**
     * 物流中心id,pinshang_tms.t_tms_logistics_center.id
     */
    @ApiModelProperty(value = "物流中心id")
    private Long logisticsCenterId;

    /**
     * 配送批次
     */
    @ApiModelProperty(value = "配送批次")
    private String deliveryBatch;

    /**
     * 客户Id
     */
    @ApiModelProperty(value = "客户Id")
    private Long storeId;

    @ApiModelProperty(value = "运单号")
    private String waybillCode;

}

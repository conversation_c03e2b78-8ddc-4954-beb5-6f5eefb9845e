package com.pinshang.qingyun.order.dto;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.pinshang.qingyun.base.enums.StoreTypeEnums;
import com.pinshang.qingyun.base.enums.order.OrderTypeEnum;
import com.pinshang.qingyun.box.utils.DateUtil;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2024/03/13
 * @Version 1.0
 */
@Data
public class BStockShortPageODTO {
    @ExcelIgnore
    private Long id;
    @ExcelIgnore
    private Long storeId;
    @ExcelProperty(value = "客户编码")
    private String storeCode;
    @ExcelProperty(value = "客户名称")
    private String storeName;
    @ExcelIgnore
    private Long storeTypeId;
    @ExcelProperty(value = "客户类型")
    private String storeTypeName;
    @ExcelIgnore
    private Long commodityId;
    @ExcelProperty(value = "商品编码")
    private String commodityCode;
    @ExcelProperty(value = "商品名称")
    private String commodityName;
    @ExcelProperty(value = "规格")
    private String commoditySpec;
    @ExcelIgnore
    private BigDecimal commodityPackageSpec;
    @ExcelProperty(value = "箱规")
    private BigDecimal salesBoxCapacity;
    @ExcelProperty(value = "计量单位")
    private String commodityUnitName;
    @ExcelIgnore
    private Integer stockType;
    @ExcelProperty(value = "商品库存依据")
    private String stockTypeName;
    @ExcelProperty(value = "购物车数量")
    private BigDecimal needQuantity;
    @ExcelProperty(value = "库存余量")
    private BigDecimal stockQuantity;
    @ExcelProperty(value = "时间点")
    private String createTimeStr;
    @ExcelIgnore
    private Integer orderType;
    @ExcelProperty(value = "订单入口")
    private String orderTypeName;
    @ExcelIgnore
    private String remark;
    @ExcelIgnore
    private Long createId;
    @ExcelIgnore
    private Date createTime;

    public String getStoreTypeName() {
        return StoreTypeEnums.getById(this.storeTypeId).getName();
    }

    public String getStockTypeName() {
        switch (this.stockType){
            case 1:
                return "依据大仓库存";
            case 2:
                return "不限量供应";
            case 3:
                return "限量供应";
            default:
                return "";
        }
    }

    public String getOrderTypeName() {
        return OrderTypeEnum.getName(this.orderType);
    }

    public String getCreateTimeStr() {
        return DateUtil.get4yMdHms(this.createTime);
    }
}

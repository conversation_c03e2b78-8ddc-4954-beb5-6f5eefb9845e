package com.pinshang.qingyun.order.model.order;

import com.pinshang.qingyun.base.po.BaseIDPO;

import javax.persistence.Table;
import java.util.Date;
@Table(name="t_employee_user")
public class User extends BaseIDPO{
    /**
     *
     */
    @Deprecated
    private Long id;
    private Long userId;

    /**
     * 所属企业ID
     */
    private Long enterpriseId;

    /**
     * 电子邮箱
     */
    private String email;

    /**
     * 手机
     */
    private String mobile;

    /**
     * 密码
     */
    private String password;

    /**
     * 真实姓名
     */
    @Deprecated
    private String realName;
    private String employeeName;

    /**
     * 工号
     */
    private String employeeNumber;
    private String employeeCode;

    /**
     * 是否删除
     */
    private Boolean isDeleted;

    /**
     * 电话
     */
    private String telephone;

    /**
     * 最近登录时间
     */
    private Date lastLoginTime;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 修改时间
     */
    private Date updateTime;

    /**
     * 创建人(前台用户)
     */
    private Long createrId;

    /**
     * 创建人（后台用户）--内置用户由后台创建
     */
    private Long backCreaterId;

    /**
     * 是否特权用户
     */
    private Boolean isPrerogative;

    /**
     * 是否内置用户
     */
    private Boolean isInternal;

    /**
     *
     */
    private String weixinOpenId;

    /**
     *
     * @return id
     */
    @Override
    public Long getId() {
        return id;
    }

    /**
     *
     * @param id
     */
    @Override
    public void setId(Long id) {
        this.id = id;
    }

    /**
     * 所属企业ID
     * @return enterprise_id 所属企业ID
     */
    public Long getEnterpriseId() {
        return enterpriseId;
    }

    /**
     * 所属企业ID
     * @param enterpriseId 所属企业ID
     */
    public void setEnterpriseId(Long enterpriseId) {
        this.enterpriseId = enterpriseId;
    }

    /**
     * 电子邮箱
     * @return email 电子邮箱
     */
    public String getEmail() {
        return email;
    }

    /**
     * 电子邮箱
     * @param email 电子邮箱
     */
    public void setEmail(String email) {
        this.email = email == null ? null : email.trim();
    }

    /**
     * 手机
     * @return mobile 手机
     */
    public String getMobile() {
        return mobile;
    }

    /**
     * 手机
     * @param mobile 手机
     */
    public void setMobile(String mobile) {
        this.mobile = mobile == null ? null : mobile.trim();
    }

    /**
     * 密码
     * @return password 密码
     */
    public String getPassword() {
        return password;
    }

    /**
     * 密码
     * @param password 密码
     */
    public void setPassword(String password) {
        this.password = password == null ? null : password.trim();
    }

    /**
     * 真实姓名
     * @return real_name 真实姓名
     */
    public String getRealName() {
        return realName;
    }

    /**
     * 真实姓名
     * @param realName 真实姓名
     */
    public void setRealName(String realName) {
        this.realName = realName == null ? null : realName.trim();
    }

    /**
     * 工号
     * @return employee_number 工号
     */
    public String getEmployeeNumber() {
        return employeeNumber;
    }

    /**
     * 工号
     * @param employeeNumber 工号
     */
    public void setEmployeeNumber(String employeeNumber) {
        this.employeeNumber = employeeNumber == null ? null : employeeNumber.trim();
    }

    /**
     * 是否删除
     * @return is_deleted 是否删除
     */
    public Boolean getIsDeleted() {
        return isDeleted;
    }

    /**
     * 是否删除
     * @param isDeleted 是否删除
     */
    public void setIsDeleted(Boolean isDeleted) {
        this.isDeleted = isDeleted;
    }

    /**
     * 电话
     * @return telephone 电话
     */
    public String getTelephone() {
        return telephone;
    }

    /**
     * 电话
     * @param telephone 电话
     */
    public void setTelephone(String telephone) {
        this.telephone = telephone == null ? null : telephone.trim();
    }

    /**
     * 最近登录时间
     * @return last_login_time 最近登录时间
     */
    public Date getLastLoginTime() {
        return lastLoginTime;
    }

    /**
     * 最近登录时间
     * @param lastLoginTime 最近登录时间
     */
    public void setLastLoginTime(Date lastLoginTime) {
        this.lastLoginTime = lastLoginTime;
    }

    /**
     * 创建时间
     * @return create_time 创建时间
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     * 创建时间
     * @param createTime 创建时间
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * 修改时间
     * @return update_time 修改时间
     */
    public Date getUpdateTime() {
        return updateTime;
    }

    /**
     * 修改时间
     * @param updateTime 修改时间
     */
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    /**
     * 创建人(前台用户)
     * @return creater_id 创建人(前台用户)
     */
    public Long getCreaterId() {
        return createrId;
    }

    /**
     * 创建人(前台用户)
     * @param createrId 创建人(前台用户)
     */
    public void setCreaterId(Long createrId) {
        this.createrId = createrId;
    }

    /**
     * 创建人（后台用户）--内置用户由后台创建
     * @return back_creater_id 创建人（后台用户）--内置用户由后台创建
     */
    public Long getBackCreaterId() {
        return backCreaterId;
    }

    /**
     * 创建人（后台用户）--内置用户由后台创建
     * @param backCreaterId 创建人（后台用户）--内置用户由后台创建
     */
    public void setBackCreaterId(Long backCreaterId) {
        this.backCreaterId = backCreaterId;
    }

    /**
     * 是否特权用户
     * @return is_prerogative 是否特权用户
     */
    public Boolean getIsPrerogative() {
        return isPrerogative;
    }

    /**
     * 是否特权用户
     * @param isPrerogative 是否特权用户
     */
    public void setIsPrerogative(Boolean isPrerogative) {
        this.isPrerogative = isPrerogative;
    }

    /**
     * 是否内置用户
     * @return is_internal 是否内置用户
     */
    public Boolean getIsInternal() {
        return isInternal;
    }

    /**
     * 是否内置用户
     * @param isInternal 是否内置用户
     */
    public void setIsInternal(Boolean isInternal) {
        this.isInternal = isInternal;
    }

    /**
     *
     * @return weixin_open_id
     */
    public String getWeixinOpenId() {
        return weixinOpenId;
    }

    /**
     *
     * @param weixinOpenId
     */
    public void setWeixinOpenId(String weixinOpenId) {
        this.weixinOpenId = weixinOpenId == null ? null : weixinOpenId.trim();
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public String getEmployeeName() {
        return employeeName;
    }

    public void setEmployeeName(String employeeName) {
        this.employeeName = employeeName;
    }

    public String getEmployeeCode() {
        return employeeCode;
    }

    public void setEmployeeCode(String employeeCode) {
        this.employeeCode = employeeCode;
    }
}
package com.pinshang.qingyun.order.dto.xda;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @Version 0.1.0
 * @Date 2021/1/21 15:33
 * @Copyright © 2019-2021 qm
 * @Description -- 类说明 取消投诉单
 **/
@Data
public class XdaComplaintOrderCancelDTO {
    @ApiModelProperty(value = "操作类型，true-取消投诉单下的所有投诉商品，false-取消指定的投诉商品", example = "false", required = true)
    private Boolean operationType;

    @ApiModelProperty(value = "投诉单Id", example = "123456code",position = 1,  required = true)
    private String complainId;

    @ApiModelProperty(value = "商品id", example = "123456", position = 2, required = true)
    private Long commodityId;

    @ApiModelProperty(value = "商品单价", example = "56.36",position = 3, required = true)
    private BigDecimal commodityPrice;

    @ApiModelProperty(value = "客户差异投诉数量/客户退货数量", example = "45.47", position = 4, required = true)
    private BigDecimal realReturnQuantity;

    @ApiModelProperty(value = "客户id", example = "8562", position = 5, required = true, hidden = true)
    private Long storeId;

    @ApiModelProperty("实发数量")
    private BigDecimal realDeliveryQuantity;

}

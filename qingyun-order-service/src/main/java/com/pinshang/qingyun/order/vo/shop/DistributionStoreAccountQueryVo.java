package com.pinshang.qingyun.order.vo.shop;

import com.pinshang.qingyun.base.page.Pagination;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2018/8/7 17:49
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class DistributionStoreAccountQueryVo extends Pagination {
    private String vagueStr;
    private Long storeTypeId;
    private List<Long> storeIds;
}

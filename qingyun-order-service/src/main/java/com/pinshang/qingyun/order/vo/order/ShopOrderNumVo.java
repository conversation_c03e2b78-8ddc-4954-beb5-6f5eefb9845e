package com.pinshang.qingyun.order.vo.order;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ShopOrderNumVo {

    private Long shopId;

    @ApiModelProperty("门店名称")
    private String shopName;

    @ApiModelProperty("该门店送货数量")
    private BigDecimal reserveNum;

    @ApiModelProperty("已清点数量")
    private BigDecimal countNum;
}

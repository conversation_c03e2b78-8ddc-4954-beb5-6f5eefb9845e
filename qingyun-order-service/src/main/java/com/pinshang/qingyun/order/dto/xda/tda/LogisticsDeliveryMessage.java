package com.pinshang.qingyun.order.dto.xda.tda;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 物流信息
 *
 * <AUTHOR>
 */
@Data
public class LogisticsDeliveryMessage {
    /**
     * 退货单id
     */
    private Long returnOrderId;

    /**
     * 业务类型：10-通达销售
     */
    private Integer businessType;

    /**
     * 运单号
     */
    private String waybillCode;

    /**
     * 司机ID
     */
    private Long driverId;

    /**
     * 送货日期
     */
    private Date deliveryDate;

    /**
     * 配送批次
     */
    private Integer deliveryBatch;

    /**
     * 客户类型ID
     */
    private Long storeTypeId;

    /**
     * 客户ID
     */
    private Long storeId;

    /**
     * 客户手机号
     */
    private String phone;

    /**
     * 物流中心ID
     */
    private Long logisticsCenterId;

    /**
     * 收货地址
     */
    private String deliveryAddress;

    /**
     * 订单编码
     */
    private String orderCode;

    /**
     * 订单号
     */
    private Long orderId;

    /**
     * 运单的关联单类型：11-订单、21-退货单
     */
    private Integer refType;

    /**
     * 物流流程状态
     */
    private Integer processStatus;

    /**
     * 订单金额
     */
    private BigDecimal orderAmount;

    /**
     * 物流商品信息
     */
    private List<LogisticsCommodityItem> complaintItemList;
}

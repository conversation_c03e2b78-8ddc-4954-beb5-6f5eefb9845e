package com.pinshang.qingyun.order.model.gift;

import javax.persistence.Entity;
import javax.persistence.Table;

import com.pinshang.qingyun.base.po.BaseIDPO;

/**
 * 促销配货管理-赠品方案-适用范围
 **/
@Entity
@Table(name = "t_gift_model_scope")
public class GiftModelScope extends BaseIDPO {
	/**t_gift_model 表中的主键id,该作用范围属于哪一个方案 **/
    private Long giftModelId;
	/**促销配货管理-适用范围类型.1结账客户 2产品价格方案3门店 **/
    private Integer scopeType;
	/**适用范围的id,比如渠道表示的是渠道ID,店铺即是店铺ID **/
    private Long typeId;
	/**适用范围状态,0表示正常使用,1表示禁用 **/
    private Integer status;
    private Long createId;
    
	public Long getGiftModelId(){
        return giftModelId;
    }
    public void setGiftModelId(Long giftModelId){
        this.giftModelId = giftModelId;
    }
    public Integer getScopeType(){
        return scopeType;
    }
    public void setScopeType(Integer scopeType){
        this.scopeType = scopeType;
    }
    public Long getTypeId(){
        return typeId;
    }
    public void setTypeId(Long typeId){
        this.typeId = typeId;
    }
    public Integer getStatus(){
        return status;
    }
    public void setStatus(Integer status){
        this.status = status;
    }
    public Long getCreateId(){
        return createId;
    }
    public void setCreateId(Long createId){
        this.createId = createId;
    }
}
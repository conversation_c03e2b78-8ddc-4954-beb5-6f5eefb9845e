package com.pinshang.qingyun.order.model.promotionSale;

import javax.persistence.Entity;
import javax.persistence.Table;
import javax.persistence.Transient;

import com.pinshang.qingyun.base.po.BaseIDPO;

import java.math.BigDecimal;

/**
 * 配比方案商品相应的设定表
 **/
@Entity
@Table(name = "t_promotion_sale_comm")
public class PromotionSaleComm extends BaseIDPO {
	/**优惠ID **/
    private Long promotionId;
	/**商品Id **/
    private Long commId;
	/**配比 比率 **/
    private Long sale;
	/**限量数量 **/
    private Long limes;
	/**创建用户Id **/
    private Long createUserId;

    @Transient
    private BigDecimal commodityPrice;

    public BigDecimal getCommodityPrice() {
        return commodityPrice;
    }

    public void setCommodityPrice(BigDecimal commodityPrice) {
        this.commodityPrice = commodityPrice;
    }

    public Long getPromotionId(){
        return promotionId;
    }
    public void setPromotionId(Long promotionId){
        this.promotionId = promotionId;
    }
    public Long getCommId(){
        return commId;
    }
    public void setCommId(Long commId){
        this.commId = commId;
    }
    public Long getSale(){
        return sale;
    }
    public void setSale(Long sale){
        this.sale = sale;
    }
    public Long getLimes(){
        return limes;
    }
    public void setLimes(Long limes){
        this.limes = limes;
    }
    public Long getCreateUserId(){
        return createUserId;
    }
    public void setCreateUserId(Long createUserId){
        this.createUserId = createUserId;
    }
}
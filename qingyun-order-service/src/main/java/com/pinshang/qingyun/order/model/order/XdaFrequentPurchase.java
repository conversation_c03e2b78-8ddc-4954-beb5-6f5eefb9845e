package com.pinshang.qingyun.order.model.order;

import com.pinshang.qingyun.base.po.BaseIDPO;
import lombok.Builder;
import lombok.Data;

import javax.persistence.Entity;
import javax.persistence.Table;
import java.math.BigDecimal;
import java.util.Date;

@Entity
@Data
@Table(name = "t_xda_frequent_purchase")
public class XdaFrequentPurchase extends BaseIDPO{
    private Long storeId;

    private Long commodityId;

    private Date updateTime;

    private BigDecimal quantity;
}
package com.pinshang.qingyun.order.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;
import lombok.ToString;

import java.util.Date;


@Data
@ToString
@Builder
@ApiModel("OrderBillLogSaveIDTO")
public class OrderBillLogSaveIDTO {

    @ApiModelProperty("客户ID")
    private Long storeId;

    @ApiModelProperty("host")
    private String host;

    @ApiModelProperty("方法类型 1-充值，2-红冲，3-扣款")
    private String methodType;

    @ApiModelProperty("参数列表")
    private String param;

    @ApiModelProperty("返回值")
    private String response;

    @ApiModelProperty("创建时间")
    private Date createTime;

}
package com.pinshang.qingyun.order.dto.tms;

import io.swagger.annotations.ApiModelProperty;

import java.math.BigDecimal;
import java.util.List;

import lombok.Data;
import lombok.NoArgsConstructor;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.pinshang.qingyun.base.enums.tms.TmsBusinessTypeEnums;
import com.pinshang.qingyun.base.enums.tms.TmsDeliveryBatchEnums;

/**
 * 【源单信息-用于运单】
 */
@Data
@NoArgsConstructor
public class RefOrderForWaybillODTO {
	@ApiModelProperty(position = 10, required = true, value = "源单类型：11-订单、21-退货单")
	private Integer refType;
	@JsonSerialize(using = ToStringSerializer.class)
	@ApiModelProperty(position = 11, required = true, value = "源单ID")
	private Long refOrderId;
	@ApiModelProperty(position = 12, required = true, value = "源单编码")
	private String refOrderCode;
	@ApiModelProperty(position = 13, required = true, value = "源单金额")
	private BigDecimal refOrderAmount;
	@ApiModelProperty(position = 14, required = true, value = "源单状态")
	private Integer refOrderStatus;
	@ApiModelProperty(position = 15, required = true, value = "源单-订单是否直送 0=否  1=是")
	private Integer refDirectStatus;
	
	@ApiModelProperty(position = 21, required = true, value = "送货/取货时间段")
	private String deliveryTimeRange;
	@ApiModelProperty(position = 22, required = true, value = "业务类型" + TmsBusinessTypeEnums.BUSINESS_TYPE_LABEL)
	private Integer businessType;
	@JsonSerialize(using = ToStringSerializer.class)
	@ApiModelProperty(position = 23, required = true, value = "物流中心ID")
	private Long logisticsCenterId;
	@ApiModelProperty(position = 24, required = true, value = "配送批次" + TmsDeliveryBatchEnums.DELIVERY_BATCH_LABEL)
	private Integer deliveryBatch;
	@JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
	@ApiModelProperty(position = 25, required = true, value = "送货日期：yyyy-MM-dd")
	private String deliveryDate;
	@JsonSerialize(using = ToStringSerializer.class)
	@ApiModelProperty(position = 26, required = true, value = "档口ID")
	private Long stallId;

	@JsonSerialize(using = ToStringSerializer.class)
	@ApiModelProperty(position = 31, required = true, value = "客户ID")
	private Long storeId;
	@ApiModelProperty(position = 32, required = true, value = "客户编码")
	private String storeCode;
	@ApiModelProperty(position = 33, required = true, value = "客户名称")
	private String storeName;
	@ApiModelProperty(position = 34, required = true, value = "客户联系人")
	private String storeLinkman;
	@ApiModelProperty(position = 35, required = true, value = "客户电话")
	private String storePhone;
	@ApiModelProperty(position = 36, required = true, value = "客户地址")
	private String storeAddress;
	
	@ApiModelProperty(position = 51, required = true, value = "源单明细集合")
	private List<RefOrderItemForWaybillODTO> refOrderItemList;
}

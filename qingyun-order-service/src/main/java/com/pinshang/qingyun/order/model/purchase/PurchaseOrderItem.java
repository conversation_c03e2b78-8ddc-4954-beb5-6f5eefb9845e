package com.pinshang.qingyun.order.model.purchase;

import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.math.BigDecimal;

@Table(name="t_dc_purchase_order_item")
@Entity
public class PurchaseOrderItem {
    /** 主键id */
    @Id
    private Long id;

    /** 采购订单id */
    private Long purchaseOrderId;

    /** 商品id */
    private Long commodityId;

    /** 一级分类ID(冗余字段) */
    private Long categoryId;

    /** 采购单价 */
    private BigDecimal price;

    /** 采购数量 */
    private BigDecimal quantity;

    private BigDecimal receivedQuantity;

    public PurchaseOrderItem() {
    }

    public PurchaseOrderItem(Long id, Long purchaseOrderId, Long commodityId, BigDecimal price, BigDecimal quantity) {
        this.id = id;
        this.purchaseOrderId = purchaseOrderId;
        this.commodityId = commodityId;
        this.price = price;
        this.quantity = quantity;
    }

    public Long getCategoryId() {
        return categoryId;
    }

    public void setCategoryId(Long categoryId) {
        this.categoryId = categoryId;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getPurchaseOrderId() {
        return purchaseOrderId;
    }

    public void setPurchaseOrderId(Long purchaseOrderId) {
        this.purchaseOrderId = purchaseOrderId;
    }

    public Long getCommodityId() {
        return commodityId;
    }

    public void setCommodityId(Long commodityId) {
        this.commodityId = commodityId;
    }

    public BigDecimal getPrice() {
        return price;
    }

    public void setPrice(BigDecimal price) {
        this.price = price;
    }

    public BigDecimal getQuantity() {
        return quantity;
    }

    public void setQuantity(BigDecimal quantity) {
        this.quantity = quantity;
    }

    public BigDecimal getReceivedQuantity() {
        return receivedQuantity;
    }

    public void setReceivedQuantity(BigDecimal receivedQuantity) {
        this.receivedQuantity = receivedQuantity;
    }

    @Override
    public String toString() {
        return "PurchaseOrderItem{" +
                "id=" + id +
                ", purchaseOrderId=" + purchaseOrderId +
                ", commodityId=" + commodityId +
                ", price=" + price +
                ", quantity=" + quantity +
                '}';
    }
}
package com.pinshang.qingyun.order.dto;

import com.pinshang.qingyun.pay.dto.Attach;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.Date;

/**
 * @Author: liuZhen
 * @DateTime: 2022/1/10 14:33
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class CallBackIDTO {
    /**
     * 订单code
     */
    private String orderCode;
    /**
     * 外部code
     */
    private String transactionId;
    /**
     * 校验结果是否合法
     * 当且仅当校验合法与支付成功时为true
     */
    private boolean validate;
    /**
     * 请求报文
     */
    private String json;

    /**
     * 支付状态，后面可调整为枚举
     * 阿里的回调通知状态有 TRADE_FINISHED TRADE_SUCCESS TRADE_CLOSED
     */
    private String payStatus;
    private Boolean noLog;

    private Date payTime;
    private Integer payType;
    private String appCode;

    private Attach attach;

    private BigDecimal payAmount;
}

package com.pinshang.qingyun.order.vo.order;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class OrderVo {

    private Long sourceId;//订单ID，退货单ID 等

    /** 订单编码 */
    private String sourceCode;

    /** 直送订单=ZS_ORDER，后台下单=ORDER、手机APP下单=ORDER 退货RT_XSRETURN */
    private String sourceType;

    /** 订单类型(1=PC下单,2=APP下单, 8=鲜达APP 10-门店订货下单) */
    private Integer orderType;

    private Long storeId;// 客户ID

    private Date orderTime;//订单时间 或者 退货确认入库时间等

    private Date createTime;//数据产生日期，如 订单创建时间，出库单创建时间，确认退货单入库时间等

    private BigDecimal totalAmount;// 下单或者退货 实际有效的总金额

    private List<ItemVo> items;


    /** 实发总金额 */
    private BigDecimal deliveryTotalAmount;
    /** 订单送货时间 或者 退货确认入库时间等 */
    private Date deliveryTime;
    /** 消息发送时间 */
    private String sendMqTime;

    //出库单号
    private String stockOutOrderCode;
    // 出库时间
   // private Date stockOutTime;
    private Long companyId;

    private Long consignmentId; // 代销商户id

    /** 业务类型 */
    private Integer businessType;
}

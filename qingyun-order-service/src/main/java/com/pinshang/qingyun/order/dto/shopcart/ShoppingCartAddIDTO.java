package com.pinshang.qingyun.order.dto.shopcart;

import com.pinshang.qingyun.base.enums.YesOrNoEnums;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @Date 2019/11/18 10:46
 */
@Data
@ApiModel
@NoArgsConstructor
@AllArgsConstructor
public class ShoppingCartAddIDTO {
    @ApiModelProperty(value = "商品id, 在购物车外面商品列表点击添加时必传",example = "10758910621399202")
    private Long commodityId;
    @ApiModelProperty(value = "追加数量,在商品列表页面追加带处理方式的商品时,输入的数量增量.")
    private BigDecimal appendQuantity;
    @ApiModelProperty(value = "客户id",hidden = true)
    private Long storeId;
    @ApiModelProperty(value = "订货时间")
    private Date orderTime;
    @ApiModelProperty("是否是购物车页面请求")
    private YesOrNoEnums shopCartPage;
}

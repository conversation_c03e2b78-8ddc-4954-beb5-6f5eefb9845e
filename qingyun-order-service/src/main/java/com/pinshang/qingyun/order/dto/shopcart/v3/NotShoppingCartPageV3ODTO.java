package com.pinshang.qingyun.order.dto.shopcart.v3;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.pinshang.qingyun.base.configure.codec.DecimalSerializerKeep2;
import com.pinshang.qingyun.order.dto.shopcart.v2.NotShoppingCartPageV2ODTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

@Data
@ApiModel
@NoArgsConstructor
@AllArgsConstructor
public class NotShoppingCartPageV3ODTO {
    @ApiModelProperty(value = "商品id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long commodityId;

    @ApiModelProperty(value = "商品类型 默认1、普通商品 2、特惠商品")
    private Integer commodityType;

    @ApiModelProperty(value = "数量")
    private BigDecimal quantity;

    @ApiModelProperty("是否可继续添加-->用于是否置灰购物车内加号按钮的判断")
    private Boolean ableAdd;

    @ApiModelProperty("商品库存缺少修改提示")
    private String stockWarningTips;

    @ApiModelProperty("正常商品组商品优惠后的价格")
    @JsonSerialize(using = DecimalSerializerKeep2.class)
    private BigDecimal normalGroupAmount = BigDecimal.ZERO;

    @ApiModelProperty("特惠提示")
    private String thCategoryTips;

    public static NotShoppingCartPageV3ODTO init(Long commodityId, Integer commodityType, BigDecimal quantity){
        NotShoppingCartPageV3ODTO notShoppingCartPageV3ODTO = new NotShoppingCartPageV3ODTO();
        notShoppingCartPageV3ODTO.setCommodityId(commodityId);
        notShoppingCartPageV3ODTO.setCommodityType(commodityType);
        notShoppingCartPageV3ODTO.setQuantity(quantity);
        return notShoppingCartPageV3ODTO;
    }
}

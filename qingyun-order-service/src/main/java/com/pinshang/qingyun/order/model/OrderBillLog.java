package com.pinshang.qingyun.order.model;

import com.pinshang.qingyun.base.po.BaseIDPO;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import javax.persistence.Entity;
import javax.persistence.Table;
import java.util.Date;

/**
 * <p>
 * 订单账单业务日志
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-02
 */
@Data
@Entity
@ToString
@EqualsAndHashCode(callSuper = true)
@Table(name = "t_order_bill_log")
public class OrderBillLog extends BaseIDPO {

    /**
     * 客户ID
     */
    private Long storeId;

    /**
     * host
     */
    private String host;

    /**
     * 方法类型 1-充值，2-红冲，3-扣款
     */
    private String methodType;

    /**
     * 参数列表
     */
    private String param;

    /**
     * 返回值
     */
    private String response;

    /**
     * 创建时间
     */
    private Date createTime;

}

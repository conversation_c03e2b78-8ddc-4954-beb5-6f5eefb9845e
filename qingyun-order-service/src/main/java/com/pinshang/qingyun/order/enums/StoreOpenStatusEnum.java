package com.pinshang.qingyun.order.enums;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;

/**
 *
 * <AUTHOR>
 * @Date 2018/4/8 16:24
 */
public enum StoreOpenStatusEnum implements BaseEnum{

    ENABLE(0,"启用"),
    DISABLE(1,"停用"),
    INIT(-1,"刚创建了客户,待指定线路"),
    TO_SETTLEMENT(2,"待指定结账客户")
    ;
    private Integer code;
    private String desc;

    StoreOpenStatusEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    @Override
    public Integer convert() {
        return this.code;
    }
    @JsonValue
    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }
    @JsonCreator
    public static StoreOpenStatusEnum getEnumByCode(Integer code){
        if(code == null){
            return null;
        }
        for(StoreOpenStatusEnum e : StoreOpenStatusEnum.values()){
            if(e.getCode().intValue() == code){
                return e;
            }
        }
        return null;
    }
}

package com.pinshang.qingyun.order.enums;

public enum ShopReceiveOrderStatusEnums {

	UNRECEIVED("待收货", 0),
	UNCHECKED("待审核", 1), 
	REJECT("审核未通过", 2), 
	PASS("已完成", 3),
	CANCEL("取消", 4);
    private String name;
    private int code;

    ShopReceiveOrderStatusEnums(String name, int code) {
        this.name = name;
        this.code = code;
    }

    public static String getName(int code) {
        for (ShopReceiveOrderStatusEnums es : ShopReceiveOrderStatusEnums.values()) {
            if (code == es.getCode()) {
                return es.name;
            }
        }
        return null;
    }

    public String getName() {
        return name;
    }

    public int getCode() {
        return code;
    }
}

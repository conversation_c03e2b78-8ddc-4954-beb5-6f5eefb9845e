package com.pinshang.qingyun.order.dto.shopcart.v4;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.pinshang.qingyun.base.configure.codec.DecimalSerializerKeep2;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date 2024/3/4 14:00
 */
@Data
public class ShoppingCartV4ODTO {
    @ApiModelProperty("购物车底部合计(含促销特价合计,值可能与订单详情中商品总金额不同) --优惠后合计")
    @JsonSerialize(using = DecimalSerializerKeep2.class)
    private BigDecimal summation;

    @ApiModelProperty("原价合计")
    @JsonSerialize(using = DecimalSerializerKeep2.class)
    private BigDecimal originalAmount;

    @ApiModelProperty("正常商品组商品优惠后的价格")
    @JsonSerialize(using = DecimalSerializerKeep2.class)
    private BigDecimal normalGroupAmount = BigDecimal.ZERO;

    @ApiModelProperty("总共立减")
    @JsonSerialize(using = DecimalSerializerKeep2.class)
    private BigDecimal discountTotal;

    @ApiModelProperty("是否满足特惠")
    private Boolean isThInvalidate = Boolean.FALSE;

    @ApiModelProperty("特惠价阈值")
    private BigDecimal thFullPrice;

    @ApiModelProperty("特惠提示")
    private String thCategoryTips = "";

    @ApiModelProperty("普通商品组")
    private ShoppingCartGroupV4ODTO normalGroup;

    @ApiModelProperty("普通商品失效商品组")
    private ShoppingCartGroupV4ODTO invalidateGroup;

    @ApiModelProperty("特惠商品组")
    private ShoppingCartGroupV4ODTO thGroups;

    @ApiModelProperty("买赠方案商品组集合")
    private List<ShoppingCartGroupV4ODTO> promotionGroup;

    @ApiModelProperty("品种合计")
    private Integer varietySum;

    @ApiModelProperty("普通商品数量+赠品数量")
    private BigDecimal commodityNum;

    @ApiModelProperty("是否可结算")
    private Boolean canSettlement;
    @ApiModelProperty("购物车底部标语")
    private String bottomTips;
    @ApiModelProperty("购物车顶部送货时间")
    private String topTips;
    @ApiModelProperty(value = "结算报错", hidden = true)
    private String warnMessage;
    @ApiModelProperty("商品库存缺少修改提示 -- 仅限购物车内修改商品数量使用")
    private String stockWarningTips;
    @ApiModelProperty(value = "赠品所属活动分组", hidden = true)
    private Map<Long, List<ShoppingCartCommodityV4ODTO>> conditionMap;
    @ApiModelProperty(value = "商品信息，非购物车页访问购物车接口时返回的参数")
    private NotShoppingCartPageV4ODTO notShoppingCartPageODTO;

    @ApiModelProperty(value = "创建订单使用判断商品库存依据是否和购物车一致", hidden = true)
    private Map<Long, Integer> commodityStockTypeMap;

    @ApiModelProperty(value = "现付倒计时 为0时正常")
    private Long xfOrderCountdown;

    @ApiModelProperty(value = "送货时间段")
    private String deliveryTimeRange;
    @ApiModelProperty(value = "配送批次")
    private Integer deliveryBatch;

    @ApiModelProperty("商品特价限购提示")
    private String specialLimitWarningTips;

    @ApiModelProperty("优惠明细")
    private DiscountDetailODTO discountDetail;

    @ApiModelProperty("用户优惠券id")
    private Long couponUserId;

    @ApiModelProperty("优惠券ID")
    private Long couponId;

    @ApiModelProperty("券可用数量")
    private Integer couponAvailableNumber;

    @ApiModelProperty("券优惠合计")
    @JsonSerialize(using = DecimalSerializerKeep2.class)
    private BigDecimal couponAmount;


    @ApiModelProperty("送货日期是否当天")
    private Boolean orderTimeIsToday = false;

}

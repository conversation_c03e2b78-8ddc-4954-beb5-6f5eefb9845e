package com.pinshang.qingyun.order.util.list;

import java.util.List;
import java.util.function.Function;
import java.util.stream.Collectors;

public class ListExtractor {
    /**
     * Extracts a specific property from a list of objects and collects them into a list.
     *
     * @param list the input list to be processed
     * @param mapper function to extract the property from an element of type T
     * @param <T> the type of elements in the input list
     * @param <R> the type of elements in the resulting list
     * @return a List of properties extracted from the input list
     */
    public static <T, R> List<R> extractToList(List<T> list, Function<T, R> mapper) {
        return list.stream().map(mapper).collect(Collectors.toList());
    }
}

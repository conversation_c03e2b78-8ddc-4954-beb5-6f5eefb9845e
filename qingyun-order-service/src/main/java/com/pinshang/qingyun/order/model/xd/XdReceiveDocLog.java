package com.pinshang.qingyun.order.model.xd;

import com.pinshang.qingyun.base.po.BaseSimplePO;

import javax.persistence.Entity;
import javax.persistence.Table;
import java.math.BigDecimal;
import java.util.Date;

/**
 * @Author: sk
 * @Date: 2020/3/17
 */
@Entity
@Table(name="t_xd_receive_doc_log")
public class XdReceiveDocLog extends BaseSimplePO {

    private Long docId;

    /** 前置仓id **/
    private Long shopId;

    /** 送货日期 **/
    private Date deliveryTime;

    /** 商品id **/
    private Long  commodityId;

    /** 正常品数量 **/
    private BigDecimal normalQuantity;

    /** 正常品份数 **/
    private Long normalShares;

    /** 异常品数量 **/
    private BigDecimal abnormalQuantity;

    /** 异常品份数 **/
    private Long abnormalShares;

    public Long getDocId() {
        return docId;
    }

    public void setDocId(Long docId) {
        this.docId = docId;
    }

    public Long getShopId() {
        return shopId;
    }

    public void setShopId(Long shopId) {
        this.shopId = shopId;
    }

    public Date getDeliveryTime() {
        return deliveryTime;
    }

    public void setDeliveryTime(Date deliveryTime) {
        this.deliveryTime = deliveryTime;
    }

    public Long getCommodityId() {
        return commodityId;
    }

    public void setCommodityId(Long commodityId) {
        this.commodityId = commodityId;
    }

    public BigDecimal getNormalQuantity() {
        return normalQuantity;
    }

    public void setNormalQuantity(BigDecimal normalQuantity) {
        this.normalQuantity = normalQuantity;
    }

    public Long getNormalShares() {
        return normalShares;
    }

    public void setNormalShares(Long normalShares) {
        this.normalShares = normalShares;
    }

    public BigDecimal getAbnormalQuantity() {
        return abnormalQuantity;
    }

    public void setAbnormalQuantity(BigDecimal abnormalQuantity) {
        this.abnormalQuantity = abnormalQuantity;
    }

    public Long getAbnormalShares() {
        return abnormalShares;
    }

    public void setAbnormalShares(Long abnormalShares) {
        this.abnormalShares = abnormalShares;
    }
}

/**
 * fuson.com Inc.
 * Copyright (c) 2021-2022 All Rights Reserved.
 */
package com.pinshang.qingyun.order.dto.xda.v4;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Collections;
import java.util.List;

/**
 * <p>
 * 订单优惠券信息
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-15
 */
@Data
public class XdaOrderCouponODTO implements Serializable {

    /**
     * 推荐用户券id
     */
    @ApiModelProperty("推荐用户券id")
    private Long recommendedCouponUserId;

    /**
     * 可用券列表
     */
    @ApiModelProperty("可用券列表")
    private List<XdaCouponUserInfoODTO> availableCouponInfos;

    /**
     * 不可用券列表
     */
    @ApiModelProperty("不可用券列表")
    private List<XdaCouponUserInfoODTO> unavailableCouponInfos;

    public static XdaOrderCouponODTO initOrderCoupon() {
        XdaOrderCouponODTO xdaOrderCoupon = new XdaOrderCouponODTO();
        xdaOrderCoupon.setAvailableCouponInfos(Collections.emptyList());
        xdaOrderCoupon.setUnavailableCouponInfos(Collections.emptyList());
        xdaOrderCoupon.setRecommendedCouponUserId(null);
        return xdaOrderCoupon;
    }
}


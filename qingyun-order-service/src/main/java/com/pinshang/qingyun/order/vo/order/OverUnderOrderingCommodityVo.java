package com.pinshang.qingyun.order.vo.order;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Collections;
import java.util.List;

/**
 * <p>
 * 多订、少订的商品列表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-02-19
 */
@Data
public class OverUnderOrderingCommodityVo {
    @ApiModelProperty(value = "多订商品列表")
    private List<OverUnderOrderingCommodityInfoVo> overOrderingCommodityVos;

    @ApiModelProperty(value = "少订商品列表")
    private List<OverUnderOrderingCommodityInfoVo> underOrderingCommodityVos;

    public static OverUnderOrderingCommodityVo init() {
        OverUnderOrderingCommodityVo commodityVo = new OverUnderOrderingCommodityVo();
        commodityVo.setOverOrderingCommodityVos(Collections.emptyList());
        commodityVo.setUnderOrderingCommodityVos(Collections.emptyList());
        return commodityVo;
    }
}

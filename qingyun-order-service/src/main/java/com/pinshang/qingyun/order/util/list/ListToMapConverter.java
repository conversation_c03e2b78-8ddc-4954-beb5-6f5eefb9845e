package com.pinshang.qingyun.order.util.list;

import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

public class ListToMapConverter {

    /**
     * Converts a List of type T to a Map with keys of type X and values of type Y.
     *
     * @param list the input list to be converted
     * @param keyMapper function to extract the key from an element of type T
     * @param valueMapper function to extract the value from an element of type T
     * @param <T> the type of elements in the list
     * @param <X> the type of keys in the resulting map
     * @param <Y> the type of values in the resulting map
     * @return a Map with keys of type X and values of type Y
     */

    public static <T, X, Y> Map<X, Y> convertListToMap(List<T> list, Function<T, X> keyMapper, Function<T, Y> valueMapper) {
        return list.stream().collect(Collectors.toMap(
                keyMapper,
                valueMapper,
                (existing, replacement) -> replacement // in case of duplicate keys, the new value overwrites the old one
        ));
    }

    public static <T, X> Map<X, T> convertListToMap(List<T> list, Function<T, X> keyMapper) {
        return list.stream().collect(Collectors.toMap(
                keyMapper,
                item->item,
                (existing, replacement) -> replacement // in case of duplicate keys, the new value overwrites the old one
        ));
    }
}

package com.pinshang.qingyun.order.dto.order;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * @Author: sk
 * @Date: 2024/9/2
 */
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
public class AssociationOrderODTO {

    private Long commodityId;

    /**类型 1订单商品,2赠品,3配货商品,5特惠商品	—— 参见 ProductTypeEnums  **/
    private Integer type;

    private BigDecimal commodityPrice;

    private Long orderListId;

    private Boolean orderDone = false;
}

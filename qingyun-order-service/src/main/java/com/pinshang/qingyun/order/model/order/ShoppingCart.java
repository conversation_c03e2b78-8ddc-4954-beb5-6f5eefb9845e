package com.pinshang.qingyun.order.model.order;

import com.pinshang.qingyun.base.po.BaseEnterpriseSimplePO;

import javax.persistence.Entity;
import javax.persistence.Table;
import java.math.BigDecimal;

@Entity
@Table(name = "t_md_shopping_cart")
public class ShoppingCart extends BaseEnterpriseSimplePO{
    private Integer logisticsModel;

    private Long storeId;

    private Long warehouseId;

    private Long supplierId;

    private String deleveryTimeRange;

    private BigDecimal totalPrice;

    private Long subOrderId;

    private Integer status;

    private Integer varietyTotal;

    private Integer adminStatus;
    private String orderTime;
    private String deliveryBatch;

    private Integer autoStatus; // 是否自动订货

    private Long consignmentId; // 代销商户id

    /** 库存依据  1=依据大仓, 2=不限量订货, 3=限量供应 **/
    private Integer stockType;
    /** 档口id */
    private Long stallId;

    public Long getStallId() {
        return stallId;
    }

    public void setStallId(Long stallId) {
        this.stallId = stallId;
    }

    public Integer getStockType() {
        return stockType;
    }

    public void setStockType(Integer stockType) {
        this.stockType = stockType;
    }

    public Long getConsignmentId() {
        return consignmentId;
    }

    public void setConsignmentId(Long consignmentId) {
        this.consignmentId = consignmentId;
    }

    public Integer getAutoStatus() {
        return autoStatus;
    }

    public void setAutoStatus(Integer autoStatus) {
        this.autoStatus = autoStatus;
    }

    public String getOrderTime() {
        return orderTime;
    }

    public void setOrderTime(String orderTime) {
        this.orderTime = orderTime;
    }

    public String getDeliveryBatch() {
        return deliveryBatch;
    }

    public void setDeliveryBatch(String deliveryBatch) {
        this.deliveryBatch = deliveryBatch;
    }

    public Long getStoreId() {
        return storeId;
    }

    public void setStoreId(Long storeId) {
        this.storeId = storeId;
    }

    public Long getWarehouseId() {
        return warehouseId;
    }

    public void setWarehouseId(Long warehouseId) {
        this.warehouseId = warehouseId;
    }

    public Long getSupplierId() {
        return supplierId;
    }

    public void setSupplierId(Long supplierId) {
        this.supplierId = supplierId;
    }

    public BigDecimal getTotalPrice() {
        return totalPrice;
    }

    public void setTotalPrice(BigDecimal totalPrice) {
        this.totalPrice = totalPrice;
    }

    public Long getSubOrderId() {
        return subOrderId;
    }

    public void setSubOrderId(Long subOrderId) {
        this.subOrderId = subOrderId;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Integer getVarietyTotal() {
        return varietyTotal;
    }

    public void setVarietyTotal(Integer varietyTotal) {
        this.varietyTotal = varietyTotal;
    }

	public Integer getLogisticsModel() {
		return logisticsModel;
	}

	public void setLogisticsModel(Integer logisticsModel) {
		this.logisticsModel = logisticsModel;
	}

    public String getDeleveryTimeRange() {
        return deleveryTimeRange;
    }

    public void setDeleveryTimeRange(String deleveryTimeRange) {
        this.deleveryTimeRange = deleveryTimeRange;
    }

    public Integer getAdminStatus() {
        return adminStatus;
    }

    public void setAdminStatus(Integer adminStatus) {
        this.adminStatus = adminStatus;
    }
}
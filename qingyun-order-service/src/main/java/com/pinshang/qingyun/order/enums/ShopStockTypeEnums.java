package com.pinshang.qingyun.order.enums;

/*
 * 门店库存操作类型枚举
 */
public enum ShopStockTypeEnums {

	SHOP_RECEIVE("门店收货", 1),
	SHOP_PREORDER_RECEIVE("预订单收货", 2),
	SHOP_CHECK("门店审核", 3),
	SHOP_RETURN_ADD("门店退货单新增", 4),
	SHOP_RETURN_CANCEL("门店退货单取消", 5),
	SHOP_APP_RETURN_INSTOCK("APP订单退货入库", 6),
	SHOP_APP_DELIVERY("APP订单配送", 7),
	SHOP_APP_CANCEL("APP订单取消", 8),
	SHOP_STOCK_ADJUST("库存调整单", 9),
	SHOP_STOCK_ADD_ONLINE("添加冻结库存", 10);
    private String name;
    private Integer code;

    ShopStockTypeEnums(String name, Integer code) {
        this.name = name;
        this.code = code;
    }

    public static String getName(int code) {
        for (ShopStockTypeEnums es : ShopStockTypeEnums.values()) {
            if (code == es.getCode()) {
                return es.name;
            }
        }
        return null;
    }

    public String getName() {
        return name;
    }

    public Integer getCode() {
        return code;
    }
}

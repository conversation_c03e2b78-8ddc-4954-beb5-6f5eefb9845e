package com.pinshang.qingyun.order.vo.pda;

import com.pinshang.qingyun.renderer.annotation.FieldRender;
import com.pinshang.qingyun.renderer.constant.RenderFieldHelper;
import com.pinshang.qingyun.renderer.enums.FieldTypeEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

@Data
@ApiModel
public class PdaCommodityOrderQueryData {

    @ApiModelProperty("商品Id")
    private Long commodityId;

    @ApiModelProperty("商品名")
    @FieldRender(fieldType = FieldTypeEnum.COMMODITY,fieldName = RenderFieldHelper.Commodity.commodityName,keyName = "commodityId")
    private String commodityName;

    @ApiModelProperty("型号规格")
    @FieldRender(fieldType = FieldTypeEnum.COMMODITY,fieldName = RenderFieldHelper.Commodity.commoditySpec,keyName = "commodityId")
    private String commoditySpec;

    @ApiModelProperty("商品单位")
    private BigDecimal commodityUnitId;

    @ApiModelProperty("计量单位")
    @FieldRender(fieldType = FieldTypeEnum.DICTIONARY,fieldName = RenderFieldHelper.Dictionary.optionName,keyName = "commodityUnitId")
    private String commodityUnit;

    @ApiModelProperty("待出库数量")
    private BigDecimal notShipCount;

    @ApiModelProperty("已出库短交数量")
    private BigDecimal shipShotCount;

    @ApiModelProperty("已出库未短交数量")
    private BigDecimal shipNotShotCount;

    @ApiModelProperty("订货数量")
    private BigDecimal orderCount;

}

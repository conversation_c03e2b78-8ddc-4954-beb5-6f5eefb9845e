package com.pinshang.qingyun.order.model.order;

import com.pinshang.qingyun.base.po.BaseIDPO;

import javax.persistence.Entity;
import javax.persistence.Table;
import java.math.BigDecimal;
import java.util.Date;
@Entity
@Table(name = "t_md_shopping_cart_item")
public class ShoppingCartItem extends BaseIDPO{
    private Long shoppingCartId;

    private Long commodityId;

    private BigDecimal commodityNum;

    private BigDecimal adviseCommodityNum;

    private Date createTime;
    private Long createId;

    public Long getCreateId() {
        return createId;
    }

    public void setCreateId(Long createId) {
        this.createId = createId;
    }

    public BigDecimal getAdviseCommodityNum() {
        return adviseCommodityNum;
    }

    public void setAdviseCommodityNum(BigDecimal adviseCommodityNum) {
        this.adviseCommodityNum = adviseCommodityNum;
    }

    public Long getCommodityId() {
        return commodityId;
    }

    public void setCommodityId(Long commodityId) {
        this.commodityId = commodityId;
    }

    public BigDecimal getCommodityNum() {
        return commodityNum;
    }

    public void setCommodityNum(BigDecimal commodityNum) {
        this.commodityNum = commodityNum;
    }

	public Date getCreateTime() {
		return createTime;
	}

	public void setCreateTime(Date createTime) {
		this.createTime = createTime;
	}

	public Long getShoppingCartId() {
		return shoppingCartId;
	}

	public void setShoppingCartId(Long shoppingCartId) {
		this.shoppingCartId = shoppingCartId;
	}
}
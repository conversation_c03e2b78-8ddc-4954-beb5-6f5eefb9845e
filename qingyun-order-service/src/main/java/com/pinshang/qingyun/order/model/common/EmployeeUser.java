package com.pinshang.qingyun.order.model.common;

import javax.persistence.*;

import lombok.Data;
import lombok.NoArgsConstructor;

import com.pinshang.qingyun.base.po.BaseSimplePO;

/**
 * 职员用户		—— 从SMM库同步过来的
 */
@Data
@Entity
@NoArgsConstructor
@Table(name = "t_employee_user")
public class EmployeeUser{

    @Id
    @Column(name = "id")
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    protected Long id;

    /** 职员ID **/
    private Long employeeId;
    /** 职员code **/
    private Long employeeCode;
    /** 职员姓名 **/
    private String employeeName;
    /** 职员手机号 **/
    private String employeePhone;
    /**
     * 职员状态：1-在职、4-离职(smm.t_smm_employee 字段)
     */
    private Integer employeeState;
    /** 账号状态：0-未开通、1-已开通、2-已关闭(smm.t_smm_employee 字段) **/
    private Integer employeeAccountState;
    /** 用户ID **/
    private Long userId;
    //用户是否删除(smm.t_smm_user 主键)
    private Integer isDeleted;
}

package com.pinshang.qingyun.order.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class CommodityPriceVo{

    private Long commodityId;
    private String commodityCode;
    @ApiModelProperty("单价")
    private BigDecimal originPrice;
    @ApiModelProperty("促销单价")
    private BigDecimal promotionPrice;

    public CommodityPriceVo(Long commodityId,String commodityCode,BigDecimal originPrice){
        this.commodityId = commodityId;
        this.commodityCode = commodityCode;
        this.originPrice = originPrice;
    }
}

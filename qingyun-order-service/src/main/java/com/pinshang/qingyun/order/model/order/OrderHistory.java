package com.pinshang.qingyun.order.model.order;

import com.pinshang.qingyun.base.po.BaseIDPO;
import com.pinshang.qingyun.order.vo.order.OrderHistoryEntityTypeEnums;
import com.pinshang.qingyun.order.vo.order.OrderHistoryOperationTypeEnums;

import javax.persistence.Entity;
import javax.persistence.Table;
import java.util.Date;

/**
 * 订单日志
 *
 * <AUTHOR>
 *
 * @date 2017年4月6日
 */
@Entity
@Table(name = "t_order_history")
public class OrderHistory extends BaseIDPO {
	
	private static final long serialVersionUID = -4511234091130452454L;
	
	private Integer entityType;
	
	private Integer operationType;
	
    private Long orderId;
    
    private Long commodityId;
    
    private String code;
    private String name;
    
    private String newValue;
    
    private String oldValue;
    
    private Integer addType;
    
 	private Long createId;
	
	private String createName;
 	
 	private Date createTime;
    
    public OrderHistory() {
		super();
	}
    
    /**
     * 订单-新建
     * 
     * @param orderId
     * @param orderCode
     * @param newValue
     * @param createId
     * @param createName
     * @param createTime
     * @return
     */
    public static OrderHistory order_create(Long orderId, String orderCode, String newValue, Long createId, String createName, Date createTime) {
    	return new OrderHistory(OrderHistoryEntityTypeEnums.ORDER.getCode(), OrderHistoryOperationTypeEnums.ORDER_CREATE.getCode(), orderId, null, orderCode, "订单日期", newValue, null, null, createId, createName, createTime);
    }
    
    /**
     * 订单-取消
     * 
     * @param orderId
     * @param orderCode
     * @param createId
     * @param createName
     * @param createTime
     * @return
     */
    public static OrderHistory order_cancel(Long orderId, String orderCode, Long createId, String createName, Date createTime) {
    	return new OrderHistory(OrderHistoryEntityTypeEnums.ORDER.getCode(), OrderHistoryOperationTypeEnums.ORDER_CANCEL.getCode(), orderId, null, orderCode, null, null, null, null, createId, createName, createTime);
    }

	public static OrderHistory order_cancel_zero_out(Long orderId, String orderCode, Long createId, String createName, Date createTime) {
		return new OrderHistory(OrderHistoryEntityTypeEnums.ORDER.getCode(), OrderHistoryOperationTypeEnums.ORDER_CANCEL.getCode(), orderId, null, orderCode, null, null, null, 3, createId, createName, createTime);
	}

	public static OrderHistory order_modify(Long orderId, String orderCode, Long createId, String createName, Date createTime, String newValue, String oldValue) {
		return new OrderHistory(OrderHistoryEntityTypeEnums.ORDER.getCode(), OrderHistoryOperationTypeEnums.ORDER_MODIFY.getCode(), orderId, null, orderCode, "订单日期", newValue, oldValue, null, createId, createName, createTime);
	}

	/**
     * 商品-添加
     * 
     * @param orderId
     * @param commodityId
     * @param commodityCode
     * @param commodityName
     * @param newValue
     * @param addType
     * @param createId
     * @param createName
     * @param createTime
     * @return
     */
    public static OrderHistory commodity_add(Long orderId, Long commodityId, String commodityCode, String commodityName, String newValue, Integer addType, Long createId, String createName, Date createTime) {
    	return new OrderHistory(OrderHistoryEntityTypeEnums.COMMODITY.getCode(), OrderHistoryOperationTypeEnums.COMMODITY_ADD.getCode(), orderId, commodityId, commodityCode, commodityName, newValue, null, addType, createId, createName, createTime);
    }
    
    /**
     * 商品-修改
     * 
     * @param orderId
     * @param commodityId
     * @param commodityCode
     * @param commodityName
     * @param newValue
     * @param oldValue
     * @param addType
     * @param createId
     * @param createName
     * @param createTime
     * @return
     */
    public static OrderHistory commodity_modify(Long orderId, Long commodityId, String commodityCode, String commodityName, String newValue, String oldValue, Integer addType, Long createId, String createName, Date createTime) {
    	return new OrderHistory(OrderHistoryEntityTypeEnums.COMMODITY.getCode(), OrderHistoryOperationTypeEnums.COMMODITY_MODIFY.getCode(), orderId, commodityId, commodityCode, commodityName, newValue, oldValue, addType, createId, createName, createTime);
    }

    /**
     * 商品-删除
     * 
     * @param orderId
     * @param commodityId
     * @param commodityCode
     * @param commodityName
     * @param addType
     * @param createId
     * @param createTime
     * @return
     */
    public static OrderHistory commodity_delete(Long orderId, Long commodityId, String commodityCode, String commodityName, Integer addType, Long createId, String createName, Date createTime) {
    	return new OrderHistory(OrderHistoryEntityTypeEnums.COMMODITY.getCode(), OrderHistoryOperationTypeEnums.COMMODITY_DELETE.getCode(), orderId, commodityId, commodityCode, commodityName, null, null, addType, createId, createName, createTime);
    }
	
	private OrderHistory(Integer entityType, Integer operationType, Long orderId, Long commodityId, String code, String name, String newValue, String oldValue, Integer addType, Long createId, String createName, Date createTime) {
		super();
		// this.id = id;
		this.entityType = entityType;
		this.operationType = operationType;
		this.orderId = orderId;
		this.commodityId = commodityId;
		this.code = code;
		this.name = name;
		this.newValue = newValue;
		this.oldValue = oldValue;
		this.addType = addType;
		this.createId = createId;
		this.createName = createName;
		this.createTime = createTime;
	}

	public Integer getEntityType() {
		return entityType;
	}

	public void setEntityType(Integer entityType) {
		this.entityType = entityType;
	}

	public Integer getOperationType() {
		return operationType;
	}

	public void setOperationType(Integer operationType) {
		this.operationType = operationType;
	}

	public Long getOrderId() {
		return orderId;
	}

	public void setOrderId(Long orderId) {
		this.orderId = orderId;
	}

	public Long getCommodityId() {
		return commodityId;
	}

	public void setCommodityId(Long commodityId) {
		this.commodityId = commodityId;
	}

	public String getCode() {
		return code;
	}

	public void setCode(String code) {
		this.code = code;
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public String getNewValue() {
		return newValue;
	}

	public void setNewValue(String newValue) {
		this.newValue = newValue;
	}

	public String getOldValue() {
		return oldValue;
	}

	public void setOldValue(String oldValue) {
		this.oldValue = oldValue;
	}

	public Integer getAddType() {
		return addType;
	}

	public void setAddType(Integer addType) {
		this.addType = addType;
	}
	
	public Long getCreateId() {
		return createId;
	}

	public void setCreateId(Long createId) {
		this.createId = createId;
	}
	
	public String getCreateName() {
		return createName;
	}

	public void setCreateName(String createName) {
		this.createName = createName;
	}

	public Date getCreateTime() {
		return createTime;
	}

	public void setCreateTime(Date createTime) {
		this.createTime = createTime;
	}
}
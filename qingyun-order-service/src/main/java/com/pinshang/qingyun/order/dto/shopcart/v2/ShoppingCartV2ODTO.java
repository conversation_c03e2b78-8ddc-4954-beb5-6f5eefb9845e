package com.pinshang.qingyun.order.dto.shopcart.v2;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.pinshang.qingyun.base.configure.codec.DecimalSerializerKeep2;
import com.pinshang.qingyun.order.dto.shopcart.NotShoppingCartPageODTO;
import com.pinshang.qingyun.order.dto.shopcart.ShoppingCartCommodityODTO;
import com.pinshang.qingyun.order.dto.shopcart.ShoppingCartGroupODTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 23/6/14/014 10:11
 */
@Data
@ApiModel
@NoArgsConstructor
@AllArgsConstructor
public class ShoppingCartV2ODTO {
    @ApiModelProperty(" 购物车底部合计(含促销特价合计,值可能与订单详情中商品总金额不同) --优惠后合计")
    @JsonSerialize(using = DecimalSerializerKeep2.class)
    private BigDecimal summation;
    @ApiModelProperty(" 商品合计金额（不含特价）--原价合计")
    @JsonSerialize(using = DecimalSerializerKeep2.class)
    private BigDecimal originalAmount;

    @ApiModelProperty("正常商品组商品优惠后的价格")
    @JsonSerialize(using = DecimalSerializerKeep2.class)
    private BigDecimal normalGroupAmount = BigDecimal.ZERO;

    @ApiModelProperty("总共立减")
    @JsonSerialize(using = DecimalSerializerKeep2.class)
    private BigDecimal discountTotal;

    @ApiModelProperty("是否满足特惠")
    private Boolean isThInvalidate = Boolean.FALSE;

    private BigDecimal thFullPrice;

    @ApiModelProperty("特惠提示")
    private String thCategoryTips = "";


    @ApiModelProperty("普通商品组")
    private ShoppingCartGroupV2ODTO normalGroup;

    @ApiModelProperty("特惠商品组")
    private ShoppingCartGroupV2ODTO thGroups;

    @ApiModelProperty("失效商品组")
    private ShoppingCartGroupV2ODTO invalidateGroup;
    @ApiModelProperty("赠送商品组")
    private ShoppingCartGroupV2ODTO giftGroup;
    @ApiModelProperty("品种合计")
    private Integer varietySum;
    @ApiModelProperty("是否可结算")
    private Boolean canSettlement;
    @ApiModelProperty("购物车底部标语")
    private String bottomTips;
    @ApiModelProperty("购物车顶部送货时间")
    private String topTips;
    @ApiModelProperty(value = "结算报错", hidden = true)
    private String warnMessage;
    @ApiModelProperty(value = "赠品所属活动分组", hidden = true)
    private Map<Long, List<ShoppingCartCommodityV2ODTO>> conditionMap;
    @ApiModelProperty(value = "商品信息，非购物车页访问购物车接口时返回的参数", hidden = true)
    private NotShoppingCartPageV2ODTO notShoppingCartPageODTO;

}

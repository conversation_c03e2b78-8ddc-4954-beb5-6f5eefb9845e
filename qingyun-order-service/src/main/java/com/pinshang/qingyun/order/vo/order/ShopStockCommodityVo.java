package com.pinshang.qingyun.order.vo.order;

import java.math.BigDecimal;

public class ShopStockCommodityVo {

    private Long commodityId;

    private BigDecimal modifyQuantity;

    private Integer modifyNumber;

    //price不为null的 处理加权平均价
    private BigDecimal price;

    private BigDecimal freezeQuantity;
    
    private BigDecimal onlineQuantity;
    //totalPrice不为null的 处理加权平均价
    private BigDecimal totalPrice;

    //已订货数量-门店订货未发货的数量
    private BigDecimal orderedQuantity;

    public Integer getModifyNumber() {
        return modifyNumber;
    }

    public void setModifyNumber(Integer modifyNumber) {
        this.modifyNumber = modifyNumber;
    }

    public Long getCommodityId() {
        return commodityId;
    }

    public void setCommodityId(Long commodityId) {
        this.commodityId = commodityId;
    }

    public BigDecimal getModifyQuantity() {
        return modifyQuantity;
    }

    public void setModifyQuantity(BigDecimal modifyQuantity) {
        this.modifyQuantity = modifyQuantity;
    }

    public BigDecimal getPrice() {
        return price;
    }

    public void setPrice(BigDecimal price) {
        this.price = price;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }

        ShopStockCommodityVo that = (ShopStockCommodityVo) o;

        return commodityId.equals(that.commodityId);

    }

    @Override
    public int hashCode() {
        return commodityId.hashCode();
    }

	public BigDecimal getFreezeQuantity() {
		return freezeQuantity;
	}

	public void setFreezeQuantity(BigDecimal freezeQuantity) {
		this.freezeQuantity = freezeQuantity;
	}

	public BigDecimal getOnlineQuantity() {
		return onlineQuantity;
	}

	public void setOnlineQuantity(BigDecimal onlineQuantity) {
		this.onlineQuantity = onlineQuantity;
	}

    public BigDecimal getTotalPrice() {
        return totalPrice;
    }

    public void setTotalPrice(BigDecimal totalPrice) {
        this.totalPrice = totalPrice;
    }

    public BigDecimal getOrderedQuantity() {
        return orderedQuantity;
    }

    public void setOrderedQuantity(BigDecimal orderedQuantity) {
        this.orderedQuantity = orderedQuantity;
    }
}
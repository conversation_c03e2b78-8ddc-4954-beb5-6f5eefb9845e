package com.pinshang.qingyun.order.model.order;

import java.math.BigDecimal;
import java.util.Date;

import javax.persistence.Table;

import com.pinshang.qingyun.base.po.BaseIDPO;

import lombok.Getter;
import lombok.Setter;

/**
 *
 */
@Table(name = "t_pf_commodity_sale_day_statistics")
@Getter
@Setter
public class PfCommoditySaleDayStatistics extends BaseIDPO {
    private Long commodityId;
    private BigDecimal totalQuantity;
    private BigDecimal totalAmount;
    private Date orderTime;
}

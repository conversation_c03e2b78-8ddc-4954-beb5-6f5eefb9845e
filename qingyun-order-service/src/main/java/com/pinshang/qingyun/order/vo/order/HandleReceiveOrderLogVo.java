package com.pinshang.qingyun.order.vo.order;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.Date;

/**
 * @Author: sk
 * @Date: 2021/6/16
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class HandleReceiveOrderLogVo {

    private Long shopId;
    private String orderCode;
    private Long subOrderId;
    private Long commodityId;
    private BigDecimal receivedQuantity;
    private BigDecimal quantity;
    private Long createId;
    private Date createTime;
}

package com.pinshang.qingyun.order.model.xda;


import com.pinshang.qingyun.base.po.BaseIDPO;
import lombok.Data;

import javax.persistence.Entity;
import javax.persistence.Table;
import javax.persistence.Transient;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;


/**
 * <AUTHOR>
 */
@Data
@Entity
@Table(name = "t_xda_return_order_item")
public class XdaReturnOrderItem extends BaseIDPO {

    /**
     * 退货单id
     */
    private Long returnOrderId;

    /**
     * 退货订单类型：1-退货，2-少货，3-多货
     */
    private Integer returnOrderType;

    /**
     * 商品id
     */
    private Long commodityId;

    /**
     * 促销后单价
     */
    private BigDecimal commodityPrice;

    /**
     * 商品订货数量
     */
    private BigDecimal commodityOrderQuantity;

    /**
     * 申请份数
     */
    private Integer applyNumber;

    /**
     * 申请商品金额
     */
    private BigDecimal applyMoney;

    /**
     * 申请商品数量
     */
    private BigDecimal applyQuantity;

    /**
     * 审核商品金额
     */
    private BigDecimal checkMoney;

    /**
     * 审核商品数量
     */
    private BigDecimal checkQuantity;

    /**
     * 审核商品份数
     */
    private Integer checkNumber;

    /**
     * 确认份数
     */
    private Integer confirmNumber;

    /**
     * 确认数量
     */
    private BigDecimal confirmQuantity;

    /**
     * 确认金额
     */
    private BigDecimal confirmMoney;

    /**
     * 退货原因类型-字典类型
     */
    private Long returnReasonType;

    /**
     * 责任方
     */
    private String responsibleParty;

    /**
     * 审核备注
     */
    private String remark;

    /**
     * 是否已取消：1-已取消、0-正常（全部取消按钮会设置，单个取消走下面的删除逻辑）
     */
    private Integer status;

    /**
     * 是否删除：1-已删除、0-正常（判断可用，需要保证没取消，也没删除）
     */
    private Integer delFlag;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    @Transient
    List<String> complaintPicList;

    /**
     * 投诉内容
     */
    private String complaintContent;
}

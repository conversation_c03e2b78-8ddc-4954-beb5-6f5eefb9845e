package com.pinshang.qingyun.order.dto.xda.v4;

import com.pinshang.qingyun.base.constant.XdaAppVersionConstant;
import com.pinshang.qingyun.base.enums.TerminalSourceTypeEnum;
import com.pinshang.qingyun.base.enums.xda.XdaStoreTypeEnum;
import com.pinshang.qingyun.order.dto.shopcart.v4.TdaDeliveryTimeRange;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @Date 2024/3/8 11:13
 */
@Data
public class XdaCreatePrePayOrderV4IDTO extends TdaDeliveryTimeRange {
    @ApiModelProperty(hidden = true)
    private Long storeId;
    @ApiModelProperty("备注")
    private String remark;
    @ApiModelProperty("订单金额 ")
    private BigDecimal orderAmount;
    @ApiModelProperty("充值参数-充值金额")
    private BigDecimal payAmount;
    @ApiModelProperty("加密后支付密码，预售客户必穿")
    private String payPassword;
    @ApiModelProperty("订货日期")
    private Date orderTime;
    @ApiModelProperty(value = "appCode", hidden = true)
    private String appCode;
    @ApiModelProperty(value = "客户类型", hidden = true)
    private XdaStoreTypeEnum storeType;
    @ApiModelProperty(value = "订单截止时间", hidden = true)
    private String storeEndTime;
    @ApiModelProperty("品种合计")
    private Integer varietySum;
    @ApiModelProperty("普通商品数量+赠品数量")
    private BigDecimal commodityNum;
    @ApiModelProperty(value = "userId", hidden = true)
    private Long userId;
    @ApiModelProperty(value = "typeEnum", hidden = true)
    private TerminalSourceTypeEnum typeEnum;

    //是否需要判断密码,用于现付回调,其他场景勿用
    @ApiModelProperty(hidden = true)
    private Boolean isPayPassword = Boolean.TRUE;

    private String appVersion;

    @ApiModelProperty("用户优惠券id\n" +
            "--传 null 或空值： 表示系统推荐最优优惠券。\n" +
            "--传 -1： 表示用户不使用优惠券。\n" +
            "--传 -2： 表示老版本不使用优惠券。\n" +
            "--传实际的 couponUserId 值： 表示指定使用该优惠券。")
    private Long couponUserId;


    public void convert(Long storeId, String appCode, Long userId, TerminalSourceTypeEnum typeEnum){
        this.storeId = storeId;
        this.appCode = appCode;
        this.userId = userId;
        this.typeEnum = typeEnum;
    }

    public Long getCouponUserId() {
        // 判断版本号，为空或者小于 164则为老版本
        if (StringUtils.isEmpty(appVersion) || appVersion.compareToIgnoreCase(XdaAppVersionConstant.XDA_VERSION_165) < 0) {
            couponUserId = -2L;
        }
        return couponUserId;
    }

}

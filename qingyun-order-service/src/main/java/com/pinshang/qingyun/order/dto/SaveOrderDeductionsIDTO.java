package com.pinshang.qingyun.order.dto;

import com.pinshang.qingyun.order.enums.XdaPayTypeEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@Data
public class SaveOrderDeductionsIDTO {

    @ApiModelProperty(position = 1,value = "结账客户编码")
    Long storeId;

    @ApiModelProperty(position = 2,value = "扣款/回款枚举")
    XdaPayTypeEnum typeEnum;

    @ApiModelProperty(position = 3,value = "备注")
    String remark;

    @ApiModelProperty(position = 4,value = "金额")
    BigDecimal amount;

    @ApiModelProperty(position = 5,value = "订单Id")
    Long orderId;

    @ApiModelProperty(position = 6,value = "交易类型：com.pinshang.qingyun.base.enums.StoreBillTypeEnums")
    private Integer billType;

    @ApiModelProperty("送货日期")
    private Date orderTime;
}

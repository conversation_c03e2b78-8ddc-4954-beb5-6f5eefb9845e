package com.pinshang.qingyun.order.dto.xda.v4;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.pinshang.qingyun.base.configure.codec.DecimalSerializerKeep2;
import com.pinshang.qingyun.base.enums.xda.XdaStoreTypeEnum;
import com.pinshang.qingyun.order.dto.shopcart.v4.ShoppingCartCommodityV4ODTO;
import com.pinshang.qingyun.order.mapper.entry.payType.PayTypeAndTipEntry;
import com.pinshang.qingyun.store.dto.customer.StoreSelectODTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2024/3/7 16:06
 */
@Data
public class XdaPreOrderV4ODTO {
    @ApiModelProperty("客户名称")
    private String storeName;
    @ApiModelProperty("送货地址")
    private String deliveryAddress;

    @ApiModelProperty("联系人")
    private String storeLinkman;
    @ApiModelProperty("联系人手机号")
    private String linkmanMobile;

    @ApiModelProperty("商品详情信息")
    private List<XdaOrderItemAppV4ODTO> commodities;

    @ApiModelProperty("品种合计")
    private Integer varietySum;

    @ApiModelProperty("送达时间")
    private Date orderTime;


    @ApiModelProperty("商品金额(原价总计)")
    @JsonSerialize(using = DecimalSerializerKeep2.class)
    private BigDecimal totalOriginPrice;

    @ApiModelProperty("优惠(优惠总计)")
    @JsonSerialize(using = DecimalSerializerKeep2.class)
    private BigDecimal discountTotal;

    @ApiModelProperty("实付(提交订单按钮上的金额)")
    @JsonSerialize(using = DecimalSerializerKeep2.class)
    private BigDecimal actuallyPaid;

    @ApiModelProperty("配送费默认为0")
    @JsonSerialize(using = DecimalSerializerKeep2.class)
    private BigDecimal freightAmount = BigDecimal.ZERO;

    @ApiModelProperty("账户类型")
    private XdaStoreTypeEnum storeType;
    @ApiModelProperty( "可用额度/授信额度")
    @JsonSerialize(using = DecimalSerializerKeep2.class)
    private BigDecimal currentAmount;
    @ApiModelProperty( "最小充值金额")
    private String minRechargeMoney;
    @ApiModelProperty("备注")
    private String remark;

    @ApiModelProperty("充值方式")
    private List<PayTypeAndTipEntry> payTypeList;

    @ApiModelProperty("1 = 已有1个待支付订单，可到订单列表继续支付")
    private Integer xdaPreOrder = 0;

    @ApiModelProperty("券优惠合计")
    @JsonSerialize(using = DecimalSerializerKeep2.class)
    private BigDecimal couponAmount;

    @ApiModelProperty("活动立减")
    @JsonSerialize(using = DecimalSerializerKeep2.class)
    private BigDecimal activityDiscount;

    @ApiModelProperty("券可用数量")
    private Integer couponAvailableNumber;

    @ApiModelProperty("券已选数量")
    private Integer couponSelectedNumber;

    @ApiModelProperty("用户优惠券Id")
    private Long couponUserId;

    // 送货日期为今天提示语
    private String todayTips;

    @ApiModelProperty("赠品是否存在缺货情况")
    private Boolean isGiftExceedLimit;

    @ApiModelProperty("存在缺货的赠品")
    private List<ShoppingCartCommodityV4ODTO> giftExceedLimitCommodities;

    @ApiModelProperty("赠品不足提示")
    private String notEnoughGiftItemTips;

    @ApiModelProperty("送货时间段")
    private String deliveryTimeRange;

    /**
     * 客户进本信息初始化
     * @param selectODTO
     */
    public void covertStoreBaseInfo(StoreSelectODTO selectODTO){
        storeLinkman = selectODTO.getStoreLinkman();
        linkmanMobile = selectODTO.getLinkmanMobile();
        storeName = selectODTO.getStoreName();
        deliveryAddress = selectODTO.getDeliveryAddress();
    }
}

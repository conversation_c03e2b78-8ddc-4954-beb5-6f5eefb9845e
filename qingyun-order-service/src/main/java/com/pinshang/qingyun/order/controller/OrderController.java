package com.pinshang.qingyun.order.controller;

import com.github.pagehelper.PageInfo;
import com.pinshang.qingyun.base.annotations.RepeatRetryServiceAnno;
import com.pinshang.qingyun.base.api.TokenInfo;
import com.pinshang.qingyun.base.configure.expand.QYAssert;
import com.pinshang.qingyun.base.enums.order.OrderTypeEnum;
import com.pinshang.qingyun.base.util.FastThreadLocalUtil;
import com.pinshang.qingyun.box.utils.SpringUtil;
import com.pinshang.qingyun.order.config.LockConstants;
import com.pinshang.qingyun.order.dto.*;
import com.pinshang.qingyun.order.dto.order.UpdateOrderIDTO;
import com.pinshang.qingyun.order.dto.order.XiaoeTongPushOrderIDTO;
import com.pinshang.qingyun.order.dto.xda.MtCouponDayStatisticsODTO;
import com.pinshang.qingyun.order.mapper.entry.order.ModifyDeliverDateEntry;
import com.pinshang.qingyun.order.mapper.entry.order.OrderListEntry;
import com.pinshang.qingyun.order.mapper.entry.order.OrderListInfoEntry;
import com.pinshang.qingyun.order.mapper.entry.order.OrderLogListEntry;
import com.pinshang.qingyun.order.mapper.entry.store.OutstandingShopEntry;
import com.pinshang.qingyun.order.model.order.PreOrder;
import com.pinshang.qingyun.order.model.order.User;
import com.pinshang.qingyun.order.service.*;
import com.pinshang.qingyun.order.service.order.OrderStatusChangeService;
import com.pinshang.qingyun.order.service.order.XiaoetongOrderService;
import com.pinshang.qingyun.order.service.xda.CouponDayStatisticsService;
import com.pinshang.qingyun.order.util.ThreadLocalUtils;
import com.pinshang.qingyun.order.vo.commodity.CreateHandleOrderRequestVO;
import com.pinshang.qingyun.order.vo.order.*;
import com.pinshang.qingyun.order.vo.pda.PdaCommodityOrderQueryRequestVo;
import com.pinshang.qingyun.order.vo.pda.PdaCommodityOrderQueryResponseVo;
import com.pinshang.qingyun.order.vo.shop.OutstandingShopVo;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import java.text.ParseException;
import java.util.List;

@RestController
@RequestMapping("/order")
@Slf4j
public class OrderController {
	@Autowired
	private OrderService orderService;
	@Autowired
	private GeneratePurchaseOrderService generatePurchaseOrderService;

	@Autowired
	private GrouponOrderService grouponOrderService;

	@Autowired
	private SubOrderService subOrderService;
	@Autowired
	private WeChatSendMessageService weChatSendMessageService;
	@Autowired
	private CloudOrderAllocationService cloudOrderAllocationService;
	@Autowired
	private RedissonClient redissonClient;
	@Autowired
	private CloudGrouponOrderService cloudGrouponOrderService;
	@Autowired
	private CouponDayStatisticsService couponDayStatisticsService;
	@Autowired
	private XiaoetongOrderService xiaoetongOrderService;
	@Autowired
	private OrderStatusChangeService orderStatusChangeService;

	/**
	 * 门店订单查询
	 * @param vo
	 * @return
	 */
	@PostMapping("/findOrderListByPage")
	public PageInfo<OrderListEntry> findOrderListByPage(@RequestBody OrderListRequestVo vo){
		TokenInfo ti = FastThreadLocalUtil.getQY();
		vo.setEnterpriseId(ti.getEnterpriseId());
		if(ti.getStoreId()!= null){
			vo.setStoreId(ti.getStoreId());
		}
		return this.orderService.findOrderListByPage(vo);
	}

	/**
	 * 门店直送预置订单列表
	 * @param vo
	 * @return
	 */
	@PostMapping("/findPreOrderListByPage")
	public PageInfo<OrderListEntry> findPreOrderListByPage(@RequestBody OrderListRequestVo vo){
		return this.orderService.findPreOrderListByPage(vo);
	}

	@GetMapping("/preOrderCancelByOrderId/{orderId}/{isInternal}")
	@RepeatRetryServiceAnno
	public boolean preOrderCancelByOrderId(@PathVariable(value = "orderId",required = false) Long orderId, @PathVariable(value = "isInternal",required = false) Boolean isInternal, @RequestParam(value = "userId",required = false) Long userId) throws Throwable{
		return this.orderService.preOrderCancelByOrderId(orderId, isInternal, userId);
	}
	
	@GetMapping("/orderCancelById/{orderId}/{isInternal}")
	@RepeatRetryServiceAnno
	public boolean orderCancelById(@PathVariable(value = "orderId",required = false) Long orderId, @PathVariable(value = "isInternal",required = false) Boolean isInternal, @RequestParam(value = "userId",required = false) Long userId, @RequestParam(value = "realName",required = false) String realName) throws Throwable{
		return this.orderService.orderCancelById(orderId, isInternal, userId, realName);
	}
	
	@GetMapping("/copyOrderById/{orderId}/{isInternal}")
	public boolean copyOrderById(@PathVariable("orderId") Long orderId, @PathVariable("isInternal") Boolean isInternal){
		return this.orderService.copyOrderById2(orderId, isInternal);
	}
	
	@GetMapping("/copyPreOrderById/{orderId}/{isInternal}")
	public boolean copyPreOrderById(@PathVariable("orderId") Long orderId, @PathVariable("isInternal") Boolean isInternal){
		return this.orderService.copyPreOrderById(orderId, isInternal);
	}

	@RequestMapping(value = "/createOrder", method = RequestMethod.POST)
	@RepeatRetryServiceAnno
    public CreateOrderFilterVo createOrder(@RequestBody List<CreateOrderVo> vos)throws Throwable{
		return orderService.createOrder(vos);
	}


	/**
	 * 直送补货
	 * 创建补货单
	 * @param backOrderVo
	 * @param backOrderVo
	 * @return
	 */
	@PostMapping("/createDirectSendingBackOrder")
	@RepeatRetryServiceAnno
	public boolean createDirectSendingBackOrder(@RequestBody DirectSendingBackOrderVo backOrderVo) throws Throwable {
		if (SpringUtil.isEmpty(backOrderVo.getItems())) {
			return true;
		}

		// 1. 创建预订单
		PreOrder preOrder = orderService.createPreOrder(backOrderVo);
		QYAssert.isTrue(preOrder != null, "直送补单创建失败");

		boolean isSuccesss = false;
		try {
			isSuccesss = orderService.createDirectSendingBackOrder(backOrderVo, preOrder);
		} catch (Exception e) {
			log.error("创建补货单异常", e);
			orderService.deletePreOrder(preOrder.getId());

			StringBuffer sb = new StringBuffer();
			sb.append("创建补货单异常");
			//发送微信模板信息
			weChatSendMessageService.sendWeChatMessage(sb.toString());
		}

		return isSuccesss;
	}

	@GetMapping("/findStoreOrderTimeByStoreId/{storeId}")
	public String findStoreOrderTimeByStoreId(@PathVariable("storeId")String storeId){
		return this.orderService.findStoreOrderTimeByStoreId(storeId);
	}

	/**
	 * 直送单总部审核通过
	 * @param vo
	 * @return
	 * @throws Throwable
	 */
	@RequestMapping(value = "/preOrderCheckSuucess", method = RequestMethod.POST)
	public boolean preOrderCheckSuucess(@RequestBody PreOrderRequestVo vo)throws Throwable{
		ThreadLocalUtils.setOrderType(OrderTypeEnum.DIRECT_AUDIT_ORDER.getCode());
		boolean preOrderCheckSuccess = this.orderService.preOrderCheckSuucess(vo);
		ThreadLocalUtils.remove();
		return preOrderCheckSuccess;
	}

/*	private boolean buildShopShoppingCartToOrder(List<OrderShopShoppingCart> osscList) throws Throwable {
		Boolean flag =false;
		if(SpringUtil.isNotEmpty(osscList)){
			for(OrderShopShoppingCart c : osscList){
				OrderRequestVo orderRequestVo = new OrderRequestVo();
				QYAssert.isTrue(SpringUtil.isNotEmpty(c.getItems()), "ShoppingCartItems  is not null !");
				orderRequestVo.setStoreId(c.getStoreId().toString());
				orderRequestVo.setOrderTime(DateTimeUtil.getNowDayDate());
				orderRequestVo.setEnterpriseId(c.getEnterpriseId());
				orderRequestVo.setUserId(c.getCreateId());
				orderRequestVo.setLogisticsModel(c.getLogisticsModel());
				orderRequestVo.setSupplierId(c.getSupplierId());
				orderRequestVo.setWarehouseId(c.getWarehouseId());

				List<OrderItemRequestVo> itemsList = new ArrayList<OrderItemRequestVo>();
				c.getItems().forEach(i->{
					OrderItemRequestVo item = new OrderItemRequestVo();
					item.setProductId(i.getCommodityId().toString());
					item.setProductNum(i.getCommodityNum());
					itemsList.add(item);
				});
				orderRequestVo.setItemsList(itemsList);
				Boolean success =orderService.createPhdOrder(orderRequestVo);
				if(success){
					flag =true;
				}
			}
		}else{
			QYAssert.isTrue(false, "无有效商品(物流模式/默认供应商/默认仓库),无法生成铺货单,请检查后再试!");
		}
		return flag;
	}*/

	@PostMapping("/generatePurchaseOrder")
	public boolean generatePurchaseOrder(){
		return generatePurchaseOrderService.generatePurchaseOrder();
	}
	
	@RequestMapping(value = "/createPurchaseOrderByTime", method = RequestMethod.GET)
	public boolean createPurchaseOrderByTime(@RequestParam(value = "times",required = false)String times) throws ParseException{
		return  orderService.createPurchaseOrderByTime(times);
	}

	@PostMapping("/outstanding")
	public PageInfo<OutstandingShopEntry> outstandingStore(@RequestBody OutstandingShopVo vo) {
		QYAssert.notNull(vo,"查询未订货客户参数为空!");
		if(vo.getEmployeeId() != null && vo.getEmployeeId().longValue() >0){
			return orderService.findOutstandingShopList(vo);
		}
		QYAssert.isTrue(false,"查询未订货客户参数异常:"+vo.toString());
		return null;
	}

	@PostMapping(value ="/queryModifyDeliverDateList")
	public PageInfo<ModifyDeliverDateEntry> queryModifyDeliverDateList(@RequestBody ModifyDeliverDateVo vo){
		return orderService.queryModifyDeliverDateList(vo);
	}

	@RequestMapping(value ="/modifyDeliverDate", method = RequestMethod.PUT)
	public void modifyDeliverDate(String orderCode, String date) {
		orderService.modifyDeliverDate(orderCode, date);
	}

	/**
	 *结算消息同步
	 * @param sourceCode
	 * @param sourceType
	 */
	@RequestMapping(value ="/fixedSettleOrder", method = RequestMethod.GET)
	public void fixedSettleOrder(@RequestParam(value = "sourceCode",required = false)String sourceCode,@RequestParam(value = "sourceType",required = false) String sourceType,@RequestParam("topic")String topic) {
		orderService.fixedSettleOrder(sourceCode,sourceType,topic);
	}

	//根据购物车ID获取购物车详细信息
	@RequestMapping(value ="/findShoppingCartItems", method = RequestMethod.GET)
	public List<CreateOrderVo.CreateOrderItemIDTO> findShoppingCartItems(@RequestParam(value = "storeId",required = false)Long storeId,@RequestParam("shoppingCartId") Long shoppingCartId) {
		return orderService.findShoppingCartItems(storeId,shoppingCartId);
	}

	//(手持)端提交订单
	@RequestMapping( value = "/createHandleOrder", method = RequestMethod.POST)
	@RepeatRetryServiceAnno
	public CreateOrderFilterVo createHandleOrder(@RequestBody CreateHandleOrderRequestVO vo){
		RLock lock = redissonClient.getLock(LockConstants.createOrder + vo.getUserId());
		if (lock.tryLock()) {
			try {
				return orderService.createHandleOrder(vo);
			} finally {
				lock.unlock();
			}
		}else {
			QYAssert.isFalse("系统繁忙,请勿频繁操作!");
		}
		return new CreateOrderFilterVo();
	}


	/**
	 * 未提交的订单消息提醒
	 */
	@RequestMapping(value ="/orderNotCommitTips", method = RequestMethod.GET)
	public void orderNotCommitTips() {
		orderService.orderNotCommitTips();
	}


	/**
	 * 查询订单日志
	 * @param orderId
	 * @return
	 */
	@RequestMapping(value = "/logs/{orderId}", method = RequestMethod.GET)
	List<OrderLogListEntry> getOrderLogList(@PathVariable("orderId") Long orderId){
		return orderService.getOrderLogList(orderId);
	}


	@RequestMapping(value = "/createOrderYear", method = RequestMethod.POST)
	@RepeatRetryServiceAnno
	public void createOrderYear()throws Throwable{
		orderService.createOrderYear();
	}

	@RequestMapping(value = "/createGrouponOrder", method = RequestMethod.POST)
	public Boolean createGrouponOrder(@RequestParam(value = "timeStamp",required = false) String timeStamp) {
		return grouponOrderService.createGrouponOrder(timeStamp);
	}


	@RequestMapping(value = "/createCloudGrouponOrder", method = RequestMethod.POST)
	public Boolean createCloudGrouponOrder(@RequestParam(value = "timeStamp",required = false) String timeStamp) {
		return cloudGrouponOrderService.createCloudGrouponOrder(timeStamp);
	}

	/**
	 * 出库消息记录表(是否发结算消息) job
	 * @param beginTime  yyyy-MM-dd HH:mm:ss
	 * @param endTime  yyyy-MM-dd HH:mm:ss
	 * @return
	 */
	@RequestMapping(value = "/stockOutSettleMsg", method = RequestMethod.POST)
	public Boolean stockOutSettleMsg(@RequestParam(value = "beginTime",required = false) String beginTime, @RequestParam(value = "endTime",required = false) String endTime){
		subOrderService.stockOutSettleMsg(beginTime,endTime);
		return true;
	}

	@PostMapping(value = "/getSumRealQuantity")
	public Double getSumRealQuantity(@RequestParam(value = "subOrderId",required = false)Long subOrderId){
		Double result = orderService.getSumRealQuantity(subOrderId);
		return result == null ? 0.0 :result;
	}


	@ApiOperation(value = " 门店配送单打印", notes = " 门店配送单打印", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
	@RequestMapping(value = "/printOrderInfo", method = RequestMethod.GET)
	public PrintOrderODTO printOrderInfo(@RequestParam(value = "orderTime",required = false) String orderTime) {
		TokenInfo tokenInfo = FastThreadLocalUtil.getQY();
		return orderService.printOrderInfo(tokenInfo.getStoreId(),tokenInfo.getRealName(),orderTime);
	}


	/****
	 * 场景：
	 * 修改客户的所属公司后，需将改该客户下的订单修改成对应所属公司
	 * 修改订单的条件为：客户下的订单、订单送货时间>当前时间、订单所属公司不等于客户所属公司
	 * @return
	 */
	@ApiOperation(value = "根据客户修改订单对应的所属公司", notes = " 根据客户修改订单对应的所属公司", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
	@RequestMapping(value = "/modifyOrderCompany", method = RequestMethod.POST)
	public Integer modifyOrderCompanyByStore(@RequestParam(value = "storeId",required = false) Long storeId,@RequestParam(value = "companyId",required = false) Long companyId) {
		QYAssert.isTrue(storeId!=null,"客户不能为空!");
		QYAssert.isTrue(companyId!=null,"公司不能为空!");
		return orderService.modifyOrderCompanyByStore(storeId,companyId);
	}

	/**
	 * 云超配货单分配客户提交订单
	 * @param list
	 * @return
	 * @throws Throwable
	 */
	@RequestMapping( value = "/cloudOrderAllocationCreateOrder", method = RequestMethod.POST)
	public List<CloudOrderAllocationResultOrderODTO> cloudOrderAllocationCreateOrder(@RequestBody List<CloudOrderAllocationSaveIDTO>  list)throws Throwable{
		return cloudOrderAllocationService.cloudOrderAllocationCreateOrder(list);
	}

    /***
     * 根据订单查询订单明细
     * @param vo
     */
    @PostMapping(value = "/selectOrderListGiftByOrderIdAndCommodityIdList")
    public List<OrderListInfoEntry> selectOrderListGiftByOrderIdAndCommodityIdList(@RequestBody OrderListOrderIdAndCommVO vo){
        return orderService.selectOrderListGiftByOrderIdAndCommodityIdList(vo);
    }

	@PostMapping(value = "/updateSettleOrderDateByDate")
	public Integer updateSettleOrderDateByDate(@RequestParam("orderTime") String orderTime){
		return orderService.updateSettleOrderDateByDate(orderTime);
	}

	@PostMapping(value = "/updateStoreTypeIdByDate")
	public Integer updateStoreTypeIdByDate(@RequestParam("orderTime") String orderTime){
		return orderService.updateStoreTypeIdByDate(orderTime);
	}

	@Deprecated
	@PostMapping(value = "/saveOrderDeductions")
	public Boolean saveOrderDeductions(@RequestBody SaveOrderDeductionsIDTO idto){
		return orderService.saveOrderDeductions(idto);
	}

	/**
	 * 批量取消当天单（new）
	 * @param idto
	 * @return
	 */
	@ApiOperation(value = "批量取消当天单（new）", notes = " 批量取消当天单（new）", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
	@PostMapping("/batchCancelOrder")
	public OrderBatchCancelODTO batchCancelOrder(@RequestBody OrderBatchCancelIDTO idto){
		TokenInfo token = FastThreadLocalUtil.getQY();
		QYAssert.isTrue(null != token,"未登录，无法批量取消");
		User user = new User();
		user.setId(token.getUserId());
		user.setRealName(token.getRealName());
		idto.setUser(user);
		return orderService.batchCancelOrder(idto);
	}

	@ApiOperation(value = "PDA门店订货查询", notes = "PDA门店订货查询", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
	@PostMapping("/findPdaCommodityOrder")
	public PdaCommodityOrderQueryResponseVo findPdaCommodityOrder(@RequestBody PdaCommodityOrderQueryRequestVo vo){
		return orderService.findPdaCommodityOrder(vo);
	}

	@ApiOperation(value = "优惠券日统计-查询已核销数量、已核销订单总金额、折扣总金额", notes = "优惠券日统计-查询已核销数量、已核销订单总金额、折扣总金额", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
	@RequestMapping(value = "/queryOrderCouponList", method = RequestMethod.GET)
	public List<MtCouponDayStatisticsODTO> queryOrderCouponList(@RequestParam(value = "beginTime",required = false) String beginTime, @RequestParam(value = "endTime",required = false) String endTime) {
		return couponDayStatisticsService.queryOrderCouponList(beginTime, endTime);
	}

	/**
	 * 小鹅通推送订单，创建B端订单
	 * @param list
	 * @return
	 * @throws Throwable
	 */
	@RequestMapping( value = "/createXiaoeTongOrder", method = RequestMethod.POST)
	public List<XiaoeTongPushOrderIDTO> createXiaoeTongOrder(@RequestBody List<XiaoeTongPushOrderIDTO> xiaoeTongPushOrderList){
		return xiaoetongOrderService.createXiaoeTongOrder(xiaoeTongPushOrderList);
	}

	/**
	 * 1. 计划销售订单，顺丰通知清美入库后，大仓修改订单状态为已完成
	 * 2. B端全国订单，订单进大仓后，大仓修改订单状态为出库中
	 * 3. B端全国订单，顺丰审核失败，大仓发起取消订单后，修改订单状态为已取消
	 * @param updateOrderIDTO
	 * @return
	 */
	@RequestMapping( value = "/updateOrderStatus", method = RequestMethod.POST)
	public Boolean updateOrderStatus(@RequestBody UpdateOrderIDTO updateOrderIDTO){
		return orderStatusChangeService.updateOrderStatus(updateOrderIDTO);
	}
}

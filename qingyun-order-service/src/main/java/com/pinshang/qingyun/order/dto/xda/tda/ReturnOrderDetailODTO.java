/*
 * tramy.cn Inc.
 * Copyright (c) 2021-2024 All Rights Reserved.
 */
package com.pinshang.qingyun.order.dto.xda.tda;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.pinshang.qingyun.order.dto.xda.v4.TdaDeliveryTimeRangeODTO;
import com.pinshang.qingyun.renderer.annotation.FieldRender;
import com.pinshang.qingyun.renderer.constant.RenderFieldHelper;
import com.pinshang.qingyun.renderer.enums.FieldTypeEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <p>
 * 退货单详情
 * </p>
 *
 * <AUTHOR> shenyang
 * @version : 1.0.0
 * @history : modify history
 * <author>              <time>              <version>              <desc>
 * @since : 2024/05/11 10:09
 */
@Data
public class ReturnOrderDetailODTO {
    /**
     * id
     */
    @ApiModelProperty(value = "id")
    private Long id;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    @JsonFormat(pattern="yyyy-MM-dd",timezone = "GMT+8")
    private Date createTime;

    /**
     * 退货单编号，唯一标识每个退货单
     */
    @ApiModelProperty(value = "退货单号")
    private String returnOrderCode;

    /**
     * 退货单序列号，前端展示用
     */
    @ApiModelProperty(value = "退货单序列号，前端展示用")
    private String returnOrderSeq;

    /**
     * 退货单状态：1-待审核、2-待取货、3-取货失败、4-待大仓确认、5-已取消、6-已完成、7-审核不通过
     */
    @ApiModelProperty(value = "状态:1-待审核、2-待取货、3-取货失败、4-待大仓确认、5-已取消、6-已完成、7-审核不通过")
    private Integer status;

    /**
     * 退货单状态描述：1-待审核、2-待取货、3-取货失败、4-待大仓确认、5-已取消、6-已完成、7-审核不通过
     */
    @ApiModelProperty(value = "状态描述:1-待审核、2-待取货、3-取货失败、4-待大仓确认、5-已取消、6-已完成、7-审核不通过")
    private String statusName;

    /**
     * 退货来源：1-app客户申请，2-品鲜后台投诉，3-订单取消，4-订单配送失败
     */
    @ApiModelProperty(value = "退货来源：1-app客户申请，2-品鲜后台投诉，3-订单取消，4-订单配送失败")
    private Integer returnSource;

    /**
     * 送货日期
     */
    @ApiModelProperty(value = "送货日期")
    @JsonFormat(pattern="yyyy-MM-dd",timezone = "GMT+8")
    private Date deliveryTime;

    /**
     * 客户类型名称
     */
    @ApiModelProperty(value = "客户类型名称")
    private String storeTypeName;

    /**
     * 客户id
     */
    @ApiModelProperty(value = "客户id")
    private Long storeId;

    /**
     * 客户编码
     */
    @ApiModelProperty(value = "客户编码")
    private String storeCode;
    /**
     * 客户名称
     */
    @ApiModelProperty(value = "客户名称")
    private String storeName;

    /**
     * 手机号
     */
    @ApiModelProperty(value = "手机号")
    private String phone;

    /**
     * 物流中心id,pinshang_tms.t_tms_logistics_center.id
     */
    @ApiModelProperty(value = "物流中心id")
    private Long logisticsCenterId;

    /**
     * 物流中心
     */
    @ApiModelProperty(value = "物流中心")
    @FieldRender(fieldType = FieldTypeEnum.LOGISTICS_CENTER, fieldName = RenderFieldHelper.LogisticsCenter.logisticsCenterName, keyName = "logisticsCenterId")
    private String logisticsCenterName;

    /**
     * 送货地址
     */
    @ApiModelProperty(value = "收货地址")
    private String deliveryAddress;

    /**
     * 退货类型：1-客户退货，2-订单取消，3-配送失败
     */
    @ApiModelProperty(value = "退货类型")
    private Integer returnType;

    /**
     * 退货类型：1-客户退货，2-订单取消，3-配送失败
     */
    @ApiModelProperty(value = "退货类型：1-客户退货，2-订单取消，3-配送失败")
    private String returnTypeName;

    /**
     * 业务类型：10-通达销售
     */
    @ApiModelProperty(value = "业务类型：10-通达销售")
    private Integer businessType;

    /**
     * 业务类型：10-通达销售
     */
    @ApiModelProperty(value = "业务类型：10-通达销售")
    private String businessTypeName;

    /**
     * 配送批次
     */
    @ApiModelProperty(value = "配送批次")
    private String deliveryBatch;

    /**
     * 配送批次名称
     */
    @ApiModelProperty(value = "配送批次名称")
    private String deliveryBatchName;

    /**
     * 取货日期
     */
    @ApiModelProperty(value = "取货日期")
    @JsonFormat(pattern="yyyy-MM-dd",timezone = "GMT+8")
    private Date pickUpTime;

    /**
     * 取货时间段
     */
    @ApiModelProperty(value = "取货时间段")
    private String pickUpTimeRange;

    /**
     * 取货时间和日期集合
     */
    @ApiModelProperty(value = "取货时间和日期集合")
    private List<TdaDeliveryTimeRangeODTO> pickUpTimeRangeList;

    /**
     * 来源订单id
     */
    @ApiModelProperty(value = "来源订单id")
    private Long sourceOrderId;

    /**
     * 来源单号，即相关的订单编号
     */
    @ApiModelProperty(value = "来源单号")
    private String sourceOrderCode;

    /**
     * 退货订单类型：1-退货，2-差异（少货/多货）
     */
    private Integer returnOrderType;

    /**
     * 退货单明细
     */
    @ApiModelProperty(value = "退货单明细")
    private List<ReturnOrderItemODTO> returnOrderItemList;

    /**
     * 申请总金额
     */
    private BigDecimal totalApplyMoney;

    /**
     * 司机ID（用户ID）
     */
    private Long driverId;


    /**
     * 审核商品金额
     */
    private BigDecimal totalCheckMoney;

    /**
     * 审核时间
     */
    private Date checkTime;


    /**
     * 审核人id
     */
    private Long checkUserId;


    @ApiModelProperty(value = "备注")
    private String remark;

    /**
     * 送货/取货  完成时间
     */
    @ApiModelProperty(value = "取货完成时间")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date deliveryEndTime;

}

package com.pinshang.qingyun.order.vo.shop;

import com.pinshang.qingyun.base.page.Pagination;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class MdShopOrderSettingVo extends Pagination {

    private Long id;

    private Long enterpriseId;//企业ID

    private Long commodityId;

    private String commodityCode;

    private String commodityName;//商品名称

    private String commoditySpec;//商品规格

    private Integer shopType;
    private String shopTypeName;

    private Long supplierId;

    private String supplierCode;

    private String supplierName;//供应商名称

    private Integer logisticsModel;//物流模式

    private String deleveryTimeRange;//配送范围

    private Integer changePriceStatus;//是否可变价

    private Date createTime;

    private Date updateTime;

    private Long createId;

    private Long updateId;

    private String commodityCodeOrName;
    private String logisticsModelName;//物流模式
    private String changePriceStatusName;//是否可变价

    /** 大类ID **/
    private Long cateId1;

    /** 中类ID **/
    private Long cateId2;

    /** 小类ID **/
    private Long cateId3;
}

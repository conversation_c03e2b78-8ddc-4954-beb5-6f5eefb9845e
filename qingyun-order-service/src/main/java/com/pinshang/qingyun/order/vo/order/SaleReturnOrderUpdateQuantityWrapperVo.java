package com.pinshang.qingyun.order.vo.order;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2018/10/16 10:54.
 * @blog http://linuxsogood.org
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class SaleReturnOrderUpdateQuantityWrapperVo {

    /**退货单ID*/
    private Long id;
    /** 修复人ID**/
    private Long updateId;
    private List<SaleReturnOrderItemQuantityWrapperVo> itemList;
}

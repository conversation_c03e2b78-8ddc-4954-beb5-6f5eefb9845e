package com.pinshang.qingyun.order.validation;

import com.pinshang.qingyun.base.configure.expand.QYAssert;
import com.pinshang.qingyun.base.enums.BusinessTypeEnums;
import com.pinshang.qingyun.box.utils.DateUtil;
import com.pinshang.qingyun.order.dto.bcountry.PlanCommodityODTO;
import com.pinshang.qingyun.order.dto.bcountry.PlanCommodityQueryIDTO;
import com.pinshang.qingyun.order.dto.cup.OrderRequestDto;
import com.pinshang.qingyun.order.mapper.StoreMapper;
import com.pinshang.qingyun.order.mapper.bcountry.PlanCommodityMapper;
import com.pinshang.qingyun.order.model.store.Store;
import com.pinshang.qingyun.order.service.tms.OrderTmsService;
import com.pinshang.qingyun.order.util.OrderTimeUtil;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

@Component
@RequiredArgsConstructor
public class PlanOrderValidator extends OrderValidationHandler{

    private final PlanCommodityMapper planCommodityMapper;
    
    private final StoreMapper storeMapper;

    private final OrderTmsService orderTmsService;
    @Override
    public OrderValidationResult validate(OrderRequestDto orderRequestDto) {

        if(Objects.equals(orderRequestDto.getPlanOrderType(),1)){
            //客户编码校验：仅可录入计划销售业务类型客户
            validStoreBusinessType(orderRequestDto);

            //商品编码校验：必须在计划商品同步
            validPlanCommodity(orderRequestDto);

            //送货日期校验：计划销售不支持当天下单
            validOrderTime(orderRequestDto);

            //物流公司校验//送货地址校验
            validLogisticsCarrier(orderRequestDto);


        }
       return validateNext(orderRequestDto);
    }

    private void validLogisticsCarrier(OrderRequestDto orderRequestDto) {
        QYAssert.notNull(orderRequestDto.getLogisticsCarrierCode(),"物流承运商code为空");
        orderTmsService.checkTransportFlow(orderRequestDto.getStoreId(),BusinessTypeEnums.PLAN_SALE.getCode(),
                orderRequestDto.getDeliveryBatch(),orderRequestDto.getLogisticsCenterId(),orderRequestDto.getLogisticsCarrierCode());
    }

    private void validOrderTime(OrderRequestDto orderRequestDto) {
        String orderTime = orderRequestDto.getOrderTime();
        if(OrderTimeUtil.isTheSameDay(new Date(), DateUtil.parseDate(orderTime,"yyyy-MM-dd"))){
            QYAssert.isFalse("B端全国业务类型的客户不支持当天下单！");
        }
    }

    private void validPlanCommodity(OrderRequestDto orderRequestDto) {
        List<Long> commodityIdList = orderRequestDto.getItemsList()
                .stream()
                .map(item -> Long.valueOf(item.getProductId().trim()))
                .collect(Collectors.toList());

        PlanCommodityQueryIDTO planCommodityQueryIDTO = new PlanCommodityQueryIDTO();
        planCommodityQueryIDTO.setCommodityIdList(commodityIdList);
        planCommodityQueryIDTO.setSyncStatus(1);
        List<PlanCommodityODTO> planCommodityODTOList = planCommodityMapper.queryPlanCommodityList(planCommodityQueryIDTO);
        Map<String, PlanCommodityODTO> planCommodityMap = planCommodityODTOList.stream().collect(Collectors.toMap(PlanCommodityODTO::getCommodityId, Function.identity()));
        for (Long commodityId : commodityIdList) {
            if(!planCommodityMap.containsKey(String.valueOf(commodityId))){
                QYAssert.isFalse("存在非计划销售商品或商品未同步！");
            }
        }
    }

    private void validStoreBusinessType(OrderRequestDto orderRequestDto) {
        Long storeId = orderRequestDto.getStoreId();
        Store store = storeMapper.selectByPrimaryKey(storeId);
        Integer businessType = store.getBusinessType();
        QYAssert.isTrue(Objects.equals(businessType, BusinessTypeEnums.PLAN_SALE.getCode()),"非计划销售类型客户！");
    }
}

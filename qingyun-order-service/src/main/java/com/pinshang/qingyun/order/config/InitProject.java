package com.pinshang.qingyun.order.config;

import com.pinshang.qingyun.order.job.GenerateCreateTakeCardOrderTasks;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.stereotype.Component;

@Component
public class InitProject implements ApplicationRunner {

    @Autowired
    private GenerateCreateTakeCardOrderTasks generateCreateTakeCardOrderTasks;

    @Override
    public void run(ApplicationArguments applicationArguments){
        //generateCreateTakeCardOrderTasks.scheduleJobs();
    }
}

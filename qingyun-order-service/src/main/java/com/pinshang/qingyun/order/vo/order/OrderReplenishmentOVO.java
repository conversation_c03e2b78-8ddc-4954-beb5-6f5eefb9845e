package com.pinshang.qingyun.order.vo.order;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class OrderReplenishmentOVO {

    private String deliveryDate;//送货日期

    private String shopId;

    private String shopCode;//门店编码

    private String shopName;//门店名称

    private String storeId;

    private BigDecimal totalAmount;//金额合计

    private List<OrderReplenishmentCommodityOVO> commodityList;//补货明细

}

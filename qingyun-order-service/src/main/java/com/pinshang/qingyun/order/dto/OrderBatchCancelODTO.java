package com.pinshang.qingyun.order.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.collections.CollectionUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * @ClassName OrderBatchCancelODTO
 * <AUTHOR>
 * @Date 2024/06/27 16:46
 * @Description OrderBatchCancelODTO
 * @Version 1.0
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class OrderBatchCancelODTO {

    @ApiModelProperty("取消失败订单code列表")
    private List<String> cancelFailErrors;

    public void addCancelFailError(List<String> errors){
        if(CollectionUtils.isEmpty(cancelFailErrors)){
            cancelFailErrors = new ArrayList<>();
        }
        cancelFailErrors.addAll(errors);
    }
}

package com.pinshang.qingyun.order.util;


import com.pinshang.qingyun.base.enums.order.OrderTypeEnum;
import com.pinshang.qingyun.order.vo.order.FilterTipVo;

import java.util.ArrayList;
import java.util.List;

/**
 * summary.
 * <p>
 * detailed description
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2019-05-10
 */
public class ThreadLocalUtils {
    private static ThreadLocal<Boolean> threadLocal = new ThreadLocal<>();
    private static ThreadLocal<List<FilterTipVo>> threadLocalTip = new ThreadLocal<>();
    private static ThreadLocal<Boolean> xdThreadLocal = new ThreadLocal<>();
    private static ThreadLocal<Boolean> adminThreadLocal = new ThreadLocal<>();
    private static ThreadLocal<Integer> shoppingCartSourceThreadLocal = new ThreadLocal<>();
    private static ThreadLocal<Boolean> groupOrderThreadLocal = new ThreadLocal<>();
    private static ThreadLocal<Integer> orderTypeThreadLocal = new ThreadLocal<>(); // 订单类型 com.pinshang.qingyun.order.enums.OrderType

    public static Boolean get() {
        return threadLocal.get() == null ? false : threadLocal.get();
    }

    public static void set(Boolean b) {
        threadLocal.set(b);
    }

    public static List<FilterTipVo> getFilterTips() {
        return threadLocalTip.get() == null ? new ArrayList<>() : threadLocalTip.get();
    }

    public static void setFilterTips(List<FilterTipVo> filterTips) {
        threadLocalTip.set(filterTips);
    }

    public static Boolean getXd() {
        return xdThreadLocal.get() == null ? false : xdThreadLocal.get();
    }

    public static void setXd(Boolean b) {
        xdThreadLocal.set(b);
    }

    public static Boolean getAdmin() {
        return adminThreadLocal.get() == null ? false : adminThreadLocal.get();
    }

    public static void setAdmin(Boolean b) {
        adminThreadLocal.set(b);
    }

    public static int getAdminStatus(){
        return getAdmin()?1:0;
    }

    public static Integer getShoppingCartSource() {
        return shoppingCartSourceThreadLocal.get() == null ? 0 : shoppingCartSourceThreadLocal.get();
    }
    public static void setShoppingCartSource(Integer b) {
        shoppingCartSourceThreadLocal.set(b);
    }
    public static Boolean getGroupOrder() {
        return groupOrderThreadLocal.get() == null ? false : groupOrderThreadLocal.get();
    }
    public static void setGroupOrder(Boolean b) {
        groupOrderThreadLocal.set(b);
    }

    public static Integer getOrderType() { //默认为10，门店订单
        return orderTypeThreadLocal.get() == null ? OrderTypeEnum.STORE_ORDER.getCode() : orderTypeThreadLocal.get();
    }
    public static void setOrderType(Integer b) {
        orderTypeThreadLocal.set(b);
    }

    public static void remove(){
        threadLocal.remove();
        threadLocalTip.remove();
        xdThreadLocal.remove();
        adminThreadLocal.remove();
        shoppingCartSourceThreadLocal.remove();
        groupOrderThreadLocal.remove();
        orderTypeThreadLocal.remove();
    }

}

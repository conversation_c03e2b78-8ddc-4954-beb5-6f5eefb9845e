package com.pinshang.qingyun.order.model.shop;


import lombok.Data;

import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;

@Entity
@Table(name = "t_md_shop_order_setting_log")
@Data
public class MdShopOrderSettingLog {
    @Id
    private Long id;

    private Integer opreateType;//0：新增1:删除  2：修改

    private String commodityCode;

    private String commodityName;//商品名称

    private String commoditySpec;//商品规格

    private Integer shopType;

    private Long supplierId;

    private String supplierCode;

    private String supplierName;//供应商名称

    private Integer fromLogisticsModel;//物流模式

    private Integer toLogisticsModel;

    private String fromDeleveryTimeRange;//配送范围

    private String toDeleveryTimeRange;

    private Integer fromChangePriceStatus;//是否可变价

    private Integer toChangePriceStatus;

    private Date createTime;

    private Long createId;

}

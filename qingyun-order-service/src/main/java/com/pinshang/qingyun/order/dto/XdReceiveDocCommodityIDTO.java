package com.pinshang.qingyun.order.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * @Author: sk
 * @Date: 2020/3/17
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class XdReceiveDocCommodityIDTO {

    private Long userId;

    @ApiModelProperty("单据id")
    private String docId;

    @ApiModelProperty("单据号")
    private String docCode;

    @ApiModelProperty("前置仓id")
    private Long shopId;

    @ApiModelProperty("前置仓名称")
    private String shopName;


    private String commodityId;
    private String commodityCode;
    private String commodityName;

    private BigDecimal price;
    @ApiModelProperty("收货正常品份数")
    private BigDecimal normalShares;

    @ApiModelProperty("实收正常品数量")
    private BigDecimal normalQuantity;


    @ApiModelProperty("收货异常品份数")
    private BigDecimal abnormalShares;

    @ApiModelProperty("收货异常品数量")
    private BigDecimal abnormalQuantity;



    @ApiModelProperty("包装规格")
    private BigDecimal commodityPackageSpec;

    @ApiModelProperty("货位")
    private String shelfNo;

    private Long shelfId;

    public BigDecimal getNormalShares() {
        if(null == normalShares){
            return BigDecimal.ZERO;
        }
        return normalShares;
    }

    public BigDecimal getNormalQuantity() {
        if(null == normalQuantity){
            return BigDecimal.ZERO;
        }
        return normalQuantity;
    }

    public BigDecimal getAbnormalShares() {
        if(null == abnormalShares){
            return BigDecimal.ZERO;
        }
        return abnormalShares;
    }

    public BigDecimal getAbnormalQuantity() {
        if(null == abnormalQuantity){
            return BigDecimal.ZERO;
        }
        return abnormalQuantity;
    }


    @ApiModelProperty("01 散装  02 整包")
    private String commodityPackageKind;

    @ApiModelProperty("是否称重0-不称量,1-称重")
    private Integer isWeight;

    public Integer getCommodityType() {
        if(isWeight == 0){ //标品
            return 1;
        }
        if(isWeight == 1 && "01".equals(commodityPackageKind)){//散称商品
            return 2;
        }
        if(isWeight == 1 && "02".equals(commodityPackageKind)){//称重包装品
            return 3;
        }
        return null;
    }
}

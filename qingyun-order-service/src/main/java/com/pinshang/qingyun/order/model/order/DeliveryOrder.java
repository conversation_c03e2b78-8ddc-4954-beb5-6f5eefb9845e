package com.pinshang.qingyun.order.model.order;

import com.pinshang.qingyun.base.po.BaseTimePO;
import lombok.Data;

import javax.persistence.Entity;
import java.util.Date;

@Entity
@Data
public class DeliveryOrder extends BaseTimePO {


	/** 发货单号 */
	private String orderCode;

	/** 客户id */
	private Long storeId;

	/** 子订单id */
	private Long subOrderId;

	/** 仓库ID */
	private Long warehouseId;

	/** 0=生成拣货单,1=已生成拣货单 */
	private Integer status;

	//发货单类型,业务类型:0-采购, 1-调拨, 2-销售退货
	private Integer type;

	/*物流模式 0-直送, 1-配送, 2-直通*/
	private Integer logisticsModel;
	//品种合计
	private Integer varietyTotal;
	//供应商id
	private Long supplierId;

	//配送批次：0-无配送批次, 1-1配，2-2配，3-3配，9-临时批次
	private Integer deliveryBatch;

	//送货日期
	private Date orderTime;

	private Long referOrderId;
	private String referOrderCode;

	private Long enterpriseId;
	private Long createId;
	private Long updateId;

}
/*
 * tramy.cn Inc.
 * Copyright (c) 2021-2024 All Rights Reserved.
 */
package com.pinshang.qingyun.order.dto.xda.tda;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.pinshang.qingyun.renderer.annotation.FieldRender;
import com.pinshang.qingyun.renderer.constant.RenderFieldHelper;
import com.pinshang.qingyun.renderer.enums.FieldTypeEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 退货单列表
 * </p>
 *
 * <AUTHOR> shenyang
 * @version : 1.0.0
 * @history : modify history
 * <author>              <time>              <version>              <desc>
 * @since : 2024/05/11 10:09
 */
@Data
public class WarehousePendingDetailODTO {
    /**
     * id
     */
    @ApiModelProperty(value = "id")
    private Long id;


    /**
     * 送货日期
     */
    @ApiModelProperty(value = "送货日期")
    @JsonFormat(pattern="yyyy-MM-dd",timezone = "GMT+8")
    private Date deliveryTime;



    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    @JsonFormat(pattern="yyyy-MM-dd",timezone = "GMT+8")
    private Date createTime;

    /**
     * 退货单编号，唯一标识每个退货单
     */
    @ApiModelProperty(value = "退货单号")
    private String returnOrderCode;

    /**
     * 退货单序列号，前端展示用
     */
    @ApiModelProperty(value = "退货单序列号，前端展示用")
    private String returnOrderSeq;

    /**
     * 物流中心id,pinshang_tms.t_tms_logistics_center.id
     */
    @ApiModelProperty(value = "物流中心id")
    private Long logisticsCenterId;

    /**
     * 物流中心
     */
    @ApiModelProperty(value = "物流中心")
    @FieldRender(fieldType = FieldTypeEnum.LOGISTICS_CENTER, fieldName = RenderFieldHelper.LogisticsCenter.logisticsCenterName, keyName = "logisticsCenterId")
    private String logisticsCenterName;


    /**
     * 退货类型：1-客户退货，2-订单取消，3-配送失败
     */
    @ApiModelProperty(value = "退货类型")
    private Integer returnType;

    /**
     * 退货类型：1-客户退货，2-订单取消，3-配送失败
     */
    @ApiModelProperty(value = "退货类型：1-客户退货，2-订单取消，3-配送失败")
    private String returnTypeName;

    /**
     * 手机号
     */
    @ApiModelProperty(value = "手机号")
    private String phone;


    /**
     * 送货地址
     */
    @ApiModelProperty(value = "收货地址")
    private String deliveryAddress;

    /**
     * 退货单状态：1-待审核、2-待取货、3-取货失败、4-待大仓确认、5-已取消、6-已完成、7-审核不通过
     */
    @ApiModelProperty(value = "状态:1-待审核、2-待取货、3-取货失败、4-待大仓确认、5-已取消、6-已完成、7-审核不通过")
    private Integer status;

    /**
     * 退货单状态描述：1-待审核、2-待取货、3-取货失败、4-待大仓确认、5-已取消、6-已完成、7-审核不通过
     */
    @ApiModelProperty(value = "状态描述:1-待审核、2-待取货、3-取货失败、4-待大仓确认、5-已取消、6-已完成、7-审核不通过")
    private String statusName;


    /**
     * 取货日期
     */
    @ApiModelProperty(value = "取货日期")
    @JsonFormat(pattern="yyyy-MM-dd",timezone = "GMT+8")
    private Date pickUpTime;

    /**
     * 取货时间段
     */
    @ApiModelProperty(value = "取货时间段")
    private String pickUpTimeRange;

    /**
     * 取货时间和日期映射
     */
    @ApiModelProperty(value = "取货时间和日期映射")
    private Map<String,String> pickUpTimeRangeMap;


    /**
     * 来源订单id
     */
    @ApiModelProperty(value = "来源订单id")
    private Long sourceOrderId;

    /**
     * 来源单号，即相关的订单编号
     */
    @ApiModelProperty(value = "来源单号")
    private String sourceOrderCode;





    /**
     * 退货订单类型：1-退货，2-差异（少货/多货）
     */
    private Integer returnOrderType;

    /**
     * 退货单明细
     */
    @ApiModelProperty(value = "退货单明细")
    private List<ReturnOrderItemODTO> returnOrderItemList;

}

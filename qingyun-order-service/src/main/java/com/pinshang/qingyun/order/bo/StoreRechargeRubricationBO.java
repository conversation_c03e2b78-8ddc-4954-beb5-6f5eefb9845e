package com.pinshang.qingyun.order.bo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@Data
@Builder
public class StoreRechargeRubricationBO {
    @ApiModelProperty("客户ID")
    private Long storeId;
    @ApiModelProperty("充值金额")
    private BigDecimal money;
    @ApiModelProperty("银行Id:从数据字典code[bank]获取")
    private Long bank;
    @ApiModelProperty("收款类型Id:从数据字典code[receiptType]获取")
    private Long receiptType;
    @ApiModelProperty("付款方式Id:从数据字典code[paymentMethod]获取")
    private Long paymentMethod;
    @ApiModelProperty("收款日期")
    private Date receiptDate;
    @ApiModelProperty("银行日期")
    private Date bankDate;
    @ApiModelProperty("交易备注")
    private String remark;
    @ApiModelProperty("系统内部交易code(内部业务流水号，订单号、投诉单号等)")
    private String tradeCode;
    @ApiModelProperty("第三方交易code(对外流水号，农行交易流水、银联交易流水号、支付宝等)")
    private String thirdPartyTradeCode;
    @ApiModelProperty("")
    private Date tradeTime;
    @ApiModelProperty("交易类型：com.pinshang.qingyun.base.enums.StoreBillTypeEnums")
    private Integer billType;
    @ApiModelProperty("订单Id")
    private Long orderId;
    @ApiModelProperty("订单编码")
    private String orderCode;
    @ApiModelProperty("销售主体公司Id=t_company.id （销售||网络公司Id）")
    private Long salesCompanyId;
    @ApiModelProperty("销售主体公司简称（备注不一定是公司全称,销售||网络）")
    private String salesCompanyName;
    private Long userId;
}
package com.pinshang.qingyun.order.util;

import org.apache.commons.lang3.StringUtils;

import java.lang.reflect.Method;
import java.util.Objects;
import java.util.concurrent.ConcurrentHashMap;

/**
 * <p>
 * 反射工具类
 * </p>
 *
 * <AUTHOR> shenyang
 * @version : 1.0.0
 * @history : modify history
 * <author>              <time>              <version>              <desc>
 * @since : 2024/06/24 10:57
 */
public class ReflectionUtils {
    private static final ConcurrentHashMap<Class<?>, Method> SPECIAL_LIMIT_WARNING_TIPS_METHOD_CACHE = new ConcurrentHashMap<>();
    private static final ConcurrentHashMap<Class<?>, Method> IS_EXCEED_LIMIT_METHOD_CACHE = new ConcurrentHashMap<>();

    /**
     * 设置特价超限提示语
     */
    public static void setSpecialLimitWarningTips(Object object, String specialLimitWarningTips) {
        if (Objects.isNull(object) || StringUtils.isBlank(specialLimitWarningTips)) {
            throw new IllegalArgumentException("Object and specialLimitWarningTips must not be null");
        }

        Class<?> clazz = object.getClass();
        try {
            Method method = SPECIAL_LIMIT_WARNING_TIPS_METHOD_CACHE.computeIfAbsent(clazz, cls -> {
                try {
                    Method m = cls.getDeclaredMethod("setSpecialLimitWarningTips", String.class);
                    m.setAccessible(true);
                    return m;
                } catch (NoSuchMethodException e) {
                    throw new RuntimeException("Method setSpecialLimitWarningTips not found in class " + cls.getName(), e);
                }
            });
            method.invoke(object, specialLimitWarningTips);
        } catch (Exception e) {
            throw new RuntimeException("Failed to invoke setSpecialLimitWarningTips: " + e.getMessage());
        }
    }

    /**
     * 设置特价是否超限，没有这个字段就不设置
     */
    public static void setIsExceedLimit(Object object, Boolean isExceedLimit) {
        if (Objects.isNull(object) || Objects.isNull(isExceedLimit)) {
            throw new IllegalArgumentException("Object and setIsExceedLimit must not be null");
        }

        Class<?> clazz = object.getClass();
        try {
            Method method = IS_EXCEED_LIMIT_METHOD_CACHE.computeIfAbsent(clazz, cls -> {
                try {
                    Method m = cls.getMethod("setIsExceedLimit", Boolean.class);
                    m.setAccessible(true);
                    return m;
                } catch (NoSuchMethodException e) {
                    throw new RuntimeException("Method setIsExceedLimit not found in class " + cls.getName(), e);
                }
            });
            method.invoke(object, isExceedLimit);
        } catch (Exception e) {
            throw new RuntimeException("Failed to invoke setIsExceedLimit: " + e.getMessage());
        }
    }
}

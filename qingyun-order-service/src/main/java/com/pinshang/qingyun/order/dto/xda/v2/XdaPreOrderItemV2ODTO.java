package com.pinshang.qingyun.order.dto.xda.v2;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @date 23/6/21/021 10:14
 */
@Data
public class XdaPreOrderItemV2ODTO {

    @ApiModelProperty("普通商品组")
    private List<XdaOrderItemAppV2ODTO> normalGroup;

    @ApiModelProperty("特惠商品组")
    private List<XdaOrderItemAppV2ODTO> thGroups;

    @ApiModelProperty("赠送商品组")
    private List<XdaOrderItemAppV2ODTO> giftGroup;

    public static XdaPreOrderItemV2ODTO initXdaPreOrderItemV2(){
        XdaPreOrderItemV2ODTO xdaPreOrderItemV2ODTO = new XdaPreOrderItemV2ODTO();
        xdaPreOrderItemV2ODTO.setNormalGroup(new ArrayList<>());
        xdaPreOrderItemV2ODTO.setThGroups(new ArrayList<>());
        xdaPreOrderItemV2ODTO.setGiftGroup(new ArrayList<>());
        return xdaPreOrderItemV2ODTO;
    }
}

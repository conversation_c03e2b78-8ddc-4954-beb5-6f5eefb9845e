package com.pinshang.qingyun.order.dto.xda.v3;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2023/12/21 14:32
 */
@Data
public class XdaPreOrderItemV3ODTO {
    @ApiModelProperty("普通商品组")
    private List<XdaOrderItemAppV3ODTO> normalGroup;

    @ApiModelProperty("促销商品组")
    private List<XdaPreOrderItemGroupV3ODTO> promotionGroups;

    @ApiModelProperty("特惠商品组")
    private List<XdaOrderItemAppV3ODTO> thGroup;

//    @ApiModelProperty("失效商品组")
//    private List<XdaOrderItemAppV3ODTO> invalidateGroup;

    public static XdaPreOrderItemV3ODTO initXdaPreOrderItemV3ODTO(){
        XdaPreOrderItemV3ODTO xdaPreOrderItemV2ODTO = new XdaPreOrderItemV3ODTO();
        xdaPreOrderItemV2ODTO.setNormalGroup(new ArrayList<>());
        xdaPreOrderItemV2ODTO.setThGroup(new ArrayList<>());
        xdaPreOrderItemV2ODTO.setPromotionGroups(new ArrayList<>());
//        xdaPreOrderItemV2ODTO.setInvalidateGroup(new XdaPreOrderItemGroupV3ODTO());
        return xdaPreOrderItemV2ODTO;
    }
}

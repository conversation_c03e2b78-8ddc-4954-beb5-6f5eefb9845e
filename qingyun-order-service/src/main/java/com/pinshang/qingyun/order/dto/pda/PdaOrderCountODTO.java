package com.pinshang.qingyun.order.dto.pda;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;

import java.math.BigDecimal;

@Data
@AllArgsConstructor
public class PdaOrderCountODTO {

    @ApiModelProperty("待出库数量")
    private BigDecimal notShipCount;

    @ApiModelProperty("已出库短交数量")
    private BigDecimal shipShotCount;

    @ApiModelProperty("已出库未短交数量")
    private BigDecimal shipNotShotCount;

    @ApiModelProperty("订货数量")
    private BigDecimal orderCount;
}

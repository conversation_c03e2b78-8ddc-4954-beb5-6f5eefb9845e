package com.pinshang.qingyun.order.vo.order;

import com.pinshang.qingyun.order.mapper.entry.stall.StallEntry;

import java.util.List;

/*
 * 添加购物车
 */
public class ShoppingCartBatchVo extends StallEntry {
	private List<ShoppingCartBatchListVo> list;
	private Long storeId;
	private Long enterpriseId;
	//删除shoppingCart整个
	private Long shoppingCartId;
	//删除shoppingCartItem
	private Long shoppingCartItemId;
	
	private Long createId;

	/**
	 * 是否内置用户
	 */
	private Boolean isInternal;

	// 配送批次
	private String orderTime;

	//送货日期
	private String deliveryBatch;
	private Integer orderType;
	/** 档口id */
	private Long stallId;

	public Long getStallId() {
		return stallId;
	}

	public void setStallId(Long stallId) {
		this.stallId = stallId;
	}

	public Integer getOrderType() {
		return orderType;
	}

	public void setOrderType(Integer orderType) {
		this.orderType = orderType;
	}

	public String getOrderTime() {
		return orderTime;
	}

	public void setOrderTime(String orderTime) {
		this.orderTime = orderTime;
	}

	public String getDeliveryBatch() {
		return deliveryBatch;
	}

	public void setDeliveryBatch(String deliveryBatch) {
		this.deliveryBatch = deliveryBatch;
	}

	public List<ShoppingCartBatchListVo> getList() {
		return list;
	}

	public void setList(List<ShoppingCartBatchListVo> list) {
		this.list = list;
	}

	public Long getStoreId() {
		return storeId;
	}

	public void setStoreId(Long storeId) {
		this.storeId = storeId;
	}

	public Long getEnterpriseId() {
		return enterpriseId;
	}

	public void setEnterpriseId(Long enterpriseId) {
		this.enterpriseId = enterpriseId;
	}

	public Long getShoppingCartId() {
		return shoppingCartId;
	}

	public void setShoppingCartId(Long shoppingCartId) {
		this.shoppingCartId = shoppingCartId;
	}

	public Long getShoppingCartItemId() {
		return shoppingCartItemId;
	}

	public void setShoppingCartItemId(Long shoppingCartItemId) {
		this.shoppingCartItemId = shoppingCartItemId;
	}

	public Long getCreateId() {
		return createId;
	}

	public void setCreateId(Long createId) {
		this.createId = createId;
	}

	public Boolean getInternal() {
		return isInternal;
	}

	public void setInternal(Boolean internal) {
		isInternal = internal;
	}
}

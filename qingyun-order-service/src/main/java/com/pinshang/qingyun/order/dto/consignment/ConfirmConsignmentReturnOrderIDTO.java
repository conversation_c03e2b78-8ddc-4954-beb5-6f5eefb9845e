package com.pinshang.qingyun.order.dto.consignment;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;


/**
 * <p>
 * 代销商门店确认退货
 * </p>
 *
 * <AUTHOR> chenlong
 * @version : 1.0.0
 * @history : modify history
 * <author>              <time>              <version>              <desc>
 * @since : 2024/05/11 10:09
 */
@Data
public class ConfirmConsignmentReturnOrderIDTO {

    /**
     * 退货单id
     */
    @ApiModelProperty(value = "退货单id")
    private Long id;

    /**
     * 确认明细数据
     */
    @ApiModelProperty(value = "确认明细数据")
    private List<ConfirmConsignmentReturnOrdeItemIDTO> confirmSaleReturnOrderItems;

    /**
     * 确认人id
     */
    @ApiModelProperty(value = "确认人id")
    private Long confirmId;

}

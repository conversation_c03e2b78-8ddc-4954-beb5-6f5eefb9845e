/******************************************************************
 *
 *
 *    Package:     com
 *
 *    Filename:    Test.java
 *
 *    Description: TODO()
 *
 *    @author:     abelx
 *
 *    @version:    1.0.0
 *
 *    Create at:   2016年10月20日 下午5:22:34
 *
 *    Revision:
 *
 *    2016年10月20日 下午5:22:34
 *        - first revision
 *
 *****************************************************************/
package com.pinshang.qingyun.order.util;

import com.itextpdf.text.*;
import com.itextpdf.text.pdf.PdfPCell;
import com.itextpdf.text.pdf.PdfPTable;
import com.itextpdf.text.pdf.PdfWriter;
import com.pinshang.qingyun.box.utils.TimeUtil;
import com.pinshang.qingyun.order.config.CustomerProperties;
import com.pinshang.qingyun.order.mapper.entry.deliveryBill.DeliveryOrderItemStkPrintEntry;
import com.pinshang.qingyun.order.mapper.entry.deliveryBill.DeliveryOrderStkPrintEntry;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * @ClassName Test
 * @Description TODO()
 * <AUTHOR>
 * @Date 2016年10月20日 下午5:22:34
 * @version 1.0.0
 */
@Component
@Slf4j
public class CreateOrderStkA4 {


	private static String filePathDir;

	private static List<String> paths = new ArrayList<>();

	private final CustomerProperties customerProperties;

	@Autowired
	public CreateOrderStkA4(CustomerProperties customerProperties) {
		this.customerProperties = customerProperties;
	}


	public static void main(String[] args) {

		// System.out.println(getVal(2.00));

		DeliveryOrderStkPrintEntry op = new DeliveryOrderStkPrintEntry();
		op.setDeliverymanName("杨青华(106宁波)");
		op.setOrderNo("1628062838784342072");
		op.setStoreName("付款单位abcd");
		op.setStoreCode("1165132");
		op.setStoreLineName("上海线路组");
		op.setOrderTime(new Date());
		op.setStoreName("清美鲜家共江路1号店");
		List<DeliveryOrderItemStkPrintEntry> orderPrintItems = new ArrayList<>();
		for (int i = 0; i < 100; i++) {
			DeliveryOrderItemStkPrintEntry oi = new DeliveryOrderItemStkPrintEntry();
			oi.setCateName("豆制品");
			oi.setBarCode("6927128225853");
			oi.setCommodityCode("0000001");
			oi.setStkNumber(2323.32d);
			oi.setSubNumber(-32.23d);
			oi.setSpec("300克/盒");
			oi.setUnit("盒");
			oi.setNumber(1d);
			oi.setProductName("日式日式日式日式日式日式日式日式日式日式日式日式日式日式日式日式日式日式日式内酯豆腐(两联盒)(280克2/盒)" + i);
			orderPrintItems.add(oi);
		}
		op.setOrderPrintItems(orderPrintItems);
//		createStartPdf("d:\\pdf\\a.pdf", "110", "周蕊(001983)");
//		CreateOrderStkA4.execCreatePdfByOrder(op, 57);
		try {
			// PDFprint.execPrint("d:\\pdf\\a.pdf","192.168.2.223_0");
			// PDFprint.execPrint("d:\\pdf\\20161207141135\\000358_1481074955924277273_1_877.pdf","192.168.2.223_0");
		} catch (Exception e) {
			e.printStackTrace();
		}

	}

	/**
	 * @Description (TODO)
	 * @param orderPrint
	 * @param size
	 *            void
	 * <AUTHOR>
	 * @Date 2016年10月21日 下午3:02:38
	 */
	public String execCreatePdfByOrder(DeliveryOrderStkPrintEntry orderPrint, int size) {
		paths.clear();

		long sysStartTime = System.currentTimeMillis();
		List<DeliveryOrderItemStkPrintEntry> orderPrintItems = orderPrint.getOrderPrintItems();
		int pageSize = orderPrintItems.size() / size;
		int j = 0;
		int ys = orderPrintItems.size() % size;
		int ym=orderPrintItems.size() / size;
		boolean bool = false;
		if (ys == 0) {
			bool = true;
		}else{
			ym++;
		}


		SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");
		String pdfSave = customerProperties.getAbsoluteSavePath();
		String datePath = TimeUtil.parseSimpleDateTime(new Date());
		String savePath = customerProperties.getSavePath();

		String path= pdfSave+savePath+datePath;
		Document document = new Document(PageSize.A4,10f,10f,10f,10f);
		try {
			File f = new File(path);
			if(!f.isDirectory())f.mkdirs();
			String fileName = orderPrint.getStoreCode()+"_"+ TimeUtil.simpleDateShort(orderPrint.getOrderTime())+ "_" + orderPrint.getOrderNo()+".pdf";
			PdfWriter.getInstance(document, new FileOutputStream(path +"/"+fileName));
			document.open();
			for (; j < pageSize; j++) {
				if(j!=0){
					log.debug("new page  j:{}",j);
					document.newPage();
				}
				createPdf(document, orderPrint, orderPrintItems.subList(j * size, (j + 1) * size), j * size, ym,
						j + 1, bool);
			}
			if (ys != 0) {
				if(j!=0){
					log.debug("new page  j:{}",j);
					document.newPage();
				}
				createPdf(document, orderPrint, orderPrintItems.subList(j * size, orderPrintItems.size()), j * size,
						ym, j + 1, true);
			}
			document.close();
			log.debug("sum 耗时：" + (System.currentTimeMillis() - sysStartTime));
			return savePath+datePath+"/"+fileName;
		}catch (Exception e) {
			log.error("pdfb5 error:",e);
			return null;
		}
	}


	/**
	 * @Description (TODO)
	 * @param orderPrint
	 * @param orderPrintItems
	 * @param index
	 * @param pageSize
	 * @param pageNo
	 *            void
	 * <AUTHOR>
	 * @Date 2016年10月21日 下午2:49:31
	 */
	private static void createPdf(Document document, DeliveryOrderStkPrintEntry orderPrint, List<DeliveryOrderItemStkPrintEntry> orderPrintItems,
			int index, int pageSize, int pageNo,  boolean viewPrice) {
		// Rectangle rectangle = new Rectangle(100,160);
		// 创建一个Document对象
		try {
			// 生成名为 AsianTest.pdf 的文档
			/**
			 * 新建一个字体,iText的方法 STSongStd-Light 是字体，在iTextAsian.jar 中以property为后缀
			 * UniGB-UCS2-H 是编码，在iTextAsian.jar 中以cmap为后缀 H 代表文字版式是 横版， 相应的 V
			 * 代表竖版
			 */
			// 设置字体
			Font fontChinese = PdfUtils.chineseSTFANGSOFont(9, Font.NORMAL);// 创建字体，设置family，size，style,还可以设置color
			Font subBoldFontChinese = PdfUtils.chineseSTFANGSOFont(9, Font.NORMAL);
			// 打开文档，将要写入内容
			// document.add(new Paragraph("Some more text on the first page with
			// differasdfasdfasdaaaaaaaaaaaaaaaaaaaaaaaaaaaaafent color and font
			// type.",
			// FontFactory.getFont(FontFactory.COURIER, 14, Font.BOLD, new
			// Color(255, 150, 200))));

			/**
			 * 头信息
			 */
			setHead(document, fontChinese, subBoldFontChinese, pageNo, pageSize, orderPrint);
			float[] widths = { 2f,3f,3f,5,11f, 3f, 3f, 3f, 3f, 3f };// 设置表格的列宽和列数
			// 默认是4列
			PdfPTable table = new PdfPTable(widths);// 建立一个pdf表格
			table.setHorizontalAlignment(align);
			table.setWidthPercentage(width);// 设置表格宽度为100%
			setContextHead(fontChinese, table);
			setContext(fontChinese, table, orderPrintItems, index);
			document.add(table);
//			setButtom(document, fontChinese, subBoldFontChinese, orderPrint);
		} catch (DocumentException de) {
			System.err.println(de.getMessage());
		} catch (Exception ioe) {
			System.err.println(ioe.getMessage());
		}
	}

	static int align = Element.ALIGN_RIGHT;

	/**
	 * @Description (头信息)
	 * @param fontChinese
	 * @param table
	 *            void
	 * <AUTHOR>
	 * @Date 2016年10月21日 上午11:17:51
	 */
	private static void setContextHead(Font fontChinese, PdfPTable table) {
		// table.setSpacingBefore(20f);
		PdfPCell cell = new PdfPCell(new Paragraph("序号", fontChinese));//
		cell.setHorizontalAlignment(Element.ALIGN_CENTER);
		table.addCell(cell);
		cell = new PdfPCell(new Paragraph("一级品类", fontChinese));//
		cell.setHorizontalAlignment(Element.ALIGN_CENTER);
		table.addCell(cell);
		cell = new PdfPCell(new Paragraph("商品编码", fontChinese));//
		cell.setHorizontalAlignment(Element.ALIGN_CENTER);
		table.addCell(cell);
		cell = new PdfPCell(new Paragraph("条码", fontChinese));//
		cell.setHorizontalAlignment(Element.ALIGN_CENTER);
		table.addCell(cell);
		cell = new PdfPCell(new Paragraph("商品名称", fontChinese));//
		cell.setHorizontalAlignment(Element.ALIGN_CENTER);
		table.addCell(cell);
		cell = new PdfPCell(new Paragraph("规格", fontChinese));//
		cell.setHorizontalAlignment(Element.ALIGN_CENTER);
		table.addCell(cell);
		cell = new PdfPCell(new Paragraph("计量单位", fontChinese));//
		cell.setHorizontalAlignment(Element.ALIGN_CENTER);
		table.addCell(cell);
		cell = new PdfPCell(new Paragraph("订货数量", fontChinese));//
		cell.setHorizontalAlignment(Element.ALIGN_CENTER);
		table.addCell(cell);
		cell = new PdfPCell(new Paragraph("实配数量", fontChinese));//
		cell.setHorizontalAlignment(Element.ALIGN_CENTER);
		table.addCell(cell);
		cell = new PdfPCell(new Paragraph("短交数量", fontChinese));//
		cell.setHorizontalAlignment(Element.ALIGN_CENTER);
		table.addCell(cell);
	}

	/**
	 * @Description (内容)
	 * @param fontChinese
	 * @param table
	 *            void
	 * <AUTHOR>
	 * @Date 2016年10月21日 上午11:17:51
	 */
	private static void setContext(Font fontChinese, PdfPTable table, List<DeliveryOrderItemStkPrintEntry> orderPrintItems, int index) {
		for (DeliveryOrderItemStkPrintEntry orderPrintItem : orderPrintItems) {
			index++;
			log.debug("=============================start======================================");
			log.debug("letter orderItem:{}",orderPrintItem.toString());
			log.debug("=============================end======================================");
			PdfPCell cell = new PdfPCell(new Paragraph("" + index, fontChinese));// 下标
			cell.setHorizontalAlignment(Element.ALIGN_CENTER);
			cell.setVerticalAlignment(Element.ALIGN_MIDDLE);
			// cell.setFixedHeight(24f);
			table.addCell(cell);

			cell = new PdfPCell(new Paragraph(orderPrintItem.getCateName(), fontChinese));// 一级品类
			cell.setHorizontalAlignment(Element.ALIGN_LEFT);
			cell.setVerticalAlignment(Element.ALIGN_LEFT);
			cell.setFixedHeight(12f);
			table.addCell(cell);

			cell = new PdfPCell(new Paragraph(orderPrintItem.getCommodityCode(), fontChinese));// 一级品类
			cell.setHorizontalAlignment(Element.ALIGN_CENTER);
			cell.setVerticalAlignment(Element.ALIGN_MIDDLE);
			table.addCell(cell);

			cell = new PdfPCell(new Paragraph(orderPrintItem.getBarCode(), fontChinese));// 一级品类
			cell.setHorizontalAlignment(Element.ALIGN_CENTER);
			cell.setVerticalAlignment(Element.ALIGN_MIDDLE);
			table.addCell(cell);

			String productName = orderPrintItem.getProductName();
//			if (productName.getBytes().length > 23) {
//				productName = bSubstring(productName, 23);// productName.substring(0,
//				// 26);
//			}
			cell = new PdfPCell(new Paragraph(productName, fontChinese));// 一级品类
			cell.setHorizontalAlignment(Element.ALIGN_LEFT);
			cell.setVerticalAlignment(Element.ALIGN_LEFT);
			cell.setFixedHeight(12f);
			table.addCell(cell);


			cell = new PdfPCell(new Paragraph(orderPrintItem.getSpec(), fontChinese));// 品名规格
			cell.setHorizontalAlignment(Element.ALIGN_LEFT);
			cell.setVerticalAlignment(Element.ALIGN_MIDDLE);
			cell.setFixedHeight(12f);
			table.addCell(cell);

			cell = new PdfPCell(new Paragraph(orderPrintItem.getUnit(), fontChinese));// 品名规格
			cell.setHorizontalAlignment(Element.ALIGN_CENTER);
			cell.setVerticalAlignment(Element.ALIGN_MIDDLE);
			cell.setFixedHeight(12f);
			table.addCell(cell);
			cell = new PdfPCell(new Paragraph(getVal(orderPrintItem.getNumber()), fontChinese));// 数量
			cell.setHorizontalAlignment(Element.ALIGN_CENTER);
			cell.setVerticalAlignment(Element.ALIGN_MIDDLE);
			table.addCell(cell);
			cell = new PdfPCell(new Paragraph(getVal(orderPrintItem.getStkNumber()), fontChinese));// 单价
			cell.setHorizontalAlignment(Element.ALIGN_CENTER);
			cell.setVerticalAlignment(Element.ALIGN_MIDDLE);
			table.addCell(cell);
			cell = new PdfPCell(new Paragraph(getVal(orderPrintItem.getSubNumber()), fontChinese));// 金额
			cell.setHorizontalAlignment(Element.ALIGN_CENTER);
			cell.setVerticalAlignment(Element.ALIGN_MIDDLE);
			table.addCell(cell);
		}
//		if (orderPrint.getShow() == 0 && viewPrice) {
//			PdfPCell cell = new PdfPCell(new Paragraph("", fontChinese));// 下标
//			cell.setHorizontalAlignment(Element.ALIGN_CENTER);
//			table.addCell(cell);
//			cell = new PdfPCell(new Paragraph("合计", fontChinese));// 品名规格
//			cell.setHorizontalAlignment(Element.ALIGN_LEFT);
//			cell.setFixedHeight(12f);
//			table.addCell(cell);
//			cell = new PdfPCell(new Paragraph("", fontChinese));// 数量
//			cell.setHorizontalAlignment(Element.ALIGN_CENTER);
//			table.addCell(cell);
//			cell = new PdfPCell(new Paragraph("", fontChinese));// 单价
//			cell.setHorizontalAlignment(Element.ALIGN_CENTER);
//			table.addCell(cell);
//			cell = new PdfPCell(new Paragraph(orderPrint.getPayMoney(), fontChinese));// 金额
//			cell.setHorizontalAlignment(Element.ALIGN_CENTER);
//			cell.setFixedHeight(12f);
//			table.addCell(cell);
//			cell = new PdfPCell(new Paragraph("", fontChinese));// 生成日期
//			cell.setHorizontalAlignment(Element.ALIGN_CENTER);
//			table.addCell(cell);
//			cell = new PdfPCell(new Paragraph("", fontChineseRemark));// 备注
//			cell.setHorizontalAlignment(Element.ALIGN_CENTER);
//			table.addCell(cell);
//		}
	}

	public static BigDecimal setScaleRoundDown(int newScale, BigDecimal b) {
		return b.setScale(newScale, BigDecimal.ROUND_DOWN);
	}

	public static String getVal(Double b) {
		if (b == null || b == 0) {
			log.error("b   is null");
			return "0";
		}
		// if (b % 1 == 0) {
		// return b.intValue()+"";
		// } else {
		// BigDecimal bg = new BigDecimal(b);
		// return bg.setScale(2, BigDecimal.ROUND_DOWN).toString();
		// }
		String val = b.toString();
		if (val.endsWith(".00")) {
			val = val.substring(0, val.indexOf("."));
		} else if (val.endsWith(".0")) {
			val = val.substring(0, val.indexOf("."));
		}
		return val;
	}

	static float width = 100f;

	/**
	 * @Description (TODO)
	 * @param document
	 * @param fontChinese
	 * @param subBoldFontChinese
	 * @throws DocumentException
	 *             void
	 * <AUTHOR>
	 * @Date 2016年10月21日 上午11:12:27
	 */
	private static void setHead(Document document, Font fontChinese, Font subBoldFontChinese, int pageNo, int pageSize,
								DeliveryOrderStkPrintEntry orderPrint) throws DocumentException, IOException {
		float[] widths2 = { 40f, 40f, 40f, 40f };// 设置表格的列宽和列数 默认是4列
		PdfPTable table2 = new PdfPTable(widths2);// 建立一个pdf表格
		table2.setWidthPercentage(100);
//		table2.setSpacingBefore(20f);
		table2.setHorizontalAlignment(Element.ALIGN_CENTER);
		String orderTime = TimeUtil.parseSimpleDateTime(orderPrint.getOrderTime());
		Font companyName =PdfUtils.chineseSTFont(13, Font.BOLD);;
		PdfPCell cell = new PdfPCell(new Paragraph(orderPrint.getStoreName(), companyName));//
		cell.setColspan(3);
		cell.setBorder(0);
		cell.setHorizontalAlignment(Element.ALIGN_LEFT);
		cell.setVerticalAlignment(Element.ALIGN_LEFT);
		table2.addCell(cell);
		cell = new PdfPCell(new Paragraph("第" + pageNo + "页 共" + pageSize + "页", fontChinese));//
		cell.setColspan(2);
		cell.setBorder(0);
		cell.setHorizontalAlignment(Element.ALIGN_RIGHT);
		table2.addCell(cell);

		cell = new PdfPCell(new Paragraph("配送单号：PS"+orderTime.replaceAll("-","")+orderPrint.getOrderNo().substring(13), subBoldFontChinese));//
		cell.setHorizontalAlignment(Element.ALIGN_LEFT);
		cell.setVerticalAlignment(Element.ALIGN_LEFT);
		cell.setBorder(0);
		table2.addCell(cell);
		cell = new PdfPCell(new Paragraph("订单编号："+orderPrint.getOrderNo(), subBoldFontChinese));//
		cell.setBorder(0);
		cell.setColspan(3);
		cell.setVerticalAlignment(Element.ALIGN_LEFT);
		cell.setHorizontalAlignment(Element.ALIGN_LEFT);
		table2.addCell(cell);



		cell = new PdfPCell(new Paragraph("客户编码："+orderPrint.getStoreCode(), subBoldFontChinese));//
		cell.setHorizontalAlignment(Element.ALIGN_LEFT);
		cell.setBorder(0);
		table2.addCell(cell);
		cell = new PdfPCell(new Paragraph("送货日期："+orderTime, subBoldFontChinese));//
		cell.setBorder(0);
		cell.setHorizontalAlignment(Element.ALIGN_LEFT);
		table2.addCell(cell);

		cell = new PdfPCell(new Paragraph("送货员："+orderPrint.getDeliverymanName(), subBoldFontChinese));//
		cell.setHorizontalAlignment(Element.ALIGN_LEFT);
		cell.setBorder(0);
		cell.setColspan(2);
		table2.addCell(cell);



		cell = new PdfPCell(new Paragraph("配送商品列表", subBoldFontChinese));//
		cell.setHorizontalAlignment(Element.ALIGN_LEFT);
		cell.setPaddingTop(5);
		cell.setColspan(4);
		cell.setBorder(0);
		table2.addCell(cell);
		document.add(table2);
	}

	public static String bSubstring(String s, int length) {
		try {
			byte[] bytes = s.getBytes("Unicode");
			int n = 0; // 表示当前的字节数
			int i = 2; // 要截取的字节数，从第3个字节开始
			for (; i < bytes.length && n < length; i++) {
				// 奇数位置，如3、5、7等，为UCS2编码中两个字节的第二个字节
				if (i % 2 == 1) {
					n++; // 在UCS2第二个字节时n加1
				} else {
					// 当UCS2编码的第一个字节不等于0时，该UCS2字符为汉字，一个汉字算两个字节
					if (bytes[i] != 0) {
						n++;
					}
				}
			}
			// 如果i为奇数时，处理成偶数
			if (i % 2 == 1) {
				// 该UCS2字符是汉字时，去掉这个截一半的汉字
				if (bytes[i - 1] != 0) {
					i = i - 1;
					// 该UCS2字符是字母或数字，则保留该字符
				}else {
					i = i + 1;
				}
			}
			return new String(bytes, 0, i, "Unicode");
		} catch (Exception e) {
			log.error("截取字符串错误：", e);
			return s.substring(0, 20);
		}

	}
}

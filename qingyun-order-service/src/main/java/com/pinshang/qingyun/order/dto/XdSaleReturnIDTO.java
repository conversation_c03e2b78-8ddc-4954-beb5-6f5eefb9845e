package com.pinshang.qingyun.order.dto;

import com.pinshang.qingyun.base.page.Pagination;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @Author: sk
 * @Date: 2020/4/9
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class XdSaleReturnIDTO extends Pagination {

    private Long shopId;

    private String beginDate;

    private String endDate;

    private Integer status;

    private String orderCode;

    private String commodityKey;

    private List<Long> commodityIdList;

}

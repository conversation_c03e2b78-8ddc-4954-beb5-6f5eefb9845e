package com.pinshang.qingyun.order.vo.order;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/12/13/0013 10:17
 */
@Data
public class OrderKafkaVo {

    @ApiModelProperty("送货日期")
    private String orderTime;

    @ApiModelProperty("客户id")
    private Long storeId;

    @ApiModelProperty("订单类型：com.pinshang.qingyun.order.enums.OrderType.JL_AUTO_ORDER.getCode()")
    private Integer orderType;

    @ApiModelProperty("订单明细")
    private List<OrderItemKafkaVo> orderItems;

}

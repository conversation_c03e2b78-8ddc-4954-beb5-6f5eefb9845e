package com.pinshang.qingyun.order.vo.subOrder;

import com.pinshang.qingyun.order.constant.DictionaryCodeConstant;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class UnDeliverySubOrderJobRespVo {

    private Long orderId;
    private Long warehouseId;
    private Long shiftStoreTypeId;

}

package com.pinshang.qingyun.order.model.order;

import javax.persistence.Entity;
import javax.persistence.Table;

import com.pinshang.qingyun.base.po.BaseIDPO;
import lombok.Data;

@Entity
@Table(name = "t_order_mirror")
@Data
public class OrderMirror extends BaseIDPO {
	/**订单id **/
    private Long orderId;
	/**订单编号 **/
    private String orderCode;
	/**送货员id **/
    private Long deliveryManId;
	/**送货员名称 **/
    private String deliveryManName;
	/**销售员id **/
    private Long salesmanId;
	/**销售员名称 **/
    private String salesmanName;
	/**督导id **/
    private Long supervisionId;
	/**督导名称 **/
    private String supervisionName;
	/**大区经理id **/
    private Long regionalManagerId;
	/**大区经理名称 **/
    private String regionalManagerName;
	/**主任id **/
    private Long directorId;
	/**主任名称 **/
    private String directorName;
}
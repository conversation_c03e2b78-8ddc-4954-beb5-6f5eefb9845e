package com.pinshang.qingyun.order.enums;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;

/**
 * <AUTHOR>
 * @Version 0.1.0
 * @Date 2021/2/26 12:21
 * @Copyright © 2019-2021 qm
 * @Description -- 类说明
 **/
public enum XdaComplainItemTypeEnums implements BaseEnum {

    LESS(1, "少货"),
    RETURN(2, "退货"),
    MORE(3, "多货"),

    ;

    private int code;
    private String name;


    private XdaComplainItemTypeEnums(int code, String name) {
        this.code = code;
        this.name = name;
    }

    @JsonValue
    public int getCode() {
        return this.code;
    }

    public String getName() {
        return this.name;
    }


    @JsonCreator
    public static XdaComplainItemTypeEnums getByCode(Integer code) {
        if (code == null) {
            return null;
        } else {
            XdaComplainItemTypeEnums[] var1 = values();
            int var2 = var1.length;
            for (int var3 = 0; var3 < var2; ++var3) {
                XdaComplainItemTypeEnums value = var1[var3];
                if (value.getCode() == code) {
                    return value;
                }
            }
            return null;
        }
    }

    @Override
    public Integer convert() {
        return this.code;
    }
}


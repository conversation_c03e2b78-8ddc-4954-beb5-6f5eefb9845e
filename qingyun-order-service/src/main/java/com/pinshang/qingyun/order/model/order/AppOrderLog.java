package com.pinshang.qingyun.order.model.order;

import com.pinshang.qingyun.base.po.BaseIDPO;
import com.pinshang.qingyun.order.vo.order.OrderHistoryEntityTypeEnums;
import com.pinshang.qingyun.order.vo.order.OrderHistoryOperationTypeEnums;
import lombok.Builder;
import lombok.Data;

import javax.persistence.Entity;
import javax.persistence.Table;
import java.util.Date;

/**
 * @Author: sk
 * @Date: 2025/3/4
 */
@Builder
@Entity
@Table(name = "t_app_order_log")
@Data
public class AppOrderLog extends BaseIDPO {

    /** 订单id */
    private Long orderId;

    /** 客户id */
    private Long storeId;

    /** 设备id(手机设备的唯一标识符) */
    private String deviceId;

    /** 移动系统类型( 1 = android, 2 = ios) */
    private Integer systemType;

    /** 移动设备的系统版本信息 */
    private String systemVersion;

    /** app版本 V4.1.1 */
    private String appVersionName;

    /** 订单时间*/
    private Date orderTime;
    private Date createTime;

    public AppOrderLog() {
        super();
    }

    public static AppOrderLog initAppOrderLog(Long orderId, Long storeId, String deviceId, Integer systemType, String systemVersion, String appVersionName,Date orderTime,Date createTime) {
        AppOrderLog appOrderLog = new AppOrderLog(orderId,storeId,deviceId,systemType,systemVersion,appVersionName,orderTime,createTime);
        return appOrderLog;
    }

    private AppOrderLog(Long orderId, Long storeId, String deviceId, Integer systemType, String systemVersion, String appVersionName,Date orderTime,Date createTime) {
        super();
        this.orderId = orderId;
        this.storeId = storeId;
        this.orderId = orderId;
        this.deviceId = deviceId;
        this.systemType = systemType;
        this.systemVersion = systemVersion;
        this.appVersionName = appVersionName;
        this.orderTime = orderTime;
        this.createTime = createTime;
    }
}

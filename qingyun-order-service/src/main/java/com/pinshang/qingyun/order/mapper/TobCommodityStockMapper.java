package com.pinshang.qingyun.order.mapper;

import com.pinshang.qingyun.base.mybatis.MyMapper;
import com.pinshang.qingyun.order.dto.tob.ToBStockRepairIDTO;
import com.pinshang.qingyun.order.model.tob.TobCommodityStock;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface TobCommodityStockMapper  extends MyMapper<TobCommodityStock> {
    /**
     * 批量更新
     * @param updateList
     */
    void batchUpdate(@Param("tobCommodityStockList") List<ToBStockRepairIDTO> updateList);

    /**
     * 批量插入
     * @param insertList
     */
    void batchInsert(List<ToBStockRepairIDTO> insertList);

    List<Long> selectCommodityIdByStockType(@Param("stockType") Integer stockType);

    List<TobCommodityStock> selectStockByCommodityIdList(@Param("list") List<Long> commodityIdList, @Param("logisticsCenterId") Long logisticsCenterId);
}

package com.pinshang.qingyun.order.vo.order;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.List;

/**
 * Created by crell on 2017/11/6.
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class SaleReturnItemAddVo {
    private Long id;

    private String commodityId;
    private String commodityCode;
    private String commodityName;

    private BigDecimal returnQuantity;
    
    private BigDecimal price;
    
    private BigDecimal totalPrice;

    private BigDecimal realReturnQuantity;

    private Integer returnReason;

    private String remark;

    //商品所属物流模式、供应商、仓库
    private Integer logisticsModel;

    private Long supplierId;

    private Long warehouseId;

    private BigDecimal commodityPackageSpec;

    public BigDecimal getTotalPrice(){
        if(null != totalPrice){
            return totalPrice;
        }
        if(returnQuantity != null && price != null){
            return  returnQuantity.multiply(price);
        }
        return BigDecimal.ZERO;
    }
    /** 图片list */
    private List<SaleReturnItemAddPicVO> picList;
    /** 视频list */
    private List<SaleReturnItemAddPicVO> videoList;

    private Long consignmentId; // 代销商id
}

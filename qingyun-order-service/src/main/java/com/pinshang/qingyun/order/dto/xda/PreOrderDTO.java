package com.pinshang.qingyun.order.dto.xda;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.pinshang.qingyun.base.configure.codec.DecimalSerializerKeep2;
import com.pinshang.qingyun.base.enums.xda.XdaStoreTypeEnum;
import com.pinshang.qingyun.store.dto.customer.StoreSelectODTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 待确认订单
 * <AUTHOR>
 * @Date 2019/11/21 19:45
 */
@Data
@ApiModel
public class PreOrderDTO {
    @ApiModelProperty("联系人")
    private String storeLinkman;
    @ApiModelProperty("联系人手机号")
    private String linkmanMobile;
    @ApiModelProperty("客户名称")
    private String storeName;
    @ApiModelProperty("送货地址")
    private String deliveryAddress;
    @ApiModelProperty("送达时间")
    private Date orderTime;
    @ApiModelProperty("品种合计")
    private Integer varietySum;
    @ApiModelProperty("商品金额(原价总计)")
    @JsonSerialize(using = DecimalSerializerKeep2.class)
    private BigDecimal totalOriginPrice;
    @ApiModelProperty("实付(提交订单按钮上的金额)")
    @JsonSerialize(using = DecimalSerializerKeep2.class)
    private BigDecimal actuallyPaid;
    @ApiModelProperty("账户类型")
    private XdaStoreTypeEnum storeType;
    @ApiModelProperty( "可用额度/授信额度")
    @JsonSerialize(using = DecimalSerializerKeep2.class)
    private BigDecimal currentAmount;
    @ApiModelProperty( "最小充值金额")
    private String minRechargeMoney;
    @ApiModelProperty("备注")
    private String remark;


    public void covertStoreBaseInfo(StoreSelectODTO selectODTO){
        storeLinkman = selectODTO.getStoreLinkman();
        linkmanMobile = selectODTO.getLinkmanMobile();
        storeName = selectODTO.getStoreName();
        deliveryAddress = selectODTO.getDeliveryAddress();
    }

}

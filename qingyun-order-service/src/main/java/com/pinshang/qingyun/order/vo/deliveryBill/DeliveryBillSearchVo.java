package com.pinshang.qingyun.order.vo.deliveryBill;

import com.pinshang.qingyun.base.page.Pagination;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @description:
 * @author: hhf
 * @time: 2021/8/4 14:34
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class DeliveryBillSearchVo extends Pagination {

    /**开始时间**/
    private String orderStartTime;
    /**结束时间**/
    private String orderEndTime;
    /**客户类型**/
    private Long storeTypeId;
    /**线路**/
    private Long lineId;
    /**客户**/
    private Long storeId;
    /**订单编码**/
    private String orderCode;
    /**送货员**/
    private Long deliverymanId;
}

package com.pinshang.qingyun.order.enums;

public enum PrePaidAccountReqTypeEnum {

	PAY(1), RECEIVE(2);
	
	private final int code;
	private PrePaidAccountReqTypeEnum(int code) {
		this.code = code;
	}
	public int getCode() {
		return code;
	}
	
	public static PrePaidAccountReqTypeEnum fromCode(Integer code) {
		if (code == null) {
			return null;
		}
		for (PrePaidAccountReqTypeEnum t : PrePaidAccountReqTypeEnum.values()) {
			if (t.getCode() == code) {
				return t;
			}
		}
		return null;
	}
	
}

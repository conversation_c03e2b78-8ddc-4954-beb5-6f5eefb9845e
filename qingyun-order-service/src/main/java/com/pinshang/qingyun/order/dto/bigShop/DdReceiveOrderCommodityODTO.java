package com.pinshang.qingyun.order.dto.bigShop;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * @Author: sk
 * @Date: 2020/3/18
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class DdReceiveOrderCommodityODTO {
      // 订单号
      private String orderCode;
      // 物流配送模式, 0-直送, 1-配送, 2-直通
      private Integer logisticsModel;
      // 订单金额
      private BigDecimal orderAmount;

      private Long subOrderId;
      private String subOrderCode;
      private Long subOrderItemId;

      private Long commodityId;
      // 订货数量
      private BigDecimal quantity;
      // 实发数量
      private BigDecimal realDeliveryQuantity;
      // 实收数量
      private BigDecimal realReceiveQuantity;

      private BigDecimal price;
      @ApiModelProperty("包装规格")
      private BigDecimal commodityPackageSpec;
}

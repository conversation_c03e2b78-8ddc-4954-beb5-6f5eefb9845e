package com.pinshang.qingyun.order.dto.order;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * @Author: sk
 * @Date: 2025/2/19
 */
@Data
public class PosXdSaleDataODTO {

    private Long commodityId;

    /** 销售数量(当日) */
    private BigDecimal quantity = BigDecimal.ZERO;

    @ApiModelProperty(value = "近15天日均销量")
    private BigDecimal avgDailySales15Days = BigDecimal.ZERO;

    @ApiModelProperty(value = "近15天毛利率")
    private BigDecimal grossProfitMargin15Days = BigDecimal.ZERO;
}

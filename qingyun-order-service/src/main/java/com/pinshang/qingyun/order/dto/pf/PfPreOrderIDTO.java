package com.pinshang.qingyun.order.dto.pf;

import java.math.BigDecimal;
import java.util.Date;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 待确认订单预览
 * <AUTHOR>
 */
@Data
@ApiModel
public class PfPreOrderIDTO {
    @ApiModelProperty(value = "客户id", hidden = true)
    private Long storeId;
    @ApiModelProperty(value = "appCode", hidden = true)
    private String appCode;
    @ApiModelProperty("订单金额")
    private BigDecimal orderAmount;
    @ApiModelProperty("订货日期")
    private Date orderTime;
    @ApiModelProperty("品种合计")
    private Integer varietySum;

    @ApiModelProperty("普通商品数量+赠品数量")
    private BigDecimal commodityNum;
}

package com.pinshang.qingyun.order.model.promotionStk;

import java.util.Date;

import javax.persistence.Entity;
import javax.persistence.Table;

import com.pinshang.qingyun.base.po.BaseEnterprisePO;

/**
 * 配货方案表
 **/
@Entity
@Table(name = "t_promotion_stk")
public class PromotionStk extends BaseEnterprisePO {
	/**方案名称 **/
    private String name;
	/**描述 **/
    private String remark;
	/**0：开启 1：关闭 **/
    private Integer status;
	/**创建者 **/
    private Long createUserId;
	/**开始时间 **/
    private Date startTime;
	/**结束时间 **/
    private Date endTime;

    public String getName(){
        return name;
    }
    public void setName(String name){
        this.name = name;
    }
    public String getRemark(){
        return remark;
    }
    public void setRemark(String remark){
        this.remark = remark;
    }
    public Integer getStatus(){
        return status;
    }
    public void setStatus(Integer status){
        this.status = status;
    }
    public Long getCreateUserId(){
        return createUserId;
    }
    public void setCreateUserId(Long createUserId){
        this.createUserId = createUserId;
    }
    public Date getStartTime(){
        return startTime;
    }
    public void setStartTime(Date startTime){
        this.startTime = startTime;
    }
    public Date getEndTime(){
        return endTime;
    }
    public void setEndTime(Date endTime){
        this.endTime = endTime;
    }
    
}
package com.pinshang.qingyun.order.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * @Author: sk
 * @Date: 2021/9/1
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ShopOrderedQuantityODTO {
    private Long id;

    private Long shopId;

    private Long commodityId;

    private BigDecimal quantity;

    private String orderTime;

}

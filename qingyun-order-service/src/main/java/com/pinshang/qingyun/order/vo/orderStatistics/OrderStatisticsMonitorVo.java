package com.pinshang.qingyun.order.vo.orderStatistics;

import com.pinshang.qingyun.base.page.Pagination;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;
import java.util.List;

/**
 * 订单统计查询--订单监控查询参数
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class OrderStatisticsMonitorVo extends Pagination {
    private List<String> orderCodeList;

    private List<Integer> filterOrderTypeList;

    private Date orderTime;
    private Date startTime;
    private Date endTime;
    private Date sysTime;//当前系统时间

    private Date startCreateTime;
    private Date endCreateTime;
}

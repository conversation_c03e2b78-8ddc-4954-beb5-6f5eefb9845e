package com.pinshang.qingyun.order.job;

import java.util.List;


import com.pinshang.qingyun.base.spring.SpringBeanFinder;
import lombok.extern.slf4j.Slf4j;
import org.quartz.Job;
import org.quartz.JobDataMap;
import org.quartz.JobExecutionContext;
import org.quartz.JobExecutionException;
import org.springframework.stereotype.Component;

import com.pinshang.qingyun.order.mapper.entry.order.generatePurchaseOrder.SubOrderEntry;
import com.pinshang.qingyun.order.service.PurchaseService;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2017/6/19.
 */
@Component
@Slf4j
public class GeneratePurchaseOrderJob implements Job{


    @Override
    public void execute(JobExecutionContext jobExecutionContext) throws JobExecutionException {
    	
        JobDataMap jobDataMap = jobExecutionContext.getJobDetail().getJobDataMap();

        String endTime = jobDataMap.getString("supplierEndTime");

        PurchaseService purchaseService = (PurchaseService) SpringBeanFinder.getBean("purchaseService");

//        Executor executor = (ThreadPoolExecutor) jobDataMap.get("fixedThreadPoolExecutor");

        List <SubOrderEntry> subOrderEntries = purchaseService.getSubOrderEntries(endTime);
        log.info("生成直送采购单,供应商结束时间endTime:" +endTime +", 需要生成的采购单数："+(subOrderEntries ==null?0:subOrderEntries.size()));
        /*subOrderEntries.stream().forEach(entry -> CompletableFuture.runAsync(
                ()->purchaseService.generatePurchaseOrder(entry), executor
        ));*/
        subOrderEntries.stream().forEach(entry -> purchaseService.generatePurchaseOrder(entry));
    }

}

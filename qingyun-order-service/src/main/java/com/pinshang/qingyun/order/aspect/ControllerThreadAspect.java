package com.pinshang.qingyun.order.aspect;

import com.pinshang.qingyun.order.util.ThreadLocalUtils;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.aspectj.lang.annotation.Pointcut;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

@Slf4j
@Aspect
@Order(5)
@Component
public class ControllerThreadAspect {

    @Pointcut("execution(public * com.pinshang.qingyun.order.controller.OrderController.*(..)) || " +
            "execution(public * com.pinshang.qingyun.order.controller.QuickGoodsController.*(..)) || " +
            "execution(public * com.pinshang.qingyun.order.controller.ShoppingCartController.*(..))")
    public void controllerThread() {
    }  
  
    @Before("controllerThread()")
    public void doBefore(JoinPoint joinPoint) {
        ThreadLocalUtils.remove();
    }


}

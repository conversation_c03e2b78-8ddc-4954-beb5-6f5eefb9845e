package com.pinshang.qingyun.order.enums;

public enum PrePaidAccountReqRefBillTypeEnum {

	ORDER(1);
	
	private final int code;
	private PrePaidAccountReqRefBillTypeEnum(int code) {
		this.code = code;
	}
	public int getCode() {
		return code;
	}
	public static PrePaidAccountReqRefBillTypeEnum fromCode(Integer code) {
		if (code == null) {
			return null;
		}
		for (PrePaidAccountReqRefBillTypeEnum s : PrePaidAccountReqRefBillTypeEnum.values()) {
			if (s.getCode() == code) {
				return s;
			}
		}
		return null;
	}
}

package com.pinshang.qingyun.order.service;

import com.pinshang.qingyun.base.configure.expand.QYAssert;
import com.pinshang.qingyun.base.enums.IogisticsModelEnums;
import com.pinshang.qingyun.base.enums.SaleReturnReasonEnums;
import com.pinshang.qingyun.box.utils.SpringUtil;
import com.pinshang.qingyun.order.mapper.ShopMapper;
import com.pinshang.qingyun.order.mapper.entry.commodity.CommodityBasicEntry;
import com.pinshang.qingyun.order.model.shop.Shop;
import com.pinshang.qingyun.order.model.bigShop.DdReceiveDocLog;
import com.pinshang.qingyun.order.vo.order.SaleReturnAddVo;
import com.pinshang.qingyun.order.vo.order.SaleReturnItemAddVo;
import com.pinshang.qingyun.shop.service.consignment.ConsignmentClient;
import com.pinshang.qingyun.xd.wms.dto.StockIDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 代销商通用service
 * @Author: sk
 * @Date: 2023/4/12
 */
@Slf4j
@Service
public class ConsigneeCommonService {
    @Autowired
    private ShopMapper shopMapper;
    @Autowired
    private SaleReturnService saleReturnService;
    @Autowired
    private ConsignmentClient consignmentClient;
    /**
     * 调用client 获取代销商商品map
     * key:商品id
     * value:代销商id
     * @return
     */
   /* public Map<Long,Long> getConsigneeCommMap(Long shopId, Long storeId){
        if(shopId == null){
            Example shopEx = new Example(Shop.class);
            shopEx.createCriteria().andEqualTo("storeId", storeId);
            List<Shop> shopList = shopMapper.selectByExample(shopEx);
            shopId = shopList.get(0).getId();
        }
        Map<Long,Long> consigneeCommMap = new HashMap<>();

        List<ConsignmentCommodityODTO> consignmentCommodityList = consignmentClient.selectConsignmentCommodityByShopId(shopId);
        if(CollectionUtils.isNotEmpty(consignmentCommodityList)){
            consigneeCommMap = consignmentCommodityList.stream().collect(Collectors.toMap(ConsignmentCommodityODTO::getCommodityId,ConsignmentCommodityODTO::getConsignmentId,(key1 , key2)-> key2));
        }
        return consigneeCommMap;
    }*/

   /*
    @Transactional(rollbackFor = Exception.class)
    public String addSaleReturn(SaleReturnAddVo vo) throws Throwable {
        QYAssert.isTrue(SpringUtil.isNotEmpty(vo.getSaleReturnItems()), "商品列表不能为空");

       *//* Map<Long,Long> consigneeCommMap = getConsigneeCommMap(vo.getShopId(), null);
        if(consigneeCommMap.size() > 0){
            vo.getSaleReturnItems().forEach(item -> {
                item.setConsignmentId(consigneeCommMap.get(Long.valueOf(item.getCommodityId())));
            });
        }*//*

        Map<Long, List<SaleReturnItemAddVo>> saleReturnMap = vo.getSaleReturnItems().stream().collect(Collectors.groupingBy(item -> {
            if (item.getConsignmentId() == null) {
                return -1L;
            }else {
                return item.getConsignmentId();
            }
        }));

        for (Map.Entry<Long, List<SaleReturnItemAddVo>> entry : saleReturnMap.entrySet()){
            vo.setSaleReturnItems(entry.getValue());
            saleReturnService.addSaleReturn(vo);
        }
        return "";
    }*/

    /**
     * 大店收货，少货时候生成少货单
     * 不判断库存情况
     * @param shortList
     */
    @Transactional(rollbackFor = Exception.class)
    public void addBigShopShortReturn(List<DdReceiveDocLog> shortList, Map<String, CommodityBasicEntry> commodityBasicMap,
                                      Long enterpriseId, Long userId) {
        DdReceiveDocLog ddReceiveDocLog = shortList.get(0);
        SaleReturnAddVo vo = new SaleReturnAddVo();
        vo.setShopId(ddReceiveDocLog.getShopId());
        vo.setStallId(ddReceiveDocLog.getStallId());
        vo.setXdQuality(false);
        vo.setCreateId(userId);
        vo.setRemark("大店收货差异");

        Shop shop = shopMapper.selectByPrimaryKey(vo.getShopId());
        vo.setStoreId(shop.getStoreId());
        vo.setEnterpriseId(enterpriseId);
        List<SaleReturnItemAddVo> saleReturnItems = new ArrayList<>();
        for(DdReceiveDocLog item : shortList){
            SaleReturnItemAddVo saleReturnItem = new SaleReturnItemAddVo();
            saleReturnItem.setReturnReason(SaleReturnReasonEnums.少货.getCode());
            saleReturnItem.setCommodityId(item.getCommodityId() + "");
            saleReturnItem.setReturnQuantity(item.getShortReceiveQuantity());

            saleReturnItems.add(saleReturnItem);
        }
        vo.setSaleReturnItems(saleReturnItems);

        // 设置商品价格和供应商，仓库
        saleReturnService.setXdPriceSupplierWarehouse(saleReturnItems, shop, vo.getStallId());

        List<SaleReturnItemAddVo> sendingItems = new ArrayList<>();
        //直通、配送item
        List<SaleReturnItemAddVo> companyItems = new ArrayList<>();
        for (SaleReturnItemAddVo saleReturnItem : saleReturnItems) {
            QYAssert.isTrue(null != saleReturnItem.getPrice(), "商品信息异常，价格不能为空");
            String commodtiyCode = commodityBasicMap.get(saleReturnItem.getCommodityId()).getCommodityCode();
            if(IogisticsModelEnums.DIRECT_SENDING.getCode() == saleReturnItem.getLogisticsModel()){
                QYAssert.isTrue(saleReturnItem.getSupplierId() != null, commodtiyCode + " 请设置首选供应商");
                sendingItems.add(saleReturnItem);
            }else{
                QYAssert.isTrue(saleReturnItem.getWarehouseId() != null, commodtiyCode + " 请设置首选仓库");
                companyItems.add(saleReturnItem);
            }
        }

        List<StockIDTO> stockList = new ArrayList<>();
        //处理直送
        if(sendingItems.size() > 0){
            Map<Long, List<SaleReturnItemAddVo>> sendingMap = sendingItems.stream().collect(Collectors.groupingBy(SaleReturnItemAddVo::getSupplierId));
            saleReturnService.doSaveSaleReturn(vo, sendingMap, IogisticsModelEnums.DIRECT_SENDING.getCode(), shop ,null,null);

        }
        //处理直通、配送
        if(companyItems.size() > 0){
            Map<Long, List<SaleReturnItemAddVo>> companyMap = companyItems.stream().collect(Collectors.groupingBy(SaleReturnItemAddVo::getWarehouseId));
            saleReturnService.doSaveSaleReturn(vo, companyMap, IogisticsModelEnums.DISPATCHING.getCode(), shop,null,null);

        }
    }
}

package com.pinshang.qingyun.order.util;

import org.apache.commons.lang3.StringUtils;

import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.temporal.TemporalAccessor;
import java.time.temporal.TemporalAdjusters;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2021/05/18 14:28
 */
public class TimeUtil {
    public static final String DATETIME_PATTERN = "yyyy-MM-dd HH:mm:ss";
    public static final String DATE_PATTERN = "yyyy-MM-dd";

    /**
     * 日期时间字符串转换为日期时间(java.time.LocalDateTime)
     *
     * @param localDateTimeStr 日期时间字符串
     * @param pattern          日期时间格式 例如DATETIME_PATTERN
     * @return LocalDateTime 日期时间
     */
    public static LocalDateTime parseLocalDateTime(String localDateTimeStr, String pattern) {
        DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern(pattern);
        return LocalDateTime.parse(localDateTimeStr, dateTimeFormatter);
    }

    /**
     * 获取指定日期当月第一天的日期字符串,带日期格式化参数
     *
     * @param localDateTime 指定日期时间
     * @return String 格式：yyyy-MM-dd 00:00:00
     */
    public static String getFirstDayOfMonthStr(LocalDateTime localDateTime, String pattern) {
        return format(localDateTime.withDayOfMonth(1).withHour(0).withMinute(0).withSecond(0), pattern);
    }

    /**
     * 获取指定日期当月最后一天的日期字符串,带日期格式化参数
     *
     * @param localDateTime 指定日期时间
     * @param pattern       日期时间格式
     * @return String 格式：yyyy-MM-dd 23:59:59
     */
    public static String getLastDayOfMonthStr(LocalDateTime localDateTime, String pattern) {
        return format(localDateTime.with(TemporalAdjusters.lastDayOfMonth()).withHour(23).withMinute(59).withSecond(59), pattern);
    }


    /**
     * 获取日期时间字符串
     *
     * @param temporal 需要转化的日期时间
     * @param pattern  时间格式
     * @return String 日期时间字符串，例如 2015-08-11 09:51:53
     */
    public static String format(TemporalAccessor temporal, String pattern) {
        DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern(pattern);
        return dateTimeFormatter.format(temporal);
    }

    public static boolean isValidDate(String strValue ) {
        if(StringUtils.isBlank(strValue)){
            return false;
        }
        strValue = strValue.replaceAll("-","");
        int d = Integer.parseInt(strValue.substring(6, 8));
        int m = Integer.parseInt(strValue.substring(4, 6));
        int y = Integer.parseInt(strValue.substring(0, 4));
        if (d < 1 || m < 1 || m > 12) {return false;}
        if (m == 2) {
            if (isLeapYear(y)) return d <= 29;
            else return d <= 28;
        }else if (m == 4 || m == 6 || m == 9 || m == 11)
            return d <= 30;
        else
            return d <= 31;
    }

    public static boolean isLeapYear(int y) {//判断是否为闰年
        return y % 4 == 0 && (y % 400 == 0 || y % 100 != 0);
    }

    public static Date getNowDate(){
        DateFormat dateFormat = new SimpleDateFormat(DATE_PATTERN);
        String now= dateFormat.format(new Date());
        Date nowDate = null;
        try {
            nowDate = dateFormat.parse(now);
        } catch (ParseException e) {
            e.printStackTrace();
        }
        return nowDate;
    }
}

package com.pinshang.qingyun.order.dto.xda.v4;

import com.pinshang.qingyun.order.model.order.Order;
import io.swagger.annotations.ApiModel;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2023/12/26 15:46
 */
@Data
@ApiModel
public class SaveOrderDTO {
    private XdaCreatePrePayOrderV4IDTO xdaCreatePrePayOrderV4IDTO;
    private List<CreOrderItemV4DTO> creOrderItemDTOList;
    private BigDecimal amount;
    private BigDecimal freightAmount;
    private Date orderDurationTime;
    private Order freezeOrder;
    private TdaDeliveryTimeRangeODTO tdaDeliveryTimeRangeODTO;
    private Integer businessType;
    private Boolean isSaveOrder;
    private String logisticsCarrierCode;
}

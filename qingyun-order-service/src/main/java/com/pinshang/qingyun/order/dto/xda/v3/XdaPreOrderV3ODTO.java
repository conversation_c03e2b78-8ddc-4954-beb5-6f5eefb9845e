package com.pinshang.qingyun.order.dto.xda.v3;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.pinshang.qingyun.base.configure.codec.DecimalSerializerKeep2;
import com.pinshang.qingyun.base.enums.xda.XdaStoreTypeEnum;
import com.pinshang.qingyun.order.mapper.entry.payType.PayTypeAndTipEntry;
import com.pinshang.qingyun.store.dto.customer.StoreSelectODTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2023/12/21 10:21
 */
@Data
@ApiModel
public class XdaPreOrderV3ODTO {
    @ApiModelProperty("客户名称")
    private String storeName;
    @ApiModelProperty("送货地址")
    private String deliveryAddress;

    @ApiModelProperty("联系人")
    private String storeLinkman;
    @ApiModelProperty("联系人手机号")
    private String linkmanMobile;

    @ApiModelProperty("商品详情信息")
    private List<XdaOrderItemAppV3ODTO> commodities;

    @ApiModelProperty("品种合计")
    private Integer varietySum;

    @ApiModelProperty("送达时间")
    private Date orderTime;


    @ApiModelProperty("商品金额(原价总计)")
    @JsonSerialize(using = DecimalSerializerKeep2.class)
    private BigDecimal totalOriginPrice;

    @ApiModelProperty("优惠(优惠总计)")
    @JsonSerialize(using = DecimalSerializerKeep2.class)
    private BigDecimal discountTotal;

    @ApiModelProperty("实付(提交订单按钮上的金额)")
    @JsonSerialize(using = DecimalSerializerKeep2.class)
    private BigDecimal actuallyPaid;

    @ApiModelProperty("配送费默认为0")
    @JsonSerialize(using = DecimalSerializerKeep2.class)
    private BigDecimal freightAmount = BigDecimal.ZERO;

    @ApiModelProperty("账户类型")
    private XdaStoreTypeEnum storeType;
    @ApiModelProperty( "可用额度/授信额度")
    @JsonSerialize(using = DecimalSerializerKeep2.class)
    private BigDecimal currentAmount;
    @ApiModelProperty( "最小充值金额")
    private String minRechargeMoney;
    @ApiModelProperty("备注")
    private String remark;

    @ApiModelProperty("充值方式")
    private List<PayTypeAndTipEntry> payTypeList;

    /**
     * 客户进本信息初始化
     * @param selectODTO
     */
    public void covertStoreBaseInfo(StoreSelectODTO selectODTO){
        storeLinkman = selectODTO.getStoreLinkman();
        linkmanMobile = selectODTO.getLinkmanMobile();
        storeName = selectODTO.getStoreName();
        deliveryAddress = selectODTO.getDeliveryAddress();
    }
}

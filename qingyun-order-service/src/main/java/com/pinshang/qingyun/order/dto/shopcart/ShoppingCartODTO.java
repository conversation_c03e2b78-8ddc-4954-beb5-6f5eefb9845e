package com.pinshang.qingyun.order.dto.shopcart;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.pinshang.qingyun.base.configure.codec.DecimalSerializerKeep2;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.*;


/**
 * <AUTHOR>
 * @Date 2019/11/20 9:50
 */
@Data
@ApiModel
@NoArgsConstructor
@AllArgsConstructor
public class ShoppingCartODTO {

    @ApiModelProperty(" 购物车底部合计(含促销特价合计,值可能与订单详情中商品总金额不同)")
    @JsonSerialize(using = DecimalSerializerKeep2.class)
    private BigDecimal summation;
    @ApiModelProperty(" 商品合计金额（不含特价）")
    @JsonSerialize(using = DecimalSerializerKeep2.class)
    private BigDecimal originalAmount;
    @ApiModelProperty("总共立减")
    @JsonSerialize(using = DecimalSerializerKeep2.class)
    private BigDecimal discountTotal;
    @ApiModelProperty("普通商品组")
    private ShoppingCartGroupODTO normalGroup;
    @ApiModelProperty("冷冻商品组")
    private ShoppingCartGroupODTO freezeGroups;
    @ApiModelProperty("失效商品组")
    private ShoppingCartGroupODTO invalidateGroup;
    @ApiModelProperty("赠送商品组")
    private ShoppingCartGroupODTO giftGroup;
    @ApiModelProperty("品种合计")
    private Integer varietySum;
    @ApiModelProperty("普通商品数量+赠品数量")
    private BigDecimal commodityNum;
    @ApiModelProperty("是否可结算")
    private Boolean canSettlement;
    @ApiModelProperty("购物车底部标语")
    private String bottomTips;
    @ApiModelProperty("购物车顶部送货时间")
    private String topTips;
    @ApiModelProperty("商品库存缺少修改提示 -- 仅限购物车内修改商品数量使用")
    private String stockWarningTips;
    @ApiModelProperty(value = "结算报错", hidden = true)
    private String warnMessage;
    @ApiModelProperty(value = "赠品所属活动分组", hidden = true)
    private Map<Long, List<ShoppingCartCommodityODTO>> conditionMap;
    @ApiModelProperty(value = "商品信息，非购物车页访问购物车接口时返回的参数", hidden = true)
    private NotShoppingCartPageODTO notShoppingCartPageODTO;
    @ApiModelProperty(value = "创建订单使用判断商品库存依据是否和购物车一致", hidden = true)
    private Map<Long, Integer> commodityStockTypeMap;
	public List<Long> getAllCommodityIds() {
		List<Long> result = new ArrayList<>();
		if (normalGroup != null) {
			result.addAll(retrieveCommodityIds(normalGroup));
		}
		return result;
	}

	private List<Long> retrieveCommodityIds(ShoppingCartGroupODTO group) {
		List<ShoppingCartCommodityODTO> commodities = group.getCommodities();
		if (commodities == null) {
			return new ArrayList<>();
		}
		List<Long> cids = new ArrayList<>();
		for (ShoppingCartCommodityODTO c : commodities) {
			cids.add(c.getCommodityId());
		}
		return cids;
	}

}

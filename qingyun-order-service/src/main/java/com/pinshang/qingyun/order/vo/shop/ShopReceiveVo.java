package com.pinshang.qingyun.order.vo.shop;

import com.pinshang.qingyun.base.page.Pagination;
import com.pinshang.qingyun.order.mapper.entry.stall.StallEntry;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.List;


/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2017/6/7.
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ShopReceiveVo extends StallEntry {

    @ApiModelProperty("送货开始日期(yyyy-MM-dd)")
    private String beginDate;

    @ApiModelProperty("送货结束日期(yyyy-MM-dd)")
    private String endDate;

    @ApiModelProperty("订单号")
    private String subOrderCode;

    private String supplierStr;

    private Integer logisticsModel;

    @ApiModelProperty("状态传入[0, 2]")
    private List<Integer> status;

    private String enterpriseId;

    private String storeId;

    private BigDecimal longitude;

    private BigDecimal latitude;

    private String supplierId;

    private List<Long> supplyIdList;

    /**
     * 是否内置用户
     */
    private Boolean isInternal;
    // 代销商户ID
    private Long consignmentId;
}

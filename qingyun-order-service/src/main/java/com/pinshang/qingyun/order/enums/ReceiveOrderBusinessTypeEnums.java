package com.pinshang.qingyun.order.enums;

/**
 * 业务类型 1 自动收货后  2 手动收货前
 */
public enum ReceiveOrderBusinessTypeEnums {

	AUTORECEIVE("自动收货后", 1),
	HANDLERECEIVE("手动收货前", 2);

    private int code;
    private String name;

    ReceiveOrderBusinessTypeEnums(String name, int code) {
        this.name = name;
        this.code = code;
    }

    public static String getName(int code) {
        for (ReceiveOrderBusinessTypeEnums es : ReceiveOrderBusinessTypeEnums.values()) {
            if (code == es.getCode()) {
                return es.name;
            }
        }
        return null;
    }

    public String getName() {
        return name;
    }

    public int getCode() {
        return code;
    }
}

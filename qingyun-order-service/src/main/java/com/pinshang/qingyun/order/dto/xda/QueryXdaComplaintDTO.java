package com.pinshang.qingyun.order.dto.xda;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @Version 0.1.0
 * @Date 2021/1/18 17:24
 * @Copyright © 2019-2021 qm
 * @Description -- 类说明
 **/
@Data
public class QueryXdaComplaintDTO {
    @ApiModelProperty(value = "投诉处理状态:1待审核;2已审核，全部为空", example = "1", required = true)
    private Integer status;

    @ApiModelProperty(value = "客户id", example = "123456", required = true, hidden = true)
    private Long storeId;;
}

package com.pinshang.qingyun.order.model.commodity;

import com.pinshang.qingyun.base.po.BaseEnterprisePO;

import javax.persistence.Entity;
import javax.persistence.Table;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 门店商品/门店库存
 */
@Entity
@Table(name="t_xs_shop_commodity")
public class ShopCommodity extends BaseEnterprisePO {

    /**门店ID**/
    private Long shopId;
    /**商品ID**/
    private Long commodityId;
    private String barCode;
    private String commodityName;
    private String commodityCode;
    /**商品价格**/
    private BigDecimal commodityPrice;
    /**app状态：0-上架，1-下架**/
    private Integer appStatus;
    /**pos状态：0-上架，1-下架**/
    private Integer posStatus;

    /** 商品状态:1-正常,0-淘汰(t_xs_shop_commodity) */
    private Integer commodityStatus;
    /** 淘汰状态:0-淘汰,1-正常(t_commodity) */
    private Integer status;

    /** 是否可售:1-是,0-否(t_xs_shop_commodity) */
    private Integer commoditySaleStatus;
    /** 是否可采:1-可采,0-否,不可采(t_xs_shop_commodity_purchase_status) */
    private Integer commodityPurchaseStatus;

    /** 可采状态:1-可采，0-不可采(t_commodity) */
    private Integer purchaseStatus;

    /**库存数量**/
    private BigDecimal stockQuantity;
    /**最低库存**/
    private BigDecimal lowestQuantity;
    /**安全库存**/
    private BigDecimal safeQuantity;
    /**线下预留**/
    private BigDecimal offlineQuantity;
    
    private Date appUpTime;
    private Date appDownTime;
    private Date posUpTime;
    private Date posDownTime;

    public Long getShopId() {
        return shopId;
    }

    public void setShopId(Long shopId) {
        this.shopId = shopId;
    }

    public Long getCommodityId() {
        return commodityId;
    }

    public void setCommodityId(Long commodityId) {
        this.commodityId = commodityId;
    }

    public BigDecimal getCommodityPrice() {
        return commodityPrice;
    }

    public void setCommodityPrice(BigDecimal commodityPrice) {
        this.commodityPrice = commodityPrice;
    }

    public Integer getAppStatus() {
        return appStatus;
    }

    public void setAppStatus(Integer appStatus) {
        this.appStatus = appStatus;
    }

    public Integer getPosStatus() {
        return posStatus;
    }

    public void setPosStatus(Integer posStatus) {
        this.posStatus = posStatus;
    }

    public BigDecimal getStockQuantity() {
        return stockQuantity;
    }

    public void setStockQuantity(BigDecimal stockQuantity) {
        this.stockQuantity = stockQuantity;
    }

    public BigDecimal getLowestQuantity() {
        return lowestQuantity;
    }

    public void setLowestQuantity(BigDecimal lowestQuantity) {
        this.lowestQuantity = lowestQuantity;
    }

    public BigDecimal getSafeQuantity() {
        return safeQuantity;
    }

    public void setSafeQuantity(BigDecimal safeQuantity) {
        this.safeQuantity = safeQuantity;
    }

    public BigDecimal getOfflineQuantity() {
        return offlineQuantity;
    }

    public void setOfflineQuantity(BigDecimal offlineQuantity) {
        this.offlineQuantity = offlineQuantity;
    }

	public Date getAppUpTime() {
		return appUpTime;
	}

	public void setAppUpTime(Date appUpTime) {
		this.appUpTime = appUpTime;
	}

	public Date getAppDownTime() {
		return appDownTime;
	}

	public void setAppDownTime(Date appDownTime) {
		this.appDownTime = appDownTime;
	}

	public Date getPosUpTime() {
		return posUpTime;
	}

	public void setPosUpTime(Date posUpTime) {
		this.posUpTime = posUpTime;
	}

	public Date getPosDownTime() {
		return posDownTime;
	}

	public void setPosDownTime(Date posDownTime) {
		this.posDownTime = posDownTime;
	}

    public Integer getCommodityStatus() {
        return commodityStatus;
    }

    public void setCommodityStatus(Integer commodityStatus) {
        this.commodityStatus = commodityStatus;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Integer getCommoditySaleStatus() {
        return commoditySaleStatus;
    }

    public void setCommoditySaleStatus(Integer commoditySaleStatus) {
        this.commoditySaleStatus = commoditySaleStatus;
    }

    public Integer getCommodityPurchaseStatus() {
        return commodityPurchaseStatus;
    }

    public void setCommodityPurchaseStatus(Integer commodityPurchaseStatus) {
        this.commodityPurchaseStatus = commodityPurchaseStatus;
    }

    public Integer getPurchaseStatus() {
        return purchaseStatus;
    }

    public void setPurchaseStatus(Integer purchaseStatus) {
        this.purchaseStatus = purchaseStatus;
    }

    public String getBarCode() {
        return barCode;
    }

    public void setBarCode(String barCode) {
        this.barCode = barCode;
    }

    public String getCommodityName() {
        return commodityName;
    }

    public void setCommodityName(String commodityName) {
        this.commodityName = commodityName;
    }

    public String getCommodityCode() {
        return commodityCode;
    }

    public void setCommodityCode(String commodityCode) {
        this.commodityCode = commodityCode;
    }
}

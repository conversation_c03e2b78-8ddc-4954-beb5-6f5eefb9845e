package com.pinshang.qingyun.order.model.order;

import com.pinshang.qingyun.base.po.BaseIDPO;

import javax.persistence.Entity;
import javax.persistence.Table;
import java.math.BigDecimal;

@Entity
@Table(name="t_sale_return_order_item")
public class SaleReturnItem extends BaseIDPO {
	
	private Long saleReturnOrderId;

	private Long commodityId;

	private Integer returnReason;

	private BigDecimal price;

	private BigDecimal returnQuantity;

	private BigDecimal realReturnQuantity;

	private BigDecimal breakageQuantity;

	private BigDecimal totalPrice;
	private String remark;

	private String auditRemark;

	public SaleReturnItem() {
	}

	public SaleReturnItem(Long commodityId, BigDecimal price, BigDecimal returnQuantity, BigDecimal totalPrice, Integer returnReason) {
		this.commodityId = commodityId;
		this.price = price;
		this.returnQuantity = returnQuantity;
		this.totalPrice = totalPrice;
		this.returnReason = returnReason;
	}

	public Long getSaleReturnOrderId() {
		return saleReturnOrderId;
	}

	public void setSaleReturnOrderId(Long saleReturnOrderId) {
		this.saleReturnOrderId = saleReturnOrderId;
	}

	public Long getCommodityId() {
		return commodityId;
	}

	public void setCommodityId(Long commodityId) {
		this.commodityId = commodityId;
	}

	public Integer getReturnReason() {
		return returnReason;
	}

	public void setReturnReason(Integer returnReason) {
		this.returnReason = returnReason;
	}

	public BigDecimal getPrice() {
		return price;
	}

	public void setPrice(BigDecimal price) {
		this.price = price;
	}

	public BigDecimal getReturnQuantity() {
		return returnQuantity;
	}

	public void setReturnQuantity(BigDecimal returnQuantity) {
		this.returnQuantity = returnQuantity;
	}

	public BigDecimal getRealReturnQuantity() {
		return realReturnQuantity;
	}

	public void setRealReturnQuantity(BigDecimal realReturnQuantity) {
		this.realReturnQuantity = realReturnQuantity;
	}

	public BigDecimal getBreakageQuantity() {
		return breakageQuantity;
	}

	public void setBreakageQuantity(BigDecimal breakageQuantity) {
		this.breakageQuantity = breakageQuantity;
	}

	public BigDecimal getTotalPrice() {
		return totalPrice;
	}

	public void setTotalPrice(BigDecimal totalPrice) {
		this.totalPrice = totalPrice;
	}

	public String getRemark() {
		return remark;
	}

	public void setRemark(String remark) {
		this.remark = remark;
	}

	public String getAuditRemark() {
		return auditRemark;
	}

	public void setAuditRemark(String auditRemark) {
		this.auditRemark = auditRemark;
	}
}

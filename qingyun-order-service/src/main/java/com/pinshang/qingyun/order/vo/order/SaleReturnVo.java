package com.pinshang.qingyun.order.vo.order;

import com.pinshang.qingyun.base.page.Pagination;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;


/**
 * Created by crell on 2017/11/6.
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class SaleReturnVo extends Pagination {

    /**
	 * 
	 */
	private static final long serialVersionUID = -4571825205606131106L;

	private String beginDate;

    private String endDate;

    private String orderCode;

    private List<Integer> status;

    private Integer logisticsModel;

    private Long enterpriseId;

    private Long storeId;

    private Long supplierId;

    private Long shopId;

    // 退货原因
    private Boolean ifReturnShort = false;
    private Integer returnReason;

    private Long consignmentId;

    private Long stallId;

    private List<Long> stallIdList;
}

package com.pinshang.qingyun.order.dto.xda;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * @Author: sk
 * @Date: 2024/8/20
 */
@Data
public class MtCouponDayStatisticsODTO {

    private Long couponId;

    /** 发放数量 */
    private Integer quantity = 0;

    // 发放或核销日期
    private Date statisticsDate = new Date();


    /** 核销数量 */
    private Integer userQuntity = 0;

    @ApiModelProperty("已核销订单总金额")
    private BigDecimal usedTotalOrderAmount = BigDecimal.ZERO;

    @ApiModelProperty("折扣总金额")
    private BigDecimal discountTotalAmount = BigDecimal.ZERO;

    private Long orderId;
}

package com.pinshang.qingyun.order.vo.order;

import com.pinshang.qingyun.base.page.Pagination;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;
import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
@ApiModel
public class OrderSyncToDcReqVo extends Pagination {
    @ApiModelProperty("订单编号集合")
    private List<String> orderCodeList;

    @ApiModelProperty("送货日期范围：开始日期")
    private String startTime;

    @ApiModelProperty("送货日期范围：结束日期")
    private String endTime;

    @ApiModelProperty("配送批次")
    private Integer deliveryBatch;

    @ApiModelProperty("订单Id集合(只有生成发货单时有数据)")
    private List<Long> orderIdList;

    @ApiModelProperty("当前时间")
    private Date currentTime;

    @ApiModelProperty("送货日期")
    private String orderTime;

    @ApiModelProperty("创建开始时间")
    private String startCreateTime;

    @ApiModelProperty("创建结束时间")
    private String endCreateTime;

}

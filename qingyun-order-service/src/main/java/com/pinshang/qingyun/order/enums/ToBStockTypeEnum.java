package com.pinshang.qingyun.order.enums;

import lombok.Getter;


/**
 * 库存依据类型
 */
@Getter
public enum ToBStockTypeEnum {
    /**
     * 1=依据大仓, 2=不限量订货,3=限量供应
     */
    STORAGE( 1,"依据大仓"),
    NOT_LIMIT( 2,"不限量"),
    LIMIT(3,"限量")
    ;
    private final Integer code;
    private final String value;
    ToBStockTypeEnum(Integer code, String value){
        this.code = code;
        this.value = value;
    }

    public static ToBStockTypeEnum getByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (ToBStockTypeEnum enums : values()) {
            if (enums.code.equals(code)) {
                return enums;
            }
        }
        return null;
    }


    public static String getName(int code) {
        for (ToBStockTypeEnum es : ToBStockTypeEnum.values()) {
            if (code == es.getCode()) {
                return es.value;
            }
        }
        return null;
    }


}

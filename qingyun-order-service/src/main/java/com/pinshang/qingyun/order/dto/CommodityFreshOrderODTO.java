package com.pinshang.qingyun.order.dto;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * @Author: sk
 * @Date: 2023/3/3
 */
@Data
public class CommodityFreshOrderODTO extends MsShopOrderSettingBaseDTO{
    private Long storeId;

    private Long shopId;
    private Long commodityId;
    private BigDecimal quantity;

    private Integer orderedStatus; // 当天是否已经下单 1：是   0：否
    private Long createId;
    private Date createTime;

    //private String orderTime;
    private String productId;
    private BigDecimal productNum;
}

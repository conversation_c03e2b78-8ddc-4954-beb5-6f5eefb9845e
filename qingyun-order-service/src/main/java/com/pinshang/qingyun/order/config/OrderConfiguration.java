package com.pinshang.qingyun.order.config;

import com.pinshang.qingyun.base.interceptor.PfServiceInterceptor;
import com.pinshang.qingyun.base.interceptor.QYServiceInterceptor;
import com.pinshang.qingyun.base.interceptor.RepeatRetryServiceInterceptor;
import com.pinshang.qingyun.base.interceptor.XdaServiceInterceptor;
import com.pinshang.qingyun.infrastructure.exportfile.cache.starter.FileCacheConfiguration;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;
import org.springframework.web.servlet.handler.HandlerInterceptorAdapter;

import javax.annotation.Resource;

/**
 * Created by weican on 2018-11-14.
 */
@Configuration
@Import(FileCacheConfiguration.class)
public class OrderConfiguration {
    @Resource
    private RedissonClient redissonClient;
    @Value("${spring.application.name:unknown}")
    private String applicationName;
    @Bean("repeatRetryServiceInterceptor")
    public HandlerInterceptorAdapter repeatRetryServiceInterceptor(){
        return new RepeatRetryServiceInterceptor(redissonClient,applicationName);
    }
    @Bean("qyServiceInterceptor")
    public HandlerInterceptorAdapter qyServiceInterceptor(){
        return new QYServiceInterceptor(redissonClient);
    }

    @Bean("xdaServiceInterceptor")
    public HandlerInterceptorAdapter xdaServiceInterceptor(){
        return new XdaServiceInterceptor();
    }

    @Bean("pfServiceInterceptor")
    public HandlerInterceptorAdapter pfServiceInterceptor(){
        return new PfServiceInterceptor();
    }
}

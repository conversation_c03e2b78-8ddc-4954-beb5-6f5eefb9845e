package com.pinshang.qingyun.order.enums;

import java.util.EnumSet;

import com.pinshang.qingyun.box.utils.EnumUtils;

/**
 * 赠品方案枚举类
 */
public enum GiftModelTypeEnum {

	ORDERAMOUT(1,"订单金额"),
	COMMODITYNUMBER(2,"商品数量"),
	COMMODITYAMOUT(3,"商品金额");
	
	private Integer code;
	private String remark;
	
	private GiftModelTypeEnum(Integer code, String remark) {
		this.setCode(code);
		this.setRemark(remark);
	}

	public static GiftModelTypeEnum fromName(Integer code) {
		return EnumUtils.fromEnumProperty(GiftModelTypeEnum.class, "code", code);
	}

	public static EnumSet<GiftModelTypeEnum> allList() {
		EnumSet<GiftModelTypeEnum> adverPositionList = EnumSet.allOf(GiftModelTypeEnum.class);
		return adverPositionList;
	}

	public Integer getCode() {
		return code;
	}

	public void setCode(Integer code) {
		this.code = code;
	}

	public String getRemark() {
		return remark;
	}

	public void setRemark(String remark) {
		this.remark = remark;
	}
	
}

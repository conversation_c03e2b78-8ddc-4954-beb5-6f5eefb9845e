package com.pinshang.qingyun.order.dto.consignment;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ConsignmentSaleReturnOrderItemODTO {

    @ApiModelProperty(value = "代销退货单id")
    private Long id;

    /**
     * 商品id
     */
    @ApiModelProperty(value = "商品id")
    private Long commodityId;

    /**
     * 商品编码
     */
    @ApiModelProperty(value = "商品编码")
    private String commodityCode;

    /**
     * 商品名称
     */
    @ApiModelProperty(value = "商品名称")
    private String commodityName;

    /**
     * 包装规格
     */
    @ApiModelProperty(value = "包装规格")
    private BigDecimal commodityPackageSpec;

    /**
     * 计量单位
     */
    @ApiModelProperty(value = "计量单位")
    private String commodityUnitName;

    /**
     * 商品条码
     */
    @ApiModelProperty(value = "商品条码")
    private String barCode;


    /** 退货数量*/
    @ApiModelProperty(value = "退货数量")
    private BigDecimal returnQuantity;

    /** 确认数量*/
    @ApiModelProperty(value = "确认数量")
    private BigDecimal confirmQuantity;

    /** 审核数量*/
    @ApiModelProperty(value = "审核数量")
    private BigDecimal checkQuantity;

    /**
     * 驳回原因
     */
    @ApiModelProperty(value = "驳回原因")
    private String rejectionReason;

    @ApiModelProperty(value = "价格")
    private BigDecimal price;

    /**
     * 是否称重品
     */
    @ApiModelProperty(value = "是否称重0-不称量,1-称重")
    private Integer isWeight;

}

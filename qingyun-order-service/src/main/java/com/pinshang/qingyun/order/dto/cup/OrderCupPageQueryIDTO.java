package com.pinshang.qingyun.order.dto.cup;

import com.pinshang.qingyun.base.page.Pagination;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @Author: sk
 * @Date: 2024/7/22
 */
@Data
public class OrderCupPageQueryIDTO extends Pagination {

    /**订单号 **/
    @ApiModelProperty("订单编码")
    private String orderCode;

    @ApiModelProperty("客户编码")
    private String storeCode;

    @ApiModelProperty("客户名称")
    private String storeName;

    @ApiModelProperty("送货日期开始  yyyy-MM-dd")
    private String orderTimeStart;

    @ApiModelProperty("送货日期结束  yyyy-MM-dd")
    private String orderTimeEnd;

    /**订单状态(0正常,1删除,2取消) **/
    @ApiModelProperty("订单状态  0正常,1删除,2取消")
    private Integer orderStatus;

    @ApiModelProperty("送货员")
    private Long deliverymanId;

    @ApiModelProperty("线路组")
    private Long lineGroupId;

    @ApiModelProperty("督导")
    private Long supervisorId;

    @ApiModelProperty("操作员")
    private Long createId;

    private List<Long> storeIdList;

    @ApiModelProperty("客户id")
    private Long storeId;

    @ApiModelProperty("是否计划单 1是")
    private Integer planOrderType;

    @ApiModelProperty("订单来源")
    private Integer orderType;
}

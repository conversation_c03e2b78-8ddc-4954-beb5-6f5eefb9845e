package com.pinshang.qingyun.order.service;

import cn.jiguang.common.utils.StringUtils;
import com.pinshang.qingyun.base.enums.YesOrNoEnums;
import com.pinshang.qingyun.base.enums.xda.TdaOrderProcessStatusEnum;
import com.pinshang.qingyun.base.enums.xda.XdaOrderProcessStatusEunm;
import com.pinshang.qingyun.box.utils.BeanCloneUtils;
import com.pinshang.qingyun.box.utils.SpringUtil;
import com.pinshang.qingyun.common.dto.DictionaryODTO;
import com.pinshang.qingyun.common.service.DictionaryClient;
import com.pinshang.qingyun.order.dto.xda.tda.LogisticsCommodityItem;
import com.pinshang.qingyun.order.dto.xda.tda.LogisticsDeliveryMessage;
import com.pinshang.qingyun.order.enums.CombTypeEnum;
import com.pinshang.qingyun.order.enums.ProductTypeEnums;
import com.pinshang.qingyun.order.enums.SettleBillCalcTypeEnum;
import com.pinshang.qingyun.order.mapper.*;
import com.pinshang.qingyun.order.mapper.entry.commodity.CommodityItemEntry;
import com.pinshang.qingyun.order.mapper.entry.commodity.CommodityItemEntryList;
import com.pinshang.qingyun.order.model.commodity.Commodity;
import com.pinshang.qingyun.order.model.order.*;
import com.pinshang.qingyun.order.model.shop.Shop;
import com.pinshang.qingyun.order.model.statistics.OrderCompanyCommodityStatistics;
import com.pinshang.qingyun.order.service.comparator.OrderListComparator;
import com.pinshang.qingyun.order.service.xda.v4.ToBService;
import com.pinshang.qingyun.order.vo.order.PickSubOrderItemRespVo;
import com.pinshang.qingyun.order.vo.order.PickSubOrderVo;
import com.pinshang.qingyun.order.vo.order.RealDeliveryQuantityDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.entity.Example;

import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @Author: sk
 * @Date: 2021/12/16
 */
@Service
@Slf4j
public class SubOrderQuantityService {

    @Autowired
    private SubOrderMapper subOrderMapper;
    @Autowired
    private SubOrderItemMapper subOrderItemMapper;
    @Autowired
    private OrderMapper orderMapper;
    @Autowired
    private OrderListMapper orderListMapper;
    @Autowired
    private OrderListGiftMapper orderListGiftMapper;
    @Autowired
    private ToBService toBService;
    @Autowired
    private DictionaryClient dictionaryClient;


    /**
     * 更新t_sub_order_item,t_order、t_order_list、t_order_list_gift 实发数量,实发总金额
     *
     * 1.大仓发货一个subOrder下面单个明细行只会发货一次，多个明细行可能会发货次数>= 0 , <= 明细行size
     * 2.订单回填实发数量优先级: 组合商品、正常品、特惠品(鲜达)、促销配货、赠品
     *
     * 注意：和大仓确认同一个 subOrder 只会发货一次
     * @param pickSubOrderVo
     */
    @Transactional(rollbackFor = Exception.class)
    public Boolean updateOrderRealQuantity(PickSubOrderVo pickSubOrderVo, Long orderId) {

        // 查询原始 subOrderItemList、orderList、orderGiftList
        List<SubOrderItem> subOrderItemList = subOrderItemMapper.queryBySubOrderId(pickSubOrderVo.getSubOrderId());
        List<OrderList> orderList = orderListMapper.queryOrderListByOrderId(orderId);
        List<OrderListGift>	orderGiftList = orderListGiftMapper.queryOrderGiftListByOrderId(orderId);

        Example example = new Example(SubOrder.class);
        example.createCriteria().andEqualTo("orderId", orderId);
        int count = subOrderMapper.selectCountByExample(example);
        if(count > 1){
            // 当一个订单拆成多个subOrder时候，order_list和order_list_gift是按照顺序排序了。
            // 防止大仓发货先发配货和赠品的。导致order_list和order_list_gift回写错误
            // order_list和order_list_gift只匹配出当前subOrder下面的商品
            orderList = getNewOrderList(orderList, subOrderItemList);
            orderGiftList = getNewOrderGiftList(orderGiftList, subOrderItemList);
        }

        //最终逻辑，所有的商品数量全部合并(称重或非称重),依次往下回写数量。
        //  如果多发，则把多的数量回写到第一条
        Map<Long, RealDeliveryQuantityDTO> mergeMap = getMergeItemList(pickSubOrderVo);

        List<SubOrderItem> subOrderItemUpdateList = new ArrayList<>();

        // 获取组合品下面的子商品
        Map<Long, List<CommodityItemEntry>> combMap = getCombMap(orderList);

        List<OrderList> sortOrderList = getSortOrderList(orderList);
        List<OrderListGift>	sortOrderGiftList = getSortOrderGiftList(orderGiftList);
        // 将subOrderList进行排序
        List<SubOrderItem> sortSubOrderItems = getSortSubOrderItems(subOrderItemList);

        //更新subOrderItem无需改动
        for(SubOrderItem subOrderItem : sortSubOrderItems){
            // 只有未更新实发的才更新
            if(subOrderItem.getRealDeliveryQuantity() != null){
                continue;
            }

            if(mergeMap != null && mergeMap.get(subOrderItem.getCommodityId()) != null){
                RealDeliveryQuantityDTO realDeliveryQuantityDTO = mergeMap.get(subOrderItem.getCommodityId());
                BigDecimal realDeliveryQuantity = realDeliveryQuantityDTO.getRealDeliveryQuantity();
                if(realDeliveryQuantity.compareTo(subOrderItem.getQuantity()) >= 0){
                    subOrderItem.setRealDeliveryQuantity(subOrderItem.getQuantity());
                }else {
                    if(realDeliveryQuantity.compareTo(BigDecimal.ZERO) > 0){
                        subOrderItem.setRealDeliveryQuantity(realDeliveryQuantity);
                    }else {
                        subOrderItem.setRealDeliveryQuantity(BigDecimal.ZERO);
                    }
                }

                subOrderItemUpdateList.add(subOrderItem);
                // 依次减下去
                realDeliveryQuantityDTO.setRealDeliveryQuantity(realDeliveryQuantityDTO.getRealDeliveryQuantity().subtract(subOrderItem.getQuantity()));
            }
        }

        // 如果mergeMap 多发，商品没有分摊完。则重新再次分摊到第一条
        if(mergeMap != null){
            Map<Long, RealDeliveryQuantityDTO> filteredMap = mergeMap.entrySet()
                    .stream()
                    .filter(entry -> entry.getValue().getRealDeliveryQuantity().compareTo(BigDecimal.ZERO) > 0)
                    .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue));

            if(filteredMap != null && filteredMap.size() > 0){
                for(SubOrderItem subOrderItem : subOrderItemUpdateList){
                    RealDeliveryQuantityDTO realDeliveryQuantityDTO = filteredMap.get(subOrderItem.getCommodityId());
                    if(realDeliveryQuantityDTO != null && realDeliveryQuantityDTO.getRealDeliveryQuantity().compareTo(BigDecimal.ZERO) > 0){
                        subOrderItem.setRealDeliveryQuantity(subOrderItem.getRealDeliveryQuantity().add(realDeliveryQuantityDTO.getRealDeliveryQuantity()));
                        // 依次减下去
                        realDeliveryQuantityDTO.setRealDeliveryQuantity(BigDecimal.ZERO);
                    }
                }
            }
        }

        // 批量更新 subOrderItemList
        if(CollectionUtils.isNotEmpty(subOrderItemUpdateList)){
            Order order = orderMapper.selectByPrimaryKey(orderId);
            // 如果是大店，不更新实收数量
            if(order.getStallId() != null && order.getStallId() > 0){
                subOrderMapper.batchUpdateCombDeliveryQuantityNOReceive(subOrderItemUpdateList);
            }else {
                subOrderMapper.batchUpdateCombDeliveryQuantity(subOrderItemUpdateList);
            }


            //特价限购需求更新t_order_list和t_order_list_gift 2024-06-21
            List<RealDeliveryQuantityDTO> realDeliveryDTOList = BeanCloneUtils.copyTo(subOrderItemUpdateList, RealDeliveryQuantityDTO.class);
            Map<Long, OrderList> orderListUpdateMap = new HashMap<>();
            Map<Long, OrderListGift> orderListGiftUpdateMap = new HashMap<>();
            // 组合品即是订单商品也是特惠商品。回写order_list和order_list_gift时候，
            // 无法根据 sub_order_item里面的comb_commodity_id来找到order_list和order_list_gift里面的组合主品.导致回写重复覆盖了
            Map<Long, Set<Long>> combOrderListMap = new HashMap();
            Map<Long, Set<Long>> combOrderGiftListMap = new HashMap();
            // subOrderItem有更新，则更新t_order_list 和 t_order_list_gift
            for(RealDeliveryQuantityDTO updateItem : realDeliveryDTOList){
                // 此子单是组合品明细
                if(updateItem.getCombCommodityId() != null){
                    getCombOrderListMapBySubOrderItem(sortOrderList, updateItem, combMap, orderListUpdateMap, combOrderListMap);
                    getCombOrderListGiftMapBySubOrderItem(sortOrderGiftList, updateItem, combMap, orderListGiftUpdateMap, combOrderGiftListMap);
                }else {
                    getNormalOrderListMapBySubOrderItem(sortOrderList, updateItem, orderListUpdateMap);
                    getNormalOrderListGiftMapBySubOrderItem(sortOrderGiftList, updateItem, orderListGiftUpdateMap);
                }
            }

            if(!orderListUpdateMap.isEmpty()){
                List<OrderList> orderListUpdateList = new ArrayList<>();
                orderListUpdateMap.forEach((k,v) -> {
                    orderListUpdateList.add(v);
                });
                orderListMapper.batchUpdateOrderList(orderListUpdateList);
            }

            if(!orderListGiftUpdateMap.isEmpty()){
                List<OrderListGift> orderGiftListUpdateList = new ArrayList<>();
                orderListGiftUpdateMap.forEach((k,v) -> {
                    orderGiftListUpdateList.add(v);
                });
                orderListGiftMapper.batchUpdateOrderGiftList(orderGiftListUpdateList);
            }
        }

        // 1.标记子单已回填实发数量和实发金额
        // 2.更新订单实发总金额
        // 3.如果子单全部实发，更新realSubOrderDoneStatus = 1 . 订单所有子单是否出库完成（不包括直送类型） 等于1-完成
        updateOrderInfo(pickSubOrderVo, orderId);

        return Boolean.TRUE;
    }

    /**
     *  1.标记子单已回填实发数量和实发金额
     *  2.更新订单实发总金额
     *  3.如果子单全部实发，更新realSubOrderDoneStatus = 1 . 订单所有子单是否出库完成（不包括直送类型） 等于1-完成
     * @param pickSubOrderVo
     * @param orderId
     */
    @Transactional(rollbackFor = Exception.class)
    public void updateOrderInfo(PickSubOrderVo pickSubOrderVo, Long orderId){
        // 1.标记子单已回填实发数量和实发金额
        if(CollectionUtils.isNotEmpty(pickSubOrderVo.getSubOrderIds())){
            for(Long subOrderId : pickSubOrderVo.getSubOrderIds()){
                // 更新 SubOrder.writebackRealQtyFlag = true
                updateSubOrderWritebackRealQty(subOrderId);
            }
        }else {
            // 更新 SubOrder.writebackRealQtyFlag = true
            updateSubOrderWritebackRealQty(pickSubOrderVo.getSubOrderId());
        }

        Boolean allOut = isAllOut(orderId);

        // 查询t_order_list_gift的合计realTotalPrice，排除掉子商品
        BigDecimal realTotalPrice = orderListMapper.queryRealTotalPrice(orderId);

        Order order = orderMapper.selectByPrimaryKey(orderId);

        // 2.更新订单实发总金额
        order.setRealTotalPrice(realTotalPrice);
        order.setRealOrderTime(pickSubOrderVo.getStockOutTime());
        setOrderSettleTimeByOrder(order);
        //所有子单出库(实发数量全部不为空) 修改订单状态为配送中
        if(allOut && !pickSubOrderVo.getIsTda()){
            order.setProcessStatus(XdaOrderProcessStatusEunm.DELIVERYING.getCode());
        }

        order.setUpdateTime(new Date());

        /**
         *
         *   http://*************/story-view-14047.html
         *  调用验证 出库完成是否需要跳过结算完成状态设置
         *
         *  20250716 追加 设置，特定情况下 配送完成 才需结算
         */
        boolean isSkipRealSubDoneStatus = shouldSkipUpdate(order);

        if(allOut && !isSkipRealSubDoneStatus){
            /**
             * 3.如果子单全部实发，更新realSubOrderDoneStatus = 1
             * 20250716 追加 设置跳过 业务类型的白名单， 特定业务类型 需要再 配送完成 才能结算
             */

            order.setRealSubOrderDoneStatus(YesOrNoEnums.YES.getCode());
        }
        orderMapper.updateByPrimaryKeySelective(order);
    }

    /**
     * 判断是否应跳过更新 realSubOrderDoneStatus 字段
     * @param order 订单对象
     * @return 是否跳过更新，true 表示跳过，false 表示不跳过
     */
    private boolean shouldSkipUpdate(Order order) {
        try{
            DictionaryODTO skipUpdateDelivery = dictionaryClient.getDictionaryByCode("SKIP_UPDATE_DELIVERY");
            // 配置存在且有有效值时才进行处理
            if (skipUpdateDelivery != null && StringUtils.isNotEmpty(skipUpdateDelivery.getOptionValue())) {
                // 提前获取订单业务类型，避免重复调用
                Integer orderBusinessType = order.getBusinessType();
                // 分割配置值为业务类型数组
                String[] businessTypes = skipUpdateDelivery.getOptionValue().split(",");

                for (String typeStr : businessTypes) {
                    // 跳过空字符串，处理配置中的可能的空格
                    String trimmedType = typeStr.trim();
                    if (trimmedType.isEmpty()) {
                        continue;
                    }

                    try {
                        // 转换为整数并比较
                        int businessType = Integer.parseInt(trimmedType);
                        if (orderBusinessType != null && orderBusinessType.equals(businessType)) {
                            return true;
                        }
                    } catch (NumberFormatException e) {
                        log.error("字典配置中的业务类型格式错误，值为: {}", trimmedType, e);
                    }
                }
            }
        }catch (Exception e){
            log.error("获取字典配置 处理订单{} 信息失败:",order.getOrderCode(), e);
            return true;
        }
        return false;
    }

    /**
     * 更新 SubOrder.writebackRealQtyFlag = true
     * @param subOrderId
     */
    private void updateSubOrderWritebackRealQty(Long subOrderId) {
        SubOrder subOrder = new SubOrder();
        subOrder.setId(subOrderId);
        subOrder.setWritebackRealQtyFlag(true);
        subOrderMapper.updateByPrimaryKeySelective(subOrder);
    }

    /**
     * 判断订单所有子单是否出库完成
     * @param orderId
     * @return
     */
    @NotNull
    public Boolean isAllOut(Long orderId) {
        Example subOrderExample = new Example(SubOrder.class);
        subOrderExample.createCriteria().andEqualTo("orderId", orderId)
                .andNotEqualTo("status", 2);
        List<SubOrder> subOrderList = subOrderMapper.selectByExample(subOrderExample);
        Boolean allOut = true;
        for(SubOrder subOrder : subOrderList){
            if(subOrder.getWritebackRealQtyFlag() == null || !subOrder.getWritebackRealQtyFlag()){
                allOut = false;
                break;
            }
        }
        return allOut;
    }


    private List<OrderListGift> getNewOrderGiftList(List<OrderListGift> orderGiftList, List<SubOrderItem> subOrderItemList) {
        List<OrderListGift>	newOrderGiftList = new ArrayList<>();
        for(OrderListGift o : orderGiftList){
            Boolean isEqual = false;
            for(SubOrderItem s : subOrderItemList){
                Boolean b1 = (o.getCommodityId().equals(s.getCommodityId())
                        && o.getCommodityNum().compareTo(s.getQuantity()) == 0
                        && o.getCommodityPrice().compareTo(s.getPrice()) == 0
                        && o.getType().equals(s.getType()));
                Boolean b2 = o.getCommodityId().equals(s.getCombCommodityId());
                if( b1 || b2){
                    isEqual = true;
                    break;
                }
            }
            if(isEqual){
                newOrderGiftList.add(o);
            }
        }
        return newOrderGiftList;
    }

    private List<OrderList> getNewOrderList(List<OrderList> orderList, List<SubOrderItem> subOrderItemList) {
        List<OrderList> newOrderList = new ArrayList<>();
        for(OrderList o : orderList){
            Boolean isEqual = false;
            for(SubOrderItem s : subOrderItemList){
                Boolean b1 = (o.getCommodityId().equals(s.getCommodityId())
                        && o.getCommodityNum().compareTo(s.getQuantity()) == 0
                        && o.getCommodityPrice().compareTo(s.getPrice()) == 0
                        && o.getType().equals(s.getType()));
                Boolean b2 = o.getCommodityId().equals(s.getCombCommodityId());
                if(b1 || b2){
                    isEqual = true;
                    break;
                }
            }
            if(isEqual){
                newOrderList.add(o);
            }
        }
        return newOrderList;
    }


    /**
     * 设置结算日期
     * @param order
     */

    private void setOrderSettleTimeByOrder(Order order) {
        order.setSettleOrderTime(order.getOrderTime());
        try {
            if(order.getStoreTypeId()==null){
                order.setSettleOrderTime(order.getOrderTime());
            }else {
                /**
                 * settleBillCalcType 结算类型：
                 * 0：不结算，
                 * 1：鲜达模式=取最小（数量：实发小取实发，下单小去下单，日期：实发晚取实发日期，送货日期晚取送货日期），
                 * 2：批发模式， （结算日期=出库日期、下单日期谁大取谁，结算金额取出库金额）
                 * 3：门店结算（取实发），
                 * 4：取下单
                 * @param id 客户类型Id
                 * @return
                 */
                Integer settleBillCalcType = toBService.findSettleBillCalcTypeById(order.getStoreTypeId());
                if(settleBillCalcType==null){
                    log.error("客户类型t_store_type_desc结算方式没有初始值：{}",order.getStoreTypeId());
                    order.setSettleOrderTime(order.getOrderTime());
                }

                if(settleBillCalcType == SettleBillCalcTypeEnum.PF.getCode()
                        || settleBillCalcType == SettleBillCalcTypeEnum.XD.getCode()){
                    if(order.getOrderTime().after(order.getRealOrderTime())){
                        order.setSettleOrderTime(order.getOrderTime());
                    }else{
                        order.setSettleOrderTime(order.getRealOrderTime());
                    }
                }else if(settleBillCalcType == SettleBillCalcTypeEnum.SHOP.getCode()){
                    order.setSettleOrderTime(order.getRealOrderTime());
                }else{
                    order.setSettleOrderTime(order.getOrderTime());
                }
            }
        } catch (Exception e) {
            log.error("=================================================================");
            log.error("设置结算日期异常 order {}", order);
            log.error("设置结算日期异常", e);
            log.error("=================================================================");
        }
    }


    /**
     * 所有子单出库(实发数量全部不为空) 修改订单状态为配送中
     * @param orderId
     * @return
     */
    @NotNull
    private Boolean isDeliverying(Long orderId) {
        Boolean isDeliverying = Boolean.FALSE;
        Example subOrderEx = new Example(SubOrder.class);
        subOrderEx.createCriteria().andEqualTo("orderId", orderId).andNotEqualTo("status", 2);
        List<SubOrder> subOrders = subOrderMapper.selectByExample(subOrderEx);
        if (SpringUtil.isNotEmpty(subOrders)) {
            Set<Long> subOrderIds = subOrders.stream().map(SubOrder::getId).collect(Collectors.toSet());
            Example subOrderItemEx = new Example(SubOrderItem.class);
            subOrderItemEx.createCriteria().andIn("subOrderId", subOrderIds);
            List<SubOrderItem> subOrderItems = subOrderItemMapper.selectByExample(subOrderItemEx);
            List<SubOrderItem> collect = subOrderItems.stream().filter(item -> null == item.getRealDeliveryQuantity()).collect(Collectors.toList());
            if (SpringUtil.isEmpty(collect)) {
                isDeliverying = Boolean.TRUE;
            }
        }
        return isDeliverying;
    }


    /**
     * 根据组合品commodityIdList 获取组合品下面的子商品信息
     * @param orderList
     * @return
     */
    public Map<Long, List<CommodityItemEntry>> getCombMap(List<OrderList> orderList){
        Map<Long, List<CommodityItemEntry>> combMap = new HashMap<>();
        List<OrderList> orderCombList = orderList.stream().filter(p -> CombTypeEnum.COMB.getCode().equals(p.getCombType())).collect(Collectors.toList());
        if(CollectionUtils.isNotEmpty(orderCombList)){
            List<Long> combCommodityIdList = orderCombList.stream().map(item -> item.getCommodityId()).collect(Collectors.toList());
            List<CommodityItemEntryList> combList = toBService.getCommodityItemListByCommodityIdList(combCommodityIdList);
            if(CollectionUtils.isNotEmpty(combList)){
                combMap = combList.stream().collect(Collectors.toMap(CommodityItemEntryList::getCommodityId, CommodityItemEntryList::getCommodityItemEntry));
            }
        }
        return combMap;
    }

    /**
     * 最终逻辑，所有的商品数量全部合并(称重或非称重),依次往下回写数量。
     * 如果多发，则把多的数量回写到第一条
     * @param pickSubOrderVo
     * @param subOrderItemList
     * @return
     */
    public  Map<Long, RealDeliveryQuantityDTO> getMergeItemList(PickSubOrderVo pickSubOrderVo){
        Map<Long, RealDeliveryQuantityDTO> mergeMap = new HashMap<>();

        List<PickSubOrderItemRespVo> resultList = pickSubOrderVo.getItemList().stream()
                .collect(Collectors.toMap(PickSubOrderItemRespVo::getCommodityId, a -> a, (o1, o2)-> {
                    o1.setRealDeliveryQuantity(o1.getRealDeliveryQuantity().add(o2.getRealDeliveryQuantity()));
                    return o1;
                })).values().stream().collect(Collectors.toList());

        resultList.forEach(item -> {
            RealDeliveryQuantityDTO realDeliveryQuantityDTO = new RealDeliveryQuantityDTO();
            realDeliveryQuantityDTO.setCommodityId(item.getCommodityId());
            realDeliveryQuantityDTO.setRealDeliveryQuantity(item.getRealDeliveryQuantity());
            mergeMap.put(item.getCommodityId(), realDeliveryQuantityDTO);
        });

        return mergeMap;
    }

    /**
     * 获取排序的subOrderItems
     * 根据orderList里面组合品的排序，subOrderItem里面优先找到orderList排第一的组合品
     * @param subOrderItemList
     * @return
     */
    public List<SubOrderItem> getSortSubOrderItems(List<SubOrderItem> subOrderItemList){
        subOrderItemList = subOrderItemList.stream()
                .sorted(OrderListComparator.orderListCombComparator())
                .collect(Collectors.toList());
        return subOrderItemList;
    }


    /**
     * 获取排序的order_list
     * @return
     */
    public List<OrderList> getSortOrderList(List<OrderList> orderList){
        orderList = orderList.stream()
                .sorted(OrderListComparator.orderListCombComparator())
                .collect(Collectors.toList());
        return orderList;
    }




    /**
     * 获取排序的 order_gift_list
     * @return
     */
    public List<OrderListGift> getSortOrderGiftList(List<OrderListGift> orderGiftList){
        orderGiftList = orderGiftList.stream()
                .sorted(OrderListComparator.orderListCombComparator())
                .collect(Collectors.toList());
        return orderGiftList;
    }

    /**
     * 组合品 根据sub_order_item 找 order_list
     * @param orderList
     * @param updateItem
     * @param orderListUpdateMap
     * @return
     */
    public Map<Long, OrderList> getCombOrderListMapBySubOrderItem(List<OrderList> orderList, RealDeliveryQuantityDTO updateItem, Map<Long, List<CommodityItemEntry>> combMap, Map<Long, OrderList> orderListUpdateMap, Map<Long, Set<Long>> combOrderListMap){
        List<OrderList> orderListUpdateList = new ArrayList<>();
        // 组合主品下面的子商品明细
        List<CommodityItemEntry> combItems = combMap.get(updateItem.getCombCommodityId());
        Map<String, CommodityItemEntry> combItemMap = combItems.stream().collect(Collectors.toMap(CommodityItemEntry::getCommodityItemId, Function.identity()));

        //组合商品C的实发数量=Max（子品A的实发/1，子品B的实发/2），然后再向上取整
        BigDecimal combRealDeliveryQuantity = BigDecimal.ZERO;

        OrderList masterOrderList = new OrderList(); // 组合主品，每次组合主品下面的子品进来都要重新计算
        for(OrderList p : orderList){
            // 组合主品
            if(CombTypeEnum.COMB.getCode().equals(p.getCombType()) && p.getCommodityId().equals(updateItem.getCombCommodityId())){
                Set<Long> ids = combOrderListMap.get(p.getId());
                if(CollectionUtils.isEmpty(ids) || !ids.contains(updateItem.getCommodityId())){
                    masterOrderList = p;
                    if(CollectionUtils.isEmpty(ids)){
                        ids = new HashSet<>();
                    }
                    ids.add(updateItem.getCommodityId());
                    combOrderListMap.put(p.getId(), ids);
                    break;
                }
            }
        }

        // 根据sub_order_item里面的实发数量，回写order_list里面的组合子品的实发数量
        Optional<OrderList> vo = orderList.stream().filter(p -> CombTypeEnum.COMB_CHILD.getCode().equals(p.getCombType())
                && p.getCombCommodityId().equals(updateItem.getCombCommodityId())
                && p.getCommodityId().equals(updateItem.getCommodityId())
                && !p.getOrderDone()).findFirst();
        if (vo.isPresent()) {
            OrderList p = vo.get();
            p.setRealQuantity(updateItem.getRealDeliveryQuantity());
            p.setRealTotalPrice(p.getTotalPrice().divide(p.getCommodityNum(),6, BigDecimal.ROUND_HALF_UP).multiply(p.getRealQuantity()).setScale(2, BigDecimal.ROUND_HALF_UP));
            p.setOrderDone(true);

            CommodityItemEntry item = combItemMap.get(p.getCommodityId() + "");
            combRealDeliveryQuantity = p.getRealQuantity().divide(item.getCommodityNum(),0,BigDecimal.ROUND_UP).max(combRealDeliveryQuantity);
            orderListUpdateList.add(p);
        }

        combRealDeliveryQuantity = combRealDeliveryQuantity.max((masterOrderList.getRealQuantity() != null ? masterOrderList.getRealQuantity() : new BigDecimal("0")));
        masterOrderList.setRealQuantity(combRealDeliveryQuantity);
        masterOrderList.setRealTotalPrice(masterOrderList.getTotalPrice().divide(masterOrderList.getCommodityNum(),6, BigDecimal.ROUND_HALF_UP).multiply(masterOrderList.getRealQuantity()).setScale(2, BigDecimal.ROUND_HALF_UP));
        orderListUpdateList.add(masterOrderList);

        orderListUpdateList.forEach(p -> orderListUpdateMap.put(p.getId(), p));
        return orderListUpdateMap;
    }


    /**
     * 组合品 根据sub_order_item 找 order_list_gift
     * @param orderListGift
     * @param updateItem
     * @param orderListGiftUpdateMap
     * @return
     */
    public Map<Long, OrderListGift> getCombOrderListGiftMapBySubOrderItem(List<OrderListGift> orderListGift, RealDeliveryQuantityDTO updateItem,Map<Long, List<CommodityItemEntry>> combMap, Map<Long, OrderListGift> orderListGiftUpdateMap, Map<Long, Set<Long>> combOrderGiftListMap){
        List<OrderListGift> orderListGiftUpdateList = new ArrayList<>();
        // 组合主品下面的子商品明细
        List<CommodityItemEntry> combItems = combMap.get(updateItem.getCombCommodityId());
        Map<String, CommodityItemEntry> combItemMap = combItems.stream().collect(Collectors.toMap(CommodityItemEntry::getCommodityItemId, Function.identity()));

        //组合商品C的实发数量=Max（子品A的实发/1，子品B的实发/2），然后再向上取整
        BigDecimal combRealDeliveryQuantity = BigDecimal.ZERO;

        OrderListGift masterOrderListGift = new OrderListGift(); // 组合主品，每次组合主品下面的子品进来都要重新计算
        for(OrderListGift p : orderListGift){
            // 组合主品
            if(CombTypeEnum.COMB.getCode().equals(p.getCombType()) && p.getCommodityId().equals(updateItem.getCombCommodityId())){
                Set<Long> ids = combOrderGiftListMap.get(p.getId());
                if(CollectionUtils.isEmpty(ids) || !ids.contains(updateItem.getCommodityId())){
                    masterOrderListGift = p;
                    if(CollectionUtils.isEmpty(ids)){
                        ids = new HashSet<>();
                    }
                    ids.add(updateItem.getCommodityId());
                    combOrderGiftListMap.put(p.getId(), ids);
                    break;
                }
            }
        }

        // 根据sub_order_item里面的实发数量，回写order_gift_list里面的组合子品的实发数量
        Optional<OrderListGift> vo = orderListGift.stream().filter(p -> CombTypeEnum.COMB_CHILD.getCode().equals(p.getCombType())
                && p.getCombCommodityId().equals(updateItem.getCombCommodityId())
                && p.getCommodityId().equals(updateItem.getCommodityId())
                && !p.getGiftDone()).findFirst();
        if (vo.isPresent()) {
            OrderListGift p = vo.get();
            p.setRealQuantity(updateItem.getRealDeliveryQuantity());
            p.setRealTotalPrice(p.getTotalPrice().divide(p.getCommodityNum(),6, BigDecimal.ROUND_HALF_UP).multiply(p.getRealQuantity()).setScale(2, BigDecimal.ROUND_HALF_UP));
            p.setGiftDone(true);

            CommodityItemEntry item = combItemMap.get(p.getCommodityId() + "");
            combRealDeliveryQuantity = p.getRealQuantity().divide(item.getCommodityNum(),0,BigDecimal.ROUND_UP).max(combRealDeliveryQuantity);
            orderListGiftUpdateList.add(p);
        }

        combRealDeliveryQuantity = combRealDeliveryQuantity.max((masterOrderListGift.getRealQuantity() != null ? masterOrderListGift.getRealQuantity() : new BigDecimal("0")));
        masterOrderListGift.setRealQuantity(combRealDeliveryQuantity);
        masterOrderListGift.setRealTotalPrice(masterOrderListGift.getTotalPrice().divide(masterOrderListGift.getCommodityNum(),6, BigDecimal.ROUND_HALF_UP).multiply(masterOrderListGift.getRealQuantity()).setScale(2, BigDecimal.ROUND_HALF_UP));
        orderListGiftUpdateList.add(masterOrderListGift);

        orderListGiftUpdateList.forEach(p -> orderListGiftUpdateMap.put(p.getId(), p));
        return orderListGiftUpdateMap;
    }

    /**
     * 非组合品 根据sub_order_item 找 order_list
     * @param orderList
     * @param updateItem
     * @param orderListUpdateMap
     * @return
     */
    public void getNormalOrderListMapBySubOrderItem(List<OrderList> orderList, RealDeliveryQuantityDTO updateItem, Map<Long, OrderList> orderListUpdateMap){
        List<OrderList> orderListUpdateList = new ArrayList<>();

        Optional<OrderList> vo = orderList.stream().filter(p ->
                (p.getRealQuantity() == null || p.getRealQuantity().compareTo(BigDecimal.ZERO) == 0)
               && updateItem.getCommodityId().equals(p.getCommodityId()) && !p.getOrderDone()).findFirst();
        if (vo.isPresent()) {
            OrderList p = vo.get();
            p.setRealQuantity(updateItem.getRealDeliveryQuantity());
            //p.setRealTotalPrice(p.getRealQuantity().multiply(p.getCommodityPrice()));
            p.setRealTotalPrice(p.getTotalPrice().divide(p.getCommodityNum(),6, BigDecimal.ROUND_HALF_UP).multiply(p.getRealQuantity()).setScale(2, BigDecimal.ROUND_HALF_UP));
            p.setOrderDone(true);
            orderListUpdateList.add(p);
        }

        orderListUpdateList.forEach(p -> orderListUpdateMap.put(p.getId(), p));
    }

    /**
     * 非组合品 根据sub_order_item 找 order_list_gift
     * @param orderListGift
     * @param updateItem
     * @param orderListGiftUpdateMap
     * @return
     */
    public void getNormalOrderListGiftMapBySubOrderItem(List<OrderListGift> orderListGift, RealDeliveryQuantityDTO updateItem, Map<Long, OrderListGift> orderListGiftUpdateMap){
        List<OrderListGift> orderListUpdateList = new ArrayList<>();
        Optional<OrderListGift> vo = orderListGift.stream().filter(p ->
                (p.getRealQuantity() == null || p.getRealQuantity().compareTo(BigDecimal.ZERO) == 0)
                        && updateItem.getCommodityId().equals(p.getCommodityId()) && !p.getGiftDone()).findFirst();
        if (vo.isPresent()) {
            OrderListGift p = vo.get();
            p.setRealQuantity(updateItem.getRealDeliveryQuantity());
            //p.setRealTotalPrice(p.getRealQuantity().multiply(p.getCommodityPrice()));
            p.setRealTotalPrice(p.getTotalPrice().divide(p.getCommodityNum(),6, BigDecimal.ROUND_HALF_UP).multiply(p.getRealQuantity()).setScale(2, BigDecimal.ROUND_HALF_UP));
            p.setGiftDone(true);
            orderListUpdateList.add(p);
        }

        orderListUpdateList.forEach(p -> orderListGiftUpdateMap.put(p.getId(), p));
    }
}

package com.pinshang.qingyun.order.model.gift;

import java.math.BigDecimal;

import javax.persistence.Entity;
import javax.persistence.Table;

import com.pinshang.qingyun.base.po.BaseIDPO;

@Entity
@Table(name = "t_gift_model_condition")
public class GiftModelCondition extends BaseIDPO {
    private Long giftModelId;
    private Integer conditionType;
    private BigDecimal conditionValue;
    private Integer status;
    private String remark;
    
	public Long getGiftModelId(){
        return giftModelId;
    }
    public void setGiftModelId(Long giftModelId){
        this.giftModelId = giftModelId;
    }
    public Integer getConditionType(){
        return conditionType;
    }
    public void setConditionType(Integer conditionType){
        this.conditionType = conditionType;
    }
    public BigDecimal getConditionValue(){
        return conditionValue;
    }
    public void setConditionValue(BigDecimal conditionValue){
        this.conditionValue = conditionValue;
    }
    public Integer getStatus(){
        return status;
    }
    public void setStatus(Integer status){
        this.status = status;
    }
    public String getRemark(){
        return remark;
    }
    public void setRemark(String remark){
        this.remark = remark;
    }
}
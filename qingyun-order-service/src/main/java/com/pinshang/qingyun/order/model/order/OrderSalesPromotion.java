package com.pinshang.qingyun.order.model.order;

import com.pinshang.qingyun.base.po.BaseIDPO;
import lombok.Data;

import javax.persistence.Entity;
import javax.persistence.Table;

/**
 * @<PERSON> <PERSON><PERSON><PERSON>
 * @Date 2024/1/10 11:31
 */
@Entity
@Table(name = "t_order_sales_promotion")
@Data
public class OrderSalesPromotion extends BaseIDPO {

    private Long orderId;

    private Long promotionId;

    private String promotionName;

    private String tips;

    private Integer promotionType;

    private Integer fullStatus;

    private String promotionRules;
}

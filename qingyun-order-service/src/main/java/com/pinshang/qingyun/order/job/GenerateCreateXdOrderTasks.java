package com.pinshang.qingyun.order.job;

import com.pinshang.qingyun.box.utils.DateUtil;
import com.pinshang.qingyun.order.mapper.MdShopOrderSettingMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.quartz.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.quartz.SchedulerFactoryBean;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.List;


@Component
@Slf4j
public class GenerateCreateXdOrderTasks {

    @Autowired
    private SchedulerFactoryBean schedulerFactoryBean;

    @Autowired
    private MdShopOrderSettingMapper mdShopOrderSettingMapper;

    /**
     * 每天凌晨 5分 时候开始种植任务
     */
    /*@Scheduled(cron ="0 5 0 * * ?")*/
    public void scheduleJobs(){
        String nowHour = DateUtil.getDateFormate(new Date(),"HH:mm");
        log.info("开始种植任务--------------------------------------------------------------"+new Date());
        List<String> endTimeList = mdShopOrderSettingMapper.getEndTimeList(nowHour);
        generateJob(endTimeList);

    }


    public void generateJob(List<String> endTimeList){
        if(CollectionUtils.isNotEmpty(endTimeList)){
            for(String endTime : endTimeList){
                Scheduler scheduler = schedulerFactoryBean.getScheduler();
                startJob(scheduler, endTime);
            }
        }
    }

    private void startJob(Scheduler scheduler, String endTime)  {
    	if(StringUtils.isBlank(endTime)){
    		return;
    	}
        JobDataMap jobDataMap = new JobDataMap();
        jobDataMap.put("endTime", endTime);
        String jobName = "job" + endTime;
        String groupName = "group2xd";


        // 定义一个 job，并绑定到 GenerateCreateXdOrderJob.class
        // 这里并不会马上创建一个 GenerateCreateXdOrderJob 实例，实例创建是在 scheduler 安排任务触发执行时创建的
        JobDetail jobDetail = JobBuilder.newJob(GenerateCreateXdOrderJob.class).withIdentity(jobName, groupName).usingJobData(jobDataMap).build();

        String cronExp = buildCronExpression(endTime);
        CronScheduleBuilder scheduleBuilder = CronScheduleBuilder.cronSchedule(cronExp);

        // 声明一个触发器
        CronTrigger cronTrigger = TriggerBuilder.newTrigger().withIdentity(jobName, groupName).withSchedule(scheduleBuilder).build();

        // 安排执行任务
        Date date = null;
        try {
            date = scheduler.scheduleJob(jobDetail, cronTrigger);
        } catch (SchedulerException e) {
           log.error("鲜道种植任务异常",e);
        }
        log.info("种植任务的时间为----------------------------------------"+date);
    }




    private String buildCronExpression(String endTime) {
        String[] times = endTime.split(":");
        String  cronExpression = "0 " + times[1] + " " + times[0] + " * * ?";
        return cronExpression;
    }
}

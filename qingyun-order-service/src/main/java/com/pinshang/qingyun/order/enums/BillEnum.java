package com.pinshang.qingyun.order.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;


/**
 * 状态枚举
 *
 * <AUTHOR>
 * @since 2023/04/18
 */
@Getter
@NoArgsConstructor
@AllArgsConstructor
public enum BillEnum {

    /**
     * 红冲
     */
    RED_INK("ORDER_RED_INK", "红冲"),
    /**
     * 充值
     */
    RECHARGE("ORDER_RECHARGE", "充值"),
    /**
     * 扣款
     */
    DEDUCTION("ORDER_DEDUCTION", "扣款"),

    ;

    /**
     * 类型码
     */
    private String code;

    /**
     * 类型描述
     */
    private String desc;

    // 静态常量字段
    public static final String RED_INK_DESC = "红冲";

    public static final String RECHARGE_DESC = "充值";

    public static final String DEDUCTION_DESC = "扣款";

    /**
     * 解析
     *
     * @param code 名称
     * @return 枚举类
     */
    public static BillEnum explain(String code) {
        for (BillEnum billEnum : BillEnum.values()) {
            if (billEnum.code.equals(code)) {
                return billEnum;
            }
        }
        return null;
    }

}

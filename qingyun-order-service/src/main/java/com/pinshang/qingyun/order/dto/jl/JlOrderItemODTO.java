package com.pinshang.qingyun.order.dto.jl;

import com.pinshang.qingyun.base.enums.YesOrNoEnums;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * @Author: liuZhen
 * @DateTime: 2022/5/11 14:06
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class JlOrderItemODTO {

    @ApiModelProperty("商品id")
    private String commodityId;
    @ApiModelProperty("单价=给玖琅的供价")
    private BigDecimal commodityPrice;
    @ApiModelProperty("订货数量")
    private BigDecimal commodityNum;
    @ApiModelProperty("商品总金额")
    private BigDecimal totalPrice;
    /** 实发数量 **/
    @ApiModelProperty("实发数量")
    private BigDecimal realQuantity;
    /** 实发总金额 **/
    @ApiModelProperty("实发总金额")
    private BigDecimal realTotalPrice;
    private Integer type=1;
}

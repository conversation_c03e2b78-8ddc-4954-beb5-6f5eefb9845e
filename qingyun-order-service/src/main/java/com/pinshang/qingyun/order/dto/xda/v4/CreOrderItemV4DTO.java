package com.pinshang.qingyun.order.dto.xda.v4;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.pinshang.qingyun.base.configure.codec.DecimalSerializerKeep2;
import com.pinshang.qingyun.order.enums.ProductTypeEnums;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @Date 2023/12/26 15:46
 */
@Data
@ApiModel
public class CreOrderItemV4DTO {
    @ApiModelProperty("商品id")
    private Long commodityId;
    @ApiModelProperty("商品编码")
    private String commodityCode;
    @ApiModelProperty("原价")
    @JsonSerialize(using = DecimalSerializerKeep2.class)
    private BigDecimal commodityPrice;
    @ApiModelProperty(value ="是否有特价：0=无特价，1=普通特价",position = 14)
    private Integer isSpecialPrice;
    @ApiModelProperty("特价")
    @JsonSerialize(using = DecimalSerializerKeep2.class)
    private BigDecimal specialPrice;

    @ApiModelProperty("是否满足特惠")
    private Boolean isThInvalidate = Boolean.FALSE;
    @ApiModelProperty(value = "特惠价格")
    @JsonSerialize(using = DecimalSerializerKeep2.class)
    private BigDecimal thPrice;

    private BigDecimal sumPrice;
    @ApiModelProperty("预估到手价 -参加满折商品展示")
    @JsonSerialize(using = DecimalSerializerKeep2.class)
    private BigDecimal deliveryPrice;
    @ApiModelProperty("商品数量")
    private BigDecimal quantity;
    @ApiModelProperty("配送模式")
    private Integer logisticsModel;
    @ApiModelProperty("默认供应商id")
    private Long supplierId;
    @ApiModelProperty("默认仓库id")
    private Long warehouseId;
    @ApiModelProperty("商品类型")
    private ProductTypeEnums type;
    @ApiModelProperty("赠送方案id")
    private Long conditionId;
    //是否称重(单位是否为kg)0-不称量,1-称重
    private Integer isWeight;
    private BigDecimal commodityPackageSpec;
    @ApiModelProperty(value ="最早可订货时间,订单使用",hidden = true)
    private Date beginDeliveryTime;
    @ApiModelProperty(value ="最晚可订货时间,订单使用",hidden = true)
    private Date endDeliveryTime;
    /**
     * 商品类型：1-普通商品，2-组合商品
     */
    @ApiModelProperty(value ="商品类型",hidden = true)
    private Integer productType;
    /**
     * 商品类型-1-非组合 2-组合  3-组合子品
     */
    private Integer combType = 1;
    /**
     * 属于组合商品id
     */
    private Long combCommodityId;
    /**
     * 金额占比
     */
    private BigDecimal proportion;

    @ApiModelProperty("特价Id")
    private Long pricePromotionId;

    @ApiModelProperty("优惠券分摊金额")
    private BigDecimal couponDiscountAmount;

    @ApiModelProperty("优惠券ID")
    private Long couponId;

    @ApiModelProperty("用户券ID")
    private Long couponUserId;

    public String getClassifyKey(){
        boolean flag = this.logisticsModel == null ||  (this.logisticsModel != 0 && this.warehouseId == null)
                || (this.logisticsModel == 0 && this.supplierId == null);
        if(flag){
            return "classifyKey";
        }else {
            return this.logisticsModel+"_"+this.warehouseId+"_"+this.supplierId;
        }
    }

    //当前方法获取的不是落地的支付价格(如果存在满折的情况下,需要单独计算)
    public BigDecimal salePrice(){
        if(this.isThInvalidate){
            return thPrice;
        }
        return this.isSpecialPrice == 1 ? this.specialPrice :  this.commodityPrice;
    }

    public BigDecimal saleAmount(){
        return salePrice().multiply(quantity).setScale(2,BigDecimal.ROUND_HALF_UP);
    }


    public BigDecimal saleQuantity(){
        return quantity;
    }
}

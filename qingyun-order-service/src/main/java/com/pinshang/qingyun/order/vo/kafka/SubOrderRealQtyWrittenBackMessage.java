package com.pinshang.qingyun.order.vo.kafka;

import java.math.BigDecimal;
import java.util.List;

import lombok.Getter;
import lombok.Setter;

/**
 * 子单回写实发数量消息
 * <AUTHOR>
 *
 */
@Getter
@Setter
public class SubOrderRealQtyWrittenBackMessage {

	private Long orderId;
	
	private Long subOrderId;
	
	private List<Item> items;	
	
	@Getter
	@Setter
	public static final class Item {
		private Long commodityId;
		private BigDecimal realDeliveryQuantity;
	}
}

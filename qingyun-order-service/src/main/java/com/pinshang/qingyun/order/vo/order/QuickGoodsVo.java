package com.pinshang.qingyun.order.vo.order;

import com.pinshang.qingyun.order.mapper.entry.stall.StallEntry;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;


public class QuickGoodsVo extends StallEntry {
	private Long storeId;
	private Long enterpriseId;
	private Long createId;
	private Long shopId;
	/**
	 * 是否内置用户
	 */
	private Boolean isInternal = false;
	private List<QuickGoodsItemVo> items;

	private List<QuickGoodsItemVo> list;

	//是否 管理员操作
	private Boolean ifAdmin = false;
	private Long consignmentId; // 代销商户id

	/** true 大店  false 非大店*/
	private Boolean bigShop = false;

	public Boolean getBigShop() {
		return bigShop;
	}

	public void setBigShop(Boolean bigShop) {
		this.bigShop = bigShop;
	}

	public Long getConsignmentId() {
		return consignmentId;
	}

	public void setConsignmentId(Long consignmentId) {
		this.consignmentId = consignmentId;
	}

	public QuickGoodsVo() {
	}

	public static class QuickGoodsItemVo implements Serializable{
		/**
		 * 
		 */
		private static final long serialVersionUID = 3240628565586680984L;
		private Long commodityId;
		private BigDecimal quantity;
		private Integer logisticsModel;
		private Long warehouseId;
		private Long supplierId;

		private String storeCode;
		private String commodityCode;

		private String orderTime;
		private String deliveryBatch;

		private Boolean consignmentCommodity = false; // 是否代销门店的商品
		/** 档口编码 */
		private String stallCode;

		public String getStallCode() {
			return stallCode;
		}

		public void setStallCode(String stallCode) {
			this.stallCode = stallCode;
		}

		public Boolean getConsignmentCommodity() {
			return consignmentCommodity;
		}

		public void setConsignmentCommodity(Boolean consignmentCommodity) {
			this.consignmentCommodity = consignmentCommodity;
		}

		public String getOrderTime() {
			return orderTime;
		}

		public void setOrderTime(String orderTime) {
			this.orderTime = orderTime;
		}

		public String getDeliveryBatch() {
			return deliveryBatch;
		}

		public void setDeliveryBatch(String deliveryBatch) {
			this.deliveryBatch = deliveryBatch;
		}

		public String getStoreCode() {
			return storeCode;
		}

		public void setStoreCode(String storeCode) {
			this.storeCode = storeCode;
		}

		public String getCommodityCode() {
			return commodityCode;
		}

		public void setCommodityCode(String commodityCode) {
			this.commodityCode = commodityCode;
		}

		public BigDecimal getQuantity() {
			return quantity;
		}

		public void setQuantity(BigDecimal quantity) {
			this.quantity = quantity;
		}

		public Long getCommodityId() {
			return commodityId;
		}

		public void setCommodityId(Long commodityId) {
			this.commodityId = commodityId;
		}

		public Integer getLogisticsModel() {
			return logisticsModel;
		}

		public void setLogisticsModel(Integer logisticsModel) {
			this.logisticsModel = logisticsModel;
		}

		public Long getWarehouseId() {
			return warehouseId;
		}

		public void setWarehouseId(Long warehouseId) {
			this.warehouseId = warehouseId;
		}

		public Long getSupplierId() {
			return supplierId;
		}

		public void setSupplierId(Long supplierId) {
			this.supplierId = supplierId;
		}
	}
	public Long getStoreId() {
		return storeId;
	}
	public void setStoreId(Long storeId) {
		this.storeId = storeId;
	}
	public Long getEnterpriseId() {
		return enterpriseId;
	}
	public void setEnterpriseId(Long enterpriseId) {
		this.enterpriseId = enterpriseId;
	}
	public Long getCreateId() {
		return createId;
	}
	public void setCreateId(Long createId) {
		this.createId = createId;
	}
	public Boolean getIsInternal() {
		return isInternal;
	}
	public void setIsInternal(Boolean isInternal) {
		this.isInternal = isInternal;
	}
	public List<QuickGoodsItemVo> getItems() {
		return items;
	}
	public void setItems(List<QuickGoodsItemVo> items) {
		this.items = items;
	}

	public List<QuickGoodsItemVo> getList() {
		return list;
	}

	public void setList(List<QuickGoodsItemVo> list) {
		this.list = list;
	}

	public Boolean getIfAdmin() {
		if(null == ifAdmin){
			return false;
		}
		return ifAdmin;
	}

	public void setIfAdmin(Boolean ifAdmin) {
		this.ifAdmin = ifAdmin;
	}

	public Long getShopId() {
		return shopId;
	}

	public void setShopId(Long shopId) {
		this.shopId = shopId;
	}
}

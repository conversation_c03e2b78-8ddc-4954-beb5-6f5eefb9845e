package com.pinshang.qingyun.order.dto.consignment;

import com.pinshang.qingyun.base.page.Pagination;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @Author: sk
 * @Date: 2024/6/7
 */
@Data
public class ConsignmentOrderQueryIDTO extends Pagination {

    @ApiModelProperty("送货开始时间yyyy-MM-dd")
    private String beginDate;

    @ApiModelProperty("送货日期结束时间yyyy-MM-dd")
    private String endDate;

    @ApiModelProperty("门店ID")
    private Long shopId;

    private List<Long> shopIdList;

    @ApiModelProperty("部门code")
    private String orgCode;

    @ApiModelProperty("大类id")
    private Long cateId1;

    @ApiModelProperty("中类id")
    private Long cateId2;

    @ApiModelProperty("小类id")
    private Long cateId3;

    @ApiModelProperty("订单编码")
    private String orderCode;

    @ApiModelProperty("商品id")
    private Long commodityId;

    @ApiModelProperty("条码")
    private String barCode;

    @ApiModelProperty("代销商id")
    private Long consignmentId;

    private List<Long> commodityIdList;
}

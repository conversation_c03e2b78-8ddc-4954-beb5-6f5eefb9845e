package com.pinshang.qingyun.order.config;

import com.pinshang.qingyun.base.spring.SpringBeanFinder;
import com.pinshang.qingyun.order.constant.ThreadPoolBeanConstants;
import com.pinshang.qingyun.order.service.WeChatSendMessageService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Component;

import java.util.concurrent.ThreadPoolExecutor;

/**
 * @Author: sk
 * @Date: 2024/11/18
 */
@Slf4j
@Component
public class RejectedExecutionHandler implements java.util.concurrent.RejectedExecutionHandler{

    @Override
    public void rejectedExecution(Runnable r, ThreadPoolExecutor executor) {
        //System.out.println("线程池队列满了，开启新线程执行" + executor.getQueue().size());

        // 开启新线程执行
        new Thread(r).start();

        // 线程池队列满了，发送消息。调整核心参数
        WeChatSendMessageService weChatSendMessageService = (WeChatSendMessageService) SpringBeanFinder.getBean("weChatSendMessageService");
        weChatSendMessageService.sendWeChatMessage("order通用线程池队列满了,请设置核心参数");

    }
}

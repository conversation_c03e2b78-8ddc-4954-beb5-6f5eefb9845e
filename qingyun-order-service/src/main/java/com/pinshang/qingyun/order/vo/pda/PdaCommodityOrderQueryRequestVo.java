package com.pinshang.qingyun.order.vo.pda;

import com.pinshang.qingyun.base.configure.expand.QYAssert;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.util.Objects;

@Data
@ApiModel
public class PdaCommodityOrderQueryRequestVo {

    @ApiModelProperty("shopId")
    private Long shopId;

    @ApiModelProperty("商品id")
    private Long commodityId;

    @ApiModelProperty("送货日期 yyyy-MM-dd")
    private String orderTime;

    public void check(){
        QYAssert.isTrue(Objects.nonNull(shopId), "门店不能为空");
        QYAssert.isTrue(Objects.nonNull(commodityId), "商品不能为空");
        QYAssert.isTrue(StringUtils.isNotEmpty(orderTime), "送货日期不能为空");
    }

}

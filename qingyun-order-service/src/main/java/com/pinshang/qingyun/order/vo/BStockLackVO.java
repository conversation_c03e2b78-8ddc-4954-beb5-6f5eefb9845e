package com.pinshang.qingyun.order.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2024/03/12
 * @Version 1.0
 */
@Data
public class BStockLackVO {
    private Long storeId;

    @ApiModelProperty("产品id")
    private Long commodityId;

    @ApiModelProperty("库存依据  1=依据大仓, 2=不限量订货, 3=限量供应")
    private Integer stockType;

    @ApiModelProperty("库存数量")
    private BigDecimal stockQuantity;

    @ApiModelProperty("需要的数量")
    private BigDecimal needQuantity;

    @ApiModelProperty("需要的数量")
    private Integer orderType;

    @ApiModelProperty("备注")
    private String remark;

    @ApiModelProperty("操作人user_id")
    private Long createId;
}

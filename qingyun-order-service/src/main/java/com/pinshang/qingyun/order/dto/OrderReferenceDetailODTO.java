package com.pinshang.qingyun.order.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.List;

/**
 * @Author: sk
 * @Date: 2019/12/2
 */

@Data
@NoArgsConstructor
@AllArgsConstructor
public class OrderReferenceDetailODTO {

    private String commodityId;

    @ApiModelProperty("商品编码")
    private String commodityCode;

    @ApiModelProperty("商品名称")
    private String commodityName;

    @ApiModelProperty("是否促销")
    private Boolean isPromotionProduct;

    @ApiModelProperty("是否新品")
    private Boolean isNewProduct;

    @ApiModelProperty("单位")
    private String commodityUnit;

    @ApiModelProperty("包装规格")
    private BigDecimal commodityPackageSpec;

    @ApiModelProperty("销售箱装量")
    private BigDecimal salesBoxCapacity;

    @ApiModelProperty("当前库存")
    private BigDecimal stockQuantity;

    @ApiModelProperty("建议订货量")
    private BigDecimal adviseCommodityNum;

    @ApiModelProperty("进销损明细")
    private List<CommodityOrderSalesBreakageODTO> orderSaleBreakageList;

    public static class CommodityOrderSalesBreakageODTO{

        @ApiModelProperty("周")
        private String week;

        @ApiModelProperty("日")
        private String saleDate;

        @ApiModelProperty("进货数量")
        private BigDecimal orderQuantity;

        @ApiModelProperty("销售数量")
        private BigDecimal salesQuantity;

        @ApiModelProperty("损(过保/临保)")
        private BigDecimal normalBreakageQuantity;

        @ApiModelProperty("损(非过保/非临保)")
        private BigDecimal overNormalBreakageQuantity;

        public String getWeek() {
            return week;
        }

        public void setWeek(String week) {
            this.week = week;
        }

        public String getSaleDate() {
            return saleDate;
        }

        public void setSaleDate(String saleDate) {
            this.saleDate = saleDate;
        }

        public BigDecimal getOrderQuantity() {
            return orderQuantity;
        }

        public void setOrderQuantity(BigDecimal orderQuantity) {
            this.orderQuantity = orderQuantity;
        }

        public BigDecimal getSalesQuantity() {
            return salesQuantity;
        }

        public void setSalesQuantity(BigDecimal salesQuantity) {
            this.salesQuantity = salesQuantity;
        }

        public BigDecimal getNormalBreakageQuantity() {
            return normalBreakageQuantity;
        }

        public void setNormalBreakageQuantity(BigDecimal normalBreakageQuantity) {
            this.normalBreakageQuantity = normalBreakageQuantity;
        }

        public BigDecimal getOverNormalBreakageQuantity() {
            return overNormalBreakageQuantity;
        }

        public void setOverNormalBreakageQuantity(BigDecimal overNormalBreakageQuantity) {
            this.overNormalBreakageQuantity = overNormalBreakageQuantity;
        }
    }

}

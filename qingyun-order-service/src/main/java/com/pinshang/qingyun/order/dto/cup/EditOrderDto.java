package com.pinshang.qingyun.order.dto.cup;

import com.pinshang.qingyun.order.model.commodity.Commodity;
import com.pinshang.qingyun.order.model.order.Order;


import java.util.List;

public class EditOrderDto extends Order {

	private String storeIdStr;
	private Double collectPrice; // 余额

	private static final long serialVersionUID = 1L;

	private List<Commodity> commoditylist;// 订单商品

	private List<Commodity> giftCommoditylist;// 赠品商品

	private List<Commodity> rationCommoditylist; // 配货商品

	private boolean combTypeCommodityFilterFlag; //是否过滤掉组合商品

	public boolean isCombTypeCommodityFilterFlag() {
		return combTypeCommodityFilterFlag;
	}

	public void setCombTypeCommodityFilterFlag(boolean combTypeCommodityFilterFlag) {
		this.combTypeCommodityFilterFlag = combTypeCommodityFilterFlag;
	}

	public String getStoreIdStr() {
		return storeIdStr;
	}

	public void setStoreIdStr(String storeIdStr) {
		this.storeIdStr = storeIdStr;
	}

	public Double getCollectPrice() {
		return collectPrice;
	}

	public void setCollectPrice(Double collectPrice) {
		this.collectPrice = collectPrice;
	}

	public List<Commodity> getCommoditylist() {
		return commoditylist;
	}

	public void setCommoditylist(List<Commodity> commoditylist) {
		this.commoditylist = commoditylist;
	}

	public List<Commodity> getGiftCommoditylist() {
		return giftCommoditylist;
	}

	public void setGiftCommoditylist(List<Commodity> giftCommoditylist) {
		this.giftCommoditylist = giftCommoditylist;
	}

	public List<Commodity> getRationCommoditylist() {
		return rationCommoditylist;
	}

	public void setRationCommoditylist(List<Commodity> rationCommoditylist) {
		this.rationCommoditylist = rationCommoditylist;
	}

}

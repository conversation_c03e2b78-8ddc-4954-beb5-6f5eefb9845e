package com.pinshang.qingyun.order.util;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * @Author: sk
 * @Date: 2025/3/28
 */
public class NumberUtils {

    public static boolean isNumber(String str){
        // 长度最多15位，允许2位小数
        Pattern pattern = Pattern.compile("^(\\d{1,15}|\\d{1,15}\\.\\d{1,2})$");
        Matcher match = pattern.matcher(str);
        return match.matches();
    }

    /**
     * 是否整数
     * @param input
     * @return
     */
    public static boolean isInteger(String input) {
        Pattern pattern = Pattern.compile("^-?\\d{1,15}$");
        return pattern.matcher(input).matches();
    }
}

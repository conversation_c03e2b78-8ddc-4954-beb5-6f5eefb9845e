package com.pinshang.qingyun.order.enums;
/*
 * 预订单收获单状态
 * 状态: 0-待收货, 1-待审核, 2-审核不通过, 3-审核通过
 */
public enum PreOrderReceiveStatusEnums {

	UNRECEIVED("待收货", 0),
	UNCHECKED("待审核", 1),
	REJECT("审核不通过", 2),
	PASS("审核通过", 3);
    private String name;
    private int code;

    PreOrderReceiveStatusEnums(String name, int code) {
        this.name = name;
        this.code = code;
    }

    public static String getName(int code) {
        for (PreOrderReceiveStatusEnums es : PreOrderReceiveStatusEnums.values()) {
            if (code == es.getCode()) {
                return es.name;
            }
        }
        return null;
    }

    public String getName() {
        return name;
    }

    public int getCode() {
        return code;
    }
}

package com.pinshang.qingyun.order.enums.tda;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import com.pinshang.qingyun.order.enums.BaseEnum;

/**
 * <AUTHOR>
 * @Version 0.1.0
 * @Date 2020/12/30 16:07
 * @Copyright © 2019-2020 qm
 * @Description -- 类说明 退货来源：1-app客户申请，2-品鲜后台投诉，3-订单取消，4-订单配送失败
 **/
public enum TDaReturnSourceEnum implements BaseEnum {

    APP_RETURN(1, "app客户申请"),
    PX_RETURN(2, "品鲜后台投诉"),
    ORDER_CANCEL(3, "订单取消"),
    DELIVERY_FAILURE(4, "配送失败"),

    ;

    private int code;
    private String name;


    private TDaReturnSourceEnum(int code, String name) {
        this.code = code;
        this.name = name;
    }

    @JsonValue
    public int getCode() {
        return this.code;
    }

    public String getName() {
        return this.name;
    }


    @JsonCreator
    public static TDaReturnSourceEnum getByCode(Integer code) {
        if (code == null) {
            return null;
        } else {
            TDaReturnSourceEnum[] var1 = values();
            int var2 = var1.length;
            for (int var3 = 0; var3 < var2; ++var3) {
                TDaReturnSourceEnum value = var1[var3];
                if (value.getCode() == code) {
                    return value;
                }
            }
            return null;
        }
    }

    @Override
    public Integer convert() {
        return this.code;
    }
}

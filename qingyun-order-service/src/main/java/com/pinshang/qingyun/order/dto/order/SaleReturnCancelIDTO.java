package com.pinshang.qingyun.order.dto.order;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @ClassName SaleReturnCancelIDTO
 * <AUTHOR>
 * @Date 2022/10/12 10:25
 * @Description SaleReturnCancelIDTO
 * @Version 1.0
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class SaleReturnCancelIDTO {
    @ApiModelProperty("订单code")
    private String orderCode;

    @ApiModelProperty("用户id-后端赋值")
    private Long userId;

    @ApiModelProperty("取消理由")
    private String remark;
}

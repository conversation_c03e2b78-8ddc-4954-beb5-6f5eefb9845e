package com.pinshang.qingyun.order.vo.commodity;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class CreateHandleOrderRequestVO {

    private Boolean delAndSubmit=false;//是否删除并提交订单:true 是    false：否

    private Long shoppingCartId;//购物车主ID

    private String orderTime;//送货日期yyyy-MM-dd

    private String deliveryBatch;//配送批次

    private Long enterpriseId;
    private Long storeId;
    private Long userId;
    private String realName;
    private Boolean isInternal;

    public Long getShoppingCartId() {
        return shoppingCartId;
    }
    public void setShoppingCartId(Long shoppingCartId) {
        this.shoppingCartId = shoppingCartId;
    }
    public String getOrderTime() {
        return orderTime;
    }
    public void setOrderTime(String orderTime) {
        this.orderTime = orderTime;
    }
    public String getDeliveryBatch() {
        return deliveryBatch;
    }
    public void setDeliveryBatch(String deliveryBatch) {
        this.deliveryBatch = deliveryBatch;
    }
    public Boolean getDelAndSubmit() {
        return delAndSubmit;
    }
    public void setDelAndSubmit(Boolean delAndSubmit) {
        this.delAndSubmit = delAndSubmit;
    }

    public Long getEnterpriseId() {
        return enterpriseId;
    }

    public void setEnterpriseId(Long enterpriseId) {
        this.enterpriseId = enterpriseId;
    }

    public Long getStoreId() {
        return storeId;
    }

    public void setStoreId(Long storeId) {
        this.storeId = storeId;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public String getRealName() {
        return realName;
    }

    public void setRealName(String realName) {
        this.realName = realName;
    }

    public Boolean getInternal() {
        return isInternal;
    }

    public void setInternal(Boolean internal) {
        isInternal = internal;
    }
}

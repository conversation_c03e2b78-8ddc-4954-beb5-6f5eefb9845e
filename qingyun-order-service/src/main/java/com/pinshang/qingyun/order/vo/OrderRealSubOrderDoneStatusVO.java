package com.pinshang.qingyun.order.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2024/7/2 11:32
 */
@Data
public class OrderRealSubOrderDoneStatusVO {

    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date startTime;
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date endTime;
    /*** 多个订单id 逗号分隔 */
    private String orderIds;
}

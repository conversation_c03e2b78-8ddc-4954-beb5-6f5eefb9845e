package com.pinshang.qingyun.order.vo.order;

import com.pinshang.qingyun.base.page.Pagination;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;
import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class SubOrderSearchVo extends Pagination {
	/**
	 *
	 */
	private static final long serialVersionUID = -5920644605352450524L;
	private Long id;
	//订单编号
	private String orderCode;
	//仓库
	private Long warehouseId;

	private Long enterpriseId;

	private List<Long> orderIds;
	// 客户类型
	private Long storeTypeId;
	private Long userId;
	// 批次时间段start (已无用)
	private String startTime;
	// 批次时间段end　(已无用)
	private String endTime;
	/** 批次查询字段  **/
	private Integer deliveryBatch;
	//送货日期
	private Date orderTime;

	@ApiModelProperty("业务类型：0=B端销售，1=调拨，4=线上销售，10=通达销售")
	private Integer businessType;

	public SubOrderSearchVo(Long warehouseId,List<Long> orderIds){
		this.warehouseId = warehouseId;
		this.orderIds = orderIds;
	}
}

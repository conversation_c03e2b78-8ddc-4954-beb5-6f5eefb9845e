package com.pinshang.qingyun.order.enums;

public enum  ProductTypeEnums {
    PRODUCT(1),  //订单商品
    GIFT(2),  //赠品
    RATION(3), //配货商品
    ratio(4), //配比商品
    TH(5);//特惠商品
    private Integer code;

    public Integer getCode() {
        return code;
    }

    ProductTypeEnums(Integer code) {
        this.code = code;
    }

    public static ProductTypeEnums getEnumByCode(Integer code){
        if(code == null){
            return null;
        }
        for(ProductTypeEnums e : ProductTypeEnums.values()){
            if(e.getCode().intValue() == code){
                return e;
            }
        }
        return null;
    }
}

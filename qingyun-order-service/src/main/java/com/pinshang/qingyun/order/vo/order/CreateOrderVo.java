package com.pinshang.qingyun.order.vo.order;

import io.swagger.annotations.ApiModelProperty;

import java.math.BigDecimal;
import java.util.List;

/*
 * 下单DTO
 */
public class CreateOrderVo {
	private Long storeId;
	private Long enterpriseId;

	@ApiModelProperty("购物车主ID")
	private Long shoppingCartId;
	private List<CreateOrderItemIDTO> items;
	private Long createId;
	private String createName;
	/**
	 * 是否内置用户
	 */
	private Boolean isInternal;

	@ApiModelProperty("配送批次")
	private String orderTime;

	@ApiModelProperty("送货日期")
	private String deliveryBatch;

	/** true 大店  false 非大店*/
	private Boolean bigShop = false;

	@ApiModelProperty("是否强制提交：0-否、1-是")
	private Integer forceStatus = 0;

	public Boolean getBigShop() {
		return bigShop;
	}

	public void setBigShop(Boolean bigShop) {
		this.bigShop = bigShop;
	}
	public List<CreateOrderItemIDTO> getItems() {
		return items;
	}

	public void setItems(List<CreateOrderItemIDTO> items) {
		this.items = items;
	}

	public Long getStoreId() {
		return storeId;
	}

	public void setStoreId(Long storeId) {
		this.storeId = storeId;
	}

	public Long getEnterpriseId() {
		return enterpriseId;
	}

	public void setEnterpriseId(Long enterpriseId) {
		this.enterpriseId = enterpriseId;
	}

	public Long getShoppingCartId() {
		return shoppingCartId;
	}

	public void setShoppingCartId(Long shoppingCartId) {
		this.shoppingCartId = shoppingCartId;
	}

	public static class CreateOrderItemIDTO {

		@ApiModelProperty("购物车明细ID")
		private Long shoppingCartItemId;

		@ApiModelProperty("数量")
		private BigDecimal quantity;

		@ApiModelProperty("商品ID")
		private Long commodityId;

		private String commodityName;//商品名称
		
		public String getCommodityName() {
			return commodityName;
		}
		public void setCommodityName(String commodityName) {
			this.commodityName = commodityName;
		}
		public Long getShoppingCartItemId() {
			return shoppingCartItemId;
		}
		public void setShoppingCartItemId(Long shoppingCartItemId) {
			this.shoppingCartItemId = shoppingCartItemId;
		}
		public BigDecimal getQuantity() {
			return quantity;
		}
		public void setQuantity(BigDecimal quantity) {
			this.quantity = quantity;
		}
		public Long getCommodityId() {
			return commodityId;
		}
		public void setCommodityId(Long commodityId) {
			this.commodityId = commodityId;
		}
	}

	public Long getCreateId() {
		return createId;
	}

	public void setCreateId(Long createId) {
		this.createId = createId;
	}

	public String getCreateName() {
		return createName;
	}

	public void setCreateName(String createName) {
		this.createName = createName;
	}

	public Boolean getInternal() {
		return isInternal;
	}

	public void setInternal(Boolean internal) {
		isInternal = internal;
	}

	public String getOrderTime() {
		return orderTime;
	}

	public void setOrderTime(String orderTime) {
		this.orderTime = orderTime;
	}

	public String getDeliveryBatch() {
		return deliveryBatch;
	}

	public void setDeliveryBatch(String deliveryBatch) {
		this.deliveryBatch = deliveryBatch;
	}

	public Integer getForceStatus() {
		return forceStatus;
	}

	public void setForceStatus(Integer forceStatus) {
		this.forceStatus = forceStatus;
	}
}

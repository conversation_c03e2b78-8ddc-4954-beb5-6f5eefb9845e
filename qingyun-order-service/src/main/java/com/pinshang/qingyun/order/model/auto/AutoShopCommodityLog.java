package com.pinshang.qingyun.order.model.auto;

import com.pinshang.qingyun.base.po.BaseIDPO;
import lombok.Data;

import javax.persistence.Entity;
import javax.persistence.Table;
import java.util.Date;

/**
 * <AUTHOR>
 */
@Entity
@Data
@Table(name="t_log_auto_shop_commodity")
public class AutoShopCommodityLog extends BaseIDPO {

    private Long shopId;

    private String shopCode;

    private String shopName;

    private Long commodityId;

    private String commodityCode;

    private String commodityName;

    /**
     * 类型:1=启用自动订货 2=停用自动订货 3=导入添加商品 4=导入修改安全库存 5=删除商品
     */
    private Integer type;

    private String createName;

    private Date createTime;

    private Long createId;
}

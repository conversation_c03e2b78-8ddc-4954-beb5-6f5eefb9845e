package com.pinshang.qingyun.order.vo.settle;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class OrderReqVo {


    private List<Long> ids;

    private List<Integer> notOrderTypes;

    private List<Integer> orderTypes;

//    /**订单编号集合**/
//    private List<Long> subOrderIdList;

    private List<Long> storeIds;

    private Long settleId;

    /**送货日期-开始**/
    private String deliveryTimeStart;
    private String deliveryTimeStartHms;

    /**送货日期-结束**/
    private String deliveryTimeEnd;
    private String deliveryTimeEndHms;

    public String getDeliveryTimeStartHms() {
        return deliveryTimeStart+" 0:00:00";
    }

    public String getDeliveryTimeEndHms() {
        return deliveryTimeEnd+" 23:59:59";
    }
}

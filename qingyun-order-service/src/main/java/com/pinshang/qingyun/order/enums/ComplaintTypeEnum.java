package com.pinshang.qingyun.order.enums;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;

/**
 * <AUTHOR>
 * @Version 0.1.0
 * @Date 2020/12/30 16:07
 * @Copyright © 2019-2020 qm
 * @Description -- 类说明 投诉类型 0-普通投诉，1-退货投诉，2-门店投诉
 **/
public enum ComplaintTypeEnum implements BaseEnum {

    COMMON(0, "普通投诉-差异投诉"),
    RETURN(1, "退货投诉"),
    SHOP(2, "门店投诉"),

    ;

    private int code;
    private String name;


    private ComplaintTypeEnum(int code, String name) {
        this.code = code;
        this.name = name;
    }

    @JsonValue
    public int getCode() {
        return this.code;
    }

    public String getName() {
        return this.name;
    }


    @JsonCreator
    public static ComplaintTypeEnum getByCode(Integer code) {
        if (code == null) {
            return null;
        } else {
            ComplaintTypeEnum[] var1 = values();
            int var2 = var1.length;
            for (int var3 = 0; var3 < var2; ++var3) {
                ComplaintTypeEnum value = var1[var3];
                if (value.getCode() == code) {
                    return value;
                }
            }
            return null;
        }
    }

    @Override
    public Integer convert() {
        return this.code;
    }
}

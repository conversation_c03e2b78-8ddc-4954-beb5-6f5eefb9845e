package com.pinshang.qingyun.order.dto.shopcart.v3;

import com.pinshang.qingyun.base.enums.YesOrNoEnums;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

@Data
public class ShoppingCartV3IDTO {
    @ApiModelProperty(value = "商品类型 默认1、普通商品 2、特惠商品")
    private Integer commodityType;

    @ApiModelProperty(value = "商品id")
    private Long commodityId;

    @ApiModelProperty(hidden = true)
    private Long storeId;

    @ApiModelProperty("送货日期")
    private Date orderTime;

    @ApiModelProperty("是否是购物车页面请求")
    private YesOrNoEnums shopCartPage;

    public static ShoppingCartAddV3IDTO builder(Long storeId, Date orderTime, YesOrNoEnums shopCartPage){
        ShoppingCartAddV3IDTO shoppingCartAddV3IDTO = new ShoppingCartAddV3IDTO();
        shoppingCartAddV3IDTO.setStoreId(storeId);
        shoppingCartAddV3IDTO.setOrderTime(orderTime);
        shoppingCartAddV3IDTO.setShopCartPage(shopCartPage);
        return shoppingCartAddV3IDTO;
    }
}

package com.pinshang.qingyun.order.dto;

import com.pinshang.qingyun.base.enums.YesOrNoEnums;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * @Author: liuZhen
 * @DateTime: 2022/5/13 16:11
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class AutoPreOrderItemODTO {

    private Long commodityId;
    @ApiModelProperty("商品编码")
    private String commodityCode;
    @ApiModelProperty("条形码")
    private String barCode;
    @ApiModelProperty("商品名称")
    private String commodityName;
    @ApiModelProperty("规格")
    private String commoditySpec;
    @ApiModelProperty("是否速冻")
    private Integer isFreeze;
    @ApiModelProperty("是否速冻")
    private String isFreezeStr;
    @ApiModelProperty("供价")
    private BigDecimal supplyPrice;
    @ApiModelProperty("份数")
    private Integer stockNumber;
    @ApiModelProperty("数量")
    private BigDecimal stockQuantity;
    @ApiModelProperty("单位")
    private String commodityUnit;
    @ApiModelProperty("小计")
    private BigDecimal totalPrice;

    public void setIsFreeze(Integer isFreeze) {
        this.isFreeze = isFreeze;
        this.isFreezeStr= YesOrNoEnums.getByCode(isFreeze).getName();
    }
}

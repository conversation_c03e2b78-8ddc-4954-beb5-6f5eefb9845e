package com.pinshang.qingyun.order.dto;

import com.pinshang.qingyun.base.enums.YesOrNoEnums;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * @Author: sk
 * @Date: 2022/5/11
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class AutoShopCommodityODTO {

    private Long shopId;
    private Long storeId;

    private Long commodityId;
    /** 安全库存 */
    private BigDecimal safeQuantity;

    /** 是否速冻 0-否  1-是 */
    private Integer commodityIsQuickFreeze; // 是否速冻
    private BigDecimal salesBoxCapacity; // 销售箱规
    private BigDecimal commodityPackageSpec; // 包装规格
    private BigDecimal price;
    private String commodityCode;
    private String commodityName;

    /** 是否称重 0-不称量 1-称重 */
    private Integer isWeight;


    /** 是否称重并且冻品 */
    public Boolean isWeightFreeze(){
        if(commodityIsQuickFreeze != null && commodityIsQuickFreeze.equals(YesOrNoEnums.YES.getCode())
                && isWeight != null && isWeight.equals(YesOrNoEnums.YES.getCode())){
            return Boolean.TRUE;
        }
        return Boolean.FALSE;
    }
}

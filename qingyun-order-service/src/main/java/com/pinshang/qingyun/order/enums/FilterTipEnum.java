package com.pinshang.qingyun.order.enums;


public enum FilterTipEnum {
    REMOVE(1, "不能订货，已被剔除"),
    SETTING_CHANGE(2, "配置信息变更，已重新添加到购物车");

    private Integer type;
    private String msg;

    public Integer getType() {
        return type;
    }

    public String getMsg() {
        return msg;
    }

    FilterTipEnum(Integer type, String msg) {
        this.type = type;
        this.msg = msg;
    }
}

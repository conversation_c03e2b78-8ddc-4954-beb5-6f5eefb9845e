package com.pinshang.qingyun.order.vo.order;

import java.io.Serializable;
import java.math.BigDecimal;

public class OrderItemDto implements Serializable{
	
	private static final long serialVersionUID = 1L;
		private String remark;
	    private String productId;
		private BigDecimal originalPrice;
	    private BigDecimal price;
	    private BigDecimal productNum;
	    private String productName;
	    private BigDecimal productNumLimit;
	    private Integer type;
	    private Integer status = 0 ;
	    private Integer commodityStatus;
	    private Integer commodityPurchaseStatus;
	    private Integer logisticsModel;//物流模式(物流配送模式0=直送，1＝配送，2＝直通)
		private String commodityCode;
		//是否可变价——特价不可变价，其余从 订货设置 取
		private Integer changePriceStatus;
		private Long giftModelId;
		private Long promotionId;

		private String productCode;

		private Integer presaleStatus; // 是否预售  0 否   1 是

		private Integer stockType; // 库存依据  1=依据大仓, 2=不限量订货, 3=限量供应

		/**
		 * 原材料比例
		 */
		private Integer sourceRatio;
		/**
		 * 转成品比例
		 */
		private Integer targetRatio;

		/**
		 * 组合商品转换的最小单位商品ID
		 */
		private Long targetCommodityId;

		/**
		 * 组合商品转换状态：0=无转换，1=有转换
		 */
		private Integer convertStatus;

		/**
		 * 组合商品目标数量 : 向上取整 ( quantity / source_ratio * target_ratio)
		 */
		private BigDecimal targetQuantity;

	public BigDecimal getOriginalPrice() {
		return originalPrice;
	}

	public void setOriginalPrice(BigDecimal originalPrice) {
		this.originalPrice = originalPrice;
	}

	public Integer getSourceRatio() {
		return sourceRatio;
	}

	public void setSourceRatio(Integer sourceRatio) {
		this.sourceRatio = sourceRatio;
	}

	public Integer getTargetRatio() {
		return targetRatio;
	}

	public void setTargetRatio(Integer targetRatio) {
		this.targetRatio = targetRatio;
	}

	public Long getTargetCommodityId() {
		return targetCommodityId;
	}

	public void setTargetCommodityId(Long targetCommodityId) {
		this.targetCommodityId = targetCommodityId;
	}

	public Integer getConvertStatus() {
		return convertStatus;
	}

	public void setConvertStatus(Integer convertStatus) {
		this.convertStatus = convertStatus;
	}

	public BigDecimal getTargetQuantity() {
		return targetQuantity;
	}

	public void setTargetQuantity(BigDecimal targetQuantity) {
		this.targetQuantity = targetQuantity;
	}

	public Integer getPresaleStatus() {
		return presaleStatus;
	}

	public void setPresaleStatus(Integer presaleStatus) {
		this.presaleStatus = presaleStatus;
	}

	public Integer getStockType() {
		return stockType;
	}

	public void setStockType(Integer stockType) {
		this.stockType = stockType;
	}

	@SuppressWarnings("unused")
		private OrderItemDto(){}
 
	    public OrderItemDto(String productId, BigDecimal productNum){
	    	this.productId =productId;
	    	this.productNum =productNum;
	    }
	    public OrderItemDto(String remark, String productId, BigDecimal price,
				BigDecimal productNum, Integer type, int status,BigDecimal productNumLimit) {
			super();
			this.remark = remark;
			this.productId = productId;
			this.price = price;
			this.productNum = productNum;
			this.type = type;
			this.status = status;
			this.productNumLimit = productNumLimit;
		}
	    
	    public OrderItemDto(String remark, String productId, BigDecimal price,
				BigDecimal productNum, Integer type, int status,BigDecimal productNumLimit,String productName) {
			super();
			this.remark = remark;
			this.productId = productId;
			this.price = price;
			this.productNum = productNum;
			this.type = type;
			this.status = status;
			this.productNumLimit = productNumLimit;
			this.productName = productName;
		}

		public OrderItemDto(String remark, String productId, BigDecimal price, BigDecimal productNum, Integer type,
							int status,BigDecimal productNumLimit,String productName, String commodityCode) {
			super();
			this.remark = remark;
			this.productId = productId;
			this.price = price;
			this.productNum = productNum;
			this.type = type;
			this.status = status;
			this.productNumLimit = productNumLimit;
			this.productName = productName;
			this.commodityCode = commodityCode;
		}

		public OrderItemDto(String productId, BigDecimal price, BigDecimal productNum, String remark, Integer type) {
			this.remark = remark;
			this.productId = productId;
			this.price = price;
			this.productNum = productNum;
			this.type = type;
		}

	public OrderItemDto(String productId, String productCode, String productName, BigDecimal price, BigDecimal productNum, String remark, Integer type) {
		this.remark = remark;
		this.productId = productId;
		this.price = price;
		this.productNum = productNum;
		this.type = type;
		this.productCode = productCode;
		this.productName = productName;
	}

		public OrderItemDto(String productId,BigDecimal originalPrice, BigDecimal price, BigDecimal productNum, String remark, Integer type, Long giftModelId, Long promotionId){
	        this.remark = remark;
	        this.productId = productId;
	        this.price = price;
	        this.productNum = productNum;
	        this.type = type;
	        this.giftModelId = giftModelId;
	        this.promotionId = promotionId;
			this.originalPrice = originalPrice;
	    }

		public OrderItemDto(String productId, String productName, BigDecimal price, BigDecimal productNum, String remark, Integer type){
			this.remark = remark;
			this.productId = productId;
			this.productName = productName;
			this.price = price;
			this.productNum = productNum;
			this.type = type;
		}
		
	    public String getRemark()
	    {
	        return remark;
	    }

	    public String getProductId()
	    {
	        return productId;
	    }

	    public BigDecimal getPrice()
	    {
	        return price;
	    }

	    public BigDecimal getProductNum()
	    {
	        return productNum;
	    }

	    public void setProductId(String productId) {
	        this.productId = productId;
	    }

	    public BigDecimal amount(){
	    	if(null == this.price){
	    		this.price = BigDecimal.ZERO;
	    	}
	        return price.multiply(productNum).setScale(2,BigDecimal.ROUND_HALF_UP);
	    }

	public BigDecimal originalAmount(){ // 原始金额
		if(null == this.originalPrice){
			this.originalPrice = BigDecimal.ZERO;
		}
		return originalPrice.multiply(productNum).setScale(2,BigDecimal.ROUND_HALF_UP);
	}

	public BigDecimal promotionAmount(){ // 促销金额
		BigDecimal promotionAmount = originalAmount().subtract(amount());
		return promotionAmount.compareTo(BigDecimal.ZERO) > 0 ? promotionAmount : BigDecimal.ZERO;
	}

	    public  void updatePrice(BigDecimal price){
	    	if(null != price && price.compareTo(BigDecimal.ZERO) > 0){
	    		this.price = price;
	    	}
	    } 
	    
	    
	    public void updateProductLimit(BigDecimal productLimit){
	    	if(null != productLimit && productLimit.compareTo(BigDecimal.ZERO)> 0){
	    		this.productNumLimit = productLimit;
	    	}
	    }

		public Integer getType() {
			if(this.type == null || this.type == 0){
				this.type = 1;
			}
			return type;
		}

		public void setType(Integer type) {
			this.type = type;
		}

		public void setProductNum(BigDecimal productNum) {
			this.productNum = productNum;
		}
		

		public Integer getStatus() {
			return status;
		}

		public void setStatus(Integer status) {
			this.status = status;
		}

	public Integer getCommodityStatus() {
		return commodityStatus;
	}

	public void setCommodityStatus(Integer commodityStatus) {
		this.commodityStatus = commodityStatus;
	}

	public Integer getCommodityPurchaseStatus() {
			return commodityPurchaseStatus;
		}

		public void setCommodityPurchaseStatus(Integer commodityPurchaseStatus) {
			this.commodityPurchaseStatus = commodityPurchaseStatus;
		}

	public BigDecimal getProductNumLimit() {
			return productNumLimit;
		}

		public String getProductName() {
			return productName;
		}

		public void setProductName(String productName) {
			this.productName = productName;
		}

		public Integer getLogisticsModel() {
			return logisticsModel;
		}

		public void setLogisticsModel(Integer logisticsModel) {
			this.logisticsModel = logisticsModel;
		}

		@Override
		public String toString() {
			return "OrderItemDto [remark=" + remark + ", productId="
					+ productId + ", price=" + price + ", productNum="
					+ productNum + ", productName=" + productName
					+ ", productNumLimit=" + productNumLimit + ", type=" + type
					+ ", status=" + status + ", logisticsModel="
					+ logisticsModel + "]";
		}

		public void setRemark(String remark) {
			this.remark = remark;
		}

		public void setPrice(BigDecimal price) {
			this.price = price;
		}

		public void setProductNumLimit(BigDecimal productNumLimit) {
			this.productNumLimit = productNumLimit;
		}

	public String getCommodityCode() {
		return commodityCode;
	}

	public void setCommodityCode(String commodityCode) {
		this.commodityCode = commodityCode;
	}

	public Integer getChangePriceStatus() {
		return changePriceStatus;
	}

	public void setChangePriceStatus(Integer changePriceStatus) {
		this.changePriceStatus = changePriceStatus;
	}

	public Long getGiftModelId() {
		return giftModelId;
	}

	public void setGiftModelId(Long giftModelId) {
		this.giftModelId = giftModelId;
	}

	public Long getPromotionId() {
		return promotionId;
	}

	public void setPromotionId(Long promotionId) {
		this.promotionId = promotionId;
	}

	public String getProductCode() {
		return productCode;
	}

	public void setProductCode(String productCode) {
		this.productCode = productCode;
	}


}

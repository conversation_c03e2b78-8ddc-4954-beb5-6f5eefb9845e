package com.pinshang.qingyun.order.model.payType;

import lombok.Data;

import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;

/**
 * Created by Ruansi on 2019/08/15.
 */
@Data
@Table(name = "t_xda_paytype")
public class PayType {
    @Id
    private Long id;
    // 支付类型编号
    private String code;
    // 支付类型名称
    private String name;
    // 启用状态，0=停用，1=启用
    private Integer status;
    // 添加人
    private Long createId;
    // 修改人
    private Long updateId;
    // 添加时间
    private Date createTime;
    // 修改时间
    private Date updateTime;
}

package com.pinshang.qingyun.order.dto.tms;

import io.swagger.annotations.ApiModelProperty;

import java.math.BigDecimal;

import lombok.Data;
import lombok.NoArgsConstructor;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;

/**
 * 【源单明细信息-用于运单】
 */
@Data
@NoArgsConstructor
public class RefOrderItemForWaybillODTO {
	@JsonSerialize(using = ToStringSerializer.class)
	@ApiModelProperty(position = 1, required = true, value = "源单ID")
	private Long refOrderId;
	
	@JsonSerialize(using = ToStringSerializer.class)
	@ApiModelProperty(position = 11, required = true, value = "商品ID")
	private Long commodityId;
	@ApiModelProperty(position = 12, required = true, value = "是否称重：0-否、1-是")
	private Integer isWeight;
	@ApiModelProperty(position = 13, required = true, value = "包装规格")
	private BigDecimal commodityPackageSpec;
	
	@ApiModelProperty(position = 21, required = true, value = "数量")
	private BigDecimal quantity;
	@ApiModelProperty(position = 22, required = true, value = "份数")
	private Integer number;
	@ApiModelProperty(position = 23, required = true, value = "订货金额/退货金额")
	private BigDecimal amount;
}

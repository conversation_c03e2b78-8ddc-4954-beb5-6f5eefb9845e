package com.pinshang.qingyun.order.enums;

public enum ReceiveOrderStatusEnums {
	
	UNRECEIVED("待收货", 0),
	UNCHECKED("待审核", 1), 
	REJECT("审核未通过", 2), 
	PASS("审核通过", 3),
	Cancel("已取消",4),
	;
    private String name;
    private int code;

    ReceiveOrderStatusEnums(String name, int code) {
        this.name = name;
        this.code = code;
    }

    public static String getName(int code) {
        for (ReceiveOrderStatusEnums es : ReceiveOrderStatusEnums.values()) {
            if (code == es.getCode()) {
                return es.name;
            }
        }
        return null;
    }

    public String getName() {
        return name;
    }

    public int getCode() {
        return code;
    }
}

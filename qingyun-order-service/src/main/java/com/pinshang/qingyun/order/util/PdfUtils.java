package com.pinshang.qingyun.order.util;

import com.itextpdf.text.DocumentException;
import com.itextpdf.text.Font;
import com.itextpdf.text.pdf.BaseFont;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.core.io.ClassPathResource;

import java.io.IOException;

public class PdfUtils {

    static Logger logger = LoggerFactory.getLogger(PdfUtils.class);

    public static BaseFont chineseBaseFont() {
        ClassPathResource pathResource = new ClassPathResource("simhei.ttf");
        String absolutePath = pathResource.getFilename();
        //FontFactory.register(absolutePath);
        try {
            return BaseFont.createFont(absolutePath, BaseFont.IDENTITY_H, BaseFont.EMBEDDED);
        } catch (DocumentException e) {
            e.printStackTrace();
        } catch (IOException e) {
            e.printStackTrace();
        }
        return null;
    }

    /**
     * 获取中文字体
     *
     * @param fontSize 指定中文字体大小
     * @return 返回中文字体
     * @throws IOException
     * @throws DocumentException
     */
    public static Font chineseFont(float fontSize) throws IOException, DocumentException {
        ClassPathResource pathResource = new ClassPathResource("simhei.ttf");
        String absolutePath = pathResource.getFilename();
        //FontFactory.register(absolutePath);
        BaseFont bf = BaseFont.createFont(absolutePath, BaseFont.IDENTITY_H, BaseFont.EMBEDDED);
        return new Font(bf, fontSize, Font.NORMAL);// 中文字体
    }

    public static Font chineseSTFont(float fontSize) throws IOException, DocumentException {
        ClassPathResource pathResource = new ClassPathResource("simhei.ttf");
        String absolutePath = pathResource.getFilename();
        //FontFactory.register(absolutePath);
        BaseFont bf = BaseFont.createFont(absolutePath, BaseFont.IDENTITY_H, BaseFont.EMBEDDED);
        return new Font(bf, fontSize, Font.NORMAL);// 中文字体
    }

    public static Font chineseSTFont(float fontSize, int fontStyle) throws IOException, DocumentException {
        ClassPathResource pathResource = new ClassPathResource("simhei.ttf");
        String absolutePath = pathResource.getFilename();
        //FontFactory.register(absolutePath);
        BaseFont bf = BaseFont.createFont(absolutePath, BaseFont.IDENTITY_H, BaseFont.EMBEDDED);
        return new Font(bf, fontSize, fontStyle);// 中文字体
    }

    public static Font chineseSTFANGSOFont(float fontSize, int fontStyle) throws IOException, DocumentException {
        ClassPathResource pathResource = new ClassPathResource("simhei.ttf");
        String absolutePath = pathResource.getFilename();
        BaseFont bf = BaseFont.createFont(absolutePath, BaseFont.IDENTITY_H, BaseFont.EMBEDDED);
        return new Font(bf, fontSize, fontStyle);// 中文字体
    }
}

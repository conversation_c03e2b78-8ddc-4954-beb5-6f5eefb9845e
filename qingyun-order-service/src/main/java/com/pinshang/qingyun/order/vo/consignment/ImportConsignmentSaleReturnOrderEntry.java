package com.pinshang.qingyun.order.vo.consignment;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Collection;
import java.util.List;

@Data
public class ImportConsignmentSaleReturnOrderEntry<T> {

    @ApiModelProperty(value = "true成功、false失败时")
    private Boolean success;
    @ApiModelProperty(value = "错误信息List集合对象String")
    private Collection<String> errMsgList;

    private List<T> data;

    public ImportConsignmentSaleReturnOrderEntry(Boolean success,List<T> data, Collection<String> errMsgList) {
        this.success = success;
        this.errMsgList = errMsgList;
        this.data=data;
    }

    public static ImportConsignmentSaleReturnOrderEntry success(){
        return new ImportConsignmentSaleReturnOrderEntry(true,null,null);
    }

    public static <T> ImportConsignmentSaleReturnOrderEntry success(List<T> data){
        return new ImportConsignmentSaleReturnOrderEntry(true,data,null);
    }

    public static ImportConsignmentSaleReturnOrderEntry fail(Collection<String> errMsgList){
        return new ImportConsignmentSaleReturnOrderEntry(false,null,errMsgList);
    }
}

package com.pinshang.qingyun.order.pdf;

import com.itextpdf.text.*;
import com.itextpdf.text.pdf.BaseFont;
import com.itextpdf.text.pdf.PdfPCell;
import com.itextpdf.text.pdf.PdfPTable;
import com.itextpdf.text.pdf.PdfWriter;
import com.pinshang.qingyun.box.utils.TimeUtil;
import com.pinshang.qingyun.order.config.FileConfigProperties;
import com.pinshang.qingyun.order.dto.xda.XdaOrderInfoODTO;
import com.pinshang.qingyun.order.dto.xda.XdaOrderItemInfoODTO;
import com.pinshang.qingyun.order.util.PdfUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

@Component
public class OrderPdfA4Creator {
	private static Logger logger = LoggerFactory.getLogger(OrderPdfA4Creator.class);
	
	public static final int DEFAULT_PAGE_SIZE = 46;
	private static final float WIDTH_PERCENTAGE = 90f;
//	private static SimpleDateFormat FORMAT_SIMPLE_DATE = new SimpleDateFormat("yyyy年MM月dd日");
	private static SimpleDateFormat FORMAT_SIMPLE_DATE = new SimpleDateFormat("yyyyMMdd");
	
	public static String PDF_LOCAL_ROOT_DIR;
	
	@Autowired
	private FileConfigProperties fileConfigProperties;
	
	@PostConstruct
	public void init() {
		PDF_LOCAL_ROOT_DIR = fileConfigProperties.getPdfLocalRootDir();
	}

	public static void main(String[] args) throws IOException {
		XdaOrderInfoODTO orderInfo = new XdaOrderInfoODTO();
		orderInfo.setCompanyName("上海清美绿色食品（集团）有限公司");
		// 页头信息
		orderInfo.setStoreIsTestReport(0);
		orderInfo.setStoreCode("7775527");
		orderInfo.setOrderCode("1625623095581105584");
		orderInfo.setStoreName("曲阳专柜（家乐福）");
		orderInfo.setOrderTime(new Date());
		orderInfo.setStoreDeliveryAddress("上海市浦东新区秋月路26号");
		orderInfo.setStoreLinkmanMobile("021-12345678");
		
		// 页脚信息
		orderInfo.setStoreLineGroupName("申城组");
		orderInfo.setRegionalManagerName("黎藜");
		orderInfo.setRegionalManagerPhone("15011111111");
		orderInfo.setCreateName("宁波市鄞州瀚日商贸有限公司（新江厦姜山店）");
		orderInfo.setSupervisionName("章伟达");
		orderInfo.setSupervisionPhone("15022222222");
		orderInfo.setGenerateTime(TimeUtil.toString(new Date(), null));
		orderInfo.setDeliveryManName("张士锋");
		orderInfo.setDeliveryManPhone("15033333333");
		orderInfo.setOrderRemark("请及时准确送货，谢谢！");
		
		// 汇总信息
		orderInfo.setOrderAmount(getTestAmount(1000000));
		orderInfo.setRealAmount(getTestAmount(1000000));
		
		// 数据信息
		List<XdaOrderItemInfoODTO> orderItemList = new ArrayList<>();
		for (int i = 1; i <= 47; i ++) {
			XdaOrderItemInfoODTO orderItem = new XdaOrderItemInfoODTO();
			orderItem.setCommodityName("清美内脂豆腐");
			orderItem.setCommoditySpec("400克/盒");
			orderItem.setCommodityPrice(getTestAmount(1000));
			orderItem.setCommodityQuantity(getTestAmount(10000));
			orderItem.setCommodityAmount(getTestAmount(100000));
			orderItem.setRealCommodityQuantity(getTestAmount(10000));
			orderItem.setRealCommodityAmount(getTestAmount(100000));
			orderItem.setCommodityPackageKind(0 != i % 3? "01": "02"); // 01-生产日期当天、02-见包装
			orderItem.setRemark("备注" + i);
			orderItemList.add(orderItem);
		}
		orderInfo.setOrderItemList(orderItemList);
		
		createPdfFile(orderInfo, DEFAULT_PAGE_SIZE);
	}
	private static BigDecimal getTestAmount(int upperLimit) {
		return new BigDecimal(Math.random() * upperLimit).setScale(2, BigDecimal.ROUND_HALF_UP);
	}
	
	/**
	 * 生成  PDF文件
	 * 
	 * @param orderInfo
	 * @param pageSize
	 * @return
	 */
	public static String createPdfFile(XdaOrderInfoODTO orderInfo, int pageSize) {
		long startTime = System.currentTimeMillis();
		List<XdaOrderItemInfoODTO> orderPrintItems = orderInfo.getOrderItemList();
		int totalQuantity = orderPrintItems.size();
		int pageQuantity = totalQuantity / pageSize;
		int remainder = totalQuantity % pageSize;
		if (remainder != 0) {
			pageQuantity ++;
		}
		
		PDF_LOCAL_ROOT_DIR = getPdfRootDir();
		String fullDir = PDF_LOCAL_ROOT_DIR + buildDir(orderInfo.getOrderTime());
		File file = new File(fullDir);
		if(!file.isDirectory()) {
			file.mkdirs();
		}
		String fullFileName = fullDir + "/" + buildFileName(orderInfo.getStoreCode(), orderInfo.getOrderTime(), orderInfo.getOrderCode());
		Document document = new Document(PageSize.A4);
		try {
			PdfWriter.getInstance(document, new FileOutputStream(fullFileName));
			document.open();
			for (int currentPageNo = 0; currentPageNo < pageQuantity; currentPageNo ++) {
				if(currentPageNo != 0){
					document.newPage();
				}
				boolean isViewTotal = false;
				int fromIndex = currentPageNo * pageSize;
				int toIndex = (currentPageNo + 1) * pageSize;
				if (toIndex >= totalQuantity) {
					toIndex = totalQuantity;
					isViewTotal = true;
				}
				createPdf(document, orderInfo, orderPrintItems.subList(fromIndex, toIndex), fromIndex, pageQuantity, currentPageNo + 1, isViewTotal);
			}
		} catch (Exception e) {
			logger.error("订单PDF（A4）生成器-生成文件异常:", e);
		} finally {
			try {
				document.close();
			} catch (Exception e) {
				logger.error("订单PDF（A4）生成器-document.close异常:", e);
			}
		}
		String msg = "订单PDF（A4）生成器-生成文件完成：订单号=" + orderInfo.getOrderCode() + "，路径=" + fullFileName + "，总耗时=" + (System.currentTimeMillis() - startTime) + "毫秒!";
		logger.debug(msg);
		System.out.println("msg=" + msg);
		return fullFileName;
	}
	private static String getPdfRootDir() {
		PDF_LOCAL_ROOT_DIR = null == PDF_LOCAL_ROOT_DIR? "D:/": PDF_LOCAL_ROOT_DIR;
		File file = new File(PDF_LOCAL_ROOT_DIR);
		if(!file.isDirectory()) {
			file.mkdirs();
		}
		return PDF_LOCAL_ROOT_DIR;
	}
	public static String buildDir(Date orderTime) {
		return "/order/" + TimeUtil.parseSimpleDateTime(orderTime);
	}
	private static String buildFileName(String storeCode, Date orderTime, String orderCode) {
//		String fileName = storeCode + "-" + FORMAT_SIMPLE_DATE.format(orderTime) + "送货单-" + orderCode.substring(orderCode.length() - 4, orderCode.length()) + ".pdf";
		String fileName = storeCode + "-" + FORMAT_SIMPLE_DATE.format(orderTime) + "-" + orderCode.substring(orderCode.length() - 4, orderCode.length()) + ".pdf";
		
//		try {
//			fileName = URLEncoder.encode(fileName, "UTF-8");
			
//			byte[] utf8 = fileName.getBytes("UTF-8");
//			fileName = new String(utf8, "UTF-8");
//		} catch (UnsupportedEncodingException e) {
//			e.printStackTrace();
//		}
		return fileName;
	}

	private static void createPdf(Document document, XdaOrderInfoODTO orderInfo, List<XdaOrderItemInfoODTO> orderItemList, int index, int pageSize, int pageNo, boolean isViewTotal) {
		try {
			BaseFont bfChinese = PdfUtils.chineseBaseFont();
			Font fontChinese = new Font(bfChinese, 9, Font.NORMAL);
			Font subBoldFontChinese = new Font(bfChinese, 9, Font.BOLD);

			setHead(document, fontChinese, subBoldFontChinese, pageNo, pageSize, orderInfo);
			float[] widths = {6f, 26f, 10f, 10f, 10f, 10f, 12f, 12f, 10f, 12f};
			PdfPTable table = new PdfPTable(widths);
			table.setHorizontalAlignment(Element.ALIGN_CENTER);
			table.setWidthPercentage(WIDTH_PERCENTAGE);
			setContextHead(fontChinese, table);
			setContext(fontChinese, table, orderItemList, index, orderInfo, isViewTotal);
			document.add(table);
			
			setButtom(document, fontChinese, subBoldFontChinese, orderInfo);
		} catch (DocumentException de) {
			logger.error("订单PDF（A4）生成器-生成文件异常:", de);
		} catch (Exception e) {
			logger.error("订单PDF（A4）生成器-生成文件异常:", e);
		}
	}

	// 数据头信息
	private static void setContextHead(Font fontChinese, PdfPTable table) {
		// table.setSpacingBefore(20f);
		PdfPCell cell = new PdfPCell(new Paragraph(" ", fontChinese));
		cell.setHorizontalAlignment(Element.ALIGN_CENTER);
		table.addCell(cell);
		
		cell = new PdfPCell(new Paragraph("单位|品名规格", fontChinese));
		cell.setHorizontalAlignment(Element.ALIGN_CENTER);
		table.addCell(cell);
		
		cell = new PdfPCell(new Paragraph("单位", fontChinese));
		cell.setHorizontalAlignment(Element.ALIGN_CENTER);
		table.addCell(cell);
		
		cell = new PdfPCell(new Paragraph("订单数量", fontChinese));
		cell.setHorizontalAlignment(Element.ALIGN_CENTER);
		table.addCell(cell);
		
		cell = new PdfPCell(new Paragraph("实发数量", fontChinese));
		cell.setHorizontalAlignment(Element.ALIGN_CENTER);
		table.addCell(cell);
		
		cell = new PdfPCell(new Paragraph("单价", fontChinese));
		cell.setHorizontalAlignment(Element.ALIGN_CENTER);
		table.addCell(cell);
		
		cell = new PdfPCell(new Paragraph("订单金额", fontChinese));
		cell.setHorizontalAlignment(Element.ALIGN_CENTER);
		table.addCell(cell);
		
		cell = new PdfPCell(new Paragraph("实发金额", fontChinese));
		cell.setHorizontalAlignment(Element.ALIGN_CENTER);
		table.addCell(cell);
			
		cell = new PdfPCell(new Paragraph("生产日期", fontChinese));
		cell.setHorizontalAlignment(Element.ALIGN_CENTER);
		table.addCell(cell);
		
		cell = new PdfPCell(new Paragraph("备注", fontChinese));
		cell.setHorizontalAlignment(Element.ALIGN_CENTER);
		table.addCell(cell);
	}

	// 数据信息
	private static void setContext(Font fontChinese, PdfPTable table, List<XdaOrderItemInfoODTO> orderItemList, int index, XdaOrderInfoODTO orderInfo, boolean isViewTotal) {
		
		Calendar calendar  = Calendar.getInstance();
		calendar.setTime(orderInfo.getOrderTime());
		calendar.add(Calendar.DATE, -1);
		String beforeOrderTime = TimeUtil.toString(calendar.getTime(), "MM-dd");
		
		for (XdaOrderItemInfoODTO orderItem : orderItemList) {
			index ++;
			PdfPCell cell = new PdfPCell(new Paragraph("" + index, fontChinese)); // 序号
			cell.setHorizontalAlignment(Element.ALIGN_CENTER);
			cell.setVerticalAlignment(Element.ALIGN_MIDDLE);
			// cell.setFixedHeight(24f);
			table.addCell(cell);
			
			String commodityName = orderItem.getCommodityName();
//			if (commodityName.getBytes().length > 23) {
//				commodityName = bSubstring(commodityName, 23);
//			}
			cell = new PdfPCell(new Paragraph(commodityName, fontChinese)); // 品名规格
			cell.setHorizontalAlignment(Element.ALIGN_LEFT);
			cell.setVerticalAlignment(Element.ALIGN_MIDDLE);
			cell.setFixedHeight(12f);
			table.addCell(cell);
			
			cell = new PdfPCell(new Paragraph(orderItem.getCommodityUnitName(), fontChinese)); // 单位
			cell.setHorizontalAlignment(Element.ALIGN_CENTER);
			cell.setVerticalAlignment(Element.ALIGN_MIDDLE);
			table.addCell(cell);
			
			cell = new PdfPCell(new Paragraph(getVal(orderItem.getCommodityQuantity()), fontChinese)); // 数量
			cell.setHorizontalAlignment(Element.ALIGN_CENTER);
			cell.setVerticalAlignment(Element.ALIGN_MIDDLE);
			table.addCell(cell);
			
			cell = new PdfPCell(new Paragraph(getVal(orderItem.getRealCommodityQuantity()), fontChinese)); // 实发数量
			cell.setHorizontalAlignment(Element.ALIGN_CENTER);
			cell.setVerticalAlignment(Element.ALIGN_MIDDLE);
			table.addCell(cell);
			
			cell = new PdfPCell(new Paragraph(getVal(orderItem.getCommodityPrice().setScale(2, RoundingMode.UP)), fontChinese)); // 单价
			cell.setHorizontalAlignment(Element.ALIGN_CENTER);
			cell.setVerticalAlignment(Element.ALIGN_MIDDLE);
			cell.setFixedHeight(12f);
			table.addCell(cell);
			
			cell = new PdfPCell(new Paragraph(getVal(orderItem.getCommodityAmount()), fontChinese)); // 订单金额
			cell.setHorizontalAlignment(Element.ALIGN_CENTER);
			cell.setVerticalAlignment(Element.ALIGN_MIDDLE);
			cell.setFixedHeight(12f);
			table.addCell(cell);
			
			cell = new PdfPCell(new Paragraph(getVal(orderItem.getRealCommodityAmount()), fontChinese)); // 实发金额
			cell.setHorizontalAlignment(Element.ALIGN_CENTER);
			cell.setVerticalAlignment(Element.ALIGN_MIDDLE);
			cell.setFixedHeight(12f);
			table.addCell(cell);
				
			String made = "见包装";
			if ("01".equals(orderItem.getCommodityPackageKind())) {
				made = beforeOrderTime;
			}
			cell = new PdfPCell(new Paragraph(made, fontChinese)); // 生产日期
			cell.setHorizontalAlignment(Element.ALIGN_CENTER);
			cell.setVerticalAlignment(Element.ALIGN_MIDDLE);
			cell.setFixedHeight(12f);
			table.addCell(cell);
			
			String remark = orderItem.getRemark() == null ? "" : orderItem.getRemark();
			cell = new PdfPCell(new Paragraph(remark, fontChinese)); // 备注
			cell.setHorizontalAlignment(Element.ALIGN_CENTER);
			cell.setVerticalAlignment(Element.ALIGN_MIDDLE);
			cell.setFixedHeight(12f);
			table.addCell(cell);
		}
		if (isViewTotal) {
			PdfPCell cell = new PdfPCell(new Paragraph(" ", fontChinese)); // 序号
			cell.setHorizontalAlignment(Element.ALIGN_CENTER);
			table.addCell(cell);
			
			cell = new PdfPCell(new Paragraph("合计", fontChinese)); // 品名规格
			cell.setHorizontalAlignment(Element.ALIGN_LEFT);
			cell.setFixedHeight(12f);
			table.addCell(cell);
			
			cell = new PdfPCell(new Paragraph("", fontChinese)); // 订单数量
			cell.setHorizontalAlignment(Element.ALIGN_CENTER);
			table.addCell(cell);
			cell = new PdfPCell(new Paragraph("", fontChinese)); // 实发数量
			cell.setHorizontalAlignment(Element.ALIGN_CENTER);
			table.addCell(cell);
			
			cell = new PdfPCell(new Paragraph("", fontChinese)); // 单价
			cell.setHorizontalAlignment(Element.ALIGN_CENTER);
			table.addCell(cell);
			
			cell = new PdfPCell(new Paragraph(getVal(orderInfo.getOrderAmount()), fontChinese));
			cell.setHorizontalAlignment(Element.ALIGN_CENTER);
			cell.setFixedHeight(12f);
			table.addCell(cell);
			cell = new PdfPCell(new Paragraph(getVal(orderInfo.getRealAmount()), fontChinese));
			cell.setHorizontalAlignment(Element.ALIGN_CENTER);
			cell.setFixedHeight(12f);
			table.addCell(cell);
			
			cell = new PdfPCell(new Paragraph("", fontChinese)); // 生产日期
			cell.setHorizontalAlignment(Element.ALIGN_CENTER);
			table.addCell(cell);
			
			cell = new PdfPCell(new Paragraph("", fontChinese)); // 备注
			cell.setHorizontalAlignment(Element.ALIGN_CENTER);
			table.addCell(cell);
		}
	}

	public static String getVal(BigDecimal value) {
		if (value == null) {
			logger.error("b   is null");
			return "0";
		}
		String val = value.toString();
		if (val.endsWith(".00")) {
			val = val.substring(0, val.indexOf("."));
		} else if (val.endsWith(".0")) {
			val = val.substring(0, val.indexOf("."));
		}
		return val;
	}
	
//	public static String getVal(Double b) {
//		if (b == null || b == 0) {
//			logger.error("b   is null");
//			return "0";
//		}
//		String val = b.toString();
//		if (val.endsWith(".00")) {
//			val = val.substring(0, val.indexOf("."));
//		} else if (val.endsWith(".0")) {
//			val = val.substring(0, val.indexOf("."));
//		}
//		return val;
//	}

	// 页眉信息
	private static void setHead(Document document, Font fontChinese, Font subBoldFontChinese, int pageNo, int pageSize, XdaOrderInfoODTO orderInfo) throws DocumentException {
		float[] widths = { 13f, 40f, 13f, 30f };
		PdfPTable table = new PdfPTable(widths);
		table.setHorizontalAlignment(Element.ALIGN_CENTER);
		table.setWidthPercentage(WIDTH_PERCENTAGE);
		
		BaseFont bfChinese = PdfUtils.chineseBaseFont();
		Font companyFont = new Font(bfChinese, 13, Font.BOLD);
		PdfPCell cell = new PdfPCell(new Paragraph(orderInfo.getCompanyName(), companyFont));
		cell.setColspan(4);
		cell.setBorder(0);
		cell.setHorizontalAlignment(Element.ALIGN_CENTER);
		table.addCell(cell);
		
//		String testReport = orderInfo.getStoreIsTestReport() == 0? "(化验)　": "";
//		cell = new PdfPCell(new Paragraph(testReport + "第" + pageNo + "页 共" + pageSize + "页", fontChinese));
		cell = new PdfPCell(new Paragraph("第" + pageNo + "页 共" + pageSize + "页", fontChinese));
		cell.setColspan(4);
		cell.setBorder(0);
		cell.setHorizontalAlignment(Element.ALIGN_RIGHT);
		table.addCell(cell);

		cell = new PdfPCell(new Paragraph("客户编号：", subBoldFontChinese));
		cell.setHorizontalAlignment(Element.ALIGN_CENTER);
		cell.setBorder(0);
		table.addCell(cell);
		cell = new PdfPCell(new Paragraph(orderInfo.getStoreCode(), fontChinese));
		cell.setBorder(0);
		table.addCell(cell);
		
		cell = new PdfPCell(new Paragraph("订单编号：", subBoldFontChinese));
		cell.setBorder(0);
		cell.setHorizontalAlignment(Element.ALIGN_CENTER);
		table.addCell(cell);
		cell = new PdfPCell(new Paragraph(orderInfo.getOrderCode(), fontChinese));
		cell.setBorder(0);
		table.addCell(cell);

		cell = new PdfPCell(new Paragraph("付款单位：", subBoldFontChinese));
		cell.setHorizontalAlignment(Element.ALIGN_CENTER);
		cell.setBorder(0);
		table.addCell(cell);
		cell = new PdfPCell(new Paragraph(orderInfo.getStoreName(), fontChinese));
		cell.setBorder(0);
		table.addCell(cell);
		
		cell = new PdfPCell(new Paragraph("销售日期：", subBoldFontChinese));
		cell.setBorder(0);
		cell.setHorizontalAlignment(Element.ALIGN_CENTER);
		table.addCell(cell);
		cell = new PdfPCell(new Paragraph(TimeUtil.parseSimpleDateTime(orderInfo.getOrderTime()), fontChinese));
		cell.setBorder(0);
		table.addCell(cell);

		cell = new PdfPCell(new Paragraph("送货地址：", subBoldFontChinese));
		cell.setHorizontalAlignment(Element.ALIGN_CENTER);
		cell.setBorder(0);
		table.addCell(cell);
		cell = new PdfPCell(new Paragraph(orderInfo.getStoreDeliveryAddress(), fontChinese));
		cell.setBorder(0);
		table.addCell(cell);
		
		cell = new PdfPCell(new Paragraph("联系电话：", subBoldFontChinese));
		cell.setBorder(0);
		cell.setHorizontalAlignment(Element.ALIGN_CENTER);
		table.addCell(cell);
		cell = new PdfPCell(new Paragraph(orderInfo.getStoreLinkmanMobile(), fontChinese));
		cell.setBorder(0);
		table.addCell(cell);
		
		document.add(table);
	}

	// 页脚信息
	private static void setButtom(Document document, Font fontChinese, Font subBoldFontChinese, XdaOrderInfoODTO orderInfo) throws DocumentException {
		float[] widths = {40f, 35f};
		PdfPTable table = new PdfPTable(widths);
		table.setHorizontalAlignment(Element.ALIGN_CENTER);
		table.setWidthPercentage(WIDTH_PERCENTAGE);
		
		String storeLineGroupName = null == orderInfo.getStoreLineGroupName()? " ": orderInfo.getStoreLineGroupName();
		PdfPCell cell = new PdfPCell(new Paragraph("线路组：" + storeLineGroupName, fontChinese));
		cell.setBorder(0);
		cell.setFixedHeight(13f);
		table.addCell(cell);
		
		String regionalManagerName = orderInfo.getRegionalManagerName();
		cell = new PdfPCell(new Paragraph("区域经理：" + regionalManagerName, fontChinese));
		cell.setBorder(0);
		cell.setFixedHeight(13f);
		table.addCell(cell);
		
		String createName = null == orderInfo.getCreateName()? orderInfo.getStoreCode(): orderInfo.getCreateName();
		cell = new PdfPCell(new Paragraph("操作员：" + createName, fontChinese));
		cell.setBorder(0);
		cell.setFixedHeight(13f);
		table.addCell(cell);

		cell = new PdfPCell(new Paragraph("督导：" +  orderInfo.getSupervisionName(), fontChinese));
		cell.setBorder(0);
		cell.setFixedHeight(13f);
		table.addCell(cell);

		String generateTime = orderInfo.getGenerateTime();
		cell = new PdfPCell(new Paragraph("生成时间：" + generateTime, fontChinese));
		cell.setBorder(0);
		table.addCell(cell);
		
		String deliveryManName = orderInfo.getDeliveryManName();
		cell = new PdfPCell(new Paragraph("送货员：" + deliveryManName, fontChinese));
		cell.setBorder(0);
		cell.setFixedHeight(13f);
		table.addCell(cell);

		cell = new PdfPCell(new Paragraph("签收人：", fontChinese));
		cell.setColspan(2);
		cell.setBorder(0);
		table.addCell(cell);
		
		String orderRemark = null == orderInfo.getOrderRemark()? "": orderInfo.getOrderRemark();
		cell = new PdfPCell(new Paragraph("备注：" + orderRemark, fontChinese));
		cell.setColspan(2);
		cell.setBorder(0);
		cell.setFixedHeight(13f);
		table.addCell(cell);

		String carrierName = null == orderInfo.getCarrierName()? "": orderInfo.getCarrierName();
		cell = new PdfPCell(new Paragraph("承运方：" + carrierName, fontChinese));
		cell.setColspan(2);
		cell.setBorder(0);
		table.addCell(cell);


		String thirdPartyCode = null == orderInfo.getThirdPartyCode()? "": orderInfo.getThirdPartyCode();
		cell = new PdfPCell(new Paragraph("三方物流单号：" + thirdPartyCode, fontChinese));
		cell.setColspan(2);
		cell.setBorder(0);
		cell.setFixedHeight(13f);
		table.addCell(cell);

		document.add(table);
	}
	
	public static String bSubstring(String s, int length) {
		try {
			byte[] bytes = s.getBytes("Unicode");
			int n = 0; // 表示当前的字节数
			int i = 2; // 要截取的字节数，从第3个字节开始
			for (; i < bytes.length && n < length; i++) {
				// 奇数位置，如3、5、7等，为UCS2编码中两个字节的第二个字节
				if (i % 2 == 1) {
					n++; // 在UCS2第二个字节时n加1
				} else {
					// 当UCS2编码的第一个字节不等于0时，该UCS2字符为汉字，一个汉字算两个字节
					if (bytes[i] != 0) {
						n++;
					}
				}
			}
			// 如果i为奇数时，处理成偶数
			if (i % 2 == 1) {
				// 该UCS2字符是汉字时，去掉这个截一半的汉字
				if (bytes[i - 1] != 0) {
					i = i - 1;
					// 该UCS2字符是字母或数字，则保留该字符
				}else {
					i = i + 1;
				}
			}
			return new String(bytes, 0, i, "Unicode");
		} catch (Exception e) {
			logger.error("截取字符串错误：", e);
			return s.substring(0, 20);
		}
	}

	
	
}

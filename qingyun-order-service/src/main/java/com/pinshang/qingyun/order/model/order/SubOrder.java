package com.pinshang.qingyun.order.model.order;

import com.pinshang.qingyun.base.constant.QingyunConstant;
import com.pinshang.qingyun.base.enums.SubOrderStatusEnums;
import com.pinshang.qingyun.base.po.BaseEnterpriseSimplePO;
import com.pinshang.qingyun.order.dto.miniGroupon.MiniGrouponAutoOrderIDTO;
import lombok.Data;

import javax.persistence.Entity;
import javax.persistence.Table;
import java.math.BigDecimal;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

//子订单表
@Entity
@Data
@Table(name = "t_sub_order")
public class SubOrder extends BaseEnterpriseSimplePO {
    //订单id
    private Long orderId;
    /*物流模式 0-直送, 1-配送, 2-直通*/
    private Integer logisticsModel;
    //品种合计
    private Integer varietyTotal;
    //供应商id
    private Long supplierId;
    //仓库id
    private Long warehouseId;
    //0:未生成do单, 1:已生成do单， 2已取消;
    private Integer status;
    //下单时间
    private Date orderTime;
    //采购单id(直送自动生成采购单)
    private Long purchaseOrderId;
    //子单编号
    private String subOrderCode;
    //总价
    private BigDecimal totalPrice;
    // 已回填实发数量标识
    private Boolean writebackRealQtyFlag;

    private Date updateTime;


    /**
     * 是否预售:0=否，1=是
     */
    private Integer presaleStatus;

    /**
     * 库存依据  1=依据大仓, 2=不限量订货, 3=限量供应
     */
    private Integer stockType;

    public static SubOrder initForMiniFroupon(List<MiniGrouponAutoOrderIDTO> itemList, Order order) {
        MiniGrouponAutoOrderIDTO item = itemList.get(0);
        SubOrder subOrder = new SubOrder();
        subOrder.setOrderId(order.getId());
        subOrder.setLogisticsModel(item.getLogisticsModel());
        subOrder.setVarietyTotal(itemList.size());
        subOrder.setWarehouseId(item.getWarehouseId());
        subOrder.setSupplierId(item.getSupplierId());
        subOrder.setCreateId(-1L);
        subOrder.setCreateTime(new Date());
        subOrder.setEnterpriseId(QingyunConstant.ENTERPRISE_ID);
        subOrder.setStatus(SubOrderStatusEnums.SUB_ORDER_UNDELIVERY.getCode());
        if(order.getOrderTime() != null){
            subOrder.setOrderTime(order.getOrderTime());
        }else{
            Calendar c =Calendar.getInstance();
            c.add(Calendar.DAY_OF_YEAR, 1);
            subOrder.setOrderTime(c.getTime());
        }
        subOrder.setSubOrderCode(order.getOrderCode());
        subOrder.setTotalPrice(order.getOrderAmount());
        subOrder.setUpdateTime(new Date());
        return subOrder;
    }
    
}

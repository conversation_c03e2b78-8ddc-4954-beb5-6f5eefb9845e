package com.pinshang.qingyun.order.model.order;

import com.pinshang.qingyun.base.enums.YesOrNoEnums;
import com.pinshang.qingyun.base.enums.order.OrderTypeEnum;
import com.pinshang.qingyun.base.enums.xda.XdaOrderProcessStatusEunm;
import com.pinshang.qingyun.base.po.BaseEnterprisePO;
import com.pinshang.qingyun.box.utils.DateTimeUtil;
import com.pinshang.qingyun.box.utils.IDGenerator;
import com.pinshang.qingyun.order.dto.order.AssociationOrderODTO;
import com.pinshang.qingyun.order.dto.OrderMockDTO;
import com.pinshang.qingyun.order.dto.xda.v2.XdaCreatePrePayOrderV2IDTO;
import com.pinshang.qingyun.order.dto.xda.v3.XdaCreatePrePayOrderV3IDTO;
import com.pinshang.qingyun.order.dto.xda.v4.XdaCreatePrePayOrderV4IDTO;
import com.pinshang.qingyun.order.enums.DeliveryBatchTypeEnum;
import com.pinshang.qingyun.order.enums.OrderModeType;
import com.pinshang.qingyun.order.enums.OrderPrintTypeEnum;
import com.pinshang.qingyun.order.model.store.Store;
import com.pinshang.qingyun.order.vo.StoreCompanyRespVo;
import com.pinshang.qingyun.order.vo.store.StoreCompanyVo;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import javax.persistence.Entity;
import javax.persistence.Table;
import javax.persistence.Transient;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Map;

@Entity
@Table(name = "t_order")
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class Order extends BaseEnterprisePO {
	
	/**订单号 **/
    private String orderCode;
	/**商铺id **/
    private Long storeId;
	/**订单时间 **/
    private Date orderTime;
	/**订单类型：（0：普通订单，1：补货单） **/
    private Integer modeType;
	/**订单类型 **/
    private Integer orderType;
	/**打印份数 **/
    private Integer printNum;
	/**打印类型(1：本地,2：送货员,3：不打印) **/

    private OrderPrintTypeEnum printType;
	/**应付金额（参与促销活动之前的源始金额） **/
    private BigDecimal totalAmount;
	/**订单金额 **/
    private BigDecimal orderAmount;

    /**
     * 订单的优惠券抵扣金额
     */
    @Transient
    private BigDecimal couponDiscountAmount;

    /**订单最终金额 **/
    private BigDecimal finalAmount;
    /**订单运费金额（免运费为0）**/
    private BigDecimal freightAmount;
    /** 优惠金额合计 */
    private BigDecimal promotionAmount;
	/**备注 **/
    private String orderRemark;
	/**更新者 **/
    //private Long updateId;
	/**订单状态(0正常,1删除) **/
    private Integer orderStatus;
	/**创建者 **/
    //private Long createId;
    /**创建者姓名**/
    
    @Transient
    private String createUserName;
    @Transient
    private Store store;
    @Transient
    private List<OrderList> orderList;
    @Transient
    private String  errorMsg;

    private Integer deliveryBatch;
    private String deliveryBatchRemark;

    /** 订单是否允许修改价格 */
    private Integer changePriceStatus;
    private Integer    syncStatus;
    private Integer    settleStatus;
    /** 流程状态(鲜达新加)  7=待发货    11=出库中　15=配送中　　19=配送完成 */
    private Integer processStatus;
    /** 订单截止时间(鲜达新加),根据当前客户截止时间与订单中的最晚商品截止时间得来;订单取消与是否进大仓时使用 */
    private Date orderDurationTime;

    /**
     * 实发总金额
     **/
    private BigDecimal realTotalPrice;
    /**
     * 实发日期
     **/
    private Date realOrderTime;
    /**
     * 所属公司ID
     **/
    private Long companyId;
    private Long consignmentId; // 代销商户id
    /**
     * 订单所有子单是否出库完成（不包括直送类型） 等于1-完成
     **/
    private Integer realSubOrderDoneStatus;

    private Long cacelReasonId;

    /**
     * 是否预售:0=否，1=是
     */
    private Integer presaleStatus;

    private Long logisticsCenterId;

    private String logisticsCenter;

    private Integer businessType;

    private String deliveryTimeRange;

    // 结算日期 xx
    private Date settleOrderTime;
    // 客户类型 苏坤 & 陈奇 物流 小B需求存值
    private Long storeTypeId;

    /**
     * 通达物流 司机ID
     */
    private Long driverId;

    @Transient
    private String storeTypeName;

    /** 记录order_list 和 sub_order_item关联 */
    @Transient
    private Map<Long, List<AssociationOrderODTO>> associationOrderMap;

    /** 档口id */
    private Long stallId = -1L;
    /** 是否直送 0=否  1=是 */
    private Integer directStatus = 0;

    public Order(OrderMockDTO dto) {
        this.companyId=1L;
        this.storeId=dto.getStoreId();
        this.orderTime=dto.getOrderTime();
        this.modeType=0;
        this.orderType=1;
        this.orderStatus=0;
        this.processStatus=7;
        this.syncStatus=1;
        this.deliveryBatch=0;
        this.deliveryBatchRemark="0_";
        this.presaleStatus=dto.getPresaleStatus();
        this.businessType=dto.getBusinessType();
        this.stallId=dto.getStallId();
    }

    public Long getStallId() {
        return stallId;
    }

    public void setStallId(Long stallId) {
        this.stallId = stallId;
    }

    public BigDecimal getActualPaid() {
        BigDecimal actualPaid = BigDecimal.ZERO;

        if (freightAmount != null) {
            actualPaid = actualPaid.add(freightAmount);
        }

        if (orderAmount != null) {
            actualPaid = actualPaid.add(orderAmount);
    	}
    	
    	return actualPaid;
    }

    public static Order initForDo(Long storeId,BigDecimal amount){
        Order order = new Order();
        order.setOrderCode(IDGenerator.newOrderCode());
        order.setStoreId(storeId);
        order.setFinalAmount(amount);
        order.setOrderAmount(amount);
        Date date = new Date();
        order.setOrderTime(date);
        order.setCreateTime(date);
        order.setUpdateTime(date);
        order.setCreateId(254L);
        order.setUpdateId(254L);
        order.setModeType(0);
        order.setOrderType(1);
        order.setPrintNum(1);
        order.setPrintType(OrderPrintTypeEnum.LOCAL);
        order.setOrderStatus(0);
        order.setSyncStatus(1);
        order.setSettleStatus(0);
        order.setDeliveryBatch(1);
        order.setEnterpriseId(78L);
        return order;
    }

    public static Order buildCompleteOrder(Integer processStatus,Long userId){
        Order order = new Order();
        Date date = new Date();
        order.setUpdateTime(date);
        order.setUpdateId(userId);
        order.setProcessStatus(processStatus);
        return order;
    }

    public static Order initForMiniFroupon(StoreCompanyRespVo shopInfo, String orderTime, BigDecimal orderAmount,BigDecimal promotionAmount,Integer changePriceStatus){
        Date date = new Date();
        Order order = new Order();
        order.setEnterpriseId(78L);
        order.setOrderCode(IDGenerator.newOrderCode());
        order.setOrderTime(DateTimeUtil.parse(orderTime, "yyyy-MM-dd"));
        if(shopInfo != null){
            order.setStoreId(shopInfo.getStoreId());
            order.setCompanyId(shopInfo.getCompanyId());
        }
        order.setModeType(OrderModeType.ORDER.getCode());
        order.setOrderType(OrderTypeEnum.MINI_GROUPON_ORDER.getCode());
        order.setPrintNum(1);
        order.setPrintType(OrderPrintTypeEnum.NOPRInteger);
        order.setOrderAmount(orderAmount);
        order.setFinalAmount(promotionAmount);
        order.setOrderRemark("mini团购订单");
        order.setOrderStatus(0);
        order.setCreateId(-1L);
        order.setUpdateId(-1L);
        order.setCreateTime(date);
        order.setUpdateTime(date);
        order.setProcessStatus(XdaOrderProcessStatusEunm.WAITING_SHIP.getCode());
        order.setSyncStatus(YesOrNoEnums.NO.getCode());
        order.setSettleStatus(0);
        order.setDeliveryBatch(DeliveryBatchTypeEnum.ONE_BATCH.getCode());
        order.setDeliveryBatchRemark(order.getDeliveryBatch()+"_");
        order.setChangePriceStatus(changePriceStatus);
        order.setPresaleStatus(1);
        order.setSettleOrderTime(order.getOrderTime());
        return order;
    }

    /**
     * 初始化 鲜达订单信息
     * @param xdaCreatePrePayOrderV2IDTO
     * @param amount
     * @param orderDurationTime
     * @param companyIdByStoreId
     * @return
     */
    public static Order initForXdaOrder(XdaCreatePrePayOrderV2IDTO xdaCreatePrePayOrderV2IDTO,BigDecimal amount, Date orderDurationTime,StoreCompanyVo companyIdByStoreId){
        Order order = new Order();
        Date date = new Date();
        order.setCreateId(xdaCreatePrePayOrderV2IDTO.getStoreId());
        order.setUpdateId(xdaCreatePrePayOrderV2IDTO.getStoreId());
        order.setCreateTime(date);
        order.setUpdateTime(date);
        order.setOrderAmount(amount);
        order.setFinalAmount(amount);
        order.setStoreId(xdaCreatePrePayOrderV2IDTO.getStoreId());
        order.setOrderTime(xdaCreatePrePayOrderV2IDTO.getOrderTime());
        order.setOrderCode(IDGenerator.newOrderCode());
        order.setPrintNum(1);
        order.setOrderType(OrderTypeEnum.XDA_APP_ORDER.getCode());
        order.setModeType(OrderModeType.ORDER.getCode());
        order.setPrintType(OrderPrintTypeEnum.NOPRInteger);
        order.setOrderRemark(xdaCreatePrePayOrderV2IDTO.getRemark());
        order.setOrderStatus(0);
        order.setSyncStatus(YesOrNoEnums.NO.getCode());
        order.setOrderDurationTime(orderDurationTime);
        order.setProcessStatus(XdaOrderProcessStatusEunm.WAITING_SHIP.getCode());
        order.setCompanyId(companyIdByStoreId.getCompanyId());
        order.setStoreTypeId(companyIdByStoreId.getStoreTypeId());
        order.setPresaleStatus(1);
        order.setSettleOrderTime(order.getOrderTime());
        return order;
    }
    /**
     * 初始化 鲜达订单信息
     * @param xdaCreatePrePayOrderV3IDTO
     * @param amount
     * @param orderDurationTime
     * @param companyIdByStoreId
     * @return
     */
    public static Order initForXdaOrderV3(XdaCreatePrePayOrderV3IDTO xdaCreatePrePayOrderV3IDTO, BigDecimal amount, Date orderDurationTime, StoreCompanyVo companyIdByStoreId){
        Order order = new Order();
        Date date = new Date();
        order.setCreateId(xdaCreatePrePayOrderV3IDTO.getStoreId());
        order.setUpdateId(xdaCreatePrePayOrderV3IDTO.getStoreId());
        order.setCreateTime(date);
        order.setUpdateTime(date);
        order.setOrderAmount(amount);
        order.setFinalAmount(amount);
        order.setStoreId(xdaCreatePrePayOrderV3IDTO.getStoreId());
        order.setOrderTime(xdaCreatePrePayOrderV3IDTO.getOrderTime());
        order.setOrderCode(IDGenerator.newOrderCode());
        order.setPrintNum(1);
        order.setOrderType(OrderTypeEnum.XDA_APP_ORDER.getCode());
        order.setModeType(OrderModeType.ORDER.getCode());
        order.setPrintType(OrderPrintTypeEnum.NOPRInteger);
        order.setOrderRemark(xdaCreatePrePayOrderV3IDTO.getRemark());
        order.setOrderStatus(0);
        order.setSyncStatus(YesOrNoEnums.NO.getCode());
        order.setOrderDurationTime(orderDurationTime);
        order.setProcessStatus(XdaOrderProcessStatusEunm.WAITING_SHIP.getCode());
        order.setCompanyId(companyIdByStoreId.getCompanyId());
        order.setStoreTypeId(companyIdByStoreId.getStoreTypeId());
        order.setPresaleStatus(1);
        order.setSettleOrderTime(order.getOrderTime());
        return order;
    }
    /**
     * 初始化 鲜达订单信息
     *
     * @param xdaCreatePrePayOrderV4IDTO
     * @param amount
     * @param freightAmount
     * @param orderDurationTime
     * @return
     */
    public static Order initForXdaOrderV4(XdaCreatePrePayOrderV4IDTO xdaCreatePrePayOrderV4IDTO, BigDecimal amount, BigDecimal freightAmount, Date orderDurationTime, StoreCompanyVo storeCompanyVo){
        Order order = new Order();
        Date date = new Date();
        order.setCreateId(xdaCreatePrePayOrderV4IDTO.getStoreId());
        order.setUpdateId(xdaCreatePrePayOrderV4IDTO.getStoreId());
        order.setCreateTime(date);
        order.setUpdateTime(date);
        order.setOrderAmount(amount);
        order.setFinalAmount(amount);
        order.setStoreId(xdaCreatePrePayOrderV4IDTO.getStoreId());
        order.setOrderTime(xdaCreatePrePayOrderV4IDTO.getOrderTime());
        order.setOrderCode(IDGenerator.newOrderCode());
        order.setPrintNum(1);
        order.setOrderType(OrderTypeEnum.XDA_APP_ORDER.getCode());
        order.setModeType(OrderModeType.ORDER.getCode());
        order.setPrintType(OrderPrintTypeEnum.NOPRInteger);
        order.setOrderRemark(xdaCreatePrePayOrderV4IDTO.getRemark());
        order.setOrderStatus(0);
        order.setSyncStatus(YesOrNoEnums.NO.getCode());
        order.setOrderDurationTime(orderDurationTime);
        order.setProcessStatus(XdaOrderProcessStatusEunm.WAITING_SHIP.getCode());
        order.setCompanyId(storeCompanyVo.getCompanyId());
        order.setStoreTypeId(storeCompanyVo.getStoreTypeId());
        order.setPresaleStatus(YesOrNoEnums.NO.getCode());
        order.setSettleOrderTime(order.getOrderTime());
        order.setFreightAmount(freightAmount);
        return order;
    }


}
/*
 * tramy.cn Inc.
 * Copyright (c) 2021-2024 All Rights Reserved.
 */
package com.pinshang.qingyun.order.dto.xda.tda;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <p>
 * 退货单审核
 * </p>
 *
 * <AUTHOR> shenyang
 * @version : 1.0.0
 * @history : modify history
 * <author>              <time>              <version>              <desc>
 * @since : 2024/05/11 10:09
 */
@Data
public class AuditReturnOrdeItemIDTO {

    /**
     * id
     */
    @ApiModelProperty(value = "id")
    private Long id;

    /**
     * 审核商品份数
     */
    @ApiModelProperty(value = "审核份数")
    private Integer checkNumber;

    /**
     * 审核商品金额
     */
    @ApiModelProperty("审核商品金额")
    private BigDecimal checkMoney;

    /**
     * 审核商品数量
     */
    @ApiModelProperty("审核商品数量")
    private BigDecimal checkQuantity;

    /**
     * 责任方
     */
    @ApiModelProperty(value = "责任方编码")
    private String responsibleParty;

    /**
     * 审核备注
     */
    @ApiModelProperty(value = "审核备注")
    private String auditRemark;

    /**
     * 商品价格
     */
    @ApiModelProperty(value = "商品价格")
    private BigDecimal commodityPrice;
}

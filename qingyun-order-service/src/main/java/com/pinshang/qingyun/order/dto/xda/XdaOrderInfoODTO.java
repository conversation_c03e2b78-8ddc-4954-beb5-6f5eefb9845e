package com.pinshang.qingyun.order.dto.xda;

import io.swagger.annotations.ApiModelProperty;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import lombok.Data;
import lombok.NoArgsConstructor;

import com.pinshang.qingyun.box.utils.StringUtil;
import com.pinshang.qingyun.box.utils.TimeUtil;
import com.pinshang.qingyun.order.model.order.Order;
import com.pinshang.qingyun.order.model.order.OrderMirror;
import com.pinshang.qingyun.order.model.store.Store;

/**
 * 送货单
 */
@Data
@NoArgsConstructor
public class XdaOrderInfoODTO {
	@ApiModelProperty(position = 10, value = "订单ID", required = true, hidden = true)
	private Long orderId;
	@ApiModelProperty(position = 11, value = "订单编号", required = true)
	private String orderCode;
	@ApiModelProperty(position = 12, value = "销售日期", required = true)
	private Date orderTime;
	@ApiModelProperty(position = 13, value = "备注", required = true)
	private String orderRemark;
	@ApiModelProperty(position = 14, value = "订单金额", required = true)
	private BigDecimal orderAmount;
	@ApiModelProperty(position = 15, value = "实发金额", required = true)
	private BigDecimal realAmount;
	@ApiModelProperty(position = 16, value = "公司名称", required = true)
	private String companyName;
	@ApiModelProperty(position = 17, value = "生成时间", required = true)
	private String generateTime;
	
	@ApiModelProperty(position = 20, value = "客户ID", required = true, hidden = true)
	private Long storeId;
	@ApiModelProperty(position = 21, value = "客户编号", required = true)
	private String storeCode;
	@ApiModelProperty(position = 22, value = "客户名称", required = true)
	private String storeName;
	@ApiModelProperty(position = 23, value = "客户送货地址", required = true)
	private String storeDeliveryAddress;
	@ApiModelProperty(position = 24, value = "客户联系电话", required = true)
	private String storeLinkmanMobile;
	@ApiModelProperty(position = 25, value = "客户检验报告", required = true) // null 默认为1
	private Integer storeIsTestReport;
	@ApiModelProperty(position = 26, value = "线路组", required = true)
	private String storeLineGroupName;
	@ApiModelProperty(position = 27, value = "操作员", required = true)
	private String createName;
	
	@ApiModelProperty(position = 31, value = "区域经理ID", required = true, hidden = true)
	private Long regionalManagerId;
	@ApiModelProperty(position = 31, value = "区域经理名称", required = true)
	private String regionalManagerName;
	@ApiModelProperty(position = 31, value = "区域经理电话", required = true)
	private String regionalManagerPhone;
	
	@ApiModelProperty(position = 32, value = "督导ID", required = true, hidden = true)
	private Long supervisionId;
	@ApiModelProperty(position = 32, value = "督导名称", required = true)
	private String supervisionName;
	@ApiModelProperty(position = 32, value = "督导电话", required = true, hidden = true)
	private String supervisionPhone;
	
	@ApiModelProperty(position = 33, value = "送货员ID", required = true)
	private Long deliveryManId;
	@ApiModelProperty(position = 33, value = "送货员名称", required = true)
	private String deliveryManName;
	@ApiModelProperty(position = 33, value = "送货员电话", required = true)
	private String deliveryManPhone;

	@ApiModelProperty(position = 34, value = "承运商名称", required = true)
	private String carrierName;

	@ApiModelProperty(position = 35, value = "第三方承运商编码", required = true)
	private String thirdPartyCode;
	
	@ApiModelProperty(position = 90, value = "订单项List", required = true)
	private List<XdaOrderItemInfoODTO> orderItemList;
	
	public XdaOrderInfoODTO(Order order) {
		this.orderId = order.getId();
		this.orderCode = order.getOrderCode();
		this.orderTime = order.getOrderTime();
		this.orderRemark = order.getOrderRemark();
		this.orderAmount = order.getOrderAmount();
		this.realAmount = order.getRealTotalPrice();
//		this.companyName = companyName;
		this.generateTime = TimeUtil.formatFullDate(new Date());
	}
	
	public XdaOrderInfoODTO setStoreInfo(Store store) {
		if (null != store) {
			this.storeId = store.getId();
			this.storeCode = store.getStoreCode();
			this.storeName = store.getStoreName();
			this.storeDeliveryAddress = store.getDeliveryAddress();
			this.storeLinkmanMobile = store.getLinkmanMobile();
			this.storeIsTestReport = store.getIsTestReport();
//			this.storeLineGroupName = storeLineGroupName;
//			this.createName = createName;
		}
		return this;
	}
	
	public XdaOrderInfoODTO setOrderMirrorInfo(OrderMirror orderMirror) {
		if (null != orderMirror) {
			this.regionalManagerId = orderMirror.getRegionalManagerId();
			this.regionalManagerName = orderMirror.getRegionalManagerName();
//			this.regionalManagerPhone = regionalManagerPhone;
			
			this.supervisionId = orderMirror.getSupervisionId();
			this.supervisionName = orderMirror.getSupervisionName();
//			this.supervisionPhone = supervisionPhone;
			
			this.deliveryManId = orderMirror.getDeliveryManId();
			this.deliveryManName = orderMirror.getDeliveryManName();
//			this.deliveryManPhone = deliveryManPhone;
		}
		return this;
	}

	public String getRegionalManagerName() {
		return this.name(regionalManagerName, regionalManagerPhone);
	}

	public String getSupervisionName() {
		return this.name(supervisionName, supervisionPhone);
	}

	public String getDeliveryManName() {
		return this.name(deliveryManName, deliveryManPhone);
	}
	
	public List<XdaOrderItemInfoODTO> getOrderItemList() {
		return (null == orderItemList) ? new ArrayList<>(): orderItemList;
	}
	
	private String name(String name, String phone) {
		if (StringUtil.isNullOrEmpty(name)) {
			return "";
		}
		if (!StringUtil.isNullOrEmpty(phone)) {
			return name + "(" + phone + ")";
		}
		return name;
	}
	
}
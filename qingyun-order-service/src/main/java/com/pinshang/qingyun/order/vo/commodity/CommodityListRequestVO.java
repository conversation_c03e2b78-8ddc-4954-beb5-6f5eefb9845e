package com.pinshang.qingyun.order.vo.commodity;

import com.pinshang.qingyun.order.mapper.entry.stall.StallEntry;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
@Data
@NoArgsConstructor
@AllArgsConstructor
public class CommodityListRequestVO extends StallEntry {

	private static final long serialVersionUID = 1L;

	private Long enterpriseId;
	
	private String storeId;
	private Long shopId;
	
	private String commodityKey;
	
	private Long categoryId;
	
	private Long supplerId;
	
	private boolean isPromotionProduct;//是否促销
	
	private String orderTime;

	private List<Long> commodityIdList;
	private List<String> commodityCodeList;

	private String barCode;//条形码
	private Long commodityId;//商品ID
	private boolean isWeight;//称重商品
	private boolean isNormal;//标品
	private boolean isNew;//新品

	// 是否新品 1=是 0= 否
	private Integer newProduct;

	private Integer logisticsModel;//物流模式

	private Long warehouseId;//仓库
	private List<Long> commodityIdList2;
	private List<Long> commodityIdList3;

	private boolean showZero;//是否展示数量为0的商品
	private List<Long> commodityIdListAll;

	private Long checkGroupId; //考核组id
	private List<Long> checkGroupCommodityIdList;

	private List<Long> commodityPurchaseIdList;

	private Boolean autoCommodity; // 是否自定订货

	/** app状态：0-上架，1-下架 */
	private Integer appStatus;

	private boolean freshCommodity; // 是否日日鲜商品

	private boolean oftenBuyProduct; // 是否常购商品

	public boolean isOftenBuyProduct() {
		return oftenBuyProduct;
	}

	public void setOftenBuyProduct(boolean oftenBuyProduct) {
		this.oftenBuyProduct = oftenBuyProduct;
	}

	public boolean isFreshCommodity() {
		return freshCommodity;
	}

	public void setFreshCommodity(boolean freshCommodity) {
		this.freshCommodity = freshCommodity;
	}

	public Integer getAppStatus() {
		return appStatus;
	}

	public void setAppStatus(Integer appStatus) {
		this.appStatus = appStatus;
	}

	public Boolean getAutoCommodity() {
		return autoCommodity;
	}

	public void setAutoCommodity(Boolean autoCommodity) {
		this.autoCommodity = autoCommodity;
	}

	public List<Long> getCommodityPurchaseIdList() {
		return commodityPurchaseIdList;
	}

	public void setCommodityPurchaseIdList(List<Long> commodityPurchaseIdList) {
		this.commodityPurchaseIdList = commodityPurchaseIdList;
	}

	public Long getCheckGroupId() {
		return checkGroupId;
	}

	public void setCheckGroupId(Long checkGroupId) {
		this.checkGroupId = checkGroupId;
	}

	public List<Long> getCheckGroupCommodityIdList() {
		return checkGroupCommodityIdList;
	}

	public void setCheckGroupCommodityIdList(List<Long> checkGroupCommodityIdList) {
		this.checkGroupCommodityIdList = checkGroupCommodityIdList;
	}

	public List<Long> getCommodityIdListAll() {
		return commodityIdListAll;
	}

	public void setCommodityIdListAll(List<Long> commodityIdListAll) {
		this.commodityIdListAll = commodityIdListAll;
	}

	public boolean isShowZero() {
		return showZero;
	}

	public void setShowZero(boolean showZero) {
		this.showZero = showZero;
	}

	public List<Long> getCommodityIdList2() {
		return commodityIdList2;
	}

	public void setCommodityIdList2(List<Long> commodityIdList2) {
		this.commodityIdList2 = commodityIdList2;
	}

	public List<Long> getCommodityIdList3() {
		return commodityIdList3;
	}

	public void setCommodityIdList3(List<Long> commodityIdList3) {
		this.commodityIdList3 = commodityIdList3;
	}

	public Integer getLogisticsModel() {
		return logisticsModel;
	}

	public void setLogisticsModel(Integer logisticsModel) {
		this.logisticsModel = logisticsModel;
	}

	public Long getWarehouseId() {
		return warehouseId;
	}

	public void setWarehouseId(Long warehouseId) {
		this.warehouseId = warehouseId;
	}

	public String getBarCode() {
		return barCode;
	}

	public void setBarCode(String barCode) {
		this.barCode = barCode;
	}

	public Long getCommodityId() {
		return commodityId;
	}

	public void setCommodityId(Long commodityId) {
		this.commodityId = commodityId;
	}

	public boolean isWeight() {
		return isWeight;
	}

	public void setWeight(boolean weight) {
		isWeight = weight;
	}

	public boolean isNormal() {
		return isNormal;
	}

	public void setNormal(boolean normal) {
		isNormal = normal;
	}

	public boolean isNew() {
		return isNew;
	}

	public void setNew(boolean aNew) {
		isNew = aNew;
	}

	public String getStoreId() {
		return storeId;
	}

	public void setStoreId(String storeId) {
		this.storeId = storeId;
	}

	public Long getShopId() {
		return shopId;
	}

	public void setShopId(Long shopId) {
		this.shopId = shopId;
	}

	public Long getCategoryId() {
		return categoryId;
	}

	public void setCategoryId(Long categoryId) {
		this.categoryId = categoryId;
	}

	public Long getSupplerId() {
		return supplerId;
	}

	public void setSupplerId(Long supplerId) {
		this.supplerId = supplerId;
	}

	public boolean isPromotionProduct() {
		return isPromotionProduct;
	}

	public void setPromotionProduct(boolean isPromotionProduct) {
		this.isPromotionProduct = isPromotionProduct;
	}

	public String getCommodityKey() {
		return commodityKey;
	}

	public void setCommodityKey(String commodityKey) {
		this.commodityKey = commodityKey;
	}

	public Long getEnterpriseId() {
		return enterpriseId;
	}

	public void setEnterpriseId(Long enterpriseId) {
		this.enterpriseId = enterpriseId;
	}

	public String getOrderTime() {
		return orderTime;
	}

	public void setOrderTime(String orderTime) {
		this.orderTime = orderTime;
	}

	public List<Long> getCommodityIdList() {
		return commodityIdList;
	}

	public void setCommodityIdList(List<Long> commodityIdList) {
		this.commodityIdList = commodityIdList;
	}

	public List<String> getCommodityCodeList() {
		return commodityCodeList;
	}

	public void setCommodityCodeList(List<String> commodityCodeList) {
		this.commodityCodeList = commodityCodeList;
	}
}

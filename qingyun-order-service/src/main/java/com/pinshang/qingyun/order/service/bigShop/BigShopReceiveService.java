package com.pinshang.qingyun.order.service.bigShop;

import com.github.pagehelper.PageInfo;
import com.pinshang.qingyun.base.api.TokenInfo;
import com.pinshang.qingyun.base.configure.expand.QYAssert;
import com.pinshang.qingyun.base.enums.YesOrNoEnums;
import com.pinshang.qingyun.base.enums.xd.StorageAreaEnum;
import com.pinshang.qingyun.base.util.FastThreadLocalUtil;
import com.pinshang.qingyun.box.utils.BeanCloneUtils;
import com.pinshang.qingyun.box.utils.DateUtil;
import com.pinshang.qingyun.box.utils.ListToPageInfoUtil;
import com.pinshang.qingyun.box.utils.SpringUtil;
import com.pinshang.qingyun.common.dto.DelayMsgIDTO;
import com.pinshang.qingyun.common.service.CodeClient;
import com.pinshang.qingyun.common.service.DelayMsgClient;
import com.pinshang.qingyun.order.dto.OrderInfoODTO;
import com.pinshang.qingyun.order.dto.bigShop.*;
import com.pinshang.qingyun.order.enums.DeliveryBatchTypeEnum;
import com.pinshang.qingyun.order.enums.ReceiveOrderStatusEnums;
import com.pinshang.qingyun.order.mapper.CommodityMapper;
import com.pinshang.qingyun.order.mapper.OrderMapper;
import com.pinshang.qingyun.order.mapper.ShopMapper;
import com.pinshang.qingyun.order.mapper.ShopReceiveOrderMapper;
import com.pinshang.qingyun.order.mapper.bigShop.*;
import com.pinshang.qingyun.order.mapper.entry.commodity.CommodityBasicEntry;
import com.pinshang.qingyun.order.mapper.entry.order.CommodityInfoEntry;
import com.pinshang.qingyun.order.model.bigShop.*;
import com.pinshang.qingyun.order.model.commodity.Commodity;
import com.pinshang.qingyun.order.model.order.SaleReturnOrderPic;
import com.pinshang.qingyun.order.model.shop.Shop;
import com.pinshang.qingyun.order.model.shop.ShopReceiveOrder;
import com.pinshang.qingyun.order.service.CommonService;
import com.pinshang.qingyun.order.service.ConsigneeCommonService;
import com.pinshang.qingyun.order.service.ConsignmentSupplierService;
import com.pinshang.qingyun.order.service.ShopService;
import com.pinshang.qingyun.order.vo.commodity.CommodityVO;
import com.pinshang.qingyun.order.vo.order.SaleReturnOrderPicVO;
import com.pinshang.qingyun.renderer.client.dto.DictionaryODTO;
import com.pinshang.qingyun.xd.wms.dto.StockReceiptIDTO;
import com.pinshang.qingyun.xd.wms.dto.StockReceiptItemDTO;
import com.pinshang.qingyun.xd.wms.dto.bigShop.DdStockInOutExtraIDTO;
import com.pinshang.qingyun.xd.wms.dto.bigShop.GoodsAllocationODTO;
import com.pinshang.qingyun.xd.wms.service.XdStockClient;
import com.pinshang.qinyun.cache.enums.RedisDelayQueueEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.ApplicationContext;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.entity.Example;

import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @Author: sk
 * @Date: 2024/10/11
 */
@Slf4j
@Service
public class BigShopReceiveService {

    @Autowired
    private DdReceiveDocMapper ddReceiveDocMapper;
    @Autowired
    private DdReceiveDocLogMapper ddReceiveDocLogMapper;
    @Autowired
    private DdReceiveDocOrderMapper ddReceiveDocOrderMapper;
    @Autowired
    private DdReceiveDocRecordMapper ddReceiveDocRecordMapper;
    @Autowired
    private DdReceiveDocPicMapper ddReceiveDocPicMapper;
    @Autowired
    private RedissonClient redissonClient;
    @Autowired
    private ShopService shopService;
    @Autowired
    private CodeClient codeClient;
    @Autowired
    private ConsignmentSupplierService consignmentSupplierService;
    @Autowired
    private ShopMapper shopMapper;
    @Autowired
    private CommodityMapper commodityMapper;
    @Autowired
    private CommonService commonService;
    @Autowired
    private ShopReceiveOrderMapper shopReceiveOrderMapper;
    @Autowired
    private XdStockClient xdStockClient;
    @Autowired
    private ConsigneeCommonService consigneeCommonService;
    @Autowired
    private ApplicationContext applicationContext;
    @Autowired
    private DelayMsgClient delayMsgClient;
    @Autowired
    private OrderMapper orderMapper;
    @Value("${pinshang.img-server-url}")
    private String imgServerUrl;

    /**
     * 新增订单实时维护大店收货单
     * @param orderTime
     * @param shopId
     */
    @Async
    public void createReceiveDoc(String orderTime, Long storeId, Long stallId, Integer deliveryBatch ) {
        log.info("大店创建收货单,  orderTime {}  storeId {}  stallId {}  deliveryBatch {}", orderTime, storeId, stallId, deliveryBatch);

        if(orderTime == null || storeId == null || stallId == null || deliveryBatch == null){
            return;
        }

        String lockKey = ( storeId + orderTime + stallId );
        RLock lock = redissonClient.getLock("order:createReceiveDoc:" + lockKey);
        lock.lock(2L, TimeUnit.SECONDS);

        try{
            BigShopReceiveService bigShopReceiveService = applicationContext.getBean(BigShopReceiveService.class);
            bigShopReceiveService.saveReceiveDoc(orderTime, storeId, stallId, deliveryBatch);
        }finally{
            lock.unlock(); // 释放锁
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public void saveReceiveDoc(String orderTime, Long storeId, Long stallId, Integer deliveryBatch) {
        Shop shop = shopService.getShopByStoreId(storeId);
        Long shopId = shop.getId();

        // 配送批次 1=1配 + 补货  2=2配  8=新开店
        // 补货和1配批次放一起
        if(DeliveryBatchTypeEnum.REPLENISHMENT.getCode().equals(deliveryBatch)){
            deliveryBatch = DeliveryBatchTypeEnum.ONE_BATCH.getCode();
        }

        Example ddReceiveDocEx = new Example(DdReceiveDoc.class);
        ddReceiveDocEx.createCriteria().andEqualTo("orderTime", orderTime)
                .andEqualTo("shopId", shopId)
                .andEqualTo("stallId", stallId)
                .andEqualTo("deliveryBatch", deliveryBatch)
                .andEqualTo("docStatus", YesOrNoEnums.NO.getCode());
        List<DdReceiveDoc> ddReceiveDocList = ddReceiveDocMapper.selectByExample(ddReceiveDocEx);
        if(CollectionUtils.isEmpty(ddReceiveDocList)){
            String code = codeClient.createCode("BIG_SHOP_RECEIVE_DOC_CODE");
            String yyyyMMdd = DateUtil.getDateFormate(DateUtil.parseDate(orderTime, "yyyy-MM-dd"), "yyyyMMdd");
            String docCode = code.substring(0, 4) + yyyyMMdd + code.substring(4, code.length());

            Date nowTime = new Date();
            DdReceiveDoc ddReceiveDoc = new DdReceiveDoc();
            ddReceiveDoc.setDocCode(docCode);
            ddReceiveDoc.setOrderTime(DateUtil.parseDate(orderTime, "yyyy-MM-dd"));
            ddReceiveDoc.setShopId(shopId);
            ddReceiveDoc.setStallId(stallId);
            ddReceiveDoc.setDeliveryBatch(deliveryBatch);
            ddReceiveDoc.setDocStatus(YesOrNoEnums.NO.getCode());
            ddReceiveDoc.setCreateId(-1L);
            ddReceiveDoc.setCreateTime(nowTime);
            ddReceiveDoc.setUpdateId(-1L);
            ddReceiveDoc.setUpdateTime(nowTime);
            ddReceiveDocMapper.insert(ddReceiveDoc);
        }
    }

    /**
     * 查询收货单列表
     * @param idto
     * @return
     */
    public PageInfo<DdReceiveDocODTO> getReceiveDocList(DdReceiveDocPageIDTO idto) {
        PageInfo<DdReceiveDocODTO> pageDate = new PageInfo<>() ;

        QYAssert.isTrue(idto.getShopId() != null, "门店id不能为空");
        QYAssert.isTrue(StringUtils.isNotBlank(idto.getOrderTime()), "送货日期不能为空");

        List<Long> stallIdList = consignmentSupplierService.selectUserStallIdList(idto.getShopId());
        if(CollectionUtils.isEmpty(stallIdList)){
            return new PageInfo<>();
        }else {
            idto.setStallIdList(stallIdList);
        }

        // 订单号不为空
        if(StringUtils.isNotBlank(idto.getOrderCode())) {

            OrderInfoODTO order = orderMapper.queryOrderByOrderCode(idto.getOrderCode());
            if(order != null) {
                List<Shop> shopList = shopService.getShopByStoreIdList(Collections.singletonList(order.getStoreId()));
                if(CollectionUtils.isNotEmpty(shopList)) {
                    Long relationShopId = shopList.get(0).getId();
                    String relationOrderTime = DateUtil.getDateFormate(order.getOrderTime(), "yyyy-MM-dd");
                    Long relationStallId = order.getStallId();
                    Integer relationDeliveryBatch = (DeliveryBatchTypeEnum.REPLENISHMENT.getCode().equals(order.getDeliveryBatch())
                                  ? DeliveryBatchTypeEnum.ONE_BATCH.getCode() : order.getDeliveryBatch() );

                    idto.setRelationShopId(relationShopId);
                    idto.setRelationOrderTime(relationOrderTime);
                    idto.setRelationStallId(relationStallId);
                    idto.setRelationDeliveryBatch(relationDeliveryBatch);
                }else {
                    return new PageInfo<>();
                }
            }else {
                return new PageInfo<>();
            }
        }

        // 查询全部，内存分页
        List<DdReceiveDocODTO> list = ddReceiveDocMapper.getReceiveDocList(idto);

        if (SpringUtil.isNotEmpty(list)) {
            List<Long> stallIds = list.stream().filter(p -> p.getStallId() != null && p.getStallId() > 0).collect(Collectors.toList())
                    .stream().map(item -> item.getStallId()).collect(Collectors.toList());
            Map<Long, String> stallIdAndNameMap = consignmentSupplierService.queryStallMapByIds(stallIds);

             // 根据门店送货日期查询关联的订单数(未收货的)
            Map<String, Integer> waitOrderCountMap = new HashMap<>(20);

            // 关联订单数(已经收货的)
            Map<String, Integer> orderCountMap = new HashMap<>(20);
            List<DdReceiveDocODTO> waitReceiveList = list.stream().filter(p -> YesOrNoEnums.NO.getCode().equals(p.getDocStatus())).collect(Collectors.toList());
            List<Long> docIdList = list.stream().filter(p -> YesOrNoEnums.YES.getCode().equals(p.getDocStatus())).map(item -> Long.valueOf(item.getDocId())).collect(Collectors.toList());

            if(CollectionUtils.isNotEmpty(waitReceiveList)){
                waitOrderCountMap = getWaitReceiveOrderCountMap(idto.getShopId(), idto.getOrderTime());
            }

            if(CollectionUtils.isNotEmpty(docIdList)){
                orderCountMap = getReceiveOrderCountMap(docIdList);
            }

            for(DdReceiveDocODTO docODTO :list){
                docODTO.setStallName(stallIdAndNameMap.get(docODTO.getStallId()));

                if(YesOrNoEnums.NO.getCode().equals(docODTO.getDocStatus())){
                    if(DeliveryBatchTypeEnum.ONE_BATCH.getCode().equals(docODTO.getDeliveryBatch())){
                        String oneBatchKey = docODTO.getStallId() + "_" + DeliveryBatchTypeEnum.ONE_BATCH.getCode();
                        String replenishmentKey = docODTO.getStallId() + "_" + DeliveryBatchTypeEnum.REPLENISHMENT.getCode();
                        Integer oneBatchcount = (waitOrderCountMap.get(oneBatchKey) != null ? waitOrderCountMap.get(oneBatchKey) : 0);
                        Integer replenishmentCount = (waitOrderCountMap.get(replenishmentKey) != null ? waitOrderCountMap.get(replenishmentKey) : 0);
                        docODTO.setOrderCodeCount(oneBatchcount + replenishmentCount);
                    }else {
                        String key = docODTO.getStallId() + "_" + docODTO.getDeliveryBatch();
                        docODTO.setOrderCodeCount(waitOrderCountMap.get(key));
                    }
                }else {
                    docODTO.setOrderCodeCount(orderCountMap.get(docODTO.getDocId()));
                }
            }

            // 关联订单数为0的过滤掉
            list = list.stream().filter(p -> p.getOrderCodeCount() != null && p.getOrderCodeCount() > 0).collect(Collectors.toList());

            pageDate = ListToPageInfoUtil.convert(list, idto.getPageSize(), idto.getPageNo());
        }
        return pageDate;
    }

    /**
     * 获取关联订单号(未收货)
     * @param shopId
     * @param orderTime
     * @return
     */
    private Map<String, Integer> getWaitReceiveOrderCountMap(Long shopId, String orderTime){
        Map<String, Integer> orderCountMap = new HashMap<>(20);
        List<DdReceiveOrderCountODTO> orderCountList = ddReceiveDocMapper.getOrderCount(shopId, orderTime);
        if(CollectionUtils.isNotEmpty(orderCountList)){
            orderCountList.forEach(item -> {
                 String key = item.getStallId() + "_" + item.getDeliveryBatch();
                orderCountMap.put(key, item.getOrderCodeCount());
            });
        }

        return orderCountMap;
    }

    /**
     * 获取关联订单号(已经收货)
     * @param shopId
     * @param orderTime
     * @return
     */
    private Map<String, Integer> getReceiveOrderCountMap(List<Long> docIdList){
        Map<String, Integer> orderCountMap = new HashMap<>(docIdList.size());
        List<DdReceiveOrderCountODTO> orderCountList = ddReceiveDocMapper.getReceiveOrderCount(docIdList);
        if(CollectionUtils.isNotEmpty(orderCountList)){
            orderCountList.forEach(item -> {
                orderCountMap.put(item.getDocId() + "", item.getOrderCodeCount());
            });
        }

        return orderCountMap;
    }

    /**
     * 收货详情、去收货页面
     *
     * @param docId
     * @param commodityBarCode
     * @param isQueryPendingItems
     * @return
     */
    public DdReceiveDocODTO getReceiveDocCommodityList(Long docId, String commodityBarCode, Boolean isQueryPendingItems) {
        DdReceiveDoc ddReceiveDoc = ddReceiveDocMapper.selectByPrimaryKey(docId);
        QYAssert.isTrue(ddReceiveDoc != null, "单据不存在");

        DdReceiveDocODTO receiveDocResult = BeanCloneUtils.copyTo(ddReceiveDoc, DdReceiveDocODTO.class);

        // 档口信息
        Map<Long, String> stallIdAndNameMap = consignmentSupplierService.queryStallMapByIds(Collections.singletonList(ddReceiveDoc.getStallId()));
        receiveDocResult.setStallName(stallIdAndNameMap.get(ddReceiveDoc.getStallId()));

        Shop shop = shopMapper.selectByPrimaryKey(ddReceiveDoc.getShopId());
        receiveDocResult.setShopName(shop.getShopName());

        List<DdReceiveDocCommodityODTO> commodityList = new ArrayList<>();
        Map<Long, List<DdReceiveDocPic>> ddReceiveDocPicMap = new HashMap<>();

        int confirmedCommodityCount = 0;
        int unConfirmedCommodityCount = 0;
        List<DdReceiveDocCommodityODTO> unConfirmedCommodityList = new ArrayList<>();
        List<Commodity> barCodeCommodityList = new ArrayList<>();

        // 未收货实时查询订单商品信息
        if(YesOrNoEnums.NO.getCode().equals(ddReceiveDoc.getDocStatus())){
            List<Integer> deliveryBatchList = getDeliveryBatchList(ddReceiveDoc.getDeliveryBatch());

            Example example = new Example(DdReceiveDocRecord.class);
            example.createCriteria().andEqualTo("docId", docId);
            List<DdReceiveDocRecord> ddReceiveDocRecords = ddReceiveDocRecordMapper.selectByExample(example);
            confirmedCommodityCount = CollectionUtils.isNotEmpty(ddReceiveDocRecords) ? ddReceiveDocRecords.size() : 0;
            List<Long> confirmedCommodityIdList = ddReceiveDocRecords.stream()
                    .map(DdReceiveDocRecord::getCommodityId)
                    .collect(Collectors.toList());

            // 查询所有可收货商品
            commodityList = ddReceiveDocMapper.querySumReceiveOrderCommodityList(
                    ddReceiveDoc.getShopId(),
                    ddReceiveDoc.getStallId(),
                    DateUtil.getDateFormate(ddReceiveDoc.getOrderTime(), "yyyy-MM-dd"),
                    deliveryBatchList
            );

            if (CollectionUtils.isNotEmpty(commodityList)) {
                // 获取图片信息
                ddReceiveDocPicMap = getDdReceiveDocPicMap(docId, DdReceiveDocPic.ReceiveTypeEnums.PDA.getCode());

                unConfirmedCommodityList = commodityList
                        .stream()
                        .filter(x -> !confirmedCommodityIdList.contains(Long.valueOf(x.getCommodityId())))
                        .collect(Collectors.toList());
                // 设置待收货数量
                unConfirmedCommodityCount = unConfirmedCommodityList.size();
            }

            // 根据条形码过滤商品
            if (StringUtils.isNotBlank(commodityBarCode)) {
                barCodeCommodityList = commodityMapper.findCommodityBarCodeByParam(null, commodityBarCode);
                // 如果获取的条形码商品列表不为空，进行过滤
                if (CollectionUtils.isNotEmpty(barCodeCommodityList)) {
                    List<Long> barcodeCommodityIds = barCodeCommodityList.stream()
                            .map(Commodity::getId)
                            .collect(Collectors.toList());

                    // 过滤出商品ID在条形码商品列表中的商品
                    commodityList = commodityList.stream()
                            .filter(item -> barcodeCommodityIds.contains(Long.valueOf(item.getCommodityId())))
                            .collect(Collectors.toList());
                } else {
                    // 如果没有找到符合条形码的商品，可以设置为空或其他处理逻辑
                    commodityList.clear();
                }

            }

            // 移除已确认商品
            if (BooleanUtils.isTrue(isQueryPendingItems)) {
                commodityList.removeIf(item -> confirmedCommodityIdList.contains(Long.valueOf(item.getCommodityId())));
            }

        }else {
            // 已经收货就直接查询收获记录表
            commodityList = ddReceiveDocMapper.queryReceiveCommodityLogList(docId);
            ddReceiveDocPicMap = getDdReceiveDocPicMap(docId, DdReceiveDocPic.ReceiveTypeEnums.PC.getCode());
        }

        if(CollectionUtils.isNotEmpty(commodityList)){
            // 查询pda收货记录
            Map<Long, DdReceiveDocRecord> ddReceiveDocRecordMap = getDdReceiveDocRecordMap(docId);

            // 如果pda已经收货，则设置pda收货.并且设置pda收货人、收货时间、实收数量、库区、货位、收货图片
            setPdaReceiveInfo(commodityList, ddReceiveDocRecordMap, ddReceiveDocPicMap);

            List<Long> commodityIdList = commodityList.stream().map(item -> Long.valueOf(item.getCommodityId())).collect(Collectors.toList());

            // 查询商品基础信息
            CommodityVO vo = new CommodityVO();
            vo.setCommodityIdList(commodityIdList);
            List<CommodityBasicEntry> commBasicList = commodityMapper.findCommodityBasicListByParam(vo);
            Map<String, CommodityBasicEntry> commBasicMap = commBasicList.stream().collect(Collectors.toMap(CommodityBasicEntry::getCommodityId, Function.identity()));
            List<CommodityBasicEntry> commBarCodeList = commodityMapper.findCommodityBarCodeByIds(commodityIdList);
            Map<String, String> barCodeMap = commBarCodeList.stream().collect(Collectors.toMap(CommodityBasicEntry::getCommodityId,CommodityBasicEntry::getBarCodes,(key1 , key2)-> key2));
            Set<Long> dictionaryIdSet = new HashSet<>();
            for (CommodityBasicEntry commodityBasicEntry : commBasicList) {
                dictionaryIdSet.add(commodityBasicEntry.getCommodityUnitId());
            }
            Map<String, DictionaryODTO> dictionaryMap = commonService.listDictionarysByIdSet(dictionaryIdSet);

            // 查询货位信息
            List<Long> goodsAllocationIdList = commodityList.stream().filter(p -> p.getGoodsAllocationId() != null).collect(Collectors.toList())
                    .stream().map(item -> item.getGoodsAllocationId()).collect(Collectors.toList());
            Map<Long, GoodsAllocationODTO> goodsAllocationMap = consignmentSupplierService.queryGoodsAllocationMapByIds(goodsAllocationIdList);

            for(DdReceiveDocCommodityODTO odto : commodityList){
                CommodityBasicEntry commodityBasic = commBasicMap.get(odto.getCommodityId());
                if(commodityBasic != null){
                    BeanUtils.copyProperties(commodityBasic,odto);
                }
                odto.setUnit(null != dictionaryMap.get(odto.getCommodityUnitId() + "" ) ? dictionaryMap.get(odto.getCommodityUnitId() + "").getOptionName() : "");
                odto.setBarCodes(barCodeMap.get(odto.getCommodityId()));

                if(org.apache.commons.lang3.StringUtils.isNotBlank(odto.getBarCodes())){
                    String[] barCode = odto.getBarCodes().trim().split(",");
                    odto.setBarCode(barCode[0]);
                    List barCodeList = java.util.Arrays.asList(barCode);
                    odto.setBarCodeList(barCodeList);
                }

                odto.setGoodsAllocationCode(goodsAllocationMap.get(odto.getGoodsAllocationId()) != null ? goodsAllocationMap.get(odto.getGoodsAllocationId()).getGoodsAllocationCode() : "");
            }
        }
        receiveDocResult.setCommodityList(commodityList);
        receiveDocResult.setUnConfirmedCommodityCount(unConfirmedCommodityCount);
        receiveDocResult.setConfirmedCommodityCount(confirmedCommodityCount);
        if (CollectionUtils.size(unConfirmedCommodityList) == 1 && CollectionUtils.isNotEmpty(barCodeCommodityList)) {
            DdReceiveDocCommodityODTO odto = unConfirmedCommodityList.get(0);
            Commodity commodity = barCodeCommodityList.get(0);
            if (Objects.equals(Long.valueOf(odto.getCommodityId()), commodity.getId())) {
                receiveDocResult.setLastCommodityFlag(true);
            }
        }

        return receiveDocResult;
    }

    /**
     * 如果pda已经收货，则设置pda收货.并且设置pda收货人、收货时间、实收数量、库区、货位、收货图片
     */
    private void setPdaReceiveInfo(List<DdReceiveDocCommodityODTO> commodityList, Map<Long, DdReceiveDocRecord> ddReceiveDocRecordMap, Map<Long, List<DdReceiveDocPic>> ddReceiveDocPicMap) {
        for(DdReceiveDocCommodityODTO item : commodityList){
            Long commodityId = Long.valueOf(item.getCommodityId());
            // 如果存在pda收货记录
            if(ddReceiveDocRecordMap.containsKey(commodityId)) {
                DdReceiveDocRecord ddReceiveDocRecord = ddReceiveDocRecordMap.get(commodityId);
                item.setPdaStatus(YesOrNoEnums.YES.getCode());
                item.setReceiveUserId(ddReceiveDocRecord.getCreateId());
                item.setReceiveTime(DateUtil.getDateFormate(ddReceiveDocRecord.getCreateTime(), "yyyy-MM-dd HH:mm:ss"));
                item.setStorageArea(ddReceiveDocRecord.getStorageArea());
                item.setGoodsAllocationId(ddReceiveDocRecord.getGoodsAllocationId());
                item.setRemark(ddReceiveDocRecord.getRemark());
                item.setRealReceiveQuantity(ddReceiveDocRecord.getRealReceiveQuantity());

            }

            // 如果存在pda收货设置收货图片
            if(ddReceiveDocPicMap.containsKey(commodityId)){
                List<DdReceiveDocPic> ddReceiveDocPicList = ddReceiveDocPicMap.get(commodityId);

                List<DdReceiveDocPic> picList = ddReceiveDocPicList.stream().filter(picItem -> {return SaleReturnOrderPic.PicTypeEnums.PIC.getCode().equals(picItem.getPicType());}).collect(Collectors.toList());
                List<DdReceiveDocPic> videoList = ddReceiveDocPicList.stream().filter(picItem -> {return SaleReturnOrderPic.PicTypeEnums.VIDEO.getCode().equals(picItem.getPicType());}).distinct().collect(Collectors.toList());

                if(CollectionUtils.isNotEmpty(picList)) {
                    List<SaleReturnOrderPicVO> saleReturnPicList = new ArrayList<>();
                    for(DdReceiveDocPic picItem : picList){
                        SaleReturnOrderPicVO picVO = new SaleReturnOrderPicVO(picItem.getPicUrl(), imgServerUrl + picItem.getPicUrl());
                        saleReturnPicList.add(picVO);
                    }
                    item.setPicList(saleReturnPicList);
                }

                if(CollectionUtils.isNotEmpty(videoList)) {
                    List<SaleReturnOrderPicVO> saleReturnVideoList = new ArrayList<>();
                    for(DdReceiveDocPic videoItem : videoList){
                        SaleReturnOrderPicVO videoVO = new SaleReturnOrderPicVO(videoItem.getPicUrl(), imgServerUrl + videoItem.getPicUrl());
                        saleReturnVideoList.add(videoVO);
                    }
                    item.setVideoList(saleReturnVideoList);
                }
            }
        }
    }

    /**
     * 查询pda收货记录
     * @param docId
     * @return
     */
    private Map<Long, DdReceiveDocRecord> getDdReceiveDocRecordMap(Long docId) {
        Map<Long, DdReceiveDocRecord> ddReceiveDocRecordMap = new HashMap<>();
        DdReceiveDocRecord ddReceiveDocRecord = new DdReceiveDocRecord();
        ddReceiveDocRecord.setDocId(docId);
        List<DdReceiveDocRecord> recordList = ddReceiveDocRecordMapper.select(ddReceiveDocRecord);
        if(CollectionUtils.isNotEmpty(recordList)) {
            ddReceiveDocRecordMap = recordList.stream().collect(Collectors.toMap(item -> item.getCommodityId(), Function.identity()));
        }
        return ddReceiveDocRecordMap;
    }

    /**
     * 获取PDA收货图片
     * @param docId
     * @return
     */
    private Map<Long, List<DdReceiveDocPic>> getDdReceiveDocPicMap(Long docId, Integer receiveType) {
        Map<Long, List<DdReceiveDocPic>> ddReceiveDocPicMap = new HashMap<>();
        DdReceiveDocPic ddReceiveDocPic = new DdReceiveDocPic();
        ddReceiveDocPic.setDocId(docId);
        ddReceiveDocPic.setReceiveType(receiveType);
        List<DdReceiveDocPic> picList = ddReceiveDocPicMapper.select(ddReceiveDocPic);
        if(CollectionUtils.isNotEmpty(picList)) {
            ddReceiveDocPicMap = picList.stream().collect(Collectors.groupingBy(DdReceiveDocPic::getCommodityId));
        }
        return ddReceiveDocPicMap;
    }

    /**
     * 打印收货清单
     * @param docId
     * @return
     */
    public DdReceiveDocODTO printReceiveDocCommodityList(Long docId) {
        TokenInfo tokenInfo = FastThreadLocalUtil.getQY();
        DdReceiveDocODTO receiveDocResult = getReceiveDocCommodityList(docId,null, null);
        receiveDocResult.setPrintTime(DateUtil.getDateFormate(new Date(), "yyyy-MM-dd HH:mm:ss"));
        receiveDocResult.setPrintUserName(tokenInfo.getRealName());
        return  receiveDocResult;
    }

    /**
     * 关联订单号
     * @param docId
     * @return
     */
    public List<DdReceiveDocOrderODTO> receiveOrderList(Long docId) {
        DdReceiveDoc ddReceiveDoc = ddReceiveDocMapper.selectByPrimaryKey(docId);
        QYAssert.isTrue(ddReceiveDoc != null, "单据不存在");

        List<DdReceiveDocOrderODTO> orderList = new ArrayList<>();
        // 未收货实时查询关联的订单号
        if(YesOrNoEnums.NO.getCode().equals(ddReceiveDoc.getDocStatus())){
            // 未收货实时查询关联订单号
            List<Integer> deliveryBatchList = getDeliveryBatchList(ddReceiveDoc.getDeliveryBatch());

            orderList = ddReceiveDocMapper.queryWaitReceiveRelationOrderList(ddReceiveDoc.getShopId(), ddReceiveDoc.getStallId(),
                    DateUtil.getDateFormate(ddReceiveDoc.getOrderTime(), "yyyy-MM-dd"), deliveryBatchList);
        }else {

            // 已经收货查询关联订单记录表
            orderList = ddReceiveDocMapper.queryRelationOrderListLog(ddReceiveDoc.getId());
        }
        return orderList;
    }

    /**
     * 获取配送批次，收货单里面的1配对应订单里面的1配 + 补货
     * @param deliveryBatch
     * @return
     */
    public   List<Integer> getDeliveryBatchList(Integer deliveryBatch) {
        List<Integer> deliveryBatchList = new ArrayList<>();
        if(DeliveryBatchTypeEnum.ONE_BATCH.getCode().equals(deliveryBatch)){
            deliveryBatchList.add(DeliveryBatchTypeEnum.ONE_BATCH.getCode());
            deliveryBatchList.add(DeliveryBatchTypeEnum.REPLENISHMENT.getCode());
        }else {
            deliveryBatchList.add(deliveryBatch);
        }
        return deliveryBatchList;
    }


    /**
     * 收货操作(大店)
     * @param idto
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public Boolean addBigShopReceive(DdReceiveDocSaveIDTO idto) {
        Long userId = idto.getUserId();
        Date nowTime = new Date();

        DdReceiveDoc ddReceiveDoc = ddReceiveDocMapper.selectByPrimaryKey(idto.getDocId());
        QYAssert.isTrue(ddReceiveDoc != null, "单据不存在");
        QYAssert.isTrue(YesOrNoEnums.NO.getCode().equals(ddReceiveDoc.getDocStatus()), "单据已完成收货");

        // 入参校验
        receiveParamCheck(idto);

        // 判断实收数量不能大于实发数量
        List<Integer> deliveryBatchList = getDeliveryBatchList(ddReceiveDoc.getDeliveryBatch());
        // 查询订单商品订货数量、实发数量汇总
        Map<String, DdReceiveDocCommodityODTO> realDeliveryQuantityMap = getOrderSumDeliveryQuantityMap(ddReceiveDoc, deliveryBatchList);

        Set<Long> subOrderIdList = new HashSet<>();
        // 大店收货单日志表
        List<DdReceiveDocLog> receiveDocLogList = new ArrayList<>();
        // 大店收货图片表
        List<DdReceiveDocPic> receiveDocPicList = new ArrayList<>();
        // 大店收货订单记录表
        List<DdReceiveDocOrder> receiveDocOrderList = new ArrayList<>();
        // 记录订单号重复
        List<String> orderCodeList = new ArrayList<>();

        List<StockReceiptItemDTO> stockReceiptList = new ArrayList<>();
        Map<Long, BigDecimal> commodityPriceMap = new HashMap<>(idto.getCommodityList().size());

        // 查询商品基础信息
        List<Long> commodityIdList = idto.getCommodityList().stream().map(item -> Long.valueOf(item.getCommodityId())).collect(Collectors.toList());
        CommodityVO vo = new CommodityVO();
        vo.setCommodityIdList(commodityIdList);
        List<CommodityBasicEntry> commodityBasicList = commodityMapper.findCommodityBasicListByParam(vo);
        Map<String, CommodityBasicEntry> commodityBasicMap = commodityBasicList.stream().collect(Collectors.toMap(CommodityBasicEntry::getCommodityId, Function.identity()));

        List<DdReceiveUpdateSubOrderCommodityQuantityDTO> ddReceiveUpdateSubOrderCommodityQuantityDTOList = Lists.newArrayList();
        for(DdReceiveDocCommodityODTO odto : idto.getCommodityList()){
            DdReceiveDocCommodityODTO receiveOrderCommodity = null;
            if(realDeliveryQuantityMap.containsKey(odto.getCommodityId())){
                receiveOrderCommodity = realDeliveryQuantityMap.get(odto.getCommodityId());
                QYAssert.notNull(receiveOrderCommodity.getRealDeliveryQuantity(), odto.getCommodityName() + " 大仓未发货，请联系大仓发货完成再收货");

                QYAssert.isTrue(odto.getRealReceiveQuantity().compareTo(receiveOrderCommodity.getRealDeliveryQuantity()) <= 0, odto.getCommodityName() + " 实收数量不能大于实发数量");
            }else {
                QYAssert.isFalse(odto.getCommodityName() + " 大仓未发货，请联系大仓发货完成再收货");
            }

            // 单商品本次收货数量
            BigDecimal receiveTotalQuantity = odto.getRealReceiveQuantity();

            // 开始收货,大店下单是过滤配比、配货、赠品
            // 收货单收货完成，实收数量要回写关联订单的实收数量，同时更新订单的收货状态。
            //若一个商品存在多个订单，且存在正常订货，配货，赠品三种情况时，需先回写正常订货数，后配货，最后再赠品的逻辑
            //若正常订货的商品出现在多个订单，则优先满足订货数量少的订单，再满足订货数量多的订单；若订货数一样，优先按照订单号小的先回写
            List<DdReceiveOrderCommodityODTO> orderCommodityList = ddReceiveDocMapper.getOrderCommodityInfo(ddReceiveDoc.getShopId(),
                    ddReceiveDoc.getStallId(),DateUtil.getDateFormate(ddReceiveDoc.getOrderTime(), "yyyy-MM-dd"),
                    deliveryBatchList,Long.valueOf(odto.getCommodityId()));

            for(DdReceiveOrderCommodityODTO comm : orderCommodityList){

                // 把实收数量分摊到每个订单商品里面
                if(receiveTotalQuantity.compareTo(BigDecimal.ZERO) > 0){
                    if(receiveTotalQuantity.compareTo(comm.getRealDeliveryQuantity()) > 0){
                        ddReceiveUpdateSubOrderCommodityQuantityDTOList.add(new DdReceiveUpdateSubOrderCommodityQuantityDTO(comm.getRealDeliveryQuantity(), comm.getSubOrderItemId(), comm.getCommodityId()));
                    }else {
                        ddReceiveUpdateSubOrderCommodityQuantityDTOList.add(new DdReceiveUpdateSubOrderCommodityQuantityDTO(receiveTotalQuantity, comm.getSubOrderItemId(), comm.getCommodityId()));
                    }
                    receiveTotalQuantity = receiveTotalQuantity.subtract(comm.getRealDeliveryQuantity());
                }else {
                    ddReceiveUpdateSubOrderCommodityQuantityDTOList.add(new DdReceiveUpdateSubOrderCommodityQuantityDTO(BigDecimal.ZERO, comm.getSubOrderItemId(), comm.getCommodityId()));
                }
                subOrderIdList.add(comm.getSubOrderId());

                // 设置关联订单号
                if(!orderCodeList.contains(comm.getOrderCode())){
                    receiveDocOrderList.add(getReceiveDocOrderList(userId, nowTime, ddReceiveDoc, comm.getOrderCode(), comm.getLogisticsModel(), comm.getOrderAmount()));
                }
                orderCodeList.add(comm.getOrderCode());

                commodityPriceMap.put(comm.getCommodityId(), comm.getPrice());
            }

            // 设置日志
            receiveDocLogList.add(getDdReceiveDocLog(userId, nowTime, ddReceiveDoc, odto, receiveOrderCommodity.getQuantity(), receiveOrderCommodity.getRealDeliveryQuantity()));

            // 调用入库组装
            stockReceiptList.add(getStockReceipt(odto, ddReceiveDoc, commodityPriceMap, commodityBasicMap.get(odto.getCommodityId()).getCommodityPackageSpec()));

            // 保存收货单图片
            receiveDocPicList.addAll(getDdReceiveDocPic(userId, nowTime, ddReceiveDoc.getId(), odto));
        }

        // 批量更新实收数量
        if(CollectionUtils.isNotEmpty(ddReceiveUpdateSubOrderCommodityQuantityDTOList)){
            ddReceiveDocMapper.batchUpdateRealReceiveQuantity(ddReceiveUpdateSubOrderCommodityQuantityDTOList);
        }

        // 将收货单置为已收货
        setShopReceiveOrderDone(subOrderIdList, userId, nowTime);

        // 更新主单为已经收货
        setDdReceiveDocDone(ddReceiveDoc, userId, nowTime);

        // 大店收货单日志表
        if(CollectionUtils.isNotEmpty(receiveDocLogList)){
            ddReceiveDocLogMapper.insertList(receiveDocLogList);

            // 如果有少货，生成少货单
            List<DdReceiveDocLog> shortList = receiveDocLogList.stream().filter(p -> p.getShortReceiveQuantity() != null
                    && p.getShortReceiveQuantity().compareTo(BigDecimal.ZERO) > 0).collect(Collectors.toList());
            if(CollectionUtils.isNotEmpty(shortList)){
                String data = shortList.get(0).getDocId() + "," + idto.getEnterpriseId() + "," + idto.getUserId();
                // 如果存在少货，则放入延长队列5分钟，等待处理
                DelayMsgIDTO delayMsgIDTO = new DelayMsgIDTO(
                        RedisDelayQueueEnum.DD_RECEIVE_SHORT_RETURN.getCode(),
                        data,
                        5L,
                        TimeUnit.MINUTES);
                delayMsgClient.addDelayQueue(delayMsgIDTO);
                //consigneeCommonService.addBigShopShortReturn(shortList, commodityBasicMap, tokenInfo.getEnterpriseId(), tokenInfo.getUserId());
            }
        }

        // 大店收货订单记录表
        if(CollectionUtils.isNotEmpty(receiveDocOrderList)){
            ddReceiveDocOrderMapper.insertList(receiveDocOrderList);
        }

        // 保存大店收货图片表
        if(CollectionUtils.isNotEmpty(receiveDocPicList)) {
            ddReceiveDocPicMapper.insertList(receiveDocPicList);
        }

        // 调用入库操作
        ddStockReceipt(userId, ddReceiveDoc, stockReceiptList);
        return true;
    }

    private @NotNull Map<String, DdReceiveDocCommodityODTO> getOrderSumDeliveryQuantityMap(DdReceiveDoc ddReceiveDoc, List<Integer> deliveryBatchList) {
        List<DdReceiveDocCommodityODTO> receiveOrderCommodityList = ddReceiveDocMapper.querySumReceiveOrderCommodityList(
                    ddReceiveDoc.getShopId(), ddReceiveDoc.getStallId(),
                    DateUtil.getDateFormate(ddReceiveDoc.getOrderTime(), "yyyy-MM-dd"), deliveryBatchList);

        QYAssert.notEmpty(receiveOrderCommodityList, "商品都没有实发，不能完成收货");

        return receiveOrderCommodityList.stream().collect(Collectors.toMap(DdReceiveDocCommodityODTO::getCommodityId, Function.identity()));
    }

    /**
     * 调用入库操作
     */
    private void ddStockReceipt(Long userId, DdReceiveDoc ddReceiveDoc, List<StockReceiptItemDTO> stockReceiptList) {
        StockReceiptIDTO stockReceiptIDTO = new StockReceiptIDTO();
        stockReceiptIDTO.setUserId(userId);
        stockReceiptIDTO.setReferId(ddReceiveDoc.getId());
        stockReceiptIDTO.setReferCode(ddReceiveDoc.getDocCode());
        stockReceiptIDTO.setWarehouseId(ddReceiveDoc.getShopId());
        stockReceiptIDTO.setCommodityList(stockReceiptList);
        xdStockClient.stockReceipt(stockReceiptIDTO);
    }

    /**
     * 更新主单为已经收货
     */
    private void setDdReceiveDocDone(DdReceiveDoc ddReceiveDoc, Long userId, Date nowTime) {
        ddReceiveDoc.setReceiveUserId(userId);
        ddReceiveDoc.setReceiveTime(nowTime);
        ddReceiveDoc.setDocStatus(YesOrNoEnums.YES.getCode());
        ddReceiveDoc.setUpdateId(userId);
        ddReceiveDoc.setUpdateTime(nowTime);
        ddReceiveDocMapper.updateByPrimaryKey(ddReceiveDoc);
    }

    /**
     * 将收货单置为已收货
     */
    private void setShopReceiveOrderDone(Set<Long> subOrderIdList, Long userId, Date nowTime) {
        if(CollectionUtils.isNotEmpty(subOrderIdList)){
            ShopReceiveOrder shopReceiveOrder = new ShopReceiveOrder();
            shopReceiveOrder.setStatus(ReceiveOrderStatusEnums.PASS.getCode());
            shopReceiveOrder.setUpdateId(userId);
            shopReceiveOrder.setUpdateTime(nowTime);
            shopReceiveOrder.setReceiveId(userId);
            shopReceiveOrder.setReceiveTime(nowTime);
            Example example = new Example(ShopReceiveOrder.class);
            example.createCriteria().andIn("subOrderId", subOrderIdList);
            shopReceiveOrderMapper.updateByExampleSelective(shopReceiveOrder, example);
        }
    }

    /**
     * 收货单图片表
     */
    private List<DdReceiveDocPic> getDdReceiveDocPic(Long userId, Date nowTime, Long docId, DdReceiveDocCommodityODTO odto) {
        List<DdReceiveDocPic> picList = new ArrayList<>();
        if(CollectionUtils.isNotEmpty(odto.getPicList())) {
            picList.addAll(getPicList(userId, nowTime, docId, odto.getPicList(), Long.valueOf(odto.getCommodityId()), SaleReturnOrderPic.PicTypeEnums.PIC.getCode()));
        }

        if(CollectionUtils.isNotEmpty(odto.getVideoList())) {
            picList.addAll(getPicList(userId, nowTime, docId, odto.getVideoList(), Long.valueOf(odto.getCommodityId()), SaleReturnOrderPic.PicTypeEnums.VIDEO.getCode()));
        }
        return picList;
    }

    /**
     *收货单图片表(组装数据)
     */
    private List<DdReceiveDocPic> getPicList(Long userId, Date nowTime, Long docId,
                                            List<SaleReturnOrderPicVO> picList,
                                            Long commodityId, Integer picType) {
        List<DdReceiveDocPic> resultList = new ArrayList<>();
        picList.forEach(pic -> {
            DdReceiveDocPic ddReceiveDocPic = new DdReceiveDocPic();
            ddReceiveDocPic.setDocId(docId);
            ddReceiveDocPic.setCommodityId(commodityId);
            ddReceiveDocPic.setReceiveType(DdReceiveDocPic.ReceiveTypeEnums.PC.getCode());
            ddReceiveDocPic.setPicType(picType);
            ddReceiveDocPic.setPicUrl(pic.getPicUrl());
            ddReceiveDocPic.setCreateId(userId);
            ddReceiveDocPic.setCreateTime(nowTime);
            resultList.add(ddReceiveDocPic);
        });
        return resultList;
    }

    /**
     * 调用入库组装数据
     */
    private StockReceiptItemDTO getStockReceipt(DdReceiveDocCommodityODTO odto, DdReceiveDoc ddReceiveDoc,
                                                Map<Long,BigDecimal> commodityPriceMap,
                                                BigDecimal commodityPackageSpec) {
        StockReceiptItemDTO itemDTO = new StockReceiptItemDTO();
        itemDTO.setCommodityId(Long.valueOf(odto.getCommodityId()));
        itemDTO.setQuantity(odto.getRealReceiveQuantity());
        itemDTO.setNumber(itemDTO.getQuantity().divide(commodityPackageSpec, 0, BigDecimal.ROUND_UP).intValue());
        itemDTO.setNormalNumber(itemDTO.getNumber());
        itemDTO.setNormalQuantity(itemDTO.getQuantity());

        itemDTO.setAbnormalNumber(0);
        itemDTO.setAbnormalQuantity(BigDecimal.ZERO);
        itemDTO.setPrice(commodityPriceMap.get(Long.valueOf(odto.getCommodityId())));
        itemDTO.setTotalPrice(itemDTO.getPrice().multiply(itemDTO.getQuantity()));

        DdStockInOutExtraIDTO ddStockInOutExtraIDTO = DdStockInOutExtraIDTO.buildDdStockInOutExtraVO(Long.valueOf(odto.getCommodityId()),
                ddReceiveDoc.getStallId(), odto.getStorageArea(), odto.getGoodsAllocationId(), odto.getGoodsAllocationCode());
        itemDTO.setDdStockInOutExtraVO(ddStockInOutExtraIDTO);
        return itemDTO;
    }

    /**
     * 大店收货订单记录表
     */
    public DdReceiveDocOrder getReceiveDocOrderList(Long userId, Date nowTime,
                                        DdReceiveDoc ddReceiveDoc,
                                        String orderCode,
                                        Integer logisticsModel,
                                        BigDecimal orderAmount) {

        DdReceiveDocOrder ddReceiveDocOrder = new DdReceiveDocOrder();
        ddReceiveDocOrder.setDocId(ddReceiveDoc.getId());
        ddReceiveDocOrder.setOrderTime(ddReceiveDoc.getOrderTime());
        ddReceiveDocOrder.setShopId(ddReceiveDoc.getShopId());
        ddReceiveDocOrder.setOrderCode(orderCode);
        ddReceiveDocOrder.setLogisticsModel(logisticsModel);
        ddReceiveDocOrder.setOrderAmount(orderAmount);
        ddReceiveDocOrder.setCreateId(userId);
        ddReceiveDocOrder.setCreateTime(nowTime);
        ddReceiveDocOrder.setUpdateId(userId);
        ddReceiveDocOrder.setUpdateTime(nowTime);
        return ddReceiveDocOrder;
    }


    /**
     * 记录收货日志
     */
    private DdReceiveDocLog getDdReceiveDocLog(Long userId, Date nowTime,
                                    DdReceiveDoc ddReceiveDoc,
                                    DdReceiveDocCommodityODTO idto,
                                    BigDecimal quantity,
                                    BigDecimal realDeliveryQuantity) {

        DdReceiveDocLog ddReceiveDocLog = BeanCloneUtils.copyTo(ddReceiveDoc, DdReceiveDocLog.class);
        ddReceiveDocLog.setDocId(ddReceiveDoc.getId());

        ddReceiveDocLog.setCommodityId(Long.valueOf(idto.getCommodityId()));
        ddReceiveDocLog.setQuantity(quantity);
        ddReceiveDocLog.setRealDeliveryQuantity(realDeliveryQuantity);
        ddReceiveDocLog.setRealReceiveQuantity(idto.getRealReceiveQuantity());
        ddReceiveDocLog.setShortReceiveQuantity(ddReceiveDocLog.getRealDeliveryQuantity().subtract(ddReceiveDocLog.getRealReceiveQuantity()));
        ddReceiveDocLog.setStorageArea(idto.getStorageArea());
        ddReceiveDocLog.setGoodsAllocationId(idto.getGoodsAllocationId());
        ddReceiveDocLog.setCreateId(userId);
        ddReceiveDocLog.setUpdateId(userId);
        ddReceiveDocLog.setCreateTime(nowTime);
        ddReceiveDocLog.setUpdateTime(nowTime);
        ddReceiveDocLog.setRemark(idto.getRemark());
        return ddReceiveDocLog;
    }

    /**
     * 收货入参检验
     * @param idto
     */
    private void receiveParamCheck(DdReceiveDocSaveIDTO idto) {
        QYAssert.notEmpty(idto.getCommodityList(), "商品明细不能为空");

        for(DdReceiveDocCommodityODTO odto : idto.getCommodityList()){
            QYAssert.notNull(odto.getCommodityId(), "商品id不能为空");
            QYAssert.notNull(odto.getCommodityCode(), "商品编码不能为空");
            QYAssert.notNull(odto.getCommodityName(), "商品名称不能为空");
            QYAssert.notNull(odto.getRealReceiveQuantity(), "实收数量不能为空");
            QYAssert.notNull(odto.getStorageArea(), "收货库区不能为空");
            //QYAssert.isTrue(odto.getRealReceiveQuantity().compareTo(BigDecimal.ZERO) > 0, "实收数量必须大于0");

            if(StorageAreaEnum.PICKING_AREA.getCode().equals(odto.getStorageArea())
                    || StorageAreaEnum.WAREHOUSE_AREA.getCode().equals(odto.getStorageArea())){
                QYAssert.notNull(odto.getGoodsAllocationId(), "货位id不能为空");
                QYAssert.notNull(odto.getGoodsAllocationCode(), "货位编码不能为空");
            }

            // 如果实收数量小于实发数量，则图片不能为空
            if(odto.getRealReceiveQuantity().compareTo(odto.getRealDeliveryQuantity()) < 0) {
                QYAssert.isTrue(CollectionUtils.isNotEmpty(odto.getPicList()), odto.getCommodityName() + " 实收数量小于实发数量，必须上传图片");
                QYAssert.isTrue(odto.getPicList().size() <= 5, odto.getCommodityName() + " 图片最多上传5张");

                odto.getPicList().forEach(pic -> {
                    QYAssert.isTrue(StringUtils.isNotBlank(pic.getPicUrl()), odto.getCommodityName() + " 图片不能为空");
                });
            }

        }
    }

    /**
     * 登记收货单里单个商品
     * @param idto 登记信息
     * @return 操作结果
     */
    @Transactional(rollbackFor = Exception.class)
    public Boolean recordSingleCommodity(DdReceiveDocRecordIDTO idto) {
        checkParam(idto);

        TokenInfo tokenInfo = FastThreadLocalUtil.getQY();

        // 检查是否已经登记过
        Example recordExample = new Example(DdReceiveDocRecord.class);
        recordExample.createCriteria()
                .andEqualTo("docId", idto.getDocId())
                .andEqualTo("commodityId", idto.getCommodityId());
        DdReceiveDocRecord existRecord = ddReceiveDocRecordMapper.selectOneByExample(recordExample);

        // 存在就更新，不存在则插入
        if (existRecord != null) {
            existRecord.setDocId(idto.getDocId());
            existRecord.setCommodityId(idto.getCommodityId());
            existRecord.setRealReceiveQuantity(idto.getRealReceiveQuantity());
            existRecord.setStorageArea(idto.getStorageArea());
            existRecord.setGoodsAllocationId(idto.getGoodsAllocationId());
            existRecord.setRemark(idto.getRemark());
            existRecord.setUpdateId(tokenInfo.getUserId());
            existRecord.setUpdateTime(new Date());
            ddReceiveDocRecordMapper.updateByPrimaryKey(existRecord);

            // 删除原来登记的图片
            Example picExample = new Example(DdReceiveDocPic.class);
            picExample.createCriteria()
                    .andEqualTo("docId", idto.getDocId())
                    .andEqualTo("commodityId", idto.getCommodityId());
            ddReceiveDocPicMapper.deleteByExample(picExample);
        }else {
            // 保存登记记录
            DdReceiveDocRecord record = new DdReceiveDocRecord();
            record.setDocId(idto.getDocId());
            record.setCommodityId(idto.getCommodityId());
            record.setRealReceiveQuantity(idto.getRealReceiveQuantity());
            record.setStorageArea(idto.getStorageArea());
            record.setGoodsAllocationId(idto.getGoodsAllocationId());
            record.setRemark(idto.getRemark());
            record.setCreateId(tokenInfo.getUserId());
            record.setCreateTime(new Date());
            ddReceiveDocRecordMapper.insert(record);
        }

        List<DdReceiveDocPic> insertList = new ArrayList<>();
        // 保存配送单图片信息
        if (CollectionUtils.isNotEmpty(idto.getPicList())) {
            for (SaleReturnOrderPicVO picDto : idto.getPicList()) {
                DdReceiveDocPic pic = new DdReceiveDocPic();
                pic.setDocId(idto.getDocId());
                pic.setCommodityId(idto.getCommodityId());
                pic.setReceiveType(1);
                if (StringUtils.isNotBlank(picDto.getPicUrl())) {
                    pic.setPicUrl(picDto.getPicUrl());
                    pic.setPicType(SaleReturnOrderPic.PicTypeEnums.PIC.getCode());
                }
                pic.setCreateId(tokenInfo.getUserId());
                pic.setCreateTime(new Date());
                insertList.add(pic);
            }
        }
        if (CollectionUtils.isNotEmpty(idto.getVideoList())) {
            for (SaleReturnOrderPicVO picDto : idto.getVideoList()) {
                DdReceiveDocPic pic = new DdReceiveDocPic();
                pic.setDocId(idto.getDocId());
                pic.setCommodityId(idto.getCommodityId());
                pic.setReceiveType(1);
                if (StringUtils.isNotBlank(picDto.getPicUrl())) {
                    pic.setPicUrl(picDto.getPicUrl());
                    pic.setPicType(SaleReturnOrderPic.PicTypeEnums.VIDEO.getCode());
                }
                pic.setCreateId(tokenInfo.getUserId());
                pic.setCreateTime(new Date());
                insertList.add(pic);
            }
        }

        if (CollectionUtils.isNotEmpty(idto.getPicList())) {
            ddReceiveDocPicMapper.insertList(insertList);
        }

        log.info("单个商品登记成功，单据ID：{}，商品ID：{}，操作人：{}",
                idto.getDocId(), idto.getCommodityId(), tokenInfo.getUserId());
        return true;
    }

    /**
     * 整单收货所有未确认商品
     *
     * @param idto 整单收货信息
     * @return 操作结果
     */
    @Transactional(rollbackFor = Exception.class)
    public Boolean pdaBigShopReceive(DdReceiveDocBatchRecordIDTO idto) {
        QYAssert.notNull(idto.getDocId(), "单据ID不能为空");

        // 1. 校验单据
        DdReceiveDoc ddReceiveDoc = ddReceiveDocMapper.selectByPrimaryKey(idto.getDocId());
        QYAssert.notNull(ddReceiveDoc, "收货单不存在");
        QYAssert.isTrue(YesOrNoEnums.NO.getCode().equals(ddReceiveDoc.getDocStatus()), "收货单已完成，无法收货");

        // 2. 查询收货单商品
        List<Integer> deliveryBatchList = getDeliveryBatchList(ddReceiveDoc.getDeliveryBatch());
        List<DdReceiveDocCommodityODTO> commodityList = ddReceiveDocMapper.querySumReceiveOrderCommodityList(
                ddReceiveDoc.getShopId(), ddReceiveDoc.getStallId(),
                DateUtil.getDateFormate(ddReceiveDoc.getOrderTime(), "yyyy-MM-dd"),
                deliveryBatchList);
        QYAssert.isTrue(CollectionUtils.isNotEmpty(commodityList), "收货单中没有商品");

        // 3. 查询商品基础信息
        List<Long> commodityIds = commodityList.stream()
                .map(c -> Long.valueOf(c.getCommodityId())).distinct().collect(Collectors.toList());
        Map<Long, CommodityInfoEntry> commodityMap = commodityMapper.findCommodityInfoIds(commodityIds).stream()
                .collect(Collectors.toMap(CommodityInfoEntry::getId, Function.identity()));

        // 4. 查询已登记记录 & 图片
        List<DdReceiveDocRecord> recordedRecords = ddReceiveDocRecordMapper.selectByExample(
                new Example(DdReceiveDocRecord.class) {{
                    createCriteria().andEqualTo("docId", idto.getDocId());
                }});
        Map<Long, DdReceiveDocRecord> recordedCommodityMap = recordedRecords.stream()
                .collect(Collectors.toMap(DdReceiveDocRecord::getCommodityId, Function.identity()));

        List<DdReceiveDocPic> recordedPics = ddReceiveDocPicMapper.selectByExample(
                new Example(DdReceiveDocPic.class) {{
                    createCriteria().andEqualTo("docId", idto.getDocId());
                }});
        Map<Long, List<DdReceiveDocPic>> recordedCommodityPicMap = recordedPics.stream()
                .collect(Collectors.groupingBy(DdReceiveDocPic::getCommodityId));

        // 5. 按是否登记分组
        Map<Boolean, List<DdReceiveDocCommodityODTO>> grouped = commodityList.stream().collect(Collectors.partitioningBy(c -> recordedCommodityMap.containsKey(Long.valueOf(c.getCommodityId()))));
        List<DdReceiveDocCommodityODTO> recordedCommodities = grouped.getOrDefault(true, Collections.emptyList());
        List<DdReceiveDocCommodityODTO> unrecordedCommodities = grouped.getOrDefault(false, Collections.emptyList());

        // 6. 构建待保存 DTO
        TokenInfo tokenInfo = FastThreadLocalUtil.getQY();
        String now = DateUtil.getDateFormate(new Date(), "yyyy-MM-dd HH:mm:ss");

        List<DdReceiveDocCommodityODTO> receiveCommodityList = new ArrayList<>();
        unrecordedCommodities.forEach(c ->
                receiveCommodityList.add(buildDocCommodity(c, tokenInfo, commodityMap, now,
                        recordedCommodityMap, recordedCommodityPicMap, false)));
        recordedCommodities.forEach(c ->
                receiveCommodityList.add(buildDocCommodity(c, tokenInfo, commodityMap, now,
                        recordedCommodityMap, recordedCommodityPicMap, true)));

        // 7. 调用保存
        boolean result = false;
        if (CollectionUtils.isNotEmpty(receiveCommodityList)) {
            DdReceiveDocSaveIDTO docSaveDTO = new DdReceiveDocSaveIDTO();
            docSaveDTO.setDocId(idto.getDocId());
            docSaveDTO.setUserId(tokenInfo.getUserId());
            docSaveDTO.setEnterpriseId(tokenInfo.getEnterpriseId());
            docSaveDTO.setCommodityList(receiveCommodityList);
            result = addBigShopReceive(docSaveDTO);
        }
        return result;
    }

    /**
     * 校验实收数量
     * @param idto 登记信息
     * @param currentCommodity 当前商品信息
     * @param commodityBasic 商品基础信息
     */
    private void validateReceiveQuantity(DdReceiveDocRecordIDTO idto,
                                       DdReceiveDocCommodityODTO currentCommodity,
                                       CommodityBasicEntry commodityBasic) {
        BigDecimal realReceiveQuantity = idto.getRealReceiveQuantity();
        BigDecimal realDeliveryQuantity = currentCommodity.getRealDeliveryQuantity();
        Integer isWeight = commodityBasic.getIsWeight();

        // 校验实收数量不能为负数或零
        QYAssert.isTrue(realReceiveQuantity.compareTo(BigDecimal.ZERO) > 0, "实收数量必须大于0");

        // 校验实收数量不能高于实发数量
        QYAssert.notNull(realDeliveryQuantity, commodityBasic.getCommodityName() + " 大仓未发货，请联系大仓发货完成再收货");
        QYAssert.isTrue(realReceiveQuantity.compareTo(realDeliveryQuantity) <= 0, "实收数量不能高于实发数量");

        // 非称重品只能输入正整数，不允许输入小数
        if (isWeight == null || isWeight == 0) {
            // 检查是否为整数
            QYAssert.isTrue(realReceiveQuantity.scale() == 0 ||
                          realReceiveQuantity.remainder(BigDecimal.ONE).compareTo(BigDecimal.ZERO) == 0,
                          "非称重品只能输入正整数");
        }

        // 如果实收数量小于实发数量，必须上传配送单图片
        if (realReceiveQuantity.compareTo(realDeliveryQuantity) < 0) {
            QYAssert.isTrue(CollectionUtils.isNotEmpty(idto.getPicList()), "实收数量小于实发数量，必须上传图片");
            QYAssert.isTrue(idto.getPicList().size()<=5, "最多只能上传5张图片");
        }
    }

    /**
     * 校验库区和货位
     * @param idto 登记信息
     */
    private void validateStorageAreaAndAllocation(DdReceiveDocRecordIDTO idto) {
        Integer storageArea = idto.getStorageArea();
        Long goodsAllocationId = idto.getGoodsAllocationId();

        // 非排面库区时，必须选择货位
        if (!StorageAreaEnum.SHELF_AREA.getCode().equals(storageArea)) {
            QYAssert.notNull(goodsAllocationId, "货位不能为空");
        }
    }

    private void checkParam(DdReceiveDocRecordIDTO idto) {
        // 基础参数校验
        QYAssert.notNull(idto.getDocId(), "单据ID不能为空");
        QYAssert.notNull(idto.getCommodityId(), "商品ID不能为空");
        QYAssert.notNull(idto.getRealReceiveQuantity(), "实收数量不能为空");
        QYAssert.notNull(idto.getStorageArea(), "库区不能为空");

        // 商品必须是档口下面的
        consignmentSupplierService.checkStallCommodity(idto.getShopId(), idto.getStallId(), Collections.singletonList(idto.getCommodityId()));

        // 校验单据是否存在
        DdReceiveDoc ddReceiveDoc = ddReceiveDocMapper.selectByPrimaryKey(idto.getDocId());
        QYAssert.notNull(ddReceiveDoc, "收货单不存在");
        QYAssert.isTrue(YesOrNoEnums.NO.getCode().equals(ddReceiveDoc.getDocStatus()), "收货单已完成，无法登记");

        // 校验商品是否在收货单中并获取商品信息
        List<Integer> deliveryBatchList = getDeliveryBatchList(ddReceiveDoc.getDeliveryBatch());
        List<DdReceiveDocCommodityODTO> commodityList = ddReceiveDocMapper.querySumReceiveOrderCommodityList(
                ddReceiveDoc.getShopId(), ddReceiveDoc.getStallId(),
                DateUtil.getDateFormate(ddReceiveDoc.getOrderTime(), "yyyy-MM-dd"), deliveryBatchList);

        DdReceiveDocCommodityODTO currentCommodity = commodityList.stream()
                .filter(item -> idto.getCommodityId().equals(Long.valueOf(item.getCommodityId())))
                .findFirst()
                .orElse(null);
        QYAssert.notNull(currentCommodity, "商品不在当前收货单中");

        // 获取商品基础信息用于校验
        CommodityVO vo = new CommodityVO();
        vo.setCommodityIdList(Collections.singletonList(idto.getCommodityId()));
        List<CommodityBasicEntry> commBasicList = commodityMapper.findCommodityBasicListByParam(vo);
        CommodityBasicEntry commodityBasic = commBasicList.isEmpty() ? null : commBasicList.get(0);
        QYAssert.notNull(commodityBasic, "商品不存在");

        // 校验实收数量
        validateReceiveQuantity(idto, currentCommodity, commodityBasic);

        // 校验库区和货位
        validateStorageAreaAndAllocation(idto);

        if (CollectionUtils.isNotEmpty(idto.getVideoList())) {
            QYAssert.isTrue(idto.getVideoList().size() <= 1, "最多只能上传1个视频");
        }
    }

    /**
     * 构建收货商品对象（统一处理已登记/未登记）
     */
    private DdReceiveDocCommodityODTO buildDocCommodity(DdReceiveDocCommodityODTO src,
                                                        TokenInfo tokenInfo,
                                                        Map<Long, CommodityInfoEntry> commodityMap,
                                                        String now,
                                                        Map<Long, DdReceiveDocRecord> recordedCommodityMap,
                                                        Map<Long, List<DdReceiveDocPic>> recordedCommodityPicMap,
                                                        boolean isRecorded) {
        DdReceiveDocCommodityODTO docCommodity = BeanCloneUtils.copyTo(src, DdReceiveDocCommodityODTO.class);

        Long commodityId = Long.valueOf(src.getCommodityId());

        if (isRecorded) {
            // 保留登记信息
            DdReceiveDocRecord record = recordedCommodityMap.get(commodityId);
            if (record != null) {
                docCommodity.setRealReceiveQuantity(record.getRealReceiveQuantity());
                docCommodity.setStorageArea(record.getStorageArea());
                docCommodity.setGoodsAllocationId(record.getGoodsAllocationId());
                // TODO 设置货位编码
            }
            List<DdReceiveDocPic> pics = recordedCommodityPicMap.getOrDefault(commodityId, Collections.emptyList());
            docCommodity.setPicList(filterPicOrVideo(pics, SaleReturnOrderPic.PicTypeEnums.PIC.getCode()));
            docCommodity.setVideoList(filterPicOrVideo(pics, SaleReturnOrderPic.PicTypeEnums.VIDEO.getCode()));
        } else {
            // 未登记：收货数量=实发数量，默认排面
            docCommodity.setRealReceiveQuantity(src.getRealDeliveryQuantity());
            docCommodity.setStorageArea(StorageAreaEnum.SHELF_AREA.getCode());
        }

        // 公共信息
        CommodityInfoEntry info = commodityMap.get(commodityId);
        if (info != null) {
            docCommodity.setCommodityCode(info.getCommodityCode());
            docCommodity.setCommodityName(info.getCommodityName());
        }
        docCommodity.setPdaStatus(1);
        docCommodity.setReceiveUserId(tokenInfo.getUserId());
        docCommodity.setReceiveUserName(tokenInfo.getRealName());
        docCommodity.setReceiveTime(now);

        return docCommodity;
    }

    /**
     * 过滤图片/视频
     */
    private List<SaleReturnOrderPicVO> filterPicOrVideo(List<DdReceiveDocPic> pics, Integer type) {
        if (CollectionUtils.isEmpty(pics)) {
            return Collections.emptyList();
        }
        return pics.stream()
                .filter(p -> Objects.equals(type, p.getReceiveType()))
                .map(p -> {
                    SaleReturnOrderPicVO vo = new SaleReturnOrderPicVO();
                    vo.setPicUrl(p.getPicUrl());
                    return vo;
                }).collect(Collectors.toList());
    }
}

package com.pinshang.qingyun.order.service.bigShop;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.pinshang.qingyun.base.api.TokenInfo;
import com.pinshang.qingyun.base.configure.expand.QYAssert;
import com.pinshang.qingyun.base.enums.YesOrNoEnums;
import com.pinshang.qingyun.base.enums.xd.StorageAreaEnum;
import com.pinshang.qingyun.base.util.FastThreadLocalUtil;
import com.pinshang.qingyun.box.utils.BeanCloneUtils;
import com.pinshang.qingyun.box.utils.DateUtil;
import com.pinshang.qingyun.box.utils.SpringUtil;
import com.pinshang.qingyun.common.service.CodeClient;
import com.pinshang.qingyun.infrastructure.data.query.toolkit.StringUtils;
import com.pinshang.qingyun.order.dto.bigShop.*;
import com.pinshang.qingyun.order.enums.DeliveryBatchTypeEnum;
import com.pinshang.qingyun.order.enums.ReceiveOrderStatusEnums;
import com.pinshang.qingyun.order.listener.tda.TdaTmsListener;
import com.pinshang.qingyun.order.mapper.CommodityMapper;
import com.pinshang.qingyun.order.mapper.ShopMapper;
import com.pinshang.qingyun.order.mapper.ShopReceiveOrderMapper;
import com.pinshang.qingyun.order.mapper.entry.commodity.CommodityBasicEntry;
import com.pinshang.qingyun.order.mapper.bigShop.DdReceiveDocLogMapper;
import com.pinshang.qingyun.order.mapper.bigShop.DdReceiveDocMapper;
import com.pinshang.qingyun.order.mapper.bigShop.DdReceiveDocOrderMapper;
import com.pinshang.qingyun.order.model.shop.Shop;
import com.pinshang.qingyun.order.model.shop.ShopReceiveOrder;
import com.pinshang.qingyun.order.model.bigShop.DdReceiveDoc;
import com.pinshang.qingyun.order.model.bigShop.DdReceiveDocLog;
import com.pinshang.qingyun.order.model.bigShop.DdReceiveDocOrder;
import com.pinshang.qingyun.order.service.CommonService;
import com.pinshang.qingyun.order.service.ConsigneeCommonService;
import com.pinshang.qingyun.order.service.ConsignmentSupplierService;
import com.pinshang.qingyun.order.service.ShopService;
import com.pinshang.qingyun.order.vo.commodity.CommodityVO;
import com.pinshang.qingyun.renderer.client.dto.DictionaryODTO;
import com.pinshang.qingyun.xd.wms.dto.StockReceiptIDTO;
import com.pinshang.qingyun.xd.wms.dto.StockReceiptItemDTO;
import com.pinshang.qingyun.xd.wms.dto.bigShop.DdStockInOutExtraIDTO;
import com.pinshang.qingyun.xd.wms.dto.bigShop.GoodsAllocationODTO;
import com.pinshang.qingyun.xd.wms.service.XdStockClient;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.aop.framework.AopContext;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.entity.Example;

import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @Author: sk
 * @Date: 2024/10/11
 */
@Slf4j
@Service
public class BigShopReceiveService {

    @Autowired
    private DdReceiveDocMapper ddReceiveDocMapper;
    @Autowired
    private DdReceiveDocLogMapper ddReceiveDocLogMapper;
    @Autowired
    private DdReceiveDocOrderMapper ddReceiveDocOrderMapper;
    @Autowired
    private RedissonClient redissonClient;
    @Autowired
    private ShopService shopService;
    @Autowired
    private CodeClient codeClient;
    @Autowired
    private ConsignmentSupplierService consignmentSupplierService;
    @Autowired
    private ShopMapper shopMapper;
    @Autowired
    private CommodityMapper commodityMapper;
    @Autowired
    private CommonService commonService;
    @Autowired
    private ShopReceiveOrderMapper shopReceiveOrderMapper;
    @Autowired
    private XdStockClient xdStockClient;
    @Autowired
    private ConsigneeCommonService consigneeCommonService;
    @Autowired
    private ApplicationContext applicationContext;

    /**
     * 新增订单实时维护大店收货单
     * @param orderTime
     * @param shopId
     */
    @Async
    public void createReceiveDoc(String orderTime, Long storeId, Long stallId, Integer deliveryBatch ) {
        log.info("大店创建收货单,  orderTime {}  storeId {}  stallId {}  deliveryBatch {}", orderTime, storeId, stallId, deliveryBatch);

        if(orderTime == null || storeId == null || stallId == null || deliveryBatch == null){
            return;
        }

        String lockKey = ( storeId + orderTime + stallId );
        RLock lock = redissonClient.getLock("order:createReceiveDoc:" + lockKey);
        lock.lock(2L, TimeUnit.SECONDS);

        try{
            BigShopReceiveService bigShopReceiveService = applicationContext.getBean(BigShopReceiveService.class);
            bigShopReceiveService.saveReceiveDoc(orderTime, storeId, stallId, deliveryBatch);
        }finally{
            lock.unlock(); // 释放锁
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public void saveReceiveDoc(String orderTime, Long storeId, Long stallId, Integer deliveryBatch) {
        Shop shop = shopService.getShopByStoreId(storeId);
        Long shopId = shop.getId();

        // 配送批次 1=1配 + 补货  2=2配  8=新开店
        // 补货和1配批次放一起
        if(DeliveryBatchTypeEnum.REPLENISHMENT.getCode().equals(deliveryBatch)){
            deliveryBatch = DeliveryBatchTypeEnum.ONE_BATCH.getCode();
        }

        Example ddReceiveDocEx = new Example(DdReceiveDoc.class);
        ddReceiveDocEx.createCriteria().andEqualTo("orderTime", orderTime)
                .andEqualTo("shopId", shopId)
                .andEqualTo("stallId", stallId)
                .andEqualTo("deliveryBatch", deliveryBatch)
                .andEqualTo("docStatus", YesOrNoEnums.NO.getCode());
        List<DdReceiveDoc> ddReceiveDocList = ddReceiveDocMapper.selectByExample(ddReceiveDocEx);
        if(CollectionUtils.isEmpty(ddReceiveDocList)){
            Date nowTime = new Date();
            DdReceiveDoc ddReceiveDoc = new DdReceiveDoc();
            ddReceiveDoc.setDocCode(codeClient.createCode("BIG_SHOP_RECEIVE_CODE"));
            ddReceiveDoc.setOrderTime(DateUtil.parseDate(orderTime, "yyyy-MM-dd"));
            ddReceiveDoc.setShopId(shopId);
            ddReceiveDoc.setStallId(stallId);
            ddReceiveDoc.setDeliveryBatch(deliveryBatch);
            ddReceiveDoc.setDocStatus(YesOrNoEnums.NO.getCode());
            ddReceiveDoc.setCreateId(-1L);
            ddReceiveDoc.setCreateTime(nowTime);
            ddReceiveDoc.setUpdateId(-1L);
            ddReceiveDoc.setUpdateTime(nowTime);
            ddReceiveDocMapper.insert(ddReceiveDoc);
        }
    }

    /**
     * 查询收货单列表
     * @param idto
     * @return
     */
    public PageInfo<DdReceiveDocODTO> getReceiveDocList(DdReceiveDocPageIDTO idto) {
        PageInfo<DdReceiveDocODTO> pageDate = null ;

        QYAssert.isTrue(idto.getShopId() != null, "门店id不能为空");
        QYAssert.isTrue(StringUtils.isNotBlank(idto.getOrderTime()), "送货日期不能为空");

        List<Long> stallIdList = consignmentSupplierService.selectUserStallIdList(idto.getShopId());
        if(CollectionUtils.isEmpty(stallIdList)){
            return new PageInfo<>();
        }else {
            idto.setStallIdList(stallIdList);
        }

        pageDate = PageHelper.startPage(idto.getPageNo(), idto.getPageSize()).doSelectPageInfo(() -> {
            ddReceiveDocMapper.getReceiveDocList(idto);
        });

        List<DdReceiveDocODTO> list = pageDate.getList();
        if (SpringUtil.isNotEmpty(list)) {
            List<Long> stallIds = list.stream().filter(p -> p.getStallId() != null && p.getStallId() > 0).collect(Collectors.toList())
                    .stream().map(item -> item.getStallId()).collect(Collectors.toList());
            Map<Long, String> stallIdAndNameMap = consignmentSupplierService.queryStallMapByIds(stallIds);

             // 根据门店送货日期查询关联的订单数(未收货的)
            Map<String, Integer> waitOrderCountMap = new HashMap<>(20);

            // 关联订单数(已经收货的)
            Map<String, Integer> orderCountMap = new HashMap<>(20);
            List<DdReceiveDocODTO> waitReceiveList = list.stream().filter(p -> YesOrNoEnums.NO.getCode().equals(p.getDocStatus())).collect(Collectors.toList());
            List<Long> docIdList = list.stream().filter(p -> YesOrNoEnums.YES.getCode().equals(p.getDocStatus())).map(item -> Long.valueOf(item.getDocId())).collect(Collectors.toList());

            if(CollectionUtils.isNotEmpty(waitReceiveList)){
                waitOrderCountMap = getWaitReceiveOrderCountMap(idto.getShopId(), idto.getOrderTime());
            }

            if(CollectionUtils.isNotEmpty(docIdList)){
                orderCountMap = getReceiveOrderCountMap(docIdList);
            }

            for(DdReceiveDocODTO docODTO :list){
                docODTO.setStallName(stallIdAndNameMap.get(docODTO.getStallId()));

                if(YesOrNoEnums.NO.getCode().equals(docODTO.getDocStatus())){
                    if(DeliveryBatchTypeEnum.ONE_BATCH.getCode().equals(docODTO.getDeliveryBatch())){
                        String oneBatchKey = docODTO.getStallId() + "_" + DeliveryBatchTypeEnum.ONE_BATCH.getCode();
                        String replenishmentKey = docODTO.getStallId() + "_" + DeliveryBatchTypeEnum.REPLENISHMENT.getCode();
                        Integer oneBatchcount = (waitOrderCountMap.get(oneBatchKey) != null ? waitOrderCountMap.get(oneBatchKey) : 0);
                        Integer replenishmentCount = (waitOrderCountMap.get(replenishmentKey) != null ? waitOrderCountMap.get(replenishmentKey) : 0);
                        docODTO.setOrderCodeCount(oneBatchcount + replenishmentCount);
                    }else {
                        String key = docODTO.getStallId() + "_" + docODTO.getDeliveryBatch();
                        docODTO.setOrderCodeCount(waitOrderCountMap.get(key));
                    }
                }else {
                    docODTO.setOrderCodeCount(orderCountMap.get(docODTO.getDocId()));
                }
            }
        }
        return pageDate;
    }

    /**
     * 获取关联订单号(未收货)
     * @param shopId
     * @param orderTime
     * @return
     */
    private Map<String, Integer> getWaitReceiveOrderCountMap(Long shopId, String orderTime){
        Map<String, Integer> orderCountMap = new HashMap<>(20);
        List<DdReceiveOrderCountODTO> orderCountList = ddReceiveDocMapper.getOrderCount(shopId, orderTime);
        if(CollectionUtils.isNotEmpty(orderCountList)){
            orderCountList.forEach(item -> {
                 String key = item.getStallId() + "_" + item.getDeliveryBatch();
                orderCountMap.put(key, item.getOrderCodeCount());
            });
        }

        return orderCountMap;
    }

    /**
     * 获取关联订单号(已经收货)
     * @param shopId
     * @param orderTime
     * @return
     */
    private Map<String, Integer> getReceiveOrderCountMap(List<Long> docIdList){
        Map<String, Integer> orderCountMap = new HashMap<>(docIdList.size());
        List<DdReceiveOrderCountODTO> orderCountList = ddReceiveDocMapper.getReceiveOrderCount(docIdList);
        if(CollectionUtils.isNotEmpty(orderCountList)){
            orderCountList.forEach(item -> {
                orderCountMap.put(item.getDocId() + "", item.getOrderCodeCount());
            });
        }

        return orderCountMap;
    }

    /**
     * 收货详情、去收货页面
     * @param docId
     * @return
     */
    public DdReceiveDocODTO getReceiveDocCommodityList(Long docId) {

        DdReceiveDoc ddReceiveDoc = ddReceiveDocMapper.selectByPrimaryKey(docId);
        QYAssert.isTrue(ddReceiveDoc != null, "单据不存在");

        DdReceiveDocODTO receiveDocResult = BeanCloneUtils.copyTo(ddReceiveDoc, DdReceiveDocODTO.class);

        // 档口信息
        Map<Long, String> stallIdAndNameMap = consignmentSupplierService.queryStallMapByIds(Collections.singletonList(ddReceiveDoc.getStallId()));
        receiveDocResult.setStallName(stallIdAndNameMap.get(ddReceiveDoc.getStallId()));

        Shop shop = shopMapper.selectByPrimaryKey(ddReceiveDoc.getShopId());
        receiveDocResult.setShopName(shop.getShopName());

        List<DdReceiveDocCommodityODTO> commodityList = new ArrayList<>();
        // 未收货实时查询订单商品信息
        if(YesOrNoEnums.NO.getCode().equals(ddReceiveDoc.getDocStatus())){
            List<Integer> deliveryBatchList = getDeliveryBatchList(ddReceiveDoc.getDeliveryBatch());
            commodityList = ddReceiveDocMapper.querySumReceiveOrderCommodityList(
                                ddReceiveDoc.getShopId(), ddReceiveDoc.getStallId(),
                                DateUtil.getDateFormate(ddReceiveDoc.getOrderTime(), "yyyy-MM-dd"), deliveryBatchList);
        }else {
            // 已经收货就直接查询收获记录表
            commodityList = ddReceiveDocMapper.queryReceiveCommodityLogList(docId);
        }


        if(CollectionUtils.isNotEmpty(commodityList)){
            List<Long> commodityIdList = commodityList.stream().map(item -> Long.valueOf(item.getCommodityId())).collect(Collectors.toList());

            // 查询商品基础信息
            CommodityVO vo = new CommodityVO();
            vo.setCommodityIdList(commodityIdList);
            List<CommodityBasicEntry> commBasicList = commodityMapper.findCommodityBasicListByParam(vo);
            Map<String, CommodityBasicEntry> commBasicMap = commBasicList.stream().collect(Collectors.toMap(CommodityBasicEntry::getCommodityId, Function.identity()));
            List<CommodityBasicEntry> commBarCodeList = commodityMapper.findCommodityBarCodeByIds(commodityIdList);
            Map<String, String> barCodeMap = commBarCodeList.stream().collect(Collectors.toMap(CommodityBasicEntry::getCommodityId,CommodityBasicEntry::getBarCodes,(key1 , key2)-> key2));
            Set<Long> dictionaryIdSet = new HashSet<>();
            for (CommodityBasicEntry commodityBasicEntry : commBasicList) {
                dictionaryIdSet.add(commodityBasicEntry.getCommodityUnitId());
            }
            Map<String, DictionaryODTO> dictionaryMap = commonService.listDictionarysByIdSet(dictionaryIdSet);

            // 查询货位信息
            List<Long> goodsAllocationIdList = commodityList.stream().filter(p -> p.getGoodsAllocationId() != null).collect(Collectors.toList())
                    .stream().map(item -> item.getGoodsAllocationId()).collect(Collectors.toList());
            Map<Long, GoodsAllocationODTO> goodsAllocationMap = consignmentSupplierService.queryGoodsAllocationMapByIds(goodsAllocationIdList);

            for(DdReceiveDocCommodityODTO odto : commodityList){
                CommodityBasicEntry commodityBasic = commBasicMap.get(odto.getCommodityId());
                if(commodityBasic != null){
                    BeanUtils.copyProperties(commodityBasic,odto);
                }
                odto.setUnit(null != dictionaryMap.get(odto.getCommodityUnitId() + "" ) ? dictionaryMap.get(odto.getCommodityUnitId() + "").getOptionName() : "");
                odto.setBarCodes(barCodeMap.get(odto.getCommodityId()));

                if(org.apache.commons.lang3.StringUtils.isNotBlank(odto.getBarCodes())){
                    String[] barCode = odto.getBarCodes().trim().split(",");
                    odto.setBarCode(barCode[0]);
                    List barCodeList = java.util.Arrays.asList(barCode);
                    odto.setBarCodeList(barCodeList);
                }

                odto.setGoodsAllocationCode(goodsAllocationMap.get(odto.getGoodsAllocationId()) != null ? goodsAllocationMap.get(odto.getGoodsAllocationId()).getGoodsAllocationCode() : "");
            }
        }
        receiveDocResult.setCommodityList(commodityList);
        return receiveDocResult;
    }


    /**
     * 打印收货清单
     * @param docId
     * @return
     */
    public DdReceiveDocODTO printReceiveDocCommodityList(Long docId) {
        TokenInfo tokenInfo = FastThreadLocalUtil.getQY();
        DdReceiveDocODTO receiveDocResult = getReceiveDocCommodityList(docId);
        receiveDocResult.setPrintTime(DateUtil.getDateFormate(new Date(), "yyyy-MM-dd HH:mm:ss"));
        receiveDocResult.setPrintUserName(tokenInfo.getRealName());
        return  receiveDocResult;
    }

    /**
     * 关联订单号
     * @param docId
     * @return
     */
    public List<DdReceiveDocOrderODTO> receiveOrderList(Long docId) {
        DdReceiveDoc ddReceiveDoc = ddReceiveDocMapper.selectByPrimaryKey(docId);
        QYAssert.isTrue(ddReceiveDoc != null, "单据不存在");

        List<DdReceiveDocOrderODTO> orderList = new ArrayList<>();
        // 未收货实时查询关联的订单号
        if(YesOrNoEnums.NO.getCode().equals(ddReceiveDoc.getDocStatus())){
            // 未收货实时查询关联订单号
            List<Integer> deliveryBatchList = getDeliveryBatchList(ddReceiveDoc.getDeliveryBatch());

            orderList = ddReceiveDocMapper.queryWaitReceiveRelationOrderList(ddReceiveDoc.getShopId(), ddReceiveDoc.getStallId(),
                    DateUtil.getDateFormate(ddReceiveDoc.getOrderTime(), "yyyy-MM-dd"), deliveryBatchList);
        }else {

            // 已经收货查询关联订单记录表
            orderList = ddReceiveDocMapper.queryRelationOrderListLog(ddReceiveDoc.getId());
        }
        return orderList;
    }

    /**
     * 获取配送批次，收货单里面的1配对应订单里面的1配 + 补货
     * @param deliveryBatch
     * @return
     */
    private  List<Integer> getDeliveryBatchList(Integer deliveryBatch) {
        List<Integer> deliveryBatchList = new ArrayList<>();
        if(DeliveryBatchTypeEnum.ONE_BATCH.getCode().equals(deliveryBatch)){
            deliveryBatchList.add(DeliveryBatchTypeEnum.ONE_BATCH.getCode());
            deliveryBatchList.add(DeliveryBatchTypeEnum.REPLENISHMENT.getCode());
        }else {
            deliveryBatchList.add(deliveryBatch);
        }
        return deliveryBatchList;
    }


    /**
     * 收货操作(大店)
     * @param idto
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public Boolean addBigShopReceive(DdReceiveDocSaveIDTO idto) {
        TokenInfo tokenInfo = FastThreadLocalUtil.getQY();
        Long userId = tokenInfo.getUserId();
        Date nowTime = new Date();

        DdReceiveDoc ddReceiveDoc = ddReceiveDocMapper.selectByPrimaryKey(idto.getDocId());
        QYAssert.isTrue(ddReceiveDoc != null, "单据不存在");
        QYAssert.isTrue(YesOrNoEnums.NO.getCode().equals(ddReceiveDoc.getDocStatus()), "单据已完成收货");

        // 入参校验
        receiveParamCheck(idto);

        // 判断实收数量不能大于实发数量
        List<Integer> deliveryBatchList = getDeliveryBatchList(ddReceiveDoc.getDeliveryBatch());
        List<DdReceiveDocCommodityODTO> receiveOrderCommodityList = ddReceiveDocMapper.querySumReceiveOrderCommodityList(
                    ddReceiveDoc.getShopId(), ddReceiveDoc.getStallId(),
                    DateUtil.getDateFormate(ddReceiveDoc.getOrderTime(), "yyyy-MM-dd"), deliveryBatchList);

        QYAssert.notEmpty(receiveOrderCommodityList, "商品都没有实发，不能完成收货");
        Map<String, DdReceiveDocCommodityODTO> receiveOrderCommodityMap = receiveOrderCommodityList.stream().collect(Collectors.toMap(DdReceiveDocCommodityODTO::getCommodityId, Function.identity()));

        Set<Long> subOrderIdList = new HashSet<>();
        // 大店收货单日志表
        List<DdReceiveDocLog> receiveDocLogList = new ArrayList<>();

        // 大店收货订单记录表
        List<DdReceiveDocOrder> receiveDocOrderList = new ArrayList<>();
        // 记录订单号重复
        List<String> orderCodeList = new ArrayList<>();

        List<StockReceiptItemDTO> stockReceiptList = new ArrayList<>();
        Map<Long, BigDecimal> commodityPriceMap = new HashMap<>(idto.getCommodityList().size());

        // 查询商品基础信息
        List<Long> commodityIdList = idto.getCommodityList().stream().map(item -> Long.valueOf(item.getCommodityId())).collect(Collectors.toList());
        CommodityVO vo = new CommodityVO();
        vo.setCommodityIdList(commodityIdList);
        List<CommodityBasicEntry> commodityBasicList = commodityMapper.findCommodityBasicListByParam(vo);
        Map<String, CommodityBasicEntry> commodityBasicMap = commodityBasicList.stream().collect(Collectors.toMap(CommodityBasicEntry::getCommodityId, Function.identity()));

        List<DdReceiveUpdateSubOrderCommodityQuantityDTO> ddReceiveUpdateSubOrderCommodityQuantityDTOList = Lists.newArrayList();
        for(DdReceiveDocCommodityODTO odto : idto.getCommodityList()){
            if(receiveOrderCommodityMap.containsKey(odto.getCommodityId())){
                DdReceiveDocCommodityODTO receiveOrderCommodity = receiveOrderCommodityMap.get(odto.getCommodityId());
                QYAssert.notNull(receiveOrderCommodity.getRealDeliveryQuantity(), odto.getCommodityName() + " 大仓未发货，请联系大仓发货完成再收货");

                QYAssert.isTrue(odto.getRealReceiveQuantity().compareTo(receiveOrderCommodity.getRealDeliveryQuantity()) <= 0, odto.getCommodityName() + " 实收数量不能大于实发数量");
            }else {
                QYAssert.isFalse(odto.getCommodityName() + " 大仓未发货，请联系大仓发货完成再收货");
            }

            // 单商品本次收货数量
            BigDecimal receiveTotalQuantity = odto.getRealReceiveQuantity();

            // 开始收货,大店下单是过滤配比、配货、赠品
            // 收货单收货完成，实收数量要回写关联订单的实收数量，同时更新订单的收货状态。
            //若一个商品存在多个订单，且存在正常订货，配货，赠品三种情况时，需先回写正常订货数，后配货，最后再赠品的逻辑
            //若正常订货的商品出现在多个订单，则优先满足订货数量少的订单，再满足订货数量多的订单；若订货数一样，优先按照订单号小的先回写
            List<DdReceiveOrderCommodityODTO> orderCommodityList = ddReceiveDocMapper.getOrderCommodityInfo(ddReceiveDoc.getShopId(),
                    ddReceiveDoc.getStallId(),DateUtil.getDateFormate(ddReceiveDoc.getOrderTime(), "yyyy-MM-dd"),
                    deliveryBatchList,Long.valueOf(odto.getCommodityId()));

            for(DdReceiveOrderCommodityODTO comm : orderCommodityList){

                // 把实收数量分摊到每个订单商品里面
                if(receiveTotalQuantity.compareTo(BigDecimal.ZERO) > 0){
                    if(receiveTotalQuantity.compareTo(comm.getRealDeliveryQuantity()) > 0){
                        ddReceiveUpdateSubOrderCommodityQuantityDTOList.add(new DdReceiveUpdateSubOrderCommodityQuantityDTO(comm.getRealDeliveryQuantity(), comm.getSubOrderItemId(), comm.getCommodityId()));
                    }else {
                        ddReceiveUpdateSubOrderCommodityQuantityDTOList.add(new DdReceiveUpdateSubOrderCommodityQuantityDTO(receiveTotalQuantity, comm.getSubOrderItemId(), comm.getCommodityId()));
                    }
                    receiveTotalQuantity = receiveTotalQuantity.subtract(comm.getRealDeliveryQuantity());
                }else {
                    ddReceiveUpdateSubOrderCommodityQuantityDTOList.add(new DdReceiveUpdateSubOrderCommodityQuantityDTO(BigDecimal.ZERO, comm.getSubOrderItemId(), comm.getCommodityId()));
                }
                subOrderIdList.add(comm.getSubOrderId());

                // 设置关联订单号
                if(!orderCodeList.contains(comm.getOrderCode())){
                    receiveDocOrderList.add(getReceiveDocOrderList(userId, nowTime, ddReceiveDoc, comm.getOrderCode(), comm.getLogisticsModel(), comm.getOrderAmount()));
                }
                orderCodeList.add(comm.getOrderCode());

                commodityPriceMap.put(comm.getCommodityId(), comm.getPrice());
            }

            // 设置日志
            receiveDocLogList.add(getDdReceiveDocLog(userId, nowTime, ddReceiveDoc, odto, receiveOrderCommodityMap.get(odto.getCommodityId())));

            // 调用入库组装
            stockReceiptList.add(getStockReceipt(odto, ddReceiveDoc, commodityPriceMap, commodityBasicMap.get(odto.getCommodityId()).getCommodityPackageSpec()));
        }

        if(CollectionUtils.isNotEmpty(ddReceiveUpdateSubOrderCommodityQuantityDTOList)){
            ddReceiveDocMapper.batchUpdateRealReceiveQuantity(ddReceiveUpdateSubOrderCommodityQuantityDTOList);
        }

        // 将收货单置为已收货
        if(CollectionUtils.isNotEmpty(subOrderIdList)){
            ShopReceiveOrder shopReceiveOrder = new ShopReceiveOrder();
            shopReceiveOrder.setStatus(ReceiveOrderStatusEnums.PASS.getCode());
            shopReceiveOrder.setUpdateId(tokenInfo.getUserId());
            shopReceiveOrder.setUpdateTime(nowTime);
            shopReceiveOrder.setReceiveId(tokenInfo.getUserId());
            shopReceiveOrder.setReceiveTime(nowTime);
            Example example = new Example(ShopReceiveOrder.class);
            example.createCriteria().andIn("subOrderId", subOrderIdList);
            shopReceiveOrderMapper.updateByExampleSelective(shopReceiveOrder, example);
        }

        // 更新主单为已经收货
        ddReceiveDoc.setReceiveUserId(userId);
        ddReceiveDoc.setReceiveTime(nowTime);
        ddReceiveDoc.setDocStatus(YesOrNoEnums.YES.getCode());
        ddReceiveDoc.setUpdateId(userId);
        ddReceiveDoc.setUpdateTime(nowTime);
        ddReceiveDocMapper.updateByPrimaryKey(ddReceiveDoc);

        // 大店收货单日志表
        if(CollectionUtils.isNotEmpty(receiveDocLogList)){
            ddReceiveDocLogMapper.insertList(receiveDocLogList);

            // 如果有少货，生成少货单
            List<DdReceiveDocLog> shortList = receiveDocLogList.stream().filter(p -> p.getShortReceiveQuantity() != null
                    && p.getShortReceiveQuantity().compareTo(BigDecimal.ZERO) > 0).collect(Collectors.toList());
            if(CollectionUtils.isNotEmpty(shortList)){
                consigneeCommonService.addBigShopShortReturn(shortList, commodityBasicMap, tokenInfo.getEnterpriseId(), tokenInfo.getUserId());
            }
        }

        // 大店收货订单记录表
        if(CollectionUtils.isNotEmpty(receiveDocOrderList)){
            ddReceiveDocOrderMapper.insertList(receiveDocOrderList);
        }

        // 调用入库操作
        StockReceiptIDTO stockReceiptIDTO = new StockReceiptIDTO();
        stockReceiptIDTO.setUserId(userId);
        stockReceiptIDTO.setReferId(ddReceiveDoc.getId());
        stockReceiptIDTO.setReferCode(ddReceiveDoc.getDocCode());
        stockReceiptIDTO.setWarehouseId(ddReceiveDoc.getShopId());
        stockReceiptIDTO.setCommodityList(stockReceiptList);
        xdStockClient.stockReceipt(stockReceiptIDTO);
        return true;
    }

    /**
     * 调用入库组装数据
     */
    private StockReceiptItemDTO getStockReceipt(DdReceiveDocCommodityODTO odto, DdReceiveDoc ddReceiveDoc,
                                                Map<Long,BigDecimal> commodityPriceMap,
                                                BigDecimal commodityPackageSpec) {
        StockReceiptItemDTO itemDTO = new StockReceiptItemDTO();
        itemDTO.setCommodityId(Long.valueOf(odto.getCommodityId()));
        itemDTO.setQuantity(odto.getRealReceiveQuantity());
        itemDTO.setNumber(itemDTO.getQuantity().divide(commodityPackageSpec, 0, BigDecimal.ROUND_UP).intValue());
        itemDTO.setNormalNumber(itemDTO.getNumber());
        itemDTO.setNormalQuantity(itemDTO.getQuantity());

        itemDTO.setAbnormalNumber(0);
        itemDTO.setAbnormalQuantity(BigDecimal.ZERO);
        itemDTO.setPrice(commodityPriceMap.get(Long.valueOf(odto.getCommodityId())));
        itemDTO.setTotalPrice(itemDTO.getPrice().multiply(itemDTO.getQuantity()));

        DdStockInOutExtraIDTO ddStockInOutExtraIDTO = DdStockInOutExtraIDTO.buildDdStockInOutExtraVO(Long.valueOf(odto.getCommodityId()),
                ddReceiveDoc.getStallId(), odto.getStorageArea(), odto.getGoodsAllocationId(), odto.getGoodsAllocationCode());
        itemDTO.setDdStockInOutExtraVO(ddStockInOutExtraIDTO);
        return itemDTO;
    }

    /**
     * 大店收货订单记录表
     */
    private DdReceiveDocOrder getReceiveDocOrderList(Long userId, Date nowTime,
                                        DdReceiveDoc ddReceiveDoc,
                                        String orderCode,
                                        Integer logisticsModel,
                                        BigDecimal orderAmount) {

        DdReceiveDocOrder ddReceiveDocOrder = new DdReceiveDocOrder();
        ddReceiveDocOrder.setDocId(ddReceiveDoc.getId());
        ddReceiveDocOrder.setOrderTime(ddReceiveDoc.getOrderTime());
        ddReceiveDocOrder.setShopId(ddReceiveDoc.getShopId());
        ddReceiveDocOrder.setOrderCode(orderCode);
        ddReceiveDocOrder.setLogisticsModel(logisticsModel);
        ddReceiveDocOrder.setOrderAmount(orderAmount);
        ddReceiveDocOrder.setCreateId(userId);
        ddReceiveDocOrder.setCreateTime(nowTime);
        ddReceiveDocOrder.setUpdateId(userId);
        ddReceiveDocOrder.setUpdateTime(nowTime);
        return ddReceiveDocOrder;
    }


    /**
     * 记录收货日志
     */
    private DdReceiveDocLog getDdReceiveDocLog(Long userId, Date nowTime,
                                    DdReceiveDoc ddReceiveDoc,
                                    DdReceiveDocCommodityODTO idto,
                                    DdReceiveDocCommodityODTO receiveDocCommodityODTO) {
        DdReceiveDocLog ddReceiveDocLog = BeanCloneUtils.copyTo(ddReceiveDoc, DdReceiveDocLog.class);
        ddReceiveDocLog.setDocId(ddReceiveDoc.getId());

        ddReceiveDocLog.setCommodityId(Long.valueOf(idto.getCommodityId()));
        ddReceiveDocLog.setQuantity(receiveDocCommodityODTO.getQuantity());
        ddReceiveDocLog.setRealDeliveryQuantity(receiveDocCommodityODTO.getRealDeliveryQuantity());
        ddReceiveDocLog.setRealReceiveQuantity(idto.getRealReceiveQuantity());
        ddReceiveDocLog.setShortReceiveQuantity(ddReceiveDocLog.getRealDeliveryQuantity().subtract(ddReceiveDocLog.getRealReceiveQuantity()));
        ddReceiveDocLog.setStorageArea(idto.getStorageArea());
        ddReceiveDocLog.setGoodsAllocationId(idto.getGoodsAllocationId());
        ddReceiveDocLog.setCreateId(userId);
        ddReceiveDocLog.setUpdateId(userId);
        ddReceiveDocLog.setCreateTime(nowTime);
        ddReceiveDocLog.setUpdateTime(nowTime);
        return ddReceiveDocLog;
    }

    /**
     * 收货入参检验
     * @param idto
     */
    private void receiveParamCheck(DdReceiveDocSaveIDTO idto) {
        QYAssert.notEmpty(idto.getCommodityList(), "商品明细不能为空");

        for(DdReceiveDocCommodityODTO odto : idto.getCommodityList()){
            QYAssert.notNull(odto.getCommodityId(), "商品id不能为空");
            QYAssert.notNull(odto.getCommodityCode(), "商品编码不能为空");
            QYAssert.notNull(odto.getCommodityName(), "商品名称不能为空");
            QYAssert.notNull(odto.getRealReceiveQuantity(), "实收数量不能为空");
            QYAssert.notNull(odto.getStorageArea(), "收货库区不能为空");
            //QYAssert.isTrue(odto.getRealReceiveQuantity().compareTo(BigDecimal.ZERO) > 0, "实收数量必须大于0");

            if(StorageAreaEnum.PICKING_AREA.getCode().equals(odto.getStorageArea())
                    || StorageAreaEnum.WAREHOUSE_AREA.getCode().equals(odto.getStorageArea())){
                QYAssert.notNull(odto.getGoodsAllocationId(), "货位id不能为空");
                QYAssert.notNull(odto.getGoodsAllocationCode(), "货位编码不能为空");
            }
        }
    }
}

package com.pinshang.qingyun.order.service;

import com.pinshang.qingyun.base.configure.expand.QYAssert;
import com.pinshang.qingyun.base.constant.XdaAppVersionConstant;
import com.pinshang.qingyun.base.enums.xda.XdaStoreTypeEnum;
import com.pinshang.qingyun.base.util.FastThreadLocalUtil;
import com.pinshang.qingyun.box.utils.SpringUtil;
import com.pinshang.qingyun.box.utils.StringUtil;
import com.pinshang.qingyun.order.constant.PreOrderTipConstant;
import com.pinshang.qingyun.order.dto.PrePayBillODTO;
import com.pinshang.qingyun.order.dto.xda.v4.XdaCreatePrePayOrderV4IDTO;
import com.pinshang.qingyun.order.dto.xda.v4.XdaSaveOrderODTO;
import com.pinshang.qingyun.order.dto.xforder.CreateXfOrderIDTO;
import com.pinshang.qingyun.order.dto.xforder.SelectXfOrderStatusIDTO;
import com.pinshang.qingyun.order.dto.xforder.XfOrderStatusODTO;
import com.pinshang.qingyun.order.enums.XdaPreOrderPayStatusEnums;
import com.pinshang.qingyun.order.mapper.XdaPreOrderMapper;
import com.pinshang.qingyun.order.mapper.XfOrderMapper;
import com.pinshang.qingyun.order.model.order.XdaPreOrder;
import com.pinshang.qingyun.order.model.order.XfOrder;
import com.pinshang.qingyun.order.service.recharge.RechargeService;
import com.pinshang.qingyun.order.service.xda.v4.TdaOrderService;
import com.pinshang.qingyun.order.service.xda.v4.ToBService;
import com.pinshang.qingyun.order.service.xda.v4.XdaPreOrderService;
import com.pinshang.qingyun.order.vo.recharge.RechargeVo;
import com.pinshang.qingyun.store.dto.storeSettlement.PaymentParentChildStoreSettlementODTO;
import com.pinshang.qingyun.store.dto.xda.QueryXdaUserAccountDTO;
import com.pinshang.qingyun.store.dto.xda.XdaUserAccountDTO;
import com.pinshang.qingyun.store.service.XdaStoreUserClient;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.BeanFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.RequestBody;
import tk.mybatis.mapper.entity.Example;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * 现付单
 */
@Slf4j
@Service
public class XfOrderService {
	
	@Autowired
	private BeanFactory beanFactory;

	@Autowired
	private XfOrderMapper xfOrderMapper;

	@Autowired
	private RechargeService rechargeService;

	@Autowired
	private XdaStoreUserClient xdaStoreUserClient;
	
	public static final String TIMEOUT_ERRORMSG = "提交订单失败（13），请刷新购物车重新支付";

	@Autowired
	private XdaPreOrderService xdaPreOrderService;
	@Value("${pinshang.xfOrder.countdown:90}")
	private String countdown;
	@Autowired
	private XdaPreOrderMapper xdaPreOrderMapper;
	@Autowired
	private DictionaryService dictionaryService;
	@Autowired
	private TdaOrderService tdaOrderService;
	@Autowired
	private ToBService toBService;
	@Autowired
	private StoreService storeService;

	/**
	 * 创建 现付单
	 * 
	 * @param idto
	 * @return
	 */
	@Transactional(rollbackFor = Exception.class)
	public PrePayBillODTO createXfOrder(CreateXfOrderIDTO idto, XdaCreatePrePayOrderV4IDTO xdaCreatePrePayOrderV4IDTO) {
		QYAssert.isTrue(null != idto, "参数不能为空!");
		Long storeId = idto.getStoreId();
		Long userId = idto.getUserId();
		if(StringUtils.isBlank(idto.getBillCode())){
			idto.setBillCode(rechargeService.getNewBillCode());
		}
		// String appCode = idto.getAppCode();
		BigDecimal payAmount = idto.getPayAmount();
		QYAssert.isTrue(null != storeId && null != userId, "未能获取到用户信息,请重新登录!");
		QYAssert.isTrue(null != payAmount && BigDecimal.ZERO.compareTo(payAmount) < 0, "付现金额必须大于零!");

		QueryXdaUserAccountDTO queryXdaUserAccountDTO = new QueryXdaUserAccountDTO();
		queryXdaUserAccountDTO.setStoreId(storeId);
		XdaUserAccountDTO xdaUserAccountDTO = xdaStoreUserClient.queryUserAccountInfo(queryXdaUserAccountDTO);
		QYAssert.notNull(xdaUserAccountDTO, "客户账户不存在");
		// Date orderTime = xdaPreOrderV3IDTO.getOrderTime();
		idto.setStoreType(xdaUserAccountDTO.getStoreType());
		QYAssert.isTrue(XdaStoreTypeEnum.PRE_PAY.equals(idto.getStoreType()), "只有预付客户才能现付下单!");

		Integer businessType = storeService.getStoreBussinessType(storeId);

		XdaPreOrder xdaPreOrder = xdaPreOrderService.queryNoPayXdaPreOrder(storeId);
		if(xdaPreOrder != null){
			Boolean oldVersion = StringUtils.isNotBlank(FastThreadLocalUtil.getXDA().getAppVersion()) && FastThreadLocalUtil.getXDA().getAppVersion().compareToIgnoreCase(XdaAppVersionConstant.TDA_VERSION) < 0;
			if(oldVersion){
				QYAssert.isFalse(PreOrderTipConstant.OLD_PRE_ORDER_TIPS);
			}else {
				QYAssert.isFalse(PreOrderTipConstant.NEW_PRE_ORDER_TIPS);
			}
		}

		PrePayBillODTO result = new PrePayBillODTO();

		// 保存预订单
		XdaSaveOrderODTO xdaSaveOrderODTO = xdaPreOrderService.saveXdaPreOrder(xdaCreatePrePayOrderV4IDTO, idto.getBillCode(), idto.getAppVersion());

		// 预订单保存成功
		if(xdaSaveOrderODTO.getIsSuccess()){
			try {
				result = rechargeService.xdaAppRecharge(this.convert(idto));

				Date operateTime = new Date();
				idto.setBillCode(result.getBillCode());
				xfOrderMapper.insert(XfOrder.forInsert(idto, operateTime));
				result.setIsSuccess(true);

			}catch (Exception e){
				log.warn("调用xdaAppRecharge 异常, orderId {}", xdaSaveOrderODTO.getOrderId());
				toBService.warehouseUnfreezeInventory(xdaSaveOrderODTO.getOrderId(), xdaCreatePrePayOrderV4IDTO.getStoreId(),businessType);
				throw e;
			}
		}else {
			// 老版本直接抛出异常,新版本app前端提示
			if(StringUtils.isNotBlank(idto.getAppVersion()) && idto.getAppVersion().compareToIgnoreCase(XdaAppVersionConstant.TDA_VERSION) < 0){
				QYAssert.isFalse(xdaSaveOrderODTO.getErrorMsg());
			}else {
				result.setIsSuccess(xdaSaveOrderODTO.getIsSuccess());
				result.setOrderId(xdaSaveOrderODTO.getOrderId());
				result.setErrorMsg(xdaSaveOrderODTO.getErrorMsg());
			}
		}
		return result;
	}

	/**
	 * 预订单去支付
	 * @param idto
	 * @return
	 */
	@Transactional(rollbackFor = Exception.class)
	public PrePayBillODTO preOrderGoPay(@RequestBody CreateXfOrderIDTO idto) {
		QYAssert.isTrue(null != idto, "参数不能为空!");
		QYAssert.isTrue(null != idto.getOrderId(), "订单id不能为空!");

		Long storeId = idto.getStoreId();
		Long userId = idto.getUserId();
		BigDecimal payAmount = idto.getPayAmount();
		QYAssert.isTrue(null != storeId && null != userId, "未能获取到用户信息,请重新登录!");
		QYAssert.isTrue(null != payAmount && BigDecimal.ZERO.compareTo(payAmount) < 0, "付现金额必须大于零!");

		QueryXdaUserAccountDTO queryXdaUserAccountDTO = new QueryXdaUserAccountDTO();
		queryXdaUserAccountDTO.setStoreId(storeId);
		//XdaUserAccountDTO xdaUserAccountDTO = xdaStoreUserClient.queryUserAccountInfo(queryXdaUserAccountDTO);
		PaymentParentChildStoreSettlementODTO xdaUserAccountDTO = storeService.queryStoreCollectPriceByStoreId(storeId, true);

		QYAssert.notNull(xdaUserAccountDTO, "客户账户不存在");
		idto.setStoreType(XdaStoreTypeEnum.PRE_PAY.getCode() == xdaUserAccountDTO.getCollectStatus() ? XdaStoreTypeEnum.PRE_PAY : XdaStoreTypeEnum.POST_PAY);
		QYAssert.isTrue(XdaStoreTypeEnum.PRE_PAY.equals(idto.getStoreType()), "只有预付客户才能现付下单!");

		// 只有待支付才可以继续支付
		XdaPreOrder xdaPreOrder = xdaPreOrderMapper.selectByPrimaryKey(idto.getOrderId());
		if(!XdaPreOrderPayStatusEnums.INIT.getCode().equals(xdaPreOrder.getPayStatus())){
			QYAssert.isFalse("订单已经支付或取消，请刷新订单列表！");
		}

		// billCode不每次获取新的，用预订单里面的billCode
		idto.setBillCode(xdaPreOrder.getBillCode());
		// 如果账户支付的有金额，判断账户支付金额是否小于账户余额
		BigDecimal accountPayAmount = xdaPreOrder.getOrderAmount().subtract(payAmount);
		if(accountPayAmount.compareTo(BigDecimal.ZERO) > 0){
			Boolean amountFlag = accountPayAmount.compareTo(xdaUserAccountDTO.getCollectPrice()) <= 0;
			QYAssert.isTrue(amountFlag, "账户金额不足，无法下单");
		}

		// 支付服务需要后面2位小数的，5要转成 5.00
		idto.setPayAmount(idto.getPayAmount().setScale(2, BigDecimal.ROUND_HALF_UP));
		PrePayBillODTO result = rechargeService.xdaAppRecharge(this.convert(idto));

		return result;
	}
	/**
	 * 超时  现付单	【注意这里的事务传播】
	 * 
	 * @param xfOrderId
	 * @return
	 */
	@Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRES_NEW)
	public int timeoutXfOrder(Long xfOrderId) {
		Example example = new Example(XfOrder.class);
        example.createCriteria().andEqualTo("id", xfOrderId).andEqualTo("status", XfOrder.XfOrderStatusEnums.INIT.getCode());
        
        XfOrder record = XfOrder.forUpdateStatus(xfOrderId, XfOrder.XfOrderStatusEnums.FAILURE.getCode(), TIMEOUT_ERRORMSG, "超时");
        return xfOrderMapper.updateByExampleSelective(record, example);
	}
	
	/**
	 * 查询  客户现付单倒计时，单位秒
	 * 
	 * @param storeId
	 * @return
	 */
	public long getXfOrderCountdown(Long storeId) {
		List<XfOrder> list = xfOrderMapper.select(new XfOrder(storeId, XfOrder.XfOrderStatusEnums.INIT.getCode()));
		if (SpringUtil.isNotEmpty(list)) {
			int defaultCountdown = this.getDefaultCountdown();
			long currentCountdown = System.currentTimeMillis() / 1000 - (list.get(0).getCreateTime().getTime() / 1000 + defaultCountdown);
			return (currentCountdown < 0)? - currentCountdown: 0;
		}
		return 0;
	}


	public long getXdaPreOrderCountdown(Long storeId) {
		XdaPreOrder xdaPreOrder = xdaPreOrderService.queryNoPayXdaPreOrder(storeId);
		if(xdaPreOrder != null){
			Integer xdaPreOrderDelaySeconds = dictionaryService.queryXdaPreOrderDelaySeconds();
			int defaultCountdown = xdaPreOrderDelaySeconds;
			long currentCountdown = System.currentTimeMillis() / 1000 - (xdaPreOrder.getCreateTime().getTime() / 1000 + defaultCountdown);
			return (currentCountdown < 0)? - currentCountdown: 0;
		}
		return 0;
	}

	/**
	 * 订单列表，待支付订单获取剩余时间
	 * @param orderCreateTime
	 * @return
	 */
	public long orderListCountdown(Date orderCreateTime) {
		Integer xdaPreOrderDelaySeconds = dictionaryService.queryXdaPreOrderDelaySeconds();
		int defaultCountdown = xdaPreOrderDelaySeconds + 2;
		long currentCountdown = System.currentTimeMillis() / 1000 - (orderCreateTime.getTime() / 1000 + defaultCountdown);
		return (currentCountdown < 0)? - currentCountdown: 0;
	}

	/**
	 * 获取预订单超时时间搓
	 * @return
	 */
	public Long queryOrderAutoTimeout(){
		Integer xdaPreOrderDelaySeconds = dictionaryService.queryXdaPreOrderDelaySeconds();
		return TimeUnit.SECONDS.toMillis(xdaPreOrderDelaySeconds + 2);
	}

	/**
	 * 查询默认倒计时，单位秒，默认0
	 * 
	 * @return
	 */
	private Integer getDefaultCountdown() {
		if (null != countdown) {
			try {
				return Integer.valueOf(countdown);
			} catch(Exception e) {
				log.error("\n获取现付单默认倒计时错误", e);
			}
		}
		return 0;
	}
	
	/**
	 * 构建充值参数
	 * 
	 * @param idto
	 * @return
	 */
	private RechargeVo convert(CreateXfOrderIDTO idto) {
		RechargeVo vo = new RechargeVo();
		vo.setPayAmount(idto.getPayAmount());
		vo.setPayType(idto.getPayType());
		vo.setClientIp(idto.getClientIp());
		vo.setJsCode(idto.getJsCode());
		vo.setStoreId(idto.getStoreId());
		vo.setUserId(idto.getUserId());
		vo.setAppCode(idto.getAppCode());
		vo.setBillCode(idto.getBillCode());
		return vo;
	}

	/**
	 * 查询 现付单状态
	 * 
	 * @param idto
	 * @return
	 */
	public XfOrderStatusODTO selectXfOrderStatus(@RequestBody SelectXfOrderStatusIDTO idto) {
		QYAssert.isTrue(null != idto, "参数不能为空!");

		String billCode = idto.getBillCode();
		QYAssert.isTrue(!StringUtil.isNullOrEmpty(billCode), "单号不存在!");

		XfOrder xfOrder = xfOrderMapper.selectOne(new XfOrder(billCode));
		QYAssert.isTrue(null != xfOrder, "单据不存在!");
		
		// 检查现付单，是否应该过期
		int defaultCountdown = this.getDefaultCountdown();
		if ((System.currentTimeMillis() / 1000) > (xfOrder.getCreateTime().getTime() / 1000 + defaultCountdown)) {
			int count = beanFactory.getBean(XfOrderService.class).timeoutXfOrder(xfOrder.getId());
			if (count > 0) {
				xfOrder.setStatus(XfOrder.XfOrderStatusEnums.FAILURE.getCode());
				xfOrder.setErrorMsg(TIMEOUT_ERRORMSG);
			}
		}

		return XfOrderStatusODTO.init(xfOrder);
	}

	/**
	 *
	 * @param id
	 * @param status XfOrderStatusEnums
	 * @return
	 */
	public Integer updateStatus(Long id,Integer status,String errorDetailMsg,Long orderId){
		String errorMsg = "";
		if(status == 2){
			errorMsg = TIMEOUT_ERRORMSG;
		}
		XfOrder xfOrder = XfOrder.forUpdateStatus(id, status, errorMsg, errorDetailMsg);
		//支付成功 创建订单成功 有订单 xf单回写orderId
		if(orderId != null){
			xfOrder.setOrderId(orderId);
		}
		return xfOrderMapper.updateByPrimaryKeySelective(xfOrder);
	}

}

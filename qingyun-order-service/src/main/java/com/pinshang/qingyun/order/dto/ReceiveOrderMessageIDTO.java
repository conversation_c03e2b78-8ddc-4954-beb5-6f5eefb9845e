package com.pinshang.qingyun.order.dto;

import com.pinshang.qingyun.base.page.Pagination;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Author: sk
 * @Date: 2021/6/21
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ReceiveOrderMessageIDTO extends Pagination {
    /**1短信  2 广播 */
    private Integer messageType;

    @ApiModelProperty("发送开始日期(yyyy-MM-dd HH:mm:ss)")
    private String beginDate;
    @ApiModelProperty("发送结束日期(yyyy-MM-dd HH:mm:ss)")
    private String endDate;

    @ApiModelProperty("业务类型 1 自动收货后  2 手动收货前")
    private Integer businessType;

    @ApiModelProperty("门店id")
    private Long shopId;
    @ApiModelProperty("门店编码")
    private String shopCode;
    @ApiModelProperty("门店名称")
    private String shopName;

    @ApiModelProperty("职员姓名")
    private String employeeName;
    @ApiModelProperty("职员电话")
    private String employeePhone;
}

package com.pinshang.qingyun.order.dto.giftLimit;/**
 * @Author: sk
 * @Date: 2025/4/17
 */

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2025年04月17日 上午10:54
 */
@Data
public class GiftLimitQuantitySaveIDTO {

    @ApiModelProperty("商品id")
    private Long commodityId;

    @ApiModelProperty("赠送数量")
    private BigDecimal totalQuantity;

    /** 赠送方案id **/
    private Long giftPrommotionId;

    /** 订单id **/
    private Long orderId;
}

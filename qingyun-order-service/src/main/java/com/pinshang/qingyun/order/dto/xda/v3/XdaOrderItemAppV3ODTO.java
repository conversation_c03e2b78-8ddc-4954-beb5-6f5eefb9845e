package com.pinshang.qingyun.order.dto.xda.v3;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.pinshang.qingyun.base.configure.codec.DecimalSerializerKeep2;
import com.pinshang.qingyun.order.dto.xda.v2.XdaOrderItemAppV2ODTO;
import com.pinshang.qingyun.xda.product.dto.commodityText.XdaCommodityInfoODTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.math.RoundingMode;

/**
 * <AUTHOR>
 * @Date 2023/12/21 10:21
 */
@Data
@ApiModel
public class XdaOrderItemAppV3ODTO {
    @ApiModelProperty(value = "orderId", hidden = true)
    private Long orderId;

    @ApiModelProperty("商品名称")
    private String commodityName;

    @ApiModelProperty("商品id")
    private Long commodityId;

    @ApiModelProperty(value = "商品类型",hidden = true)
    private Integer commodityType;

    @ApiModelProperty("单位")
    private String commodityUnitName;

    @ApiModelProperty(" 支付后商品支付价")
    @JsonSerialize(using = DecimalSerializerKeep2.class)
    private BigDecimal commodityPrice;

    @ApiModelProperty(value ="是否有特价：0=无特价，1=普通特价")
    private Integer isSpecialPrice;

    @ApiModelProperty("特价")
    @JsonSerialize(using = DecimalSerializerKeep2.class)
    private BigDecimal specialPrice;

    @ApiModelProperty(value = "是否有特惠：0=无，1=有")
    private Integer isThPrice;

    @ApiModelProperty(value = "特惠价格")
    @JsonSerialize(using = DecimalSerializerKeep2.class)
    private BigDecimal thPrice;

    @ApiModelProperty("订货数量")
    private BigDecimal commodityNum;

    @ApiModelProperty(value = "订货数量- 解决类拷贝",hidden = true)
    private BigDecimal quantity;

    @ApiModelProperty("商品url")
    private String imageUrl;

    @ApiModelProperty("实发数量")
    private BigDecimal realDeliveryQuantity;

    /* 促销ID */
    private String giftModelId;

    @ApiModelProperty("实付金额")
    @JsonSerialize(using = DecimalSerializerKeep2.class)
    private BigDecimal realPrice;

    @ApiModelProperty("商品规格")
    private String commoditySpec;

    @ApiModelProperty("商品原价")
    @JsonSerialize(using = DecimalSerializerKeep2.class)
    private BigDecimal originalPrice;

    @ApiModelProperty("包装规格")
    private BigDecimal commodityPackageSpec;

    @ApiModelProperty("箱规")
    private BigDecimal salesBoxCapacity;

    @ApiModelProperty("预估到手价 -参加满折商品展示")
    @JsonSerialize(using = DecimalSerializerKeep2.class)
    private BigDecimal deliveryPrice;

    private BigDecimal totalPrice;

    @ApiModelProperty("是否为赠品 0、否 1、是")
    private Boolean isGift = Boolean.FALSE;

    private Long pricePromotionId;

    public BigDecimal getCommodityNum(){
        return null != quantity ? quantity :BigDecimal.ZERO;
    }

//    public BigDecimal getRealPrice(){
//        if(null != quantity){
//            if(null != deliveryPrice){
//                if(commodityUnitName.trim().equals("kg")){
//                    BigDecimal divide = deliveryPrice.multiply(BigDecimal.valueOf(2).setScale(2, BigDecimal.ROUND_HALF_UP));
//                    return quantity.multiply(divide).setScale(2,BigDecimal.ROUND_HALF_UP);
//                }else {
//                    return quantity.multiply(deliveryPrice).setScale(2,BigDecimal.ROUND_HALF_UP);
//                }
//            }else if(null != commodityPrice){
//                return quantity.multiply(this.commodityPrice).setScale(2,BigDecimal.ROUND_HALF_UP);
//            }
//        }
//        return null;
//    }


    public static XdaOrderItemAppV3ODTO covert(XdaCommodityInfoODTO odto){
        XdaOrderItemAppV3ODTO xdaOrderItem4AppODTO = new XdaOrderItemAppV3ODTO();
        xdaOrderItem4AppODTO.setCommodityName(odto.getCommodityAppName());
        xdaOrderItem4AppODTO.setCommodityUnitName(odto.getCommodityUnitName());
        xdaOrderItem4AppODTO.setCommoditySpec(odto.getCommoditySpec());
        xdaOrderItem4AppODTO.setImageUrl(odto.getDefaultPicUrl());
        return xdaOrderItem4AppODTO;
    }
}

package com.pinshang.qingyun.order.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class SaleReturnOrderODTO {
    @ApiModelProperty("退货单日期")
    private Date returnOrderDate;
    @ApiModelProperty("退货单号")
    private String returnOrderCode;
    @ApiModelProperty("退货ID")
    private String returnOrderId;
    @ApiModelProperty("退货单状态")
    private Integer status;

    private Long shopId;
    private Long commodityId;
    private BigDecimal price;
    private BigDecimal returnQuantity;
    private BigDecimal commodityPackageSpec;

    private Long storeId;
    private Integer returnReason;

    private Long stallId;
}
package com.pinshang.qingyun.order.vo;

import lombok.Data;

@Data
public class StoreTypeDescVo {

    /**ID，客户类型 t_dictionary.Id**/
    private Long id;
    /**客户类型code**/
    private String storeTypeCode;
    /**客户类型名称**/
    private String storeTypeName;
    /**结算类型：0：不结算，1：鲜达模式=取最小（数量：实发小取实发，下单小去下单，日期：实发晚取实发日期，送货日期晚取送货日期），2：批发模式，3：取下单，4：鲜食（取实发）**/
    private Integer settleBillCalcType;
    /**是否启用B端结算单短交充值状态：0-停用、1-启用**/
    private boolean settleBillReturnAmountState;
    /**
     *是否启用B端投诉审核充值，状态：0-停用、1-启用
     */
    private boolean settleComplaintState;
    /**是否启用提货单打印：0-停用、1-启用**/
    private boolean thdPrinterState;
    /**
     * 结算对账投诉充值：0：不充值；1：充值
     */
    private boolean settleBillReturnComplaintPrice;
}

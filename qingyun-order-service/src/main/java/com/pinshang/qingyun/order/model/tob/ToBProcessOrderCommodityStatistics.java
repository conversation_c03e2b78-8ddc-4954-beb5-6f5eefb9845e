package com.pinshang.qingyun.order.model.tob;

import com.pinshang.qingyun.base.po.BasePO;
import com.pinshang.qingyun.order.vo.tob.ToBOrderStatisticsReqVo;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Table;
import java.math.BigDecimal;
import java.util.Date;

@Table(name = "t_dc_tob_process_order_commodity_statistics")
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ToBProcessOrderCommodityStatistics extends BasePO {
    private Date orderTime;
    private Long commodityId;
    private BigDecimal orderQuantity;
    private Integer orderNumber;

    public ToBProcessOrderCommodityStatistics(ToBOrderStatisticsReqVo reqVo) {
        this.orderTime = reqVo.getOrderTime();
        this.commodityId = reqVo.getCommodityId();
        if (reqVo.getType() == 1) {
            this.orderQuantity = reqVo.getQuantity();
            this.orderNumber = reqVo.getNumber();
            this.setCreateTime(new Date());
            this.setCreateId(-1L);
        }else if (reqVo.getType() == 2) {
            this.orderQuantity = reqVo.getQuantity();
            this.orderNumber = reqVo.getNumber();
        }
        this.setUpdateTime(new Date());
        this.setUpdateId(-1L);
    }

    public static ToBProcessOrderCommodityStatistics initAdd(ToBOrderStatisticsReqVo reqVo) {
        ToBProcessOrderCommodityStatistics statistics = new ToBProcessOrderCommodityStatistics();
        statistics.setCommodityId(reqVo.getCommodityId());
        statistics.setOrderTime(reqVo.getOrderTime());
        statistics.setOrderQuantity(reqVo.getQuantity());
        statistics.setOrderNumber(reqVo.getNumber());
        statistics.setCreateTime(new Date());
        statistics.setCreateId(-1L);
        statistics.setUpdateTime(new Date());
        statistics.setUpdateId(-1L);
        return statistics;
    }

    public static ToBProcessOrderCommodityStatistics initSub(ToBOrderStatisticsReqVo reqVo) {
        ToBProcessOrderCommodityStatistics statistics = new ToBProcessOrderCommodityStatistics();
        statistics.setOrderQuantity(reqVo.getQuantity().negate());
        statistics.setOrderNumber(-reqVo.getNumber());
        statistics.setUpdateTime(new Date());
        statistics.setUpdateId(-1L);
        return statistics;
    }

}

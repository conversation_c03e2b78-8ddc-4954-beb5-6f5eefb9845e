package com.pinshang.qingyun.order.util;

import java.io.File;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.HttpURLConnection;
import java.net.URI;
import java.net.URL;
import java.util.HashMap;
import java.util.Iterator;
import java.util.Map;

import lombok.extern.slf4j.Slf4j;

import org.apache.http.HttpEntity;
import org.apache.http.HttpStatus;
import org.apache.http.client.ClientProtocolException;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.mime.FormBodyPart;
import org.apache.http.entity.mime.MultipartEntity;
import org.apache.http.entity.mime.content.FileBody;
import org.apache.http.entity.mime.content.StringBody;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;

import com.alibaba.fastjson.JSON;
import com.pinshang.qingyun.box.utils.KeyUtil;
import com.pinshang.qingyun.box.utils.MD5Support;
import com.pinshang.qingyun.order.dto.file.FileUploadRestSingleVo;
import com.pinshang.qingyun.order.dto.file.FileUploadResultVo;

@Slf4j
public class UploadFileUtil {

	public static void main(String[] args) throws ClientProtocolException, IOException {
		String fullFileName = "D://order/2021-07-16/7775527-20210716-5584.pdf";
		String url = "http://*************:9078/restUpload";
		System.out.println("result=" + UploadFileUtil.postFile(url, fullFileName));
		
		System.out.println(UploadFileUtil.checkNetworkFile());
	}
	
	/**
	 * 判断  网络文件
	 * 
	 * @return
	 */
	public static boolean checkNetworkFile() {
		URL httpurl = null;
        try {
	        httpurl = new URL(new URI("http://*************:8088/XDA_CHECK_REPORT_FILE/2021/07/14/7775527-20210714-5584.pdf").toASCIIString());
	        
	        HttpURLConnection urlcon = (HttpURLConnection) httpurl.openConnection();
	        // HTTP/1.1 404 Not Found     or     HTTP/1.1 200 OK
	        String message = urlcon.getHeaderField(0);
	        System.out.println("message=" + message);
	        
	        if (null != message && message.startsWith("HTTP/1.1 404")) {
	            System.out.println("不存在"); // HTTP/1.1 404 Not Found
	            return false;
	        } else {
	            System.out.println("存在"+message);
	            return true;
	        }
	        
//	        URLConnection urlConnection = httpurl.openConnection();
//			Long TotalSize = Long.parseLong(urlConnection.getHeaderField("Content-Length"));  
//			if (TotalSize <= 0) {
//	            return false;
//	     	}
//			return true;
        } catch (Exception e) {
            System.out.println("文件不存在");
            return false;
        }
	}
	
	/**
	 * 删除本地临时文件
	 * 
	 * @param fullFileName
	 */
	public static void deleteLocalFile(String fullFileName) {
		try {
			File file = new File(fullFileName);
			boolean deleteFileResult = file.delete();
			System.out.println("deleteFileResult=" +deleteFileResult);
		} catch (Exception e) {
			log.error("\n删除本地临时文件异常", e);
		}
	}
	
	/**
	 * 上传  本地文件到文件服务器
	 * 
	 * @param serverUrl
	 * @param fullFileName
	 * @return
	 * @throws ClientProtocolException
	 * @throws IOException
	 */
	public static FileUploadResultVo postFile(String serverUrl, String fullFileName) throws ClientProtocolException, IOException {
		File file = new File(fullFileName);
        CloseableHttpClient httpClient = HttpClients.createDefault();
        HttpPost httppost = new HttpPost(serverUrl);
        httppost.setEntity(getMutipartEntry(getXdaDeliveryOrderFileMap(),file));
        CloseableHttpResponse response = httpClient.execute(httppost);
        int statusCode = response.getStatusLine().getStatusCode();
        HttpEntity entity = response.getEntity();
        String res = EntityUtils.toString(entity, "UTF-8");
        response.close();
        System.out.println("res=" + res);
        
        if (statusCode != HttpStatus.SC_OK) {
        	log.warn("\n上传文件-statusCode={}，res={}", statusCode, res);
            return null;
        }
        FileUploadRestSingleVo result = JSON.parseObject(res, FileUploadRestSingleVo.class);
        if (null == result) {
        	log.warn("\n上传文件-返回结果为null，statusCode={}，res={}", statusCode, res);
        	return null;
        }
        if (!result.getSuccess()) {
        	log.warn("\n上传文件-success=false，statusCode={}，res={}", statusCode, res);
        	return null;
        }
        FileUploadResultVo data = result.getData();
        if (null == data) {
        	log.warn("\n上传文件-返回结果之data为null，statusCode={}，res={}", statusCode, res);
        	return null;
        }
        return data;
    }
 
	private static MultipartEntity getMutipartEntry(Map<String, Object> param, File file) throws UnsupportedEncodingException {
        if (file == null) {
            throw new IllegalArgumentException("文件不能为空");
        }
        FileBody fileBody = new FileBody(file);
        FormBodyPart filePart = new FormBodyPart("file", fileBody);
        MultipartEntity multipartEntity = new MultipartEntity();
        multipartEntity.addPart(filePart);

        Iterator<String> iterator = param.keySet().iterator();
        while (iterator.hasNext()) {
            String key = iterator.next();
            FormBodyPart field = new FormBodyPart(key, new StringBody((String) param.get(key)));
            multipartEntity.addPart(field);

        }
        return multipartEntity;
    }
	
	private static Map<String, Object> getXdaDeliveryOrderFileMap(){
		String timestamp = System.currentTimeMillis() + "";
		String signature = MD5Support.MD5(KeyUtil.getMD5Key() + timestamp);
		Map<String, Object> map = new HashMap<>();
		map.put("modelType", "XDA_DELIVERY_ORDER_FILE");
		map.put("timestamp", timestamp);
		map.put("signature", signature);
		return map;
	}

}

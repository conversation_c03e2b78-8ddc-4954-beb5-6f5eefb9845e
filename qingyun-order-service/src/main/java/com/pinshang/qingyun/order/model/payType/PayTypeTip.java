package com.pinshang.qingyun.order.model.payType;

import lombok.Data;

import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;

/**
 * Created by Ruansi on 2019/08/15.
 */
@Data
@Table(name = "t_xda_paytype_tip")
public class PayTypeTip {
    @Id
    private Long id;
    // 支付类型ID
    private Long paytypeId;
    // 促销标语
    private String tip;
    // 标语开启状态，0=停用，1=启用
    private Integer status;
    // 开始时间
    private Date beginTime;
    // 结束时间
    private Date endTime;
    // 添加人
    private Long createId;
    // 修改人
    private Long updateId;
    // 添加时间
    private Date createTime;
    // 修改时间
    private Date updateTime;
}

package com.pinshang.qingyun.order.util;

import com.github.pagehelper.PageInfo;
import org.springframework.beans.BeanUtils;

import java.io.*;
import java.util.ArrayList;
import java.util.List;

/**
 * summary.
 * <p>
 * detailed description
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2018/5/17
 */
public class BeanUtil {
    /**
     * 拷贝单个实体对象
     * @param source
     * @param clazz
     * @param <T>
     * @return
     */
    public static <T> T copyProperties(Object source, Class<T> clazz) {
        T target = BeanUtils.instantiate(clazz);
        BeanUtils.copyProperties(source, target);
        return target;
    }

    /**
     * 拷贝List集合对象
     * @param source
     * @param clazz
     * @param <T>
     * @return
     */
    public static <T> List<T> copyProperties(List source, Class<T> clazz) {
        if (source == null) {
            return null;
        }

        List<T> targets = new ArrayList<>();
        source.forEach(item -> {
            T target = BeanUtils.instantiate(clazz);
            BeanUtils.copyProperties(item, target);
            targets.add(target);
        });

        return targets;
    }

    /**
     * 拷贝分页中的List的对象
     * @param pageInfo
     * @param clazz
     * @param <T>
     * @return
     */
    public static <T> PageInfo<T> copyProperties(PageInfo pageInfo, Class<T> clazz) {
        boolean flag = pageInfo == null || (pageInfo.getList() == null || pageInfo.getList().size() == 0);
        if (flag) {
            return pageInfo;
        }

        List<T> targets = new ArrayList<>();
        pageInfo.getList().forEach(item -> {
            T target = BeanUtils.instantiate(clazz);
            BeanUtils.copyProperties(item, target);
            targets.add(target);
        });
        pageInfo.setList(targets);

        return pageInfo;
    }

    public static <T> T pageInfo2TablePageInfo(PageInfo pageInfo, Class<T> clazz) {
        if (pageInfo == null) {
            return null;
        }

        T target = BeanUtils.instantiate(clazz);
        BeanUtils.copyProperties(pageInfo, target);

        return target;
    }

    public static <T> List<T> deepCopy(List<T> src)  {
        ByteArrayOutputStream byteOut = new ByteArrayOutputStream();
        ObjectOutputStream out = null;
        List<T> dest = null;
        try {
            out = new ObjectOutputStream(byteOut);
            out.writeObject(src);
            ByteArrayInputStream byteIn = new ByteArrayInputStream(byteOut.toByteArray());
            ObjectInputStream in = new ObjectInputStream(byteIn);
            dest = (List<T>) in.readObject();
        } catch (Exception e) {
            e.printStackTrace();
        }

        return dest;
    }
}

package com.pinshang.qingyun.order.model.shop;

import com.pinshang.qingyun.base.po.BaseSimplePO;

import javax.persistence.Entity;
import javax.persistence.Table;

/**
 * @Author: sk
 * @Date: 2020/9/29
 */
@Entity
@Table(name="t_md_shop_receive_setting_log")
public class ShopReceiveSettingLog extends BaseSimplePO {

    private Long shopId;

    /** DEFAULT '0'  收货方式 0 自动收货  1 手动收货 */
    private Integer receiveType;

    private String createName;

    public Long getShopId() {
        return shopId;
    }

    public void setShopId(Long shopId) {
        this.shopId = shopId;
    }

    public Integer getReceiveType() {
        return receiveType;
    }

    public void setReceiveType(Integer receiveType) {
        this.receiveType = receiveType;
    }

    public String getCreateName() {
        return createName;
    }

    public void setCreateName(String createName) {
        this.createName = createName;
    }
}

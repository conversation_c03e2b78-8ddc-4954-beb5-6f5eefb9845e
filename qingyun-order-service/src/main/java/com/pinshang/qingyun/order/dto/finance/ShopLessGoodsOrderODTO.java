package com.pinshang.qingyun.order.dto.finance;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2025/7/16 13:42
 */
@Data
public class ShopLessGoodsOrderODTO {

    @ApiModelProperty(value ="1-出库、2-入库")
    private Integer inoutType = 1;

    @ApiModelProperty(value = "业务类型:12-门店少货")
    private Integer businessType = 12;

    @ApiModelProperty(value = "业务单号")
    private String businessCode;

    @ApiModelProperty(value = "业务日期：yyyy-MM-dd")
    private String businessDate;

    @ApiModelProperty(value = "业务时间：yyyy-MM-dd HH:mm:ss")
    private Date businessTime;

    @ApiModelProperty(value = "业务明细ID")
    private Long businessOrderItemId;

    @ApiModelProperty("商品id")
    private Long commodityId;

    @ApiModelProperty("数量")
    private BigDecimal quantity;

    @ApiModelProperty("门店")
    private Long shopId;

    @ApiModelProperty("客户")
    private Long storeId;

    @ApiModelProperty("档口")
    private Long stallId;
}

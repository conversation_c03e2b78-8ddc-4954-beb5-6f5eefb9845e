package com.pinshang.qingyun.order.vo.consignment;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class BatchImportConsignmentSaleReturnOrderResultVO {

    @ApiModelProperty(value = "正确的导入数据")
    List<ImportConsignmentSaleReturnOrderVO> list;

    @ApiModelProperty(value = "错误信息")
    List<String> errors;
}

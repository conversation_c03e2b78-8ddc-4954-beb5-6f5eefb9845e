package com.pinshang.qingyun.order.job;

import com.pinshang.qingyun.box.utils.DateUtil;
import com.pinshang.qingyun.order.mapper.MdShopOrderSettingMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.quartz.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.quartz.SchedulerFactoryBean;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.List;


@Component
@Slf4j
public class GenerateCreateTakeCardOrderTasks {

    @Autowired
    private SchedulerFactoryBean schedulerFactoryBean;

    @Autowired
    private MdShopOrderSettingMapper mdShopOrderSettingMapper;

    @Value("${config.autoCreateOrder:0}")
    private int autoCreateOrder;

    /**
     * 每天凌晨 5分 时候开始种植任务
     */
    //@Scheduled(cron ="0 5 0 * * ?")
    public void scheduleJobs(){
        // 为了区分环境  0不种植任务  1 种植任务
       /* if(autoCreateOrder == 0){
            return;
        }
        String nowHour = DateUtil.getDateFormate(new Date(),"HH:mm");
        log.info("提货卡预约单提交订单开始种植任务--------------------------------------------------------------"+new Date());
        List<String> orderTimeList = mdShopOrderSettingMapper.getEndTimeList(nowHour);
        generateJob(orderTimeList);*/

    }


    public void generateJob(List<String> orderTimeList){
        // 为了区分环境  0不种植任务  1 种植任务
        /*if(autoCreateOrder == 0){
            return;
        }
        if(CollectionUtils.isNotEmpty(orderTimeList)){
            for(String orderTime : orderTimeList){
                Scheduler scheduler = schedulerFactoryBean.getScheduler();
                startJob(scheduler, orderTime);
            }
        }*/
    }

    private void startJob(Scheduler scheduler, String orderTime)  {
    	if(StringUtils.isBlank(orderTime)){
    		return;
    	}
        JobDataMap jobDataMap = new JobDataMap();
        jobDataMap.put("orderTime", orderTime);
        String jobName = DateUtil.getDateFormate(new Date(),"yyMMdd") +"job" + orderTime;
        String groupName = "groupTakeCard";


        // 定义一个 job，并绑定到 GenerateCreateTakeCardOrderJob.class
        // 这里并不会马上创建一个 GenerateCreateTakeCardOrderJob 实例，实例创建是在 scheduler 安排任务触发执行时创建的
        JobDetail jobDetail = JobBuilder.newJob(GenerateCreateTakeCardOrderJob.class).withIdentity(jobName, groupName).usingJobData(jobDataMap).build();

        String cronExp = buildCronExpression(orderTime);
        CronScheduleBuilder scheduleBuilder = CronScheduleBuilder.cronSchedule(cronExp);

        // 声明一个触发器
        CronTrigger cronTrigger = TriggerBuilder.newTrigger().withIdentity(jobName, groupName).withSchedule(scheduleBuilder).build();

        // 安排执行任务
        Date date = null;
        try {
            Boolean flag = scheduler.checkExists(cronTrigger.getKey());
            if(!flag){
                date = scheduler.scheduleJob(jobDetail, cronTrigger);
            }
        } catch (SchedulerException e) {
           log.error("提货卡预约单提交订单种植任务异常",e);
        }
        log.info("提货卡预约单提交订单种植任务的时间为----------------------------------------"+date);
    }




    private String buildCronExpression(String orderTime) {
        String[] times = orderTime.split(":");
        String  cronExpression = "0 " + times[1] + " " + times[0] + " * * ?";
        return cronExpression;
    }
}

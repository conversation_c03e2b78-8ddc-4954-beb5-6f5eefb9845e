package com.pinshang.qingyun.order.model.shop;

import lombok.Data;

import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;

@Entity
@Table(name = "t_md_shop_order_setting")
@Data
public class MdShopOrderSetting {
    @Id
    private Long id;

    private Long enterpriseId;//企业ID

    private Long commodityId;

    private String commodityCode;

    private String commodityName;//商品名称

    private String commoditySpec;//商品规格

    private Integer shopType;

    private Long supplierId;

    private String supplierCode;

    private String supplierName;//供应商名称

    private  String defaultSupplierBeginTime;

    private String defaultSupplierEndTime;

    private Long warehouseId;

    private  String defaultWarehouseBeginTime;

    private  String defaultWarehouseEndTime;

    private Integer logisticsModel;//物流模式

    private String deleveryTimeRange;//配送范围

    private Integer changePriceStatus;//是否可变价

    private Integer isNew;

    private Date createTime;

    private Date updateTime;

    private Long createId;

    private Long updateId;
}

package com.pinshang.qingyun.order.model.pf;

import java.math.BigDecimal;
import java.util.Date;

import javax.persistence.Entity;
import javax.persistence.Table;

import com.pinshang.qingyun.base.po.BaseIDPO;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Entity
@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "t_pf_shopping_cart")
public class PfShoppingCart extends BaseIDPO {
	private Long storeId;

    private Long commodityId;

    private BigDecimal quantity;

    private Long createId;
    
    private String createName;
    
    private Date createTime;
    
    private Long updateId;
    
    private String updateName;
    
    private Date updateTime; 
}

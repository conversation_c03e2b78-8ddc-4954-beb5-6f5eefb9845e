package com.pinshang.qingyun.order.model.settlement;

import javax.persistence.Entity;
import javax.persistence.Table;
import javax.persistence.Transient;

import com.pinshang.qingyun.base.po.BaseTimePO;

/**
 * 结账客户
 **/
@Entity
@Table(name = "t_settlement")
public class Settlement extends BaseTimePO {
	/**客户编码 **/
    private String customerCode;
	/**客户名称 **/
    private String customerName;
	/**客户助记码 **/
    private String customerAid;
	/**客户老编码 **/
    private String customerOldCode;
	/**客户描述 **/
    private String customerDescribe;
	/**客户状态(1:停用,0:启用) **/
    private String customerStatus;
	/**客户类型id **/
    private Long customerTypeId;
	/**客户等级id **/
    private Long customerLevelId;
	/**客户类别id **/
    private Long customerCategoryId;
	/**结账周期id **/
    private Long checkCycleId;
	/**账期(天数) **/
    private Integer paymentDays;
	/**税号 **/
    private String taxCode;
	/**开户行 **/
    private String openBank;
	/**账户 **/
    private String account;
	/**账户名 **/
    private String accountName;
	/**金蝶接口 **/
    private String kingdeeInterface;
	/**覆盖客户数量 **/
    private Integer coverCustomerNumber;
	/**创建人 **/
    private Long createId;
    private String remark;
    /**搜索模糊查询字段**/
    @Transient
    private String searchStr;

    public String getCustomerCode(){
        return customerCode;
    }
    public void setCustomerCode(String customerCode){
        this.customerCode = customerCode;
    }
    public String getCustomerName(){
        return customerName;
    }
    public void setCustomerName(String customerName){
        this.customerName = customerName;
    }
    public String getCustomerAid(){
        return customerAid;
    }
    public void setCustomerAid(String customerAid){
        this.customerAid = customerAid;
    }
    public String getCustomerOldCode(){
        return customerOldCode;
    }
    public void setCustomerOldCode(String customerOldCode){
        this.customerOldCode = customerOldCode;
    }
    public String getCustomerDescribe(){
        return customerDescribe;
    }
    public void setCustomerDescribe(String customerDescribe){
        this.customerDescribe = customerDescribe;
    }
    public String getCustomerStatus(){
        return customerStatus;
    }
    public void setCustomerStatus(String customerStatus){
        this.customerStatus = customerStatus;
    }
    public Long getCustomerTypeId(){
        return customerTypeId;
    }
    public void setCustomerTypeId(Long customerTypeId){
        this.customerTypeId = customerTypeId;
    }
    public Long getCustomerLevelId(){
        return customerLevelId;
    }
    public void setCustomerLevelId(Long customerLevelId){
        this.customerLevelId = customerLevelId;
    }
    public Long getCustomerCategoryId(){
        return customerCategoryId;
    }
    public void setCustomerCategoryId(Long customerCategoryId){
        this.customerCategoryId = customerCategoryId;
    }
    public Long getCheckCycleId(){
        return checkCycleId;
    }
    public void setCheckCycleId(Long checkCycleId){
        this.checkCycleId = checkCycleId;
    }
    public Integer getPaymentDays(){
        return paymentDays;
    }
    public void setPaymentDays(Integer paymentDays){
        this.paymentDays = paymentDays;
    }
    public String getTaxCode(){
        return taxCode;
    }
    public void setTaxCode(String taxCode){
        this.taxCode = taxCode;
    }
    public String getOpenBank(){
        return openBank;
    }
    public void setOpenBank(String openBank){
        this.openBank = openBank;
    }
    public String getAccount(){
        return account;
    }
    public void setAccount(String account){
        this.account = account;
    }
    public String getAccountName(){
        return accountName;
    }
    public void setAccountName(String accountName){
        this.accountName = accountName;
    }
    public String getKingdeeInterface(){
        return kingdeeInterface;
    }
    public void setKingdeeInterface(String kingdeeInterface){
        this.kingdeeInterface = kingdeeInterface;
    }
    public Integer getCoverCustomerNumber(){
    	if(this.coverCustomerNumber == null ){
    		return 0;
    	}
        return coverCustomerNumber;
    }
    public void setCoverCustomerNumber(Integer coverCustomerNumber){
        this.coverCustomerNumber = coverCustomerNumber;
    }
    public Long getCreateId(){
        return createId;
    }
    public void setCreateId(Long createId){
        this.createId = createId;
    }
	public String getSearchStr() {
		return searchStr;
	}
	public void setSearchStr(String searchStr) {
		this.searchStr = searchStr;
	}
	public String getRemark() {
		return remark;
	}
	public void setRemark(String remark) {
		this.remark = remark;
	}
	
    
}
package com.pinshang.qingyun.order.dto;

import com.pinshang.qingyun.base.page.Pagination;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @Author: liu<PERSON>hen
 * @DateTime: 2022/5/12 17:17
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class AutoPreOrderIDTO extends Pagination {
    private Long shopId;
    private String orderCode;
    @ApiModelProperty("申请人")
    private Long createId;
    private Integer status;
    private String deliveryStartTime;
    private String deliveryEndTime;
    private List<Long> shopIdList;

    /**
     * 是否内置用户
     */
    private Boolean isInternal;
    // 代销商户ID
    private Long consignmentId;
}

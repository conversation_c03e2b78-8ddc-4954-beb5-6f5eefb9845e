package com.pinshang.qingyun.order.model.order;

import com.pinshang.qingyun.base.enums.xd.XdPayTypeEnum;
import com.pinshang.qingyun.base.po.BasePO;
import com.pinshang.qingyun.order.mapper.entry.recharge.XdaPayBillEntry;
import lombok.Data;

import javax.persistence.Entity;
import javax.persistence.Table;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 客户结账信息表
 **/
@Entity
@Table(name = "t_store_recharge")
@Data
public class StoreRecharge extends BasePO {
	
	/** 客户id **/
	private Long storeId;
	/** 充值金额 **/
	private BigDecimal money;
	/** 客户账户当前余额 **/
	private BigDecimal storeBalance;
	/** 付款方式 **/
	private Long paymentMethod;
	/** 银行 **/
	private Long bank;
	/** 收款日期 **/
	private Date receiptDate;
	/** 银行日期 **/
	private Date bankDate;
	/** 收款类型 **/
	private Long receiptType;
	/** 金蝶编码 **/
	private String kingdeeCode;
	/** 备注 **/
	private String remark;
	/** 空余扣款字段,显示充值记录 **/
	private BigDecimal emptyMoney;
	/** 操作批次 **/
	private String operationBatch;
	/** 是否反还记录(0:否1:是)**/
	private Integer isReturn;

    public static StoreRecharge builder(XdaPayBillEntry payBill,BigDecimal storeBalance){
        Long paymentMethod = 0L;
        if(payBill.getPayType().equals(XdPayTypeEnum.ALIPAY.getCode())){
            paymentMethod = 9131078242860601319L;
        }else if(payBill.getPayType().equals(XdPayTypeEnum.WECHAT.getCode())){
            paymentMethod = 9131078242860601318L;
        }else if(payBill.getPayType().equals(XdPayTypeEnum.UNION_MINI.getCode())){
			paymentMethod = 9131078242888821829L;
		}else if(payBill.getPayType().equals(XdPayTypeEnum.UNION_PAY.getCode())){
            paymentMethod = 9131078242860601330L;
        }
        Date date = new Date();

        StoreRecharge vo = new StoreRecharge();
        vo.setCreateId(1L);
        vo.setCreateTime(date);
        vo.setUpdateId(1L);
        vo.setStoreId(payBill.getStoreId());
        vo.setMoney(payBill.getPayAmount());
        vo.setStoreBalance(storeBalance);
        vo.setReceiptDate(date);
        vo.setPaymentMethod(paymentMethod);
        vo.setReceiptType(9131078242860601331L);
        vo.setRemark("鲜达APP充值");
        return vo;
    }
}
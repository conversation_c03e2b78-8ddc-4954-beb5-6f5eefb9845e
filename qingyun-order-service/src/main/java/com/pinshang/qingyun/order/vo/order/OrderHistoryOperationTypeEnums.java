package com.pinshang.qingyun.order.vo.order;


/**
 * 
 * 类型 0 创建 1 修改 2取消 3删除 4 补货 5 配货
 * 
 * <AUTHOR>
 *
 */
/**
 * 订单日志-操作类型
 *
 * <AUTHOR>
 *
 * @date 2017年4月6日
 */
public enum OrderHistoryOperationTypeEnums {
	ORDER_CREATE(11, "新建"),
	ORDER_CANCEL(12, "取消"),
	ORDER_MODIFY(13, "修改"),
	COMMODITY_ADD(21, "添加"),
	COMMODITY_MODIFY(22, "修改"),
	COMMODITY_DELETE(23, "删除")
	;
	private Integer code;
	private String name;

	public Integer getCode() {
		return code;
	}

	OrderHistoryOperationTypeEnums(Integer code, String name) {
		this.code = code;
		this.name=name;
	}
	
	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public void setCode(Integer code) {
		this.code = code;
	}
}

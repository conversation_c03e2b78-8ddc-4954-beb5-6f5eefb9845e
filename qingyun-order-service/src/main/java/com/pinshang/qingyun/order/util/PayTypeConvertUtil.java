package com.pinshang.qingyun.order.util;

import com.pinshang.qingyun.base.configure.expand.QYAssert;
import com.pinshang.qingyun.base.enums.pay.PayTypeIntEnum;
import com.pinshang.qingyun.base.enums.xd.XdPayTypeEnum;

/**
 * @Author: liu<PERSON>hen
 * @DateTime: 2022/6/22 14:20
 */
public class PayTypeConvertUtil {
    public static XdPayTypeEnum payTypeTOXdaPayType(Integer payType) {
        PayTypeIntEnum payTypeIntEnum = PayTypeIntEnum.getByCode(payType);
        XdPayTypeEnum xdPayTypeEnum = XdPayTypeEnum.UNION_PAY;
        switch (payTypeIntEnum) {
            case UNIONPAY_ALI:
                xdPayTypeEnum = XdPayTypeEnum.ALIPAY;
                break;
            case UNIONPAY_WECHAT:
                xdPayTypeEnum = XdPayTypeEnum.WECHAT;
                break;
            case UNION_PAY:
                xdPayTypeEnum = XdPayTypeEnum.UNION_PAY;
                break;
            case UNIONPAY_MINI:
                xdPayTypeEnum = XdPayTypeEnum.UNION_MINI;
                break;
            default:
                QYAssert.isFalse("不支持此模式");
        }
        return xdPayTypeEnum;
    }

    public static PayTypeIntEnum xdPayTypeEnumToPayTypeIntEnum(XdPayTypeEnum xdPayTypeEnum) {
        PayTypeIntEnum payTypeIntEnum = PayTypeIntEnum.UNION_PAY;
        switch (xdPayTypeEnum) {
            case WECHAT:
                payTypeIntEnum = PayTypeIntEnum.UNIONPAY_WECHAT;
                break;
            case ALIPAY:
                payTypeIntEnum = PayTypeIntEnum.UNIONPAY_ALI;
                break;
            case UNION_PAY:
                payTypeIntEnum = PayTypeIntEnum.UNION_PAY;
                break;
            case UNION_MINI:
                payTypeIntEnum = PayTypeIntEnum.UNIONPAY_MINI;
                break;
            default:
                QYAssert.isFalse("不支持的支付渠道");
        }
        return payTypeIntEnum;
    }

}

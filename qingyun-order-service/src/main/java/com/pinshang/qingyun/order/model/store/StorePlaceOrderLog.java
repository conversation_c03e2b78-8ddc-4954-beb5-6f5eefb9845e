package com.pinshang.qingyun.order.model.store;

import com.pinshang.qingyun.base.po.BaseSimplePO;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Table;
import java.util.Date;

/**
 * @author: ch<PERSON><PERSON><PERSON>
 * @time: 2021/5/13 14:37
 */
@Data
@Table(name = "t_store_place_order_log")
@NoArgsConstructor
public class StorePlaceOrderLog extends BaseSimplePO {
    private Integer operateType;

    private Long storeId;

    private String storeCode;

    private String storeName;

    private Integer status;

    private Long deliverymanId;

    private Long distributionLineId;

    private Long lineGroupId;

    private String storeDurationBeginTime;

    private String storeDurationEndTime;

    private Integer orderNumber;

    private Integer xdaAppStatus;

    private Integer xsAppStatus;

    private String phoneNumber;
}

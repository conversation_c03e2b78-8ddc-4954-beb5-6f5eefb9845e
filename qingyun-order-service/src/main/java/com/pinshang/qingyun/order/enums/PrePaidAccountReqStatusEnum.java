package com.pinshang.qingyun.order.enums;

public enum PrePaidAccountReqStatusEnum {

	INIT(0), RECORDED(1), EXCEPTION(2), MARK_TO_ASYNC_RECORD(3);
	
	private final int code;
	private PrePaidAccountReqStatusEnum(int code) {
		this.code = code;
	}
	public int getCode() {
		return code;
	}
	public static PrePaidAccountReqStatusEnum fromCode(Integer code) {
		if (code == null) {
			return null;
		}
		for (PrePaidAccountReqStatusEnum s : PrePaidAccountReqStatusEnum.values()) {
			if (s.getCode() == code) {
				return s;
			}
		}
		return null;
	}
}

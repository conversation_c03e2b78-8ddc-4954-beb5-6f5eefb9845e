package com.pinshang.qingyun.order.dto.job;

import com.pinshang.qingyun.renderer.annotation.FieldRender;
import com.pinshang.qingyun.renderer.constant.RenderFieldHelper;
import com.pinshang.qingyun.renderer.enums.FieldTypeEnum;
import lombok.Data;
import org.apache.poi.sl.usermodel.TextRun;

import java.math.BigDecimal;

@Data
public class OrderBillStoreBalanceDTO {

    private Long storeId;

    @FieldRender(fieldType = FieldTypeEnum.STORE,fieldName = RenderFieldHelper.Store.storeName,keyName = "storeId")
    private String storeName;

    private BigDecimal storeBalance;
}

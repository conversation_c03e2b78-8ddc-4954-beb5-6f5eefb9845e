package com.pinshang.qingyun.order.dto.xda.v2;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.pinshang.qingyun.base.configure.codec.DecimalSerializerKeep2;
import com.pinshang.qingyun.xda.product.dto.commodityText.XdaCommodityInfoODTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 23/6/20/020 16:53
 */

@Data
@ApiModel
public class XdaOrderItemAppV2ODTO {
    @ApiModelProperty(value = "orderId", hidden = true)
    private Long orderId;

    @ApiModelProperty("商品名称")
    private String commodityName;

    @ApiModelProperty("商品id")
    private Long commodityId;

    @ApiModelProperty(value = "商品类型",hidden = true)
    private Integer commodityType;

    @ApiModelProperty("单位")
    private String commodityUnitName;

    @ApiModelProperty(" 支付后商品支付价")
    @JsonSerialize(using = DecimalSerializerKeep2.class)
    private BigDecimal commodityPrice;

    @ApiModelProperty(value ="是否有特价：0=无特价，1=普通特价")
    private Integer isSpecialPrice;

    @ApiModelProperty("特价")
    @JsonSerialize(using = DecimalSerializerKeep2.class)
    private BigDecimal specialPrice;

    @ApiModelProperty(value = "是否有特惠：0=无，1=有")
    private Integer isThPrice;

    @ApiModelProperty(value = "特惠价格")
    @JsonSerialize(using = DecimalSerializerKeep2.class)
    private BigDecimal thPrice;

    @ApiModelProperty("订货数量")
    private BigDecimal commodityNum;

    @ApiModelProperty(value = "订货数量- 解决类拷贝",hidden = true)
    private BigDecimal quantity;

    @ApiModelProperty("商品url")
    private String imageUrl;

    @ApiModelProperty("实发数量")
    private BigDecimal realDeliveryQuantity;

    @ApiModelProperty("实付金额")
    @JsonSerialize(using = DecimalSerializerKeep2.class)
    private BigDecimal realPrice;

    @ApiModelProperty("商品规格")
    private String commoditySpec;

    @ApiModelProperty("商品原价")
    @JsonSerialize(using = DecimalSerializerKeep2.class)
    private BigDecimal originalPrice;

    @ApiModelProperty("包装规格")
    private BigDecimal commodityPackageSpec;

    @ApiModelProperty("箱规")
    private BigDecimal salesBoxCapacity;

    @ApiModelProperty("特价Id")
    private Long pricePromotionId;

    @ApiModelProperty("优惠券分摊金额")
    private BigDecimal couponDiscountAmount;

    @ApiModelProperty("优惠券ID")
    private Long couponId;

    @ApiModelProperty("用户券ID")
    private Long couponUserId;

    public BigDecimal getCommodityNum(){
        return null != quantity ? quantity :BigDecimal.ZERO;
    }

    public BigDecimal getRealPrice(){
        if(null != quantity && null != commodityPrice){
            return quantity.multiply(this.commodityPrice).setScale(2,BigDecimal.ROUND_HALF_UP);
        }
        return null;
    }


    public static XdaOrderItemAppV2ODTO covert(XdaCommodityInfoODTO odto){
        XdaOrderItemAppV2ODTO xdaOrderItem4AppODTO = new XdaOrderItemAppV2ODTO();
        xdaOrderItem4AppODTO.setCommodityName(odto.getCommodityAppName());
        xdaOrderItem4AppODTO.setCommodityUnitName(odto.getCommodityUnitName());
        xdaOrderItem4AppODTO.setCommoditySpec(odto.getCommoditySpec());
        xdaOrderItem4AppODTO.setImageUrl(odto.getDefaultPicUrl());
        return xdaOrderItem4AppODTO;
    }
}

package com.pinshang.qingyun.order.enums;

import com.pinshang.qingyun.box.utils.EnumUtils;

public enum ShopReceiveTypeEnums {
    AUTOMATIC(0,"自动收货"),
    HANDLE(1,"手动收货");

    private Integer code;
    private String name;

    private ShopReceiveTypeEnums(Integer code, String name) {
        this.code = code;
        this.name = name;
    }

    public static ShopReceiveTypeEnums get(Integer code) {
        return (ShopReceiveTypeEnums) EnumUtils.fromEnumProperty(ShopReceiveTypeEnums.class, "code", code);
    }

    public static String getName(Integer code) {
        ShopReceiveTypeEnums o = get(code);
        return null == o ? "" : o.getName();
    }

    public Integer getCode() {
        return this.code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    public String getName() {
        return this.name;
    }

    public void setName(String name) {
        this.name = name;
    }
}

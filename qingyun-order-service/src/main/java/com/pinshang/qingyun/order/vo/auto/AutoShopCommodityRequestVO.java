package com.pinshang.qingyun.order.vo.auto;

import com.pinshang.qingyun.base.page.Pagination;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class AutoShopCommodityRequestVO extends Pagination {

    private Long shopId;

    private Long commodityId;

    private List<Long> commodityIdList;

    @ApiModelProperty("商品条码")
    private String barCode;

    @ApiModelProperty("后台大类id")
    private Long cate1;

    @ApiModelProperty("后台中类id")
    private Long cate2;

    @ApiModelProperty("后台小类id")
    private Long cate3;

    @ApiModelProperty("是否速冻产品")
    private Integer commodityIsQuickFreeze;

    @ApiModelProperty("否称重0-不称量,1-称重")
    private Integer isWeight;

    @ApiModelProperty("订货开始时间")
    private String startTime;

    @ApiModelProperty("订货截止时间")
    private String endTime;

    @ApiModelProperty("2 = 只展示不要求品项")
    private Integer showNoRequire;
}

package com.pinshang.qingyun.order.model.order;

import lombok.Data;

import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;

@Data
@Table(name="t_sale_return_order_pic")
public class SaleReturnOrderPic {
    /**主键ID*/
    @Id
    private Long id;

    /**退货单表主键ID*/
    private Long saleReturnOrderId;

    /**商品ID*/
    private Long commodityId;

    /** 类型：1-普通图片、2-长图、3-视频 */
    private Integer picType;

    /**URL*/
    private String picUrl;

    private Date createTime;
    private Long createId;

    public enum PicTypeEnums {
        PIC(1),
        LONG_PIC(2),
        VIDEO(3),
        ;
        private Integer code;
        PicTypeEnums(Integer code) {
            this.code = code;
        }
        public Integer getCode() {
            return code;
        }
    }
}

package com.pinshang.qingyun.order.model.order;

import com.pinshang.qingyun.base.po.BaseIDPO;
import com.pinshang.qingyun.base.po.BasePO;
import com.pinshang.qingyun.base.po.BaseTimePO;
import lombok.Data;

import javax.persistence.Entity;
import javax.persistence.Table;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Entity
@Data
@Table(name="t_sub_order_confirm")
public class SubOrderConfirm extends BaseIDPO {
	/** 子单ID */
	private Long subOrderId;

	/** 创建时间 */
	private Date createTime;

	public static List<SubOrderConfirm> initList(List<Long> subOrderIds){
		List<SubOrderConfirm> list = new ArrayList<>(subOrderIds.size());
		subOrderIds.forEach(soId->{
			SubOrderConfirm subOrderConfirm = new SubOrderConfirm();
			subOrderConfirm.setSubOrderId(soId);
			subOrderConfirm.setCreateTime(new Date());
			list.add(subOrderConfirm);
		});
		return list;
	}

}

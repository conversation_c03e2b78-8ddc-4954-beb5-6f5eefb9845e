package com.pinshang.qingyun.order.model.xda;

import com.pinshang.qingyun.order.dto.xda.XdaComplaintCommodityItemDTO;
import com.pinshang.qingyun.order.enums.ComplaintTypeEnum;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.math.BigDecimal;
import java.util.Date;

@Entity
@Table(name="t_complaint_commodity_list")
@Data
@NoArgsConstructor
public class XdaComplaintCommodityItem {
  @Id
  @Column(name = "uid")
  private String id;
  private Date createdAt;
  private Boolean enabled;
  private String enterpriseId;
  private String commodityCode;
  private Long commodityId;
  private String complaintId;
  /**
   * 投诉商品金额
   */
  private Double complaintMoney;

  /**
   * 投诉商品数量
   */
  private Double complaintNumber;
  private String createId;
  private Date updateTime;
  private Double checkMoney;
  private Double checkNumber;
  /**
   * 投诉类型:1少货（负值）;2退货（负值）;3多货（正值）;4服务;5质量
   */
  private Integer complaintType;
  private Double workshopDuty;
  private Double deliveryDuty;
  private Double customDuty;
  private Double shipDuty;
  private Double purchDuty;
  private Double supplyDuty;
  private Double companyDuty;
  /**
   * 商品订货数量
   */
  private Double commodityOrderNumber;
  private Double commodityPrice;
  private String complaintContent;

  /**
   * 问题类型
   */
  private Long questionType;

  public XdaComplaintCommodityItem(XdaComplaintCommodityItemDTO item, String itemId, String xdaComplaintId, Date d) {
    this.setId(itemId);
    this.setEnabled(Boolean.TRUE);
    this.setCommodityCode(item.getCommodityCode());
    this.setComplaintId(xdaComplaintId);
    this.setCommodityId(item.getCommodityId());
    this.setCommodityOrderNumber(item.getRealDeliveryQuantity().doubleValue());
    this.setComplaintMoney(item.getRealDeliveryQuantity().abs().multiply(item.getCommodityPrice()).doubleValue());
    this.setCommodityPrice(item.getCommodityPrice().doubleValue());
    this.setQuestionType(item.getQuestionType().longValue());
    this.setCreatedAt(d);
    this.setUpdateTime(d);
    // app端 投诉单默认为系统创建
    this.setCreateId("-1");
    this.setComplaintType(item.getComplaintType());
    this.setComplaintNumber(item.getRealReturnQuantity().abs().doubleValue());
    this.setComplaintMoney(item.getComplaintMoney().doubleValue());

  }
}

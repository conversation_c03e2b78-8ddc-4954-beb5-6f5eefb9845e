package com.pinshang.qingyun.order.enums;


import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;

/**
 * <AUTHOR>
 * @Version 0.1.0
 * @Date 2024/6/11 16:07
 * @Description -- 类说明 代售退货单状态：0＝取消，1＝待确认，2＝待审核，3＝已驳回，4＝已完成
 **/
public enum ConsignmentSaleReturnOrderStatusEnum implements BaseEnum{

    CANCELLED(0, "取消"),
    NEED_CONFIRM(1, "待确认"),
    NEED_CHECK(2, "待审核"),
    REJECTED(3, "已驳回"),
    COMPLETED(4, "已完成"),
    ;

    private int code;
    private String name;


    private ConsignmentSaleReturnOrderStatusEnum(int code, String name) {
        this.code = code;
        this.name = name;
    }

    @JsonValue
    public int getCode() {
        return this.code;
    }

    public String getName() {
        return this.name;
    }


    @JsonCreator
    public static ConsignmentSaleReturnOrderStatusEnum getByCode(Integer code) {
        if (code == null) {
            return null;
        } else {
            ConsignmentSaleReturnOrderStatusEnum[] var1 = values();
            int var2 = var1.length;
            for (int var3 = 0; var3 < var2; ++var3) {
                ConsignmentSaleReturnOrderStatusEnum value = var1[var3];
                if (value.getCode() == code) {
                    return value;
                }
            }
            return null;
        }
    }

    @Override
    public Integer convert() {
        return this.code;
    }
}

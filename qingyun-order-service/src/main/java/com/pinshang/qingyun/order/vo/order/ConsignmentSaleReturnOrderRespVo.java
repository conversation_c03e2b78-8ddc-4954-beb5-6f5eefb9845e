package com.pinshang.qingyun.order.vo.order;

import com.pinshang.qingyun.renderer.annotation.FieldRender;
import com.pinshang.qingyun.renderer.constant.RenderFieldHelper;
import com.pinshang.qingyun.renderer.enums.FieldTypeEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ConsignmentSaleReturnOrderRespVo {

    @ApiModelProperty(value = "退货单id")
    private Long id;

    /**
     * 代销供应商id
     */
    @ApiModelProperty(value = "代销供应商id")
    private Long supplierId;

    /**
     * 代销供应商名称
     */
    @ApiModelProperty(value = "代销供应商名称")
    @FieldRender(fieldType = FieldTypeEnum.SUPPLIER, fieldName = RenderFieldHelper.Supplier.supplierName, keyName = "supplierId")
    private String supplierName;

    /**
     * 客户id
     */
    @ApiModelProperty(value = "客户id")
    private Long storeId;

    /**
     * 客户编码
     */
    @ApiModelProperty(value = "客户编码")
    private String storeCode;

    /**
     * 门店id
     */
    @ApiModelProperty(value = "门店id")
    private Long shopId;

    /**
     *门店编码
     */
    @ApiModelProperty(value = "门店编码")
    private String shopCode;

    /**
     * 门店名称
     */
    @ApiModelProperty(value = "门店名称")
    private String shopName;

    /**
     * 退货单号
     */
    @ApiModelProperty(value = "退货单号")
    private String returnOrderCode;

    /**
     * 创建人
     */
    @ApiModelProperty(value = "创建人")
    private String createUser;

    /**
     * 退货时间
     */
    @ApiModelProperty(value = "退货时间")
    private Date returnOrderDate;

    /**
     * 确认人
     */
    @ApiModelProperty(value = "确认人")
    private String confirmUser;

    /**
     * 确认时间
     */
    @ApiModelProperty(value = "确认时间")
    private Date confirmOrderDate;

    /**
     * 退货状态
     */
    @ApiModelProperty(value = "退货状态")
    private Integer status;

    @ApiModelProperty(value = "退货状态具体名")
    private String statusName;

}

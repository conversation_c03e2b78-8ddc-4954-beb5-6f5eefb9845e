package com.pinshang.qingyun.order.dto.xda.v4;

import com.pinshang.qingyun.base.constant.XdaAppVersionConstant;
import com.pinshang.qingyun.order.dto.shopcart.v4.TdaDeliveryTimeRange;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @Date 2024/3/7 16:02
 */
@Data
public class XdaPreOrderV4IDTO extends TdaDeliveryTimeRange {
    @ApiModelProperty(value = "客户id", hidden = true)
    private Long storeId;
    @ApiModelProperty(value = "appCode", hidden = true)
    private String appCode;
    @ApiModelProperty("订单金额")
    private BigDecimal orderAmount;
    @ApiModelProperty("订货日期")
    private Date orderTime;

    @ApiModelProperty("普通商品数量+赠品数量")
    private BigDecimal commodityNum;

    @ApiModelProperty("品种合计")
    private Integer varietySum;

    private String appVersion;

    @ApiModelProperty("用户优惠券id\n" +
            "--传 null 或空值： 表示系统推荐最优优惠券。\n" +
            "--传 -1： 表示用户不使用优惠券。\n" +
            "--传 -2： 表示老版本不使用优惠券。\n" +
            "--传实际的 couponUserId 值： 表示指定使用该优惠券。")
    private Long couponUserId;

    @ApiModelProperty("是否指定了券(包括正反选),1-是  0-否")
    private Integer useCoupon;

    @ApiModelProperty("是否强制提交：0-否、1-是")
    private Integer forceStatus = 0;

    public Long getCouponUserId() {
        // 判断版本号，为空或者小于 164则为老版本
        if (StringUtils.isEmpty(appVersion) || appVersion.compareToIgnoreCase(XdaAppVersionConstant.XDA_VERSION_165) < 0) {
            couponUserId = -2L;
        }
        return couponUserId;
    }

}

package com.pinshang.qingyun.order.dto.xda.v2;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 23/6/20/020 16:13
 */
@Data
@ApiModel
public class XdaPreOrderV2IDTO {
    @ApiModelProperty(value = "客户id", hidden = true)
    private Long storeId;
    @ApiModelProperty(value = "appCode", hidden = true)
    private String appCode;
    @ApiModelProperty("订单金额")
    private BigDecimal orderAmount;
    @ApiModelProperty("订货日期")
    private Date orderTime;

    @ApiModelProperty("品种合计")
    private Integer varietySum;
}

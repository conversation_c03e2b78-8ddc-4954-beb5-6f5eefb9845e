package com.pinshang.qingyun.order.enums;

public enum StockType{
        DEPEND_STOCK(1,"依据大仓"),
        UNLIMITED(2,"不限量订货"),
        LIMIT(3,"限量供应");
        private Integer code;
        private String desc;
        StockType(Integer code, String desc) {
                this.code = code;
                this.desc = desc;
        }
        public Integer getCode() {
                return code;
        }

        public void setCode(Integer code) {
                this.code = code;
        }

        public String getDesc() {
                return desc;
        }

        public void setDesc(String desc) {
                this.desc = desc;
        }

        public static StockType getEnumByCode(Integer code){
                if(code == null){
                        return null;
                }
                for(StockType e : StockType.values()){
                        if(e.getCode().intValue() == code){
                                return e;
                        }
                }
                return null;
        }
}
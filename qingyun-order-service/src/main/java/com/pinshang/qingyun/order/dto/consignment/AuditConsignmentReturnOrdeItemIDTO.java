package com.pinshang.qingyun.order.dto.consignment;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class AuditConsignmentReturnOrdeItemIDTO {
    /**
     * id
     */
    @ApiModelProperty(value = "id")
    private Long id;

    /**
     * 审核商品数量
     */
    @ApiModelProperty("审核商品数量")
    private BigDecimal checkQuantity=BigDecimal.ZERO;

    /**
     * 驳回原因
     */
    @ApiModelProperty("驳回原因")
    private String rejectionReason;


}

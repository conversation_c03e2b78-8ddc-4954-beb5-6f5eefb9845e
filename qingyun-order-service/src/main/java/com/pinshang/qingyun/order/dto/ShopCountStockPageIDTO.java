package com.pinshang.qingyun.order.dto;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.pinshang.qingyun.base.page.Pagination;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @ClassName ShopCountStockPageIDTO
 * <AUTHOR>
 * @Date 2022/12/8 18:16
 * @Description ShopCountStockPageIDTO
 * @Version 1.0
 */
@Data
public class ShopCountStockPageIDTO extends Pagination {
    @ApiModelProperty("门店id")
    private Long shopId;

    @ApiModelProperty("部门code")
    private String orgCode;

    @ApiModelProperty("送货时间")
    private String beginTime;

    @ApiModelProperty("送货时间")
    private String endTime;

    @JsonIgnore
    @ApiModelProperty("门店id-后端使用")
    private List<Long> shopIdList;
}

package com.pinshang.qingyun.order.vo;

import com.pinshang.qingyun.base.page.Pagination;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.List;


/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2017/6/7.
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ShopReceiveHQVo extends Pagination {

    private String shopId;

    private String beginDate;

    private String endDate;

    private String subOrderCode;

    private String supplierStr;

    private Integer logisticsModel;

    private List<Integer> status;

    private String enterpriseId;

    private String storeId;

    private BigDecimal longitude;

    private BigDecimal latitude;

    private String supplierId;

    private Integer isReplenishment;//是否补货

    private String createUser;

    private String orderCode;

}

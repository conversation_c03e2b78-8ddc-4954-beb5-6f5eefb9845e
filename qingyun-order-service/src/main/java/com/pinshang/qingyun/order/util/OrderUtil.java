package com.pinshang.qingyun.order.util;

import com.pinshang.qingyun.box.utils.DateUtil;
import org.apache.commons.collections.CollectionUtils;

import java.lang.reflect.Field;
import java.text.ParseException;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

public class OrderUtil {
    /**
     * 获取送货日期
     * @param deleveryTimeRange
     * @return
     */
    public static String getOrderTime(String deleveryTimeRange){
        String day[] = deleveryTimeRange.split("-");
        Integer begin = Integer.parseInt(day[0]);

        Calendar calendar = Calendar.getInstance();
        if(begin >= 1 ){
            calendar.add(Calendar.DATE,begin);
        }else{
            calendar.add(Calendar.DATE,1);
        }
        return DateUtil.getDateFormate(calendar.getTime(),"yyyy-MM-dd");
    }


    /**
     *
     * @param stockOutTime
     * @return
     */
    public static boolean compareHHmmss(Date stockOutTime, Date orderTimeDate, int changePriceFinalHour) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(stockOutTime);
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        Date stockOutDate = calendar.getTime();

        if (stockOutDate.compareTo(orderTimeDate) < 0) {
            int hourForStockOutTime = DateUtil.getPartOfTime(stockOutTime, "hour");
            int minuteForStockOutTime = DateUtil.getPartOfTime(stockOutTime, "minute");
            int secondForStockOutTime = DateUtil.getPartOfTime(stockOutTime, "second");

            int secondsForStockOutTime = hourForStockOutTime * 60 * 60 + minuteForStockOutTime * 60 + secondForStockOutTime;
            int secondsChangePriceFinalTime = changePriceFinalHour * 60 * 60;
            if (secondsForStockOutTime < secondsChangePriceFinalTime) {
                return true;
            }
        }

        return false;
    }


    /**
     * 行转列
     * @param clazz
     * @param list
     * @param <T>
     * @return
     */
    public static <T> List convert(Class<T> clazz, List<T> list){
        if(CollectionUtils.isEmpty(list)){
            return new ArrayList();
        }
        List<List> result;
        Field[] fields = clazz.getDeclaredFields();
        result = new ArrayList<>(fields.length);
        for (int i = 0; i < fields.length; i++) {
            result.add(new ArrayList());
        }
        for (T t : list) {
            for (int i = 0; i < fields.length; i++) {
                List l = result.get(i);
                Field field = fields[i];
                field.setAccessible(true);
                try {
                    l.add(field.get(t));
                } catch (IllegalAccessException e) {
                    e.printStackTrace();
                }
            }
        }
        return result;
    }

}

package com.pinshang.qingyun.order.enums;
/*
 * 退货单状态
 * 状态: 0-取消, 1-待入库(待确认), 2-已入库（已确认）
 */
public enum SaleReturnStatusEnums {

	CANCEL("取消", 0),
	DEAL("待入库(待确认)", 1),
	ALREADY("已入库（已确认）", 2),
    DEALING("收货中", 3);
    private String name;
    private int code;

    SaleReturnStatusEnums(String name, int code) {
        this.name = name;
        this.code = code;
    }

    public static String getName(int code) {
        for (SaleReturnStatusEnums es : SaleReturnStatusEnums.values()) {
            if (code == es.getCode()) {
                return es.name;
            }
        }
        return null;
    }

    public String getName() {
        return name;
    }

    public int getCode() {
        return code;
    }
}

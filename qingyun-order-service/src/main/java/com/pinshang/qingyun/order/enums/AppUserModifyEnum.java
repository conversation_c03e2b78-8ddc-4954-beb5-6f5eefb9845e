package com.pinshang.qingyun.order.enums;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;

/**
 *
 * <AUTHOR>
 * @Date 2018/4/15 17:33
 */
public enum AppUserModifyEnum implements BaseEnum {
    NEEDLESS(0,"不需要"),
    NEED(1,"需要");

    private Integer code;
    private String desc;

    AppUserModifyEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    @Override
    public Integer convert() {
        return this.code;
    }
    @JsonValue
    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }
    @JsonCreator
    public static AppUserModifyEnum getEnumByCode(Integer code){
        if(code == null){
            return null;
        }
        for(AppUserModifyEnum e : AppUserModifyEnum.values()){
            if(e.getCode().intValue() == code){
                return e;
            }
        }
        return null;
    }
}

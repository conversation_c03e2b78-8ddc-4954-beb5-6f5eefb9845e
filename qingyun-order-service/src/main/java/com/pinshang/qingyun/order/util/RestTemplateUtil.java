package com.pinshang.qingyun.order.util;

import com.github.pagehelper.PageInfo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;

import java.nio.charset.StandardCharsets;
import java.util.Collections;

/**
 * <AUTHOR>
 * @date 2023/12/18/0018 13:37
 */
@Component
public class RestTemplateUtil {


        @Autowired
        private RestTemplate xRestTemplate;

        public <T> PageInfo<T> exchangeAsList(HttpMethod method, String url,Object idto,
                                                              ParameterizedTypeReference<PageInfo<T>> responseType) {
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            headers.setAcceptCharset(Collections.singletonList(StandardCharsets.UTF_8));
            HttpEntity requestEntity = new HttpEntity(idto,headers);
            return xRestTemplate.exchange(url, method, requestEntity, responseType).getBody();
        }

        public <T> PageInfo<T> postForList(String url,
                                                          Object idto,
                                                           ParameterizedTypeReference<PageInfo<T>> responseType) {
            return exchangeAsList(HttpMethod.POST, url, idto, responseType);
        }

}

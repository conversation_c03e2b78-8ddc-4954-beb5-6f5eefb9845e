/*
 * tramy.cn Inc.
 * Copyright (c) 2021-2024 All Rights Reserved.
 */
package com.pinshang.qingyun.order.dto.xda.tda;

import com.pinshang.qingyun.base.page.Pagination;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <p>
 * 大仓待确认退货单查询
 * </p>
 *
 * <AUTHOR> shenyang
 * @version : 1.0.0
 * @history : modify history
 * <author>              <time>              <version>              <desc>
 * @since : 2024/05/11 10:09
 */
@Data
public class WarehousePendingSearchIDTO extends Pagination {

    /**
     * 退货单状态：1-待审核、2-待取货、3-取货失败、4-待大仓确认、5-已取消、6-已完成、7-审核不通过
     */
    @ApiModelProperty(value = "退货单状态：4-待大仓确认、6-已确认")
    private Integer status;

    /**
     * 取货完成 开始时间
     */
    @ApiModelProperty(value = "取货完成 开始时间")
    private String pickBeginTime;

    /**
     * 取货完成 结束时间
     */
    @ApiModelProperty(value = "取货完成 结束时间")
    private String pickEndTime;

    /**
     * 确认完成 开始时间
     */
    @ApiModelProperty(value = "确认完成 开始时间")
    private String confirmBeginTime;

    /**
     * 确认完成 结束时间
     */
    @ApiModelProperty(value = "确认完成 结束时间")
    private String confirmEndTime;

    /**
     * 退货单序列号，前端展示用
     */
    @ApiModelProperty(value = "退货单序列号，前端展示用")
    private String returnOrderSeq;

    /**
     * 退货类型：1-客户退货，2-订单取消，3-配送失败
     */
    @ApiModelProperty(value = "退货类型：1-客户退货，2-订单取消，3-配送失败")
    private Integer returnType;

    /**
     * 收货人手机号
     */
    @ApiModelProperty(value = "收货人手机号")
    private String phone;

    /**
     * 来源单号，即相关的订单编号
     */
    @ApiModelProperty(value = "来源单号，即相关的订单编号")
    private String sourceOrderCode;

    /**
     * 司机ID（用户ID）
     */
    @ApiModelProperty(value = "司机ID")
    private Long driverId;

    /**
     * 运单编号
     */
    @ApiModelProperty(value = "运单号")
    private String waybillCode;

    /**
     * 退货订单类型：1-退货，2-少货，3-多货
     */
    private Integer returnOrderType;
}

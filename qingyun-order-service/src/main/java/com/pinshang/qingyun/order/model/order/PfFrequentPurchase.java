package com.pinshang.qingyun.order.model.order;

import java.math.BigDecimal;
import java.util.Date;

import javax.persistence.Entity;
import javax.persistence.Table;

import com.pinshang.qingyun.base.po.BaseIDPO;

import lombok.Getter;
import lombok.Setter;

@Entity
@Getter
@Setter
@Table(name = "t_pf_frequent_purchase")
public class PfFrequentPurchase extends BaseIDPO{
    private Long storeId;

    private Long commodityId;

    private Date updateTime;

    private BigDecimal quantity;
}
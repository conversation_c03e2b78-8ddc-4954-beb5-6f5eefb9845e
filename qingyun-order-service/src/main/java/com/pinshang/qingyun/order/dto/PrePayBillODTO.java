package com.pinshang.qingyun.order.dto;

import com.pinshang.qingyun.order.dto.xda.v4.XdaSaveOrderODTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Map;

/**
 * 待支付单
 * <AUTHOR>
 * @Date 2019/11/24 20:19
 */
@Data
@ApiModel
@NoArgsConstructor
@AllArgsConstructor
public class PrePayBillODTO extends XdaSaveOrderODTO {
    @ApiModelProperty("支付宝预支付订单")
    private Map<String, String> alipayPrePayBill;
    @ApiModelProperty("微信预支付订单")
    private Map<String, String> wechatPrePayBill;
    @ApiModelProperty("云闪付与支付订单")
    private Map<String, String> unionPayPrePayBill;
    @ApiModelProperty("订单号")
    private String billCode;
    @ApiModelProperty("小程序原始id")
    private String originalAppId;
}

package com.pinshang.qingyun.order.dto.pf;

import java.math.BigDecimal;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.pinshang.qingyun.base.configure.codec.DecimalSerializerKeep2;
import com.pinshang.qingyun.base.configure.codec.LongToStringKeep3;
import com.pinshang.qingyun.pf.product.dto.commodityText.PfCommodityInfoODTO;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;


/**
 * <AUTHOR>
 * @Date 2019/11/20 9:50
 */
@Data
@ApiModel
public class PfOrderItem4AppODTO {
    @ApiModelProperty(value = "orderId", hidden = true)
    @JsonSerialize(using = LongToStringKeep3.class)
    private Long orderId;
    @ApiModelProperty("商品名称")
    private String commodityName;
    @ApiModelProperty("商品id")
    private Long commodityId;
    @ApiModelProperty("单位")
    private String commodityUnit;
    @ApiModelProperty("销售单价")
    @JsonSerialize(using = DecimalSerializerKeep2.class)
    private BigDecimal commodityPrice;
    @ApiModelProperty("商品数量")
    private BigDecimal commodityNum;
    @ApiModelProperty("商品url")
    private String imageUrl;
    @ApiModelProperty("实发数量")
    private BigDecimal realDeliveryQuantity;
    @ApiModelProperty("商品类型")
    private Integer type;
    @ApiModelProperty("商品规格")
    private String commoditySpec;
    
    @ApiModelProperty("商品id，字符形式")
    public String getCommodityIdStr() {
    	return commodityId != null ? commodityId.toString() : null;
    }

    public void covert(PfCommodityInfoODTO odto){
        this.commodityName = odto.getCommodityAppName();
        this.commodityUnit = odto.getCommodityUnitName();
        this.commoditySpec = odto.getCommoditySpec();
        this.imageUrl = odto.getDefaultPicUrl();
    }

}

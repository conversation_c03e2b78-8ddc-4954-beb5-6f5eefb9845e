package com.pinshang.qingyun.order.enums;
//0-未生成do单, 1-已生成do单， 2-取消
@Deprecated
public enum SubOrderStatusEnums {

	UNDELIVERY("未生成do单", 0), DELIVERYED("已生成do单", 1), CANCEL("取消", 2);

    private String name;
    private int code;

    SubOrderStatusEnums(String name, int code) {
        this.name = name;
        this.code = code;
    }

    public static String getName(int code) {
        for (SubOrderStatusEnums es : SubOrderStatusEnums.values()) {
            if (code == es.getCode()) {
                return es.name;
            }
        }
        return null;
    }

    public String getName() {
        return name;
    }

    public int getCode() {
        return code;
    }
}

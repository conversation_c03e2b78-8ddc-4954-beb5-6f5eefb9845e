package com.pinshang.qingyun.order.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 *  查询待收货/收货中的客户
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class PrintOrderItemODTO {

    @ApiModelProperty("商品编码")
    private String  commodityCode;
    @ApiModelProperty("商品名称")
    private String  commodityName;

    @ApiModelProperty("规格")
    private String commoditySpec;

    @ApiModelProperty("订货数量")
    private BigDecimal quantity;

    @ApiModelProperty("发货数量")
    private BigDecimal realDeliveryQuantity;
}

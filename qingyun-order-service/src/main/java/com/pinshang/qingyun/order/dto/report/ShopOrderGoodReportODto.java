package com.pinshang.qingyun.order.dto.report;

import com.pinshang.qingyun.base.enums.shop.ShopTypeEnums;
import com.pinshang.qingyun.renderer.annotation.FieldRender;
import com.pinshang.qingyun.renderer.constant.RenderFieldHelper;
import com.pinshang.qingyun.renderer.enums.FieldTypeEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class ShopOrderGoodReportODto {
	private Long storeId;
	private Integer shopType;
	private Long shopId;
	@ApiModelProperty("所属部门")
	@FieldRender(fieldType = FieldTypeEnum.SHOP_ORG,fieldName = RenderFieldHelper.Org.parentOrgName,keyName = "shopId")
	private String orgName;
	@ApiModelProperty("门店编码")
	private String shopCode;
	@ApiModelProperty("门店名称")
	private String shopName;

	@ApiModelProperty("大类名称")
	@FieldRender(fieldType = FieldTypeEnum.COMMODITY_KIND,fieldName = RenderFieldHelper.CommodityKind.commodityFirstKindName,keyName = "commodityId")
	private String commodityFirstName;
	@ApiModelProperty("中类名称")
	@FieldRender(fieldType = FieldTypeEnum.COMMODITY_KIND,fieldName = RenderFieldHelper.CommodityKind.commoditySecondKindName,keyName = "commodityId")
	private String commoditySecondName;
	@ApiModelProperty("小类名称")
	@FieldRender(fieldType = FieldTypeEnum.COMMODITY_KIND,fieldName = RenderFieldHelper.CommodityKind.commodityThirdKindName,keyName = "commodityId")
	private String commodityThirdName;

	@ApiModelProperty("条形码")
	private String barCode;

	@ApiModelProperty("主副条码集合")
	private List<String> barCodeList;

	private Long commodityId;
	@ApiModelProperty("商品编码")
	private String commodityCode;
	@ApiModelProperty("商品名称")
	private String commodityName;
	@ApiModelProperty("规格")
	private String commoditySpec;
	@ApiModelProperty("单位")
	@FieldRender(fieldType = FieldTypeEnum.DICTIONARY,fieldName = RenderFieldHelper.Dictionary.optionName,keyName = "commodityUnitId")
	private String commodityUnit;

	private Long commodityUnitId;


	@ApiModelProperty("订货数量")
	private BigDecimal orderNum;
	@ApiModelProperty("发货数量")
	private BigDecimal deliveryNum;
	@ApiModelProperty("实发金额")
	private BigDecimal realDeliveryAmount;
	@ApiModelProperty("差异数量")
	private BigDecimal differNum;


	@ApiModelProperty("工厂")
	private String factoryName;
	@ApiModelProperty("生产组")
	private String workshopName;

	public String getShopTypeName() {
		return ShopTypeEnums.getName(this.shopType);
	}
}

package com.pinshang.qingyun.order.dto.consignment;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class SaveConsignmentSaleReturnOrderODTO {

    /**
     * 代销供应商id
     */
    @ApiModelProperty(value = "代销供应商id")
    private Long supplierId;

    /**
     * 客户id
     */
    @ApiModelProperty(value = "客户id")
    private Long storeId;

    /**
     * 商品ID
     */
    @ApiModelProperty(value = "商品ID")
    private Long commodityId;



    /** 退货数量*/
    @ApiModelProperty(value = "退货数量")
    private BigDecimal totalQuantity;

    /**
     * 退货价格
     */
    @ApiModelProperty(value = "退货价格")
    private BigDecimal price;

}

package com.pinshang.qingyun.order.dto;

import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class SaleReturnOrderIDTO{
    @ApiModelProperty("退货日期 开始日期")
    private String startDate;
    @ApiModelProperty("退货日期 结束日期")
    private String endDate;
    @ApiModelProperty("客户ID")
    private Long storeId;
    @ApiModelProperty("仓库ID")
    private Long warehouseId;
    private Long receiverId;

}

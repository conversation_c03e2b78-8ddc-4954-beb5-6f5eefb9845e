package com.pinshang.qingyun.order.vo.commodity;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class HandCartRspVO {

     @ApiModelProperty("当前修改或者删除数量")
     private BigDecimal quantity=BigDecimal.ZERO;

     @ApiModelProperty("份数")
     private BigDecimal shares=BigDecimal.ZERO;

     //品类总数
     @ApiModelProperty("品类总数")
     private BigDecimal varietyTotal;

     @ApiModelProperty("总数量")
     private BigDecimal totalQuantity=BigDecimal.ZERO;

     @ApiModelProperty("总价")
     private BigDecimal totalAmount=BigDecimal.ZERO;

     @ApiModelProperty("commodityId 不为空说明当前商品库存余量不足")
     private String commodityId;
     @ApiModelProperty("可用库存数量")
     private BigDecimal inventoryQuantity;
}

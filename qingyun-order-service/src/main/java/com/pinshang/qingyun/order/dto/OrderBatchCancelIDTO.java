package com.pinshang.qingyun.order.dto;

import com.pinshang.qingyun.order.model.order.User;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @ClassName OrderBatchCancelIDTO
 * <AUTHOR>
 * @Date 2024/6/27 15:37
 * @Description OrderBatchCancelIDTO
 * @Version 1.0
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class OrderBatchCancelIDTO {

    @ApiModelProperty("订单code列表")
    private List<String> orderCodeList;

    private User user;
}

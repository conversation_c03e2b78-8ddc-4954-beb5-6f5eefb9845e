package com.pinshang.qingyun.order.vo.purchase;

import com.pinshang.qingyun.base.page.Pagination;
import com.pinshang.qingyun.order.mapper.entry.purchase.ShopCommodityAppStatusEntry;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class ShopCommodityPurchaseStatusVo extends Pagination {

    @ApiModelProperty(position = 0,value = "门店类型 2- 鲜食店 5- 前置仓")
    private Integer shopType;

    @ApiModelProperty(position = 1,value = "门店id")
    private Long shopId;

    @ApiModelProperty(position = 2,value = "品类id")
    private Long categoryId;

    @ApiModelProperty(position = 3,value = "是否称重")
    private Integer isWeight;

    @ApiModelProperty(position = 4,value = "商品Id")
    private Long commodityId;

    @ApiModelProperty(position = 5,value = "商品条码")
    private String barCode;

    @ApiModelProperty(position = 6,value = "是否可采 1-可采,0-否,不可采")
    private Integer commodityPurchaseStatus;

    @ApiModelProperty(position = 7,value = "总部是否可售 0-不可售,1-可售")
    private Integer commodityState;

    @ApiModelProperty(position = 8,value = "app状态：0-上架，1-下架")
    private Integer appStatus;

    private Long userId;

    private List<String>  shopIdCommodityList;

}

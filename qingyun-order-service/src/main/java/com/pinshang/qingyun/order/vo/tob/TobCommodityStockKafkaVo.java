package com.pinshang.qingyun.order.vo.tob;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

@AllArgsConstructor
@NoArgsConstructor
@Data
public class TobCommodityStockKafkaVo {

    /**
     * 0= B端销售  10=通达销售
     */
    private Integer type;


    private Long commodityId;
    private Long warehouseId;
    /**
     * 1=依据大仓, 2=不限量订货,3=限量供应
     */
    private Integer stockType;
    /**
     * 是否有库存；0：无；1：有
     */
    private Integer stockStatus;

    /**
     * 限量值(stock_type=3 时才有值)
     */
    private Integer limitNumber;
    /**
     * 生效方式: 1=限总量，2=每天循环限量
     */
    private Integer effectType;

    /**
     * 消息时间
     */
    private Date currentTime;

}

package com.pinshang.qingyun.order.model.xda;


import com.pinshang.qingyun.base.po.BaseIDPO;
import com.pinshang.qingyun.renderer.annotation.FieldRender;
import com.pinshang.qingyun.renderer.constant.RenderFieldHelper;
import com.pinshang.qingyun.renderer.enums.FieldTypeEnum;
import lombok.Data;

import javax.persistence.Entity;
import javax.persistence.Table;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
@Entity
@Table(name = "t_xda_return_order")
public class XdaReturnOrder extends BaseIDPO {

    /**
     * 客户id
     */
    private Long storeId;

    /**
     * 退货单编号，唯一标识每个退货单
     */
    private String returnOrderCode;

    /**
     * 退货单序列号，前端展示用
     */
    private String returnOrderSeq;

    /**
     * 退货类型：1-客户退货，2-订单取消，3-配送失败
     */
    private Integer returnType;

    /**
     * 退货来源：1-app客户申请，2-品鲜后台投诉，3-订单取消，4-订单配送失败
     */
    private Integer returnSource;

    /**
     * 来源单号，即相关的订单编号
     */
    private Long sourceOrderId;

    /**
     * 来源单号，即相关的订单编号
     */
    private String sourceOrderCode;

    /**
     * 手机号
     */
    private String phone;

    /**
     * 物流中心id,pinshang_tms.t_tms_logistics_center.id
     */
    private Long logisticsCenterId;

    /**
     * 退货订单类型：1-退货，2-差异（少货/多货）
     */
    private Integer returnOrderType;

    /**
     * 退货单状态：1-待审核、2-待取货、3-取货失败、4-待大仓确认、5-已取消、6-已完成、7-审核不通过
     */
    private Integer status;

    /**
     * 配送批次
     */
    private String deliveryBatch;

    /**
     * 送货日期
     */
    private Date deliveryTime;

    /**
     * 送货地址
     */
    private String deliveryAddress;

    /**
     * 取货时间段
     */
    private String pickUpTimeRange;

    /**
     * 取货日期
     */
    private Date pickUpTime;

    /**
     * 送货/取货  完成时间
     */
    //TODO 是否修改字段名
    private Date deliveryEndTime;

    /**
     * 司机ID（用户ID）
     */
    private Long driverId;

    /**
     * 司机
     */
    @FieldRender(fieldType = FieldTypeEnum.USER, fieldName = RenderFieldHelper.User.realName, keyName = "driverId")
    private String driverName;

    /**
     * 运单编号
     */
    private String waybillCode;

    /**
     * 业务类型：10-通达销售
     */
    private Integer businessType;

    /**
     * 备注
     */
    private String remark;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 申请总金额
     */
    private BigDecimal totalApplyMoney;

    /**
     * 审核份数
     */
    private Integer totalCheckNumber;

    /**
     * 审核商品金额
     */
    private BigDecimal totalCheckMoney;

    /**
     * 审核商品数量
     */
    private BigDecimal totalCheckQuantity;

    /**
     * 审核时间
     */
    private Date checkTime;

    /**
     * 确认时间
     */
    private Date confirmTime;

    /**
     * 审核人id
     */
    private Long checkUserId;

    /**
     * 确认人id
     */
    private Long confirmUserId;
}

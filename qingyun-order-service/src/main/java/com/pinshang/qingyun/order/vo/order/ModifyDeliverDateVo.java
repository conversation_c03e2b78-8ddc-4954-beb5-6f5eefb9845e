package com.pinshang.qingyun.order.vo.order;

import com.pinshang.qingyun.base.page.Pagination;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * summary.
 * <p>
 * detailed description
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2018/7/25
 */
@Data
public class ModifyDeliverDateVo extends Pagination {

    /** 门店id */
    private Long shopId;

    /** 送货日期  */
    private String orderTime;

    /** 订单号 */
    private String orderCode;

    /** 配送批次(0:无需批次配送, 1:1配, 2:2配, 3:3配, 9:临时批次) */
    private Integer deliveryBatch;

    /** 创建人姓名或编号 */
    private String createId;

    /** T+2 */
    private String orderTime2;
}

package com.pinshang.qingyun.order.vo.mock;

import com.pinshang.qingyun.base.constant.QingyunConstant;
import com.pinshang.qingyun.base.enums.BusinessTypeEnums;
import com.pinshang.qingyun.box.utils.DateUtil;
import com.pinshang.qingyun.box.utils.IDGenerator;
import com.pinshang.qingyun.box.utils.StringUtil;
import com.pinshang.qingyun.order.model.order.Order;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class MockOrderVo {

    //送货日期
    private Date orderTime;

    private Integer deliveryBatch;

    private List<Long> storeIdList;

    private List<MockOrderItemVo> itemList;

    private Long userId;

    private Integer businessType;

    public Date getOrderTime() {
        return orderTime==null ? DateUtil.getNowDate():orderTime;
    }

    public Integer getDeliveryBatch() {
        return deliveryBatch==null?0:deliveryBatch;
    }

    public Integer getBusinessType() {
        return businessType==null ? BusinessTypeEnums.BIGSHOP_SALE.getCode() : businessType;
    }

    public static Order mockInit(MockOrderVo vo, Long storeId, BigDecimal orderAmount){
        Order order = new Order();
        order.setEnterpriseId(QingyunConstant.ENTERPRISE_ID);
        order.setOrderCode(IDGenerator.newOrderCode());
        order.setStoreId(storeId);
        order.setOrderTime(vo.getOrderTime());
        order.setDeliveryBatch(vo.getDeliveryBatch());
        order.setOrderType(1);
        order.setOrderStatus(0);
        order.setOrderRemark("PC端模拟下订单");
        order.setTotalAmount(orderAmount);
        order.setFinalAmount(orderAmount);
        order.setOrderAmount(orderAmount);
        order.setCreateId(vo.getUserId());
        order.setCreateTime(new Date());
        order.setUpdateId(vo.getUserId());
        order.setUpdateTime(new Date());
        return order;
    }
}

package com.pinshang.qingyun.order.manage.delivery.alert;

import com.pinshang.qingyun.common.dto.DictionaryODTO;
import com.pinshang.qingyun.common.service.DictionaryClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Optional;

@Component
public class AlertSwitch {

    @Autowired
    DictionaryClient dictionaryClient;

    private final String OPEN_FLAG = "1";

    public boolean isAlertEnabled(){
        DictionaryODTO dictionaryODTO = dictionaryClient.getDictionaryByCode("shortDeliveryWarn");
        String optionValue = Optional.ofNullable(dictionaryODTO.getOptionValue()).orElse("");
        return OPEN_FLAG.equals(optionValue);
    }
}

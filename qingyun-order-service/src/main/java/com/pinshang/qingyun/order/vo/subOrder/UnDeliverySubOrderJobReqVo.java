package com.pinshang.qingyun.order.vo.subOrder;

import com.pinshang.qingyun.base.enums.StoreTypeEnums;
import com.pinshang.qingyun.box.utils.DateUtil;
import com.pinshang.qingyun.order.constant.DictionaryCodeConstant;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;
import java.util.List;
import java.util.Map;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class UnDeliverySubOrderJobReqVo {
    //仓库(此字段用于测试)
    private Long warehouseId;
    //送货日期(此字段用于测试)
    private String orderTime;
    //鲜达客户类型
    private Long xdaStoreTypeId;
    private List<Long> directStoreTypeList;
    private List<Long> tobStoreTypeList;
    private List<Long> tobWarehouseList;
    private String currentTime;
    private Integer businessType;

    public void init(Map<String,List<Long>> dictionaryMap){
        this.setDirectStoreTypeList(dictionaryMap.get(DictionaryCodeConstant.STORETYPE_DIRECT_LIST));
        this.setTobStoreTypeList(dictionaryMap.get(DictionaryCodeConstant.STORETYPE_ToB_LIST));
        this.setTobWarehouseList(dictionaryMap.get(DictionaryCodeConstant.WAREHOUSE_ToB_LIST));
        this.setCurrentTime(DateUtil.getDateFormate(new Date(),"yyyy-MM-dd HH:mm"));
    }

    public  void initCurrentTime(){
        this.currentTime = DateUtil.getDateFormate(new Date(),"yyyy-MM-dd HH:mm:ss");
    }

    public Long getXdaStoreTypeId() {
        return StoreTypeEnums.XDA.getId();
    }

}

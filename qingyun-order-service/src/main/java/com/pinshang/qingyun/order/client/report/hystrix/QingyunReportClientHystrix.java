package com.pinshang.qingyun.order.client.report.hystrix;


import com.pinshang.qingyun.order.client.report.service.QingyunReportClient;
import com.pinshang.qingyun.order.mapper.entry.commodity.CommodityOrderSalesEntry;
import feign.hystrix.FallbackFactory;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public class QingyunReportClientHystrix implements FallbackFactory<QingyunReportClient> {
	private Logger logger = LoggerFactory.getLogger(getClass());
	@Override
	public QingyunReportClient create(Throwable throwable) {
		return new QingyunReportClient() {

			@Override
			public List<CommodityOrderSalesEntry> findMonthSalesQuanty(Long commodityId, Long storeId, String inBegin, String yesterday, int flag) {
				return null;
			}
		};
	}
}

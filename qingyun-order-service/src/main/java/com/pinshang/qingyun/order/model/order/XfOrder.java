package com.pinshang.qingyun.order.model.order;

import java.math.BigDecimal;
import java.util.Date;

import javax.persistence.Entity;
import javax.persistence.Table;

import lombok.Data;
import lombok.NoArgsConstructor;

import com.alibaba.fastjson.JSON;
import com.pinshang.qingyun.base.po.BaseEnterprisePO;
import com.pinshang.qingyun.box.utils.EnumUtils;
import com.pinshang.qingyun.order.dto.xforder.CreateXfOrderIDTO;

/**
 * 现付单（鲜达App现付下单初始信息）
 */
@Data
@NoArgsConstructor
@Entity
@Table(name = "t_xf_order")
public class XfOrder extends BaseEnterprisePO {
	// 客户ID
	private Long storeId;
	// 客户用户ID
	private Long userId;
	// AppCode
	private String appCode;

	// 付款流水编码(充值单号），XDAPayBill.billCode
	private String billCode;
	// 充值金额（现付金额）
	private BigDecimal payAmount;
	
	// 状态：0-待处理、1-处理成功、2-处理失败	—— 参见枚举XfOrder.XfOrderStatusEnums
	private Integer status;
	// 错误信息-给用户看的
	private String errorMsg;
	// 错误详细信息-研发看
	private String errorDetailMsg;
	
	// 请求参数
	private String reqJson;

	private Long orderId;
	
	public XfOrder(String billCode) {
		this.billCode = billCode;
	}
	
	public XfOrder(Long storeId, Integer status) {
		this.storeId = storeId;
		this.status = status;
	}
	
	public static XfOrder forInsert(CreateXfOrderIDTO idto, Date operateTime) {
		XfOrder model = new XfOrder();
		// model.id = id;
		model.storeId = idto.getStoreId();
		model.userId = idto.getUserId();
		model.appCode = idto.getAppCode();
		
		model.billCode = idto.getBillCode();
		model.payAmount = idto.getPayAmount();
		
		model.status = XfOrderStatusEnums.INIT.getCode();
		model.errorMsg = null;
		model.errorDetailMsg = null;
		
		model.reqJson = JSON.toJSONString(idto);
		
		model.setEnterpriseId(78L);
		model.setCreateId(0L);
		model.setUpdateId(0L);
		model.setCreateTime(operateTime);
		model.setUpdateTime(operateTime);
		return model;
	}
	
	public static XfOrder forUpdateStatus(Long id, Integer status, String errorMsg, String errorDetailMsg) {
		XfOrder model = new XfOrder();
		model.id = id;
		model.status = status;
		model.errorMsg = errorMsg;
		model.errorDetailMsg = errorDetailMsg;
		
		model.setUpdateTime(new Date());
		return model;
	}

	/**
	 * 现付单状态
	 */
	public enum XfOrderStatusEnums {
		INIT(0, "待处理"), //
		SUCCESS(1, "下单成功"), //
		FAILURE(2, "下单失败"), //
		;
		private Integer code;
		private String name;

		XfOrderStatusEnums(Integer code, String name) {
			this.code = code;
			this.name = name;
		}

		public String getName() {
			return name;
		}

		public Integer getCode() {
			return code;
		}

		public static XfOrderStatusEnums get(Integer code) {
			return EnumUtils.fromEnumProperty(XfOrderStatusEnums.class, "code",
					code);
		}

		public static String getName(Integer code) {
			XfOrderStatusEnums o = get(code);
			return null == o ? "" : o.name;
		}
	}

}
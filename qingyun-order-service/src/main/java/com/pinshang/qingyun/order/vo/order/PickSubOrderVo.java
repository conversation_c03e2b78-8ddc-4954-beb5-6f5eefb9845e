package com.pinshang.qingyun.order.vo.order;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2018/11/1 18:13.
 * @blog http://linuxsogood.org
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class PickSubOrderVo {
    private Long storeId;

    private Long subOrderId;

    /** 实际出库时间 */
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss" , timezone = "GMT+8")
    private Date stockOutTime;

    /** 消息类型 **/
    private String optionType;

    //出库单号
    private String stockOutOrderCode;
    private List<PickSubOrderItemRespVo> itemList;

    /**
     * 是否需要把消息发给结算
     */
    private Boolean isSendSettle;

    /**
     * 是否通达业务
     */
    private Boolean isTda = false;

    private List<Long> subOrderIds;
}

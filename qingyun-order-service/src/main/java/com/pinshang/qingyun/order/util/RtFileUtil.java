package com.pinshang.qingyun.order.util;

import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.net.MalformedURLException;
import java.net.URL;
import java.net.URLEncoder;
import java.text.SimpleDateFormat;
import java.util.Date;

public class RtFileUtil {

    private static String RK_FILE_URL = "http://static.tramy.cn/download/template/drfWarehouseReceipt.xlsx";
    private static String CK_FILE_URL = "http://static.tramy.cn/download/template/outstocktemp.xlsx";

    private static String rtWarehouse = "rtWarehouse_";

    private static String rtOutbound = "rtOutbound_";

    /**
     * 导出execl
     * @param response
     * @param newFile
     * @throws IOException
     */
    public static void exportExcel(Integer type,HttpServletResponse response, File newFile, int storeShortName) throws IOException{

        SimpleDateFormat sf = new SimpleDateFormat("yyyyMMdd");
        InputStream fis = new BufferedInputStream(new FileInputStream(newFile));

        byte[] buffer = new byte[fis.available()];
        fis.read(buffer);
        fis.close();
        response.reset();
        response.setContentType("text/html;charset=UTF-8");
        OutputStream toClient = new BufferedOutputStream(
                response.getOutputStream());
        response.setContentType("application/x-msdownload");
        String name = null;

        if(storeShortName == 1){
            name = "大润发";
        }else if(storeShortName == 2){
            name = "欧尚";
        }if(storeShortName == 3){
            name = "乐购";
        }
        String typeName = type == 1 ? "入库单_":"出库单_";
        String newName = URLEncoder.encode(name+ typeName + sf.format(new Date()) + ".xlsx", "UTF-8");
        response.addHeader("Content-Disposition", "attachment;filename=\"" + newName + "\"");
        response.addHeader("Content-Length", "" + newFile.length());
        toClient.write(buffer);
        toClient.flush();
    }


    /**
     * 读取excel模板，并复制到新文件中供写入和下载
     *
     * @return
     */
    public static File createNewFile(Integer type) {

        // 读取模板，并赋值到新文件************************************************************
        File newFile = null;
        try {
            // 文件模板路径
            String path = type == 1 ? RK_FILE_URL : CK_FILE_URL;

            String fileName = type == 1 ? rtWarehouse: rtOutbound;
            URL url = new URL(path);
            // 保存文件的路径
            String realPath = new String("d:\\rtMart");
            // 新的文件名
            String newFileName = fileName + System.currentTimeMillis() + ".xlsx";
            // 判断路径是否存在
            File dir = new File(realPath);
            if (!dir.exists()) {
                dir.mkdirs();
            }
            // 写入到新的excel
            newFile = new File(realPath, newFileName);

            newFile.createNewFile();
            // 复制模板到新文件
            fileChannelCopy(url, newFile);
        }catch (MalformedURLException e) {
            e.printStackTrace();
        } catch (Exception e) {
            e.printStackTrace();
        }
        return newFile;
    }

    /**
     * 复制文件
     *
     * @param url
     *            源文件
     * @param t
     *            复制到的新文件
     */

    public static void fileChannelCopy(URL url, File t) {
        try {
            InputStream in = null;
            OutputStream out = null;
            try {
                in = new BufferedInputStream(url.openStream(), 1024);
                out = new BufferedOutputStream(new FileOutputStream(t), 1024);
                byte[] buffer = new byte[1024];
                int len;
                while ((len = in.read(buffer)) != -1) {
                    out.write(buffer, 0, len);
                }
            } finally {
                if (null != in) {
                    in.close();
                }
                if (null != out) {
                    out.close();
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }


    /**
     * 下载成功后删除
     *
     * @param files
     */
    public static void deleteFile(File... files) {
        for (File file : files) {
            if (file.exists()) {
                file.delete();
            }
        }
    }
}

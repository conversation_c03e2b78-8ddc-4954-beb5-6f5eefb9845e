package com.pinshang.qingyun.order.dto.xda.v4;

import lombok.Data;

/**
 * <AUTHOR>
 * @Date 2024/3/13 14:22
 */
@Data
public class XdaSaveOrderODTO {

    private Boolean isSuccess;

    private String errorMsg;

    private Long orderId;

    public static XdaSaveOrderODTO Success(){
        XdaSaveOrderODTO xdaSaveOrderODTO = new XdaSaveOrderODTO();
        xdaSaveOrderODTO.setIsSuccess(Boolean.TRUE);
        return xdaSaveOrderODTO;
    }

    public static XdaSaveOrderODTO Error(String erroeMasg){
        XdaSaveOrderODTO xdaSaveOrderODTO = new XdaSaveOrderODTO();
        xdaSaveOrderODTO.setIsSuccess(Boolean.TRUE);
        xdaSaveOrderODTO.setErrorMsg(erroeMasg);
        return xdaSaveOrderODTO;
    }
}

package com.pinshang.qingyun.order.model.order;

import com.pinshang.qingyun.base.po.BaseEnterprisePO;
import lombok.Data;

import javax.persistence.Entity;
import javax.persistence.Table;
import java.math.BigDecimal;
import java.util.Date;

/*
 *门店直送预订单主表 
 */
@Entity
@Table(name="t_md_preorder")
@Data
public class PreOrder extends BaseEnterprisePO{
	private String orderCode;
	private Integer logisticsModel;
	private Integer orderStatus;
	private Integer receiveStatus;
	private BigDecimal totalPrice;
	private Date orderTime;
	private Long storeId;
	private Long purchaseOrderId;
	private String purchaseCode;
	private Long enterpriseId;
	private Long receiverId;
	private Date receiverTime;
	private String remark;
	private Long supplierId;
	private Long orderId;
	
	private Date approvingTime;
	private Long approvingId;
	private String realOrderCode;

	/** 所属公司ID **/
	private Long companyId;
	/** // 代销商户id */
	private Long consignmentId;

	/** 档口id */
	private Long stallId;
}

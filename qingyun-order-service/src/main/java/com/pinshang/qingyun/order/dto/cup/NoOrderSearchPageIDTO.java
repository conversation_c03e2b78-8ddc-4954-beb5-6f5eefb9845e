package com.pinshang.qingyun.order.dto.cup;

import com.pinshang.qingyun.base.page.Pagination;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * @Author: sk
 * @Date: 2024/7/16
 */
@Data
public class NoOrderSearchPageIDTO extends Pagination {

    @ApiModelProperty("送货日期开始 yyyy-MM-dd")
    private String startDate;
    @ApiModelProperty("送货日期结束 yyyy-MM-dd")
    private String endDate;

    @ApiModelProperty("客户编码")
    private String storeCode;
    @ApiModelProperty("客户名称")
    private String storeName;

    @ApiModelProperty("送货员id")
    private Long deliverymanId;

    @ApiModelProperty("客户类型id")
    private Long storeTypeId;

    @ApiModelProperty("线路组id")
    private Long lineGroupId;

    @ApiModelProperty("结账客户id")
    private Long settlementId;


    @ApiModelProperty("发货时间")
    private String lineSendDate;

    @ApiModelProperty("未订货备注")
    private String noOrderRemark;
}

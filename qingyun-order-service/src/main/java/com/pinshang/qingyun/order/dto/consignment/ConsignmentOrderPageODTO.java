package com.pinshang.qingyun.order.dto.consignment;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.pinshang.qingyun.renderer.annotation.FieldRender;
import com.pinshang.qingyun.renderer.constant.RenderFieldHelper;
import com.pinshang.qingyun.renderer.enums.FieldTypeEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * @Author: sk
 * @Date: 2024/6/7
 */
@Data
public class ConsignmentOrderPageODTO {

    @ExcelProperty(value = "送货日期")
    @ApiModelProperty("送货日期")
    private String orderTime;

    @ExcelProperty(value = "订单编号")
    @ApiModelProperty("订单编号")
    private String orderCode;

    @ExcelIgnore
    private Long storeId;

    @ExcelProperty(value = "客户编码")
    @FieldRender(fieldType = FieldTypeEnum.STORE, fieldName = RenderFieldHelper.Store.storeCode, keyName = "storeId")
    @ApiModelProperty("客户编码")
    private String storeCode;

    @ExcelIgnore
    private Long shopId;

    @ExcelProperty(value = "门店编码")
    @FieldRender(fieldType = FieldTypeEnum.SHOP, keyName = "shopId", fieldName = RenderFieldHelper.Shop.shopCode)
    @ApiModelProperty("门店编码")
    private String shopCode;

    @ExcelProperty(value = "门店名称")
    @FieldRender(fieldType = FieldTypeEnum.SHOP, keyName = "shopId", fieldName = RenderFieldHelper.Shop.shopName)
    @ApiModelProperty("门店名称")
    private String shopName;

    @ExcelProperty(value = "条形码")
    @FieldRender(fieldName = RenderFieldHelper.Commodity.barCode, fieldType = FieldTypeEnum.COMMODITY, keyName = "commodityId")
    @ApiModelProperty("条形码")
    private String barCode;

    @ExcelIgnore
    private Long commodityId;

    @ExcelProperty(value = "商品编码")
    @FieldRender(fieldName = RenderFieldHelper.Commodity.commodityCode, fieldType = FieldTypeEnum.COMMODITY, keyName = "commodityId")
    @ApiModelProperty("商品编码")
    private String commodityCode;

    @ExcelProperty(value = "商品名称")
    @FieldRender(fieldName = RenderFieldHelper.Commodity.commodityName, fieldType = FieldTypeEnum.COMMODITY, keyName = "commodityId")
    @ApiModelProperty("商品名称")
    private String commodityName;

    @ExcelProperty(value = "规格")
    @FieldRender(fieldName = RenderFieldHelper.Commodity.commoditySpec, fieldType = FieldTypeEnum.COMMODITY, keyName = "commodityId")
    @ApiModelProperty("规格")
    private String commoditySpec;

    @ExcelProperty(value = "计量单位")
    @FieldRender(fieldName = RenderFieldHelper.Commodity.commodityUnit, fieldType = FieldTypeEnum.COMMODITY, keyName = "commodityId")
    @ApiModelProperty("计量单位")
    private String commodityUnit;

    @ExcelProperty(value = "订货数量")
    @ApiModelProperty("订货数量")
    private BigDecimal requireQuantity;

    @ExcelProperty(value = "收货数量")
    @ApiModelProperty("收货数量")
    private BigDecimal realReceiveQuantity;

    @ExcelProperty(value = "审核数量")
    @ApiModelProperty("审核数量")
    private BigDecimal auditQuantity;

    @ExcelProperty(value = "差异数量")
    @ApiModelProperty("差异数量")
    private BigDecimal differQuantity;
}

package com.pinshang.qingyun.order.vo.order;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2023/12/13/0013 10:17
 */
@Data
public class OrderItemKafkaVo {

    @ApiModelProperty("商品id")
    private String commodityId;
    @ApiModelProperty("单价")
    private BigDecimal commodityPrice;
    @ApiModelProperty("订货数量")
    private BigDecimal commodityNum;
    @ApiModelProperty("商品总金额")
    private BigDecimal totalPrice;
    @ApiModelProperty("实发数量")
    private BigDecimal realQuantity;
    @ApiModelProperty("实发总金额")
    private BigDecimal realTotalPrice;

}

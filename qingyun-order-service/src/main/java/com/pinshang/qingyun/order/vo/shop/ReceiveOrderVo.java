package com.pinshang.qingyun.order.vo.shop;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;
import java.util.List;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2017/6/12.
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ReceiveOrderVo {

    private String storeId;

    @ApiModelProperty("订单id")
    private String subOrderId;

    @ApiModelProperty("订单编号")
    private String subOrderCode;

    @ApiModelProperty("收货商品明细")
    private List <Quantity> receiveQuantities;

    @ApiModelProperty("收货时间")
    private Date receiveTime;

    @ApiModelProperty("收货人id")
    private String receiveId;

    private String remark;

    private Integer logisticsModel;
    
    private String preOrderId;
    
    private Long createId;
    
    private Long shopId;

    private String orderCode;

}

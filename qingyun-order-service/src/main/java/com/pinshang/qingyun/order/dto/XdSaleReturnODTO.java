package com.pinshang.qingyun.order.dto;

import com.pinshang.qingyun.box.utils.DateUtil;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.Date;

/**
 * @Author: sk
 * @Date: 2020/4/9
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class XdSaleReturnODTO {

    private String id;

    private String orderCode;

    private String shopCode;
    private String shopName;
    private String commodityId;
    private String commodityCode;
    private String commodityName;
    private String commoditySpec;

    private BigDecimal realReturnQuantity;
    private BigDecimal price;
    private BigDecimal totalPrice;

    private Integer returnReason;
    private String returnReasonName;

    public String getReturnReasonName() {
        if(null != returnReason){
            if(returnReason.equals(10)){
                return "质量问题";
            }
            if(returnReason.equals(20)){
                return "已过保质期";
            }
            if(returnReason.equals(25)){
                return "临保";
            }
            if(returnReason.equals(30)){
                return "包装破损";
            }
            if(returnReason.equals(40)){
                return "条码不符";
            }
            if(returnReason.equals(50)){
                return "少货";
            }
            if(returnReason.equals(90)){
                return "其他";
            }
            if(returnReason.equals(1)){
                return "临保";
            }
            if(returnReason.equals(2)){
                return "过保";
            }
            if(returnReason.equals(3)){
                return "破损";
            }
            if(returnReason.equals(4)){
                return "丢失";
            }
            if(returnReason.equals(5)){
                return "损坏";
            }
        }
        return "";
    }

    private Date createTime;
    private String createTimeStr;

    public String getCreateTimeStr() {
        if(null != createTime ){
            return DateUtil.getDateFormate(createTime,"yyyy-MM-dd HH:mm:ss");
        }
        return "";
    }
    private Integer status;
    private String statusName;

    public String getStatusName() {
        if(null != status){
            if(status.equals(0)){
                return "取消";
            }
            if(status.equals(1)){
                return "待确认";
            }
            if(status.equals(2)){
                return "已确认";
            }
        }
        return "";
    }
}

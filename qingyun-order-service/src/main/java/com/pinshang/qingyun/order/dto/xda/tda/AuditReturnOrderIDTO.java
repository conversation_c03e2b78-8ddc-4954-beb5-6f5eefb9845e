/*
 * tramy.cn Inc.
 * Copyright (c) 2021-2024 All Rights Reserved.
 */
package com.pinshang.qingyun.order.dto.xda.tda;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * <p>
 * 退货单审核
 * </p>
 *
 * <AUTHOR> shenyang
 * @version : 1.0.0
 * @history : modify history
 * <author>              <time>              <version>              <desc>
 * @since : 2024/05/11 10:09
 */
@Data
public class AuditReturnOrderIDTO {
    /**
     * 退货单id
     */
    @ApiModelProperty(value = "退货单id")
    private Long id;

    /**
     * 取货时间段
     */
    @ApiModelProperty(value = "取货时间段")
    private String pickUpTimeRange;

    /**
     * 取货日期
     */
    @ApiModelProperty(value = "取货日期")
    private Date pickUpTime;

    /**
     * 审核明细数据
     */
    @ApiModelProperty(value = "审核明细数据")
    private List<AuditReturnOrdeItemIDTO> auditReturnOrderItems;

    /**
     * 审核结果:0-审核不通过,1-审核通过
     */
    @ApiModelProperty(value = "审核结果:0-审核不通过,1-审核通过")
    private Integer auditResult;

    /**
     * 审核人id
     */
    private Long checkUserId;

    @ApiModelProperty(value = "物流中心ID")
    @JsonSerialize(using= ToStringSerializer.class)
    private Long logisticsCenterId;

    @ApiModelProperty(value = "配送批次：1-一配、2-二配		—— 参见枚举： DeliveryBatchEnums")
    private Integer deliveryBatch;

}

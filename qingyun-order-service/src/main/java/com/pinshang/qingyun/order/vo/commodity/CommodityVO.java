package com.pinshang.qingyun.order.vo.commodity;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class CommodityVO{

	private Long commodityId;

	private String  commodityKey;

	private String barCode;

	/** 一级分类id */
	private Long    cateId1;
	/** 二级分类id */
	private Long    cateId2;
	/** 三级分类id */
	private Long    cateId3;

	private List<Long> commodityIdList;

	private Boolean newProduct;
}

package com.pinshang.qingyun.order.listener.tda;

import com.alibaba.fastjson.JSON;
import com.pinshang.qingyun.base.enums.BusinessTypeEnums;
import com.pinshang.qingyun.base.enums.OperateTypeEnums;
import com.pinshang.qingyun.base.enums.StoreBillTypeEnums;
import com.pinshang.qingyun.base.enums.xda.TdaOrderProcessStatusEnum;
import com.pinshang.qingyun.base.enums.xda.XdaOrderProcessStatusEunm;
import com.pinshang.qingyun.box.utils.BeanCloneUtils;
import com.pinshang.qingyun.box.utils.DateUtil;
import com.pinshang.qingyun.kafka.base.KafkaMessageWrapper;
import com.pinshang.qingyun.kafka.base.KafkaTopicConstant;
import com.pinshang.qingyun.order.dto.xda.tda.LogisticsDeliveryMessage;
import com.pinshang.qingyun.order.dto.xda.tda.ReturnOrderDetailODTO;
import com.pinshang.qingyun.order.dto.xda.tda.SaveReturnOrderODTO;
import com.pinshang.qingyun.order.dto.xda.tda.SyncReturnOrderIDTO;
import com.pinshang.qingyun.order.enums.tda.TDaReturnSourceEnum;
import com.pinshang.qingyun.order.enums.tda.TDaReturnStatusEnum;
import com.pinshang.qingyun.order.model.order.Order;
import com.pinshang.qingyun.order.model.xda.XdaReturnOrder;
import com.pinshang.qingyun.order.service.SubOrderService;
import com.pinshang.qingyun.order.service.tda.XdaReturnOrderService;
import com.pinshang.qingyun.order.service.tda.XdaReturnOrderServiceForAdmin;
import com.pinshang.qingyun.order.service.xda.CouponDayStatisticsService;
import com.pinshang.qingyun.order.service.xda.v4.OrderMtCouponService;
import com.pinshang.qingyun.order.service.xda.v4.XdaOrderV4Service;
import com.pinshang.qingyun.renderer.service.IRenderService;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

/**
 * <p>
 * 通达退货单监听物流消息
 * </p>
 *
 * <AUTHOR> shenyang
 * @version : 1.0.0
 * @history : modify history
 * <author>              <time>              <version>              <desc>
 * @since : 2024/05/13 18:02
 */
@Slf4j
@Component
public class TdaTmsListener {
    @Autowired
    private IRenderService renderService;

    @Autowired
    private XdaOrderV4Service xdaOrderV4Service;

    @Autowired
    private XdaReturnOrderService xdaReturnOrderService;

    @Autowired
    private XdaReturnOrderServiceForAdmin xdaReturnOrderServiceForAdmin;

    @Autowired
    private SubOrderService subOrderService;

    @Autowired
    private ApplicationContext applicationContext;

    @Autowired
    private RedissonClient redissonClient;

    @Autowired
    private OrderMtCouponService mtCouponService;
    @Autowired
    private CouponDayStatisticsService couponDayStatisticsService;

    /**
     * 通达退货单监听物流消息
     */
    @KafkaListener(
            topics = "${application.name.switch}" + KafkaTopicConstant.XDA_PROCESS_STATUS_CHANGE_TOPIC,
            errorHandler = "kafkaConsumerErrorHandler",
            containerFactory = "kafkaListenerContainerFactory"
    )
    public void deliveryFailureListener(String message) {
        log.info("====================监听到物流状态流转的message==" + message);
        try {
            KafkaMessageWrapper messageWrapper = JSON.parseObject(message, KafkaMessageWrapper.class);
            if (messageWrapper != null && messageWrapper.getData() != null) {
                log.info("data:{}", messageWrapper.getData().toString());
                LogisticsDeliveryMessage dto = JSON.parseObject(messageWrapper.getData().toString(), LogisticsDeliveryMessage.class);

                // 根据orderId 加锁60秒
                RLock lock = redissonClient.getLock("order:tdaTmsDelivery:" + dto.getOrderId());
                lock.lock(60L, TimeUnit.SECONDS);
                try {
                    //处理消息
                    TdaTmsListener tmsListener = applicationContext.getBean(TdaTmsListener.class);
                    tmsListener.handleMessage(dto);
                } finally {
                    if (lock.isLocked()) {
                        lock.unlock();
                    }
                }

            }
        } catch (Exception e) {
            log.error("处理物流状态流转的消息错误,异常:", e);
        }
    }

    /**
     * 处理消息
     */
    @Transactional(rollbackFor = Exception.class)
    public void handleMessage(LogisticsDeliveryMessage dto) {
        if (Objects.isNull(dto)) {
            return;
        }
        SaveReturnOrderODTO saveDto = BeanCloneUtils.copyTo(dto, SaveReturnOrderODTO.class);
        saveDto.setComplaintTotalMoney(dto.getOrderAmount());
        if (Objects.nonNull(dto.getDeliveryDate())) {
            saveDto.setDeliveryDate(DateUtil.getDateFormate(dto.getDeliveryDate(), "yyyy-MM-dd"));
        }
        Integer refType = dto.getRefType();
        if (Objects.equals(refType, 21)) {
            dto.setReturnOrderId(dto.getOrderId());
            saveDto.setReturnOrderId(dto.getOrderId());
        }
        Integer processStatus = dto.getProcessStatus();

        if (Objects.equals(processStatus, TdaOrderProcessStatusEnum.LOGISTICS_DELIVERY_FAIL.getCode())) {  // 物流配送失败
            //生成退货单
            handleGenerateReturnOrder(saveDto, TDaReturnSourceEnum.DELIVERY_FAILURE.getCode());
            subOrderService.handleRefund(dto, "线上订单配送失败", StoreBillTypeEnums.ONLINE_ORDER_DISPATCHING_ERROR.getCode());//业务类型通达销售时，且账号类型=预付费客户,配送失败完成原路返回退
            xdaOrderV4Service.updateOrderStatus(saveDto.getOrderId(), XdaOrderProcessStatusEunm.DELIVERY_FAIL.getCode(), dto);
        } else if (Objects.equals(processStatus, TdaOrderProcessStatusEnum.INTERCEPT_CONFIRM.getCode())) {  // 已拦截-待确认
            subOrderService.updateTdaDeliveryQuantity(dto);
            //生成退货单
            handleGenerateReturnOrder(saveDto, TDaReturnSourceEnum.ORDER_CANCEL.getCode());
        } else if (Objects.equals(processStatus, TdaOrderProcessStatusEnum.PICK_WAIT_CONFIRM.getCode())) {  // 已取货-待确认
            //更新退货单状态
            handleUpdateReturnOrder(dto, new Date(), TDaReturnStatusEnum.PENDING_WAREHOUSE_CONFIRMATION.getCode());
            //取货完成，同步到投诉表
            ReturnOrderDetailODTO returnOrderDetail = xdaReturnOrderServiceForAdmin.queryReturnOrderItemList(dto.getReturnOrderId());
            SyncReturnOrderIDTO syncReturnOrderIDTO = BeanCloneUtils.copyTo(returnOrderDetail, SyncReturnOrderIDTO.class);
            syncReturnOrderIDTO.setCheckTime(new Date());
            xdaReturnOrderServiceForAdmin.syncReturnOrder2Complaint(syncReturnOrderIDTO);
        } else if (Objects.equals(processStatus, TdaOrderProcessStatusEnum.PICK_FAIL.getCode())) {  // 取货失败
            //更新退货单状态
            handleUpdateReturnOrder(dto, null, TDaReturnStatusEnum.PICKUP_FAILED.getCode());
        } else if (Objects.equals(processStatus, TdaOrderProcessStatusEnum.STOCK_SEND_GOODS.getCode())) {  // 大仓执行发货
            xdaOrderV4Service.updateOrderStatus(saveDto.getOrderId(), XdaOrderProcessStatusEunm.OUT_STOCKING.getCode(), dto);
        } else if (Objects.equals(processStatus, TdaOrderProcessStatusEnum.LOGISTICS_PICK_COMPLETE.getCode())) {  // 物流分拣完成
            xdaOrderV4Service.updateOrderStatus(saveDto.getOrderId(), XdaOrderProcessStatusEunm.WAITING_COLLECT.getCode(), dto);
        } else if (Objects.equals(processStatus, TdaOrderProcessStatusEnum.LOGISTICS_COLLECT_COMPLETE.getCode())) { //物流揽收完成
            subOrderService.updateTdaDeliveryQuantity(dto);//业务类型通达销售时，且账号类型=预付费客户 配送成功处理短交退款
            xdaOrderV4Service.updateOrderStatus(saveDto.getOrderId(), XdaOrderProcessStatusEunm.WAITING_DELIVERY.getCode(), dto);
        } else if (Objects.equals(processStatus, TdaOrderProcessStatusEnum.LOGISTICS_DELIVERY_COMPLETE.getCode())) { // 物流配送完成
            subOrderService.handleShotDelivery(dto);//业务类型通达销售时，且账号类型=预付费客户,配送成功处理短交退款
            xdaOrderV4Service.updateOrderStatus(saveDto.getOrderId(), XdaOrderProcessStatusEunm.DELIVERY_COMPLETED.getCode(), dto);

            // 对于B端全国类型的订单，配送完成后同步订单数据给结算
            subOrderService.sendBCountrySettleMsgOrJobSend(dto);

        }else if (Objects.equals(processStatus, TdaOrderProcessStatusEnum.REAL_DELIVERY_EMPTY.getCode())) { // 大仓实发全是0，物流揽收后发消息
            subOrderService.updateTdaDeliveryQuantity(dto);//实发全部更新为0
            subOrderService.handleRefund(dto, "线上订单实发全是0,取消订单", StoreBillTypeEnums.ONLINE_ZERO_CANCEL.getCode());//业务类型通达销售时，且账号类型=预付费客户,配送失败完成原路返回退
            // 大仓实发全是0，物流揽收后发消息.进行退款
            xdaOrderV4Service.updateOrderStatus(saveDto.getOrderId(), XdaOrderProcessStatusEunm.CANCEL.getCode(), dto);
            //返还优惠券
            mtCouponService.refundCoupon(saveDto.getOrderId(), dto.getStoreId());
            // 查询订单优惠券信息，实时维护优惠券统计表
            couponDayStatisticsService.saveOrUpdateCouponDayStatistics(saveDto.getOrderId(), OperateTypeEnums.删除.getCode());
        }
    }

    /**
     * 生成退货单
     */
    private void handleGenerateReturnOrder(SaveReturnOrderODTO saveDto, Integer returnSource) {
        saveDto.setReturnSource(returnSource);
        xdaReturnOrderService.generateReturnOrder(saveDto);
    }

    /**
     * 更新退货单状态
     */
    private void handleUpdateReturnOrder(LogisticsDeliveryMessage dto, Date deliveryEndTime, Integer status) {
        if (Objects.isNull(dto.getReturnOrderId())) {
            return;
        }
        XdaReturnOrder returnOrder = new XdaReturnOrder();
        returnOrder.setId(dto.getReturnOrderId());
        returnOrder.setStatus(status);
        returnOrder.setDeliveryEndTime(deliveryEndTime);
        returnOrder.setSourceOrderId(dto.getOrderId());
        returnOrder.setLogisticsCenterId(dto.getLogisticsCenterId());
        returnOrder.setDeliveryBatch(Objects.toString(dto.getDeliveryBatch(), null));
        returnOrder.setDriverId(dto.getDriverId());
        returnOrder.setWaybillCode(dto.getWaybillCode());
        renderService.render(returnOrder, "handleUpdateReturnOrder");
        xdaReturnOrderServiceForAdmin.updateReturnOrder(returnOrder);
    }

}

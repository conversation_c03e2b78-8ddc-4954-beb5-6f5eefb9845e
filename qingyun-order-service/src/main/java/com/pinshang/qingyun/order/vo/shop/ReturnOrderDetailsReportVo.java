package com.pinshang.qingyun.order.vo.shop;

import com.pinshang.qingyun.base.page.Pagination;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper=false)
public class ReturnOrderDetailsReportVo extends Pagination{
	/**
	 * 
	 */
	private static final long serialVersionUID = 1583432760070473359L;
	private Date createTime;
	private String orderCode;
	private Long commodityFirstId;
	private String cateName;
	private String barCode;
	private String barCodes;
	private String commodityCode;
	private String commodityName;
	private String commoditySpec;
	private BigDecimal price;
	private BigDecimal returnQuantity;
	private BigDecimal realReturnQuantity;
	//退货原因
	private Integer returnReason;

	private Long storeId;
	private Long shopId;
	private String orderTimeStartStr;
	private String orderTimeEndStr;
	private String searchWord;
	//代销商id
	private Long consignmentId;
	private Long stallId;
	private List<Long> stallIdList;
}

package com.pinshang.qingyun.order.vo.order;

import com.pinshang.qingyun.order.mapper.entry.stall.StallEntry;

import java.math.BigDecimal;

/*
 * 添加购物车
 */
public class ShoppingCartVo extends StallEntry {
	private Long commodityId;
	private Long storeId;
	private Long enterpriseId;
	//删除shoppingCart整个
	private Long shoppingCartId;
	//删除shoppingCartItem
	private Long shoppingCartItemId;
	
	//数量
	private BigDecimal quantity;
	
	private Long createId;

	/**
	 * 是否内置用户
	 */
	private Boolean isInternal;

	private String orderTime;
	private String deliveryBatch;

	private Integer orderType;
	/** 档口id */
	private Long stallId;

	public Long getStallId() {
		return stallId;
	}

	public void setStallId(Long stallId) {
		this.stallId = stallId;
	}

	public Integer getOrderType() {
		return orderType;
	}

	public void setOrderType(Integer orderType) {
		this.orderType = orderType;
	}

	public String getOrderTime() {
		return orderTime;
	}

	public void setOrderTime(String orderTime) {
		this.orderTime = orderTime;
	}

	public String getDeliveryBatch() {
		return deliveryBatch;
	}

	public void setDeliveryBatch(String deliveryBatch) {
		this.deliveryBatch = deliveryBatch;
	}

	public Long getCommodityId() {
		return commodityId;
	}
	public void setCommodityId(Long commodityId) {
		this.commodityId = commodityId;
	}
	public Long getStoreId() {
		return storeId;
	}
	public void setStoreId(Long storeId) {
		this.storeId = storeId;
	}
	public Long getEnterpriseId() {
		return enterpriseId;
	}
	public void setEnterpriseId(Long enterpriseId) {
		this.enterpriseId = enterpriseId;
	}
	public Long getCreateId() {
		return createId;
	}
	public void setCreateId(Long createId) {
		this.createId = createId;
	}
	public BigDecimal getQuantity() {
		return quantity;
	}
	public void setQuantity(BigDecimal quantity) {
		this.quantity = quantity;
	}
	public Long getShoppingCartId() {
		return shoppingCartId;
	}
	public void setShoppingCartId(Long shoppingCartId) {
		this.shoppingCartId = shoppingCartId;
	}
	public Long getShoppingCartItemId() {
		return shoppingCartItemId;
	}
	public void setShoppingCartItemId(Long shoppingCartItemId) {
		this.shoppingCartItemId = shoppingCartItemId;
	}

	public Boolean getInternal() {
		return isInternal;
	}

	public void setInternal(Boolean internal) {
		isInternal = internal;
	}

}

package com.pinshang.qingyun.order.dto.cup;



import com.pinshang.qingyun.base.enums.storage.StockTypeEnum;
import com.pinshang.qingyun.order.enums.OrderModeType;
import com.pinshang.qingyun.order.enums.ProductTypeEnums;
import com.pinshang.qingyun.order.model.order.OrderListGift;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;

import java.io.Serializable;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;

public class OrderDto implements Serializable{

	private Long id;
	private static final long serialVersionUID = 1L;
	private String orderTime;
	private String orderRemark;
	private Long storeId;
	/** 企业id **/
	private Long enterpriseId;
	private Long userId;
	/** 打印类型(1：本地,2：送货员,3：不打印) */
	private Integer printType;
	/** 打印份数 */
	private Integer printNum;
	/** 0:普通订单 1：补货订单 **/
	private Integer modeType;
	/**
	 * 配送费
	 */
	private BigDecimal freightAmount;
	
	
	
	private String orderNo;
	private Date orderDate;
	

	private List<OrderItemDto> items = new ArrayList<>();
	private List<OrderItemDto> giftItems = new ArrayList<>();
	private List<OrderListGift> orderListGifts = new ArrayList<>();
    /***
     * 商品 大仓库存 库存不足map  用户页面显示
     * key 商品id  value 库存余量
     */
	private Map<String,String> commodityUnderstockMap= new HashMap<>();


	private Integer deliveryBatch;

	private Long logisticsCenterId;

	private String logisticsCenter;

	private Integer businessType;

	private String deliveryTimeRange;

	/**订单截止时间,根据当前客户截止时间与订单中的最晚商品截止时间得来;订单取消与是否进大仓时使用**/
	private Date orderDurationTime;

	@Getter
	private String logisticsCarrierCode;

	@Getter
	@ApiModelProperty("是否计划订单 0：否 1：是")
	private Integer planOrderType;

	/** 冻结返回的商品仓库信息 key：商品id  value: 仓库id */
	@Getter
	private Map<Long, Long> commWarehouseMap;

	public void setCommWarehouseMap(Map<Long, Long> commWarehouseMap) {
		this.commWarehouseMap = commWarehouseMap;
	}

	public void setPlanOrderType(Integer planOrderType) {
		this.planOrderType = planOrderType;
	}

	public void setLogisticsCarrierCode(String logisticsCarrierCode) {
		this.logisticsCarrierCode = logisticsCarrierCode;
	}

	public OrderDto() {
    }

    public OrderDto(Map<String, String> commodityUnderstockMap) {
        this.commodityUnderstockMap = commodityUnderstockMap;
    }

	public Date getOrderDurationTime() {
		return orderDurationTime;
	}

	public void setOrderDurationTime(Date orderDurationTime) {
		this.orderDurationTime = orderDurationTime;
	}

	public Integer getDeliveryBatch() {
		return deliveryBatch;
	}

	public void setDeliveryBatch(Integer deliveryBatch) {
		this.deliveryBatch = deliveryBatch;
	}

	public Long getLogisticsCenterId() {
		return logisticsCenterId;
	}

	public void setLogisticsCenterId(Long logisticsCenterId) {
		this.logisticsCenterId = logisticsCenterId;
	}

	public String getLogisticsCenter() {
		return logisticsCenter;
	}

	public void setLogisticsCenter(String logisticsCenter) {
		this.logisticsCenter = logisticsCenter;
	}

	public Integer getBusinessType() {
		return businessType;
	}

	public void setBusinessType(Integer businessType) {
		this.businessType = businessType;
	}

	public String getDeliveryTimeRange() {
		return deliveryTimeRange;
	}

	public void setDeliveryTimeRange(String deliveryTimeRange) {
		this.deliveryTimeRange = deliveryTimeRange;
	}

	public void addGiftItem(OrderItemDto itemDto){
		this.giftItems.add(itemDto);
	}
	public void addItem(OrderItemDto itemDto) {
		this.items.add(itemDto);
	}

	public void addItemList(List<OrderItemDto> itemDtoList) {
		this.items.addAll(itemDtoList);
	}

	public BigDecimal giftAmount(){
		BigDecimal result = BigDecimal.ZERO;
		for (OrderItemDto itemDto : giftItems) {
			result = result.add(itemDto.amount());
		}
		return result;
	}
	
	public BigDecimal amount() {
		BigDecimal result = BigDecimal.ZERO;
		for (OrderItemDto itemDto : items) {
			result = result.add(itemDto.amount());
		}
		return result;
	}

	public BigDecimal originalAmount() { // 原始金额
		BigDecimal result = BigDecimal.ZERO;
		for (OrderItemDto itemDto : items) {
			result = result.add(itemDto.originalAmount());
		}
		return result;
	}

	public BigDecimal promotionAmount() { // 促销金额
		BigDecimal result = BigDecimal.ZERO;
		for (OrderItemDto itemDto : items) {
			result = result.add(itemDto.promotionAmount());
		}
		return result;
	}

	public String getOrderNo() {
		return orderNo;
	}
	public void setOrderNo(String orderNo) {
		this.orderNo = orderNo;
	}
	public String getOrderTime() {
		return orderTime;
	}

	public void setOrderTime(String orderTime) {
		this.orderTime = orderTime;
	}

	public List<OrderItemDto> getItems() {
		return items;
	}

	public void setItems(List<OrderItemDto> items) {
		this.items = items;
	}

	public Long getEnterpriseId() {
		return enterpriseId;
	}

	public void setEnterpriseId(Long enterpriseId) {
		this.enterpriseId = enterpriseId;
	}

	public Long getUserId() {
		return userId;
	}

	public void setUserId(Long userId) {
		this.userId = userId;
	}

	public Integer getPrintType() {
		return printType;
	}

	public void setPrintType(Integer printType) {
		this.printType = printType;
	}

	public Integer getPrintNum() {
		return printNum == null ? 1:printNum;
	}

	public void setPrintNum(Integer printNum) {
		this.printNum = printNum;
	}

	public Long getStoreId() {
		return storeId;
	}

	public void setStoreId(Long storeId) {
		this.storeId = storeId;
	}

	public String getOrderRemark() {
		return orderRemark;
	}

	public void setOrderRemark(String orderRemark) {
		this.orderRemark = orderRemark;
	}

	public Integer getModeType() {
		return modeType==null? OrderModeType.ORDER.getCode():modeType;
	}

	public void setModeType(Integer modeType) {
		this.modeType = modeType;
	}

	public List<OrderItemDto> getGiftItems() {
		return giftItems;
	}

	public void setGiftItems(List<OrderItemDto> giftItems) {
		this.giftItems = giftItems;
	}
	public Date getOrderDate() {
		return orderDate;
	}
	public void setOrderDate(Date orderDate) {
		this.orderDate = orderDate;
	}

	public List<OrderListGift> getOrderListGifts() {
		return orderListGifts;
	}

	public void setOrderListGifts(List<OrderListGift> orderListGifts) {
		this.orderListGifts = orderListGifts;
	}

	public BigDecimal getFreightAmount() {
		return freightAmount == null ? BigDecimal.ZERO : freightAmount;
	}

	public void setFreightAmount(BigDecimal freightAmount) {
		this.freightAmount = freightAmount;
	}

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

    public Map<String, String> getCommodityUnderstockMap() {
        return commodityUnderstockMap;
    }

    public void setCommodityUnderstockMap(Map<String, String> commodityUnderstockMap) {
        this.commodityUnderstockMap = commodityUnderstockMap;
    }

	public void setTdaDeliveryInfo(Integer deliveryBatch,Long logisticsCenterId,String logisticsCenterName,Integer businessType,String deliveryTimeRange){
		this.deliveryBatch = deliveryBatch;
		this.logisticsCenterId = logisticsCenterId;
		this.logisticsCenter = logisticsCenterName;
		this.businessType = businessType;
		this.deliveryTimeRange = deliveryTimeRange;
	}

	public BigDecimal getUnLimitOrderTotalPrice(){
		BigDecimal result = BigDecimal.ZERO;
		for (OrderItemDto dto : this.getItems()) {
			if (!ProductTypeEnums.GIFT.getCode().equals(dto.getType()) && StockTypeEnum.UN_LIMIT.getCode().equals(dto.getStockType())) {
				result = result.add(dto.amount());
			}
		}
		return result;
	}

	/**
	 * 预计算不限量订货的商品总价格，用于配比
	 */
	public BigDecimal getPreComputeUnLimitOrderTotalPrice(){

		BigDecimal result = BigDecimal.ZERO;
		for (OrderItemDto item : this.getItems()) {
			if (StockTypeEnum.UN_LIMIT.getCode().equals(item.getStockType())) {
				boolean specialFlag = item.isSpecialFlag();
				if(specialFlag){
					//处理特价
					BigDecimal productNum = item.getProductNum();
					BigDecimal availableStoreLimit = item.getAvailableStoreLimit();
					BigDecimal availableTotalLimit = item.getAvailableTotalLimit();
					BigDecimal availableLimit = availableTotalLimit.min(availableStoreLimit);
					BigDecimal specialQuantity;
					BigDecimal normalQuantity;
					BigDecimal noneLimit = new BigDecimal(999999999);
					if( 0 == item.getLimitNumber() && noneLimit.compareTo(availableTotalLimit) == 0){
						specialQuantity = productNum;
						normalQuantity = BigDecimal.ZERO;
					}else{
						BigDecimal salesBoxCapacity = item.getSalesBoxCapacity();
						availableLimit = salesBoxCapacity.multiply(availableLimit.divide(salesBoxCapacity,0, RoundingMode.FLOOR));

						BigDecimal productNumSalesBoxCapacity = salesBoxCapacity.multiply(productNum.divide(salesBoxCapacity,0,RoundingMode.FLOOR));
						specialQuantity = availableLimit.min(productNumSalesBoxCapacity);
						normalQuantity = productNum.subtract(specialQuantity);

					}

					if(specialQuantity.compareTo(BigDecimal.ZERO) > 0){
						result = result.add(specialQuantity.multiply(item.getPrice()));
					}

					if(normalQuantity.compareTo(BigDecimal.ZERO) >0){
						result = result.add(normalQuantity.multiply(item.getOriginalPrice()));
					}

				}else{
					result = result.add(item.getProductNum().multiply(item.getPrice()));
				}
			}
		}
		return result;
	}

}

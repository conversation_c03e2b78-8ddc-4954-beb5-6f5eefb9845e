//package com.pinshang.qingyun.order.enums;
//
//public enum ShopTypeEnums {
//
//	SHOP("门店", 1),
//	XS("鲜食店", 2);
//    private String name;
//    private int code;
//
//    ShopTypeEnums(String name, int code) {
//        this.name = name;
//        this.code = code;
//    }
//
//    public static String getName(int code) {
//        for (ShopTypeEnums es : ShopTypeEnums.values()) {
//            if (code == es.getCode()) {
//                return es.name;
//            }
//        }
//        return null;
//    }
//
//    public String getName() {
//        return name;
//    }
//
//    public int getCode() {
//        return code;
//    }
//}

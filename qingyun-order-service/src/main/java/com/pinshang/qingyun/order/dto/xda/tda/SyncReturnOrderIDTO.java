/*
 * tramy.cn Inc.
 * Copyright (c) 2021-2024 All Rights Reserved.
 */
package com.pinshang.qingyun.order.dto.xda.tda;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <p>
 * 退货单列表
 * </p>
 *
 * <AUTHOR> shenyang
 * @version : 1.0.0
 * @history : modify history
 * <author>              <time>              <version>              <desc>
 * @since : 2024/05/11 10:09
 */
@Data
public class SyncReturnOrderIDTO {
    /**
     * id
     */
    @ApiModelProperty(value = "id")
    private Long id;
    /**
     * 退货类型：1-客户退货，2-订单取消，3-配送失败
     */
    @ApiModelProperty(value = "退货类型")
    private Integer returnType;
    /**
     * 客户id
     */
    @ApiModelProperty(value = "客户id")
    private Long storeId;
    /**
     * 客户编码
     */
    @ApiModelProperty(value = "客户编码")
    private String storeCode;
    /**
     * 客户名称
     */
    @ApiModelProperty(value = "客户名称")
    private String storeName;

    /**
     * 退货单编号，唯一标识每个退货单
     */
    @ApiModelProperty(value = "退货单号")
    private String returnOrderCode;

    /**
     * 退货单序列号，前端展示用
     */
    @ApiModelProperty(value = "退货单序列号，前端展示用")
    private String returnOrderSeq;

    /**
     * 来源订单id
     */
    @ApiModelProperty(value = "来源订单id")
    private Long sourceOrderId;

    /**
     * 来源单号，即相关的订单编号
     */
    @ApiModelProperty(value = "来源单号")
    private String sourceOrderCode;

    /**
     * 退货单状态：1-待审核、2-待取货、3-取货失败、4-待大仓确认、5-已取消、6-已完成、7-审核不通过
     */
    @ApiModelProperty(value = "状态:1-待审核、2-待取货、3-取货失败、4-待大仓确认、5-已取消、6-已完成、7-审核不通过")
    private Integer status;

    /**
     * 退货单状态描述：1-待审核、2-待取货、3-取货失败、4-待大仓确认、5-已取消、6-已完成、7-审核不通过
     */
    @ApiModelProperty(value = "状态描述:1-待审核、2-待取货、3-取货失败、4-待大仓确认、5-已取消、6-已完成、7-审核不通过")
    private String statusName;

    /**
     * 配送批次
     */
    @ApiModelProperty(value = "配送批次")
    private String deliveryBatch;

    /**
     * 取货时间段
     */
    @ApiModelProperty(value = "取货时间段")
    private String pickUpTimeRange;

    /**
     * 手机号
     */
    @ApiModelProperty(value = "手机号")
    private String phone;

    /**
     * 物流中心id,pinshang_tms.t_tms_logistics_center.id
     */
    @ApiModelProperty(value = "物流中心id")
    private Long logisticsCenterId;

    /**
     * 物流中心
     */
    @ApiModelProperty(value = "物流中心")
    private Long logisticsCenterName;

    /**
     * 退货订单类型：1-退货，2-差异（少货/多货）
     */
    private Integer returnOrderType;

    /**
     * 送货地址
     */
    @ApiModelProperty(value = "收货地址")
    private String deliveryAddress;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    /**
     * 送货日期
     */
    @ApiModelProperty(value = "送货日期")
    private Date deliveryTime;

    /**
     * 业务类型：10-通达销售
     */
    private Integer businessType;

    /**
     * 退货来源：1-app客户申请，2-品鲜后台投诉，3-订单取消，4-订单配送失败
     */
    @ApiModelProperty(value = "退货来源：1-app客户申请，2-品鲜后台投诉，3-订单取消，4-订单配送失败", example = "1", required = true)
    private Integer returnSource;

    /**
     * 申请商品金额
     */
    private BigDecimal totalCheckMoney;

    private Date updateTime;

    private String remark;

    private Long driverId;
    private Long checkUserId;
    private Date checkTime;
    /**
     * 申请商品金额
     */
    private BigDecimal totalApplyMoney;
    /**
     * 退货单明细
     */
    @ApiModelProperty(value = "退货单明细")
    private List<ReturnOrderItemODTO> returnOrderItemList;

    /**
     * 送货/取货  完成时间
     */
    @ApiModelProperty(value = "取货完成时间")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date deliveryEndTime;

}

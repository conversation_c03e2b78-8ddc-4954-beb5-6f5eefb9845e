package com.pinshang.qingyun.order.vo.recharge;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.pinshang.qingyun.base.configure.expand.QYAssert;
import com.pinshang.qingyun.base.enums.xd.XdPayTypeEnum;
import com.pinshang.qingyun.box.utils.SpringUtil;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

@Data
@ApiModel
@NoArgsConstructor
@AllArgsConstructor
public class RechargeVo{
    @ApiModelProperty("充值金额")
    private BigDecimal payAmount;

    @ApiModelProperty("支付方式：1＝支付宝，2＝微信，3=小程序，5=云闪付支付")
    private XdPayTypeEnum payType;

    @ApiModelProperty("微信支付时需要")
    private String clientIp;

    @ApiModelProperty("如果为小程序下单,需传入jsCode")
    private String jsCode;

    /** 客户id **/
    @JsonIgnore
    private Long storeId;
    @JsonIgnore
    private Long userId;
    @JsonIgnore
    @ApiModelProperty(position = 0, required = true, value = "appCode", hidden = true)
    private String appCode;
    @ApiModelProperty("订单号")
    private String billCode;

    /**
     * 验证充值金额必须大于等于配置金额
     * 验证充值金额必须是正整数
     * @param confPayAmount
     */
    public void checkPayAmount(String confPayAmount){
        QYAssert.notNull( this.payAmount,"请输入充值金额");
        QYAssert.isTrue(SpringUtil.hasTextAndNotNull( confPayAmount ),"请配置最小充值金额");
//        int confPayAmountInt =  Integer.valueOf( confPayAmount );
//        int payAmountInt = this.payAmount.intValue();
//        QYAssert.isTrue(payAmountInt>= confPayAmountInt ,"充值金额必须大于等于"+confPayAmountInt);
//        QYAssert.isTrue(this.payAmount.compareTo( new BigDecimal( payAmountInt))==0 ,"充值金额必须是正整数");
        QYAssert.isTrue(this.payAmount.compareTo(BigDecimal.ZERO) > 0, "充值金额必须是正数");
        QYAssert.isTrue(this.payAmount.compareTo(new BigDecimal(confPayAmount)) >= 0, "充值金额不能小于最小充值金额");
    }

}

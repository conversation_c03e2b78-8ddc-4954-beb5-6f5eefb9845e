package com.pinshang.qingyun.order.dto.pf;

import lombok.Data;

/**
 * <AUTHOR>
 * @Date 2024/3/13 18:18
 */
@Data
public class PfSaveOrderODTO {

    private Boolean isSuccess;

    private String errorMsg;

    public static PfSaveOrderODTO Success(){
        PfSaveOrderODTO pfSaveOrderODTO = new PfSaveOrderODTO();
        pfSaveOrderODTO.setIsSuccess(Boolean.TRUE);
        return pfSaveOrderODTO;
    }

    public static PfSaveOrderODTO Error(String erroeMasg){
        PfSaveOrderODTO pfSaveOrderODTO = new PfSaveOrderODTO();
        pfSaveOrderODTO.setIsSuccess(Boolean.TRUE);
        pfSaveOrderODTO.setErrorMsg(erroeMasg);
        return pfSaveOrderODTO;
    }
}

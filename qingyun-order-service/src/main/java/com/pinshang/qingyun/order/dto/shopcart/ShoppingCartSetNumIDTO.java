package com.pinshang.qingyun.order.dto.shopcart;

import com.pinshang.qingyun.base.enums.YesOrNoEnums;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
@ApiModel
@NoArgsConstructor
@AllArgsConstructor
public class ShoppingCartSetNumIDTO {
    @ApiModelProperty("设定数量")
    private BigDecimal quantity;
    @ApiModelProperty(value = "客户id",hidden = true)
    private Long storeId;
    @ApiModelProperty(value = "商品id")
    private Long commodityId;
    @ApiModelProperty("送货日期")
    private Date orderTime;
    @ApiModelProperty("是否是购物车页面请求")
    private YesOrNoEnums shopCartPage;
}

package com.pinshang.qingyun.order.vo.order;

import com.pinshang.qingyun.order.enums.OrderModeType;
import com.pinshang.qingyun.order.enums.ProductTypeEnums;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

public class OrderDto implements Serializable{

	private static final long serialVersionUID = 1L;
	private String orderTime;
	private String orderRemark;
	private Long storeId;
	/** 企业id **/
	private Long enterpriseId;
	private Long userId;
	/** 打印类型(1：本地,2：送货员,3：不打印) */
	private Integer printType;
	/** 打印份数 */
	private Integer printNum;
	/** 0:普通订单 1：补货订单 2: 直送补货 **/
	private Integer modeType;

	/** items包含所有商品，包括原始商品、赠送商品、配货商品和配比后的商品 */
	private List<OrderItemDto> items = new ArrayList<>();

	@Deprecated
	private List<OrderItemDto> giftItems = new ArrayList<>();

	private Integer logisticsModel;
	private Long supplierId;
	private Long warehouseId;
	private String deliveryBatch;
	private String deleveryTimeRange;

	/**订单类型 **/
	private Integer orderType;
	private Long consignmentId; // 代销商户id

	private List<Long> originOrderIdList;

	private Map<Long, List<Long>> originOrderMap;
	/** 档口id */
	private Long stallId;

	/** 物流中心ID */
	private Long logisticsCenterId;
	/**物流中心名称*/
	private String logisticsCenterName;
	/** 是否直送 0=否  1=是 */
	private Integer directStatus = 0;

	public Integer getDirectStatus() {
		return directStatus;
	}

	public void setDirectStatus(Integer directStatus) {
		this.directStatus = directStatus;
	}

	public Long getLogisticsCenterId() {
		return logisticsCenterId;
	}
	public void setLogisticsCenterId(Long logisticsCenterId) {
		this.logisticsCenterId = logisticsCenterId;
	}
	public String getLogisticsCenterName() {
		return logisticsCenterName;
	}
	public void setLogisticsCenterName(String logisticsCenterName) {
		this.logisticsCenterName = logisticsCenterName;
	}

	public Long getStallId() {
		return stallId;
	}

	public void setStallId(Long stallId) {
		this.stallId = stallId;
	}

	public Map<Long, List<Long>> getOriginOrderMap() {
		return originOrderMap;
	}

	public void setOriginOrderMap(Map<Long, List<Long>> originOrderMap) {
		this.originOrderMap = originOrderMap;
	}

	public List<Long> getOriginOrderIdList() {
		return originOrderIdList;
	}

	public void setOriginOrderIdList(List<Long> originOrderIdList) {
		this.originOrderIdList = originOrderIdList;
	}

	public Long getConsignmentId() {
		return consignmentId;
	}

	public void setConsignmentId(Long consignmentId) {
		this.consignmentId = consignmentId;
	}

	public Integer getOrderType() {
		return orderType;
	}

	public void setOrderType(Integer orderType) {
		this.orderType = orderType;
	}

	public String getDeleveryTimeRange() {
		return deleveryTimeRange;
	}

	public void setDeleveryTimeRange(String deleveryTimeRange) {
		this.deleveryTimeRange = deleveryTimeRange;
	}

	public void addGiftItem(OrderItemDto itemDto){
		this.giftItems.add(itemDto);
	}
	public void addItem(OrderItemDto itemDto) {
		this.items.add(itemDto);
	}

	public BigDecimal giftAmount(){
		BigDecimal result = BigDecimal.ZERO;
		for (OrderItemDto itemDto : items) {
			if (ProductTypeEnums.PRODUCT.getCode().equals(itemDto.getType())) {
				result = result.add(itemDto.amount());
			}
		}
		return result;
	}
	
	public BigDecimal amount() {// 订单金额
		BigDecimal result = BigDecimal.ZERO;
		for (OrderItemDto itemDto : items) {
			result = result.add(itemDto.amount());
		}
		return result;
	}

	public BigDecimal originalAmount() { // 原始金额
		BigDecimal result = BigDecimal.ZERO;
		for (OrderItemDto itemDto : items) {
			result = result.add(itemDto.originalAmount());
		}
		return result;
	}

	public BigDecimal promotionAmount() { // 促销金额
		BigDecimal result = BigDecimal.ZERO;
		for (OrderItemDto itemDto : items) {
			result = result.add(itemDto.promotionAmount());
		}
		return result;
	}

	public String getOrderTime() {
		return orderTime;
	}

	public void setOrderTime(String orderTime) {
		this.orderTime = orderTime;
	}

	public List<OrderItemDto> getItems() {
		return items;
	}

	public void setItems(List<OrderItemDto> items) {
		this.items = items;
	}

	public Long getEnterpriseId() {
		return enterpriseId;
	}

	public void setEnterpriseId(Long enterpriseId) {
		this.enterpriseId = enterpriseId;
	}

	public Long getUserId() {
		return userId;
	}

	public void setUserId(Long userId) {
		this.userId = userId;
	}

	public Integer getPrintType() {
		return printType;
	}

	public void setPrintType(Integer printType) {
		this.printType = printType;
	}

	public Integer getPrintNum() {
		return printNum;
	}

	public void setPrintNum(Integer printNum) {
		this.printNum = printNum;
	}

	public Long getStoreId() {
		return storeId;
	}

	public void setStoreId(Long storeId) {
		this.storeId = storeId;
	}

	public String getOrderRemark() {
		return orderRemark;
	}

	public void setOrderRemark(String orderRemark) {
		this.orderRemark = orderRemark;
	}

	public Integer getModeType() {
		return modeType==null?OrderModeType.ORDER.getCode():modeType;
	}

	public void setModeType(Integer modeType) {
		this.modeType = modeType;
	}

	public List<OrderItemDto> getGiftItems() {
		return giftItems;
	}

	public void setGiftItems(List<OrderItemDto> giftItems) {
		this.giftItems = giftItems;
	}
	public Integer getLogisticsModel() {
		return logisticsModel;
	}
	public void setLogisticsModel(Integer logisticsModel) {
		this.logisticsModel = logisticsModel;
	}
	public Long getSupplierId() {
		return supplierId;
	}
	public void setSupplierId(Long supplierId) {
		this.supplierId = supplierId;
	}
	public Long getWarehouseId() {
		return warehouseId;
	}
	public void setWarehouseId(Long warehouseId) {
		this.warehouseId = warehouseId;
	}
	public String getDeliveryBatch() {
		return deliveryBatch;
	}
	public void setDeliveryBatch(String deliveryBatch) {
		this.deliveryBatch = deliveryBatch;
	}
}

package com.pinshang.qingyun.order.vo.shop;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

import com.pinshang.qingyun.order.model.order.ShoppingCartItem;

public class OrderShopShoppingCart {
    private Integer logisticsModel;
    private Long storeId; 
    private Long warehouseId;
    private Long supplierId; 
    private BigDecimal totalPrice;
    private Long subOrderId;
    private Integer status;
    private Integer varietyTotal;
    private Long enterpriseId;
    private Long createId;
    private List<ShoppingCartItem> items = new ArrayList<>();
	public Integer getLogisticsModel() {
		return logisticsModel;
	}
	public void setLogisticsModel(Integer logisticsModel) {
		this.logisticsModel = logisticsModel;
	}
	public Long getStoreId() {
		return storeId;
	}
	public void setStoreId(Long storeId) {
		this.storeId = storeId;
	}
	public Long getWarehouseId() {
		return warehouseId;
	}
	public void setWarehouseId(Long warehouseId) {
		this.warehouseId = warehouseId;
	}
	public Long getSupplierId() {
		return supplierId;
	}
	public void setSupplierId(Long supplierId) {
		this.supplierId = supplierId;
	}
	public BigDecimal getTotalPrice() {
		return totalPrice;
	}
	public void setTotalPrice(BigDecimal totalPrice) {
		this.totalPrice = totalPrice;
	}
	public Long getSubOrderId() {
		return subOrderId;
	}
	public void setSubOrderId(Long subOrderId) {
		this.subOrderId = subOrderId;
	}
	public Integer getStatus() {
		return status;
	}
	public void setStatus(Integer status) {
		this.status = status;
	}
	public Integer getVarietyTotal() {
		return varietyTotal;
	}
	public void setVarietyTotal(Integer varietyTotal) {
		this.varietyTotal = varietyTotal;
	}
	public Long getEnterpriseId() {
		return enterpriseId;
	}
	public void setEnterpriseId(Long enterpriseId) {
		this.enterpriseId = enterpriseId;
	}
	public Long getCreateId() {
		return createId;
	}
	public void setCreateId(Long createId) {
		this.createId = createId;
	}
	public List<ShoppingCartItem> getItems() {
		return items;
	}
	public void setItems(List<ShoppingCartItem> items) {
		this.items = items;
	}
	
	public void addItems(ShoppingCartItem item){
		this.items.add(item);
	}
}

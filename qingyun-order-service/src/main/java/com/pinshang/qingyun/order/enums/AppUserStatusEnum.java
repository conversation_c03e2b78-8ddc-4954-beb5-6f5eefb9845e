package com.pinshang.qingyun.order.enums;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;

/**
 *
 * <AUTHOR>
 * @Date 2018/4/8 16:34
 */
public enum AppUserStatusEnum implements BaseEnum{
    //账号状态:0-有效,1-无效
    VALID(0,"有效"),
    INVALID(1,"无效")
    ;
    private Integer code;
    private String desc;

    AppUserStatusEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    @Override
    public Integer convert() {
        return this.code;
    }
    @JsonValue
    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }
    @JsonCreator
    public static AppUserStatusEnum getEnumByCode(Integer code){
        if(code == null){
            return null;
        }
        for(AppUserStatusEnum e : AppUserStatusEnum.values()){
            if(e.getCode().intValue() == code){
                return e;
            }
        }
        return null;
    }
}

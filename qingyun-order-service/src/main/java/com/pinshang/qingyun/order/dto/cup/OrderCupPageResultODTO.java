package com.pinshang.qingyun.order.dto.cup;

import com.pinshang.qingyun.order.model.order.OrderList;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Transient;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * @Author: sk
 * @Date: 2024/7/22
 */
@Data
public class OrderCupPageResultODTO {

    @ApiModelProperty("订单id")
    private String orderId;

    @ApiModelProperty("订单编码")
    private String orderCode;

    private Long storeId;
    private Date createTime;

    /**备注 **/
    private String orderRemark;

    /**订单金额 **/
    @ApiModelProperty("订单金额")
    private BigDecimal orderAmount;

    @ApiModelProperty("订单状态 (0正常,1删除,2取消)")
    private Integer orderStatus;
    /**订单时间 **/
    @ApiModelProperty("送货日期")
    private Date orderTime;

    @ApiModelProperty("客户编码")
    private String storeCode;
    @ApiModelProperty("客户名称")
    private String storeName;

    @ApiModelProperty("送货员id")
    private Long deliverymanId;
    @ApiModelProperty("送货员名称")
    private String deliverymanName;
    @ApiModelProperty("督导id")
    private Long supervisorId;
    @ApiModelProperty("督导")
    private String supervisorName;

    private Long createId;
    @ApiModelProperty("操作员")
    private String createUserName;

    /**订单类型 **/
    private Integer orderType;

    @ApiModelProperty("线路组id")
    private Long lineGroupId;
    @ApiModelProperty("线路组名称")
    private String lineGroupName;

    private Integer businessType;



}

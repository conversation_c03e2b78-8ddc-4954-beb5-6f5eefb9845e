package com.pinshang.qingyun.order.vo.order;

import com.pinshang.qingyun.order.mapper.entry.stall.StallEntry;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * Created by crell on 2017/11/6.
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class SaleReturnAddVo extends StallEntry {

    private Long storeId;

    private Long enterpriseId;

    private List<SaleReturnItemAddVo> saleReturnItems;
    
    private Long createId;

    //退货单id
    private Long saleReturnOrderId;

    private Long shopId;

    /** 前置仓质检： true不扣库存    fasle扣库存 */
    private Boolean xdQuality;

    /** 档口id */
    private Long stallId;
    private String remark;
}

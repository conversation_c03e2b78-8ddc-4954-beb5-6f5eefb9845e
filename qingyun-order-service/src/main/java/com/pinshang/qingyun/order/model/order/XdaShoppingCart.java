package com.pinshang.qingyun.order.model.order;

import com.pinshang.qingyun.base.po.BaseIDPO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Entity;
import javax.persistence.Table;
import java.math.BigDecimal;
import java.util.Date;

@Entity
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "t_xda_shopping_cart")
public class XdaShoppingCart extends BaseIDPO{
    private Long storeId;

    private Long commodityId;

    private BigDecimal quantity;

    /**
     * commodity_type DEFAULT '1' COMMENT '商品类型1、普通，2、特惠'
     */
    private Integer commodityType;

    private Date createTime;


    public BigDecimal getQuantity(){
        BigDecimal zero = BigDecimal.ZERO;
        if(null == this.quantity){
            return zero;
        }
        return quantity;
    }
}
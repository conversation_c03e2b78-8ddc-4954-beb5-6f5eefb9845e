package com.pinshang.qingyun.order.bo;

import lombok.Data;

import java.util.List;

/**
 * @Description：
 * @Author：<PERSON><PERSON>ui
 * @Package：com.pinshang.qingyun.order.controller.tob
 * @Date: 2024/4/12
 */
@Data
public class QueryOrderBo {


    //订单状态(0正常,1删除,2取消)
    private List<Integer> orderStatus;

    //送货日期时间集合
    private List<String> orderTimes;

    //商品id集合
    private List<String> commodityIds;

    //送货开始日期
    private String startOrderTime;

    //送货结束日期
    private String endOrderTime;
}

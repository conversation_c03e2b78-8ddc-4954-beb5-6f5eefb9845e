package com.pinshang.qingyun.order.vo.order;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import lombok.ToString;

import java.util.List;

/**
 * summary.
 * <p>
 * detailed description
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2019-02-15
 */
@Data
@ToString
@RequiredArgsConstructor
@AllArgsConstructor
public class CreateOrderFilterVo {
    // 创建订单是否成功
    private Boolean createOrderResult;
    // 门店订货-过滤提示
    private List<FilterTipVo> filterTips;

    private List<BStockShortResponseVO> shortStockList;
}

package com.pinshang.qingyun.order.model.xda;


import com.pinshang.qingyun.box.utils.DateUtil;
import com.pinshang.qingyun.order.dto.xda.XdaComplaintCommodityDTO;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.UUID;

@Entity
@Table(name="t_complaint")
@Data
@NoArgsConstructor
public class XdaComplaint {
  @Id
  @Column(name = "uid")
  private String id;
  private Date createdAt;
  private Boolean enabled;
  private String enterpriseId;
  private String complaintCode;
  private Integer complaintHandle;
  /**
   * 投诉商品总金额
   */
  private Double complaintTotalMoney;
  /**
   * 投诉类型 0-普通投诉（差异投诉），1-退货投诉，2-门店投诉
   */
  private Integer complaintType;
  private String complaintUser;
  private String createId;
  private Date deliveryDate;
  private String handleRemark;
  private Long handleResult;
  private String storeCode;
  private Long storeId;
  private Date updateTime;
  private Date checkDate;
  private String checkRemark;
  /**
   * 投诉处理状态:1待审核;2已审核;3已取消'
   */
  private Long complaintHandleStatus;
  private String complaintRemark;
  private String linkmanMobile;
  private Double checkTotalMoney;
  private String batchNo;
  private Long deliverymanId;

  public XdaComplaint(XdaComplaintCommodityDTO xdaComplaintCommodityDTO, String id, Long deliveryManId,Date d)  {
    //DateFormat format = new SimpleDateFormat("yyyy-MM-dd");
    this.setId(id);
    this.setBatchNo(UUID.randomUUID().toString());
    this.setComplaintCode(String.valueOf(d.getTime()));
    this.setComplaintHandleStatus(1L);
    this.setComplaintRemark(xdaComplaintCommodityDTO.getComplaintReason());
    // 投诉类型 0-普通投诉-差异投诉，1-退货投诉，2-门店投诉
    this.setComplaintType(xdaComplaintCommodityDTO.getComplaintType().getCode());
    this.setCreatedAt(d);
    this.setEnabled(Boolean.TRUE);
    // app端 投诉单默认为系统创建
    this.setCreateId(xdaComplaintCommodityDTO.getStoreId().toString());
    this.setDeliveryDate(DateUtil.parseDate(xdaComplaintCommodityDTO.getDeliveryDate(), "yyyy-MM-dd"));
    this.setStoreCode(xdaComplaintCommodityDTO.getStoreCode());
    this.setStoreId(xdaComplaintCommodityDTO.getStoreId());
    this.setUpdateTime(d);
    this.setComplaintTotalMoney(xdaComplaintCommodityDTO.getComplaintTotalMoney().doubleValue());
    this.setDeliverymanId(deliveryManId);
  }
}

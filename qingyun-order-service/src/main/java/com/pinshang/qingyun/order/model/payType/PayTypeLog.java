package com.pinshang.qingyun.order.model.payType;

import lombok.Data;

import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;

/**
 * Created by Ruansi on 2019/08/15.
 */
@Data
@Table(name = "t_xda_paytype_log")
public class PayTypeLog {
    @Id
    private Long id;
    // 支付类型ID
    private Long paytypeId;
    // 旧状态
    private Integer oldValue;
    // 新状态
    private Integer newValue;
    // 添加人
    private Long createId;
    // 添加时间
    private Date createTime;
}

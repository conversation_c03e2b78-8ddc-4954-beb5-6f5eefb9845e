package com.pinshang.qingyun.order.service;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.pinshang.qingyun.base.enums.IogisticsModelEnums;
import com.pinshang.qingyun.base.enums.shop.ShopTypeEnums;
import com.pinshang.qingyun.base.spring.SpringBeanFinder;
import com.pinshang.qingyun.box.utils.DateUtil;
import com.pinshang.qingyun.common.dto.DictionaryODTO;
import com.pinshang.qingyun.common.service.DictionaryClient;
import com.pinshang.qingyun.order.constant.RedissKeyConstant;
import com.pinshang.qingyun.order.constant.ThreadPoolBeanConstants;
import com.pinshang.qingyun.order.dto.ReceiveOrderMessageIDTO;
import com.pinshang.qingyun.order.dto.ReceiveOrderMessageODTO;
import com.pinshang.qingyun.order.enums.ReceiveOrderBusinessTypeEnums;
import com.pinshang.qingyun.order.enums.ShopReceiveTypeEnums;
import com.pinshang.qingyun.order.mapper.ShopReceiveOrderMapper;
import com.pinshang.qingyun.order.model.shop.Shop;
import com.pinshang.qingyun.order.vo.shop.Quantity;
import com.pinshang.qingyun.order.vo.shop.ReceiveOrderVo;
import com.pinshang.qingyun.shop.dto.ShopEmployeeODTO;
import com.pinshang.qingyun.shop.service.ShopClient;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.concurrent.ArrayBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * @Author: sk
 * @Date: 2021/3/16
 */
@Service
@Slf4j
public class ShopAutoReceiveService {
    @Lazy
    @Autowired
    private ShopReceiveSettingService shopReceiveSettingService;
    @Autowired
    private ShopReceiveService shopReceiveService;
    @Autowired
    private ShopReceiveOrderMapper shopReceiveOrderMapper;
    @Autowired
    private ShopService shopService;
    @Lazy
    @Autowired
    private ShopHandleAutoReceiveService shopHandleAutoReceiveService;
    @Autowired
    private XDShoppingCartService xDShoppingCartService;
    @Autowired
    private RedissonClient redissonClient;
    @Autowired
    private ShopClient shopClient;
    @Autowired
    private DictionaryClient dictionaryClient;
    @Autowired
    private XdReceiveService xdReceiveService;
    @Autowired
    private WeChatSendMessageService weChatSendMessageService;

    private static AtomicInteger count = new AtomicInteger(0);

    /**
     * 发货完成将subOrderId 放入队列
     */
    @Async
    public void pushSubOrderIdToQueue(List<Long> subOrderIds){
        subOrderIds.forEach(subOrderId->{
            try{
                RQueue<Long> subOrderIdQueue = redissonClient.getQueue(RedissKeyConstant.AUTO_REVEIVE_BY_QUEUE);
                subOrderIdQueue.offer(subOrderId);
            }catch (Exception e){
                log.warn("发货完成将subOrderId放入队列失败,失败 subOrderId {}. 异常信息 {}", subOrderId, e.getMessage());
            }
        });
    }


    /**
     * 从队列获取50个SubOrderId 用于自动收货
     * @return
     */
    public List<Long> getSubOrderIdFromQueue(){
        Set<Long> setList = new HashSet<>();
        for(int i = 0; i < 50; i ++){
            try{
                RQueue<Long> subOrderIdQueue = redissonClient.getQueue(RedissKeyConstant.AUTO_REVEIVE_BY_QUEUE);
                Long subOrderId = subOrderIdQueue.poll();
                if(subOrderId != null){
                    setList.add(subOrderId);
                }else {
                    break;
                }
            }catch (Exception e){
                log.warn("自动收货,获取延迟队列失败 {}", e.getMessage());
            }
        }

        List<Long> subOrderIdList = new ArrayList<>(setList);
        return subOrderIdList;
    }

    /**
     * 根据队列收货
     */
    public void autoReceiveByQueue(){
        if(count.getAndIncrement() > 0){
           // log.warn("退出了。。。。。。。。。。。。");
            return;
        }

        // 从队列获取 subOrderIdList
        List<Long> subOrderIdList = getSubOrderIdFromQueue();
        if(CollectionUtils.isEmpty(subOrderIdList)){
            count.set(0);
            return;
        }

        // 自动收货排除掉鲜食加盟的(钱大妈)
        List<Long> xsjmStoreIdList = shopService.getStoreIdListByShopType(ShopTypeEnums.XSJM.getCode());
        List<ReceiveOrderVo> autoReceiveOrderList = shopReceiveService.queryAutoReceiveOrderList(subOrderIdList, xsjmStoreIdList);
        if(CollectionUtils.isEmpty(autoReceiveOrderList)){
            count.set(0);
            return;
        }

        Set<Long> storeIdList = autoReceiveOrderList.stream().map(item -> Long.valueOf(item.getStoreId())).collect(Collectors.toSet());
        List<Shop> shopList = shopService.getShopByStoreIdList(new ArrayList<>(storeIdList));
        Map<Long, Long> shopMap = shopList.stream().collect(Collectors.toMap(Shop::getStoreId,Shop::getId,(key1 , key2)-> key2));

        for(ReceiveOrderVo o : autoReceiveOrderList){
            try{
                o.setShopId(shopMap.get(Long.valueOf(o.getStoreId())));
                o.setCreateId(-1L);
                o.setReceiveId("-1");
                o.setRemark("自动收货");
                o.setReceiveTime(new Date());
                List<Quantity> autoDeliveryQuantity = shopReceiveOrderMapper.getAutoDeliveryQuantity(o.getSubOrderId());
                o.setReceiveQuantities(autoDeliveryQuantity);
                shopReceiveService.addReceive(o);
            }catch (Throwable e){
                // 报错再次放入队列，下次继续跑
                pushSubOrderIdToQueue(Collections.singletonList(Long.valueOf(o.getSubOrderId())));
                log.error("订单自动收货失败 subOrderId {} Exception {}", o.getSubOrderId(), e);

                RLock lock = redissonClient.getLock(RedissKeyConstant.AUTO_REVEIVE_ERROR_MSG_LOCK);
                lock.lock(2L, TimeUnit.SECONDS);
                try{
                    RBucket<String> bucket = redissonClient.getBucket(RedissKeyConstant.AUTO_REVEIVE_ERROR_MSG);
                    String data = bucket.get();
                    if(StringUtils.isBlank(data)){
                        StringBuffer sb = new StringBuffer();
                        sb.append("订单自动收货失败");
                        //发送微信模板信息
                        weChatSendMessageService.sendWeChatMessage(sb.toString());
                        bucket.set(RedissKeyConstant.AUTO_REVEIVE_ERROR_MSG, 60, TimeUnit.MINUTES);
                    }
                }finally{
                    lock.unlock(); // 释放锁
                }
            }
        }

        count.set(0);
    }


    /**
     * 自动收货
     * @throws
     */
    @Async
    public void autoReceive(String orderTime) {
        // 校验重复任务
        String lockKey =  "order:automaticReceipt";
        RAtomicLong lockKeyRa = redissonClient.getAtomicLong(lockKey);
        long automaticLock = lockKeyRa.incrementAndGet();
        if (automaticLock == 1) {
            lockKeyRa.expire(1, TimeUnit.MINUTES);
        }
        if (automaticLock > 1) {
            return;
        }

        // 查询开启自动收货的门店
        List<Long> shopIdList = shopReceiveSettingService.getShopIdList(ShopReceiveTypeEnums.AUTOMATIC.getCode());
        if(CollectionUtils.isEmpty(shopIdList)){
            return;
        }

        List<Shop> shopList = shopService.getShopByIdList(shopIdList);
        List<Long> storeIdList = shopList.stream().map(item -> item.getStoreId()).collect(Collectors.toList());
        Map<Long, Long> shopMap = shopList.stream().collect(Collectors.toMap(Shop::getStoreId,Shop::getId,(key1 , key2)-> key2));
        String beginTime = DateUtil.getDateFormate(DateUtil.addDay(DateUtil.parseDate(orderTime,"yyyy-MM-dd"),-1), "yyyy-MM-dd");

        // 自动收货排除掉鲜食加盟的(钱大妈)
        List<Long> xsjmStoreIdList = shopService.getStoreIdListByShopType(ShopTypeEnums.XSJM.getCode());
        List<ReceiveOrderVo> autoAllReceiveOrderList = shopReceiveOrderMapper.getAutoReceiveOrderNew(Arrays.asList(IogisticsModelEnums.DIRECT_CONNECTION.getCode(), IogisticsModelEnums.DISPATCHING.getCode()), beginTime, orderTime,storeIdList, xsjmStoreIdList);
        if(CollectionUtils.isEmpty(autoAllReceiveOrderList)){
            return;
        }

        // 根据storeId分组
        Map<String, List<ReceiveOrderVo>> autoAllReceiveOrderMap = autoAllReceiveOrderList.stream().collect(Collectors.groupingBy(ReceiveOrderVo::getStoreId));

        // 获取线程池
        ThreadPoolTaskExecutor threadPool = (ThreadPoolTaskExecutor) SpringBeanFinder.getBean(ThreadPoolBeanConstants.ORDER_THREADPOOL);

        Set<String> storeIdSet = autoAllReceiveOrderMap.keySet();
        List<String> successStoreIdList = new ArrayList<>(storeIdSet);
        for(Map.Entry<String, List<ReceiveOrderVo>> entry : autoAllReceiveOrderMap.entrySet()){
            // 创建任务并提交到线程池中
            threadPool.execute(new Runnable() {
                @Override
                public void run() {
                    List<ReceiveOrderVo> receiveOrderVoList = entry.getValue();

                    for(ReceiveOrderVo o : receiveOrderVoList){
                        try{
                            o.setShopId(shopMap.get(Long.valueOf(o.getStoreId())));
                            o.setCreateId(-1L);
                            o.setReceiveId("-1");
                            o.setRemark("自动收货");
                            o.setReceiveTime(new Date());
                            String subOrderId = o.getSubOrderId();
                            List<Quantity> autoDeliveryQuantity = shopReceiveOrderMapper.getAutoDeliveryQuantity(subOrderId);
                            o.setReceiveQuantities(autoDeliveryQuantity);
                            shopReceiveService.addReceive(o);
                        }catch (Throwable e){
                            log.error("订单自动收货失败 subOrderId {} Exception {}", o.getSubOrderId(), e);

                            RLock lock = redissonClient.getLock(RedissKeyConstant.AUTO_REVEIVE_ERROR_MSG_LOCK);
                            lock.lock(2L, TimeUnit.SECONDS);
                            try{
                                RBucket<String> bucket = redissonClient.getBucket(RedissKeyConstant.AUTO_REVEIVE_ERROR_MSG);
                                String data = bucket.get();
                                if(StringUtils.isBlank(data)){
                                    StringBuffer sb = new StringBuffer();
                                    sb.append("订单自动收货失败");
                                    //发送微信模板信息
                                    weChatSendMessageService.sendWeChatMessage(sb.toString());
                                    bucket.set(RedissKeyConstant.AUTO_REVEIVE_ERROR_MSG + orderTime, 60, TimeUnit.MINUTES);
                                }
                            }finally{
                                lock.unlock(); // 释放锁
                            }
                        }
                    }

                }
            });
        }

        // 将当日自动收货的storeId放入缓存
        if(CollectionUtils.isNotEmpty(successStoreIdList)){
            RBucket<List<String>> bucket = redissonClient.getBucket("autoReceiveStoreIdList");
            List<String> storeIds = bucket.get();
            if(CollectionUtils.isNotEmpty(storeIds)){
                successStoreIdList.addAll(storeIds);
            }
            successStoreIdList = successStoreIdList.stream().distinct().collect(Collectors.toList());
            bucket.set(successStoreIdList, DateUtil.getSurplusSeconds(), TimeUnit.SECONDS);
        }
    }


    /**
     * 鲜食加盟自动收货(钱大妈)
     * @param orderTime
     */
    @Async
    public void autoReceiveXsJm(String orderTime) {
        // 校验重复任务
        String lockKey =  "order:automaticReceiptXsjm";
        RAtomicLong lockKeyRa = redissonClient.getAtomicLong(lockKey);
        long automaticLock = lockKeyRa.incrementAndGet();
        if (automaticLock == 1) {
            lockKeyRa.expire(1, TimeUnit.MINUTES);
        }
        if (automaticLock > 1) {
            return;
        }

        // 查询开启自动收货的门店
        List<Long> shopIdList = shopReceiveSettingService.getShopIdList(ShopReceiveTypeEnums.AUTOMATIC.getCode());
        if(CollectionUtils.isEmpty(shopIdList)){
            return;
        }
        List<Shop> shopList = shopService.getShopByIdList(shopIdList);
        List<Long> storeIdList = shopList.stream().map(item -> item.getStoreId()).collect(Collectors.toList());
        Map<Long, Long> shopMap = shopList.stream().collect(Collectors.toMap(Shop::getStoreId,Shop::getId,(key1 , key2)-> key2));

        // 鲜食加盟的storeIdList
        List<Long> xsjmStoreIdList = shopService.getStoreIdListByShopType(ShopTypeEnums.XSJM.getCode());
        if(CollectionUtils.isEmpty(xsjmStoreIdList)){
            return;
        }

        // 获取开启自动订货的限时加盟门店
        storeIdList.retainAll(xsjmStoreIdList);
        if(CollectionUtils.isEmpty(storeIdList)){
            return;
        }

        List<ReceiveOrderVo> autoAllReceiveOrderList = shopReceiveOrderMapper.getAutoReceiveXsjmOrder(Arrays.asList(IogisticsModelEnums.DIRECT_CONNECTION.getCode(), IogisticsModelEnums.DISPATCHING.getCode()), orderTime, storeIdList);
        if(CollectionUtils.isEmpty(autoAllReceiveOrderList)){
            return;
        }

        // 根据storeId分组
        Map<String, List<ReceiveOrderVo>> autoAllReceiveOrderMap = autoAllReceiveOrderList.stream().collect(Collectors.groupingBy(ReceiveOrderVo::getStoreId));

        // 获取线程池
        ThreadPoolTaskExecutor threadPool = (ThreadPoolTaskExecutor) SpringBeanFinder.getBean(ThreadPoolBeanConstants.ORDER_THREADPOOL);

        for(Map.Entry<String, List<ReceiveOrderVo>> entry : autoAllReceiveOrderMap.entrySet()){
            // 创建任务并提交到线程池中
            threadPool.execute(new Runnable() {
                @Override
                public void run() {
                    List<ReceiveOrderVo> receiveOrderVoList = entry.getValue();

                    for(ReceiveOrderVo o : receiveOrderVoList){

                        // 鲜食加盟收货加锁
                        RLock xsjmReceiveOrderLock = redissonClient.getLock("order:xsjmAutoReceive:" + o.getSubOrderId());
                        xsjmReceiveOrderLock.lock(20L, TimeUnit.SECONDS);

                        try{
                            o.setShopId(shopMap.get(Long.valueOf(o.getStoreId())));
                            o.setCreateId(-1L);
                            o.setReceiveId("-1");
                            o.setRemark("自动收货");
                            o.setReceiveTime(new Date());
                            String subOrderId = o.getSubOrderId();
                            List<Quantity> autoDeliveryQuantity = shopReceiveOrderMapper.getAutoDeliveryQuantity(subOrderId);
                            o.setReceiveQuantities(autoDeliveryQuantity);
                            shopReceiveService.addReceive(o);
                        }catch (Throwable e){
                            log.error("鲜食加盟订单自动收货失败 subOrderId {} Exception {}", o.getSubOrderId(), e);

                            RLock lock = redissonClient.getLock(RedissKeyConstant.AUTO_REVEIVE_ERROR_MSG_LOCK);
                            lock.lock(2L, TimeUnit.SECONDS);
                            try{
                                RBucket<String> bucket = redissonClient.getBucket(RedissKeyConstant.AUTO_REVEIVE_ERROR_MSG);
                                String data = bucket.get();
                                if(StringUtils.isBlank(data)){
                                    StringBuffer sb = new StringBuffer();
                                    sb.append("鲜食加盟订单自动收货失败");
                                    //发送微信模板信息
                                    weChatSendMessageService.sendWeChatMessage(sb.toString());
                                    bucket.set(RedissKeyConstant.AUTO_REVEIVE_ERROR_MSG + orderTime, 60, TimeUnit.MINUTES);
                                }
                            }finally{
                                lock.unlock(); // 释放锁
                            }
                        }finally {
                            if(xsjmReceiveOrderLock.isLocked()){ // 是否还是锁定状态
                                if(xsjmReceiveOrderLock.isHeldByCurrentThread()){ // 时候是当前执行线程的锁
                                    xsjmReceiveOrderLock.unlock(); // 释放锁
                                }
                            }
                        }
                    }

                }
            });
        }

    }
    /**
     * 手动收货:超时后自动收掉
     * @param orderTime
     * @return
     * @throws Throwable
     */
    @Async
    public Boolean handleAutoReceive(String orderTime){
        List<Long> shopIdList = shopReceiveSettingService.getShopIdList(ShopReceiveTypeEnums.HANDLE.getCode());
        if(CollectionUtils.isEmpty(shopIdList)){
            return false;
        }

        List<Shop> shopList = shopService.getShopByIdList(shopIdList);
        List<Long> storeIdList = shopList.stream().map(item -> item.getStoreId()).collect(Collectors.toList());
        Map<Long, Long> shopMap = shopList.stream().collect(Collectors.toMap(Shop::getStoreId,Shop::getId,(key1 , key2)-> key2));
        List<Integer> logisticsModelList = Arrays.asList(IogisticsModelEnums.DIRECT_CONNECTION.getCode(), IogisticsModelEnums.DISPATCHING.getCode());

        List<ReceiveOrderVo> autoAllReceiveOrder = shopReceiveOrderMapper.getHandleAutoReceiveOrder(logisticsModelList, orderTime, storeIdList);
        List<Long> autoShopIdList = new ArrayList<>();
        for(ReceiveOrderVo o : autoAllReceiveOrder){
            o.setShopId(shopMap.get(Long.valueOf(o.getStoreId())));
            o.setCreateId(-1L);
            o.setReceiveId("-1");
            o.setRemark("手动收货,超时后自动收掉");
            o.setReceiveTime(new Date());
            String subOrderId = o.getSubOrderId();
            List<Quantity> autoDeliveryQuantity = shopReceiveOrderMapper.getHandleAutoDeliveryQuantity(subOrderId);
            o.setReceiveQuantities(autoDeliveryQuantity);
            try{
                shopHandleAutoReceiveService.addHandleReceive(o);
                autoShopIdList.add(o.getShopId());
            }catch (Exception e){
                log.error("手动收货，超时后自动收掉 失败 subOrderId {} Exception {}", subOrderId, e);

                StringBuffer sb = new StringBuffer();
                sb.append("手动收货,超时后自动收掉失败");
                //发送微信模板信息
                weChatSendMessageService.sendWeChatMessage(sb.toString());
            }
        }

        // 手动收货:超时后自动收掉成功的门店，关闭其收货单(t_xd_receive_doc)，然后创建一个新的收货单
        if(CollectionUtils.isNotEmpty(autoShopIdList)){
            xdReceiveService.createReceiveDoc(orderTime, autoShopIdList);
        }

        // 批量更新手动收货的门店单子为完成收货
        xdReceiveService.batchUpdateReceiveOrderPass(orderTime, shopIdList);

        return Boolean.TRUE;
    }


    /**
     * 自动收货:完成后提醒
     * @param
     * @return
     */
    @Async
    public Boolean autoReceiveRemind() {
        RBucket<List<String>> bucket = redissonClient.getBucket("autoReceiveStoreIdList");
        List<String> storeIds = bucket.get();
        if(CollectionUtils.isEmpty(storeIds)){
            return false;
        }

        List<Long> storeIdList = new ArrayList<>();
        storeIds.forEach(item -> {
            storeIdList.add(Long.valueOf(item));
        });

        // 构建线程池发送短信和广播
        buildPoolSendMsg(storeIdList, ReceiveOrderBusinessTypeEnums.AUTORECEIVE, null, "自动收货:完成后提醒");
        return Boolean.TRUE;
    }

    /**
     * 手动收货:t时间截止手动收货提醒
     * @param
     * @return
     */
    @Async
    public Boolean handleReceiveRemind() {
        DictionaryODTO dictionaryODTO = dictionaryClient.getDictionaryByCode("overTimeAutoReceive");
        String hour = dictionaryODTO.getOptionValue();

        List<Long> shopIdList = shopReceiveSettingService.getShopIdList(ShopReceiveTypeEnums.HANDLE.getCode());
        if(CollectionUtils.isEmpty(shopIdList)){
            return false;
        }
        List<Shop> shopList = shopService.getShopByIdList(shopIdList);
        List<Long> storeIdList = shopList.stream().map(item -> item.getStoreId()).collect(Collectors.toList());
        List<Integer> logisticsModelList = Arrays.asList(IogisticsModelEnums.DIRECT_CONNECTION.getCode(), IogisticsModelEnums.DISPATCHING.getCode());

        String orderTime = DateUtil.getDateFormate(new Date(),"yyyy-MM-dd");
        List<ReceiveOrderVo> autoAllReceiveOrder = shopReceiveOrderMapper.getHandleAutoReceiveOrder(logisticsModelList, orderTime, storeIdList);
        if(CollectionUtils.isNotEmpty(autoAllReceiveOrder)) {
            List<Long> idList = autoAllReceiveOrder.stream().map(item -> Long.valueOf(item.getStoreId())).distinct().collect(Collectors.toList());

            // 构建线程池发送短信和广播
            buildPoolSendMsg(idList, ReceiveOrderBusinessTypeEnums.HANDLERECEIVE, hour, "手动收货:t时间截止手动收货提醒");
        }
        return Boolean.TRUE;
    }


    /**
     * 构建线程池发送短信和广播
     */
    private void buildPoolSendMsg(List<Long> storeIdList, ReceiveOrderBusinessTypeEnums receiveOrderBusinessTypeEnums, String hour, String errorMsg) {
        List<ShopEmployeeODTO> shopEmployeeList = shopClient.getShopEmployeeListByStoreIdList(storeIdList);

        long currentTimeMillis = System.currentTimeMillis();

        // 获取线程池
        ThreadPoolTaskExecutor threadPool = (ThreadPoolTaskExecutor) SpringBeanFinder.getBean(ThreadPoolBeanConstants.ORDER_THREADPOOL);

        for (ShopEmployeeODTO shopEmployee : shopEmployeeList) {
            //创建任务并提交到线程池中
            threadPool.execute(new Runnable() {
                @Override
                public void run() {
                    try {
                        shopHandleAutoReceiveService.autoReceiveRemindSendMsg(shopEmployee, receiveOrderBusinessTypeEnums.getCode(), hour);
                        Thread.sleep(10);
                    } catch (Throwable e) {
                        log.error(errorMsg + "----异常", e);
                    }
                }
            });
        }

    }


    public PageInfo<ReceiveOrderMessageODTO> receiveOrderMessagePage(ReceiveOrderMessageIDTO idto) {
        PageInfo <ReceiveOrderMessageODTO> pageDate = null ;

        /*if (!StringUtil.isBlank(idto.getBeginDate()) && !StringUtil.isBlank(idto.getEndDate())){
            idto.setBeginDate(idto.getBeginDate()+ " 00:00:00");
            idto.setEndDate(idto.getEndDate()+ " 23:59:59");
        }*/

        pageDate = PageHelper.startPage(idto.getPageNo(), idto.getPageSize()).doSelectPageInfo(() -> {
            shopReceiveOrderMapper.getReceiveOrderMessagePage(idto);
        });

        return pageDate;
    }
}

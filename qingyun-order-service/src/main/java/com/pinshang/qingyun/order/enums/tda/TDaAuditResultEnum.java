package com.pinshang.qingyun.order.enums.tda;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import com.pinshang.qingyun.order.enums.BaseEnum;

/**
 * <AUTHOR>
 * @Version 0.1.0
 * @Date 2020/12/30 16:07
 * @Copyright © 2019-2020 qm
 * @Description -- 类说明 审核结果:0-审核不通过,1-审核通过
 **/
public enum TDaAuditResultEnum implements BaseEnum {

    FAIL(0, "审核不通过"),

    SUCCESS(1, "审核通过"),


    ;

    private int code;
    private String name;


    private TDaAuditResultEnum(int code, String name) {
        this.code = code;
        this.name = name;
    }

    @JsonValue
    public int getCode() {
        return this.code;
    }

    public String getName() {
        return this.name;
    }


    @JsonCreator
    public static TDaAuditResultEnum getByCode(Integer code) {
        if (code == null) {
            return null;
        } else {
            TDaAuditResultEnum[] var1 = values();
            int var2 = var1.length;
            for (int var3 = 0; var3 < var2; ++var3) {
                TDaAuditResultEnum value = var1[var3];
                if (value.getCode() == code) {
                    return value;
                }
            }
            return null;
        }
    }

    @Override
    public Integer convert() {
        return this.code;
    }
}

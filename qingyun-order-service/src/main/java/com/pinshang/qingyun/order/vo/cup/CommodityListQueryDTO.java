package com.pinshang.qingyun.order.vo.cup;

import com.pinshang.qingyun.box.utils.SpringUtil;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Arrays;
import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class CommodityListQueryDTO {

    private Long storeId;

    private String code;

    private List<String> codes;

    private List<String> selectedCodes;

    private Integer addCommodityType;

    private Integer planOrderType;

    public static CommodityListQueryDTO convert(CommodityListQueryReqVO reqVO){
        Long storeId = reqVO.getStoreId();
        String code = reqVO.getCode();
        Integer addCommodityType = reqVO.getAddCommodityType();
        String[] codes = reqVO.getCodes();
        String[] selectedCodes = reqVO.getSelectedCodes();
        Integer planOrderType = reqVO.getPlanOrderType();

        CommodityListQueryDTO queryDTO  = new CommodityListQueryDTO();
        queryDTO.setStoreId(storeId);
        queryDTO.setCode(code);
        queryDTO.setAddCommodityType(addCommodityType);
        queryDTO.setPlanOrderType(planOrderType);

        if(SpringUtil.isNotEmpty(codes)){
            queryDTO.setCodes(Arrays.asList(codes));
        }

        if(SpringUtil.isNotEmpty(selectedCodes)){
            queryDTO.setSelectedCodes(Arrays.asList(selectedCodes));
        }

        return queryDTO;
    }
}

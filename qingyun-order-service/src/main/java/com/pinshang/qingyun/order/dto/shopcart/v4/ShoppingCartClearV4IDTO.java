package com.pinshang.qingyun.order.dto.shopcart.v4;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2024/3/4 14:07
 */
@Data
public class ShoppingCartClearV4IDTO extends TdaDeliveryTimeRange{
    @ApiModelProperty("普通商品id集合")
    private List<Long> commodityIds;

    @ApiModelProperty("特惠商品id集合")
    private List<Long> thCommodityIds;

    @ApiModelProperty("送货日期")
    private Date orderTime;
}

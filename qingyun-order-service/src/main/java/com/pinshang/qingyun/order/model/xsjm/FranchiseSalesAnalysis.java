package com.pinshang.qingyun.order.model.xsjm;

import com.pinshang.qingyun.base.po.BaseIDPO;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.annotations.ApiOperation;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * 加盟店销售综合分析
 * 实体类
 */
@Data
@Entity
@EqualsAndHashCode(callSuper = true)
@Table(name = "t_franchise_sales_analysis")
public class FranchiseSalesAnalysis extends BaseIDPO {

    /**
     * 日结编号
     */
    private String dailySettleCode;

    /**
     * 日结日期 2024-07-02
     */
    private Date dailyDate;

    /**
     * 日结金额
     */
    private BigDecimal dailyAmount;

    /**
     * 日结类型：1-手动日结，2-自动日结
     */
    private Integer settleType;

    /**
     * 门店id
     */
    private Long shopId;

    /**
     * 销售额合计
     */
    @ApiModelProperty("销售额合计")
    private BigDecimal turnover;

    /**
     * 总客单数
     */
    @ApiModelProperty("总客单数")
    private Integer totalCustomers;

    /**
     * 截止到19点前的来客数
     */
    @ApiModelProperty("截止到19点前的来客数")
    @Column(name = "customers_before_19")
    private Integer customersBefore19;

    /**
     * 截止到19点前的销售额（销售额合计-退货金额合计）
     */
    @ApiModelProperty("截止到19点前的销售额")
    @Column(name = "sales_before_19")
    private BigDecimal salesBefore19;

    /**
     * 货款:进货金额合计-退货金额合计-少货金额合计
     */
    @ApiModelProperty("货款")
    private BigDecimal goodsPayment;

    /**
     * 时段折扣率:【折扣的金额合计/原价金额合计】/时段数*100%
     */
    @ApiModelProperty("时段折扣率")
    private BigDecimal discountRate;

    /**
     * 损耗率:到货数量-退货数量-少货数量-销售数量）/（到货数量-退货数量-少货数量）
     */
    @ApiModelProperty("损耗率")
    private BigDecimal lossRate;

    /**
     * 加盟商净毛利（总销售额-总进货-手续费）
     */
    @ApiModelProperty("加盟商净毛利")
    private BigDecimal franchiseeNetProfit;

    /**
     * 备注
     */
    private String remark;

    /**
     * 修改人id
     */
    private Long updateId;

    /**
     * 创建人id
     */
    private Long createId;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;
}

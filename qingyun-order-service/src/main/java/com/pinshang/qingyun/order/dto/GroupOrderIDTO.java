package com.pinshang.qingyun.order.dto;

import com.pinshang.qingyun.base.page.Pagination;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Author: sk
 * @Date: 2021/12/28
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class GroupOrderIDTO extends Pagination {

    // 1 商品订货分析表 2 门店订货分析表
    private Integer groupType;

    @ApiModelProperty("日期开始时间 yyyy-MM-dd")
    private String beginDate;
    @ApiModelProperty("日期结束时间 yyyy-MM-dd")
    private String endDate;

    @ApiModelProperty("商品id")
    private Long commodityId;

    @ApiModelProperty("团购活动编码")
    private String grouponCode;

    @ApiModelProperty("门店id")
    private Long shopId;
}

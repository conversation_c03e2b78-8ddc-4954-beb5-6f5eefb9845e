package com.pinshang.qingyun.order.dto.bcountry;

import com.pinshang.qingyun.page.Pagination;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025年07月16日 下午1:57
 */
@Data
public class PlanCommodityQueryIDTO extends Pagination {

    @ApiModelProperty("商品id")
    private Long commodityId;

    @ApiModelProperty("商品idList")
    private List<Long> commodityIdList;

    @ApiModelProperty("大类id")
    private Long cateId1;

    @ApiModelProperty("中类id")
    private Long cateId2;

    @ApiModelProperty("小类id")
    private Long cateId3;

    @ApiModelProperty("同步状态 1=已同步  0=未同步")
    private Integer syncStatus;
}

package com.pinshang.qingyun.order.validation;

import com.pinshang.qingyun.order.dto.cup.OrderRequestDto;

public abstract class OrderValidationHandler {

    protected OrderValidationHandler nextHandler;

    public void setNextHandler(OrderValidationHandler nextHandler){
        this.nextHandler = nextHandler;
    }

    public abstract OrderValidationResult validate(OrderRequestDto orderRequestDto);

    protected OrderValidationResult validateNext(OrderRequestDto orderRequestDto){
        if(nextHandler != null){
            return nextHandler.validate(orderRequestDto);
        }

        return new OrderValidationResult(true,"validation passed");
    }
}

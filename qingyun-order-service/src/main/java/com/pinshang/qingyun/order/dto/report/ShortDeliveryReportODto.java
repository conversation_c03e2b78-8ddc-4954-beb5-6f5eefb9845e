package com.pinshang.qingyun.order.dto.report;

import com.pinshang.qingyun.base.enums.shop.ShopTypeEnums;
import com.pinshang.qingyun.renderer.annotation.FieldRender;
import com.pinshang.qingyun.renderer.constant.RenderFieldHelper;
import com.pinshang.qingyun.renderer.enums.FieldTypeEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.Date;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class ShortDeliveryReportODto {
	private Long storeId;
	private String shopCode;
	private String shopName;
	private Date deliveryDate;
	private String orderCode;
	@FieldRender(fieldType = FieldTypeEnum.COMMODITY_KIND,fieldName = RenderFieldHelper.CommodityKind.commodityFirstKindName,keyName = "commodityId")
	private String categoryName;
	@FieldRender(fieldType = FieldTypeEnum.COMMODITY_KIND,fieldName = RenderFieldHelper.CommodityKind.commoditySecondKindName,keyName = "commodityId")
	private String secondCategoryName;
	@FieldRender(fieldType = FieldTypeEnum.COMMODITY_KIND,fieldName = RenderFieldHelper.CommodityKind.commodityThirdKindName,keyName = "commodityId")
	private String thirdCategoryName;
	private String barCode;
	private String barCodes;	// 子码列表
	private String commodityId;
	private String commodityCode;
	private String commodityName;
	private String commoditySpec;
	@FieldRender(fieldType = FieldTypeEnum.DICTIONARY,fieldName = RenderFieldHelper.Dictionary.optionName,keyName = "commodityUnitId")
	private String commodityUnitName;
	private String commodityUnitId; // 单位id
	private BigDecimal orderNum;
    private BigDecimal deliveryNum;
	private BigDecimal differNum;
	private String rate;
	private BigDecimal price;
	private String factoryName;
	private String workshopName;
	private String storeCode;
	@FieldRender(fieldType = FieldTypeEnum.USER,fieldName = RenderFieldHelper.User.realName,keyName = "createId")
	private String createName;
	private Integer shopType;

	public String getShopTypeName() {
		return ShopTypeEnums.getName(this.shopType);
	}

	@ApiModelProperty("线路组")
	private String storeLineGroupName;

	@ApiModelProperty("仓库名称")
	private String warehouseName;

	private Long createId;

	@ApiModelProperty("经营模式")
	private String managementModeName;
	@ApiModelProperty("档口Id")
	private Long stallId;
	@ApiModelProperty("档口名称")
	@FieldRender(fieldType = FieldTypeEnum.STALL, fieldName = RenderFieldHelper.Stall.stallName, keyName = "stallId")
	private String stallName;
}

package com.pinshang.qingyun.order.dto;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * @Author: sk
 * @Date: 2024/3/7
 */
@Data
public class BStockShortODTO {

    private Long storeId;
    private String storeCode;
    private String storeName;
    private Long storeTypeId;

    private Long commodityId;
    private String commodityCode;
    private String commodityName;
    private String commoditySpec;
    private BigDecimal commodityPackageSpec;
    private BigDecimal salesBoxCapacity;
    private String commodityUnitName;

    private String stockType;
    private BigDecimal stockQuantity;
    private BigDecimal needQuantity;

    private Integer orderType;
    private String remark;
    private Long createId;
    private Date createTime;
}

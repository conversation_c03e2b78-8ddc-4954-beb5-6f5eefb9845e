package com.pinshang.qingyun.order.manage.delivery.utils;

import com.pinshang.qingyun.base.enums.StoreTypeClassEnums;
import com.pinshang.qingyun.base.enums.StoreTypeEnums;
import com.pinshang.qingyun.base.enums.order.OrderTypeEnum;
import com.pinshang.qingyun.order.manage.delivery.dto.DeliveryOrderDTO;
import com.pinshang.qingyun.order.manage.delivery.setting.StoreDeliverySetting;

public class PfDeliverySettingUtils {
    public static boolean ifPfUnderDeliveryConfition(DeliveryOrderDTO deliveryOrderDTO,StoreDeliverySetting storeDeliverySetting) {

        boolean isPfOrderType = (OrderTypeEnum.fromCode(deliveryOrderDTO.getOrderType()) == OrderTypeEnum.PF_APP_ORDER)
                || (OrderTypeEnum.fromCode(deliveryOrderDTO.getOrderType()) == OrderTypeEnum.XDA_APP_ORDER)
                || (OrderTypeEnum.fromCode(deliveryOrderDTO.getOrderType()) == OrderTypeEnum.PC_ORDER);
        boolean isPfStoreType = !((StoreTypeEnums.getById(storeDeliverySetting.getStoreTypeId()) != StoreTypeEnums.PFS)
                || (StoreTypeClassEnums.fromCode(storeDeliverySetting.getStoreType()) != StoreTypeClassEnums.EXTERNAL));
        return isPfOrderType && isPfStoreType;
    }

    public static void main(String[] args) {
        DeliveryOrderDTO deliveryOrderDTO = new DeliveryOrderDTO();
        deliveryOrderDTO.setOrderType(1);
        StoreDeliverySetting storeDeliverySetting = new StoreDeliverySetting();
        storeDeliverySetting.setStoreTypeId(9131078242888821656L);
        storeDeliverySetting.setStoreType(2);
        System.out.println(ifPfUnderDeliveryConfition(deliveryOrderDTO,storeDeliverySetting));
    }
}

package com.pinshang.qingyun.order.model.gift;

import java.math.BigDecimal;

import javax.persistence.Entity;
import javax.persistence.Table;

import com.pinshang.qingyun.base.po.BaseIDPO;

@Entity
@Table(name = "t_gift_product")
public class GiftProduct extends BaseIDPO {
	
	/**赠品方案明细id **/
    private Long giftModelConditionId;
	/**商品id **/
    private Long commodityId;
	/**商品code **/
    private String commodityCode;
	/**商品数量 **/
    private BigDecimal commodityNumber;
	/**商品最大数量 **/
    private BigDecimal commodityMaxNumber;
	/**商品价格 **/
    private BigDecimal commodityPrice;
	/**状态 **/
    private Integer status;
	/**备注 **/
    private String remark;
    
	public Long getGiftModelConditionId(){
        return giftModelConditionId;
    }
    public void setGiftModelConditionId(Long giftModelConditionId){
        this.giftModelConditionId = giftModelConditionId;
    }
    public Long getCommodityId(){
        return commodityId;
    }
    public void setCommodityId(Long commodityId){
        this.commodityId = commodityId;
    }
    public String getCommodityCode(){
        return commodityCode;
    }
    public void setCommodityCode(String commodityCode){
        this.commodityCode = commodityCode;
    }
    public BigDecimal getCommodityNumber(){
        return commodityNumber;
    }
    public void setCommodityNumber(BigDecimal commodityNumber){
        this.commodityNumber = commodityNumber;
    }
    public BigDecimal getCommodityMaxNumber(){
        return commodityMaxNumber;
    }
    public void setCommodityMaxNumber(BigDecimal commodityMaxNumber){
        this.commodityMaxNumber = commodityMaxNumber;
    }
    public BigDecimal getCommodityPrice(){
        return commodityPrice;
    }
    public void setCommodityPrice(BigDecimal commodityPrice){
        this.commodityPrice = commodityPrice;
    }
    public Integer getStatus(){
        return status;
    }
    public void setStatus(Integer status){
        this.status = status;
    }
    public String getRemark(){
        return remark;
    }
    public void setRemark(String remark){
        this.remark = remark;
    }
}
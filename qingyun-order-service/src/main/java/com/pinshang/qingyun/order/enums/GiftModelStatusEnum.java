package com.pinshang.qingyun.order.enums;

import java.util.EnumSet;

import com.pinshang.qingyun.box.utils.EnumUtils;

/**
 * 赠品方案枚举类
 */
public enum GiftModelStatusEnum {

	OPENED(1, "启用"),
	CLOSED(2,"停用");
	

	private Integer code;
	private String remark;
	
	private GiftModelStatusEnum(Integer code, String remark) {
		this.setCode(code);
		this.setRemark(remark);
	}

	public static GiftModelStatusEnum fromName(Integer code) {
		return EnumUtils.fromEnumProperty(GiftModelStatusEnum.class, "code", code);
	}

	public static EnumSet<GiftModelStatusEnum> allList() {
		EnumSet<GiftModelStatusEnum> adverPositionList = EnumSet.allOf(GiftModelStatusEnum.class);
		return adverPositionList;
	}

	public Integer getCode() {
		return code;
	}

	public void setCode(Integer code) {
		this.code = code;
	}

	public String getRemark() {
		return remark;
	}

	public void setRemark(String remark) {
		this.remark = remark;
	}
	
}

package com.pinshang.qingyun.order.model.order;

import com.pinshang.qingyun.base.po.BaseIDPO;
import lombok.Data;

import javax.persistence.Table;
import java.math.BigDecimal;
import java.util.Date;

/**
 *
 */
@Table(name = "t_xda_commodity_sale_day_statistics")
@Data
public class XdaCommoditySaleDayStatistics extends BaseIDPO {
    private Long commodityId;
    private BigDecimal totalQuantity;
    private BigDecimal totalAmount;
    private Date orderTime;
}

package com.pinshang.qingyun.order.vo.tob;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.Date;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class ToBOrderStatisticsReqVo {
    private Date orderTime;
    private Long commodityId;
    // 1:下单；2：取消
    private Integer type;
    private BigDecimal quantity;
    private Integer number;
}

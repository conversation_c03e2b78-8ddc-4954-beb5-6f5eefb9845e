package com.pinshang.qingyun.order.dto.xda.v2;


import com.pinshang.qingyun.base.page.Pagination;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;


/**
 * <AUTHOR>
 * @Date 2019/11/20 9:50
 */
@Data
@ApiModel
public class XdaQueryOrderAppParamV2 extends Pagination {
    @ApiModelProperty("流程状态(鲜达新加) 7=待发货11=出库中15=配送中19=配送完成")
    private String processStatus;
    @ApiModelProperty(value = "订单编码", hidden = true)
    private String orderCode;
    @ApiModelProperty(value = "客户id", hidden = true)
    private Long storeId;

}

package com.pinshang.qingyun.order.dto.cup;

import java.math.BigDecimal;

public class ProductPriceDto {

	private String productId;
	private String productCode;
	private String productName;
	private BigDecimal price;
	private BigDecimal limitNum;//限量
	private BigDecimal originalPrice;

	/**限制购买的数量*/
	private Integer limitNumber;

	/** 剩余可用的总限量*/
	private BigDecimal availableTotalLimit;

	/** 剩余可用的客户限购*/
	private BigDecimal availableStoreLimit;

	/** 特价Id*/
	private Long pricePromotionId;

	/**
	 * 是否特价标识，0否， 1是
	 */
	private boolean specialFlag;

	public boolean isSpecialFlag() {
		return specialFlag;
	}

	public void setSpecialFlag(boolean specialFlag) {
		this.specialFlag = specialFlag;
	}

	public Integer getLimitNumber() {
		return limitNumber;
	}

	public void setLimitNumber(Integer limitNumber) {
		this.limitNumber = limitNumber;
	}

	public BigDecimal getAvailableTotalLimit() {
		return availableTotalLimit;
	}

	public void setAvailableTotalLimit(BigDecimal availableTotalLimit) {
		this.availableTotalLimit = availableTotalLimit;
	}

	public BigDecimal getAvailableStoreLimit() {
		return availableStoreLimit;
	}

	public void setAvailableStoreLimit(BigDecimal availableStoreLimit) {
		this.availableStoreLimit = availableStoreLimit;
	}

	public Long getPricePromotionId() {
		return pricePromotionId;
	}

	public void setPricePromotionId(Long pricePromotionId) {
		this.pricePromotionId = pricePromotionId;
	}

	public BigDecimal getOriginalPrice() {
		return originalPrice;
	}

	public void setOriginalPrice(BigDecimal originalPrice) {
		this.originalPrice = originalPrice;
	}

	public String getProductId() {
		return productId;
	}
	public void setProductId(String productId) {
		this.productId = productId;
	}
	public String getProductCode() {
		return productCode;
	}
	public void setProductCode(String productCode) {
		this.productCode = productCode;
	}
	public BigDecimal getPrice() {
		return price;
	}
	public void setPrice(BigDecimal price) {
		this.price = price;
	}
	public BigDecimal getLimitNum() {
		return limitNum;
	}
	public void setLimitNum(BigDecimal limitNum) {
		this.limitNum = limitNum;
	}
	public String getProductName() {
		return productName;
	}
	public void setProductName(String productName) {
		this.productName = productName;
	}
}

package com.pinshang.qingyun.order.model.order;

import com.pinshang.qingyun.base.po.BaseSimplePO;
import lombok.Data;

import javax.persistence.Entity;
import javax.persistence.Table;
import java.util.Date;

@Entity
@Table(name = "t_stock_out_settle_msg")
@Data
public class StockOutSettleMsg extends BaseSimplePO {

	private Long subOrderId;

	private String stockOutOrderCode;
	private Date stockOutTime;

	private Integer status;

	/** 消息类型 **/
	private String optionType;

	private Date updateTime;
}
package com.pinshang.qingyun.order.enums;

import java.util.EnumSet;

import com.pinshang.qingyun.box.utils.EnumUtils;

/**
 * 赠品方案枚举类
 */
public enum GiftModelScopeTypeEnum {

	SETTLEMENT(1,"结账客户"),
	PRODUCTPRICEMODEL(2,"产品价格方案"),
	STORE(3,"客户");
	
	private Integer code;
	private String remark;
	
	private GiftModelScopeTypeEnum(Integer code, String remark) {
		this.setCode(code);
		this.setRemark(remark);
	}

	public static GiftModelScopeTypeEnum fromName(Integer code) {
		return EnumUtils.fromEnumProperty(GiftModelScopeTypeEnum.class, "code", code);
	}

	public static EnumSet<GiftModelScopeTypeEnum> allList() {
		EnumSet<GiftModelScopeTypeEnum> adverPositionList = EnumSet.allOf(GiftModelScopeTypeEnum.class);
		return adverPositionList;
	}

	public Integer getCode() {
		return code;
	}

	public void setCode(Integer code) {
		this.code = code;
	}

	public String getRemark() {
		return remark;
	}

	public void setRemark(String remark) {
		this.remark = remark;
	}
	
}

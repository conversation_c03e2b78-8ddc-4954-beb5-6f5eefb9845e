package com.pinshang.qingyun.order.model.order;

import com.pinshang.qingyun.base.po.BaseEnterprisePO;
import lombok.Data;

import javax.persistence.Table;
import javax.persistence.Transient;
import java.util.Date;
import java.util.List;

/**
 * Created by honway on 2017/11/3 10:59.
 */

@Data
@Table(name="t_sale_return_order")
public class SaleReturnOrder extends BaseEnterprisePO {
    /**客户ID*/
    private Long storeId;

    /**
     * 档口ID
     */
    private Long stallId;

    /**退货单号*/
    private String orderCode;

    /**0＝取消，1＝待入库(待确认)，2＝已入库（已确认）*/
    private Integer status;

    /**物流配送模式0=直送，1＝配送，2＝直通*/
    private Integer logisticsModel;

    /**供应商ID*/
    private Long supplierId;

    /**仓库ID*/
    private Long warehouseId;

    /**备注*/
    private String remark;

    private Date updateTime;

    @Transient
    private List<SaleReturnOrderItem> itemList;

    private Long consignmentId; // 代销商户id
}

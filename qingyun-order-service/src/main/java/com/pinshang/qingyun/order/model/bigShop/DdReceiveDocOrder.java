package com.pinshang.qingyun.order.model.bigShop;

import com.pinshang.qingyun.base.po.BasePO;

import javax.persistence.Entity;
import javax.persistence.Table;
import java.math.BigDecimal;
import java.util.Date;

/**
 * @Author: sk
 * @Date: 2024/10/11
 */
@Entity
@Table(name="t_dd_receive_doc_order")
public class DdReceiveDocOrder extends BasePO {

    private Long docId;

    /** 送货日期 */
    private Date orderTime;

    /** 门店id **/
    private Long shopId;

    /** 订单号 **/
    private String orderCode;

    /** 物流配送模式, 0-直送, 1-配送, 2-直通 */
    private Integer logisticsModel;

    /** 订单金额 **/
    private BigDecimal orderAmount;

    public Long getDocId() {
        return docId;
    }

    public void setDocId(Long docId) {
        this.docId = docId;
    }

    public Date getOrderTime() {
        return orderTime;
    }

    public void setOrderTime(Date orderTime) {
        this.orderTime = orderTime;
    }

    public Long getShopId() {
        return shopId;
    }

    public void setShopId(Long shopId) {
        this.shopId = shopId;
    }

    public String getOrderCode() {
        return orderCode;
    }

    public void setOrderCode(String orderCode) {
        this.orderCode = orderCode;
    }

    public Integer getLogisticsModel() {
        return logisticsModel;
    }

    public void setLogisticsModel(Integer logisticsModel) {
        this.logisticsModel = logisticsModel;
    }

    public BigDecimal getOrderAmount() {
        return orderAmount;
    }

    public void setOrderAmount(BigDecimal orderAmount) {
        this.orderAmount = orderAmount;
    }
}

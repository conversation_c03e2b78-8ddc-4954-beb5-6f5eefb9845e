package com.pinshang.qingyun.order.model.gift;

import com.pinshang.qingyun.base.po.BaseEnterprisePO;

import javax.persistence.Entity;
import javax.persistence.Table;
import java.util.Date;

/**
 * 
 **/
@Entity
@Table(name = "t_gift_model")
public class GiftModel extends BaseEnterprisePO {
	
	/**赠品方案code **/
    private String giftModelCode;
	/**赠品方案name **/
    private String giftModelName;
	/**赠品开始时间 **/
    private Date beginDate;
	/**赠品结束时间 **/
    private Date endDate;
	/**赠品类型 1-订单金额,2-商品数量,3-商品金额 **/
    private Integer giftModelType;
	/**赠品方案状态 **/
    private Integer status;
	/**备注 **/
    private String remark;
	/**商品组合 **/
    private String commmdityCodes;
	/**创建人id **/
    private Long createId;
	/**创建人名称 **/
    private String createName;
	/**更新人id **/
    private Long updateId;
    /**赠品条件对象列表**/

    public String getGiftModelCode(){
        return giftModelCode;
    }
    public void setGiftModelCode(String giftModelCode){
        this.giftModelCode = giftModelCode;
    }
    public String getGiftModelName(){
        return giftModelName;
    }
    public void setGiftModelName(String giftModelName){
        this.giftModelName = giftModelName;
    }
    public Date getBeginDate(){
        return beginDate;
    }
    public void setBeginDate(Date beginDate){
        this.beginDate = beginDate;
    }
    public Date getEndDate(){
        return endDate;
    }
    public void setEndDate(Date endDate){
        this.endDate = endDate;
    }
    public Integer getGiftModelType(){
        return giftModelType;
    }
    public void setGiftModelType(Integer giftModelType){
        this.giftModelType = giftModelType;
    }
    public Integer getStatus(){
        return status;
    }
    public void setStatus(Integer status){
        this.status = status;
    }
    public String getRemark(){
        return remark;
    }
    public void setRemark(String remark){
        this.remark = remark;
    }
    public String getCommmdityCodes(){
        return commmdityCodes;
    }
    public void setCommmdityCodes(String commmdityCodes){
        this.commmdityCodes = commmdityCodes;
    }
    @Override
    public Long getCreateId(){
        return createId;
    }
    @Override
    public void setCreateId(Long createId){
        this.createId = createId;
    }
    public String getCreateName(){
        return createName;
    }
    public void setCreateName(String createName){
        this.createName = createName;
    }
    @Override
    public Long getUpdateId(){
        return updateId;
    }
    @Override
    public void setUpdateId(Long updateId){
        this.updateId = updateId;
    }
}
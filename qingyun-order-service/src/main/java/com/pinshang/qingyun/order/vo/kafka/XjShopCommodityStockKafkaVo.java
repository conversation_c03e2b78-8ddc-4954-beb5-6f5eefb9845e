package com.pinshang.qingyun.order.vo.kafka;

import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * 门店盘点覆盖库存发消息到xd-wms
 */
@Data
public class XjShopCommodityStockKafkaVo {
    private Long shopId;
    private List<XjShopCommodityStockItemVo> commodityList;
    private Long referId;
    private String referCode;
    private Long userId;
    private Integer typeCode;

    @Data
    public static class XjShopCommodityStockItemVo {
        private Long commodityId;
        private BigDecimal quantity;
        private Integer stockNumber;

        private BigDecimal currentQuantity;
        private Integer currentStockNumber;
    }

}
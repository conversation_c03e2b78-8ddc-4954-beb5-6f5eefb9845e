package com.pinshang.qingyun.order.controller.client.finance;

import com.pinshang.qingyun.order.dto.finance.DirectDeliveryOrderIDTO;
import com.pinshang.qingyun.order.dto.finance.DirectDeliveryOrderODTO;
import com.pinshang.qingyun.order.dto.finance.ShopLessGoodsOrderIDTO;
import com.pinshang.qingyun.order.dto.finance.ShopLessGoodsOrderODTO;
import com.pinshang.qingyun.order.service.SaleReturnService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/7/2 9:41
 */
@RestController
@RequestMapping("/orderFinance/client")
@Api(value = "OrderFinanceClientController", tags = "供财务 内部往来单据提供原始数据client接口-API")
public class OrderFinanceClientController {

    @Autowired
    private SaleReturnService saleReturnService;

    @ApiOperation(value = "门店直送订单/退货", notes = "门店直送订单/退货", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @PostMapping("/directDeliveryOrder/list")
    public List<DirectDeliveryOrderODTO> selectDirectDeliveryOrderList(@RequestBody DirectDeliveryOrderIDTO idto){
        return saleReturnService.selectDirectDeliveryOrderList(idto);
    }


    @ApiOperation(value = "门店少货", notes = "门店少货", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @PostMapping("/shopLessGoods/list")
    public List<ShopLessGoodsOrderODTO> selectShopLessGoodsOrderList(@RequestBody ShopLessGoodsOrderIDTO idto){
        return saleReturnService.selectShopLessGoodsOrderList(idto);
    }

}

package com.pinshang.qingyun.order.model.order;

import com.pinshang.qingyun.base.po.BaseSimplePO;
import com.pinshang.qingyun.order.dto.miniGroupon.MiniGrouponAutoOrderIDTO;
import lombok.Data;
import lombok.Getter;

import javax.persistence.Entity;
import javax.persistence.Table;
import javax.persistence.Transient;
import java.math.BigDecimal;
import java.util.Date;

/*
 * 子订单明细表
 */
@Entity
@Table(name = "t_sub_order_item")
@Data
public class SubOrderItem extends BaseSimplePO {
	//子订单id
	private Long subOrderId;
	//商品id
	private Long commodityId;
	//数量
	private BigDecimal quantity;
	//单价
	private BigDecimal price;
	private BigDecimal totalPrice;
	
	//实收数量(门店收货)
	private BigDecimal realReceiveQuantity;
	//状态: 0-审核未通过,1-审核通过
	private Integer status;
	//real_delivery_quantity实发数量(暂时没用)
	private BigDecimal realDeliveryQuantity;
	//驳回原因
	private String rejectReason;
	// 商品价格是否可变
	private Integer changePriceStatus;

	@Transient
    private Long miniOrderId;

	private Date updateTime;
	//组合商品转换的最小单位商品ID
	private Long targetCommodityId;
	//组合商品转换状态：0=无转换，1=有转换
	private Integer convertStatus;

	//组合商品源转换比率
	private Integer sourceRatio;

	//组合商品目标转换比率
	private Integer targetRatio;

	//组合商品目标数量 : 向上取整 ( quantity / source_ratio * target_ratio)
	private BigDecimal targetQuantity;

	/** 类型:1订单商品 2赠品 3配货商品 4、配比商品 5特惠商品 */
	private Integer type = 1;
	/** 商品类型-1-非组合 2-组合(不存在)  3-组合子品 */
	private Integer combType = 1;
	/** 属于组合商品id(comb_type = 3有值，对应组合品commodityId) */
	private Long combCommodityId;
	/** 对应t_order_list表的主键id */
	private Long combOrderListId;

	@Transient
	private boolean isDone;


	private Long pricePromotionId;

	public boolean isDone() {
		return isDone;
	}

	public void setDone(boolean done) {
		isDone = done;
	}

	public static SubOrderItem initForMiniFroupon(MiniGrouponAutoOrderIDTO item, Integer changePriceStatus){
        SubOrderItem subOrderItem = new SubOrderItem();
        subOrderItem.setCommodityId(item.getCommodityId());
        subOrderItem.setPrice(item.getPromotionPrice()==null?item.getOriginPrice():item.getPromotionPrice());
        subOrderItem.setQuantity(item.getQuantity());
        subOrderItem.setTotalPrice(item.getOrderAmount());
        subOrderItem.setCreateId(-1L);
        subOrderItem.setCreateTime(new Date());
		subOrderItem.setUpdateTime(new Date());
        subOrderItem.setChangePriceStatus(changePriceStatus);
        subOrderItem.setMiniOrderId(item.getMiniGrouponOrderId());
        return subOrderItem;
    }

}

package com.pinshang.qingyun.order.manage.factory;

import com.pinshang.qingyun.order.dto.order.storemoney.OrderMoneyConditionDTO;
import com.pinshang.qingyun.order.manage.price.StorePriceManage;
import com.pinshang.qingyun.order.mapper.StoreSettlementMapper;
import com.pinshang.qingyun.order.mapper.entry.order.OrderRealDeliveryFinishEntry;
import com.pinshang.qingyun.order.service.ShopService;
import com.pinshang.qingyun.settlementTb.service.StoreRechargeClient;
import com.pinshang.qingyun.store.service.StoreTypeClient;
import org.springframework.stereotype.Component;

import java.util.Map;

@Component
public class StorePriceManageFactory {

    private final StoreSettlementMapper storeSettlementMapper;
    private final StoreTypeClient storeTypeClient;
    private final ShopService shopService;
    private final StoreRechargeClient storeRechargeClient;

    public StorePriceManageFactory(StoreSettlementMapper storeSettlementMapper, StoreTypeClient storeTypeClient, ShopService shopService, StoreRechargeClient storeRechargeClient) {
        this.storeSettlementMapper = storeSettlementMapper;
        this.storeTypeClient = storeTypeClient;
        this.shopService = shopService;
        this.storeRechargeClient = storeRechargeClient;
    }

    public StorePriceManage create(Map<Long, OrderRealDeliveryFinishEntry> deliveryFinishOrderMap
            , Map<Long, OrderMoneyConditionDTO> orderMoneyConditionDTOMap){
        return new StorePriceManage(deliveryFinishOrderMap,orderMoneyConditionDTOMap,storeSettlementMapper,
                storeTypeClient,shopService, storeRechargeClient);
    }
}

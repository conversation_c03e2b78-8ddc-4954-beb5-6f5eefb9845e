package com.pinshang.qingyun.order.vo.order;

import com.pinshang.qingyun.bigdata.dto.shoporderingrefmetrics.ShopOrderingMetricsLineChartNode;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * <p>
 * 多订、少订的商品详情
 * </p>
 *
 * <AUTHOR>
 * @since 2025-02-19
 */
@Data
public class OverUnderOrderingCommodityInfoVo {
    @ApiModelProperty(value = "商品id")
    private Long commodityId;

    @ApiModelProperty("商品名称")
    private String commodityName;

    @ApiModelProperty("商品编码")
    private String commodityCode;

    @ApiModelProperty("商品规格")
    private String commoditySpec;

    @ApiModelProperty("订货数量")
    private BigDecimal orderQuantity;

    @ApiModelProperty("已定未收数量")
    private BigDecimal orderedQuantity;

    @ApiModelProperty(value = "近15天日均销量")
    private BigDecimal avgDailySales15Days;

    @ApiModelProperty("保质期天数")
    private Integer qualityDays;

    @ApiModelProperty("库存数量")
    private BigDecimal stockQuantity;

    @ApiModelProperty(value = "销售数量趋势")
    private List<ShopOrderingMetricsLineChartNode> salesTrend;
}

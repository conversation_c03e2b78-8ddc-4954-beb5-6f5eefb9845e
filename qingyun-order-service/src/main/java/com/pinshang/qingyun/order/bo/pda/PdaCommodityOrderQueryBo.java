package com.pinshang.qingyun.order.bo.pda;

import com.pinshang.qingyun.base.configure.expand.QYAssert;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.util.Objects;

@Data
@ApiModel
public class PdaCommodityOrderQueryBo {

    @ApiModelProperty("shopId")
    private Long shopId;

    @ApiModelProperty("storeId")
    private Long storeId;

    @ApiModelProperty("商品id")
    private Long commodityId;

    @ApiModelProperty("送货日期 yyyy-MM-dd")
    private String orderTime;



}

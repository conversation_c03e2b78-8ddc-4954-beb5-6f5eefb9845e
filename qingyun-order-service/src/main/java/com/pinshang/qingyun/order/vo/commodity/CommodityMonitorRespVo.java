package com.pinshang.qingyun.order.vo.commodity;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2018/10/11 10:44
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class CommodityMonitorRespVo {
    /**
     * 订单商品总数
     */
    private BigDecimal orderNum;
    /**
     * 最新的订单创建时间
     */
    private Date lastOrderTime;
    /**
     * 取消的订单商品总数
     */
    private BigDecimal orderCancelNum;
    /**
     * 子订单商品总数
     */
    private BigDecimal subOrderNum;
    /**
     * 最新的子订单创建时间
     */
    private Date lastSubOrderTime;
    /**
     * 子单id集合
     */
    private List<Long> subOrderIds;
}

package com.pinshang.qingyun.order.vo.order;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class OrderItemCountLog {

    private Long shopId;

    private String shopName;

    private String orgName;

    private String sendDate;

    private Long commodityId;

    private String commodityCode;

    private String barCode;

    private String commodityName;

    private String commoditySpec;

    private BigDecimal countQuantity;

    private Long createId;

    private String createName;

    private String createTime;
}

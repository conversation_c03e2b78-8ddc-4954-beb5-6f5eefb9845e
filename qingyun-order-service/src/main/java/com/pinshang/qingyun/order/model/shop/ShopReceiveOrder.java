package com.pinshang.qingyun.order.model.shop;

import com.pinshang.qingyun.base.po.BaseEnterprisePO;
import com.pinshang.qingyun.box.utils.SpringUtil;
import com.pinshang.qingyun.order.enums.ShopReceiveOrderStatusEnums;
import com.pinshang.qingyun.order.enums.XdaPayTypeEnum;
import com.pinshang.qingyun.order.model.order.Order;
import com.pinshang.qingyun.order.model.order.OrderBill;
import lombok.Data;

import javax.persistence.Entity;
import javax.persistence.Table;
import java.math.BigDecimal;
import java.util.Calendar;
import java.util.Date;

@Entity
@Table(name="t_md_receive_order")
@Data
public class ShopReceiveOrder extends BaseEnterprisePO{
    private Long subOrderId;

    private Integer status;

    private Date receiveTime;

    private Long receiveId;

    private String remark;

    public static ShopReceiveOrder initForMiniFroupon(Long subOrderId){
        ShopReceiveOrder s = new ShopReceiveOrder();
        Calendar c = Calendar.getInstance();
        c.add(Calendar.DAY_OF_YEAR, 2);
        s.setReceiveTime(c.getTime());
        s.setCreateTime(c.getTime());
        s.setCreateId(-1L);
        s.setUpdateId(-1L);
        s.setEnterpriseId(78L);
        s.setStatus(ShopReceiveOrderStatusEnums.UNRECEIVED.getCode());
        s.setReceiveId(-1L);
        s.setSubOrderId(subOrderId);
        return s;
    }

}
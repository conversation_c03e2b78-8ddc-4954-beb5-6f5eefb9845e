package com.pinshang.qingyun.order.vo.splitOrder;

import com.pinshang.qingyun.kafka.enums.KafkaMessageOperationTypeEnum;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;
import java.util.Map;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class SplitOrderKafkaVo {
	private Long orderId;
	private Long enterpriseId;
	private Long createId;
	private KafkaMessageOperationTypeEnum type;
	private Date orderTime;

	/** 冻结返回的商品仓库信息 key：商品id  value: 仓库id */
	private Map<Long, Long> commWarehouseMap;
}

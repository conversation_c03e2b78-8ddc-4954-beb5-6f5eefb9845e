package com.pinshang.qingyun.order.vo.splitOrder;

import com.pinshang.qingyun.kafka.enums.KafkaMessageOperationTypeEnum;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class SplitOrderKafkaVo {
	private Long orderId;
	private Long enterpriseId;
	private Long createId;
	private KafkaMessageOperationTypeEnum type;
	private Date orderTime;
}

package com.pinshang.qingyun.order.bo;

import lombok.Data;

import java.util.List;

/**
 * @Description：提货预约查询参数
 * @Author：<PERSON><PERSON><PERSON>
 * @Package：com.pinshang.qingyun.order.bo
 * @Date: 2024/4/15
 */
@Data
public class QueryTakeBO {

    //下单状态：0-未下单、1-已下单
    private Integer orderStatus;

    //送货日期时间集合
    private List<String> orderTimes;

    //商品id集合
    private List<String> commodityIds;

    //送货开始日期
    private String startOrderTime;

    //送货结束日期
    private String endOrderTime;
}

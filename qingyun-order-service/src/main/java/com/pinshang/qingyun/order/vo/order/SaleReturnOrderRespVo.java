package com.pinshang.qingyun.order.vo.order;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class SaleReturnOrderRespVo {

    /** 客户名称 */
    private String storeName;
    /** 退货单日期 */
    private Date returnOrderDate;
    /** 退货单号 */
    private String returnOrderCode;

    /** 仓库 */
    private String warehouseName;
    /** 退货单状态 */
    private Integer status;
    //收货人
    private String updateName;
    /** 操作时间 */
    private Date updateTime;
    //退货人
    private String createName;

    private Long warehouseId;
    /**品类数*/
    private Integer categoryNum;
}
package com.pinshang.qingyun.order.dto;

import com.pinshang.qingyun.base.page.Pagination;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Author: sk
 * @Date: 2020/9/29
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ShopReceiveSettingIDTO extends Pagination {

    @ApiModelProperty("门店类型")
    private Integer shopType;

    @ApiModelProperty("门店id")
    private Long shopId;

    @ApiModelProperty("收货方式 0 自动收货  1 手动收货")
    private Integer receiveType;
}

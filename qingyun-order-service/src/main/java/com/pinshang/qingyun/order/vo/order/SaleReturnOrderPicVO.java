package com.pinshang.qingyun.order.vo.order;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;


@Data
@NoArgsConstructor
public class SaleReturnOrderPicVO {
	@ApiModelProperty(position = 11, required = true, value = "图片URL")
	private String picUrl;

	@ApiModelProperty(position = 21, required = true, value = "访问图片URL")
	private String visitPicUrl;

	public SaleReturnOrderPicVO(String picUrl, String visitPicUrl) {
		this.picUrl = picUrl;
		this.visitPicUrl = visitPicUrl;
	}
}

package com.pinshang.qingyun.order.model.order;

import com.pinshang.qingyun.base.po.BaseIDPO;
import lombok.Data;

import javax.persistence.Entity;
import javax.persistence.Table;
import java.util.Date;

/**
 * 拆单记录表
 **/
@Entity
@Table(name = "t_split_order")
@Data
public class SplitOrder extends BaseIDPO {
	private static final long serialVersionUID = -1L;
    /**订单id **/
    private Long orderId;
    
    private Date createTime;// 插入时间
    private Date updateTime;// 插入时间
    //状态0-未拆单, 1-已拆单, 2-取消拆单
    private Integer status;
}
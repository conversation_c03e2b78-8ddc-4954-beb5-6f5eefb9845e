package com.pinshang.qingyun.order.dto.shopcart.v3;

import com.pinshang.qingyun.base.enums.YesOrNoEnums;
import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

@Data
@ApiModel
@NoArgsConstructor
public class ShoppingCartAddV3IDTO extends ShoppingCartV3IDTO{

    public static ShoppingCartAddV3IDTO builder(Long storeId, Date orderTime, YesOrNoEnums shopCartPage){
        ShoppingCartAddV3IDTO shoppingCartAddV3IDTO = new ShoppingCartAddV3IDTO();
        shoppingCartAddV3IDTO.setStoreId(storeId);
        shoppingCartAddV3IDTO.setOrderTime(orderTime);
        shoppingCartAddV3IDTO.setShopCartPage(shopCartPage);
        return shoppingCartAddV3IDTO;
    }
}

package com.pinshang.qingyun.order.model.auto;

import lombok.Data;

import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.math.BigDecimal;
import java.util.Date;

/**
 * @Author: liu<PERSON>hen
 * @DateTime: 2022/5/13 14:46
 */
@Data
@Entity
@Table(name = "t_md_auto_pre_order_item")
public class AutoPreOrderItem {
    @Id
    private Long id;
    private Long autoPreId;
    private Long commodityId;
    private BigDecimal stockQuantity;
    private Integer stockNumber;
    private Long createId;
    private Long updateId;
    private Date createTime;
    private Date updateTime;
    private BigDecimal price;
}

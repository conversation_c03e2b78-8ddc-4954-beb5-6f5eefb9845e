package com.pinshang.qingyun.order.vo.auto;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class AutoSettingPageVO {

    private Long shopId;

    @ApiModelProperty("门店编码")
    private String shopCode;

    @ApiModelProperty("门店名称")
    private String shopName;

    @ApiModelProperty("门店状态")
    private Integer shopStatus;

    @ApiModelProperty("设置状态")
    private Integer status;

    @ApiModelProperty("总部设置品项")
    private Integer headItems;

    @ApiModelProperty("已设置品项")
    private Integer setItems;

    @ApiModelProperty("上个月销售品项")
    private Integer lastMonthSale;

    @ApiModelProperty("上个月销售品项详情")
    private String lastMonthSaleInfo;

    @ApiModelProperty("要求单店品项")
    private Integer requireShopItems;

    @ApiModelProperty("完成品项")
    private Integer finishItems;

    @ApiModelProperty("未完成品项")
    private Integer notFinishItems;

    @ApiModelProperty("完成率")
    private Integer finishRatio;
}

package com.pinshang.qingyun.order.dto.shopcart.v4;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2024/3/4 17:04
 */
@Data
public class CommodityPromotionV4ODTO {
    public static final List<String> GIFT_REMARK_LIST = new ArrayList<>();
    public static final List<String> GRADIENT_DISCOUNT_REMARK_LIST = new ArrayList<>();

    @ApiModelProperty("促销类型")
    private Integer promotionType;

    private String promotionTypeName;

    @ApiModelProperty("促销标语")
    private String nextTips;

    @ApiModelProperty("促销标语")
    private String tips;

    @ApiModelProperty(value = "促销名称")
    private String promotionName;
    @ApiModelProperty(value = "促销力度规则")
    private List<String> ruleList;
    @ApiModelProperty(value = "规则说明")
    private List<String> remarkList;

    static {
        GIFT_REMARK_LIST.add("按照能满足的最高一个梯度执行促销计算，不累加。");
        GIFT_REMARK_LIST.add("注：特价商品、特惠商品、赠品、配送费不参与促销计算，赠品详见购物车，限量赠品赠完为止，如有疑问请联系客服。");

        GRADIENT_DISCOUNT_REMARK_LIST.add("按照能满足的最高一个梯度执行促销计算，不累加。");
        GRADIENT_DISCOUNT_REMARK_LIST.add("注：特价商品、特惠商品、赠品、配送费不参与促销计算。");
    }
}

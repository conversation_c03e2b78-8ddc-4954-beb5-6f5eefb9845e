package com.pinshang.qingyun.order.model.order;

import com.pinshang.qingyun.base.enums.SalesPromotionStatusEnums;
import com.pinshang.qingyun.base.enums.YesOrNoEnums;
import com.pinshang.qingyun.base.enums.xd.XdOrderReturnStatusEnum;
import com.pinshang.qingyun.base.po.BaseIDPO;
import lombok.Data;

import javax.persistence.Entity;
import javax.persistence.Table;
import java.math.BigDecimal;
import java.util.Date;

@Data
@Entity
@Table(name="t_xda_order_item")
public class XdaOrderItem extends BaseIDPO {

    /**订单编号*/
    private Long orderId;
    /**商品ID*/
    private Long commodityId;
    /**可退份数(新增＝number,补差或退货时不断减少)(申请退货或取消退货，成功退货或失败退货等用，可表示退货的中间过程)*/
    private Integer ableReturnNumber;
    /**
     * 可退金额(价值:可能含积分)
     */
    private BigDecimal ableReturnAmount;
    private BigDecimal quantity;
    /**件数(针对称重商品记录份数)*/
    private Integer number;
    /**实际商品数量*/
    private BigDecimal realQuantity;
    /**实际份数(配送份数,已补差后)(新增时＝number 补差或退款时不断改变)*/
    private Integer realNumber;
    /**新增时＝quanity(新增时＝quantity 只在补差时改变为补差后数量)*/
    private BigDecimal realDiffQuantity;
    /**新增时＝number(新增时＝number 只在补差时改变为补差后份数)*/
    private Integer realDiffNumber;
    /**商品单价(展示价格,不一定比促销价低,取促销价,会员价,原价三者最低的值)*/
    private BigDecimal price;
    /**特价*/
    private BigDecimal specialPrice;
    /**会员价*/
    private BigDecimal memberPrice;
    /**原价*/
    private BigDecimal originPrice;
    /**
     * 0普通商品 1满x元减y元 3第x件y折 4满x件y折 30任选x件组合价y元 33固定组合价  34享受赠品 35买满x元赠
     * 36每买满X元赠 37买满x件赠 38每买满x件赠
     */
    private SalesPromotionStatusEnums promotionStatus;
    /**
     * 促销id
     */
    private Long promotionId;
    /**商品优惠后总金额(优惠即促销和特价计算后保存的金额),原金额=quantity*价格*/
    private BigDecimal totalAmount;
    /**实发金额(补差后金额)*/
    private BigDecimal realAmount;
    /**实际补差后的金额(新增时＝total_amount,补差后＝去了掉少的金额后的实际金额)*/
    private BigDecimal realDiffAmount;
    /**1=称重，0=非称重*/
    private YesOrNoEnums isWeight;
    /**是否需要处理 0＝不处理，1＝处理(针对称重商品)*/
    private YesOrNoEnums isProcess;
    /**处理标签ID*/
    private Integer processId;
    /**处理名称*/
    private String processName;
    /**是否有退货: 0＝未退货 1=部分退货，2＝已退货(全退了)*/
    private XdOrderReturnStatusEnum returnStatus;
    /**补差或退货时不断变化*/
    private Integer realPoint;
    /**补差时，支付金额不足时，扣减的积时记录*/
    private Integer diffPoint;
    private Long updateId;
    private Date updateTime;
    /**拣货完成后的商品数量*/
    private BigDecimal pickQuantity;
    /**拣货完成后的商品份数*/
    private Integer pickNumber;
    /** 成本价*/
    private BigDecimal weightPrice;
}
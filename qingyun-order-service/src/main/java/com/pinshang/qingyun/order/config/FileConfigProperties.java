package com.pinshang.qingyun.order.config;

import lombok.Data;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

@Data
@Configuration
@ConfigurationProperties(prefix = "pinshang.file", ignoreUnknownFields = false)
public class FileConfigProperties {
	// PDF文件本地根目录	：/opt/pdf
	@Value("${pdfLocalRootDir:/opt/pdf/}")
	private String pdfLocalRootDir;
	
	// 文件服务器地址	：http://192.168.0.207:9078/restUpload
	@Value("${serverUrl:http://192.168.0.207:9078/restUpload}")
	private String serverUrl;
	
	// 文件访问地址		：http://192.168.0.207:8088
	@Value("${visitUrl:http://192.168.0.207:8088/}")
	private String visitUrl;
}

package com.pinshang.qingyun.order.enums;


/**
 * <AUTHOR>
 * 分配库存等级
 */
public enum StockLevelEnums {
    /**
     * 特价品中的组合品
     */
    SPECIAL_COMB(10),
    /**
     * 特价品中的非组合品
     */
    SPECIAL(11),
    /**
     * 正常品中的组合品
     */
    NORMAL_COMB(20),
    /**
     * 正常品中的非组合品
     */
    NORMAL(21),
    /**
     * 赠品券(云超)中的组合品
     */
    GIFT_COUPON_COMB(30),
    /**
     * 赠品券(云超)中的非组合品
     */
    GIFT_COUPON(31),
    /**
     * 特惠品(鲜达)中的组合品
     */
    TH_COMB(40),
    /**
     * 特惠品(鲜达)中的非组合品
     */
    TH(41),
    /**
     * 赠品中的非组合品
     */
    GIFT_COMB(50),
    /**
     * 赠品中的非组合品
     */
    GIFT(51),
    ;

    private final Integer code;

    public Integer getCode() {
        return code;
    }

    StockLevelEnums(Integer code) {
        this.code = code;
    }
}

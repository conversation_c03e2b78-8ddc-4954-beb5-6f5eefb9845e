package com.pinshang.qingyun.order.dto;

import com.pinshang.qingyun.base.page.Pagination;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Author: liuZhen
 * @DateTime: 2022/5/11 13:58
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class AutoCommodityIDTO extends Pagination {
    @ApiModelProperty("商品")
    private String commodityId;
    @ApiModelProperty("条形码")
    private String barCode;
    /**
     * 一级分类id
     */
    private Long cateId1;
    /**
     * 二级分类id
     */
    private Long cateId2;
    /**
     * 三级分类id
     */
    private Long cateId3;
    @ApiModelProperty("是否速冻食品")
    private Integer isFreeze;
}

package com.pinshang.qingyun.order.dto.tob;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * @Description：
 * @Author：ZhangHui
 * @Package：com.pinshang.qingyun.order.dto.tob
 * @Date: 2024/4/12
 */
@Data
@ApiModel(value = "ToBOrderStatisticsRepairIDTO", description = "订单修复入参")
public class ToBOrderStatisticsRepairIDTO {

    @ApiModelProperty("送货日期")
    private String orderTime;

    @ApiModelProperty("商品id")
    private Long commodityId;

    @ApiModelProperty("下单数量汇总（不包括取消订单）")
    private BigDecimal orderQuantity;

    @ApiModelProperty("下单份数汇总（不包括取消订单）")
    private Integer orderNumber;

    @ApiModelProperty("创建人id")
    private Long createId = -1L;

    @ApiModelProperty("创建时间")
    private Date createTime = new Date();

    @ApiModelProperty("创建人id")
    private Long updateId = -1L;

    @ApiModelProperty("更新时间")
    private Date updateTime = new Date();

}

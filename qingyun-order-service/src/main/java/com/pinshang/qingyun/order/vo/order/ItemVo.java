package com.pinshang.qingyun.order.vo.order;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class ItemVo {
    private String sourceId; // 资源ID

    private Long commodityId;// 商品ID

    private BigDecimal number;//数量

    private BigDecimal unitPrice;//单价

    private BigDecimal totalPrice;//总金额

    /** 订单明细ID 或 退货单ID */
    private Long itemId;
    /** 实发数量 或 退货数量 */
    private BigDecimal deliveryNumber;
    /** 实发单价 或 退货单价 */
    private BigDecimal deliveryUnitPrice;
    /** 实发总金额 或 退货总金额 */
    private BigDecimal deliveryTotalPrice;
}

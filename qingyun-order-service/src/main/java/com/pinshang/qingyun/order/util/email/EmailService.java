//package com.pinshang.qingyun.order.util.email;
//
//import org.apache.commons.mail.EmailException;
//import org.apache.commons.mail.SimpleEmail;
//import org.apache.log4j.Logger;
//import vo.Email;
//
//public class EmailService {
//	Logger logger = Logger.getLogger(EmailService.class);
//	private String hostName;
//	private String userName;
//	private String userPassword;
//
//	public String getHostName() {
//		return hostName;
//	}
//
//	public void setHostName(String hostName) {
//		this.hostName = hostName;
//	}
//
//	public String getUserName() {
//		return userName;
//	}
//
//	public void setUserName(String userName) {
//		this.userName = userName;
//	}
//
//	public String getUserPassword() {
//		return userPassword;
//	}
//
//	public void setUserPassword(String userPassword) {
//		this.userPassword = userPassword;
//	}
//
//
//	@SuppressWarnings("deprecation")
//	public boolean send(Email email) {
//		SimpleEmail simpleEmail = new SimpleEmail();
//		simpleEmail.setTLS(true);
//		simpleEmail.setDebug(true);
//		simpleEmail.setHostName(this.hostName);
//		simpleEmail.setAuthentication(this.userName,this.userPassword); // 用户名和密码
//
//		try {
//			simpleEmail.addTo(email.getMailTo()); // 接收方
//			simpleEmail.setFrom(this.userName); // 发送方
//			simpleEmail.setSubject(email.getSubject()); // 标题
//			simpleEmail.setCharset("GBK");
//			simpleEmail.setContent(email.getContent(), "text/html;charset=GBK");
//			simpleEmail.send();
//			logger.info("发送邮件（邮件标题：" + email.getSubject() + "  email:" + email.getMailTo() + "  ） 发送成功！");
//			return true;
//		} catch (EmailException e) {
//			logger.info("发送邮件（邮件标题：" + email.getSubject() + "  email:" + email.getMailTo() + "  ） 发送报错：" + e.getMessage());
//			e.printStackTrace();
//			return false;
//		}
//	}
//
//}

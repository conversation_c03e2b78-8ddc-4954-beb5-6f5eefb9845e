package com.pinshang.qingyun.order.util;

import com.pinshang.qingyun.box.utils.ConcurrentDateUtil;
import com.pinshang.qingyun.box.utils.DateTimeUtil;
import com.pinshang.qingyun.box.utils.DateUtil;
import com.pinshang.qingyun.box.utils.SpringUtil;
import org.apache.commons.lang3.StringUtils;

import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

/**
 * Created by zhaoxiuju<PERSON> on 2021/3/26 14:43
 */
public class OrderTimeUtil {

    //处理job执行参数，返回送货日期列表
    public static List<Date> processOrderTimeParams(String[] orderTimes){
        List<Date> orderTimeList = new ArrayList<>();
        try {
            if(SpringUtil.isEmpty(orderTimes)){
                //job没有传日期参数，默认设置： 昨天
                DateFormat df = ConcurrentDateUtil.SDF_FULL_DATE.get();
                String yesterday = df.format(   DateTimeUtil.buildNewDate(new Date(),-1) );
                orderTimeList.add(df.parse(yesterday));
            }else{
                //job传参，日期格式化
                Date startDate = DateUtil.parseDate(orderTimes[0],"yyyy-MM-dd");
                if (orderTimes.length > 1) {
                    Date endDate = DateUtil.parseDate(orderTimes[1],"yyyy-MM-dd");
                    orderTimeList.addAll(DateUtil.getBetweenDates(startDate,endDate));
                }else{
                    orderTimeList.add(startDate);
                }
            }
        } catch (Exception e) {
        }
        return orderTimeList;
    }

    /*public static void main(String[] args) throws ParseException {
        System.out.println( DateTimeUtil.buildNewDate(new Date(),-1));
        DateFormat df = ConcurrentDateUtil.SDF_FULL_DATE.get();
        String yesterday = df.format(   DateTimeUtil.buildNewDate(new Date(),-1) );
        System.out.println( yesterday);
        System.out.println(   df.parse(yesterday) );
    }*/

    public static boolean isValidDate(String strValue ) {//2009-10-01字符串 
        if(StringUtils.isBlank(strValue)){
            return false;
        }
        strValue = strValue.replaceAll("-","");
        int d = Integer.parseInt(strValue.substring(6, 8));
        int m = Integer.parseInt(strValue.substring(4, 6));
        int y = Integer.parseInt(strValue.substring(0, 4));
        if (d < 1 || m < 1 || m > 12) {return false;}
        if (m == 2) {
            if (isLeapYear(y)) return d <= 29;
            else return d <= 28;
        }else if (m == 4 || m == 6 || m == 9 || m == 11)
            return d <= 30;
        else
            return d <= 31;
    }

    public static boolean isLeapYear(int y) {//判断是否为闰年
        return y % 4 == 0 && (y % 400 == 0 || y % 100 != 0);
    }

    /**
     * 传入时分，返回时分（18:30）
     * @param time
     * @param amount
     * @return
     */
    public static String addMinute(String time, int amount){
        String currentDate = DateUtil.getDateFormate(new Date(),"yyyy-MM-dd");
        Date date = DateUtil.parseDate(currentDate + " " + time + ":00","yyyy-MM-dd HH:mm:ss");
        Calendar c = Calendar.getInstance();
        c.setTime(date);
        c.add(Calendar.MINUTE, amount);
        return DateUtil.getDateFormate(c.getTime(), "HH:mm");
    }

    public static String getBeginDeliveryTimeRange(String deliveryTimeRange, int addDay){
        String timeRange  = deliveryTimeRange.split("-")[0];
        Calendar calendar = Calendar.getInstance();
        if(Integer.valueOf(timeRange) == 0){
            calendar.add(Calendar.DATE,1);
        }else {
            calendar.add(Calendar.DATE,Integer.valueOf(timeRange) + addDay);
        }
        return DateUtil.getDateFormate(calendar.getTime(),"yyyy-MM-dd");
    }

    /**
     * //获取上个月的第一天
     * @return
     */
    public static String getLastMonthFirstDay(){
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd"); //格式化时间

        Calendar cal_1=Calendar.getInstance();//获取当前日期
        cal_1.add(Calendar.MONTH, -1);
        cal_1.set(Calendar.DAY_OF_MONTH,1);//设置为1号
        cal_1.set(Calendar.HOUR_OF_DAY,0);
        cal_1.set(Calendar.MINUTE,0);
        cal_1.set(Calendar.SECOND,0);
        String firstDay = format.format(cal_1.getTime());
        return firstDay;
    }

    /**
     * 获取上月最后一天
     * @return
     */
    public static String getLastMonthLastDay(){
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd"); //格式化时间
        Calendar cal_2 = Calendar.getInstance();
        cal_2.set(Calendar.DAY_OF_MONTH,0);//设置为1号,当前日期既为本月第一天
        cal_2.set(Calendar.HOUR_OF_DAY,23);
        cal_2.set(Calendar.MINUTE,59);
        cal_2.set(Calendar.SECOND,59);
        String lastDay = format.format(cal_2.getTime());
        return lastDay;
    }
    /**
     * 判断是否同一天
     * @param date
     * @param minBeginDeliveryTime
     * @return
     */
    public static Boolean isTheSameDay(Date date,Date minBeginDeliveryTime){
        LocalDate localDate = minBeginDeliveryTime.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
        LocalDate currentDate = date.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
        if(localDate.getDayOfMonth() == currentDate.getDayOfMonth() &&
                localDate.getYear() == currentDate.getYear()&&
                localDate.getMonthValue() == currentDate.getMonthValue()){
            return Boolean.TRUE;
        }
        return Boolean.FALSE;
    }
    /*public static void main(String[] args) {
        System.out.println(getLastMonthFirstDay());
        System.out.println(getLastMonthLastDay());
    }*/

    /**
     * 获取最近31天（不包含今天），只返回首位两天
     * @return
     */
    public static String[] last31DaysRange(){
        LocalDate today = LocalDate.now();
        LocalDate startDate = today.minusDays(31);
        LocalDate endDate = today.minusDays(1);
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern(DateTimeUtil.YYYY_MM_DD);
        return new String[]{startDate.format(formatter),endDate.format(formatter)};
    }
}

package com.pinshang.qingyun.order.vo.order;


/**
 * 订单日志-操作实体类型
 *
 * <AUTHOR>
 *
 * @date 2017年4月6日
 */

public enum OrderHistoryEntityTypeEnums {
	ORDER(1, "订单"),
	COMMODITY(2, "商品")
	;
	private Integer code;
	private String name;

	public Integer getCode() {
		return code;
	}

	OrderHistoryEntityTypeEnums(Integer code, String name) {
		this.code = code;
		this.name=name;
	}
	
	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public void setCode(Integer code) {
		this.code = code;
	}
	
}

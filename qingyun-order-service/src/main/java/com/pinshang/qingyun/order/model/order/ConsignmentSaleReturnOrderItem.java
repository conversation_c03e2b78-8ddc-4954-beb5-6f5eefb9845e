package com.pinshang.qingyun.order.model.order;

import lombok.Data;

import javax.persistence.Id;
import javax.persistence.Table;
import java.math.BigDecimal;

/**
 * @description t_consignment_sale_return_order_item
 * <AUTHOR>
 * @date 2024-06-04
 */
@Data
@Table(name="t_consignment_sale_return_order_item")
public class ConsignmentSaleReturnOrderItem  {

    /**
     * id
     */
    @Id
    private Long id;

    /**
     * 代销退货单表主键ID
     */
    private Long consignmentSaleReturnOrderId;

    /**
     * 商品ID
     */
    private Long commodityId;

    /**
     * 退货数量
     */
    private BigDecimal returnQuantity;

    /**
     * 退货数量
     */
    private Double confirmQuantity;

    /**
     * 审核数量
     */
    private Double checkQuantity;

    /**
     * 备注
     */
    private String remark;

    /**
     * 退货价格
     */
    private BigDecimal price;



}
package com.pinshang.qingyun.order.dto.report;

import com.pinshang.qingyun.base.page.Pagination;
import com.pinshang.qingyun.infrastructure.data.query.annotate.Change;
import com.pinshang.qingyun.infrastructure.data.query.constant.DataQueryConstant;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.Date;
import java.util.List;

@Data
@EqualsAndHashCode(callSuper=false)
@NoArgsConstructor
@AllArgsConstructor
public class ActualReceiptAnalysisIDto extends Pagination{
	private static final long serialVersionUID = 5167881220777885090L;
	private Long shopId;
	private Long cateId3;
	private Long cateId1;
	private Long cateId2;
	private String searchWord;
	private String barCode;
	@ApiModelProperty("开始时间 yyyy-MM-dd")
	private String beginDate;
	@ApiModelProperty("结束时间 yyyy-MM-dd")
	@Change(value = DataQueryConstant.NOW)
	private String endDate;

	private Date orderBeginDate;
	private Date orderEndDate;
	private Long commodityId;

	private String monthBegin;

	private String monthEnd;

	private List<Long> commodityIdList;
}

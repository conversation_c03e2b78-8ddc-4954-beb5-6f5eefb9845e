package com.pinshang.qingyun.order.model.auto;

import lombok.Data;

import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.math.BigDecimal;
import java.util.Date;

/**
 * @Author: liu<PERSON>hen
 * @DateTime: 2022/5/13 15:02
 */
@Data
@Entity
@Table(name = "t_md_auto_pre_order")
public class AutoPreOrder {
    @Id
    private Long id;
    private Long shopId;
    private String orderCode;
    private BigDecimal orderAmount;
    private Integer logisticsModel;
    private Integer status;
    private Long auditId;
    private Date auditTime;
    private Long orderId;
    private Date deliveryTime;
    private Long consignmentId; // 代销商户id
    private Long createId;
    private Long updateId;
    private Date createTime;
    private Date updateTime;
}

package com.pinshang.qingyun.order.controller;

import com.github.pagehelper.PageInfo;
import com.pinshang.qingyun.base.api.TokenInfo;
import com.pinshang.qingyun.base.configure.expand.QYAssert;
import com.pinshang.qingyun.base.enums.DeliveryOrderTypeEnums;
import com.pinshang.qingyun.base.util.FastThreadLocalUtil;
import com.pinshang.qingyun.box.utils.DateUtil;
import com.pinshang.qingyun.box.utils.SpringUtil;
import com.pinshang.qingyun.order.constant.LockKeyConstant;
import com.pinshang.qingyun.order.dto.OrderCancelMockDTO;
import com.pinshang.qingyun.order.dto.OrderMockDTO;
import com.pinshang.qingyun.order.mapper.entry.order.SubOrderItemListEntry;
import com.pinshang.qingyun.order.mapper.entry.order.SubOrderListEntry;
import com.pinshang.qingyun.order.model.order.SubOrder;
import com.pinshang.qingyun.order.service.SubOrderService;
import com.pinshang.qingyun.order.vo.OrderMockVO;
import com.pinshang.qingyun.order.vo.order.PickSubOrderItemRespVo;
import com.pinshang.qingyun.order.vo.order.PickSubOrderVo;
import com.pinshang.qingyun.order.vo.order.SubOrderSearchVo;
import com.pinshang.qingyun.order.vo.order.SubOrderVo;
import com.pinshang.qingyun.order.vo.subOrder.UnDeliverySubOrderJobReqVo;
import com.pinshang.qingyun.renderer.annotation.MethodRender;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;

@RestController
@RequestMapping("/subOrder")
@Slf4j
public class SubOrderController {

	@Autowired
	private SubOrderService subOrderService;
	@Resource
	private RedissonClient redissonClient;

	//查询未生成发货单的子订单列表
	@PostMapping("/queryUnDeliverySubOrderList")
	@MethodRender
	public PageInfo<SubOrderListEntry> queryUnDeliverySubOrderList(@RequestBody SubOrderVo dto){
		QYAssert.isTrue(dto!=null && dto.getOrderTime()!=null,"送货日期不能为空");
		TokenInfo ti = FastThreadLocalUtil.getQY();
		dto.setEnterpriseId(ti.getEnterpriseId());
		return subOrderService.queryUnDeliverySubOrderListV2(dto);
	}

	/*
	 * 查询未生成发货单的子订单明细
	 */
	@PostMapping("/querySubOrderItemList")
	@MethodRender
	public List<SubOrderItemListEntry> querySubOrderItemList(@RequestBody SubOrderSearchVo vo){
		return subOrderService.querySubOrderItemList(vo);
	}

	/*
	 * so单转do单
	 */
	@PostMapping("/createDeliveryOrder")
	public boolean creatDeliveryOrder(@RequestBody SubOrderSearchVo vo)  {
		QYAssert.notNull(vo!=null && SpringUtil.isNotEmpty(vo.getOrderIds()),"请至少选择一个订单");
		QYAssert.isTrue(vo.getWarehouseId()!=null,"请选择仓库");
		RLock lock = redissonClient.getLock(LockKeyConstant.createDeliveryOrderLock(vo.getWarehouseId(), DateUtil.getDateFormate(vo.getOrderTime(),"yyyy-MM-dd")));
		if (lock.tryLock()) {
			try {
				subOrderService.creatDeliveryOrder(vo);
			} finally {
				lock.unlock();
			}
		}
		return true;
	}

	//修改子单状态
	@PostMapping("/updateSubOrderStatus")
	public Integer updateSubOrderStatus(@RequestBody List<Long> subOrderIdList,@RequestParam(value = "subOrderStatus",required = false) Integer subOrderStatus){
		return subOrderService.updateSubOrderStatus(subOrderIdList,subOrderStatus);
	}


	@RequestMapping(value = "/findItemsBySubOrderId/{subOrderId}", method = RequestMethod.GET)
	List<PickSubOrderItemRespVo> findItemsBySubOrderId(@PathVariable("subOrderId") Long subOrderId){
        if (subOrderId == null) {
            return null;
        }
        return subOrderService.findItemsBySubOrderId(subOrderId);
	}


    /**
     * 批量更新subOrder的实发数量
     * @param deliveryItemList
     * @return
     */
    @RequestMapping(value = "/batchUpdateDeliveryQuantity", method = RequestMethod.POST)
    @Deprecated
    int batchUpdateDeliveryQuantity(@RequestBody List<PickSubOrderItemRespVo> deliveryItemList) {
        if (SpringUtil.isEmpty(deliveryItemList)) {
            return 0;
        }
        return subOrderService.batchUpdateDeliveryQuantity(deliveryItemList);
    }

	@RequestMapping(value = "/batchUpdateSubOrderDeliveryQuantity", method = RequestMethod.POST)
	boolean batchUpdateDeliveryQuantityV2(@RequestBody List<PickSubOrderVo> subOrderList) {
		if (SpringUtil.isEmpty(subOrderList)) {
			return true;
		}
		for (PickSubOrderVo pickSubOrderVo : subOrderList) {
			SubOrder subOrder = subOrderService.querySubOrderBySubOrderId(pickSubOrderVo.getSubOrderId());
			if (subOrder == null) {
				continue;
			}

			// 如果已经回写过实发数量和实收数量，则跳过
			if(subOrder.getWritebackRealQtyFlag() != null && subOrder.getWritebackRealQtyFlag()){
				continue;
			}

			return subOrderService.batchUpdateDeliveryQuantityV2(pickSubOrderVo, subOrder.getOrderId());
		}

		return true;
    }

	@PostMapping("/unDeliverySubOrderJob")
	public void unDeliverySubOrderJob(@RequestBody UnDeliverySubOrderJobReqVo param) {
		List<Integer> types= Arrays.asList(DeliveryOrderTypeEnums.SALE.getCode(),DeliveryOrderTypeEnums.TD_SALE.getCode(),DeliveryOrderTypeEnums.BIGSHOP_SALE.getCode(),
				DeliveryOrderTypeEnums.PLAN_SALE.getCode(),DeliveryOrderTypeEnums.B_COUNTRY.getCode());
		for (Integer type : types){
			param.setBusinessType(type);
			subOrderService.unDeliverySubOrderJob(param);
		}
	}

	@PostMapping("/createMockOrder")
	public OrderMockVO createMockOrder(@RequestBody @Validated OrderMockDTO dto) {
		//冻结/生成订单/同步到dc
		return  subOrderService.createMockOrder(dto);
	}

	@PostMapping("/cancelMockOrder")
	public Boolean cancelMockOrder(@RequestBody @Validated OrderCancelMockDTO dto) {
		//冻结/生成订单/同步到dc
		return  subOrderService.cancelMockOrder(dto.getOrderId());
	}
}

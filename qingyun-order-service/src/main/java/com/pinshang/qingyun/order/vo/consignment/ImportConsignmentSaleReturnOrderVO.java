package com.pinshang.qingyun.order.vo.consignment;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ImportConsignmentSaleReturnOrderVO {

    /**
     * 代销供应商id
     */
    @ApiModelProperty(value = "代销供应商id")
    private Long supplierId;

    /**
     * 客户id
     */
    @ApiModelProperty(value = "客户id")
    private String storeId;

    /**
     * 客户编码
     */
    @ApiModelProperty(value = "客户编码")
    private String storeCode;

    /**
     * 门店id
     */
    @ApiModelProperty(value = "门店id")
    private Long shopId;

    /**
     *门店编码
     */
    @ApiModelProperty(value = "门店编码")
    private String shopCode;

    /**
     * 门店名称
     */
    @ApiModelProperty(value = "门店名称")
    private String shopName;

    /**
     * 商品id
     */
    @ApiModelProperty(value = "商品id")
    private String commodityId;

    /**
     * 商品编码
     */
    @ApiModelProperty(value = "商品编码")
    private String commodityCode;

    /**
     * 商品名称
     */
    @ApiModelProperty(value = "商品名称")
    private String commodityName;

    /** 型号规格 */
    @ApiModelProperty(value = "型号规格")
    private String commoditySpec;

    /** 计量单位 */
    @ApiModelProperty(value = "计量单位")
    private String commodityUnit;

    /** 退货数量*/
    @ApiModelProperty(value = "退货数量")
    private BigDecimal totalQuantity;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;

    /**
     * 退货价格
     */
    @ApiModelProperty(value = "退货价格")
    private BigDecimal price;
}

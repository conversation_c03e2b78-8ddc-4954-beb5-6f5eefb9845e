package com.pinshang.qingyun.order.model.order;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Id;
import javax.persistence.Table;
import java.math.BigDecimal;

@Data
@Table(name="t_sale_return_order_item")
public class SaleReturnOrderItem {
    /**主键ID*/
    @Id
    private Long id;

    /**退货单表主键ID*/
    private String saleReturnOrderId;

    /**商品ID*/
    private Long commodityId;

    /**退货原因：1=质量问题、2=已过保质期、3=包装破损、4=条码不符、5=其他*/
    private Integer returnReason;

    /**退货价格*/
    private BigDecimal price;

    /**退货数量*/
    private BigDecimal returnQuantity;

    /**实退数量（合格数量+报损数量）*/
    private BigDecimal realReturnQuantity;

    /**报损数量*/
    private BigDecimal breakageQuantity;

    /**责任方类型(Responsible party) 1=配送，2=大仓，3=门店*/
    private Integer rpType;

    /**
     * 审核备注
     */
    private String auditRemark;

    /**
     * 索赔金额
     */
    private BigDecimal compensatePrice;
}

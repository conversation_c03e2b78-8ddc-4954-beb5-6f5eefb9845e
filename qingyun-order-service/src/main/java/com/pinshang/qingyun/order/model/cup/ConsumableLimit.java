package com.pinshang.qingyun.order.model.cup;

import com.pinshang.qingyun.base.po.BasePO;

import javax.persistence.Entity;
import javax.persistence.Table;

/**
 * @Author: sk
 * @Date: 2024/7/18
 *
 * 客户月度限量表
 */
@Entity
@Table(name = "t_consumable_limit")
public class ConsumableLimit extends BasePO {

    /**
     * 客户id
     */
    private Long storeId;

    /**
     * 商品Id
     */
    private Long commodityId;

    /**
     * 月度限量数量
     */
    private Integer monthLimitNum;

    /**
     * 已使用数量
     */
    private Integer monthUsedNum;

    public Long getStoreId() {
        return storeId;
    }

    public void setStoreId(Long storeId) {
        this.storeId = storeId;
    }

    public Long getCommodityId() {
        return commodityId;
    }

    public void setCommodityId(Long commodityId) {
        this.commodityId = commodityId;
    }

    public Integer getMonthLimitNum() {
        return monthLimitNum;
    }

    public void setMonthLimitNum(Integer monthLimitNum) {
        this.monthLimitNum = monthLimitNum;
    }

    public Integer getMonthUsedNum() {
        return monthUsedNum;
    }

    public void setMonthUsedNum(Integer monthUsedNum) {
        this.monthUsedNum = monthUsedNum;
    }
}

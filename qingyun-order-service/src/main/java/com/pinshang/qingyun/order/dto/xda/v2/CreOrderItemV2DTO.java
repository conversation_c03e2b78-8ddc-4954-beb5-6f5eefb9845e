package com.pinshang.qingyun.order.dto.xda.v2;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.pinshang.qingyun.base.configure.codec.DecimalSerializerKeep2;
import com.pinshang.qingyun.order.enums.ProductTypeEnums;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 创建订单商品明细
 * <AUTHOR>
 * @date 23/6/21/021 16:23
 */
@Data
@ApiModel
public class CreOrderItemV2DTO {
    @ApiModelProperty("商品id")
    private Long commodityId;
    @ApiModelProperty("商品编码")
    private String commodityCode;
    @ApiModelProperty("原价")
    @JsonSerialize(using = DecimalSerializerKeep2.class)
    private BigDecimal commodityPrice;
    @ApiModelProperty(value ="是否有特价：0=无特价，1=普通特价",position = 14)
    private Integer isSpecialPrice;
    @ApiModelProperty("特价")
    @JsonSerialize(using = DecimalSerializerKeep2.class)
    private BigDecimal specialPrice;

    @ApiModelProperty("是否满足特惠")
    private Boolean isThInvalidate = Boolean.FALSE;
    @ApiModelProperty(value = "特惠价格")
    @JsonSerialize(using = DecimalSerializerKeep2.class)
    private BigDecimal thPrice;

    @ApiModelProperty("商品数量")
    private BigDecimal quantity;
    @ApiModelProperty("配送模式")
    private Integer logisticsModel;
    @ApiModelProperty("默认供应商id")
    private Long supplierId;
    @ApiModelProperty("默认仓库id")
    private Long warehouseId;
    @ApiModelProperty("商品类型")
    private ProductTypeEnums type;
    @ApiModelProperty("赠送方案id")
    private Long conditionId;
    @ApiModelProperty(value ="最早可订货时间,订单使用",hidden = true)
    private Date beginDeliveryTime;
    @ApiModelProperty(value ="最晚可订货时间,订单使用",hidden = true)
    private Date endDeliveryTime;

    public String getClassifyKey(){
        boolean flag = this.logisticsModel == null ||  (this.logisticsModel != 0 && this.warehouseId == null)
                || (this.logisticsModel == 0 && this.supplierId == null);
        if(flag){
            return "classifyKey";
        }else {
            return this.logisticsModel+"_"+this.warehouseId+"_"+this.supplierId;
        }
    }

    public BigDecimal salePrice(){
        if(this.isThInvalidate){
            return thPrice;
        }
        return this.isSpecialPrice == 1 ? this.specialPrice :  this.commodityPrice;
    }

    public BigDecimal saleAmount(){
        return salePrice().multiply(quantity).setScale(2,BigDecimal.ROUND_HALF_UP);
    }


    public BigDecimal saleQuantity(){
        return quantity;
    }
}

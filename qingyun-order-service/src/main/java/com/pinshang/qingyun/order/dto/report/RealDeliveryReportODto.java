package com.pinshang.qingyun.order.dto.report;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.pinshang.qingyun.renderer.annotation.FieldRender;
import com.pinshang.qingyun.renderer.constant.RenderFieldHelper;
import com.pinshang.qingyun.renderer.enums.FieldTypeEnum;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class RealDeliveryReportODto {
	@ExcelIgnore
	private Long storeTypeId;
	@ExcelIgnore
	private Long commodityId;
	@ExcelProperty("商品编码")
	private String commodityCode;
	@ExcelProperty("商品名称")
	private String commodityName;
	@ExcelProperty("规格")
	private String commoditySpec;
	@ExcelProperty("条码")
	private String barCode;
	@ExcelIgnore
	private String barCodes;	// 子码列表
	@ExcelProperty("一级品类")
	@FieldRender(fieldType = FieldTypeEnum.COMMODITY_KIND,fieldName = RenderFieldHelper.CommodityKind.commodityFirstKindName,keyName = "commodityId")
	private String commodityFirstName;
	@ExcelProperty("计量单位")
	@FieldRender(fieldType = FieldTypeEnum.DICTIONARY,fieldName = RenderFieldHelper.Dictionary.optionName,keyName = "commodityUnitId")
	private String commodityUnit;
	@ExcelIgnore
	private Long commodityUnitId;
	@ExcelIgnore
	private BigDecimal price;
	@ExcelProperty("订货数量")
	private BigDecimal orderNum;
	@ExcelProperty("实发数量")
	private BigDecimal deliveryNum;
	@ExcelProperty("实发金额")
	private BigDecimal realDeliveryAmount;
	@ExcelProperty("差异数量")
	private BigDecimal differNum;
	@ExcelProperty("工厂")
	private String factoryName;
	@ExcelProperty("生产组")
	private String workshopName;
	@ExcelProperty("车间")
	private String flowshopName;
	@ExcelIgnore
	private String warehouseName;
	@FieldRender(fieldType = FieldTypeEnum.DICTIONARY,fieldName = RenderFieldHelper.Dictionary.optionName,keyName = "storeTypeId")
	@ExcelIgnore
	private String storeTypeName;
	// 税率
	@ExcelProperty("税率")
	@JsonFormat
	private BigDecimal taxRate;
}

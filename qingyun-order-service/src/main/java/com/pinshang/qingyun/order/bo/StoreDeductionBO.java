/*
 * tramy.cn Inc.
 * Copyright (c) 2021-2024 All Rights Reserved.
 */
package com.pinshang.qingyun.order.bo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>
 * 扣款实体类
 * </p>
 *
 * <AUTHOR> shenyang
 * @version : 1.0.0
 * @history : modify history
 * <author>              <time>              <version>              <desc>
 * @since : 2024/07/18 14:04
 */
@Builder
@Data
public class StoreDeductionBO implements Serializable {
    private static final long serialVersionUID = -1L;
    private Long orderId;
    private Long storeId;
    private BigDecimal orderAmount;
    private String remark;
    private Long userId;
    private Date orderTime;
    @ApiModelProperty("系统内部交易code")
    private String tradeCode;
    @ApiModelProperty("第三方交易code")
    private String thirdPartyTradeCode;
    @ApiModelProperty("交易时间")
    private Date tradeTime;
    @ApiModelProperty("交易类型：com.pinshang.qingyun.base.enums.StoreBillTypeEnums")
    private Integer billType;
    @ApiModelProperty("订单编码")
    private String orderCode;
    @ApiModelProperty("销售主体公司Id=t_company.id")
    private Long salesCompanyId;
    @ApiModelProperty("销售主体公司简称（备注不一定是公司全称）")
    private String salesCompanyName;
    @ApiModelProperty("校验账户orderAmount金额；空：不校验，1：校验")
    private Integer validateAccountAmount;
}
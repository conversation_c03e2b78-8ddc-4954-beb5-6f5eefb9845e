package com.pinshang.qingyun.order.dto;

import com.pinshang.qingyun.base.enums.ShopShopStatusEnums;
import com.pinshang.qingyun.base.enums.shop.ShopTypeEnums;
import com.pinshang.qingyun.order.enums.ShopReceiveTypeEnums;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * @Author: sk
 * @Date: 2020/9/29
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ShopReceiveSettingODTO {

    private Long settingId;
    private Long shopId;

    @ApiModelProperty("门店编码")
    private String shopCode;

    @ApiModelProperty("门店名称")
    private String shopName;

    private Integer shopType;
    private Integer shopStatus;

    @ApiModelProperty("收货方式 0 自动收货  1 手动收货")
    private Integer receiveType;

    @ApiModelProperty("操作人")
    private String createName;

    @ApiModelProperty("操作时间")
    private Date createTime;

    public String getShopStatusName() {
        return ShopShopStatusEnums.getName(this.shopStatus);
    }

    public String getShopTypeName() {
        return ShopTypeEnums.getName(this.shopType);
    }

    public String getReceiveTypeName(){
        if(null != receiveType){
            return ShopReceiveTypeEnums.getName(this.receiveType);
        }else {
            return "自动收货";
        }
    }
}

package com.pinshang.qingyun.order.dto.xforder;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import com.fasterxml.jackson.annotation.JsonIgnore;

/**
 * 查询 现付单状态
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class SelectXfOrderStatusIDTO {
	@JsonIgnore
	@ApiModelProperty(position = 0, required = true, value = "角色ID")
	private Long storeId;
	@JsonIgnore
	@ApiModelProperty(position = 0, required = true, value = "角色ID")
	private Long userId;

	@ApiModelProperty(position = 11, required = true, value = "付款流水编码(充值单号），XDAPayBill.billCode")
	private String billCode;
}

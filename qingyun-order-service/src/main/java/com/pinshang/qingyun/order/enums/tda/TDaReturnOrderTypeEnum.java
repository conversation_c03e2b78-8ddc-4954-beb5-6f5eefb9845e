package com.pinshang.qingyun.order.enums.tda;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import com.pinshang.qingyun.order.enums.BaseEnum;

/**
 * <AUTHOR>
 * @Version 0.1.0
 * @Date 2020/12/30 16:07
 * @Copyright © 2019-2020 qm
 * @Description -- 类说明 通达退货订单类型 1-退货，2-少货，3-多货
 **/
public enum TDaReturnOrderTypeEnum implements BaseEnum {

    RETURN(1, "退货", 1, "退货投诉"),
    SHORTAGE(2, "少货", 2, "普通投诉-差异投诉"),
    OVERAGE(3, "多货", 2, "普通投诉-差异投诉"),

    ;

    private int code;
    private String name;
    private int categoryCode;
    private String categoryDesc;


    private TDaReturnOrderTypeEnum(int code, String name, int categoryCode, String categoryDesc) {
        this.code = code;
        this.name = name;
        this.categoryCode = categoryCode;
        this.categoryDesc = categoryDesc;
    }

    @JsonValue
    public int getCode() {
        return this.code;
    }

    public String getName() {
        return this.name;
    }

    @JsonValue
    public int getCategoryCode() {
        return categoryCode;
    }

    public String getCategoryDesc() {
        return categoryDesc;
    }

    @JsonCreator
    public static TDaReturnOrderTypeEnum getByCode(Integer code) {
        if (code == null) {
            return null;
        } else {
            TDaReturnOrderTypeEnum[] var1 = values();
            int var2 = var1.length;
            for (int var3 = 0; var3 < var2; ++var3) {
                TDaReturnOrderTypeEnum value = var1[var3];
                if (value.getCode() == code) {
                    return value;
                }
            }
            return null;
        }
    }

    @Override
    public Integer convert() {
        return this.code;
    }
}

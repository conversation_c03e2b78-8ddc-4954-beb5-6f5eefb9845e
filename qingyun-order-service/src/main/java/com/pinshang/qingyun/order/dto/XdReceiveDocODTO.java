package com.pinshang.qingyun.order.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * @Author: sk
 * @Date: 2020/3/17
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class XdReceiveDocODTO {

    @ApiModelProperty("单据id")
    private String docId;

    @ApiModelProperty("单据号")
    private String docCode;


    @ApiModelProperty("前置仓id")
    private Long shopId;

    @ApiModelProperty("前置仓名称")
    private String shopName;

    @ApiModelProperty("送货日期")
    private Date deliveryTime;

    @ApiModelProperty("单据状态：0 待收货 1 已关闭")
    private Integer docStatus;


}

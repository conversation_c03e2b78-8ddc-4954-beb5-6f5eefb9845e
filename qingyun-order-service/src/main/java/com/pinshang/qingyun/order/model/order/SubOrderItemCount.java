package com.pinshang.qingyun.order.model.order;

import com.pinshang.qingyun.base.po.BaseSimplePO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Table;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Table(name="t_sub_order_item_count")
public class SubOrderItemCount extends BaseSimplePO {

    private Long shopId;

    private Date sendDate;

    private Long commodityId;

    private BigDecimal quantity;

    private BigDecimal countQuantity;

    private Long updateId;

    private Date updateTime;

}

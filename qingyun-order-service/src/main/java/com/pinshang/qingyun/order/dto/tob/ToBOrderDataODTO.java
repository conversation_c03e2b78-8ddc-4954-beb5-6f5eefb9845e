package com.pinshang.qingyun.order.dto.tob;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;


@Data
@ApiModel(value = "ToBOrderDataODTO", description = "B端订单数量和份数数据返回参数")
public class ToBOrderDataODTO {

    @ApiModelProperty("商品ID")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long commodityId;

    @ApiModelProperty("送货日期")
    private String orderTime;

    @ApiModelProperty("实时B端下单数量")
    private BigDecimal realBTotalQuantity;

    @ApiModelProperty("实时B端下单份数")
    private Integer realBTotalNumber;

    @ApiModelProperty("实时B端取消数量")
    private BigDecimal realBCancelQuantity;

    @ApiModelProperty("实时B端取消份数")
    private Integer realBCancelNumber;


}

package com.pinshang.qingyun.order.vo.pda;

import com.pinshang.qingyun.renderer.annotation.FieldRender;
import com.pinshang.qingyun.renderer.constant.RenderFieldHelper;
import com.pinshang.qingyun.renderer.enums.FieldTypeEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

@Data
@ApiModel("门店订货查询结果")
public class PdaCommodityOrderQueryResponseVo {

    @ApiModelProperty("门店订货查询详情数据")
    PdaCommodityOrderQueryData pdaCommodityOrderQueryData;
}

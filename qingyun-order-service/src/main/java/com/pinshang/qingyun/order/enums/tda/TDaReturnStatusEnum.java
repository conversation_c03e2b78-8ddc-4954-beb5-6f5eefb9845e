package com.pinshang.qingyun.order.enums.tda;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import com.pinshang.qingyun.order.enums.BaseEnum;

/**
 * <AUTHOR>
 * @Version 0.1.0
 * @Date 2020/12/30 16:07
 * @Copyright © 2019-2020 qm
 * @Description -- 类说明 通达退货单状态：1-待审核、2-待取货、3-取货失败、4-待大仓确认、5-已取消、6-已完成、7-审核不通过
 **/
public enum TDaReturnStatusEnum implements BaseEnum {

    PENDING_AUDIT(1, "待审核"),
    PENDING_PICKUP(2, "待取货"),
    PICKUP_FAILED(3, "取货失败"),
    PENDING_WAREHOUSE_CONFIRMATION(4, "待大仓确认"),
    CANCELLED(5, "已取消"),
    COMPLETED(6, "已完成"),
    AUDIT_FAILED(7, "审核不通过"),

    ;

    private int code;
    private String name;


    private TDaReturnStatusEnum(int code, String name) {
        this.code = code;
        this.name = name;
    }

    @JsonValue
    public int getCode() {
        return this.code;
    }

    public String getName() {
        return this.name;
    }


    @JsonCreator
    public static TDaReturnStatusEnum getByCode(Integer code) {
        if (code == null) {
            return null;
        } else {
            TDaReturnStatusEnum[] var1 = values();
            int var2 = var1.length;
            for (int var3 = 0; var3 < var2; ++var3) {
                TDaReturnStatusEnum value = var1[var3];
                if (value.getCode() == code) {
                    return value;
                }
            }
            return null;
        }
    }

    @Override
    public Integer convert() {
        return this.code;
    }
}

package com.pinshang.qingyun.order.vo.mock;

import com.pinshang.qingyun.box.utils.SpringUtil;
import com.pinshang.qingyun.order.model.order.OrderList;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.swing.*;
import java.math.BigDecimal;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class MockOrderItemVo {

    private Long commodityId;

    /**
     * 单价
     */
    @ApiModelProperty(value = "单价，自动生成两位小数，无须传参",hidden = true)
    private BigDecimal price;

    /**
     * 数量
     */
    private BigDecimal quantity;

    public static OrderList convertOrderList(MockOrderItemVo vo){
        OrderList orderList = new OrderList();
        orderList.setCommodityId(vo.getCommodityId());
        orderList.setCommodityPrice(vo.getPrice());
        orderList.setCommodityNum(vo.getQuantity());
        orderList.setTotalPrice(vo.getPrice().multiply(vo.getQuantity()));
        orderList.setType(1);
        return orderList;
    }

}

package com.pinshang.qingyun.order.dto.shopcart.v4;

import com.pinshang.qingyun.base.enums.YesOrNoEnums;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @Date 2024/3/4 16:21
 */
@Data
public class ShoppingCartV4IDTO extends TdaDeliveryTimeRange{
    @ApiModelProperty(value = "商品类型 默认1、普通商品 2、特惠商品")
    private Integer commodityType;

    @ApiModelProperty(value = "商品id")
    private Long commodityId;

    @ApiModelProperty(hidden = true)
    private Long storeId;

    @ApiModelProperty("送货日期")
    private Date orderTime;

    @ApiModelProperty("是否是购物车页面请求")
    private YesOrNoEnums shopCartPage;

    public static ShoppingCartV4IDTO builder(Long storeId, Date orderTime, YesOrNoEnums shopCartPage){
        ShoppingCartV4IDTO shoppingCartAddV4IDTO = new ShoppingCartV4IDTO();
        shoppingCartAddV4IDTO.setStoreId(storeId);
        shoppingCartAddV4IDTO.setOrderTime(orderTime);
        shoppingCartAddV4IDTO.setShopCartPage(shopCartPage);
        return shoppingCartAddV4IDTO;
    }
}

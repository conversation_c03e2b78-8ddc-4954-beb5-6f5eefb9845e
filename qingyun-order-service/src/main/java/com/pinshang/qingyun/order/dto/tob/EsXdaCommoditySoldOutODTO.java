package com.pinshang.qingyun.order.dto.tob;

import com.pinshang.qingyun.base.enums.YesOrNoEnums;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * <AUTHOR>
 * @Description 查当日, 只有soldOut有值
 * @Date 2024/04/15
 * @Version 1.0
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class EsXdaCommoditySoldOutODTO {
    /**
     * 商品id
     */
    private Long commodityId;

    @ApiModelProperty("1有库存 0售罄")
    private Integer soldOut;
    @ApiModelProperty("1有库存 0售罄")
    private Integer soldOut1;
    @ApiModelProperty("1有库存 0售罄")
    private Integer soldOut2;
    @ApiModelProperty("1有库存 0售罄")
    private Integer soldOut3;
    @ApiModelProperty("1有库存 0售罄")
    private Integer soldOut4;
    @ApiModelProperty("1有库存 0售罄")
    private Integer soldOut5;
    @ApiModelProperty("1有库存 0售罄")
    private Integer soldOut6;
    @ApiModelProperty("1有库存 0售罄")
    private Integer soldOut7;

    public void setUnLimit(){
        this.soldOut = YesOrNoEnums.YES.getCode();
        this.soldOut1 = YesOrNoEnums.YES.getCode();
        this.soldOut2 = YesOrNoEnums.YES.getCode();
        this.soldOut3 = YesOrNoEnums.YES.getCode();
        this.soldOut4 = YesOrNoEnums.YES.getCode();
        this.soldOut5 = YesOrNoEnums.YES.getCode();
        this.soldOut6 = YesOrNoEnums.YES.getCode();
        this.soldOut7 = YesOrNoEnums.YES.getCode();
    }

    public void setTotal(Integer soldOut){
        this.soldOut = soldOut;
        this.soldOut1 = soldOut;
        this.soldOut2 = soldOut;
        this.soldOut3 = soldOut;
        this.soldOut4 = soldOut;
        this.soldOut5 = soldOut;
        this.soldOut6 = soldOut;
        this.soldOut7 = soldOut;
    }


}

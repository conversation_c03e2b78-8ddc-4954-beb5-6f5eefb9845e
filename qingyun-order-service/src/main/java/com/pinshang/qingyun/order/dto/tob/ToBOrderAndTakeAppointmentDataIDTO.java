package com.pinshang.qingyun.order.dto.tob;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @Description：
 * @Author：ZhangHui
 * @Package：com.pinshang.qingyun.order.dto.tob
 * @Date: 2024/4/12
 */
@Data
@ApiModel(value = "ToBOrderAndTakeAppointmentDataIDTO", description = "查询B端订单数据和提货卡数据查询入参")
public class ToBOrderAndTakeAppointmentDataIDTO {

    @ApiModelProperty("送货日期时间集合")
    private List<String> orderTimes;

    @ApiModelProperty("商品id集合")
    private List<String> commodityIds;

    @ApiModelProperty("送货开始日期")
    private String startOrderTime;

    @ApiModelProperty("送货结束日期")
    private String endOrderTime;


}

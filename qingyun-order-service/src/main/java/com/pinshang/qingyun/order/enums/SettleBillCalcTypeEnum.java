package com.pinshang.qingyun.order.enums;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;

/**
 * <AUTHOR>
 * @Version 0.1.0
 * @Date 2020/12/30 16:07
 * @Copyright © 2019-2020 qm
 * @Description -- t_store_type_desc.settle_bill_calc_type
 *  结算类型：
 *  0：不结算，
 *  1：鲜达模式=取最小（数量：实发小取实发，下单小去下单，日期：实发晚取实发日期，送货日期晚取送货日期），
 *  2：批发模式，
 *  3：门店结算（取实发），
 *  4：取下单
 **/
public enum SettleBillCalcTypeEnum implements BaseEnum {

    NO(0, "不结算"),
    XD(1, "鲜达模式（时间：取最大；数量：取最小）"),
    PF(2, "批发模式（结算日期=出库日期、下单日期谁大取谁，结算金额取出库金额）"),
    SHOP(3, "门店结算（取实发）"),
    TO_B(4, "普通（取下单）"),

    ;

    private int code;
    private String name;


    private SettleBillCalcTypeEnum(int code, String name) {
        this.code = code;
        this.name = name;
    }

    @JsonValue
    public int getCode() {
        return this.code;
    }

    public String getName() {
        return this.name;
    }


    @JsonCreator
    public static SettleBillCalcTypeEnum getByCode(Integer code) {
        if (code == null) {
            return null;
        } else {
            SettleBillCalcTypeEnum[] var1 = values();
            int var2 = var1.length;
            for (int var3 = 0; var3 < var2; ++var3) {
                SettleBillCalcTypeEnum value = var1[var3];
                if (value.getCode() == code) {
                    return value;
                }
            }
            return null;
        }
    }

    @Override
    public Integer convert() {
        return this.code;
    }
}

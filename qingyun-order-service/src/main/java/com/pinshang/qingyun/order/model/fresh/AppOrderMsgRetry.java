package com.pinshang.qingyun.order.model.fresh;

import javax.persistence.Entity;
import javax.persistence.Table;

import lombok.Data;
import lombok.NoArgsConstructor;

import com.pinshang.qingyun.base.po.BaseSimplePO;

/**
 * 清美生鲜-订单消息重试
 **/
@Data
@NoArgsConstructor
@Entity
@Table(name = "t_app_order_msg_retry")
public class AppOrderMsgRetry extends BaseSimplePO {
	// 订单ID
	private Long orderId;
	// 操作类型：INSERT-新增、UPDATE-更新、CANCEL-取消 —— 参见 KafkaMessageOperationTypeEnum
	private String operateType;
}
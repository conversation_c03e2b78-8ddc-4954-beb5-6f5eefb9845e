package com.pinshang.qingyun.order.util;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 * Created  on 16/9/14.
 */
@Component
public class SettleConfig
{
    /**
     * PDF 生成目录
     */
    @Value("${print.path.rootPath}")
    private String rootPath;
    
    /**
     * 上传文件夹
     */
    @Value("${print.path.upPath}")
    private String upPath;

    @Value("{print.path.url}")
    private String url;

    public String getRootPath()
    {
        return rootPath;
    }

    public void setRootPath(String rootPath)
    {
        this.rootPath = rootPath;
    }

	public String getUpPath() {
		return upPath;
	}

	public void setUpPath(String upPath) {
		this.upPath = upPath;
	}

	public String getUrl() {
		return url;
	}

	public void setUrl(String url) {
		this.url = url;
	}
    
    
}

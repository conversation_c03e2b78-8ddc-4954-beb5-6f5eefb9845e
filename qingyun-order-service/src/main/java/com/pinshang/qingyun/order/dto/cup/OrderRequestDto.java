package com.pinshang.qingyun.order.dto.cup;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @Author: sk
 * @Date: 2024/7/18
 */
@Data
public class OrderRequestDto {

    //编辑
    private Long orderId;
    /**商铺id **/
    private Long storeId;
    /**订单时间 **/
    private String orderTime;
    @ApiModelProperty("打印类型(1：本地,2：送货员,3：不打印)")
    private Integer printType;
    /** 添加商品方式(1：商品编码,2：商品条码) */
    private Integer addCommodityType;
    /**打印份数*/
    private Integer printNum;
    /**备注 **/
    private String orderRemark;
    /** 企业id **/
    private Long enterpriseId;
    private Long userId;
    private String userName;
    private String employeeNumber;
    /** 0:普通订单 1：补货订单 **/
    private int modeType;

    /**
     * 配送批次：1-一配、2-二配		—— 参见枚举： DeliveryBatchEnums
     */
    private Integer deliveryBatch;

    /**
     * 物流中心ID
     */
    private Long logisticsCenterId;

    /**
     * 物流中心名称
     */
    private String logisticsCenterName;

    /**
     * 业务类型：10-通达销售	—— 参见枚举： BusinessTypeEnums
     */
    private Integer businessType;

    private String deliveryTimeRange;


    private List<OrderItemRequestDto> itemsList;


    @ApiModelProperty("订单导入明细list")
    private List<OrderImportSaveIDTO> importList;
}

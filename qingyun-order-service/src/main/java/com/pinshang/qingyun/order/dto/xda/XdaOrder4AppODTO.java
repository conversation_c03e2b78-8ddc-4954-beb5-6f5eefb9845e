package com.pinshang.qingyun.order.dto.xda;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.pinshang.qingyun.base.configure.codec.DecimalSerializerKeep2;
import com.pinshang.qingyun.base.enums.xda.XdaOrderProcessStatusEunm;
import com.pinshang.qingyun.order.dto.shopcart.ShoppingCartCommodityODTO;
import com.pinshang.qingyun.order.dto.shopcart.ShoppingCartGroupODTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Map;


/**
 * <AUTHOR>
 * @Date 2019/11/20 9:50
 */
@Data
@ApiModel
public class XdaOrder4AppODTO {
    @ApiModelProperty(value = "orderId", hidden = true)
    private Long orderId;
    @ApiModelProperty("订单号")
    private String orderCode;
    @ApiModelProperty(" 实付总金额")
    @JsonSerialize(using = DecimalSerializerKeep2.class)
    private BigDecimal summation;
    @ApiModelProperty(" 实发金额")
    @JsonSerialize(using = DecimalSerializerKeep2.class)
    private BigDecimal realAmount;
    @ApiModelProperty("2=取消 7=待发货11=出库中15=配送中19=配送完成")
    private Integer processStatus;
    @ApiModelProperty("订单状态(0正常,1删除,2取消)")
    private Integer orderStatus;
    @ApiModelProperty("送货日期")
    private Date orderTime;
    @ApiModelProperty("订单创建日期")
    private Date orderCreateTime;
    @ApiModelProperty("品种合计")
    private Integer varietySum;
    @ApiModelProperty("商品列表")
    private List<XdaOrderItem4AppODTO> commodities;


}

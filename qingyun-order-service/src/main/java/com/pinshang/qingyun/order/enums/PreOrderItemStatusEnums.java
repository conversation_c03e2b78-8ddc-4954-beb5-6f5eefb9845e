package com.pinshang.qingyun.order.enums;
/*
 * 预订单明细状态
 * 状态: 0-待审核,1-驳回,2-通过, 默认0
 */
public enum PreOrderItemStatusEnums {

	UNCHECK("待审核", 0),
	REJECT("驳回", 1),
	PASS("通过", 2);
    private String name;
    private int code;

    PreOrderItemStatusEnums(String name, int code) {
        this.name = name;
        this.code = code;
    }

    public static String getName(int code) {
        for (PreOrderItemStatusEnums es : PreOrderItemStatusEnums.values()) {
            if (code == es.getCode()) {
                return es.name;
            }
        }
        return null;
    }

    public String getName() {
        return name;
    }

    public int getCode() {
        return code;
    }
}

package com.pinshang.qingyun.order.enums;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;

/**
 *
 * <AUTHOR>
 * @Date 2018/4/8 16:34
 */
public enum OperationTypeEnum implements BaseEnum{
    ORDER_NEW(11, "订单_新建"),
    ORDER_CANCEL(12,"订单_取消"),
    COMMODITY_ADD(21,"商品_添加"),
    COMMODITY_UPDATE(22,"商品_修改"),
    COMMODITY_DEL(23,"商品_删除"),
    MODIFY_DELIVER_DATE(31,"修改送货日期"),
    PRE_ORDER_CHECK(32,"直送订单审核"),
    DIRECT_SENDING_BACK_ORDER(33,"直送补单"),
    ;

    private Integer code;
    private String desc;

    OperationTypeEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    @Override
    public Integer convert() {
        return this.code;
    }
    @JsonValue
    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }
    @JsonCreator
    public static OperationTypeEnum getEnumByCode(Integer code){
        if(code == null){
            return null;
        }
        for(OperationTypeEnum e : OperationTypeEnum.values()){
            if(e.getCode().intValue() == code){
                return e;
            }
        }
        return null;
    }
}

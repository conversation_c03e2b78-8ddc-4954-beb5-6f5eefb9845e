package com.pinshang.qingyun.order.model.tob;

import lombok.Data;

import javax.persistence.Table;
import java.util.Date;

@Data
@Table(name = "t_dc_tob_commodity_stock")
public class TobCommodityStock {

    private Long commodityId;
    private Long warehouseId;
    /**
     * 1=依据大仓, 2=不限量订货,3=限量供应
     */
    private Integer stockType;
    /**
     * 是否有库存；0：无；1：有
     */
    private Integer stockStatus;

    /**
     * 是否有库存；0：无；1：有
     */
    private Integer tdaStockStatus; //通达销售类型新加
    /**
     * 限量值(stock_type=3 时才有值)
     */
    private Integer limitNumber;
    /**
     * 生效方式: 1=限总量，2=每天循环限量
     */
    private Integer effectType;
    private Long createId;
    private Date createTime;
    private Long updateId;
    private Date updateTime;

    /**
     * 分仓是否有库存；0：无；1：有
     */
    private Integer nationalStockStatus;

    /**
     * 物流中心ID
     */
    private Long logisticsCenterId;


}

package com.pinshang.qingyun.order.model.shop;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.math.BigDecimal;
import java.util.Date;

@Entity
@Table(name="t_complaint_commodity_list")
public class ComplaintItem {
	@Id
	@Column(name = "uid")
	private String uid;
	
	private Integer enabled =1;
	
	private Date createdAt;
	private Long enterpriseId;
	private String commodityCode;
	private String commodityId;
	private String commodityOrderNumber;
	private BigDecimal commodityPrice;
	private String complaintId;
	private BigDecimal complaintMoney;
	private BigDecimal complaintNumber;
	private Integer complaintType;
	private String createId;
	public Date getCreatedAt() {
		return createdAt;
	}
	public void setCreatedAt(Date createdAt) {
		this.createdAt = createdAt;
	}
	public Long getEnterpriseId() {
		return enterpriseId;
	}
	public void setEnterpriseId(Long enterpriseId) {
		this.enterpriseId = enterpriseId;
	}
	public String getCommodityCode() {
		return commodityCode;
	}
	public void setCommodityCode(String commodityCode) {
		this.commodityCode = commodityCode;
	}
	public String getCommodityId() {
		return commodityId;
	}
	public void setCommodityId(String commodityId) {
		this.commodityId = commodityId;
	}
	public String getCommodityOrderNumber() {
		return commodityOrderNumber;
	}
	public void setCommodityOrderNumber(String commodityOrderNumber) {
		this.commodityOrderNumber = commodityOrderNumber;
	}
	public BigDecimal getCommodityPrice() {
		return commodityPrice;
	}
	public void setCommodityPrice(BigDecimal commodityPrice) {
		this.commodityPrice = commodityPrice;
	}
	public String getComplaintId() {
		return complaintId;
	}
	public void setComplaintId(String complaintId) {
		this.complaintId = complaintId;
	}
	public BigDecimal getComplaintMoney() {
		return complaintMoney;
	}
	public void setComplaintMoney(BigDecimal complaintMoney) {
		this.complaintMoney = complaintMoney;
	}
	public BigDecimal getComplaintNumber() {
		return complaintNumber;
	}
	public void setComplaintNumber(BigDecimal complaintNumber) {
		this.complaintNumber = complaintNumber;
	}
	public Integer getComplaintType() {
		return complaintType;
	}
	public void setComplaintType(Integer complaintType) {
		this.complaintType = complaintType;
	}
	public String getCreateId() {
		return createId;
	}
	public void setCreateId(String createId) {
		this.createId = createId;
	}
	public String getUid() {
		return uid;
	}
	public void setUid(String uid) {
		this.uid = uid;
	}
}

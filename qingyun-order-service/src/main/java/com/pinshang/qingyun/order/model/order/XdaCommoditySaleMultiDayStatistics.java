package com.pinshang.qingyun.order.model.order;

import com.pinshang.qingyun.base.po.BaseIDPO;
import lombok.Data;

import javax.persistence.Table;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 目前统计最近7天的销售数据
 */
@Table(name = "t_xda_commodity_sale_multi_day_statistics")
@Data
public class XdaCommoditySaleMultiDayStatistics extends BaseIDPO {
    private Long commodityId;
    private BigDecimal totalQuantity;
    private Date createTime;
}

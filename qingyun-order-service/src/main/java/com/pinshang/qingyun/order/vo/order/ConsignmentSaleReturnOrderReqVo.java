package com.pinshang.qingyun.order.vo.order;

import com.pinshang.qingyun.base.page.Pagination;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;
import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class ConsignmentSaleReturnOrderReqVo extends Pagination {
    /**退货日期 开始日期*/
    @ApiModelProperty(value = "退货日期 开始日期")
    private String returnOrderStartDate;
    /**退货日期 结束日期*/
    @ApiModelProperty(value = "退货日期 结束日期")
    private String returnOrderEndDate;

    /**确认日期 开始日期*/
    @ApiModelProperty(value = "确认日期 开始日期")
    private String confirmOrderStartDate;
    /**确认日期 结束日期*/
    @ApiModelProperty(value = "确认日期 结束日期")
    private String confirmOrderEndDate;

    /**审核日期 开始日期*/
    @ApiModelProperty(value = "审核日期 开始日期")
    private String auditOrderStartDate;
    /**审核日期 结束日期*/
    @ApiModelProperty(value = "审核日期 结束日期")
    private String auditOrderEndDate;

    /**退货单号*/
    @ApiModelProperty(value = "退货单号")
    private String returnOrderCode;
    /**退货单状态 0＝取消，1＝待确认，2＝待审核,3＝已驳回,4＝已完成*/
    @ApiModelProperty(value = "退货单状态 0＝取消，1＝待确认，2＝待审核,3＝已驳回,4＝已完成")
    private Integer status;

    /**
     * 组织code
     */
    @ApiModelProperty(value = "组织code")
    private String orgCode;

    /**
     * 门店id
     */
    @ApiModelProperty(value = "门店id")
    private String shopId;

    /**
     * 代销供应商id
     */
    @ApiModelProperty(value = "代销供应商id")
    private String supplierId;

    /**
     * 客户id
     */
    @ApiModelProperty(value = "客户id")
    private String storeId;

    /**
     * 创建人
     */
    @ApiModelProperty(value = "创建人id")
    private Long createId;

    /**
     * 确认人
     */
    @ApiModelProperty(value = "确认人id")
    private Long confirmId;

    @ApiModelProperty(value = "门店列表")
    private List<Long> shopIdList;

}

package com.pinshang.qingyun.order.vo.order;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class OrderReplenishmentCommodityOVO {

     private String productId;//商品ID

     private String productCode;//商品编码

     private String productName;//商品名称

     private String productSpec;//规格

     private String barcode;//条码

     private String barcodes;//字码列表

    private BigDecimal price;//单价

    private BigDecimal productNum;//数量

    private BigDecimal productAmount;//金额小计

}

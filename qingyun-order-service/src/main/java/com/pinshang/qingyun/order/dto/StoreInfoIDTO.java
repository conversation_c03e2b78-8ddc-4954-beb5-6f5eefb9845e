package com.pinshang.qingyun.order.dto;

import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModelProperty;
import jdk.nashorn.internal.ir.annotations.Ignore;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 *  查询待收货/收货中的客户
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class StoreInfoIDTO {
    @ApiModelProperty("仓库ID")
    private Long warehouseId;
    @ApiModelProperty("开始时间")
    private String beginTime;
    @ApiModelProperty("结束时间")
    private String endTime;
    @ApiModelProperty("客户名称")
    private String storeName;
    private Long receiverId;
}

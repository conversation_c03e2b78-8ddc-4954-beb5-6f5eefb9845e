package com.pinshang.qingyun.order.model.employee;

import java.math.BigDecimal;
import java.util.Date;

import javax.persistence.Entity;
import javax.persistence.Table;

import com.pinshang.qingyun.base.po.BaseTimePO;

/**
 * 职员信息表
 **/
@Entity
@Table(name = "t_employee")
public class Employee extends BaseTimePO {
 
	/**职员工号 **/
    private String employeeCode;
	/**职员名称 **/
    private String employeeName;
	/**职员性别 **/
    private String employeeSex;
    /**助记码 **/
    private String mnemonicCode;
    /**老立成旧编码 **/
    private String oldCode;
    /**老烽火旧编码**/
    private String firehomeOldCode;
    /**证件类型 **/
    private String credentialType;
    /**条形码 **/
    private String barCode;
    /**邮政编码 **/
    private String postCode;
    /**停车位 **/
    private String parkingSpace;
    /**班组 **/
    private String workGroup;
    /**班组长 **/
    private String teamLeader;
	/**出生日期 **/
    private Date employeeBirthday;
	/**政治面貌 **/
    private String employPolity;
	/**家庭地址 **/
    private String employeeAddress;
	/**家庭电话 **/
    private String employeePhone;
	/**备注 **/
    private String employeeRemark;
	/**手机号码 **/
    private String employeeMsidn;
	/**手机集团短号 **/
    private String employeeShortNumber;
	/**办公电话 **/
    private String employeeOfficeTel;
	/**办公地址 **/
    private String employeeOfficeAddress;
	/**电子邮件地址 **/
    private String employeeEmail;
	/**电子邮件地址2 **/
    private String employeeEmail2;
	/**身份证编码 **/
    private String employeeStationCode;
	/**级别 **/
    private String employeeClass;
	/**最高学历 **/
    private String employDiploma;
	/**毕业院校 **/
    private String employSchool;
	/**专业 **/
    private String employeeSpecialty;
	/**入职时间 **/
    private Date employeeInDate;
	/**当前状态(1 正常 2请假中 3 注销,4离职) **/
    private String employeeState;
    /**职员账号状态(0 未开通 1 已开通 2 已关闭 )**/
    private String employeeAccountState;
	/**离职时间 **/
    private Date employeeOutDate;
	/**员工排序 **/
    private BigDecimal employeeOrder;
	/**员工维护人员 **/
    private Long employeeBuildId;
	/**员工录入时间 **/
    private Date employeeBuildTime;
	/**省、直辖市 **/
    private String employeeProvince;
	/**城市 **/
    private String employeeCity;
	/**地区 **/
    private String employeeArea;
	/**拼音？ **/
    private String employeeSpell;
	/**更新者? **/
    private Long updateId;
    private Long parentId;
	/**直接上级ID **/
    private Long parentEmployeeId;
    private String employeePointLatlon;
    private String employeePointExplain;
    private String employeeFax;
    private String employeePostcode;
    private Long employeeType;
	/**职员预留字段1 **/
    private String employeeSpare1;
	/**职员预留字段2 **/
    private String employeeSpare2;
	/**职员预留字段3 **/
    private String employeeSpare3;
	/**职员预留字段4 **/
    private String employeeSpare4;
	/**职员预留字段5 **/
    private String employeeSpare5;
	/**职员预留字段6 **/
    private String employeeSpare6;
    
	public String getEmployeeCode() {
		return employeeCode;
	}
	public void setEmployeeCode(String employeeCode) {
		this.employeeCode = employeeCode;
	}
	public String getEmployeeName() {
		return employeeName;
	}
	public void setEmployeeName(String employeeName) {
		this.employeeName = employeeName;
	}
	public String getEmployeeSex() {
		return employeeSex;
	}
	public void setEmployeeSex(String employeeSex) {
		this.employeeSex = employeeSex;
	}
	public String getMnemonicCode() {
		return mnemonicCode;
	}
	public void setMnemonicCode(String mnemonicCode) {
		this.mnemonicCode = mnemonicCode;
	}
	public String getOldCode() {
		return oldCode;
	}
	public void setOldCode(String oldCode) {
		this.oldCode = oldCode;
	}
	public String getFirehomeOldCode() {
		return firehomeOldCode;
	}
	public void setFirehomeOldCode(String firehomeOldCode) {
		this.firehomeOldCode = firehomeOldCode;
	}
	public String getCredentialType() {
		return credentialType;
	}
	public void setCredentialType(String credentialType) {
		this.credentialType = credentialType;
	}
	public String getBarCode() {
		return barCode;
	}
	public void setBarCode(String barCode) {
		this.barCode = barCode;
	}
	public String getPostCode() {
		return postCode;
	}
	public void setPostCode(String postCode) {
		this.postCode = postCode;
	}
	public String getParkingSpace() {
		return parkingSpace;
	}
	public void setParkingSpace(String parkingSpace) {
		this.parkingSpace = parkingSpace;
	}
	public String getWorkGroup() {
		return workGroup;
	}
	public void setWorkGroup(String workGroup) {
		this.workGroup = workGroup;
	}
	public String getTeamLeader() {
		return teamLeader;
	}
	public void setTeamLeader(String teamLeader) {
		this.teamLeader = teamLeader;
	}
	public Date getEmployeeBirthday() {
		return employeeBirthday;
	}
	public void setEmployeeBirthday(Date employeeBirthday) {
		this.employeeBirthday = employeeBirthday;
	}
	public String getEmployPolity() {
		return employPolity;
	}
	public void setEmployPolity(String employPolity) {
		this.employPolity = employPolity;
	}
	public String getEmployeeAddress() {
		return employeeAddress;
	}
	public void setEmployeeAddress(String employeeAddress) {
		this.employeeAddress = employeeAddress;
	}
	public String getEmployeePhone() {
		return employeePhone;
	}
	public void setEmployeePhone(String employeePhone) {
		this.employeePhone = employeePhone;
	}
	public String getEmployeeRemark() {
		return employeeRemark;
	}
	public void setEmployeeRemark(String employeeRemark) {
		this.employeeRemark = employeeRemark;
	}
	public String getEmployeeMsidn() {
		return employeeMsidn;
	}
	public void setEmployeeMsidn(String employeeMsidn) {
		this.employeeMsidn = employeeMsidn;
	}
	public String getEmployeeShortNumber() {
		return employeeShortNumber;
	}
	public void setEmployeeShortNumber(String employeeShortNumber) {
		this.employeeShortNumber = employeeShortNumber;
	}
	public String getEmployeeOfficeTel() {
		return employeeOfficeTel;
	}
	public void setEmployeeOfficeTel(String employeeOfficeTel) {
		this.employeeOfficeTel = employeeOfficeTel;
	}
	public String getEmployeeOfficeAddress() {
		return employeeOfficeAddress;
	}
	public void setEmployeeOfficeAddress(String employeeOfficeAddress) {
		this.employeeOfficeAddress = employeeOfficeAddress;
	}
	public String getEmployeeEmail() {
		return employeeEmail;
	}
	public void setEmployeeEmail(String employeeEmail) {
		this.employeeEmail = employeeEmail;
	}
	public String getEmployeeEmail2() {
		return employeeEmail2;
	}
	public void setEmployeeEmail2(String employeeEmail2) {
		this.employeeEmail2 = employeeEmail2;
	}
	public String getEmployeeStationCode() {
		return employeeStationCode;
	}
	public void setEmployeeStationCode(String employeeStationCode) {
		this.employeeStationCode = employeeStationCode;
	}
	public String getEmployeeClass() {
		return employeeClass;
	}
	public void setEmployeeClass(String employeeClass) {
		this.employeeClass = employeeClass;
	}
	public String getEmployDiploma() {
		return employDiploma;
	}
	public void setEmployDiploma(String employDiploma) {
		this.employDiploma = employDiploma;
	}
	public String getEmploySchool() {
		return employSchool;
	}
	public void setEmploySchool(String employSchool) {
		this.employSchool = employSchool;
	}
	public String getEmployeeSpecialty() {
		return employeeSpecialty;
	}
	public void setEmployeeSpecialty(String employeeSpecialty) {
		this.employeeSpecialty = employeeSpecialty;
	}
	public Date getEmployeeInDate() {
		return employeeInDate;
	}
	public void setEmployeeInDate(Date employeeInDate) {
		this.employeeInDate = employeeInDate;
	}
	public String getEmployeeState() {
		return employeeState;
	}
	public void setEmployeeState(String employeeState) {
		this.employeeState = employeeState;
	}
	public String getEmployeeAccountState() {
		return employeeAccountState;
	}
	public void setEmployeeAccountState(String employeeAccountState) {
		this.employeeAccountState = employeeAccountState;
	}
	public Date getEmployeeOutDate() {
		return employeeOutDate;
	}
	public void setEmployeeOutDate(Date employeeOutDate) {
		this.employeeOutDate = employeeOutDate;
	}
	public BigDecimal getEmployeeOrder() {
		return employeeOrder;
	}
	public void setEmployeeOrder(BigDecimal employeeOrder) {
		this.employeeOrder = employeeOrder;
	}
	public Long getEmployeeBuildId() {
		return employeeBuildId;
	}
	public void setEmployeeBuildId(Long employeeBuildId) {
		this.employeeBuildId = employeeBuildId;
	}
	public Date getEmployeeBuildTime() {
		return employeeBuildTime;
	}
	public void setEmployeeBuildTime(Date employeeBuildTime) {
		this.employeeBuildTime = employeeBuildTime;
	}
	public String getEmployeeProvince() {
		return employeeProvince;
	}
	public void setEmployeeProvince(String employeeProvince) {
		this.employeeProvince = employeeProvince;
	}
	public String getEmployeeCity() {
		return employeeCity;
	}
	public void setEmployeeCity(String employeeCity) {
		this.employeeCity = employeeCity;
	}
	public String getEmployeeArea() {
		return employeeArea;
	}
	public void setEmployeeArea(String employeeArea) {
		this.employeeArea = employeeArea;
	}
	public String getEmployeeSpell() {
		return employeeSpell;
	}
	public void setEmployeeSpell(String employeeSpell) {
		this.employeeSpell = employeeSpell;
	}
	public Long getUpdateId() {
		return updateId;
	}
	public void setUpdateId(Long updateId) {
		this.updateId = updateId;
	}
	public Long getParentId() {
		return parentId;
	}
	public void setParentId(Long parentId) {
		this.parentId = parentId;
	}
	public Long getParentEmployeeId() {
		return parentEmployeeId;
	}
	public void setParentEmployeeId(Long parentEmployeeId) {
		this.parentEmployeeId = parentEmployeeId;
	}
	public String getEmployeePointLatlon() {
		return employeePointLatlon;
	}
	public void setEmployeePointLatlon(String employeePointLatlon) {
		this.employeePointLatlon = employeePointLatlon;
	}
	public String getEmployeePointExplain() {
		return employeePointExplain;
	}
	public void setEmployeePointExplain(String employeePointExplain) {
		this.employeePointExplain = employeePointExplain;
	}
	public String getEmployeeFax() {
		return employeeFax;
	}
	public void setEmployeeFax(String employeeFax) {
		this.employeeFax = employeeFax;
	}
	public String getEmployeePostcode() {
		return employeePostcode;
	}
	public void setEmployeePostcode(String employeePostcode) {
		this.employeePostcode = employeePostcode;
	}
	public Long getEmployeeType() {
		return employeeType;
	}
	public void setEmployeeType(Long employeeType) {
		this.employeeType = employeeType;
	}
	public String getEmployeeSpare1() {
		return employeeSpare1;
	}
	public void setEmployeeSpare1(String employeeSpare1) {
		this.employeeSpare1 = employeeSpare1;
	}
	public String getEmployeeSpare2() {
		return employeeSpare2;
	}
	public void setEmployeeSpare2(String employeeSpare2) {
		this.employeeSpare2 = employeeSpare2;
	}
	public String getEmployeeSpare3() {
		return employeeSpare3;
	}
	public void setEmployeeSpare3(String employeeSpare3) {
		this.employeeSpare3 = employeeSpare3;
	}
	public String getEmployeeSpare4() {
		return employeeSpare4;
	}
	public void setEmployeeSpare4(String employeeSpare4) {
		this.employeeSpare4 = employeeSpare4;
	}
	public String getEmployeeSpare5() {
		return employeeSpare5;
	}
	public void setEmployeeSpare5(String employeeSpare5) {
		this.employeeSpare5 = employeeSpare5;
	}
	public String getEmployeeSpare6() {
		return employeeSpare6;
	}
	public void setEmployeeSpare6(String employeeSpare6) {
		this.employeeSpare6 = employeeSpare6;
	}
}
package com.pinshang.qingyun.order.util;


import cn.jiguang.common.ClientConfig;
import cn.jiguang.common.resp.APIConnectionException;
import cn.jiguang.common.resp.APIRequestException;
import cn.jpush.api.JPushClient;
import cn.jpush.api.push.PushResult;
import cn.jpush.api.push.model.Platform;
import cn.jpush.api.push.model.PushPayload;
import cn.jpush.api.push.model.audience.Audience;
import cn.jpush.api.push.model.notification.Notification;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

/**
 * summary.
 * <p>
 * detailed description
 *
 * <AUTHOR> Zhang
 * @version 1.0
 * @since 2018/11/19
 */
@Slf4j
@Component
public class JPushUtil {
    private static String MASTER_SECRET = "ce24c635312c27556cd9088e";
    private static String APP_KEY = "89a107ccf4fcb033ab65838a";
    private static final JPushClient jpushClient = new JPushClient(MASTER_SECRET, APP_KEY, null, ClientConfig.getInstance());

    @Async
    public void pushMsgForAlias(String alert, String alias, String title) {
        // 构建推送对象：平台是 Android，目标是 alias 为 "xxx" 的设备，内容是 Android 通知 ALERT，并且标题为 TITLE。
        PushPayload payload = buildPushObject_android_alias_alertWithTitle(alert, alias, title);
        try {
            // {"msg_id":54043197885071350,"sendno":915188647,"statusCode":0} statusCode=0表示成功
            log.info("starting push msg alias={}, title={}", alias, title);
            PushResult result = jpushClient.sendPush(payload);
            log.info("Got result - " + result);
        } catch (APIConnectionException e) {
            // Connection error, should retry later
            log.error("Connection error, should retry later", e);
        } catch (APIRequestException e) {
            // Should review the error, and fix the request
            log.error("Should review the error, and fix the request", e);
            log.info("HTTP Status: " + e.getStatus());
            log.info("Error Code: " + e.getErrorCode());
            log.info("Error Message: " + e.getErrorMessage());
        }
    }


    public static PushPayload buildPushObject_android_alias_alertWithTitle(String alert, String alias, String title) {
        return PushPayload.newBuilder()
                .setPlatform(Platform.android())
                .setAudience(Audience.alias(alias))
                .setNotification(Notification.android(alert, title, null))
                .build();
    }

    /**
     * 快捷地构建推送对象：所有平台，所有设备，内容为 ALERT 的通知。
     * @return
     */
    public static PushPayload buildPushObject_all_all_alert() {
        return PushPayload.alertAll(null);
    }

    public static void main(String[] args) {
        // 账号 009527  密码：12456  createId:372
        JPushUtil jPushUtil = new JPushUtil();
        jPushUtil.pushMsgForAlias("有订单即将超过订货时间", "372","购物车里，部分商品将在截止下单（还有5分钟）！");
    }
}

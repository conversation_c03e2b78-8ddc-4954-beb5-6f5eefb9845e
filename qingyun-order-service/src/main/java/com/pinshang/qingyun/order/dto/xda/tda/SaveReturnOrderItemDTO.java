package com.pinshang.qingyun.order.dto.xda.tda;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.pinshang.qingyun.base.configure.codec.DecimalSerializerKeep2;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @Version 0.1.0
 * @Date 2020/12/30 15:26
 * @Copyright © 2019-2020 qm
 * @Description -- 类说明投诉商品明细
 **/
@Data
public class SaveReturnOrderItemDTO {

    @ApiModelProperty(value = "商品id", example = "123456", required = true)
    private Long commodityId;

    @ApiModelProperty(value = "商品单位", example = "kg", required = true)
    private String commodityUnit;

    @ApiModelProperty(value = "商品实发数量", example = "12.6", required = true)
    private BigDecimal realDeliveryQuantity;

    @ApiModelProperty(value = "商品的投诉类型:1少货;2退货;3多货，不需要app端填写，用来在app投诉单列表展示使用", example = "2")
    private Integer complaintType;

    @ApiModelProperty(value = "客户多收少收数量（+多收-少收）/客户退货数量，投诉提交或修改时填写", example = "45.47", required = true)
    private BigDecimal realReturnQuantity;

    @ApiModelProperty(value = "投诉问题原因类型-字典类型，投诉提交或修改时填写", example = "1", required = true)
    private Long questionType;

    @ApiModelProperty(value = "投诉照片，投诉提交或修改时填写", example = "投诉图片.jpg", required = true)
    private List<String> complaintPicList;

    @ApiModelProperty(value = "商品单价", example = "56.36", required = true)
    @JsonSerialize(using = DecimalSerializerKeep2.class)
    private BigDecimal commodityPrice;
    /**
     * 是否称重 0=否 1=是
     */
    private Integer isWeight;

    /**
     * 投诉内容
     */
    private String complaintContent;


}

package com.pinshang.qingyun.order.enums;

import lombok.AllArgsConstructor;

import java.util.Objects;

/**
 * 必售标记(1.大店必售 2.大中店必售 3.全部必售)
 */
@AllArgsConstructor
public enum MustSellCommodityTypeEnum {
    BIGSELL(1, "大店必售"),
    MIDSELL(2,"大中店必售"),
    ALL(3,"全部"),

    ;
    private Integer code;
    private String name;

    public Integer getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    public static Integer getCode(String name) {
        for (MustSellCommodityTypeEnum mc : MustSellCommodityTypeEnum.values()) {
            if (Objects.equals(name, mc.name)) {
                return mc.code;
            }
        }
        return null;
    }
}

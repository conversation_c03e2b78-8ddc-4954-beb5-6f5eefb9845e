package com.pinshang.qingyun.order.vo.auto;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class AutoShopCommodityListVO {

    private Long autoShopCommodityId;

    private Long commodityId;

    @ApiModelProperty("商品编码")
    private String commodityCode;

    @ApiModelProperty("商品名称")
    private String commodityName;

    @ApiModelProperty("条形码")
    private String barCode;

    @ApiModelProperty("规格")
    private String commoditySpec;

    @ApiModelProperty("单位")
    private String commodityUnitName;

    @ApiModelProperty("商品大类")
    private String firstCateName;

    /**
     * 是否速冻产品
     */
    @ApiModelProperty("是否速冻产品")
    private Integer commodityIsQuickFreeze;

    /**
     * 是否称重0-不称量,1-称重
     */
    @ApiModelProperty("否称重0-不称量,1-称重")
    private Integer isWeight;

    /**
     * 订货箱规
     */
    @ApiModelProperty("订货箱规")
    private BigDecimal commodityPackageSpec;

    @ApiModelProperty("订货截单时间")
    private String endTime;

    @ApiModelProperty("安全库存")
    private BigDecimal stockQuantity;

    /**
     * 前置仓除外的箱规
     */
    private BigDecimal salesBoxCapacity;

    /**
     * 前置仓的销售箱规
     */
    private BigDecimal xdSalesBoxCapacity;

    @ApiModelProperty("是否要求品项  1=是 2=否")
    private Integer isRequiredItem;
}

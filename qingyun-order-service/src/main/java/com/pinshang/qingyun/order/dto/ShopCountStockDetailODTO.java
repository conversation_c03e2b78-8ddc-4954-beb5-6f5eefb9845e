package com.pinshang.qingyun.order.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.github.pagehelper.PageInfo;
import com.pinshang.qingyun.renderer.annotation.FieldRender;
import com.pinshang.qingyun.renderer.constant.RenderFieldHelper;
import com.pinshang.qingyun.renderer.enums.FieldTypeEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @ClassName ShopCountStockDetailODTO
 * <AUTHOR>
 * @Date 2022/12/14 11:47
 * @Description ShopCountStockDetailODTO
 * @Version 1.0
 */
@Data
public class ShopCountStockDetailODTO{
    @ApiModelProperty("门店名称")
    @FieldRender(fieldType = FieldTypeEnum.SHOP,fieldName = RenderFieldHelper.Shop.shopName,keyName = "shopId")
    private String shopName;

    @ApiModelProperty("部门名称")
    @FieldRender(fieldType = FieldTypeEnum.SHOP_ORG,fieldName = RenderFieldHelper.Org.parentOrgName,keyName = "shopId")
    private String orgName;

    @ApiModelProperty("门店id")
    private Long shopId;

    @ApiModelProperty("送货时间")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
    private String orderTime;

    @ApiModelProperty("订货商品")
    private Integer varietyTotal;

    @ApiModelProperty("已点商品")
    private Integer countNum;

    PageInfo<ShopCountStockDetailItemODTO> itemList;
}

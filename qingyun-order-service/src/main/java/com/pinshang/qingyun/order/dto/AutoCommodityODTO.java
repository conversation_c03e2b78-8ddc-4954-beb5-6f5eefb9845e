package com.pinshang.qingyun.order.dto;

import com.pinshang.qingyun.base.enums.YesOrNoEnums;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Author: liuZhen
 * @DateTime: 2022/5/11 14:06
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class AutoCommodityODTO {
    @ApiModelProperty("商品id")
    private String commodityId;
    @ApiModelProperty("商品编码")
    private String commodityCode;
    @ApiModelProperty("条形码")
    private String barCode;
    @ApiModelProperty("商品名称")
    private String commodityName;
    @ApiModelProperty("规格")
    private String commoditySpec;
    @ApiModelProperty("单位")
    private String commodityUnitName;
    @ApiModelProperty("大类")
    private String firstCateName;
    @ApiModelProperty("中类")
    private String secondCateName;
    @ApiModelProperty("小类")
    private String thirdCateName;
    @ApiModelProperty("是否速冻")
    private String isFreezeStr;
    @ApiModelProperty("是否速冻")
    private Integer isFreeze;

    public void setIsFreeze(Integer isFreeze) {
        this.isFreeze = isFreeze;
        this.isFreezeStr= YesOrNoEnums.getByCode(isFreeze).getName();
    }
}

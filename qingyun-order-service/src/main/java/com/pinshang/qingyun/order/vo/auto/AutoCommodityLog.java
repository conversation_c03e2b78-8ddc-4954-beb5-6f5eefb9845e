package com.pinshang.qingyun.order.vo.auto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Entity;
import javax.persistence.Table;
import java.util.Date;

/**
 * @Author: liu<PERSON>hen
 * @DateTime: 2022/5/10 18:12
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class AutoCommodityLog {

    private Long id;
    /**
     * 商品ID
     */
    private Long commodityId;
    private String commodityCode;
    private String commodityName;
    private String commoditySpec;
    private Integer operationType;
    private Long createId;
    private String createName;
    private String createTime;

}

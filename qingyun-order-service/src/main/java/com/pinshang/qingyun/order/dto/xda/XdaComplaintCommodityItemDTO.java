package com.pinshang.qingyun.order.dto.xda;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.pinshang.qingyun.base.configure.codec.DecimalSerializerKeep2;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @Version 0.1.0
 * @Date 2020/12/30 15:26
 * @Copyright © 2019-2020 qm
 * @Description -- 类说明投诉商品明细
 **/
@Data
public class XdaComplaintCommodityItemDTO {

    @ApiModelProperty(value = "商品id", example = "123456", required = true)
    private Long commodityId;

    @ApiModelProperty(value = "商品的投诉类型:1少货;2退货;3多货，不需要app端填写，用来在app投诉单列表展示使用", example = "2")
    private Integer complaintType;

    @ApiModelProperty(value = "商品图片", example = "123.jpg", required = true)
    private String commodityPicUrl;

    @ApiModelProperty(value = "商品编码", example = "123456code", required = true)
    private String commodityCode;

    @ApiModelProperty(value = "商品名称", example = "酸葡萄", required = true)
    private String commodityName;

    @ApiModelProperty(value = "商品单位", example = "kg", required = true)
    private String commodityUnit;

    @ApiModelProperty(value = "商品实发数量", example = "12.6", required = true)
    private BigDecimal realDeliveryQuantity;

    @ApiModelProperty(value = "商品实发数量+单位", example = "12.6kg", required = true)
    private String realDeliveryQuantityStr;

    @ApiModelProperty(value = "投诉商品金额，app此字段不要做修改，修改投诉单提交时原值返回", example = "12.6", required = true)
    private BigDecimal complaintMoney;

    @ApiModelProperty(value = "客户多收少收数量（+多收-少收）/客户退货数量，投诉提交或修改时填写", example = "45.47", required = true)
    private BigDecimal realReturnQuantity;

    @ApiModelProperty(value = "审核数量", example = "12.6", required = true)
    private BigDecimal checkQuantity;

    @ApiModelProperty(value = "审核数量 + 单位", example = "12.6", required = true)
    private String checkQuantityStr;

    @ApiModelProperty(value = "客户投诉数量/客户退货数量+单位", example = "18.69kg", required = true)
    private String realReturnQuantityStr;

    @ApiModelProperty(value = "投诉问题原因类型-字典类型，投诉提交或修改时填写", example = "1", required = true)
    private Long questionType;

//    @ApiModelProperty(value = "差异投诉原因备注，差异投诉提交时填写", example = "少货缺货", required = true)
//    private String complaintReason;

    @ApiModelProperty(value = "投诉照片，投诉提交或修改时填写", example = "投诉图片.jpg", required = true)
    private List<String> complaintPicList;

    @ApiModelProperty(value = "商品单价", example = "56.36", required = true)
    @JsonSerialize(using = DecimalSerializerKeep2.class)
    private BigDecimal commodityPrice;

    @ApiModelProperty(value = "型号规格", example = "4L/桶", required = true)
    private String commoditySpec;

    @ApiModelProperty(value = "商品价格全称", example = "￥23.69/kg", required = true)
    private String commodityPriceName;

    /**
     * 取货时间段
     */
    private String pickUpTimeRange;
    /**
     * 是否称重0-不称量,1-称重
     */
    private  Integer isWeight;
    private Long id;

    @ApiModelProperty(value = "商品id", example = "123456", required = true)
    private String commodityIdStr;

    @ApiModelProperty(value = "商品id", example = "123456", required = true)
    public String getCommodityIdStr() {
    	return commodityId != null ? commodityId.toString() : null;
    }

    /**
     * ,
     *             CONCAT('￥', gift.commodity_price, '/', dic.option_name) AS commodityPriceName
     */
    public String getCommodityPriceName(){
        if(null!= commodityPrice && null!= commodityUnit){
            return "￥"+ commodityPrice.setScale(2,BigDecimal.ROUND_UP) +"/"+ commodityUnit;
        }
        return "";
    }

    public Long getCommodityId(){
        if (StringUtils.isNotBlank(commodityIdStr)){
            return Long.valueOf(commodityIdStr);
        }
        return commodityId;
    }

    /**
     * 审核数量存在，则返回审核数量 + 单位，审核数量不存在，返回空
     * @return
     */
    public String getCheckQuantityStr() {
        if(checkQuantity != null) {
            return checkQuantityStr;
        }
        return "";
    }
}

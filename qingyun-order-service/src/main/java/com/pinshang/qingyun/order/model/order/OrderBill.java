package com.pinshang.qingyun.order.model.order;

import java.math.BigDecimal;
import java.util.Date;

import javax.persistence.Entity;
import javax.persistence.Table;

import com.pinshang.qingyun.base.po.BaseIDPO;
import com.pinshang.qingyun.order.enums.XdaPayTypeEnum;
import com.pinshang.qingyun.order.mapper.entry.recharge.XdaPayBillEntry;
import com.pinshang.qingyun.order.model.recharge.XDAPayBill;
import lombok.Data;

/**
 * 订单账款表
 **/
@Entity
@Table(name = "t_order_bill")
@Data
public class OrderBill extends BaseIDPO {
	/** 门店id **/
    private Long storeId;
	/** 门店余额 **/
    private BigDecimal storeBalance;
	/** 订单id **/
    private Long orderId;
	/** 应收账款 **/
    private BigDecimal arAmount;
	/** 应付账款 **/
    private BigDecimal paAmount;
	/** 订单日期 **/
    private Date orderTime;
	/** 备注 **/
    private String billRemark;
	/** 创建者 **/
    private Long crateId;
    /** 插入时间 **/
    private Date createTime;

    /** 内部交易code */
    private String tradeCode;

    /** 交易类型：com.pinshang.qingyun.base.enums.StoreBillTypeEnums */
    private Integer billType;

    public static OrderBill builder(XdaPayBillEntry payBill,BigDecimal storeBalance){
        OrderBill orderBill = new OrderBill();
        orderBill.setCrateId(1L);
        orderBill.setCreateTime(new Date());
        orderBill.setStoreId(payBill.getStoreId());
        orderBill.setStoreBalance(storeBalance);
        orderBill.setPaAmount(payBill.getPayAmount());
        orderBill.setBillRemark("鲜达app充值");
        return orderBill ;
    }

    public static OrderBill initForMiniFroupon(Order order,Long storeId,BigDecimal realAmount,Double collectPrice){
        OrderBill ob = new OrderBill();
        ob.setStoreId(storeId);
        ob.setOrderId(order.getId());
        ob.setOrderTime(order.getOrderTime());
        ob.setArAmount(realAmount);
        ob.setPaAmount(BigDecimal.ZERO);
        ob.setBillRemark("<--mini团购扣款："+order.getOrderCode()+" -->");
        ob.setStoreBalance(BigDecimal.valueOf(collectPrice));
        ob.setCreateTime(new Date());
        ob.setCrateId(-1L);
        return ob;
    }
}
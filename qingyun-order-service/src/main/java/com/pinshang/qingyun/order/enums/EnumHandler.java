package com.pinshang.qingyun.order.enums;

import org.apache.ibatis.type.BaseTypeHandler;
import org.apache.ibatis.type.JdbcType;

import java.sql.CallableStatement;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;

/**
 *
 * <AUTHOR>
 * @Date 2018/4/4 17:23
 */
public class EnumHandler<E extends Enum<?> & BaseEnum> extends BaseTypeHandler<BaseEnum> {
    private Class<E> clazz;

    public EnumHandler(Class<E> enumType){
        if(enumType == null){
            throw new IllegalArgumentException("EnumHandler init fail,Type argument cannot be null");
        }
        this.clazz = enumType;
    }


    @Override
    public void setNonNullParameter(PreparedStatement preparedStatement, int i, BaseEnum baseEnum, JdbcType jdbcType) throws SQLException {
        preparedStatement.setInt(i,baseEnum.convert());
    }

    @Override
    public BaseEnum getNullableResult(ResultSet resultSet, String s) throws SQLException {
        return convert(clazz, resultSet.getInt(s));
    }

    @Override
    public BaseEnum getNullableResult(ResultSet resultSet, int i) throws SQLException {
        return convert(clazz,resultSet.getInt(i));
    }

    @Override
    public BaseEnum getNullableResult(CallableStatement callableStatement, int i) throws SQLException {
        return convert(clazz,callableStatement.getInt(i));
    }


    public static <E extends Enum<?> & BaseEnum> E convert(Class<E> enumClass, int code){
        E[] enumConstants = enumClass.getEnumConstants();
        for (E e : enumConstants) {
            if(e.convert() == code){
                return e;
            }
        }
        return null;
    }
}

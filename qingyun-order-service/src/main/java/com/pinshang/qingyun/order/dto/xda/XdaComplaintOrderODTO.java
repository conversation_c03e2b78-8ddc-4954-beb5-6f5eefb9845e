package com.pinshang.qingyun.order.dto.xda;

import com.pinshang.qingyun.order.enums.ComplaintTypeEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @Version 0.1.0
 * @Date 2021/1/4 11:57
 * @Copyright © 2019-2020 qm
 * @Description -- 类说明 投诉单
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
public class XdaComplaintOrderODTO {

    @ApiModelProperty(value = "投诉客户id，后台内部字典获取，前端忽略", example = "123456", required = true, hidden = true)
    private Long storeId;

    @ApiModelProperty(value = "操作类型，0-取消投诉单下的所有投诉商品，1-取消指定的投诉商品，2-修改指定的投诉商品", example = "false", required = true)
    private Integer operationType;

    @ApiModelProperty(value = "投诉单投诉类型 0-差异投诉，1-退货投诉", example = "1", required = true)
    private ComplaintTypeEnum orderComplaintType;

    @ApiModelProperty(value = "送货日期", example = "2020-12-30", required = true)
    private String deliveryDate;

    @ApiModelProperty(value = "投诉单创建时间", example = "2020-12-30 11:36:59", required = true)
    private String createTime;

    @ApiModelProperty(value = "投诉单时间 2020-12-30 11:36:59 更新/创建", example = "2020-12-30 11:36:59 更新/创建", required = true)
    private String complainTime;

    @ApiModelProperty(value = "投诉单Id", example = "123456code", required = true)
    private String complainId;

    @ApiModelProperty(value = "投诉单code", example = "投诉单code", required = true)
    private String complaintCode;
    @ApiModelProperty(value = "投诉处理状态:1待审核;2已审核;3已取消,通达新增:(4待大仓确认,5待取货,6已完成,7审核不通过,8取货失败)", example = "1", required = true)
    private Integer complaintHandleStatus;

    @ApiModelProperty(value = "投诉商品总金额，后台计算", example = "1236.96", required = true, hidden = true)
    private BigDecimal complaintTotalMoney;

    @ApiModelProperty(value = "投诉原因备注，修改时填写", example = "少货缺货残货", required = true)
    private String complaintReason;

    @ApiModelProperty(value = "投诉单商品明细列表", example = "", required = true)
    private List<XdaComplaintCommodityItemDTO> complaintCommodityList;

    /**
     * 送货时间段
     */
    private String pickUpTimeRange;
}

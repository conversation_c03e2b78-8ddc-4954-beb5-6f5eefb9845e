package com.pinshang.qingyun.order.util;

import org.apache.commons.lang3.StringUtils;

public class EmojiFilter {

    // 未分类的特殊emoji
    private final static char[] uncategoryEmojiChar = {0X00A9, 0X00AE, 0X203C, 0X2049, 0X20E3, 0X2122, 0X2139, 0X2194,
            0X2195, 0X2196, 0X2197, 0X2198, 0X2199, 0X21A9, 0X21AA, 0X231A, 0X231B, 0X2328, 0X23CF, 0X23E9, 0X23EA, 0X23EB,
            0X23EC, 0X23ED, 0X23EE, 0X23EF, 0X23F0, 0X23F1, 0X23F2, 0X23F3, 0X23F8, 0X23F9, 0X23FA, 0X24C2, 0X25AA, 0X25AB,
            0X25B6, 0X25C0, 0X25FB, 0X25FC, 0X25FD, 0X25FE, 0X2600, 0X2601, 0X2602, 0X2603, 0X2604, 0X260E, 0X2611, 0X2614,
            0X2615, 0X2618, 0X261D, 0X2620, 0X2622, 0X2623, 0X2626, 0X262A, 0X262E, 0X262F, 0X2638, 0X2639, 0X263A, 0X2640,
            0X2642, 0X2648, 0X2649, 0X264A, 0X264B, 0X264C, 0X264D, 0X264E, 0X264F, 0X2650, 0X2651, 0X2652, 0X2653, 0X265F,
            0X2660, 0X2663, 0X2665, 0X2666, 0X2668, 0X267B, 0X267E, 0X267F, 0X2692, 0X2693, 0X2694, 0X2695, 0X2696, 0X2697,
            0X2699, 0X269B, 0X269C, 0X26A0, 0X26A1, 0X26AA, 0X26AB, 0X26B0, 0X26B1, 0X26BD, 0X26BE, 0X26C4, 0X26C5, 0X26C8,
            0X26CE, 0X26CF, 0X26D1, 0X26D3, 0X26D4, 0X26E9, 0X26EA, 0X26F0, 0X26F1, 0X26F2, 0X26F3, 0X26F4, 0X26F5, 0X26F7,
            0X26F8, 0X26F9, 0X26FA, 0X26FD, 0X2702, 0X2705, 0X2708, 0X2709, 0X270A, 0X270B, 0X270C, 0X270D, 0X270F, 0X2712,
            0X2714, 0X2716, 0X271D, 0X2721, 0X2728, 0X2733, 0X2734, 0X2744, 0X2747, 0X274C, 0X274E, 0X2753, 0X2754, 0X2755,
            0X2757, 0X2763, 0X2764, 0X2795, 0X2796, 0X2797, 0X27A1, 0X27B0, 0X27BF, 0X2934, 0X2935, 0X2B05, 0X2B06, 0X2B07,
            0X2B1B, 0X2B1C, 0X2B50, 0X2B55, 0X3030, 0X303D, 0X3297, 0X3299, 0XFE0F};


    /**
     * 判断是否是Emoji
     *
     * @param codePoint 比较的单个字符
     * @return true: 非emoji，false：是emoji
     */
    public static boolean isEmojiCharacter(char codePoint) {
        for (int i = 0; i < uncategoryEmojiChar.length; i++) {
            if (uncategoryEmojiChar[i] == codePoint) {
                return false;
            }
        }

        return (codePoint == 0x0) || (codePoint == 0x9) || (codePoint == 0xA) || (codePoint == 0xD)
                || ((codePoint >= 0x20) && (codePoint <= 0xD7FF)) || ((codePoint >= 0xE000) && (codePoint <= 0xFFFD));
    }

    public static boolean containsEmoji(String source) {
        int len = source.length();
        for (int i = 0; i < len; i++) {
            char codePoint = source.charAt(i);
            if (!isEmojiCharacter(codePoint)) { // 如果不能匹配,则该字符是Emoji表情

                return true;
            }
        }
        return false;
    }

}

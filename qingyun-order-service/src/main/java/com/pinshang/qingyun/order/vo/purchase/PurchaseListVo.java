package com.pinshang.qingyun.order.vo.purchase;

import com.pinshang.qingyun.base.page.Pagination;

import java.util.List;

public class PurchaseListVo extends Pagination {

    private Long enterpriseId;

    private String orderDate;

    private Long supplierId;

    private Long purchaserId;

    /** 参数：商品编码、助记码、商品名称*/
    private String  commodityKey;
    /** 一级分类id */
    private Long    cateId1;
    /** 二级分类id */
    private Long    cateId2;
    /** 三级分类id */
    private Long    cateId3;

    private List<Long> commodityIdList;

    private List<Long> commodityIdList2;

    private Integer shopType;
    /**
     * 主条形码
     */
    private String barCode;

    private Long storeTypeId;

    /** 下单开始日期 yyyy-MM-dd */
    private String createOrderBeginDate;
    /** 下单结束日期 yyyy-MM-dd */
    private String createOrderEndDate;

    public String getCreateOrderBeginDate() {
        return createOrderBeginDate;
    }

    public void setCreateOrderBeginDate(String createOrderBeginDate) {
        this.createOrderBeginDate = createOrderBeginDate;
    }

    public String getCreateOrderEndDate() {
        return createOrderEndDate;
    }

    public void setCreateOrderEndDate(String createOrderEndDate) {
        this.createOrderEndDate = createOrderEndDate;
    }

    public Long getStoreTypeId() {
        return storeTypeId;
    }

    public void setStoreTypeId(Long storeTypeId) {
        this.storeTypeId = storeTypeId;
    }

    public Integer getShopType() {
        return shopType;
    }

    public String getBarCode() {
        return barCode;
    }

    public void setBarCode(String barCode) {
        this.barCode = barCode;
    }

    public void setShopType(Integer shopType) {
        this.shopType = shopType;
    }

    public Long getEnterpriseId() {
        return enterpriseId;
    }

    public void setEnterpriseId(Long enterpriseId) {
        this.enterpriseId = enterpriseId;
    }

    public String getOrderDate() {
        return orderDate;
    }

    public void setOrderDate(String orderDate) {
        this.orderDate = orderDate;
    }

    public Long getSupplierId() {
        return supplierId;
    }

    public void setSupplierId(Long supplierId) {
        this.supplierId = supplierId;
    }

    public Long getPurchaserId() {
        return purchaserId;
    }

    public void setPurchaserId(Long purchaserId) {
        this.purchaserId = purchaserId;
    }

    public String getCommodityKey() {
        return commodityKey;
    }

    public void setCommodityKey(String commodityKey) {
        this.commodityKey = commodityKey;
    }

    public Long getCateId1() {
        return cateId1;
    }

    public void setCateId1(Long cateId1) {
        this.cateId1 = cateId1;
    }

    public Long getCateId2() {
        return cateId2;
    }

    public void setCateId2(Long cateId2) {
        this.cateId2 = cateId2;
    }

    public Long getCateId3() {
        return cateId3;
    }

    public void setCateId3(Long cateId3) {
        this.cateId3 = cateId3;
    }

    public List<Long> getCommodityIdList() {
        return commodityIdList;
    }

    public void setCommodityIdList(List<Long> commodityIdList) {
        this.commodityIdList = commodityIdList;
    }

    public List<Long> getCommodityIdList2() {
        return commodityIdList2;
    }

    public void setCommodityIdList2(List<Long> commodityIdList2) {
        this.commodityIdList2 = commodityIdList2;
    }
}
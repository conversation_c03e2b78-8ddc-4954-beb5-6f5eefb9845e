package com.pinshang.qingyun.order.dto.bigShop;

import com.pinshang.qingyun.base.enums.IogisticsModelEnums;
import com.pinshang.qingyun.box.utils.DateUtil;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * @Author: sk
 * @Date: 2024/10/11
 */
@Data
public class DdReceiveDocOrderODTO  {

    private Long docId;

    /** 送货日期 */
    private Date orderTime;

    /** 订单号 **/
    private String orderCode;

    /** 物流配送模式, 0-直送, 1-配送, 2-直通 */
    private Integer logisticsModel;

    /** 订单金额 **/
    private BigDecimal orderAmount;

    public String getOrderTimeStr() {
        if(orderTime != null){
            return DateUtil.getDateFormate(orderTime, "yyyy-MM-dd");
        }

        return "";
    }

    public String getLogisticsModelName() {
        return IogisticsModelEnums.getName(logisticsModel);
    }
}

package com.pinshang.qingyun.order.job;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.SchedulingException;
import org.springframework.scheduling.quartz.SchedulerFactoryBean;
import org.springframework.stereotype.Component;

import javax.servlet.ServletContextEvent;
import javax.servlet.ServletContextListener;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2017/6/19.
 */
//@Configuration
//@Component
public class GeneratePurchaseOrderListener implements ServletContextListener {

//    @Autowired
//    public GeneratePurchaseOrderTasks generatePurchaseOrderTasks;


    @Override
    public void contextInitialized(ServletContextEvent servletContextEvent) {
        try {
//            generatePurchaseOrderTasks.scheduleJobs();
        } catch (SchedulingException e) {
            e.printStackTrace();
        }
    }

    @Bean
    public SchedulerFactoryBean schedulerFactoryBean() {
        SchedulerFactoryBean schedulerFactoryBean = new SchedulerFactoryBean();
        return schedulerFactoryBean;
    }


    @Override
    public void contextDestroyed(ServletContextEvent servletContextEvent) {

    }
}

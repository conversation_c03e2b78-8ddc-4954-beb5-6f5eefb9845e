package com.pinshang.qingyun.order.model.bcountry;/**
 * @Author: sk
 * @Date: 2025/7/16
 */

import com.pinshang.qingyun.base.po.BasePO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Table;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2025年07月16日 下午1:41
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Table(name="t_plan_commodity")
public class PlanCommodity extends BasePO {

    private Long commodityId;

    /** 同步状态 1=已同步  0=未同步 */
    private Integer syncStatus;

    /** 同步时间 */
    private Date syncTime;
}

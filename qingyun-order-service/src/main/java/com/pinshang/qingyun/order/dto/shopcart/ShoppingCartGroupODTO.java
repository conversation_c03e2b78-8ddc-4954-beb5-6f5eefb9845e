package com.pinshang.qingyun.order.dto.shopcart;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;


@Data
@ApiModel
@NoArgsConstructor
@AllArgsConstructor
public class ShoppingCartGroupODTO {

    @ApiModelProperty("购物车组内商品列表")
    private List<ShoppingCartCommodityODTO> commodities;
    @ApiModelProperty("冷冻商品组告警提示")
    private String freezeWarnTips;
}

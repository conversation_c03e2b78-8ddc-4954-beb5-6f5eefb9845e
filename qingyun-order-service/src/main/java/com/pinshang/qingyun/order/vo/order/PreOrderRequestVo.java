package com.pinshang.qingyun.order.vo.order;

import java.util.List;

public class PreOrderRequestVo {
	/** 企业id **/
	private Long enterpriseId;
	/**商铺id **/
    private String storeId;
	/**订单时间 **/
    private String orderTime;
    /** 打印类型(1：本地,2：送货员,3：不打印) */
    private Integer printType = 3;
    /**打印份数*/
    private Integer printNum = 1;
	/** 0:普通订单 1：补货订单 **/
	private int modeType = 0 ;
	//创建人
	private Long userId;
	
	private List<PreSaveOrderItemVo> itemsList;
	//供应商id
	private Long supplierId;
	//预订单id
	private Long preOrderId;
	
	private Long shopId;
    /** 收货人id */
    private Long receiverId;
    
    //对应的采购订单id
    private Long purchaseOrderId;
    private String purchaseCode;

	public Long getEnterpriseId() {
		return enterpriseId;
	}
	public void setEnterpriseId(Long enterpriseId) {
		this.enterpriseId = enterpriseId;
	}
	public String getStoreId() {
		return storeId;
	}
	public void setStoreId(String storeId) {
		this.storeId = storeId;
	}
	public String getOrderTime() {
		return orderTime;
	}
	public void setOrderTime(String orderTime) {
		this.orderTime = orderTime;
	}
	public Integer getPrintType() {
		return printType;
	}
	public void setPrintType(Integer printType) {
		this.printType = printType;
	}
	public Integer getPrintNum() {
		return printNum;
	}
	public void setPrintNum(Integer printNum) {
		this.printNum = printNum;
	}
	public int getModeType() {
		return modeType;
	}
	public void setModeType(int modeType) {
		this.modeType = modeType;
	}
	public Long getUserId() {
		return userId;
	}
	public void setUserId(Long userId) {
		this.userId = userId;
	}
	public List<PreSaveOrderItemVo> getItemsList() {
		return itemsList;
	}
	public void setItemsList(List<PreSaveOrderItemVo> itemsList) {
		this.itemsList = itemsList;
	}
	public Long getSupplierId() {
		return supplierId;
	}
	public void setSupplierId(Long supplierId) {
		this.supplierId = supplierId;
	}
	public Long getPreOrderId() {
		return preOrderId;
	}
	public void setPreOrderId(Long preOrderId) {
		this.preOrderId = preOrderId;
	}
	public Long getShopId() {
		return shopId;
	}
	public void setShopId(Long shopId) {
		this.shopId = shopId;
	}
	public Long getReceiverId() {
		return receiverId;
	}
	public void setReceiverId(Long receiverId) {
		this.receiverId = receiverId;
	}
	public Long getPurchaseOrderId() {
		return purchaseOrderId;
	}
	public void setPurchaseOrderId(Long purchaseOrderId) {
		this.purchaseOrderId = purchaseOrderId;
	}

	public String getPurchaseCode() {
		return purchaseCode;
	}

	public void setPurchaseCode(String purchaseCode) {
		this.purchaseCode = purchaseCode;
	}
}

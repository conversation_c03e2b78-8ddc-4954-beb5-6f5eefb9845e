package com.pinshang.qingyun.order.controller.xda.v4;

import com.pinshang.qingyun.base.api.XdaTokenInfo;
import com.pinshang.qingyun.base.configure.expand.QYAssert;
import com.pinshang.qingyun.base.constant.XdaAppVersionConstant;
import com.pinshang.qingyun.base.enums.YesOrNoEnums;
import com.pinshang.qingyun.base.util.FastThreadLocalUtil;
import com.pinshang.qingyun.box.utils.DateUtil;
import com.pinshang.qingyun.marketing.service.MtPromotionClient;
import com.pinshang.qingyun.order.aspect.RequestBodyAndHeader;
import com.pinshang.qingyun.order.config.LockConstants;
import com.pinshang.qingyun.order.constant.PreOrderTipConstant;
import com.pinshang.qingyun.order.dto.shopcart.v4.*;
import com.pinshang.qingyun.order.dto.xda.v4.TdaDeliveryTimeRangeODTO;
import com.pinshang.qingyun.order.mapper.*;
import com.pinshang.qingyun.order.model.order.XdaPreOrder;
import com.pinshang.qingyun.order.service.StoreService;
import com.pinshang.qingyun.order.service.XfOrderService;
import com.pinshang.qingyun.order.service.xda.util.XdaShoppingCartToolUtil;
import com.pinshang.qingyun.order.service.xda.v4.*;
import com.pinshang.qingyun.order.util.ThreadLocalUtils;
import com.pinshang.qingyun.xda.product.service.XdaCommodityFrontClient;
import com.pinshang.qingyun.xda.product.service.XdaShoppingCartController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @Date 2024/3/4 9:52
 */

@Slf4j
@RestController
@RequestMapping("/xda/shoppingCartV4")
@Api(value = "鲜达APP购物车相关接口V4",tags ="XdaShoppingCartV4Controller",description ="鲜达APP购物车相关接口V4" )
public class XdaShoppingCartV4Controller {
    @Autowired
    private XfOrderService xfOrderService;

    @Autowired
    private RedissonClient redissonClient;

    @Autowired
    private XdaShoppingCartToolUtil xdaShoppingCartToolUtil;

    @Autowired
    private XdaShoppingCartV4Service xdaShoppingCartV4Service;
    @Autowired
    private XdaCommodityFrontClient xdaCommodityFrontClient;
    @Autowired
    private XdaShoppingCartController xdaShoppingCartController;
    @Autowired
    private OrderMapper orderMapper;
    @Autowired
    private OrderListMapper orderListMapper;

    @Autowired
    private XdaShoppingCartMapper xdaShoppingCartMapper;

    @Autowired
    private StoreService storeService;
    @Autowired
    private StoreDurationMapper storeDurationMapper;
    @Autowired
    private CommodityFreezeGroupMapper commodityFreezeGroupMapper;
    @Autowired
    private MtPromotionClient mtPromotionClient;

    @Autowired
    private ToBService toBService;

    @Value("${pinshang.xda.isThTips}")
    private String isThTips;
    @Autowired
    private XdaPreOrderService xdaPreOrderService;
    @Autowired
    private TdaOrderService tdaOrderService;

    @PostMapping("/addV4")
    @ApiOperation(value = "购物车--加")
    public ShoppingCartV4ODTO addShopCartV4(@RequestBodyAndHeader ShoppingCartAddV4IDTO shoppingCartAddV4IDTO) {
        try {
            XdaTokenInfo xdaTokenInfo = FastThreadLocalUtil.getXDA();
            Long storeId = xdaTokenInfo.getStoreId();
            QYAssert.isTrue(!xdaTokenInfo.getIsTouristStore(), "游客无法下单。");
            QYAssert.isTrue(storeId != null, "未能获取到用户信息,请重新登录!");
            ThreadLocalUtils.setLogisticsCenterId(shoppingCartAddV4IDTO.getLogisticscenterid());
            shoppingCartAddV4IDTO.setStoreId(storeId);
            xfOrderCountdown(storeId);
            String lockKey = LockConstants.generateShoppingCartAddLockKey(storeId);
            RLock lock = redissonClient.getLock(lockKey);
            NotShoppingCartPageV4ODTO notShoppingCartPageV4ODTO = null;
            BuildXdaShoppingCartV4 buildXdaShoppingCartV4 = null;
            if (lock.tryLock()) {
                try {
                    buildXdaShoppingCartV4 = new BuildXdaShoppingCartV4(storeId, shoppingCartAddV4IDTO.getOrderTime(), toBService, xdaShoppingCartMapper, orderMapper, orderListMapper, xdaCommodityFrontClient, xdaShoppingCartController, storeService, storeDurationMapper, commodityFreezeGroupMapper, mtPromotionClient, shoppingCartAddV4IDTO.getDeliverytimerange(), shoppingCartAddV4IDTO.getDeliverybatch(), redissonClient, getCouponId(xdaTokenInfo.getAppVersion()));
                    notShoppingCartPageV4ODTO = xdaShoppingCartV4Service.addShopCart(shoppingCartAddV4IDTO);
                } finally {
                    lock.unlock();
                }
            } else {
                QYAssert.isFalse("系统繁忙,请勿频繁操作!");
            }
            if (YesOrNoEnums.NO.equals(shoppingCartAddV4IDTO.getShopCartPage())) {
                ShoppingCartV4ODTO shoppingCartV4ODTO = new ShoppingCartV4ODTO();
                shoppingCartV4ODTO.setVarietySum(buildXdaShoppingCartV4.getValidCommoditySum());
                // 内部设置 普通商品组金额
                if(Objects.equals(isThTips, "true")) {
                    buildXdaShoppingCartV4.setThCommodityTips(buildXdaShoppingCartV4, shoppingCartV4ODTO, shoppingCartAddV4IDTO.getShopCartPage());
                }
                if(null != notShoppingCartPageV4ODTO.getStockWarningTips()){
                    shoppingCartV4ODTO.setStockWarningTips(notShoppingCartPageV4ODTO.getStockWarningTips());
                }
                if(StringUtils.isNotEmpty(notShoppingCartPageV4ODTO.getSpecialLimitWarningTips())){
                    shoppingCartV4ODTO.setSpecialLimitWarningTips(notShoppingCartPageV4ODTO.getSpecialLimitWarningTips());
                }
                shoppingCartV4ODTO.setNotShoppingCartPageODTO(notShoppingCartPageV4ODTO);
                return shoppingCartV4ODTO;
            }
            return shopCartList(buildXdaShoppingCartV4, shoppingCartAddV4IDTO, notShoppingCartPageV4ODTO.getStockWarningTips(),notShoppingCartPageV4ODTO.getSpecialLimitWarningTips());
        } finally {
            ThreadLocalUtils.remove();
        }
    }

    @PostMapping("/minusV4")
    @ApiOperation(value = "购物车--减")
    public ShoppingCartV4ODTO minusShopCartV4(@RequestBodyAndHeader ShoppingCartMinusV4IDTO shoppingCartAddV4IDTO){
        try {
            XdaTokenInfo xdaTokenInfo = FastThreadLocalUtil.getXDA();
            Long storeId = xdaTokenInfo.getStoreId();
            QYAssert.isTrue(!xdaTokenInfo.getIsTouristStore(), "游客无法下单。");
            QYAssert.isTrue(storeId != null, "未能获取到用户信息,请重新登录!");
            ThreadLocalUtils.setLogisticsCenterId(shoppingCartAddV4IDTO.getLogisticscenterid());
            shoppingCartAddV4IDTO.setStoreId(storeId);
            xfOrderCountdown(storeId);
            String lockKey = LockConstants.generateShoppingCartAddLockKey(storeId);
            RLock lock = redissonClient.getLock(lockKey);
            NotShoppingCartPageV4ODTO notShoppingCartPageV3ODTO = null;
            BuildXdaShoppingCartV4 buildXdaShoppingCartV4 =null;
            if(lock.tryLock()){
                try {
                    buildXdaShoppingCartV4 = new BuildXdaShoppingCartV4(storeId, shoppingCartAddV4IDTO.getOrderTime(),toBService, xdaShoppingCartMapper, orderMapper, orderListMapper, xdaCommodityFrontClient, xdaShoppingCartController,storeService, storeDurationMapper,commodityFreezeGroupMapper,mtPromotionClient,shoppingCartAddV4IDTO.getDeliverytimerange(),shoppingCartAddV4IDTO.getDeliverybatch(),redissonClient,getCouponId(xdaTokenInfo.getAppVersion()));
                    notShoppingCartPageV3ODTO = xdaShoppingCartV4Service.minus(shoppingCartAddV4IDTO);
                } finally {
                    lock.unlock();
                }
            }else {
                QYAssert.isFalse("系统繁忙,请勿频繁操作!");
            }
            if (YesOrNoEnums.NO.equals(shoppingCartAddV4IDTO.getShopCartPage())){
                ShoppingCartV4ODTO shoppingCartV4ODTO = new ShoppingCartV4ODTO();
                shoppingCartV4ODTO.setVarietySum(buildXdaShoppingCartV4.getValidCommoditySum());
                // 内部设置 普通商品组金额
                if(Objects.equals(isThTips, "true")) {
                    buildXdaShoppingCartV4.setThCommodityTips(buildXdaShoppingCartV4, shoppingCartV4ODTO, shoppingCartAddV4IDTO.getShopCartPage());
                }
                if(null != notShoppingCartPageV3ODTO.getStockWarningTips()){
                    shoppingCartV4ODTO.setStockWarningTips(notShoppingCartPageV3ODTO.getStockWarningTips());
                }
                if(StringUtils.isNotEmpty(notShoppingCartPageV3ODTO.getSpecialLimitWarningTips())){
                    shoppingCartV4ODTO.setSpecialLimitWarningTips(notShoppingCartPageV3ODTO.getSpecialLimitWarningTips());
                }
                shoppingCartV4ODTO.setNotShoppingCartPageODTO(notShoppingCartPageV3ODTO);
                return shoppingCartV4ODTO;
            }
            return shopCartList(buildXdaShoppingCartV4, shoppingCartAddV4IDTO, notShoppingCartPageV3ODTO.getStockWarningTips(),notShoppingCartPageV3ODTO.getSpecialLimitWarningTips());
        } finally {
            ThreadLocalUtils.remove();
        }
    }

    @PostMapping("/setNumV4")
    @ApiOperation(value = "购物车--设置数量")
    public ShoppingCartV4ODTO setNumShopCartV4(@RequestBodyAndHeader ShoppingCartSetNumV4IDTO shoppingCartSetNumV4IDTO){
        try {
            XdaTokenInfo xdaTokenInfo = FastThreadLocalUtil.getXDA();
            Long storeId = xdaTokenInfo.getStoreId();
            QYAssert.isTrue(!xdaTokenInfo.getIsTouristStore(), "游客无法下单。");
            QYAssert.isTrue(storeId != null, "未能获取到用户信息,请重新登录!");
            ThreadLocalUtils.setLogisticsCenterId(shoppingCartSetNumV4IDTO.getLogisticscenterid());
            shoppingCartSetNumV4IDTO.setStoreId(storeId);
            xfOrderCountdown(storeId);
            BuildXdaShoppingCartV4 buildXdaShoppingCartV4 = new BuildXdaShoppingCartV4(storeId, shoppingCartSetNumV4IDTO.getOrderTime(),toBService, xdaShoppingCartMapper, orderMapper, orderListMapper, xdaCommodityFrontClient,xdaShoppingCartController, storeService, storeDurationMapper,commodityFreezeGroupMapper,mtPromotionClient,shoppingCartSetNumV4IDTO.getDeliverytimerange(),shoppingCartSetNumV4IDTO.getDeliverybatch(),redissonClient,getCouponId(xdaTokenInfo.getAppVersion()));
            NotShoppingCartPageV4ODTO notShoppingCartPageV4ODTO = xdaShoppingCartV4Service.setNum(shoppingCartSetNumV4IDTO);
            if (YesOrNoEnums.NO.equals(shoppingCartSetNumV4IDTO.getShopCartPage())){
                ShoppingCartV4ODTO shoppingCartV4ODTO = new ShoppingCartV4ODTO();
                shoppingCartV4ODTO.setVarietySum(buildXdaShoppingCartV4.getValidCommoditySum());
                // 内部设置 普通商品组金额
                if(Objects.equals(isThTips, "true")) {
                    buildXdaShoppingCartV4.setThCommodityTips(buildXdaShoppingCartV4, shoppingCartV4ODTO, shoppingCartSetNumV4IDTO.getShopCartPage());
                }
                if(null != notShoppingCartPageV4ODTO.getStockWarningTips()){
                    shoppingCartV4ODTO.setStockWarningTips(notShoppingCartPageV4ODTO.getStockWarningTips());
                }
                if(StringUtils.isNotEmpty(notShoppingCartPageV4ODTO.getSpecialLimitWarningTips())){
                    shoppingCartV4ODTO.setSpecialLimitWarningTips(notShoppingCartPageV4ODTO.getSpecialLimitWarningTips());
                }
                shoppingCartV4ODTO.setNotShoppingCartPageODTO(notShoppingCartPageV4ODTO);
                return shoppingCartV4ODTO;
            }
            return shopCartList(buildXdaShoppingCartV4,shoppingCartSetNumV4IDTO,notShoppingCartPageV4ODTO.getStockWarningTips(),notShoppingCartPageV4ODTO.getSpecialLimitWarningTips());
        } finally {
            ThreadLocalUtils.remove();
        }
    }


    @GetMapping("/refreshV4")
    @ApiOperation(value = "购物车--刷新")
    public ShoppingCartV4ODTO refreshV4(@RequestParam(value = "orderDate", required = false) String orderDate,
                                        @RequestHeader(value = "deliveryTimeRange", required = false) String deliveryTimeRange,
                                        @RequestHeader(value = "deliveryBatch", required = false) Integer deliveryBatch,
                                        @RequestHeader(value = "logisticsCenterId", required = false) Long logisticsCenterId) {
        try {
            XdaTokenInfo xdaTokenInfo = FastThreadLocalUtil.getXDA();
            Long storeId = xdaTokenInfo.getStoreId();
            QYAssert.isTrue(!xdaTokenInfo.getIsTouristStore(), "游客无法下单。");
            QYAssert.isTrue(storeId != null, "未能获取到用户信息,请重新登录!");
            QYAssert.isTrue(orderDate != null, "送货日期不能为空!!");
            ThreadLocalUtils.setLogisticsCenterId(logisticsCenterId);
            Date date = DateUtil.parseDate(orderDate, "yyyy-MM-dd");
            if (date == null) {
                log.warn("日期格式错误, {}", orderDate);
                QYAssert.isFalse("送货日期不能为空!!!");
            }

            BuildXdaShoppingCartV4 buildXdaShoppingCartV4 = new BuildXdaShoppingCartV4(storeId, DateUtil.parseDate(orderDate, "yyyy-MM-dd"), toBService, xdaShoppingCartMapper, orderMapper, orderListMapper, xdaCommodityFrontClient, xdaShoppingCartController, storeService, storeDurationMapper, commodityFreezeGroupMapper, mtPromotionClient, deliveryTimeRange, deliveryBatch, redissonClient, getCouponId(xdaTokenInfo.getAppVersion()));
            ShoppingCartV4IDTO shoppingCartV4IDTO = ShoppingCartV4IDTO.builder(storeId, DateUtil.parseDate(orderDate, "yyyy-MM-dd"), YesOrNoEnums.YES);
            shoppingCartV4IDTO.setDeliverytimerange(deliveryTimeRange);
            shoppingCartV4IDTO.setDeliverybatch(deliveryBatch);
            return shopCartList(buildXdaShoppingCartV4, shoppingCartV4IDTO, null, null);
        } finally {
            ThreadLocalUtils.remove();
        }
    }

    @PostMapping("/clearV4")
    @ApiOperation(value = "购物车--清空")
    public ShoppingCartV4ODTO clearV4(@RequestBodyAndHeader ShoppingCartClearV4IDTO shoppingCartClearV4IDTO){
        try {
            XdaTokenInfo xdaTokenInfo = FastThreadLocalUtil.getXDA();
            Long storeId = xdaTokenInfo.getStoreId();
            QYAssert.isTrue(!xdaTokenInfo.getIsTouristStore(), "游客无法下单。");
            QYAssert.isTrue(storeId != null, "未能获取到用户信息,请重新登录!");
            ThreadLocalUtils.setLogisticsCenterId(shoppingCartClearV4IDTO.getLogisticscenterid());
            xfOrderCountdown(storeId);
            xdaShoppingCartV4Service.clearV4(storeId, shoppingCartClearV4IDTO);
            BuildXdaShoppingCartV4 buildXdaShoppingCartV4 = new BuildXdaShoppingCartV4(storeId, shoppingCartClearV4IDTO.getOrderTime(),toBService, xdaShoppingCartMapper, orderMapper, orderListMapper, xdaCommodityFrontClient,xdaShoppingCartController, storeService, storeDurationMapper,commodityFreezeGroupMapper,mtPromotionClient,shoppingCartClearV4IDTO.getDeliverytimerange(),shoppingCartClearV4IDTO.getDeliverybatch(),redissonClient,getCouponId(xdaTokenInfo.getAppVersion()));
            ShoppingCartV4IDTO shoppingCartV4IDTO = ShoppingCartV4IDTO.builder(storeId,shoppingCartClearV4IDTO.getOrderTime(),YesOrNoEnums.YES);
            shoppingCartV4IDTO.setShopCartPage(YesOrNoEnums.YES);
            shoppingCartV4IDTO.setDeliverytimerange(shoppingCartClearV4IDTO.getDeliverytimerange());
            shoppingCartV4IDTO.setDeliverybatch(shoppingCartClearV4IDTO.getDeliverybatch());
            return shopCartList(buildXdaShoppingCartV4,shoppingCartV4IDTO,null,null);
        } finally {
            ThreadLocalUtils.remove();
        }
    }

    @GetMapping("/getCategoryV4")
        @ApiOperation(value = "购物车--品类数量")
    public Integer getCategoryNumV4(@RequestParam(value = "orderDate",required = false) String orderDate, @RequestParam(value = "storeId",required = false) Long storeId,@RequestHeader(value = "deliveryTimeRange",required = false) String deliveryTimeRange,@RequestHeader(value = "deliveryBatch",required = false) Integer deliveryBatch){
        BuildXdaShoppingCartV4 buildXdaShoppingCartV4 = new BuildXdaShoppingCartV4(storeId, DateUtil.parseDate(orderDate, "yyyy-MM-dd"),toBService,  xdaShoppingCartMapper, orderMapper, orderListMapper, xdaCommodityFrontClient,xdaShoppingCartController, storeService, storeDurationMapper,commodityFreezeGroupMapper,mtPromotionClient,deliveryTimeRange,deliveryBatch,redissonClient,getCouponId(FastThreadLocalUtil.getXDA().getAppVersion()));
        return buildXdaShoppingCartV4.getValidCommoditySum();
    }

    @GetMapping("/getNormalGroupAmountV4")
    @ApiOperation(value = "购物车--普通商品组优惠后的价格")
    public BigDecimal getNormalGroupAmountV4(@RequestParam(value = "orderDate", required = false) String orderDate,
                                             @RequestParam(value = "storeId", required = false) Long storeId,
                                             @RequestHeader(value = "deliveryTimeRange", required = false) String deliveryTimeRange,
                                             @RequestHeader(value = "deliveryBatch", required = false) Integer deliveryBatch,
                                             @RequestHeader(value = "logisticsCenterId", required = false) Long logisticsCenterId) {
        try {
            ThreadLocalUtils.setLogisticsCenterId(logisticsCenterId);
            BuildXdaShoppingCartV4 buildXdaShoppingCartV4 = new BuildXdaShoppingCartV4(storeId, DateUtil.parseDate(orderDate, "yyyy-MM-dd"), toBService, xdaShoppingCartMapper, orderMapper, orderListMapper, xdaCommodityFrontClient, xdaShoppingCartController, storeService, storeDurationMapper, commodityFreezeGroupMapper, mtPromotionClient, deliveryTimeRange, deliveryBatch, redissonClient, getCouponId(FastThreadLocalUtil.getXDA().getAppVersion()));
            ShoppingCartV4ODTO shoppingCartV4ODTO = buildXdaShoppingCartV4.shopCartList();
            return shoppingCartV4ODTO.getNormalGroupAmount();
        } finally {
            ThreadLocalUtils.remove();
        }
    }

    @GetMapping("/clear/invalidV4")
    @ApiOperation(value = "购物车--清空失效商品")
    public ShoppingCartV4ODTO clearInvalidCommodityV4(@RequestParam(value = "orderDate", required = false) String orderDate,
                                                      @RequestHeader(value = "deliveryTimeRange", required = false) String deliveryTimeRange,
                                                      @RequestHeader(value = "deliveryBatch", required = false) Integer deliveryBatch,
                                                      @RequestHeader(value = "logisticsCenterId", required = false) Long logisticsCenterId) {
        try {
            XdaTokenInfo xdaTokenInfo = FastThreadLocalUtil.getXDA();
            Long storeId = xdaTokenInfo.getStoreId();
            BuildXdaShoppingCartV4 buildXdaShoppingCartV4 = new BuildXdaShoppingCartV4(storeId, DateUtil.parseDate(orderDate, "yyyy-MM-dd"), toBService, xdaShoppingCartMapper, orderMapper, orderListMapper, xdaCommodityFrontClient, xdaShoppingCartController, storeService, storeDurationMapper, commodityFreezeGroupMapper, mtPromotionClient, deliveryTimeRange, deliveryBatch, redissonClient, getCouponId(xdaTokenInfo.getAppVersion()));
            ThreadLocalUtils.setLogisticsCenterId(logisticsCenterId);
            return xdaShoppingCartV4Service.clearInvalidCommodity(buildXdaShoppingCartV4);
        } finally {
            ThreadLocalUtils.remove();
        }
    }
    /**
     * 校验购物车是否调用第三方支付
     * @param storeId
     */
    public void xfOrderCountdown(Long storeId){
        /*long xfOrderCountdown = xfOrderService.getXfOrderCountdown(storeId);
        if(xfOrderCountdown != 0){
            QYAssert.isFalse("正在第三方支付请稍候!");
        }*/
        XdaPreOrder xdaPreOrder = xdaPreOrderService.queryNoPayXdaPreOrder(storeId);
        if(xdaPreOrder != null){
            Boolean oldVersion = StringUtils.isNotBlank(FastThreadLocalUtil.getXDA().getAppVersion()) && FastThreadLocalUtil.getXDA().getAppVersion().compareToIgnoreCase(XdaAppVersionConstant.TDA_VERSION) < 0;
            if(oldVersion){
                QYAssert.isFalse(PreOrderTipConstant.OLD_PRE_ORDER_TIPS);
            }else {
                QYAssert.isFalse(PreOrderTipConstant.NEW_PRE_ORDER_TIPS);
            }
        }
    }

    /**
     * 刷新购物车
     * @param buildXdaShoppingCartV4
     * @param shoppingCartAddV4IDTO
     * @param stockWarningTips
     * @return
     */
    public ShoppingCartV4ODTO shopCartList(BuildXdaShoppingCartV4 buildXdaShoppingCartV4, ShoppingCartV4IDTO shoppingCartAddV4IDTO, String stockWarningTips, String specialLimitkWarningTips){
        ShoppingCartV4ODTO shoppingCartV4ODTO = buildXdaShoppingCartV4.shopCartList();
        /*组装返回信息 库存、限量提示*/
        if(null != stockWarningTips){
            shoppingCartV4ODTO.setStockWarningTips(stockWarningTips);
            xdaShoppingCartToolUtil.setStockWarningTipsV4(shoppingCartAddV4IDTO.getCommodityType(),shoppingCartAddV4IDTO.getCommodityId(),stockWarningTips,shoppingCartV4ODTO);
        }
        if (StringUtils.isNotBlank(specialLimitkWarningTips)) {
            shoppingCartV4ODTO.setSpecialLimitWarningTips(specialLimitkWarningTips);
            if (shoppingCartAddV4IDTO.getCommodityType().equals(1)) {
                List<ShoppingCartCommodityV4ODTO> commodities = shoppingCartV4ODTO.getNormalGroup().getCommodities();
                commodities.forEach(item -> {
                    if (item.getCommodityId().equals(shoppingCartAddV4IDTO.getCommodityId())) {
                        if (StringUtils.isBlank(item.getSpecialLimitWarningTips())) {
                            item.setSpecialLimitWarningTips(specialLimitkWarningTips);
                        } else {
                            shoppingCartV4ODTO.setSpecialLimitWarningTips(item.getSpecialLimitWarningTips());
                        }
                    }
                });
            }
        }

        buildXdaShoppingCartV4.setThCommodityTips(buildXdaShoppingCartV4,shoppingCartV4ODTO,shoppingCartAddV4IDTO.getShopCartPage());

        long xfOrderCountdown = xfOrderService.getXdaPreOrderCountdown(shoppingCartAddV4IDTO.getStoreId());
        if(xfOrderCountdown != 0){
            shoppingCartV4ODTO.setCanSettlement(Boolean.FALSE);
            shoppingCartV4ODTO.setXfOrderCountdown(xfOrderCountdown);
        }

        TdaDeliveryTimeRangeODTO timeRangeODTO = tdaOrderService.checkDeliveryTimeRange(shoppingCartAddV4IDTO.getStoreId(), shoppingCartAddV4IDTO.getDeliverytimerange(), shoppingCartAddV4IDTO.getDeliverybatch(), true);
        if(timeRangeODTO != null){
            shoppingCartV4ODTO.setDeliveryTimeRange(timeRangeODTO.getDeliveryTimeRange());
            shoppingCartV4ODTO.setDeliveryBatch(timeRangeODTO.getDeliveryBatch());
            shoppingCartV4ODTO.setTopTips("客户订货时间段："+ timeRangeODTO.getStoreBeginTime() + "~"+ timeRangeODTO.getStoreEndTime());
        }

        // 返回送货日期是否当天
        shoppingCartV4ODTO.setOrderTimeIsToday(DateUtil.get4yMd(new Date()).equals(DateUtil.get4yMd(shoppingCartAddV4IDTO.getOrderTime())));
        return shoppingCartV4ODTO;
    }

    private Long getCouponId(String appVersion) {
        Long couponId;
        // 判断版本号，为空或者小于 165则为老版本
        if (StringUtils.isEmpty(appVersion) || appVersion.compareToIgnoreCase(XdaAppVersionConstant.XDA_VERSION_165) < 0) {
            couponId = -2L;
        } else {
            couponId = null;
        }
        return couponId;
    }
}

package com.pinshang.qingyun.order.vo.order;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class OrderSyncToDcRespVo {
    @ApiModelProperty("订单Id")
    private Long id;

    @ApiModelProperty("订单编号")
    private String orderCode;

    @ApiModelProperty("订单状态")
    private Integer orderStatus;

    @ApiModelProperty("订单状态")
    private Integer processStatus;

}

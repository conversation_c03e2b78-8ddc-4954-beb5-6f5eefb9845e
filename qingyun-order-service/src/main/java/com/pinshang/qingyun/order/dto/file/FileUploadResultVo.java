package com.pinshang.qingyun.order.dto.file;

import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
public class FileUploadResultVo {
	// 保存文件的相对地址：/XDA_CHECK_REPORT_FILE/2021/07/15/7775527-20210715-5584.pdf
	private String relativeUrl;
	// 保存文件的绝对地址(磁盘全路径地址)：/var/www/static/XDA_CHECK_REPORT_FILE/2021/07/15/7775527-20210715-5584.pdf
	private String baseUrl;
	// 上传后可访问的地址：http://192.168.0.207:8088/XDA_CHECK_REPORT_FILE/2021/07/15/7775527-20210715-5584.pdf
	private String visitUrl;
	// 原文件名 ：7775527-20210715-5584.pdf
	private String oldFileName;
	// 新文件名：7775527-20210715-5584.pdf
	private String newFileName;
	// 文件类型(文件后缀名)：pdf
	private String fileType;
	// 图片或文件大小(byte)
    private Long picSpace;
}

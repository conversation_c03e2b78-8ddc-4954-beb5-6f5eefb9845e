package com.pinshang.qingyun.order.dto;
import lombok.Data;
import javax.validation.constraints.NotNull;
import java.util.Date;
import java.util.List;
@Data
public class OrderMockDTO {
    /**
     * curl --location --request POST 'http://localhost:9007/subOrder/createMockOrder' \
     * --header 'Content-Type: application/json' \
     * --data-raw '{
     *     "storeId": 999872035591512403,
     *     "businessType": 13,
     *     "stallId": 1,
     *     "orderTime": "2024-10-15",
     *     "presaleStatus": 0,
     *     "items": [
     *         {
     *             "commodityId": 56095697583367000,
     *             "orderQuantity": 3,
     *             "orderNumber": 2
     *         }
     *     ]
     * }'
     */
    @NotNull(message = "客户id不能为空")
    private Long storeId;
    /**
     * 订单时间
     **/
    @NotNull(message = "送货日期不能为空")
    private Date orderTime;
    /**
     * 创建者
     **/
    private Long createId;
    /**
     * 是否预售:0=否，1=是
     */
    @NotNull(message = "是否预售不能为空")
    private Integer presaleStatus;
    private Integer businessType;
    private Long stallId;
    private List<OrderItemMockDTO> items;

}

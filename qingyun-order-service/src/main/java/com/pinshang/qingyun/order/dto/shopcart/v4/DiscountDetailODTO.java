package com.pinshang.qingyun.order.dto.shopcart.v4;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.pinshang.qingyun.base.configure.codec.DecimalSerializerKeep2;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Tolerate;

import java.math.BigDecimal;

/**
 * 优惠明细
 *
 * <AUTHOR>
 */
@Data
@Builder
public class DiscountDetailODTO {
    @ApiModelProperty("原价合计")
    @JsonSerialize(using = DecimalSerializerKeep2.class)
    private BigDecimal originalAmount;

    @ApiModelProperty("活动立减")
    @JsonSerialize(using = DecimalSerializerKeep2.class)
    private BigDecimal activityDiscount;

    @ApiModelProperty("券优惠合计")
    @JsonSerialize(using = DecimalSerializerKeep2.class)
    private BigDecimal couponAmount;

    @ApiModelProperty("总共立减")
    @JsonSerialize(using = DecimalSerializerKeep2.class)
    private BigDecimal discountTotal;

    @ApiModelProperty("优惠后合计")
    @JsonSerialize(using = DecimalSerializerKeep2.class)
    private BigDecimal summation;

    @Tolerate
    public DiscountDetailODTO() {
    }
}

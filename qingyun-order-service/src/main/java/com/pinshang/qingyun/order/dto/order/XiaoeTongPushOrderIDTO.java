package com.pinshang.qingyun.order.dto.order;/**
 * @Author: sk
 * @Date: 2025/7/9
 */

import com.pinshang.qingyun.order.dto.MsShopOrderSettingBaseDTO;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2025年07月09日 上午9:25
 */
@Data
public class XiaoeTongPushOrderIDTO extends MsShopOrderSettingBaseDTO {

    // 门店id
    private Long shopId;
    // 订单编码
    private Long orderId;
    // 订单时间 (yyyy-MM-dd)
    private String orderTime;
    // 商品
    private Long commodityId;
    // 订单数量
    private BigDecimal orderQuantity;
    // 门店向总部订货价
    private BigDecimal price;
    // 小鹅通订单下单时间(yyyy-MM-dd HH:mm:ss)
    private String orderCreateTime;
    // 是否下单成功 1=是  0=否
    private Integer orderStauts;



    // 实际下单的数量
    private BigDecimal quantity = BigDecimal.ZERO;

    private Integer presaleStatus; // 是否预售  0 否   1 是
    private Integer stockType; // 库存依据  1=依据大仓, 2=不限量订货, 3=限量供应

    /**
     * 原材料比例
     */
    private Integer sourceRatio;
    /**
     * 转成品比例
     */
    private Integer targetRatio;

    /**
     * 组合商品转换的最小单位商品ID
     */
    private Long targetCommodityId;

    /**
     * 组合商品转换状态：0=无转换，1=有转换
     */
    private Integer convertStatus;

    /**
     * 组合商品目标数量 : 向上取整 ( quantity / source_ratio * target_ratio)
     */
    private BigDecimal targetQuantity;
}

package com.pinshang.qingyun.order.job;

import com.pinshang.qingyun.base.spring.SpringBeanFinder;
import com.pinshang.qingyun.order.service.TakeCardCreateOrderService;
import lombok.extern.slf4j.Slf4j;
import org.quartz.Job;
import org.quartz.JobDataMap;
import org.quartz.JobExecutionContext;
import org.springframework.stereotype.Component;


@Component
@Slf4j
public class GenerateCreateTakeCardOrderJob implements Job{


    @Override
    public void execute(JobExecutionContext jobExecutionContext){
    	
        JobDataMap jobDataMap = jobExecutionContext.getJobDetail().getJobDataMap();

        String orderTime = jobDataMap.getString("orderTime");

        TakeCardCreateOrderService takeCardCreateOrderService = (TakeCardCreateOrderService) SpringBeanFinder.getBean("takeCardCreateOrderService");

        try {

            takeCardCreateOrderService.createTakeCardOrder(orderTime);

        } catch (Throwable throwable) {
            log.error("GenerateCreateTakeCardOrderJob",throwable);
        }
        log.info("提货卡预约单提交订单时间endTime------------------------------------" + orderTime);


    }

}

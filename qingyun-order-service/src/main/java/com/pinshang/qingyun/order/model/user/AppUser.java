package com.pinshang.qingyun.order.model.user;

import com.pinshang.qingyun.base.po.BaseIDPO;
import com.pinshang.qingyun.order.enums.AppUserModifyEnum;
import com.pinshang.qingyun.order.enums.AppUserStatusEnum;
import lombok.Data;

import javax.persistence.Table;

/**
 * <AUTHOR>
 * @Date 2018/10/10 10:10
 */
@Table(name="t_app_user")
@Data
public class AppUser extends BaseIDPO {

    /**
     * 账户名称
     */
    private String name;
    /**
     * 账户密码
     */
    private String password;
    /**
     * 客户id
     */
    private Long storeId;
    /**
     * 客户编码
     */
    private String storeCode;
    /**
     * 是否需要修改密码
     */
    private AppUserModifyEnum modify;
    /**
     * 账号状态:0-有效,1-无效
     */
    private AppUserStatusEnum state;
    /**
     * 创建人
     */
    private Long createUserId;
}

package com.pinshang.qingyun.order.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * 门店商品信息
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class XDShopCommodityODTO {
	@ApiModelProperty(position = 1, required = true, value = "商品ID")
	private Long commodityId;

	@ApiModelProperty(position = 2, required = true, value = "包装规格")
	private BigDecimal commodityPackageSpec;

	@ApiModelProperty(position = 3, required = true, value = "销售箱装量（正数）")
	private BigDecimal salesBoxCapacity;

	private String deleveryTimeRange;
}

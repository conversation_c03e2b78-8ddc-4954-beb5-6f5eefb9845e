//package com.pinshang.qingyun.order.vo.order;
//
//import lombok.Data;
//
//import java.io.Serializable;
//
///**
// *
// * <AUTHOR>
// * @Date 2018/4/8 18:16
// */
//@Data
//public class ShipmentsVo implements Serializable {
//
//    private static final long serialVersionUID = -7611471340229568127L;
//    /**
//     * 订单日期
//     * "yyyy-MM-dd"
//     */
//    private String orderDate;
//    /**
//     * 线路组ID
//     */
//    private Long lineGroupId;
//    /**
//     * 发货仓库ID
//     */
//    private Long warehouseId;
//    /**
//     * 生产组主任code
//     */
//    private String directorCode;
//    /**
//     * 发货时间
//     * "HH:mm"
//     */
//    private String deliveryTime;
//
//    /**
//     * 配送批次
//     */
//    private Long deliveryBatch;
//}

package com.pinshang.qingyun.order.model.bigShop;

import com.pinshang.qingyun.base.po.BasePO;

import javax.persistence.Entity;
import javax.persistence.Table;
import java.util.Date;

/**
 * @Author: sk
 * @Date: 2024/10/11
 */
@Entity
@Table(name="t_dd_receive_doc")
public class DdReceiveDoc extends BasePO {

    /** 单据号 **/
    private String docCode;

    /** 送货日期 */
    private Date orderTime;

    /** 门店id **/
    private Long shopId;

    /** 档口id */
    private Long stallId;

    /** 配送批次 1=1配 + 补货  2=2配  8=新开店 */
    private Integer deliveryBatch;

    /** 单据状态：0 待收货 1 已经收货 **/
    private Integer docStatus;

    /** 收货人 */
    private Long receiveUserId;

    /** 收货时间 */
    private Date receiveTime;


    public String getDocCode() {
        return docCode;
    }

    public void setDocCode(String docCode) {
        this.docCode = docCode;
    }

    public Date getOrderTime() {
        return orderTime;
    }

    public void setOrderTime(Date orderTime) {
        this.orderTime = orderTime;
    }

    public Long getShopId() {
        return shopId;
    }

    public void setShopId(Long shopId) {
        this.shopId = shopId;
    }

    public Long getStallId() {
        return stallId;
    }

    public void setStallId(Long stallId) {
        this.stallId = stallId;
    }

    public Integer getDeliveryBatch() {
        return deliveryBatch;
    }

    public void setDeliveryBatch(Integer deliveryBatch) {
        this.deliveryBatch = deliveryBatch;
    }

    public Integer getDocStatus() {
        return docStatus;
    }

    public void setDocStatus(Integer docStatus) {
        this.docStatus = docStatus;
    }

    public Long getReceiveUserId() {
        return receiveUserId;
    }

    public void setReceiveUserId(Long receiveUserId) {
        this.receiveUserId = receiveUserId;
    }

    public Date getReceiveTime() {
        return receiveTime;
    }

    public void setReceiveTime(Date receiveTime) {
        this.receiveTime = receiveTime;
    }
}

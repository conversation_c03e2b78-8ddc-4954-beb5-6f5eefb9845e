package com.pinshang.qingyun.order.dto.shopcart.v3;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;
import java.util.List;
@Data
@ApiModel
@NoArgsConstructor
@AllArgsConstructor
public class ShoppingCartClearV3IDTO {
    @ApiModelProperty("普通商品id集合")
    private List<Long> commodityIds;

    @ApiModelProperty("特惠商品id集合")
    private List<Long> thCommodityIds;

    @ApiModelProperty("送货日期")
    private Date orderTime;
}

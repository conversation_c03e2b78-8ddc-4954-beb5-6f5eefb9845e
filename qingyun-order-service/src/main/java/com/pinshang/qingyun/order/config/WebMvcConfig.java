

package com.pinshang.qingyun.order.config;

import com.pinshang.qingyun.order.aspect.RequestBodyAndHeaderResolver;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.method.support.HandlerMethodArgumentResolver;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.ResourceHandlerRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurerAdapter;
import org.springframework.web.servlet.handler.HandlerInterceptorAdapter;

import java.util.List;

@Configuration
public class WebMvcConfig extends WebMvcConfigurerAdapter {
    @Autowired
    private HandlerInterceptorAdapter repeatRetryServiceInterceptor;

    @Autowired
    private HandlerInterceptorAdapter qyServiceInterceptor;

    @Autowired
    private HandlerInterceptorAdapter xdaServiceInterceptor;

    @Autowired
    private HandlerInterceptorAdapter pfServiceInterceptor;
    @Autowired
    private RequestBodyAndHeaderResolver requestBodyAndHeaderResolver;

    @Override
    public void addResourceHandlers(ResourceHandlerRegistry registry) {
        registry.addResourceHandler("/static/**").addResourceLocations("classpath:/static/");
    }

    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        super.addInterceptors(registry);
        registry.addInterceptor(repeatRetryServiceInterceptor);
        registry.addInterceptor(qyServiceInterceptor);
        registry.addInterceptor(xdaServiceInterceptor);
        registry.addInterceptor(pfServiceInterceptor);
    }

    @Override
    public void addArgumentResolvers(List<HandlerMethodArgumentResolver> resolvers) {
        resolvers.add(requestBodyAndHeaderResolver);
    }
}

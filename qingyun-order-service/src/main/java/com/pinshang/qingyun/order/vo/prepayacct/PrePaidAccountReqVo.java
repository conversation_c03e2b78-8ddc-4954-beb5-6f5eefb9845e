package com.pinshang.qingyun.order.vo.prepayacct;

import com.pinshang.qingyun.order.enums.PrePaidAccountReqRefBillTypeEnum;
import com.pinshang.qingyun.order.enums.PrePaidAccountReqStatusEnum;
import com.pinshang.qingyun.order.enums.PrePaidAccountReqTypeEnum;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 付款请求
 * <AUTHOR>
 *
 */
@Getter
@Setter
public class PrePaidAccountReqVo {
	private Long id;
	
	private PrePaidAccountReqTypeEnum reqType;
	private Long storeId;
	private BigDecimal amount;
	private Long promoterId;
	private String remarks;
	private PrePaidAccountReqStatusEnum status;
	
	private PrePaidAccountReqRefBillTypeEnum refBillType;
	private Long refBillId;
	private String refBillCode;
	private Date refBillTime;
	
	private BigDecimal balance; // 完成入账后，回写

	public PrePaidAccountReqVo() {}

	public PrePaidAccountReqVo(PrePaidAccountReqTypeEnum reqType, Long storeId, BigDecimal amount, Long promoterId, String remarks, PrePaidAccountReqStatusEnum status,
							   PrePaidAccountReqRefBillTypeEnum refBillType, Long refBillId,String refBillCode, Date refBillTime) {
		this(reqType, storeId, amount, promoterId, remarks, status,
				refBillType, refBillId, refBillTime);
		this.refBillCode = refBillCode;
	}

	public PrePaidAccountReqVo(Long id, PrePaidAccountReqTypeEnum reqType, Long storeId, BigDecimal amount, Long promoterId, String remarks, PrePaidAccountReqStatusEnum status, 
			PrePaidAccountReqRefBillTypeEnum refBillType, Long refBillId, Date refBillTime) {
		this(reqType, storeId, amount, promoterId, remarks, status,
				refBillType, refBillId, refBillTime);
		this.id = id;
	}
	
	public PrePaidAccountReqVo(PrePaidAccountReqTypeEnum reqType, Long storeId, BigDecimal amount, Long promoterId, String remarks, PrePaidAccountReqStatusEnum status, 
			PrePaidAccountReqRefBillTypeEnum refBillType, Long refBillId, Date refBillTime) {
		this.reqType = reqType;
		this.storeId = storeId;
		this.amount = amount;
		this.promoterId = promoterId;
		this.remarks = remarks;
		this.status = status;
		
		this.refBillType = refBillType;
		this.refBillId = refBillId;
		this.refBillTime = refBillTime;
	}
}

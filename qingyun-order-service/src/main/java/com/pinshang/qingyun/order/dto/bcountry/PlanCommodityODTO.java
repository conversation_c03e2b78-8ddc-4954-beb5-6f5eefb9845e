package com.pinshang.qingyun.order.dto.bcountry;

import com.pinshang.qingyun.base.enums.YesOrNoEnums;
import com.pinshang.qingyun.box.utils.DateUtil;
import com.pinshang.qingyun.renderer.annotation.FieldRender;
import com.pinshang.qingyun.renderer.constant.RenderFieldHelper;
import com.pinshang.qingyun.renderer.enums.FieldTypeEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2025年07月16日 下午1:50
 */
@Data
public class PlanCommodityODTO {

    @ApiModelProperty("商品id")
    private String commodityId;

    @ApiModelProperty("上面编码")
    private String commodityCode;

    @ApiModelProperty("商品名称")
    private String commodityName;

    @ApiModelProperty("规格")
    private String commoditySpec;

    @ApiModelProperty("计量单位")
    @FieldRender(fieldName = RenderFieldHelper.Commodity.commodityUnit, fieldType = FieldTypeEnum.COMMODITY, keyName = "commodityId")
    private String commodityUnit;

    @ApiModelProperty("保质期天数")
    private Integer qualityDays;

    @ApiModelProperty("保质期单位")
    private String qualityUnit;

    @ApiModelProperty("同步状态 1=已同步  0=未同步")
    private Integer syncStatus;

    @ApiModelProperty("同步时间")
    private Date syncTime;

    public String getSyncTime() {
        return DateUtil.getDateFormate(syncTime, DateUtil.DEFAULT_DATE_FORMAT);
    }

    public String getSyncStatusName(){
        return YesOrNoEnums.YES.getCode().equals(syncStatus) ? "已同步" : "同步失败";
    }
}

package com.pinshang.qingyun.order.dto.miniGroupon;

import com.pinshang.qingyun.base.enums.YesOrNoEnums;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;

import java.util.List;

@Data
@ApiModel
@NoArgsConstructor
public class MiniGrouponAutoOrderODTO{
    @ApiModelProperty("截单时间 yyyy-MM-dd HH:mm:ss")
    private String cutTime;
    @ApiModelProperty("成功后关联")
    private List<MiniGrouponAutoOrderItemODTO> items;
    @ApiModelProperty("是否成功：1-是，0-否")
    private Integer isSuccess;
    @ApiModelProperty("失败的信息")
    private String failMsgs;

    public MiniGrouponAutoOrderODTO(String cutTime){
        this.cutTime = cutTime;
    }

    public MiniGrouponAutoOrderODTO initFail(String failMsgs){
        this.failMsgs = StringUtils.isNotEmpty(this.failMsgs)?this.failMsgs+",\n"+failMsgs:failMsgs;
        this.isSuccess = YesOrNoEnums.NO.getCode();
        return this;
    }

    public MiniGrouponAutoOrderODTO initSuccess(List<MiniGrouponAutoOrderItemODTO> items){
        this.items = items;
        this.isSuccess = YesOrNoEnums.YES.getCode();
        return this;
    }
}

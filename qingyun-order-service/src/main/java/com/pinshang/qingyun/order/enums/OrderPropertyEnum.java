package com.pinshang.qingyun.order.enums;

import com.pinshang.qingyun.box.utils.EnumUtils;

/** 订货属性  1 必售  2 一级可选  3 二级可选**/
public enum OrderPropertyEnum {

    SELL_MUST(1, "必售"),
    SELL_FIRST(2, "一级可选"),
    SELL_SECOND(3, "二级可选"),

    ;

    private int code;
    private String desc;


    private OrderPropertyEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }


    public static String getName(Integer code) {
        if(code == null){
            return "";
        }
        OrderPropertyEnum o = (OrderPropertyEnum) EnumUtils.fromEnumProperty(OrderPropertyEnum.class, "code", code);
        return null == o ? "" : o.getDesc();
    }

    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }
}

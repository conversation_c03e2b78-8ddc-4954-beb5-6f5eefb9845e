package com.pinshang.qingyun.order.dto.consignment;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;


/**
 * <p>
 * 代销商总部审核退货
 * </p>
 *
 * <AUTHOR> chenlong
 * @version : 1.0.0
 * @history : modify history
 * <author>              <time>              <version>              <desc>
 * @since : 2024/05/11 10:09
 */
@Data
public class AuditConsignmentReturnOrderIDTO {

    /**
     * 退货单id
     */
    @ApiModelProperty(value = "退货单id")
    private Long id;

    /**
     * 审核明细数据
     */
    @ApiModelProperty(value = "审核明细数据")
    private List<AuditConsignmentReturnOrdeItemIDTO> auditSaleReturnOrderItems;

    /**
     * 审核人id
     */
    @ApiModelProperty(value = "审核人id")
    private Long checkUserId;

    /**
     * 审核结果:0-驳回,1-审核通过
     */
    @ApiModelProperty(value = "审核结果:0-驳回,1-审核通过")
    private Integer auditResult;

}

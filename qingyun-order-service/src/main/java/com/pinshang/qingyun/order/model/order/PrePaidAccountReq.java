package com.pinshang.qingyun.order.model.order;

import java.math.BigDecimal;
import java.util.Date;

import javax.persistence.Entity;
import javax.persistence.Table;

import com.pinshang.qingyun.base.po.BaseIDPO;

import lombok.Getter;
import lombok.Setter;

/**
 * 预付账户支付/收款请求
 * <AUTHOR>
 *
 */
@Entity
@Table(name = "t_pre_paid_account_req")
@Getter
@Setter
public class PrePaidAccountReq extends BaseIDPO {

	private Integer reqType;
	private Long storeId;
	private BigDecimal amount;
	private String remarks;
	private Integer reqStatus;
	private String errMsg;
	
	private Integer refBillType;
	private Long refBillId;
	private Date refBillTime;
	
	private Long createrId;
	private Date createTime;
	private Date updateTime;
	
}

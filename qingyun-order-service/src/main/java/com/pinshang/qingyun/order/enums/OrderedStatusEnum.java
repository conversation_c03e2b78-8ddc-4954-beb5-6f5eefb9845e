package com.pinshang.qingyun.order.enums;

import lombok.AllArgsConstructor;

/**
 * 1 当前时间可订货的商品——在订货时间段内，且有库存（商品库存依据且有库存的商品、不限量供应的商品、限量供应且有余量的商品）
 * 2 已抢光的商品——商品库存依据且库存≤0或限量供应且已满限量的商品，标记“已抢光”效果。
 * 3 当前时间不在订货时间段的商品
 */
@AllArgsConstructor
public enum OrderedStatusEnum {
    ORDERED(1, "当前时间可订货的商品"),
    SOLD_OUT(2,"已抢光的商品"),
    OVER_TIME(3,"当前时间不在订货时间段的商品"),

    ;
    private Integer code;
    private String name;

    public Integer getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

}

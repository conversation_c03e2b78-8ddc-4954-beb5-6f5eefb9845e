package com.pinshang.qingyun.order.model.order;

import com.pinshang.qingyun.base.po.BaseIDPO;
import lombok.Data;

import javax.persistence.Table;
import java.math.BigDecimal;

/**
 * @Author: sk
 * @Date: 2024/5/9
 */
@Data
@Table(name = "t_xda_pre_order_list")
public class XdaPreOrderItem extends BaseIDPO {

    /**订单id **/
    private Long orderId;

    /**商品id **/
    private Long commodityId;
    /**商品数量 **/
    private BigDecimal commodityNum;

    /**类型 1订单商品,2赠品,3配货商品,5特惠商品	—— 参见 ProductTypeEnums  **/
    private Integer type;
    /**促销后单价(原含义-单价) **/
    private BigDecimal commodityPrice;

    /**备注 **/
    private String remark;

    /**鲜达特惠商品t_xda_specials_commodity_set**/
    private Long specialsId;

    /**特价**/
    private BigDecimal specialPrice;

    /**原价**/
    private BigDecimal originalPrice;

    /**促销后的商品金额(原含义-商品销售总金额) **/
    private BigDecimal totalPrice;

    /**原金额(原价*数量)**/
    private BigDecimal originalTotalPrice;

    /** 赠送方案id **/
    private Long giftModelId;

    /**
     * 商品类型-1-非组合 2-组合  3-组合子品
     */

    private Integer combType = 1;
    /**
     * 属于组合商品id
     */
    private Long combCommodityId;

    /** 鲜达B端特价id */
    private Long pricePromotionId;

    /** 优惠券分摊金额 */
    private BigDecimal couponDiscountAmount;

    /** 优惠券ID */
    private Long couponId;

    /** 用户券ID */
    private Long couponUserId;
}

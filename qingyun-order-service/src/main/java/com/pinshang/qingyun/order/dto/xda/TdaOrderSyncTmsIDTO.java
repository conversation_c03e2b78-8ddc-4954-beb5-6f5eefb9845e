package com.pinshang.qingyun.order.dto.xda;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;


@Data
@NoArgsConstructor
public class TdaOrderSyncTmsIDTO {

	@ApiModelProperty("订单或者退单状态")
	private Integer status;
	
	@ApiModelProperty("源单类型：11-订单、21-退货单")
	private Integer refType;
	
	@ApiModelProperty("关联源单ID")
	private Long refOrderId;
	
	public static TdaOrderSyncTmsIDTO forOrder(Long orderId, Integer status) {
		TdaOrderSyncTmsIDTO idto = new TdaOrderSyncTmsIDTO();
		idto.refType = 11;
		idto.refOrderId = orderId;
		idto.status = status;
		return idto;
	}
	
	public static TdaOrderSyncTmsIDTO forReturnOrder(Long returnOrderId, Integer status) {
		TdaOrderSyncTmsIDTO idto = new TdaOrderSyncTmsIDTO();
		idto.refType = 21;
		idto.refOrderId = returnOrderId;
		idto.status = status;
		return idto;
	}

}

package com.pinshang.qingyun.order.util;

import com.pinshang.qingyun.base.configure.expand.BizLogicException;
import lombok.RequiredArgsConstructor;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Component;

import java.util.concurrent.TimeUnit;
import java.util.function.Supplier;


/**
 * redisson 分布式锁执行器。
 * 提供了多种基于分布式锁的业务逻辑执行方法，包括带有等待时间和租期时间的选项。
 * 适用于需要确保同一资源在分布式环境下不被并发修改的场景。
 *
 * <AUTHOR>
 * @since 2024/07/03 15:35:10
 */
@Component
@RequiredArgsConstructor
public class RedissonLockExecutor {

    private final RedissonClient redissonClient;

    /**
     * 处理有返回值的业务逻辑。
     *
     * @param bizKey   分布式锁的业务唯一键
     * @param supplier 提供业务逻辑的 {@link Supplier} 函数，在获取锁后执行并返回结果
     * @param <T>      业务逻辑返回值的类型
     * @return 业务逻辑的执行结果
     * @throws BizLogicException 如果无法获取锁时抛出此异常
     */
    public <T> T exec(String bizKey, Supplier<T> supplier) {
        RLock lock = redissonClient.getLock(bizKey);
        if (lock.tryLock()) {
            try {
                return supplier.get();
            } finally {
                lock.unlock();
            }
        } else {
            throw new BizLogicException("系统繁忙,请勿频繁操作!");
        }
    }

    /**
     * 处理无返回值的业务逻辑。
     *
     * @param bizKey   分布式锁的业务唯一键
     * @param runnable 提供业务逻辑的 {@link Runnable} 函数，在获取锁后执行
     * @throws BizLogicException 如果无法获取锁时抛出此异常
     */
    public void exec(String bizKey, Runnable runnable) {
        RLock lock = redissonClient.getLock(bizKey);
        if (lock.tryLock()) {
            try {
                runnable.run();
            } finally {
                lock.unlock();
            }
        } else {
            throw new BizLogicException("系统繁忙,请勿频繁操作!");
        }
    }


    /**
     * 处理有返回值，带等待锁时间的业务逻辑。
     *
     * @param bizKey   分布式锁的业务唯一键
     * @param wait     获取锁的最大等待时间（单位：秒）
     * @param supplier 提供业务逻辑的 {@link Supplier} 函数，在获取锁后执行并返回结果
     * @param <T>      业务逻辑返回值的类型
     * @return 业务逻辑的执行结果
     * @throws BizLogicException 如果在指定的等待时间内无法获取锁，或线程被中断时抛出此异常
     */
    public <T> T exec(String bizKey, long wait, Supplier<T> supplier) {
        RLock lock = redissonClient.getLock(bizKey);
        boolean isLocked = false;
        try {
            isLocked = lock.tryLock(wait, TimeUnit.SECONDS);
            if (isLocked) {
                return supplier.get();
            } else {
                throw new BizLogicException("系统繁忙,请勿频繁操作!");
            }
        } catch (InterruptedException e) {
            // 处理线程中断
            Thread.currentThread().interrupt();
            throw new BizLogicException("系统繁忙,请勿频繁操作!");
        } finally {
            if (isLocked && lock.isHeldByCurrentThread()) {
                try {
                    lock.unlock();
                } catch (Exception ignored) {

                }
            }
        }
    }

    /**
     * 处理无返回值，带等待锁时间的业务逻辑。
     *
     * @param bizKey   分布式锁的业务唯一键
     * @param wait     获取锁的最大等待时间（单位：秒）
     * @param runnable 提供业务逻辑的 {@link Runnable} 函数，在获取锁后执行
     * @throws BizLogicException 如果在指定的等待时间内无法获取锁，或线程被中断时抛出此异常
     */
    public void exec(String bizKey, long wait, Runnable runnable) {
        RLock lock = redissonClient.getLock(bizKey);
        boolean isLocked = false;
        try {
            isLocked = lock.tryLock(wait, TimeUnit.SECONDS);
            if (isLocked) {
                runnable.run();
            } else {
                throw new BizLogicException("系统繁忙,请勿频繁操作!");
            }
        } catch (InterruptedException e) {
            // 处理线程中断
            Thread.currentThread().interrupt();
            throw new BizLogicException("系统繁忙,请勿频繁操作!");
        } finally {
            if (isLocked && lock.isHeldByCurrentThread()) {
                try {
                    lock.unlock();
                } catch (Exception ignored) {

                }
            }
        }
    }

    /**
     * 执行有返回值的业务逻辑，并带有分布式锁的等待和租期时间。
     *
     * @param bizKey   分布式锁的业务唯一键
     * @param wait     获取锁的最大等待时间（单位：秒）
     * @param lease    锁的租期时间（单位：秒），即锁在自动释放之前保持的时间
     * @param supplier 提供业务逻辑的 {@link Supplier} 函数，在获取锁后执行并返回结果
     * @param <T>      业务逻辑返回值的类型
     * @return 业务逻辑的执行结果
     * @throws BizLogicException 如果在指定的等待时间内无法获取锁，或线程被中断时抛出此异常
     */
    public <T> T exec(String bizKey, long wait, long lease, Supplier<T> supplier) {
        return exec(bizKey, wait, lease, TimeUnit.SECONDS, supplier);
    }


    /**
     * 处理无返回值，带等待锁时间的业务逻辑。
     *
     * @param bizKey   分布式锁的业务唯一键
     * @param wait     获取锁的最大等待时间（单位：秒）
     * @param lease    锁的租期时间（单位：秒），即锁在自动释放之前保持的时间
     * @param runnable 提供业务逻辑的 {@link Runnable} 函数，在获取锁后执行
     * @throws BizLogicException 如果在指定的等待时间内无法获取锁，或线程被中断时抛出此异常
     */
    public void exec(String bizKey, long wait, long lease, Runnable runnable) {
        exec(bizKey, wait, lease, TimeUnit.SECONDS, runnable);
    }

    /**
     * 执行有返回值的业务逻辑，并带有分布式锁的等待和租期时间。
     *
     * @param bizKey   分布式锁的业务唯一键
     * @param wait     获取锁的最大等待时间（单位：秒）
     * @param lease    锁的租期时间（单位：秒），即锁在自动释放之前保持的时间
     * @param unit     time unit
     * @param supplier 提供业务逻辑的 {@link Supplier} 函数，在获取锁后执行并返回结果
     * @param <T>      业务逻辑返回值的类型
     * @return 业务逻辑的执行结果
     * @throws BizLogicException 如果在指定的等待时间内无法获取锁，或线程被中断时抛出此异常
     */
    public <T> T exec(String bizKey, long wait, long lease, TimeUnit unit, Supplier<T> supplier) {
        RLock lock = redissonClient.getLock(bizKey);
        boolean isLocked = false;
        try {
            isLocked = lock.tryLock(wait, lease, unit);
            if (isLocked) {
                return supplier.get();
            } else {
                throw new BizLogicException("系统繁忙,请勿频繁操作!");
            }
        } catch (InterruptedException e) {
            // 处理线程中断
            Thread.currentThread().interrupt();
            throw new BizLogicException("系统繁忙,请勿频繁操作!");
        } finally {
            if (isLocked && lock.isHeldByCurrentThread()) {
                try {
                    lock.unlock();
                } catch (Exception ignored) {

                }
            }
        }
    }


    /**
     * 处理无返回值，带等待锁时间的业务逻辑。
     *
     * @param bizKey   分布式锁的业务唯一键
     * @param wait     获取锁的最大等待时间（单位：秒）
     * @param lease    锁的租期时间（单位：秒），即锁在自动释放之前保持的时间
     * @param unit     time unit
     * @param runnable 提供业务逻辑的 {@link Runnable} 函数，在获取锁后执行
     * @throws BizLogicException 如果在指定的等待时间内无法获取锁，或线程被中断时抛出此异常
     */
    public void exec(String bizKey, long wait, long lease, TimeUnit unit, Runnable runnable) {
        RLock lock = redissonClient.getLock(bizKey);
        boolean isLocked = false;
        try {
            isLocked = lock.tryLock(wait, lease, unit);
            if (isLocked) {
                runnable.run();
            } else {
                throw new BizLogicException("系统繁忙,请勿频繁操作!");
            }
        } catch (InterruptedException e) {
            // 处理线程中断
            Thread.currentThread().interrupt();
            throw new BizLogicException("系统繁忙,请勿频繁操作!");
        } finally {
            if (isLocked && lock.isHeldByCurrentThread()) {
                try {
                    lock.unlock();
                } catch (Exception ignored) {

                }
            }
        }
    }
}
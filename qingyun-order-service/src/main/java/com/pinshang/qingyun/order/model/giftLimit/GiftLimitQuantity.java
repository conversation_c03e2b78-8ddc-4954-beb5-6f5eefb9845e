package com.pinshang.qingyun.order.model.giftLimit;

import com.pinshang.qingyun.base.po.BaseSimplePO;
import lombok.Data;

import javax.persistence.Entity;
import javax.persistence.Table;
import java.math.BigDecimal;


/**
 * B端买赠赠品总限量记录表
 **/
@Data
@Entity
@Table(name = "t_gift_limit_quantity")
public class GiftLimitQuantity extends BaseSimplePO {

    /** 买赠促销ID */
    private Long  promotionId;

    /** 商品id */
    private Long commodityId;

    /** 参与赠品总限量的订单id */
    private Long orderId;

    /** 赠送的数量 */
    private BigDecimal quantity;
}
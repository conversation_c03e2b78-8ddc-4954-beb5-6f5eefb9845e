package com.pinshang.qingyun.order.model.take;

import com.pinshang.qingyun.base.enums.takecard.TakeAppointmentStatusEnum;
import com.pinshang.qingyun.base.po.BaseIDPO;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Table;
import java.util.Date;

/**
 * 提货预约-提货卡
 */
@Data
@NoArgsConstructor
@Table(name = "t_take_applintment_card")
public class TakeAppointmentCard extends BaseIDPO {
	// 提货预约ID
	private Long takeAppointmentId;
	// 提货卡ID
	private Long cardId;
	// 提货卡号
	private String cardNo;
	// 提货状态：0-未完成、1-已完成、2-已取消预约	—— 参见 TakeAppointmentStatusEnum
	private Integer status;
	// C端用户-更新时间
	private Date userUpdateTime;
	// 后端用户-更新人ID
	private Long updateId;
	// 后端用户-更新时间
	private Date updateTime;
	
	public TakeAppointmentCard(Long takeAppointmentId) {
		this.takeAppointmentId = takeAppointmentId;
	}
	
	// 用户C端用户-新增
	public static TakeAppointmentCard forFrontInsert(Long takeAppointmentId, Long cardId, String cardNo, Date userCreateTime) {
		TakeAppointmentCard model = new TakeAppointmentCard();
		model.setTakeAppointmentId(takeAppointmentId);
		model.setCardId(cardId);
		model.setCardNo(cardNo);
		model.setStatus(TakeAppointmentStatusEnum.UN_COMPLETE.getCode());
		model.setUserUpdateTime(userCreateTime);
		return model;
	}
	
	// 用户C端用户-取消
	public static TakeAppointmentCard forFrontCancel(Long takeAppointmentId, Date userUpdateTime) {
		TakeAppointmentCard model = new TakeAppointmentCard();
		model.setTakeAppointmentId(takeAppointmentId);
		model.setStatus(TakeAppointmentStatusEnum.CANCEl.getCode());
		model.setUserUpdateTime(userUpdateTime);
		return model;
	}
	
	// 用于后端用户-完成提货
	public static TakeAppointmentCard forComplete(Long takeAppointmentCardId, Long updateId, Date updateTime) {
		TakeAppointmentCard model = new TakeAppointmentCard();
		model.setId(takeAppointmentCardId);
		model.setStatus(TakeAppointmentStatusEnum.COMPLETED.getCode());
		model.setUpdateId(updateId);
		model.setUpdateTime(updateTime);
		return model;
	}
	
}

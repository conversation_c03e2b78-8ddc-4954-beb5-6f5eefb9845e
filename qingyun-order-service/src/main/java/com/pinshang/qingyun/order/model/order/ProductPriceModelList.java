package com.pinshang.qingyun.order.model.order;

import java.math.BigDecimal;

import javax.persistence.Entity;
import javax.persistence.Table;

import com.pinshang.qingyun.base.po.BaseIDPO;

@Entity
@Table(name = "t_product_price_model_list")
public class ProductPriceModelList extends BaseIDPO {
	/**产品价格方案ID **/
    private Long productPriceModelId;
	/**货品ID **/
    private Long commodityId;
	/**货品价格 **/
    private BigDecimal commodityPrice;
	public Long getProductPriceModelId() {
		return productPriceModelId;
	}
	public void setProductPriceModelId(Long productPriceModelId) {
		this.productPriceModelId = productPriceModelId;
	}
	public Long getCommodityId() {
		return commodityId;
	}
	public void setCommodityId(Long commodityId) {
		this.commodityId = commodityId;
	}
	public BigDecimal getCommodityPrice() {
		return commodityPrice;
	}
	public void setCommodityPrice(BigDecimal commodityPrice) {
		this.commodityPrice = commodityPrice;
	}
}

package com.pinshang.qingyun.order.listener;

import com.alibaba.fastjson.JSON;
import com.pinshang.qingyun.base.annotations.OnlineSwitchWatcher;
import com.pinshang.qingyun.base.api.QYApplicationContext;
import com.pinshang.qingyun.base.spring.SpringBeanFinder;
import com.pinshang.qingyun.box.utils.JsonUtil;
import com.pinshang.qingyun.common.dto.DelayMsgUpDTO;
import com.pinshang.qingyun.kafka.base.BaseKafkaOnlineSwitchProcessor;
import com.pinshang.qingyun.kafka.base.KafkaMessageWrapper;
import com.pinshang.qingyun.kafka.base.KafkaTopicConstant;
import com.pinshang.qinyun.cache.enums.RedisDelayQueueEnum;
import com.pinshang.qinyun.cache.utils.RedisDelayQueueHandle;
import lombok.extern.slf4j.Slf4j;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.List;


@Component
@Slf4j
@OnlineSwitchWatcher
public class DelayMsgKafkaListener extends BaseKafkaOnlineSwitchProcessor {
    @Override
    public List<String> getKafkaIds() {
        return Arrays.asList(
                QYApplicationContext.applicationNameSwitch + KafkaTopicConstant.DELAY_MESSAGE_TOPIC + "kafkaListenerContainerFactory"
                );
    }

    @KafkaListener(topics = "${application.name.switch}" + KafkaTopicConstant.DELAY_MESSAGE_UP_TOPIC
            ,errorHandler = "kafkaConsumerErrorHandler"
            ,containerFactory = "kafkaListenerContainerFactory")
    public void delayQueueMessage(String message) {
        String msg = "topic:" + KafkaTopicConstant.DELAY_MESSAGE_UP_TOPIC + " ====================message==" + message;
        log.info(msg);
        try {
            KafkaMessageWrapper messageWrapper = JSON.parseObject(message, KafkaMessageWrapper.class);
            DelayMsgUpDTO idto = JsonUtil.json2java(messageWrapper.getData().toString(), DelayMsgUpDTO.class);
            if(idto != null){
                RedisDelayQueueEnum redisDelayQueueEnum = RedisDelayQueueEnum.getByCode(idto.getDelayQueueCode());
                if(redisDelayQueueEnum != null){
                    if(RedisDelayQueueEnum.XDA_PRE_ORDER_DELAY.getBeanId().equals(redisDelayQueueEnum.getBeanId())){
                        RedisDelayQueueHandle redisDelayQueueHandle = (RedisDelayQueueHandle) SpringBeanFinder.getBean(redisDelayQueueEnum.getBeanId());
                        if (redisDelayQueueHandle != null){
                            redisDelayQueueHandle.execute(idto.getCode());
                        }
                    }
                }
            }
        } catch (Exception e) {
            log.error(msg + "延时消息异常:{}", e);
        }

    }
}

package com.pinshang.qingyun.order.vo.order;

import com.pinshang.qingyun.base.page.Pagination;
import com.pinshang.qingyun.order.mapper.entry.stall.StallEntry;

public class OrderListRequestVo  extends StallEntry {
	
	private static final long serialVersionUID = 1L;
	/** 门店id **/
	private Long storeId;
	/** 企业id **/
	private Long enterpriseId;

	private String startTime;
	
	private String endTime;

	private String orderCode;

	private Long orderId;

	private Long createId;
	private Integer deliveryBatch;
	private Integer orderStatus;

	private Integer orderType;

	/**
	 * 是否内置用户
	 */
	private Boolean isInternal;
	// 代销商户ID
	private Long consignmentId;

	public Boolean getInternal() {
		return isInternal;
	}

	public void setInternal(Boolean internal) {
		isInternal = internal;
	}

	public Long getConsignmentId() {
		return consignmentId;
	}

	public void setConsignmentId(Long consignmentId) {
		this.consignmentId = consignmentId;
	}

	public Long getStoreId() {
		return storeId;
	}

	public void setStoreId(Long storeId) {
		this.storeId = storeId;
	}

	public Long getEnterpriseId() {
		return enterpriseId;
	}

	public void setEnterpriseId(Long enterpriseId) {
		this.enterpriseId = enterpriseId;
	}

	public String getStartTime() {
		return startTime;
	}

	public void setStartTime(String startTime) {
		this.startTime = startTime;
	}

	public String getEndTime() {
		return endTime;
	}

	public void setEndTime(String endTime) {
		this.endTime = endTime;
	}

	public String getOrderCode() {
		return orderCode;
	}

	public void setOrderCode(String orderCode) {
		this.orderCode = orderCode;
	}

	public Long getOrderId() {
		return orderId;
	}

	public void setOrderId(Long orderId) {
		this.orderId = orderId;
	}

	public Integer getOrderType() {
		return orderType;
	}

	public void setOrderType(Integer orderType) {
		this.orderType = orderType;
	}

	public Long getCreateId() {
		return createId;
	}

	public void setCreateId(Long createId) {
		this.createId = createId;
	}

	public Integer getDeliveryBatch() {
		return deliveryBatch;
	}

	public void setDeliveryBatch(Integer deliveryBatch) {
		this.deliveryBatch = deliveryBatch;
	}

	public Integer getOrderStatus() {
		return orderStatus;
	}

	public void setOrderStatus(Integer orderStatus) {
		this.orderStatus = orderStatus;
	}
}

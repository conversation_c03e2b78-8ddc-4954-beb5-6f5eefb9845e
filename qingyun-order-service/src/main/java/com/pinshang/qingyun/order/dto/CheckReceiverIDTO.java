package com.pinshang.qingyun.order.dto;

import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 *  查询待收货/收货中的客户
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class CheckReceiverIDTO {
    @ApiModelProperty("退货单ID")
    private Long id;
    @ApiModelProperty("退货单号")
    private String code;
    @ApiModelProperty("企业ID")
    private Long enterpriseId;
    private Long receiverId;
}

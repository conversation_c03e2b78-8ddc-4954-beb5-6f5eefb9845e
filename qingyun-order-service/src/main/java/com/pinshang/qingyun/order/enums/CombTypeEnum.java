package com.pinshang.qingyun.order.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;


/**
 * 状态枚举
 * 商品类型-1-非组合 2-组合  3-组合子品
 *
 * <AUTHOR>
 * @since 2024/04/15
 */
@Getter
@NoArgsConstructor
@AllArgsConstructor
public enum CombTypeEnum {

    /**
     * 非组合
     */
    NOT_COMB(1, "非组合"),
    /**
     * 组合
     */
    COMB(2, "组合"),
    /**
     * 组合子品
     */
    COMB_CHILD(3, "组合子品"),

    ;

    /**
     * 类型码
     */
    private Integer code;

    /**
     * 类型描述
     */
    private String desc;

    /**
     * 验证是否合法
     *
     * @param code 名称
     * @return 枚举类
     */
    public static CombTypeEnum explain(Integer code) {
        for (CombTypeEnum ruleTypeEnum : CombTypeEnum.values()) {
            if (ruleTypeEnum.code.equals(code)) {
                return ruleTypeEnum;
            }
        }
        return null;
    }


}

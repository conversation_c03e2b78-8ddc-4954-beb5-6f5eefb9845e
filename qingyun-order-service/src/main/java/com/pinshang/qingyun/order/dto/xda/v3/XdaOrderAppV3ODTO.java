package com.pinshang.qingyun.order.dto.xda.v3;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.pinshang.qingyun.base.configure.codec.DecimalSerializerKeep2;
import com.pinshang.qingyun.base.enums.OrderStatusEnums;
import com.pinshang.qingyun.base.enums.order.OrderTypeEnum;
import com.pinshang.qingyun.base.enums.xda.XdaStoreTypeEnum;
import com.pinshang.qingyun.store.dto.customer.StoreSelectODTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2023/12/28 13:46
 */
@Data
@ApiModel
public class XdaOrderAppV3ODTO {
    @ApiModelProperty(value = "orderId", hidden = true)
    private Long orderId;
    @ApiModelProperty("订单号")
    private String orderCode;

    @ApiModelProperty("联系人")
    private String storeLinkman;
    @ApiModelProperty("联系人手机号")
    private String linkmanMobile;
    @ApiModelProperty("客户名称")
    private String storeName;
    @ApiModelProperty("送货地址")
    private String deliveryAddress;

    @ApiModelProperty("商品金额(原价总计)")
    @JsonSerialize(using = DecimalSerializerKeep2.class)
    private BigDecimal totalOriginPrice;

    @ApiModelProperty(hidden = true,value = "支付金额")
    @JsonSerialize(using = DecimalSerializerKeep2.class)
    private BigDecimal commodityPrice;

    @ApiModelProperty(value = "订单类型,根据类型判断只有鲜达订单才可以取消")
    private Integer orderType;

    @ApiModelProperty("优惠(优惠总计)")
    @JsonSerialize(using = DecimalSerializerKeep2.class)
    private BigDecimal discountTotal;

    @ApiModelProperty(" 实付总金额")
    @JsonSerialize(using = DecimalSerializerKeep2.class)
    private BigDecimal summation;

    @ApiModelProperty(" 实发金额")
    @JsonSerialize(using = DecimalSerializerKeep2.class)
    private BigDecimal realAmount;

    @ApiModelProperty("配送费默认为0")
    @JsonSerialize(using = DecimalSerializerKeep2.class)
    private BigDecimal freightAmount = BigDecimal.ZERO;

    @ApiModelProperty("2=取消 7=待发货11=出库中15=配送中19=配送完成")
    private Integer processStatus;

    @ApiModelProperty("订单状态(0正常,1删除,2取消)")
    private Integer orderStatus;

    @ApiModelProperty("账户类型-支付方式")
    private XdaStoreTypeEnum storeType;

    @ApiModelProperty("送货日期")
    private Date orderTime;
    @ApiModelProperty("订单创建日期")
    private Date orderCreateTime;
    @ApiModelProperty("品种合计")
    private Integer varietySum;
    @ApiModelProperty("商品列表")
    private List<XdaOrderItemAppV3ODTO> commodities;

    public Integer getProcessStatus(){
        if(null != orderType && orderType.equals(OrderTypeEnum.XDA_APP_ORDER.getCode())){
            return processStatus;
        }else {
            //为了将APP取消按钮隐藏所做的特殊处理 19会显示到货照片;7、2 会显示取消按钮;所以选择 15、11作为返回值
            if(orderStatus == 2){
                return 2;
            }else if(this.processStatus == null || this.processStatus == 7  || this.processStatus == 2 || this.processStatus == 11 ){
                return 11;
            }else if(this.processStatus == 15  || this.processStatus == 19){
                return 15;
            }
            return 2;
        }
    }

    public Integer getOrderStatus(){
        if(null == orderType){
            return null;
        }
        if(orderType.equals(OrderTypeEnum.XDA_APP_ORDER.getCode())){
            return orderStatus;
        }else {
            return OrderStatusEnums.CANCELED.getCode();
        }
    }

    public void covertStoreBaseInfo(StoreSelectODTO selectODTO){
        storeLinkman = selectODTO.getStoreLinkman();
        linkmanMobile = selectODTO.getLinkmanMobile();
        storeName = selectODTO.getStoreName();
        deliveryAddress = selectODTO.getDeliveryAddress();
    }
}

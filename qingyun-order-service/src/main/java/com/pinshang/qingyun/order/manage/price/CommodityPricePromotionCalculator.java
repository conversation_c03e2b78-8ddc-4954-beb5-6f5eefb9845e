package com.pinshang.qingyun.order.manage.price;

import com.pinshang.qingyun.box.utils.SpringUtil;
import com.pinshang.qingyun.order.dto.cup.ProductPriceDto;
import com.pinshang.qingyun.order.service.cup.OrderCupService;
import com.pinshang.qingyun.order.util.list.ListToMapConverter;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

@Component
public class CommodityPricePromotionCalculator {

    @Autowired
    private OrderCupService orderCupService;

    /**
     * 计算特价逻辑
     * @return
     */
    public Map<String, BigDecimal> calculatePricePromotionCommodityPrice(Long storeId, List<Long> commodityIdList, String orderTime){

        List<ProductPriceDto> ppdList = orderCupService.productPrice(commodityIdList,String.valueOf(storeId),orderTime);

        return ListToMapConverter.convertListToMap(ppdList,ProductPriceDto::getProductId,ProductPriceDto::getPrice);

    }
}

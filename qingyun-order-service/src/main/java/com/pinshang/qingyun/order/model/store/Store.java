package com.pinshang.qingyun.order.model.store;

import com.pinshang.qingyun.base.po.BaseTimePO;

import javax.persistence.Entity;
import javax.persistence.Table;
import javax.persistence.Transient;
import java.math.BigDecimal;
import java.util.Date;

@Entity
@Table(name = "t_store")
public class Store  extends BaseTimePO {

	@Transient
	private String idStr;

    /**店铺编号 **/
    private String storeCode;
    /**店铺名称 **/
    private String storeName;
    /**店铺助记码 **/
    private String storeAid;
    /**旧代码 **/
    private String storeOldCode;
    /**描述 **/
    private String storeDestribe;
    /**店铺类型_id **/
    private Long storeTypeId;
    /**店铺等级_id **/
    private Long storeLevelId;
    /**商铺类别_id **/
    private Long storeCategoryId;
    /**状态
     (0启用,1停用) **/
    private Integer storeStatus;
    /**开店时间 **/
    private Date storeOpenTime;
    /**关店时间 **/
    private Date storeCloseTime;
    /**所属公司ID **/
    private Long storeCompanyId;
    /**所属区域ID **/
    private Long storeDistrictId;
    /**结账客户ID **/
    private Long settlementCustomerId;

    /**渠道ID **/
    private Long storeChannelId;
    /**线路ID **/
    private Long storeLineId;
    
    /**下浮率（折扣率） **/
    private BigDecimal lowerRate;
    /**是否预收 **/
    private Integer storeIsPrepay;
    /**是否开票 **/
    private Integer storeIsBilling;
    /**送货员 **/
    private Long deliverymanId;
 
    /**销售员 **/
    private Long salesmanId;
 
    /**督导ID **/
    private Long supervisorId;
 
    /**办事处主任ID **/
    private Long officeDirectorId;
 
    /**押金 **/
    private BigDecimal deposit;
    /**大区经理ID **/
    private Long regionManagerId;
 
    /**是否提醒 **/
    private Integer storeIsAlert;
    /**订货结束是否短信提示 **/
    private Integer orderCloseIsAlert;
    /**订货方式 **/
    private String orderMethod;
    /**第一次提醒司机 **/
    private String firstAlertTime;
    /**提醒时间间隔 **/
    private String alertInterval;
    /**提醒次数 **/
    private Integer alertTimes;
    /**停止手机订货时间
     (控制手机app的停止订货时间) **/
    private String stopMonileTime;
    /**网上订货时间 **/
    private String netOrderTime;
    /**电话停止订货
     时间 **/
    private String telStopOrder;
    /**未订货备注 **/
    private String notOrderRemark;
    /**打印份数
     (客户要求的小单子份数) **/
    private BigDecimal printCopies;
    /**打印备注 **/
    private String printBackup;
    /**打印送货单标题 **/
    private String printOrderTitle;
    /**是否显示单价 **/
    private Integer showPriceRetail;
    /**是否显示金额 **/
    private Integer moneyIsShow;
    /**是否送货单加上签字 **/
    private Integer orderSignIn;
    /**当日手机最大订单量 **/
    private BigDecimal maxMobileOrder;
    /**当日订单最大订单量 **/
    private BigDecimal maxOrder;
    /**国家ID **/
    private Long countryId;
    /**省ID **/
    private Long provinceId;
    /**市ID **/
    private Long cityId;
    /**区ID **/
    private Long areaId;
    /**详细地址**/
    private String detailAddress;
    /**商圈ID **/
    private Long businessCirclesId;
    /**门店地址 **/
    private String storeAddress;
    /**送货地址 **/
    private String deliveryAddress;
    /**联系人 **/
    private String storeLinkman;
    /**电话 **/
    private String linkmanTel;
    /**手机 **/
    private String linkmanMobile;
    /**传真 **/
    private String linkmanFax;
    /**邮箱 **/
    private String linkmanEmail;
    /**收货时间 **/
    private String receiveTime;
    /**检验报告 **/
    private Integer isTestReport;
    /**检验报告备注 **/
    private String testReportRemark;
    /**是否领用耗材 **/
    private Integer isReceiveConsumables;
    /**金蝶接口 **/
    private String kingdeeInterface;
    /**是否速冻 **/
    private Integer isQuickFreeze;
    /**速冻数量 **/
    private BigDecimal quickFreezeAmount;
    /**打印批次_id **/
    private Long printDeliveryBatch;
 
    /**结账备注 **/
    private String settlementRemark;
    /**顺序 **/
    private Integer printDeliveryQueue;

    /** 创建该客户的用户ID **/
    private Long createUserId;
    /** 是否已开启结账方式 默认为启用-1 其余0 */
    private Long settlementStatus;
    
    /**
     * 客户类型1-内部，2-外部
     */
    private Integer storeType;
    
    @Transient
    private String storeTypeName;
    @Transient
    private String storeDistrictName;
    /** 结账客户名称 **/
    @Transient
    private String settlementCustomerName;
    /**线路名称 **/
    @Transient
    private String storeLineCode;
    @Transient
    private String storeLineName;
    @Transient
    private String deliverymanName;
 
    @Transient
    private String supervisorName;
    @Transient
    private String officeDirector;
    @Transient
    private String regionManager;
    
    /**公司**/
    @Transient
    private String storeCompanyCode;
    
    @Transient //销售员名字
    private String storeSalesName;
    //大区经理名字
    @Transient
    private String regionmanagerName;
    //主任名字
    @Transient
    private String officeDirectorName;
    /**区域**/
    @Transient
    private String storeSortCode;
	/**最小配送金额=起送金额 **/
	private BigDecimal minDeliveryAmount;
	/**业务类型：10-通达销售 **/
	private Integer businessType;

	@Transient
	private Long shopId;
	@Transient
	private Integer shopType;

	@Transient
	private Long deliveryManId;

	/**客户余额**/
	@Transient
	private BigDecimal balance;

	@Transient
	private String salesmanName;

	/** 经营模式：1-直营、2-外包、3-档口分包	—— 参见枚举 ManagementModeEnums */
	@Transient
	private Integer managementMode;

	public Integer getManagementMode() {
		return managementMode;
	}

	public void setManagementMode(Integer managementMode) {
		this.managementMode = managementMode;
	}

	public String getIdStr() {
		return this.getId()==null?null:this.getId().toString();
	}

	public String getSalesmanName() {
		return salesmanName;
	}

	public void setSalesmanName(String salesmanName) {
		this.salesmanName = salesmanName;
	}

	public Long getDeliveryManId() {
		return deliveryManId;
	}

	public void setDeliveryManId(Long deliveryManId) {
		this.deliveryManId = deliveryManId;
	}

	public BigDecimal getBalance() {
		return balance;
	}

	public void setBalance(BigDecimal balance) {
		this.balance = balance;
	}

	public Long getShopId() {
		return shopId;
	}
	public void setShopId(Long shopId) {
		this.shopId = shopId;
	}
	public Integer getShopType() {
		return shopType;
	}
	public void setShopType(Integer shopType) {
		this.shopType = shopType;
	}
	public BigDecimal getMinDeliveryAmount() {
		return minDeliveryAmount;
	}
	public void setMinDeliveryAmount(BigDecimal minDeliveryAmount) {
		this.minDeliveryAmount = minDeliveryAmount;
	}
	public String getStoreCode() {
		return storeCode;
	}
	public void setStoreCode(String storeCode) {
		this.storeCode = storeCode;
	}
	public String getStoreName() {
		return storeName;
	}
	public void setStoreName(String storeName) {
		this.storeName = storeName;
	}
	public String getStoreAid() {
		return storeAid;
	}
	public void setStoreAid(String storeAid) {
		this.storeAid = storeAid;
	}
	public String getStoreOldCode() {
		return storeOldCode;
	}
	public void setStoreOldCode(String storeOldCode) {
		this.storeOldCode = storeOldCode;
	}
	public String getStoreDestribe() {
		return storeDestribe;
	}
	public void setStoreDestribe(String storeDestribe) {
		this.storeDestribe = storeDestribe;
	}
	public Long getStoreTypeId() {
		return storeTypeId;
	}
	public void setStoreTypeId(Long storeTypeId) {
		this.storeTypeId = storeTypeId;
	}
	public Long getStoreLevelId() {
		return storeLevelId;
	}
	public void setStoreLevelId(Long storeLevelId) {
		this.storeLevelId = storeLevelId;
	}
	public Long getStoreCategoryId() {
		return storeCategoryId;
	}
	public void setStoreCategoryId(Long storeCategoryId) {
		this.storeCategoryId = storeCategoryId;
	}
	public Integer getStoreStatus() {
		return storeStatus;
	}
	public void setStoreStatus(Integer storeStatus) {
		this.storeStatus = storeStatus;
	}
	public Date getStoreOpenTime() {
		return storeOpenTime;
	}
	public void setStoreOpenTime(Date storeOpenTime) {
		this.storeOpenTime = storeOpenTime;
	}
	public Date getStoreCloseTime() {
		return storeCloseTime;
	}
	public void setStoreCloseTime(Date storeCloseTime) {
		this.storeCloseTime = storeCloseTime;
	}
	public Long getStoreCompanyId() {
		return storeCompanyId;
	}
	public void setStoreCompanyId(Long storeCompanyId) {
		this.storeCompanyId = storeCompanyId;
	}
	public Long getStoreDistrictId() {
		return storeDistrictId;
	}
	public void setStoreDistrictId(Long storeDistrictId) {
		this.storeDistrictId = storeDistrictId;
	}
	public Long getSettlementCustomerId() {
		return settlementCustomerId;
	}
	public void setSettlementCustomerId(Long settlementCustomerId) {
		this.settlementCustomerId = settlementCustomerId;
	}
	public Long getStoreChannelId() {
		return storeChannelId;
	}
	public void setStoreChannelId(Long storeChannelId) {
		this.storeChannelId = storeChannelId;
	}
	public Long getStoreLineId() {
		return storeLineId;
	}
	public void setStoreLineId(Long storeLineId) {
		this.storeLineId = storeLineId;
	}
	public BigDecimal getLowerRate() {
		return lowerRate;
	}
	public void setLowerRate(BigDecimal lowerRate) {
		this.lowerRate = lowerRate;
	}
	public Integer getStoreIsPrepay() {
		return storeIsPrepay;
	}
	public void setStoreIsPrepay(Integer storeIsPrepay) {
		this.storeIsPrepay = storeIsPrepay;
	}
	public Integer getStoreIsBilling() {
		return storeIsBilling;
	}
	public void setStoreIsBilling(Integer storeIsBilling) {
		this.storeIsBilling = storeIsBilling;
	}
	public Long getDeliverymanId() {
		return deliverymanId;
	}
	public void setDeliverymanId(Long deliverymanId) {
		this.deliverymanId = deliverymanId;
	}
	public Long getSalesmanId() {
		return salesmanId;
	}
	public void setSalesmanId(Long salesmanId) {
		this.salesmanId = salesmanId;
	}
	public Long getSupervisorId() {
		return supervisorId;
	}
	public void setSupervisorId(Long supervisorId) {
		this.supervisorId = supervisorId;
	}
	public Long getOfficeDirectorId() {
		return officeDirectorId;
	}
	public void setOfficeDirectorId(Long officeDirectorId) {
		this.officeDirectorId = officeDirectorId;
	}
	public BigDecimal getDeposit() {
		return deposit;
	}
	public void setDeposit(BigDecimal deposit) {
		this.deposit = deposit;
	}
	public Long getRegionManagerId() {
		return regionManagerId;
	}
	public void setRegionManagerId(Long regionManagerId) {
		this.regionManagerId = regionManagerId;
	}
	public Integer getStoreIsAlert() {
		return storeIsAlert;
	}
	public void setStoreIsAlert(Integer storeIsAlert) {
		this.storeIsAlert = storeIsAlert;
	}
	public Integer getOrderCloseIsAlert() {
		return orderCloseIsAlert;
	}
	public void setOrderCloseIsAlert(Integer orderCloseIsAlert) {
		this.orderCloseIsAlert = orderCloseIsAlert;
	}
	public String getOrderMethod() {
		return orderMethod;
	}
	public void setOrderMethod(String orderMethod) {
		this.orderMethod = orderMethod;
	}
	public String getFirstAlertTime() {
		return firstAlertTime;
	}
	public void setFirstAlertTime(String firstAlertTime) {
		this.firstAlertTime = firstAlertTime;
	}
	public String getAlertInterval() {
		return alertInterval;
	}
	public void setAlertInterval(String alertInterval) {
		this.alertInterval = alertInterval;
	}
	public Integer getAlertTimes() {
		return alertTimes;
	}
	public void setAlertTimes(Integer alertTimes) {
		this.alertTimes = alertTimes;
	}
	public String getStopMonileTime() {
		return stopMonileTime;
	}
	public void setStopMonileTime(String stopMonileTime) {
		this.stopMonileTime = stopMonileTime;
	}
	public String getNetOrderTime() {
		return netOrderTime;
	}
	public void setNetOrderTime(String netOrderTime) {
		this.netOrderTime = netOrderTime;
	}
	public String getTelStopOrder() {
		return telStopOrder;
	}
	public void setTelStopOrder(String telStopOrder) {
		this.telStopOrder = telStopOrder;
	}
	public String getNotOrderRemark() {
		return notOrderRemark;
	}
	public void setNotOrderRemark(String notOrderRemark) {
		this.notOrderRemark = notOrderRemark;
	}
	public BigDecimal getPrintCopies() {
		return printCopies;
	}
	public void setPrintCopies(BigDecimal printCopies) {
		this.printCopies = printCopies;
	}
	public String getPrintBackup() {
		return printBackup;
	}
	public void setPrintBackup(String printBackup) {
		this.printBackup = printBackup;
	}
	public String getPrintOrderTitle() {
		return printOrderTitle;
	}
	public void setPrintOrderTitle(String printOrderTitle) {
		this.printOrderTitle = printOrderTitle;
	}
	public Integer getShowPriceRetail() {
		return showPriceRetail;
	}
	public void setShowPriceRetail(Integer showPriceRetail) {
		this.showPriceRetail = showPriceRetail;
	}
	public Integer getMoneyIsShow() {
		return moneyIsShow;
	}
	public void setMoneyIsShow(Integer moneyIsShow) {
		this.moneyIsShow = moneyIsShow;
	}
	public Integer getOrderSignIn() {
		return orderSignIn;
	}
	public void setOrderSignIn(Integer orderSignIn) {
		this.orderSignIn = orderSignIn;
	}
	public BigDecimal getMaxMobileOrder() {
		return maxMobileOrder;
	}
	public void setMaxMobileOrder(BigDecimal maxMobileOrder) {
		this.maxMobileOrder = maxMobileOrder;
	}
	public BigDecimal getMaxOrder() {
		return maxOrder;
	}
	public void setMaxOrder(BigDecimal maxOrder) {
		this.maxOrder = maxOrder;
	}
	public Long getCountryId() {
		return countryId;
	}
	public void setCountryId(Long countryId) {
		this.countryId = countryId;
	}
	public Long getProvinceId() {
		return provinceId;
	}
	public void setProvinceId(Long provinceId) {
		this.provinceId = provinceId;
	}
	public Long getCityId() {
		return cityId;
	}
	public void setCityId(Long cityId) {
		this.cityId = cityId;
	}
	public Long getAreaId() {
		return areaId;
	}
	public void setAreaId(Long areaId) {
		this.areaId = areaId;
	}
	public String getDetailAddress() {
		return detailAddress;
	}
	public void setDetailAddress(String detailAddress) {
		this.detailAddress = detailAddress;
	}
	public Long getBusinessCirclesId() {
		return businessCirclesId;
	}
	public void setBusinessCirclesId(Long businessCirclesId) {
		this.businessCirclesId = businessCirclesId;
	}
	public String getStoreAddress() {
		return storeAddress;
	}
	public void setStoreAddress(String storeAddress) {
		this.storeAddress = storeAddress;
	}
	public String getDeliveryAddress() {
		return deliveryAddress;
	}
	public void setDeliveryAddress(String deliveryAddress) {
		this.deliveryAddress = deliveryAddress;
	}
	public String getStoreLinkman() {
		return storeLinkman;
	}
	public void setStoreLinkman(String storeLinkman) {
		this.storeLinkman = storeLinkman;
	}
	public String getLinkmanTel() {
		return linkmanTel;
	}
	public void setLinkmanTel(String linkmanTel) {
		this.linkmanTel = linkmanTel;
	}
	public String getLinkmanMobile() {
		return linkmanMobile;
	}
	public void setLinkmanMobile(String linkmanMobile) {
		this.linkmanMobile = linkmanMobile;
	}
	public String getLinkmanFax() {
		return linkmanFax;
	}
	public void setLinkmanFax(String linkmanFax) {
		this.linkmanFax = linkmanFax;
	}
	public String getLinkmanEmail() {
		return linkmanEmail;
	}
	public void setLinkmanEmail(String linkmanEmail) {
		this.linkmanEmail = linkmanEmail;
	}
	public String getReceiveTime() {
		return receiveTime;
	}
	public void setReceiveTime(String receiveTime) {
		this.receiveTime = receiveTime;
	}
	public Integer getIsTestReport() {
		return isTestReport;
	}
	public void setIsTestReport(Integer isTestReport) {
		this.isTestReport = isTestReport;
	}
	public String getTestReportRemark() {
		return testReportRemark;
	}
	public void setTestReportRemark(String testReportRemark) {
		this.testReportRemark = testReportRemark;
	}
	public Integer getIsReceiveConsumables() {
		return isReceiveConsumables;
	}
	public void setIsReceiveConsumables(Integer isReceiveConsumables) {
		this.isReceiveConsumables = isReceiveConsumables;
	}
	public String getKingdeeInterface() {
		return kingdeeInterface;
	}
	public void setKingdeeInterface(String kingdeeInterface) {
		this.kingdeeInterface = kingdeeInterface;
	}
	public Integer getIsQuickFreeze() {
		return isQuickFreeze;
	}
	public void setIsQuickFreeze(Integer isQuickFreeze) {
		this.isQuickFreeze = isQuickFreeze;
	}
	public BigDecimal getQuickFreezeAmount() {
		return quickFreezeAmount;
	}
	public void setQuickFreezeAmount(BigDecimal quickFreezeAmount) {
		this.quickFreezeAmount = quickFreezeAmount;
	}
	public Long getPrintDeliveryBatch() {
		return printDeliveryBatch;
	}
	public void setPrintDeliveryBatch(Long printDeliveryBatch) {
		this.printDeliveryBatch = printDeliveryBatch;
	}
	public String getSettlementRemark() {
		return settlementRemark;
	}
	public void setSettlementRemark(String settlementRemark) {
		this.settlementRemark = settlementRemark;
	}
	public Integer getPrintDeliveryQueue() {
		return printDeliveryQueue;
	}
	public void setPrintDeliveryQueue(Integer printDeliveryQueue) {
		this.printDeliveryQueue = printDeliveryQueue;
	}
	public Long getCreateUserId() {
		return createUserId;
	}
	public void setCreateUserId(Long createUserId) {
		this.createUserId = createUserId;
	}
	public Long getSettlementStatus() {
		return settlementStatus;
	}
	public void setSettlementStatus(Long settlementStatus) {
		this.settlementStatus = settlementStatus;
	}
	public String getStoreTypeName() {
		return storeTypeName;
	}
	public void setStoreTypeName(String storeTypeName) {
		this.storeTypeName = storeTypeName;
	}
	public String getStoreDistrictName() {
		return storeDistrictName;
	}
	public void setStoreDistrictName(String storeDistrictName) {
		this.storeDistrictName = storeDistrictName;
	}
	public String getSettlementCustomerName() {
		return settlementCustomerName;
	}
	public void setSettlementCustomerName(String settlementCustomerName) {
		this.settlementCustomerName = settlementCustomerName;
	}
	public String getStoreLineCode() {
		return storeLineCode;
	}
	public void setStoreLineCode(String storeLineCode) {
		this.storeLineCode = storeLineCode;
	}
	public String getStoreLineName() {
		return storeLineName;
	}
	public void setStoreLineName(String storeLineName) {
		this.storeLineName = storeLineName;
	}
	public String getDeliverymanName() {
		return deliverymanName;
	}
	public void setDeliverymanName(String deliverymanName) {
		this.deliverymanName = deliverymanName;
	}
	public String getSupervisorName() {
		return supervisorName;
	}
	public void setSupervisorName(String supervisorName) {
		this.supervisorName = supervisorName;
	}
	public String getOfficeDirector() {
		return officeDirector;
	}
	public void setOfficeDirector(String officeDirector) {
		this.officeDirector = officeDirector;
	}
	public String getRegionManager() {
		return regionManager;
	}
	public void setRegionManager(String regionManager) {
		this.regionManager = regionManager;
	}
	public String getStoreSalesName() {
		return storeSalesName;
	}
	public void setStoreSalesName(String storeSalesName) {
		this.storeSalesName = storeSalesName;
	}
	public String getRegionmanagerName() {
		return regionmanagerName;
	}
	public void setRegionmanagerName(String regionmanagerName) {
		this.regionmanagerName = regionmanagerName;
	}
	public String getOfficeDirectorName() {
		return officeDirectorName;
	}
	public void setOfficeDirectorName(String officeDirectorName) {
		this.officeDirectorName = officeDirectorName;
	}
	public String getStoreCompanyCode() {
		return storeCompanyCode;
	}
	public void setStoreCompanyCode(String storeCompanyCode) {
		this.storeCompanyCode = storeCompanyCode;
	}
	public String getStoreSortCode() {
		return storeSortCode;
	}
	public void setStoreSortCode(String storeSortCode) {
		this.storeSortCode = storeSortCode;
	}
	public Integer getStoreType() {
		return storeType;
	}
	public void setStoreType(Integer storeType) {
		this.storeType = storeType;
	}

	public Integer getBusinessType() {
		return businessType;
	}

	public void setBusinessType(Integer businessType) {
		this.businessType = businessType;
	}
}
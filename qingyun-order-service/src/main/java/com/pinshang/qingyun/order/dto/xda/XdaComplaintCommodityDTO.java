package com.pinshang.qingyun.order.dto.xda;

import com.pinshang.qingyun.order.enums.ComplaintTypeEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @Version 0.1.0
 * @Date 2020/12/30 14:43
 * @Copyright © 2019-2020 qm
 * @Description -- 类说明
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
public class XdaComplaintCommodityDTO {

    @ApiModelProperty(value = "投诉客户id，后台内部字典获取，前端忽略", example = "123456", required = true, hidden = true)
    private Long storeId;

    @ApiModelProperty(value = "投诉客户编码，后台内部字典获取，前端忽略", example = "123456code", required = true, hidden = true)
    private String storeCode;

    @ApiModelProperty(value = "送货日期", example = "2020-12-30", required = true)
    private String deliveryDate;

    @ApiModelProperty(value = "投诉单投诉类型 0-差异投诉，1-退货投诉", example = "1", required = true)
    private ComplaintTypeEnum complaintType;

    @ApiModelProperty(value = "投诉商品总金额，后台计算", example = "1236.96", required = true, hidden = true)
    private BigDecimal complaintTotalMoney;

    @ApiModelProperty(value = "投诉原因备注，投诉提交时填写", example = "少货缺货残货", required = true)
    private String complaintReason;

    @ApiModelProperty(value = "当天未投诉的商品明细列表", example = "", required = true)
    private List<XdaComplaintCommodityItemDTO> complaintItemList;

    @ApiModelProperty(value = "当天已发起投诉的商品明细列表", example = "", required = true)
    private List<XdaComplaintCommodityItemDTO> complaintCompleteItemList;

    @ApiModelProperty(value = "取货时间段", example = "00:00-00:06" )
    private String pickUpTimeRange;

    @ApiModelProperty(value = "配送批次")
    private String deliveryBatch;

    @ApiModelProperty(value = "物流中心id")
    private Long logisticsCenterId;

    private Integer businessType;

}

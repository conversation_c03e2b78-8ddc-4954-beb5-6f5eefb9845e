package com.pinshang.qingyun.order.vo.government;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.pinshang.qingyun.base.page.Pagination;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2021/2/4 14:38
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class GovernmentOrderTraceBackRepVO extends Pagination {

    /*** 工厂id */
    @ApiModelProperty(value = "工厂id")
    private Long factoryId;
    /*** 送货日期 */
    @ApiModelProperty(value = "送货日期")
    private String deliveryDate;

    @JsonIgnore
    @ApiModelProperty(hidden = true)
    private Long userId;
}

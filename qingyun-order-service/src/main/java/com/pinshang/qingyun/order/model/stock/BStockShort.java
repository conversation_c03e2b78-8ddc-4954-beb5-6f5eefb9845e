package com.pinshang.qingyun.order.model.stock;

import com.pinshang.qingyun.base.po.BaseIDPO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Table;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2024/03/12
 * @Version 1.0
 */
@Data
@Table(name = "t_order_stock_short_log")
public class BStockShort extends BaseIDPO {
    private Long storeId;

    @ApiModelProperty("店铺编号")
    private String storeCode;

    @ApiModelProperty("店铺名称")
    private String storeName;

    @ApiModelProperty("店铺类型_id")
    private Long storeTypeId;

    @ApiModelProperty("产品id")
    private Long commodityId;

    @ApiModelProperty("产品代码")
    private String commodityCode;

    @ApiModelProperty("产品名称")
    private String commodityName;

    @ApiModelProperty("型号规格")
    private String commoditySpec;

    @ApiModelProperty("包装规格")
    private BigDecimal commodityPackageSpec;

    @ApiModelProperty("销售箱装量")
    private BigDecimal salesBoxCapacity;

    @ApiModelProperty("单位")
    private String commodityUnitName;

    @ApiModelProperty("库存依据  1=依据大仓, 2=不限量订货, 3=限量供应")
    private Integer stockType;

    @ApiModelProperty("库存数量")
    private BigDecimal stockQuantity;

    @ApiModelProperty("需要的数量")
    private BigDecimal needQuantity;

    @ApiModelProperty("需要的数量")
    private Integer orderType;

    @ApiModelProperty("备注")
    private String remark;

    @ApiModelProperty("操作人")
    private Long createId;

    @ApiModelProperty("操作时间")
    private Date createTime;
}

package com.pinshang.qingyun.order.model.order;

import com.pinshang.qingyun.base.po.BaseIDPO;
import com.pinshang.qingyun.order.dto.miniGroupon.MiniGrouponAutoOrderIDTO;
import com.pinshang.qingyun.order.enums.ProductTypeEnums;
import lombok.Data;

import javax.persistence.Entity;
import javax.persistence.Table;
import javax.persistence.Transient;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Date;

@Entity
@Table(name = "t_order_list")
@Data
public class OrderList extends BaseIDPO {
	/**订单id **/
    private Long orderId;
	/**商品id **/
    private Long commodityId;
	/**商品数量 **/
    private BigDecimal commodityNum;
	/**促销后的商品金额(原含义-商品销售总金额) **/
    private BigDecimal totalPrice;
	/**促销后单价(原含义-单价) **/
    private BigDecimal commodityPrice;

    @Transient
    private String commodityPriceView;

    /**原价**/
    private BigDecimal originalPrice;

    /**特价**/
    private BigDecimal specialPrice;

    /**原金额(原价*数量)**/
    private BigDecimal originalTotalPrice;

	/**类型 1订单商品,2赠品,3配货商品,5特惠商品	—— 参见 ProductTypeEnums  **/
    private Integer type;
    /** 赠送方案id **/
    private Long giftModelId;
    /** 配货方案id **/
    private Long promotionId;

    /**鲜达特惠商品t_xda_specials_commodity_set**/
    private Long specialsId;

    /**备注 **/
    private String remark;

    /** 实发数量 **/
    private BigDecimal realQuantity;
    /** 实发总金额 **/
    private BigDecimal realTotalPrice;
    @Transient
    private String commodityName;
    @Transient
    private String commoditySpec;
    @Transient
    private String commodityCode;
    @Transient
    private Long giftModelOrPromotionId;
    @Transient
    private String modeIdAndCommodityIdKey;

    @Transient
    private Boolean orderDone = false;
    @Transient
    private Boolean giftDone = false;
    @Transient
    private Integer isWeight;
    @Transient
    private BigDecimal commodityPackageSpec;
    /**
     * 是否预售:0=否，1=是
     */
    private Integer presaleStatus;

    /**
     * 商品类型-1-非组合 2-组合  3-组合子品
     */

    private Integer combType = 1;
    /**
     * 属于组合商品id
     */
    private Long combCommodityId;

    /** 鲜达B端特价id */
    private Long pricePromotionId;

    /**
     * 金额占比
     */
    @Transient
    private BigDecimal proportion;

    @Transient
    private String commodityPackageKind;
    @Transient
    private String commodityRemark;

    @Transient
    private  Integer orderStatus;

    @Transient
    private Date orderTime;


    /** 优惠券分摊金额 */
    private BigDecimal couponDiscountAmount;

    /** 优惠券ID */
    private Long couponId;

    /** 用户券ID */
    private Long couponUserId;

    @Transient
    private String couponCode;

    public static OrderList initForMiniFroupon(MiniGrouponAutoOrderIDTO miniGroupon, Long orderId){
        OrderList orderList = new OrderList();
        orderList.setOrderId(orderId);
        orderList.setCommodityId(miniGroupon.getCommodityId());
        orderList.setCommodityNum(miniGroupon.getQuantity());
        orderList.setCommodityPrice(miniGroupon.getPromotionPrice()==null?miniGroupon.getOriginPrice():miniGroupon.getPromotionPrice());
        orderList.setTotalPrice(miniGroupon.getPromotionAmount());
        orderList.setType(ProductTypeEnums.PRODUCT.getCode());
        orderList.setRemark("订单商品");
        return orderList;
    }

    public String getCommodityPriceView() {
        if(commodityPrice != null){
            return commodityPrice.setScale(2, RoundingMode.HALF_UP).toString();
        }else {
            return "";
        }
    }
}
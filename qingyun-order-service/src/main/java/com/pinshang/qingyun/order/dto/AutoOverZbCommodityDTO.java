package com.pinshang.qingyun.order.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Author: sk
 * @Date: 2022/7/7
 * 规划池限制开关
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class AutoOverZbCommodityDTO {
    private String id;

    @ApiModelProperty("1:允许超出规划品项  0:禁止超出规划品项 ")
    private Integer optionValue;
}

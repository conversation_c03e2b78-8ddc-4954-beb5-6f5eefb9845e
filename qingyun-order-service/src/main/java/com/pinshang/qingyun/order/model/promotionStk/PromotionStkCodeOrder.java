package com.pinshang.qingyun.order.model.promotionStk;

import javax.persistence.Entity;
import javax.persistence.Table;

import com.pinshang.qingyun.base.po.BaseIDPO;

/**
 * 配比方案表
 **/
@Entity
@Table(name = "t_promotion_stk_code_order")
public class PromotionStkCodeOrder extends BaseIDPO {
	/**优惠表Id **/
    private Long promotionId;
	/**订单满多少钱使用 **/
    private Double orderReach;
	/**配送类型（0：累计配送，1：配送一次） **/
    private Long stkType;
	/**0：开启 1：关闭 **/
    private Integer status;
	/**创建者 **/
    private Long createUserId;
   
	public Double getOrderReach(){
        return orderReach;
    }
    public void setOrderReach(Double orderReach){
        this.orderReach = orderReach;
    }
    public Long getStkType(){
        return stkType;
    }
    public void setStkType(Long stkType){
        this.stkType = stkType;
    }
    public Integer getStatus(){
        return status;
    }
    public void setStatus(Integer status){
        this.status = status;
    }
    public Long getCreateUserId(){
        return createUserId;
    }
    public void setCreateUserId(Long createUserId){
        this.createUserId = createUserId;
    }
	 
	public Long getPromotionId() {
		return promotionId;
	}
	public void setPromotionId(Long promotionId) {
		this.promotionId = promotionId;
	}
    
}
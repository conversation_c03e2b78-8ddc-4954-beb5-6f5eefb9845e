package com.pinshang.qingyun.order.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * @Author: sk
 * @Date: 2022/5/16
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class AutoPreOrderAuditItemDTO {

    private Long shopId;

    private Long storeId;

    private Long commodityId;

    private BigDecimal stockQuantity;// 加货数量

    private Long createId;

    private Long consignmentId; // 代销商户id

    private Long orderId;
    private String orderCode;
}

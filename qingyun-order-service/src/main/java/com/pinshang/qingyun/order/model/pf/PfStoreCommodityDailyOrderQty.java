package com.pinshang.qingyun.order.model.pf;

import java.math.BigDecimal;
import java.util.Date;

import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;

import lombok.Getter;
import lombok.Setter;

@Entity
@Getter
@Setter
@Table(name = "t_pf_store_commodity_daily_order_qty")
public class PfStoreCommodityDailyOrderQty {

	@Id
	private Long id;
	
	private Long storeId;
	
	private Long commodityId;
	
	private Date orderDate;
	
	private BigDecimal orderQuantity;
	
}

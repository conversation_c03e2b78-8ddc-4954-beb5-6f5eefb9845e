package com.pinshang.qingyun.order.util;

import com.github.pagehelper.PageInfo;

import java.util.HashMap;
import java.util.Map;

/**
 * @Author: sk
 * @Date: 2021/1/21
 */
public class PageUtil {

    /**
     * 获取list分页开始条数、结束条数
     * @param totalNum 总记录数
     * @param pageNum  第几页
     * @param pageSize 每页条数
     * @param info  TablePageInfo对象
     * @return
     */
    public static void getPageMap(Integer totalNum, Integer pageNum, Integer pageSize, PageInfo info){
        //分页
        Integer totalPage = 0;
        if (totalNum > 0) {
            totalPage = totalNum % pageSize == 0 ? totalNum / pageSize : totalNum / pageSize + 1;
        }
        if (pageNum > totalPage) {
            pageNum = totalPage;
        }

        int startPoint = (pageNum - 1) * pageSize;
        int endPoint = startPoint + pageSize;
        if (totalNum <= endPoint) {
            endPoint = totalNum;
        }
        info.setPageNum(pageNum);
        info.setPageSize(pageSize);
        info.setPages(totalPage);
        info.setTotal(totalNum);
        info.setSize(pageSize);
        info.setHasNextPage(pageNum < totalPage);
        if(pageNum < totalPage){
            info.setNextPage(pageNum + 1);
        }
        if (pageNum > 1) {
            info.setPrePage(pageNum - 1);
        }
        if (pageSize == 0) {
            info.setStartRow(0);
            info.setEndRow(0);
        } else {
            info.setStartRow(info.getStartRow() + 1);
            //计算实际的endRow（最后一页的时候特殊）
            info.setEndRow(info.getStartRow() - 1 + pageSize);
        }
    }

}

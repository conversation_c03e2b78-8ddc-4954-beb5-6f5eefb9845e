package com.pinshang.qingyun.order.dto.order;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.format.DateTimeFormat;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.pinshang.qingyun.order.vo.order.SaleReturnOrderPicVO;
import com.pinshang.qingyun.renderer.annotation.FieldRender;
import com.pinshang.qingyun.renderer.constant.RenderFieldHelper;
import com.pinshang.qingyun.renderer.enums.FieldTypeEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * @ClassName SaleReturnDetailPageODTO
 * <AUTHOR>
 * @Date 2022/8/23 19:19
 * @Description SaleReturnDetailPageODTO
 * @Version 1.0
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class SaleReturnDetailNoStallPageODTO {
    @ExcelIgnore
    @ApiModelProperty("退货id")
    private Long returnId;
    @ApiModelProperty("所属部门")
    @ExcelProperty(value = "部门")
    @FieldRender(fieldType = FieldTypeEnum.SHOP_ORG,fieldName = RenderFieldHelper.Org.parentOrgName,keyName = "shopId")
    private String orgName;
    @ExcelProperty(value = "门店")
    @ApiModelProperty("门店名称")
    private String shopName;
    @ExcelIgnore
    @ApiModelProperty("门店id")
    private Long shopId;
    @DateTimeFormat("yyyy/MM/dd")
    @JsonFormat(pattern="yyyy/MM/dd",timezone = "GMT+8")
    @ExcelProperty(value = "退货日期")
    @ApiModelProperty("退货日期")
    private Date returnDate;
    @ExcelProperty(value = "退货编号")
    @ApiModelProperty("退货编号")
    private String returnCode;

    @ApiModelProperty("退货状态")
    @ExcelIgnore
    private Integer status;
    @ApiModelProperty("退货状态-文字")
    @ExcelProperty(value = "退货单状态")
    private String statusStr;
    @ApiModelProperty("商品编号")
    @ExcelProperty(value = "商品编号")
    private String commodityCode;
    @ApiModelProperty("条形码")
    @ExcelProperty(value = "条形码")
    private String barCode;
    @ExcelProperty(value = "商品名称")
    @ApiModelProperty("商品名称")
    private String commodityName;
    @ExcelProperty(value = "规格")
    @ApiModelProperty("规格")
    private String commoditySpec;
    @ExcelIgnore
    @ApiModelProperty("商品id")
    private Long commodityId;
    @ExcelProperty(value = "商品分类")
    @ApiModelProperty("商品分类")
    private String cateName;
    @ApiModelProperty("单位")
    @ExcelProperty(value = "单位")
    private String commodityUnit;
    @ApiModelProperty("单价")
    @ExcelProperty(value = "单价")
    private BigDecimal price;
    @ApiModelProperty("申请退货/少货数量")
    @ExcelProperty(value = "申请退货/少货数量")
    private BigDecimal returnQuantity;
    @ApiModelProperty("申请退货/少货金额")
    @ExcelProperty(value = "申请退货/少货金额")
    private BigDecimal totalPrice;
    @ApiModelProperty("配送单照片")
    @ExcelIgnore
    private List<SaleReturnOrderPicVO> picUrl;
    @ApiModelProperty("证明视频")
    @ExcelIgnore
    private String videoUrl;
    @ApiModelProperty("证明视频")
    @ExcelIgnore
    private String visitVideoUrl;
    @ApiModelProperty("配送单照片")
    @ExcelProperty(value = "配送单照片")
    private String hasPic;
    @ApiModelProperty("证明视频")
    @ExcelProperty(value = "证明视频")
    private String hasVideo;
    @ApiModelProperty("退货原因id")
    @ExcelIgnore
    private Integer returnReasonId;
    @ApiModelProperty("退货原因")
    @ExcelProperty(value = "退货原因")
    private String returnReason;
    @ApiModelProperty("门店备注")
    @ExcelProperty(value = "门店备注")
    private String remark;
    @ApiModelProperty("实退数量")
    @ExcelProperty(value = "实退数量")
    private BigDecimal realReturnQuantity;
    @ApiModelProperty("实退金额")
    @ExcelProperty(value = "实退金额")
    private BigDecimal returnAmount;
    @ApiModelProperty("责任方id")
    @ExcelIgnore
    private Integer reTypeId;
    @ApiModelProperty("责任方")
    @ExcelProperty(value = "责任方")
    private String reType;
    @ApiModelProperty("索赔金额")
    @ExcelProperty(value = "索赔金额")
    private BigDecimal compensatePrice;
    @ApiModelProperty("审核备注")
    @ExcelProperty(value = "审核备注")
    private String auditRemark;
    @ExcelIgnore
    @ApiModelProperty("退货人id")
    private Long returnUserId;
    @ApiModelProperty("退货人")
    @ExcelProperty(value = "退货人")
    @FieldRender(fieldType = FieldTypeEnum.USER,fieldName = RenderFieldHelper.User.realName,keyName = "returnUserId")
    private String returnUserName;
    @ApiModelProperty("退货时间")
    @ExcelProperty(value = "退货时间")
    private String returnTime;
    @ExcelIgnore
    @ApiModelProperty("审核人id")
    private Long auditUserId;
    @ApiModelProperty("审核人")
    @ExcelProperty(value = "审核人")
    @FieldRender(fieldType = FieldTypeEnum.USER,fieldName = RenderFieldHelper.User.realName,keyName = "auditUserId")
    private String auditUserName;
    @ApiModelProperty("审核时间")
    @ExcelProperty(value = "审核时间")
    private String auditTime;
    @ExcelIgnore
    @ApiModelProperty("取消人id")
    private Long cancelUserId;
    @ApiModelProperty("取消人")
    @ExcelProperty(value = "取消人")
    @FieldRender(fieldType = FieldTypeEnum.USER,fieldName = RenderFieldHelper.User.realName,keyName = "cancelUserId")
    private String cancelUserName;
    @ApiModelProperty("取消时间")
    @ExcelProperty(value = "取消时间")
    private String cancelTime;

    public String getAuditUserName() {
        return 2 == this.getStatus() || 3 == this.getStatus() ? auditUserName : "";
    }

    public String getAuditTime() {
        return 2 == this.getStatus() || 3 == this.getStatus() ? auditTime : "";
    }

    public String getCancelUserName() {
        return 0 == this.getStatus() ? cancelUserName : "";
    }

    public String getCancelTime() {
        return 0 == this.getStatus() ? cancelTime : "";
    }
}

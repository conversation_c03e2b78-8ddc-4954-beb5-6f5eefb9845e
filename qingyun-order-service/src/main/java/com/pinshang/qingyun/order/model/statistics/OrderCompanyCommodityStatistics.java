package com.pinshang.qingyun.order.model.statistics;

import com.alibaba.excel.annotation.ExcelProperty;
import com.pinshang.qingyun.base.po.BaseIDPO;
import lombok.Data;

import javax.persistence.Entity;
import javax.persistence.Table;
import java.math.BigDecimal;
import java.util.Date;

@Entity
@Data
@Table(name="t_order_company_commodity_statistics")
public class OrderCompanyCommodityStatistics extends BaseIDPO {

	/** 送货日期 */
	private Date orderTime;

	/** 公司ID */
	private Long companyId;

	/** 商品ID */
	private Long commodityId;

	/** 工厂ID */
	private Long factoryId;

	/** 订货数量 */
	@ExcelProperty(value = "数量小计")
	private BigDecimal orderQuantity;

	/** 订货金额 */
	@ExcelProperty(value = "金额小计")
	private BigDecimal orderTotalAmount;

	/** 实际数量 */
	private BigDecimal realQuantity;

	/** 实际金额 */
	private BigDecimal realTotalAmount;

}
package com.pinshang.qingyun.order.enums;

import com.pinshang.qingyun.box.utils.EnumUtils;

import java.util.EnumSet;

public enum PromotionStkTypeEnum {

	//条件类型:0.累计赠送,1.赠送一次
	GIVE_ONE(1, "赠送一次"),
	CUMULATIVE_GIVE(0,"累计赠送");

	private Integer code;
	private String remark;

	private PromotionStkTypeEnum(Integer code, String remark) {
		this.setCode(code);
		this.setRemark(remark);
	}

	public static PromotionStkTypeEnum fromName(Integer code) {
		return EnumUtils.fromEnumProperty(PromotionStkTypeEnum.class, "code", code);
	}

	public static EnumSet<PromotionStkTypeEnum> allList() {
		EnumSet<PromotionStkTypeEnum> adverPositionList = EnumSet.allOf(PromotionStkTypeEnum.class);
		return adverPositionList;
	}

	public Integer getCode() {
		return code;
	}

	public void setCode(Integer code) {
		this.code = code;
	}

	public String getRemark() {
		return remark;
	}

	public void setRemark(String remark) {
		this.remark = remark;
	}
	
	
}

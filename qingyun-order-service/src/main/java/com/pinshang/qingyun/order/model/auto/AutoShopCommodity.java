package com.pinshang.qingyun.order.model.auto;

import com.pinshang.qingyun.base.po.BaseIDPO;
import lombok.Data;

import javax.persistence.Entity;
import javax.persistence.Table;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 */
@Entity
@Data
@Table(name="t_md_auto_shop_commodity")
public class AutoShopCommodity extends BaseIDPO {

    private Long shopId;

    private Long commodityId;

    /**
     * 安全库存
     */
    private BigDecimal stockQuantity;

    private Long createId;

    private Date createTime;

    private Long updateId;

    private Date updateTime;
}

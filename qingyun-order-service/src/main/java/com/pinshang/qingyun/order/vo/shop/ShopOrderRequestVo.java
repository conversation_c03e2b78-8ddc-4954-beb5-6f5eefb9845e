package com.pinshang.qingyun.order.vo.shop;

import java.util.List;

import com.pinshang.qingyun.box.utils.DateTimeUtil;

public class ShopOrderRequestVo {
	private Long  shopId;
	private List<ShopOrderItemRequestVo> items;
	/** 企业id **/
	private Long enterpriseId;
	/**订单时间 **/
    private String orderTime = DateTimeUtil.getNowDayDate() ;
    /** 打印类型(1：本地,2：送货员,3：不打印) */
    private Integer printType = 3;
    /**打印份数*/
    private Integer printNum = 1;
	/**备注 **/
    private String orderRemark;
	/** 0:普通订单 1：补货订单 **/
	private int modeType = 0 ;
	//创建人
	private Long userId;
	
	//物流模式
	private Integer logisticsModel;
	//供应商id
	private Long supplierId;
	
	private Long warehouseId;

	public Long getShopId() {
		return shopId;
	}

	public void setShopId(Long shopId) {
		this.shopId = shopId;
	}

	public List<ShopOrderItemRequestVo> getItems() {
		return items;
	}

	public void setItems(List<ShopOrderItemRequestVo> items) {
		this.items = items;
	}

	public Long getEnterpriseId() {
		return enterpriseId;
	}

	public void setEnterpriseId(Long enterpriseId) {
		this.enterpriseId = enterpriseId;
	}

	public String getOrderTime() {
		return orderTime;
	}

	public void setOrderTime(String orderTime) {
		this.orderTime = orderTime;
	}

	public Integer getPrintType() {
		return printType;
	}

	public void setPrintType(Integer printType) {
		this.printType = printType;
	}

	public Integer getPrintNum() {
		return printNum;
	}

	public void setPrintNum(Integer printNum) {
		this.printNum = printNum;
	}

	public String getOrderRemark() {
		return orderRemark;
	}

	public void setOrderRemark(String orderRemark) {
		this.orderRemark = orderRemark;
	}

	public int getModeType() {
		return modeType;
	}

	public void setModeType(int modeType) {
		this.modeType = modeType;
	}

	public Long getUserId() {
		return userId;
	}

	public void setUserId(Long userId) {
		this.userId = userId;
	}

	public Integer getLogisticsModel() {
		return logisticsModel;
	}

	public void setLogisticsModel(Integer logisticsModel) {
		this.logisticsModel = logisticsModel;
	}

	public Long getSupplierId() {
		return supplierId;
	}

	public void setSupplierId(Long supplierId) {
		this.supplierId = supplierId;
	}

	public Long getWarehouseId() {
		return warehouseId;
	}

	public void setWarehouseId(Long warehouseId) {
		this.warehouseId = warehouseId;
	}
	
}

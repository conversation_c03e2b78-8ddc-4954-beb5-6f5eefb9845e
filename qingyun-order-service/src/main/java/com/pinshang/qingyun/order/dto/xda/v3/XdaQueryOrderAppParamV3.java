package com.pinshang.qingyun.order.dto.xda.v3;

import com.pinshang.qingyun.base.page.Pagination;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @Date 2023/12/28 13:47
 */
@Data
@ApiModel
public class XdaQueryOrderAppParamV3 extends Pagination {
    @ApiModelProperty("流程状态(鲜达新加) 7=待发货11=出库中15=配送中19=配送完成")
    private String processStatus;
    @ApiModelProperty(value = "订单编码", hidden = true)
    private String orderCode;
    @ApiModelProperty(value = "客户id", hidden = true)
    private Long storeId;
}

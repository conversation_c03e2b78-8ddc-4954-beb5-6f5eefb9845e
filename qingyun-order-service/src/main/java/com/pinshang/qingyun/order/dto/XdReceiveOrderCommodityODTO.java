package com.pinshang.qingyun.order.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * @Author: sk
 * @Date: 2020/3/18
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class XdReceiveOrderCommodityODTO {

      private Long subOrderId;
      private String subOrderCode;
      private Long subOrderItemId;
      private Long commodityId;
      private BigDecimal quantity;
      private BigDecimal realDeliveryQuantity;
      private BigDecimal realReceiveQuantity;
      private BigDecimal price;
      private String remark;

      @ApiModelProperty("包装规格")
      private BigDecimal commodityPackageSpec;
      private String orderRemark;
      private String commodityName;
}

package com.pinshang.qingyun.order.model.shop;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.math.BigDecimal;
import java.util.Date;

@Entity
@Table(name="t_complaint")
public class Complaint {
	@Id
	@Column(name = "uid")
	private String id;
	
	private Integer enabled= 1;
	private Date createdAt;
	//=2为门店直送投诉单
	private Integer complaintType;
	//投诉单code（时间戳+(01)）,如果有循环则需要加01
	private String complaintCode;
	//投诉单状态 =1
	private Integer complaintHandleStatus;
	//备注,那个订单号
	private String complaintRemark;
	//投诉人
	private String complaintUser;
	//创建人
	private String createId;
	//发货时间
	private String deliveryDate;
	//手机号码,创建订单的人的手机号
	private String linkmanMobile;
	//客户code
	private String storeCode;
	//客户id
	private String storeId;
	//投诉总金额;
	private BigDecimal complaintTotalMoney;
	//批次号（时间戳+(01)）
	private String batchNo;
	//修改时间
	private Date updateTime;
	public Date getCreatedAt() {
		return createdAt;
	}
	public void setCreatedAt(Date createdAt) {
		this.createdAt = createdAt;
	}
	public Integer getComplaintType() {
		return complaintType;
	}
	public void setComplaintType(Integer complaintType) {
		this.complaintType = complaintType;
	}
	public String getComplaintCode() {
		return complaintCode;
	}
	public void setComplaintCode(String complaintCode) {
		this.complaintCode = complaintCode;
	}
	public Integer getComplaintHandleStatus() {
		return complaintHandleStatus;
	}
	public void setComplaintHandleStatus(Integer complaintHandleStatus) {
		this.complaintHandleStatus = complaintHandleStatus;
	}
	public String getComplaintRemark() {
		return complaintRemark;
	}
	public void setComplaintRemark(String complaintRemark) {
		this.complaintRemark = complaintRemark;
	}
	public String getComplaintUser() {
		return complaintUser;
	}
	public void setComplaintUser(String complaintUser) {
		this.complaintUser = complaintUser;
	}
	public String getCreateId() {
		return createId;
	}
	public void setCreateId(String createId) {
		this.createId = createId;
	}
	public String getDeliveryDate() {
		return deliveryDate;
	}
	public void setDeliveryDate(String deliveryDate) {
		this.deliveryDate = deliveryDate;
	}
	public String getLinkmanMobile() {
		return linkmanMobile;
	}
	public void setLinkmanMobile(String linkmanMobile) {
		this.linkmanMobile = linkmanMobile;
	}
	public String getStoreCode() {
		return storeCode;
	}
	public void setStoreCode(String storeCode) {
		this.storeCode = storeCode;
	}
	public String getStoreId() {
		return storeId;
	}
	public void setStoreId(String storeId) {
		this.storeId = storeId;
	}
	public BigDecimal getComplaintTotalMoney() {
		return complaintTotalMoney;
	}
	public void setComplaintTotalMoney(BigDecimal complaintTotalMoney) {
		this.complaintTotalMoney = complaintTotalMoney;
	}
	public String getBatchNo() {
		return batchNo;
	}
	public void setBatchNo(String batchNo) {
		this.batchNo = batchNo;
	}
	public String getId() {
		return id;
	}
	public void setId(String id) {
		this.id = id;
	}
	public Integer getEnabled() {
		return enabled;
	}
	public void setEnabled(Integer enabled) {
		this.enabled = enabled;
	}
	public Date getUpdateTime() {
		return updateTime;
	}
	public void setUpdateTime(Date updateTime) {
		this.updateTime = updateTime;
	}
	
	
}

package com.pinshang.qingyun.order.vo.order;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.pinshang.qingyun.order.mapper.entry.order.DeliveryBatchEntry;
import io.swagger.annotations.ApiModelProperty;

import java.util.ArrayList;
import java.util.List;

public class OrderRequestVo{
	/** 企业id **/
	private Long enterpriseId;
	/**商铺id **/
    private String storeId;
	/**订单时间 **/
    private String orderTime;
    /** 打印类型(1：本地,2：送货员,3：不打印) */
    private Integer printType = 3;
    /**打印份数*/
    private Integer printNum = 1;
	/**备注 **/
    private String orderRemark;
	/** 0:普通订单 1：补货订单 **/
	private int modeType = 0 ;
	
	//创建人
	private Long userId;
	/** 创建姓名 */
	private String createName;
	
	private List<OrderItemRequestVo> itemsList;
	
	//物流模式
	private Integer logisticsModel;
	//供应商id
	private Long supplierId;
	private Long warehouseId;
	//预订单id
	private Long preOrderId;
	
	 private Long shopStoreId;
	 
	 private String deliveryBatch;

	private String deleveryTimeRange;

	private Long consignmentId; // 代销商户id
	/** 档口id */
	private Long stallId;

	/** 物流中心ID */
	private Long logisticsCenterId;
	/**物流中心名称*/
	private String logisticsCenterName;

	List<DeliveryBatchEntry> deliveryBatchList;

	public List<DeliveryBatchEntry> getDeliveryBatchList() {
		return deliveryBatchList;
	}

	public void setDeliveryBatchList(List<DeliveryBatchEntry> deliveryBatchList) {
		this.deliveryBatchList = deliveryBatchList;
	}

	public Long getLogisticsCenterId() {
		return logisticsCenterId;
	}
	public void setLogisticsCenterId(Long logisticsCenterId) {
		this.logisticsCenterId = logisticsCenterId;
	}
	public String getLogisticsCenterName() {
		return logisticsCenterName;
	}
	public void setLogisticsCenterName(String logisticsCenterName) {
		this.logisticsCenterName = logisticsCenterName;
	}

	public Long getStallId() {
		return stallId;
	}

	public void setStallId(Long stallId) {
		this.stallId = stallId;
	}

	public Long getConsignmentId() {
		return consignmentId;
	}

	public void setConsignmentId(Long consignmentId) {
		this.consignmentId = consignmentId;
	}

	public String getDeleveryTimeRange() {
		return deleveryTimeRange;
	}

	public void setDeleveryTimeRange(String deleveryTimeRange) {
		this.deleveryTimeRange = deleveryTimeRange;
	}

	public Integer getPrintType() {
		return printType;
	}

	public void setPrintType(Integer printType) {
		this.printType = printType;
	}

	public Integer getPrintNum() {
		return printNum;
	}

	public void setPrintNum(Integer printNum) {
		this.printNum = printNum;
	}

	public String getOrderRemark() {
		return orderRemark;
	}

	public void setOrderRemark(String orderRemark) {
		this.orderRemark = orderRemark;
	}

	public List<OrderItemRequestVo> getItemsList() {
		return itemsList==null? itemsList = new ArrayList<>():itemsList;
	}

	public void setItemsList(List<OrderItemRequestVo> itemsList) {
		this.itemsList = itemsList;
	}

	public String getOrderTime() {
		return orderTime;
	}

	public void setOrderTime(String orderTime) {
		this.orderTime = orderTime;
	}

	public int getModeType() {
		return modeType;
	}

	public void setModeType(int modeType) {
		this.modeType = modeType;
	}

	public String getStoreId() {
		return storeId;
	}

	public void setStoreId(String storeId) {
		this.storeId = storeId;
	}

	public Long getEnterpriseId() {
		return enterpriseId;
	}

	public void setEnterpriseId(Long enterpriseId) {
		this.enterpriseId = enterpriseId;
	}

	public Long getUserId() {
		return userId;
	}

	public void setUserId(Long userId) {
		this.userId = userId;
	}

	public Integer getLogisticsModel() {
		return logisticsModel;
	}

	public void setLogisticsModel(Integer logisticsModel) {
		this.logisticsModel = logisticsModel;
	}

	public Long getSupplierId() {
		return supplierId;
	}

	public void setSupplierId(Long supplierId) {
		this.supplierId = supplierId;
	}

	public Long getWarehouseId() {
		return warehouseId;
	}

	public void setWarehouseId(Long warehouseId) {
		this.warehouseId = warehouseId;
	}

	public Long getPreOrderId() {
		return preOrderId;
	}

	public void setPreOrderId(Long preOrderId) {
		this.preOrderId = preOrderId;
	}

	public Long getShopStoreId() {
		return shopStoreId;
	}

	public void setShopStoreId(Long shopStoreId) {
		this.shopStoreId = shopStoreId;
	}

	public String getDeliveryBatch() {
		return deliveryBatch;
	}

	public void setDeliveryBatch(String deliveryBatch) {
		this.deliveryBatch = deliveryBatch;
	}

	public String getCreateName() {
		return createName;
	}

	public void setCreateName(String createName) {
		this.createName = createName;
	}
}

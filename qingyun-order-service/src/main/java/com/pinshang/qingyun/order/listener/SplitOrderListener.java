package com.pinshang.qingyun.order.listener;

import com.alibaba.fastjson.JSON;
import com.pinshang.qingyun.kafka.base.KafkaMessageWrapper;
import com.pinshang.qingyun.kafka.base.KafkaTopicConstant;
import com.pinshang.qingyun.order.service.SplitOrderService;
import com.pinshang.qingyun.order.service.WeChatSendMessageService;
import com.pinshang.qingyun.order.vo.splitOrder.SplitOrderKafkaVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.stereotype.Component;

import java.util.HashMap;

@Component
@Slf4j
public class SplitOrderListener {
	@Autowired
	private SplitOrderService splitOrderService;
    @Autowired
    private WeChatSendMessageService weChatSendMessageService;

	/**
     * 监听kafka消息(拆单) 
     */
    @KafkaListener(id="${application.name.switch}" + KafkaTopicConstant.SPLIT_TOPIC,topics = "${application.name.switch}" + KafkaTopicConstant.SPLIT_TOPIC)
    public void splitListen(String message) {
         this.consumeMessage(message);
    }

    /**
     * 下单拆单消息监听，兼容
     * @param message
     */
    @KafkaListener(id=KafkaTopicConstant.SPLIT_TOPIC,topics = KafkaTopicConstant.SPLIT_TOPIC)
    public void listen(String message) {
        log.info("\n====拆单消息监听，兼容原来的topic============");
        this.consumeMessage(message);
    }

    /**
     * 注意：老的1期和生鲜发的消息，消息记录保存在pinshang库 t_mq_message表
     *      新的发消息不再保存在pinshang库，保存在pinshang_edge库中
     *
     */
    public void consumeMessage(String message){
        log.info("处理拆单消息: {}", message.toString());
        KafkaMessageWrapper messageWrapper = JSON.parseObject(message, KafkaMessageWrapper.class);
        SplitOrderKafkaVo vo = JSON.parseObject(messageWrapper.getData().toString(), SplitOrderKafkaVo.class);
        try {
            // 兼容老的1期和生鲜
            if(vo.getType() == null) {
                vo.setType(messageWrapper.getOptionType());
            }

            // 初始化
            if(vo.getCommWarehouseMap() == null) {
                vo.setCommWarehouseMap(new HashMap());
            }

            splitOrderService.splitOrder(vo);

        } catch (Exception e) {
            weChatSendMessageService.sendWeChatMessage("处理拆单消息异常,订单id " + vo.getOrderId() + "消息类型 " + vo.getType());
            log.error("处理拆单消息异常: {}",JSON.toJSONString(vo),e);
        }
    }
    
}

package com.pinshang.qingyun.order.dto.shopcart.v4;

import com.pinshang.qingyun.base.enums.YesOrNoEnums;
import io.swagger.annotations.ApiModel;
import lombok.Data;

import java.util.Date;

/**
 * @<PERSON><PERSON>
 * @Date 2024/3/4 14:01
 */
@Data
@ApiModel
public class ShoppingCartAddV4IDTO extends ShoppingCartV4IDTO{

    public static ShoppingCartAddV4IDTO builder(Long storeId, Date orderTime, YesOrNoEnums shopCartPage){
        ShoppingCartAddV4IDTO shoppingCartAddV4IDTO = new ShoppingCartAddV4IDTO();
        shoppingCartAddV4IDTO.setStoreId(storeId);
        shoppingCartAddV4IDTO.setOrderTime(orderTime);
        shoppingCartAddV4IDTO.setShopCartPage(shopCartPage);
        return shoppingCartAddV4IDTO;
    }
}

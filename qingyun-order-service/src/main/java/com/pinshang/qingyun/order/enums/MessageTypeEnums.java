package com.pinshang.qingyun.order.enums;

/**
 * 1短信  2 广播
 */
public enum MessageTypeEnums {

	SMS("短信", 1),
    BROADCAST("广播", 2);

    private int code;
    private String name;

    MessageTypeEnums(String name, int code) {
        this.name = name;
        this.code = code;
    }

    public static String getName(int code) {
        for (MessageTypeEnums es : MessageTypeEnums.values()) {
            if (code == es.getCode()) {
                return es.name;
            }
        }
        return null;
    }

    public String getName() {
        return name;
    }

    public int getCode() {
        return code;
    }
}

package com.pinshang.qingyun.order.dto.xda.v4;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.Objects;

/**
 * <p>
 * 优惠券信息 实体类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-15
 */
@Data
public class XdaCouponUserInfoODTO {
    /**
     * 优惠券id
     */
    @ApiModelProperty(value = "优惠券id")
    private Long couponId;
    /**
     * 优惠券编码
     */
    @ApiModelProperty(value = "优惠券编码")
    private String couponCode;
    /**
     * 优惠券名称
     */
    @ApiModelProperty(value = "优惠券名称")
    private String couponName;
    /**
     * 优惠券类型：1-全场券、2-品类券、3-商品券	—— 参见 XSCouponTypeEnums
     */
    @ApiModelProperty(value = "优惠券类型：1-全场券、2-品类券、3-商品券	—— 参见 XSCouponTypeEnums")
    public Integer couponType;
    /**
     * 优惠券分类 1=满减券,2=兑换券 XSCouponClassEnum
     */
    @ApiModelProperty(value = "优惠券分类 1=满减券,2=兑换券 XSCouponClassEnum")
    private Integer couponClass;
    /**
     * 优惠券描述
     */
    @ApiModelProperty(value = "优惠券描述")
    private String couponDesc;

    // 商品金额满多少钱
    @ApiModelProperty(value = "商品金额满多少钱")
    private BigDecimal amountFull;

    // 1-金额减,2-金额按比例打折 3-满赠(group_class=2
    @ApiModelProperty(value = "1-金额减,2-金额按比例打折 3-满赠(group_class=2")
    private Integer couponRuleType;

    // 减少多少钱
    @ApiModelProperty(value = "减少多少钱")
    private BigDecimal reduceAmount;

    // 打折多少:如：8折输入80
    @ApiModelProperty(value = "打折多少:如：8折输入80")
    private Integer discount;

    /**
     * 是否选中，1是 0否
     */
    @ApiModelProperty(value = "是否选中，1是 0否")
    private Integer selected = 0;

    /**
     * 系统推荐券，1是 0否
     */
    @ApiModelProperty(value = "系统推荐券，1是 0否")
    private Integer recommended = 0;

    /**
     * 用户券id
     */
    @ApiModelProperty(value = "用户券id")
    private Long couponUserId;

    /**
     * 用户券开始时间
     */
    @ApiModelProperty(value = "用户券生效开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date beginTime;

    /**
     * 用户券结束时间
     */
    @ApiModelProperty(value = "用户券生效结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date endTime;

    /**
     * 用户券创建时间
     */
    @ApiModelProperty(value = "用户券创建时间")
    private Date couponUserCreateTime;

    public BigDecimal getReduceAmount() {
        if (Objects.nonNull(reduceAmount)) {
            return new BigDecimal(reduceAmount.stripTrailingZeros().toPlainString());
        }
        return null;
    }

    public BigDecimal getAmountFull() {
        if (Objects.nonNull(amountFull)) {
            return new BigDecimal(amountFull.stripTrailingZeros().toPlainString());
        }
        return null;
    }

}

package com.pinshang.qingyun.order.vo.shop;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2017/6/12.
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class Quantity {

	private String itemId;
	
    private String commodityCode;

    private String commodityName;

    @ApiModelProperty("商品id")
    private String commodityId;

    private BigDecimal realReceiveQuantity;

    @ApiModelProperty("实收份数")
    private Integer realReceiveNumber;

    @ApiModelProperty("实发数量")
    private BigDecimal realDeliveryQuantity;
    
    private Boolean isAdd;

    @ApiModelProperty("价格")
    private BigDecimal price;
    
    private BigDecimal requireQuantity;

    /** 包装规格 */
    private BigDecimal commodityPackageSpec;

    @ApiModelProperty("收货正常品份数")
    private BigDecimal normalShares;

    @ApiModelProperty("实收正常品数量")
    private BigDecimal normalQuantity;


    @ApiModelProperty("收货异常品份数")
    private BigDecimal abnormalShares;

    @ApiModelProperty("收货异常品数量")
    private BigDecimal abnormalQuantity;
}

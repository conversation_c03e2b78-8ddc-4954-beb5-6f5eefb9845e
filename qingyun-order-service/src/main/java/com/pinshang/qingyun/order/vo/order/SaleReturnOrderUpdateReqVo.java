package com.pinshang.qingyun.order.vo.order;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2018/10/15 16:48.
 * @blog http://linuxsogood.org
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class SaleReturnOrderUpdateReqVo {
    @ApiModelProperty("退货单ID")
    private Long id;
    @ApiModelProperty("退货单状态：0＝取消，1＝待入库(待确认)，2＝已入库（已确认）,3＝入库中")
    private Integer status;
    @ApiModelProperty("收货人ID")
    private Long updateId;
}

<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.pinshang.qingyun.order.mapper.bcountry.PlanCommodityMapper">


    <select id="queryPlanCommodityList" parameterType="com.pinshang.qingyun.order.dto.bcountry.PlanCommodityQueryIDTO" resultType="com.pinshang.qingyun.order.dto.bcountry.PlanCommodityODTO">
        SELECT
            t.commodity_id commodityId,
            t.sync_status,
            t.sync_time,
            c.`commodity_code`,
            c.`commodity_name`,
            c.`commodity_spec`,
            c.quality_days,
            c.quality_unit
        FROM t_plan_commodity t
        left join t_commodity c on c.id = t.commodity_id
        WHERE 1=1
        <if test="commodityId != null">
            AND t.commodity_id = #{commodityId}
        </if>
        <if test="commodityIdList !=null and commodityIdList.size > 0">
            and t.commodity_id in
            <foreach collection="commodityIdList" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
        <if test="syncStatus != null">
            AND t.sync_status = #{syncStatus}
        </if>

        <if test="cateId1 != null">
            AND c.commodity_first_id = #{cateId1}
        </if>
        <if test="cateId2 != null">
            AND c.commodity_second_id = #{cateId2}
        </if>
        <if test="cateId3 != null">
            AND c.commodity_third_id = #{cateId3}
        </if>

    </select>



</mapper>
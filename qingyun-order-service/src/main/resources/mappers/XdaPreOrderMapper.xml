<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.pinshang.qingyun.order.mapper.XdaPreOrderMapper">

    <select id="queryXdaNoPayPreOrder" resultType="com.pinshang.qingyun.order.dto.xda.v4.XdaOrderAppV4ODTO">
        SELECT
            tor.id as orderId,
            tor.order_code orderCode,
            tor.order_amount summation,
            tor.order_time orderTime,
            tor.create_time orderCreateTime,
            tor.order_type AS orderType,
            count(DISTINCT tol.commodity_id) varietySum,
            tor.delivery_time_range deliveryTimeRange,
            tor.order_duration_time orderDurationTime,
            tor.bill_code billCode
        FROM
          t_xda_pre_order tor
        INNER JOIN t_xda_pre_order_list tol ON tor.id = tol.order_id
        where tor.order_time &gt;= DATE_SUB(CURRENT_DATE(),INTERVAL 92 DAY)
        and tor.pay_status = 0
         and tor.store_id = #{storeId}
         and tol.comb_type in(1, 2)
        limit 1
    </select>


    <select id="countXdaNoPayPreOrder" resultType="java.lang.Integer">
        SELECT
            count(1)
        FROM
            t_xda_pre_order tor
                INNER JOIN t_xda_pre_order_list tol ON tor.id = tol.order_id
        where tor.order_time &gt;= DATE_SUB(CURRENT_DATE(),INTERVAL 92 DAY)
          and tor.pay_status = 0
          and tor.store_id = #{storeId}
            limit 1
    </select>

    <select id="queryXdaPreOrderItemXdaAppV4" resultType="com.pinshang.qingyun.order.dto.xda.v4.XdaOrderItemAppV4ODTO">
        SELECT
            tor.id as orderId,
            tol.commodity_id as commodityId,
            sum(tol.commodity_price) as commodityPrice,
            sum(tol.commodity_num) as commodityNum,
            tol.type
        FROM
          t_xda_pre_order tor
        INNER JOIN t_xda_pre_order_list tol ON tor.id = tol.order_id
        WHERE  tol.comb_type in(1, 2)
          and tor.id in
        <foreach collection="orderIds" index="index" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
        group by tor.id, tol.commodity_id,tol.type
    </select>
</mapper>
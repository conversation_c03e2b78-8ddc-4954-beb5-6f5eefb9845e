<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.pinshang.qingyun.order.mapper.bigShop.DdReceiveDocMapper">

    <select id="getReceiveDocList" resultType="com.pinshang.qingyun.order.dto.bigShop.DdReceiveDocODTO">
        SELECT
            t.id docId,
            t.doc_code,
            t.order_time,
            t.shop_id,
            t.stall_id,
            t.delivery_batch,
            t.doc_status,
            t.receive_user_id,
            t.receive_time
        from t_dd_receive_doc t
        where t.order_time = #{idto.orderTime}  and t.shop_id = #{idto.shopId}

        <if test="idto.stallId != null">
            and t.stall_id = #{idto.stallId}
        </if>
        <if test="idto.stallIdList != null and idto.stallIdList.size > 0">
            AND t.stall_id IN
            <foreach collection="idto.stallIdList" index="index" item="stallid" open="(" separator="," close=")">
                #{stallid}
            </foreach>
        </if>

        <if test="idto.deliveryBatch != null">
            and t.delivery_batch = #{idto.deliveryBatch}
        </if>

        <if test="idto.docStatus != null">
            and t.doc_status = #{idto.docStatus}
        </if>


        <if test="idto.relationOrderTime != null">
            and t.order_time = #{idto.relationOrderTime}
        </if>
        <if test="idto.relationShopId != null">
            and t.shop_id = #{idto.relationShopId}
        </if>
        <if test="idto.relationStallId != null">
            and t.stall_id = #{idto.relationStallId}
        </if>
        <if test="idto.relationDeliveryBatch != null">
            and t.delivery_batch = #{idto.relationDeliveryBatch}
        </if>
    </select>


    <select id="getOrderCount" resultType="com.pinshang.qingyun.order.dto.bigShop.DdReceiveOrderCountODTO">
        SELECT
            o.stall_id,
            o.delivery_batch,
            count(DISTINCT o.order_code) orderCodeCount
        from t_order o
      INNER  JOIN t_sub_order so on so.order_id = o.id
      INNER JOIN  t_md_receive_order  mro on  mro.sub_order_id = so.id and mro.status = 0
        where o.order_status = 0 and so.status != 2
        and o.order_time = #{orderTime}
        and o.store_id = (SELECT tt.store_id from t_md_shop tt where tt.id = #{shopId})
        GROUP BY o.stall_id,o.delivery_batch
    </select>

    <select id="getReceiveOrderCount" resultType="com.pinshang.qingyun.order.dto.bigShop.DdReceiveOrderCountODTO">
        SELECT
            t.doc_id,
            count(DISTINCT order_code)  orderCodeCount
        from t_dd_receive_doc_order t
        where t.doc_id in
        <foreach collection="docIdList" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
        GROUP BY t.doc_id
    </select>

    <select id="queryReceiveCommodityLogList" resultType="com.pinshang.qingyun.order.dto.bigShop.DdReceiveDocCommodityODTO">
        SELECT
            t.commodity_id,
            t.quantity,
            t.real_delivery_quantity,
            t.real_receive_quantity,
            t.storage_area,
            t.goods_allocation_id
        from t_dd_receive_doc_log t
        where t.doc_id = #{docId}
    </select>

    <select id="querySumReceiveOrderCommodityList" resultType="com.pinshang.qingyun.order.dto.bigShop.DdReceiveDocCommodityODTO">
        SELECT
            soi.commodity_id,
            sum(soi.quantity) quantity,
            sum(soi.real_delivery_quantity) realDeliveryQuantity
        from t_order o
        INNER  JOIN t_sub_order so on so.order_id = o.id
        INNER JOIN t_sub_order_item soi on soi.sub_order_id = so.id
        INNER JOIN  t_md_receive_order  mro on  mro.sub_order_id = so.id and mro.status = 0
       where o.order_status = 0 and so.status != 2
        AND o.order_time = #{orderTime}
        and o.stall_id = #{stallId}
        and o.store_id = (select store_id from t_md_shop where id = #{shopId})
        and o.delivery_batch in
        <foreach collection="deliveryBatchList" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
        <if test="null != barCode and barCode !='' ">
            AND soi.commodity_id = (select cb.commodity_id from t_commodity_bar_code cb where cb.bar_code = #{barCode} )
        </if>
        group by soi.commodity_id
    </select>


    <select id="queryRelationOrderListLog" resultType="com.pinshang.qingyun.order.dto.bigShop.DdReceiveDocOrderODTO">
        SELECT
            t.order_time,
            t.order_code,
            t.logistics_model,
            t.order_amount
        from t_dd_receive_doc_order t
        where t.doc_id = #{docId}
    </select>


    <select id="queryWaitReceiveRelationOrderList" resultType="com.pinshang.qingyun.order.dto.bigShop.DdReceiveDocOrderODTO">
        SELECT
            distinct
            o.order_time,
            o.order_code,
            so.logistics_model,
            o.order_amount
        from t_order o
        INNER  JOIN t_sub_order so on so.order_id = o.id
        INNER JOIN  t_md_receive_order  mro on  mro.sub_order_id = so.id and mro.status = 0
      where o.order_status = 0 and so.status != 2
        AND o.order_time = #{orderTime}
        and o.stall_id = #{stallId}
        and o.store_id = (select store_id from t_md_shop where id = #{shopId})
        and o.delivery_batch in
        <foreach collection="deliveryBatchList" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
    </select>


    <select id="getOrderCommodityInfo" resultType="com.pinshang.qingyun.order.dto.bigShop.DdReceiveOrderCommodityODTO">
        SELECT
            o.order_code,
            so.logistics_model,
            o.order_amount,

            so.id subOrderId,
            so.sub_order_code subOrderCode,
            soi.id subOrderItemId,

            soi.commodity_id,
            soi.quantity,
            IFNULL(soi.real_delivery_quantity,0) realDeliveryQuantity,
            IFNULL(soi.real_receive_quantity,0) realReceiveQuantity,
            soi.price
        from t_order o
             INNER  JOIN t_sub_order so on so.order_id = o.id
             INNER JOIN t_sub_order_item soi on soi.sub_order_id = so.id
             INNER JOIN  t_md_receive_order  mro on  mro.sub_order_id = so.id and mro.status = 0
        WHERE  o.order_status = 0 and so.status != 2
          and soi.real_delivery_quantity >= 0
          AND o.order_time = #{orderTime}
          and o.stall_id = #{stallId}
          and o.store_id = (select store_id from t_md_shop where id = #{shopId})
          AND soi.commodity_id = #{commodityId}
          and o.delivery_batch in
         <foreach collection="deliveryBatchList" item="item" separator="," open="(" close=")">
             #{item}
         </foreach>
        ORDER BY soi.quantity,o.id

    </select>


    <select id="getOrderListByReceiveDoc" resultType="com.pinshang.qingyun.order.dto.bigShop.DdReceiveOrderCommodityODTO">
        SELECT
            o.order_code,
            so.logistics_model,
            o.order_amount,

            so.id subOrderId,
            so.sub_order_code subOrderCode,
            soi.id subOrderItemId,

            soi.commodity_id,
            soi.quantity,
            soi.real_delivery_quantity realDeliveryQuantity,
            soi.real_receive_quantity realReceiveQuantity,
            soi.price
        from t_order o
        INNER  JOIN t_sub_order so on so.order_id = o.id
        INNER JOIN t_sub_order_item soi on soi.sub_order_id = so.id
        INNER JOIN  t_md_receive_order  mro on  mro.sub_order_id = so.id and mro.status = 0
        WHERE  o.order_status = 0 and so.status != 2
        AND o.order_time = #{orderTime}
        and o.stall_id = #{stallId}
        and o.store_id = (select store_id from t_md_shop where id = #{shopId})
        and o.delivery_batch in
        <foreach collection="deliveryBatchList" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>

    </select>

    <update id="updateRealReceiveQuantity">
        UPDATE t_sub_order_item
        SET real_receive_quantity = IFNULL(real_receive_quantity,0) + #{receiveQuantityitem}
        WHERE id = #{subOrderId}
          AND commodity_Id = #{commodityId}
    </update>

    <update id="batchUpdateRealReceiveQuantity" parameterType="java.util.List">
        <foreach collection="list" index="index" item="item" open="" close="" separator=";">
            UPDATE t_sub_order_item
            SET real_receive_quantity = IFNULL(real_receive_quantity,0) + #{item.realDeliveryQuantity}
            WHERE id = #{item.subOrderItemId}
            AND commodity_Id = #{item.commodityId}
        </foreach>
    </update>

</mapper>
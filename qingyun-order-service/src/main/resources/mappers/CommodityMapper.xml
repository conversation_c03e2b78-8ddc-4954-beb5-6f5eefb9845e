<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.pinshang.qingyun.order.mapper.CommodityMapper">

	<select id="findCommodityBarCodeByIds"  resultType="com.pinshang.qingyun.order.mapper.entry.commodity.CommodityBasicEntry">
		SELECT
			GROUP_CONCAT(t.bar_code ORDER BY default_state desc) AS bar_codes,
			t.commodity_id
		FROM
		 t_commodity_bar_code t
		WHERE 1=1
		<if test="commodityIdList != null and commodityIdList.size() > 0">
			AND t.commodity_id in <foreach collection="commodityIdList" item="id" open="(" separator="," close=")"> #{id} </foreach>
		</if>
		GROUP BY
		commodity_id

	</select>

	<select id="findCommodityBasicListByParam" parameterType="com.pinshang.qingyun.order.vo.commodity.CommodityVO" resultType="com.pinshang.qingyun.order.mapper.entry.commodity.CommodityBasicEntry">
		SELECT
			c.`id` commodityId,
			c.`commodity_code`,
			c.`bar_code`,
		    c.`commodity_name`,
		    c.`commodity_spec`,
		    IFNULL(c.commodity_package_spec,1) commodityPackageSpec,
		    c.`commodity_unit_id` commodityUnitId,

			c.`commodity_cycle_id` commodityCycleId,
			c.`xd_sales_box_capacity`,
			c.`sales_box_capacity`,

			c.commodity_third_id commodityThirdId,
			c.commodity_first_id commodityFirstId,
			c.commodity_second_id commoditySecondId,

			c.tax_rate taxRate,
			c.`commodity_type`,
			c.`commodity_package_kind` packedType,
		    c.`commodity_package_kind` commodityPackageKind,
			c.`commodity_is_quick_freeze` frozen ,
			c.`storage_condition` storageCondition,
			c.`logistics_model`,
			c.is_weight,
		    c.box_capacity
		FROM t_commodity c
		WHERE 1=1
		<if test="commodityId != null">
			AND c.id = #{commodityId}
		</if>
		<if test="commodityKey!=null and commodityKey !='' ">
			and (c.`commodity_name` like concat('%',#{commodityKey},'%') or c.`commodity_aid` like concat('%',#{commodityKey},'%') or c.`commodity_code` like concat('%',#{commodityKey},'%') or c.`bar_code` like concat('%',#{commodityKey},'%')  )
		</if>
		<if test="barCode != null and barCode != '' ">
			and c.`id` = (SELECT  commodity_id FROM t_commodity_bar_code WHERE  bar_code = #{barCode})
		</if>
		<if test="cateId1 != null">
			AND c.commodity_first_id = #{cateId1}
		</if>
		<if test="cateId2 != null">
			AND c.commodity_second_id = #{cateId2}
		</if>
		<if test="cateId3 != null">
			AND c.commodity_third_id = #{cateId3}
		</if>
		<if test="commodityIdList != null and commodityIdList.size() > 0">
			AND c.id in <foreach collection="commodityIdList" item="id" open="(" separator="," close=")"> #{id} </foreach>
		</if>

	</select>

	<select id="findShopCommodityPurchaseList" resultType="java.lang.Long" parameterType="com.pinshang.qingyun.order.vo.commodity.CommodityListRequestVO">
		SELECT
		  t.commodity_id
		FROM
		  t_xs_shop_commodity_purchase_status t
		LEFT JOIN t_commodity c on c.id = t.commodity_id
		WHERE t.shop_id = #{shopId} and t.commodity_purchase_status = 1
		and c.`commodity_state` = 1
		and c.status = 1 and c.product_type != 2
		<if test="isWeight == true and isNormal != true">
			and c.is_weight = 1
		</if>
		<if test="isNormal == true and isWeight != true">
			and c.is_weight = 0
		</if>
		<if test="isNew == true">
			and c.`commodity_cycle_id`= 348815514434650944
		</if>
		<if test="commodityId != null">
			and c.`id` = #{commodityId}
		</if>
		<if test="barCode != null and barCode !='' ">
			and c.`id` = (SELECT  commodity_id FROM t_commodity_bar_code WHERE  bar_code = #{barCode})
		</if>
		<if test="categoryId != null and categoryId != '' ">
			and ((c.`commodity_first_id`= #{categoryId}) or (c.`commodity_second_id`= #{categoryId}) or (c.`commodity_third_id`= #{categoryId}))
		</if>
		<if test="commodityKey != null and commodityKey != '' ">
			and (c.`commodity_code` like concat('%',#{commodityKey},'%') or c.`commodity_name` like concat('%',#{commodityKey},'%') or c.`commodity_aid` like concat('%',#{commodityKey},'%') or c.`bar_code` like concat('%',#{commodityKey},'%')  )
		</if>
		<if test="commodityCodeList != null and commodityCodeList.size() > 0 ">
			and c.`commodity_code` IN <foreach collection="commodityCodeList" item="commodityCode" open="(" separator="," close=")"> #{commodityCode} </foreach>
		</if>
		<if test="commodityIdList != null and commodityIdList.size() > 0 ">
			and c.`id` IN <foreach collection="commodityIdList" item="commodityId" open="(" separator="," close=")"> #{commodityId} </foreach>
		</if>
	</select>


	<select id="findStoreIdBySubOrderCode" resultType="java.lang.String" parameterType="java.lang.String" >
		SELECT
			o.store_id
		FROM
			t_sub_order so
		 JOIN t_order o ON so.order_id = o.id
		WHERE
			so.sub_order_code = #{subOrderCode}
	</select>


	<select id="queryCommodityStatus" resultType="com.pinshang.qingyun.order.mapper.entry.order.ProductStatusEntry">
		SELECT
			txscps.commodity_id,
			tc.commodity_name,
			tc.commodity_code,
			tc.status,
			tc.commodity_state,
			txscps.commodity_purchase_status
		FROM t_xs_shop_commodity_purchase_status txscps
		LEFT JOIN t_commodity tc  on txscps.commodity_id = tc.id
		WHERE  txscps.shop_id = (SELECT id FROM t_md_shop WHERE store_id = #{storeId})
		<if test="commodityIdList != null and commodityIdList.size >0 ">
			AND txscps.commodity_id IN
			<foreach collection="commodityIdList" item="id" open="(" separator="," close=")">
				#{id}
			</foreach>
		</if>
	</select>



	<select id="findCartNumByCommodityId" resultType="com.pinshang.qingyun.order.mapper.entry.commodity.CommodityResultEntry" >
		SELECT
		    c.id commodityId,
			IFNULL(sum(ci.`commodity_num`),0) cartNum
		FROM
			t_md_shopping_cart_item ci
		INNER JOIN t_md_shopping_cart sc ON sc.id = ci.shopping_cart_id
		AND sc.id IN (
			SELECT
				c.id
			FROM
				t_md_shopping_cart c
			WHERE
				c.`store_id` = #{storeId}
		)
		INNER JOIN t_commodity c ON c.id = ci.`commodity_id`
		WHERE c.id= #{commodityId}
	</select>


	<select id="getOrderedQuantity" resultType="java.math.BigDecimal" >
		SELECT
		    sum(soi.quantity)
		FROM t_order o
		LEFT JOIN t_sub_order so on  so.order_id = o.id
		LEFT JOIN t_sub_order_item soi on soi.sub_order_id = so.id
		where o.order_status = 0 and so.status != 2
		and o.order_time > DATE_FORMAT(now(),'%Y-%m-%d')
		and o.store_id = #{storeId}
		<if test="commodityId != null">
			and soi.commodity_id = #{commodityId}
		</if>
		<if test="barCode != null and barCode != '' ">
			and soi.commodity_id = (SELECT  commodity_id FROM t_commodity_bar_code WHERE  bar_code = #{barCode})
		</if>
	</select>

	<select id="findCommodityInfo" resultType="com.pinshang.qingyun.order.mapper.entry.order.CommodityInfoEntry">
		SELECT
		       id,
		       id commodityId,
		       commodity_code,
		       commodity_name,
			   commodity_spec,
			   logistics_model,
		       IFNULL(commodity_package_spec,1) commodityPackageSpec
		FROM t_commodity WHERE id IN
		<foreach collection="commodityIdList" item="id" open="(" separator="," close=")">
		     #{id}
		</foreach>
	</select>
	<select id="findCommodityInfoIds" resultType="com.pinshang.qingyun.order.mapper.entry.order.CommodityInfoEntry">
		SELECT
		id,
		commodity_code commodityCode,
		commodity_name commodityName,
		commodity_spec commoditySpec,
		logistics_model logisticsModel,
		IFNULL(commodity_package_spec,1) commodityPackageSpec,
		is_weight isWeight
		FROM t_commodity WHERE id IN
		<foreach collection="commodityIdList" item="id" open="(" separator="," close=")">
			#{id}
		</foreach>
	</select>


	<select id="getXDCommodityODTOById" resultType="com.pinshang.qingyun.order.dto.XDCommodityODTO">
		select
			c.`id` commodityId,
			c.`commodity_code`,
			c.`commodity_name`,
			c.`commodity_spec`,
			c.`product_type` productType,
			di.`option_name` commodityUnit,
			c.`xd_sales_box_capacity`,
			c.`sales_box_capacity`,
			d.`option_code` newProductFlag,
			IFNULL(c.commodity_package_spec,1) commodityPackageSpec,
		    c.logistics_model,
		    c.bar_code
		from  t_commodity c
		left  join `t_dictionary` di on di.`id` = c.`commodity_unit_id`
		left  join `t_dictionary` d on d.`id` = c.`commodity_cycle_id`
		WHERE c.id = #{commodityId}
	</select>


	<select id="getXDCommodityODTOByIdList" resultType="com.pinshang.qingyun.order.dto.XDCommodityODTO">
		select
		c.`id` commodityId,
		c.`commodity_code`,
		c.`commodity_name`,
		c.`commodity_spec`,
		di.`option_name` commodityUnit,
		c.`xd_sales_box_capacity`,
		c.`sales_box_capacity`,
		d.`option_code` newProductFlag,
		IFNULL(c.commodity_package_spec,1) commodityPackageSpec,
		c.logistics_model
		from  t_commodity c
		left  join `t_dictionary` di on di.`id` = c.`commodity_unit_id`
		left  join `t_dictionary` d on d.`id` = c.`commodity_cycle_id`
		WHERE c.id in
		<foreach collection="list" separator="," open="(" close=")" item="item">
			#{item}
		</foreach>
	</select>


	<select id="findCommodityInfoByCodes" resultType="com.pinshang.qingyun.order.dto.XDCommodityODTO">
		SELECT
			t.id commodityId,
			t.commodity_code,
			t.commodity_name,
			t.`commodity_spec`,
			IFNULL(t.commodity_package_spec,1) commodityPackageSpec,
			t.`commodity_is_quick_freeze` frozen,
			t.`sales_box_capacity`,
			t.`xd_sales_box_capacity`,
			t.`commodity_state` commodityStatus,
			t.logistics_model,
		    td.option_name commodityUnit
		FROM t_commodity t
		LEFT JOIN t_dictionary td
		ON t.commodity_unit_id = td.id
		WHERE t.product_type != 2 and t.commodity_code IN
		<foreach collection="commodityCodes" item="commodityCode" open="(" separator="," close=")">
			#{commodityCode}
		</foreach>
	</select>


	<select id="findCommodityBarCodeByParam" resultType="com.pinshang.qingyun.order.model.commodity.Commodity">
		SELECT  t.commodity_id id,
		GROUP_CONCAT(t.bar_code ORDER BY t.default_state desc) barCode
		from t_commodity_bar_code t
		where 1=1
		<if test="barCode != null and barCode !='' ">
			and  t.commodity_id = (SELECT tt.commodity_id from t_commodity_bar_code tt where tt.bar_code=#{barCode})
		</if>
		<if test="commodityIdList != null and commodityIdList.size >0 ">
			AND t.commodity_id IN
			<foreach collection="commodityIdList" item="id" open="(" separator="," close=")">
				#{id}
			</foreach>
		</if>
		GROUP BY t.commodity_id
	</select>



	<select id="findCommodityByCode" resultType="java.lang.Long">
		SELECT
		   c.`id`
		FROM t_commodity c
		WHERE 1=1
		and c.`commodity_code` = #{barCode}
	</select>

	<select id="findCommodityByBarCode" resultType="java.lang.Long">
		SELECT  commodity_id FROM t_commodity_bar_code WHERE  bar_code = #{barCode}
	</select>


	<select id="getCommodityFreezeGroup" resultType="java.lang.Long">
		SELECT t.commodity_id FROM t_commodity_freeze_group t
	</select>

    <select id="findStoreCommodityByParams" resultType="com.pinshang.qingyun.order.model.commodity.Commodity">
		select
			ppml.`id` product_price_model_list_id,
			ppml.`commodity_price`,
			d.option_name as commodityUnitName,
			(case when cg.id is null then '否' else '是' end) as isRoundedGoods,
			c.*
		from
			`t_store_settlement` s
				inner join `t_product_price_model` ppm  on ppm.`id` = s.`product_price_model_id`
				inner join `t_product_price_model_list` ppml  on ppml.`product_price_model_id` = ppm.`id`
				inner join `t_commodity` c  on ppml.`commodity_id` = c.`id`
			    <if test="planOrderType !=null and planOrderType == 1">
					inner join `t_plan_commodity` pc on c.id = pc.commodity_id and pc.sync_status =1
				</if>
				left join t_commodity_freeze_group cg  on cg.commodity_id = c.id
				left join t_dictionary d on d.id = c.commodity_unit_id
		where 1 = 1
		  and ppm.`price_model_state` = 1
		  and c.`product_type` !=2
		  and c.`commodity_state` = 1
		  and s.`store_id` = #{storeId}
		  <if test="code!=null and code!=''">
			<if test="addCommodityType == 1">
				and ((c.bar_code like concat('%',#{code})) or (c.`commodity_name` like concat('%',#{code},'%')))
			</if>
			  <if test="addCommodityType == 2">
				  and ( (c.`commodity_code` like concat('%',#{code},'%')) or (c.`commodity_aid` like concat('%',#{code},'%'))  or (c.`commodity_name` like concat('%',#{code},'%')) or (c.`commodity_spec` like concat('%',#{code},'%')) )
			  </if>
		  </if>
		  <if test="selectedCodes !=null and selectedCodes.size > 0">
		    and
			<if test="addCommodityType == 1">
				 c.`bar_code`
			</if>
			<if test="addCommodityType == 2">
				c.commodity_code
			</if>
			not in
		  	<foreach collection="selectedCodes" index="index" item="selectedCode" open="(" separator="," close=")">
				#{selectedCode}
			</foreach>
		  </if>
		  <if test="codes !=null and codes.size > 0">
				and
				<if test="addCommodityType == 1">
					c.`bar_code`
				</if>
				<if test="addCommodityType == 2">
					c.commodity_code
				</if>
				in
		       <foreach collection="codes" index="index" item="code" open="(" separator="," close=")">
				#{code}
				</foreach>
		  </if>

	</select>
    <select id="findStoreCommodityByStoreId" resultType="com.pinshang.qingyun.order.dto.cup.ProductPriceDto" flushCache="true">
		select
			c.`id` product_id,
			c.`commodity_code` product_code,
			c.`commodity_name` product_name,
			ppml.`commodity_price` price
		from
			`t_store_settlement` s
				inner join `t_product_price_model` ppm  on ppm.`id` = s.`product_price_model_id`
				inner join `t_product_price_model_list` ppml  on ppml.`product_price_model_id` = ppm.`id`
				inner join `t_commodity` c  on ppml.`commodity_id` = c.`id`
		where 1 = 1
		  and s.`store_id` = #{storeId}
		  <if test="productIds !=null and productIds.size > 0">
			  and c.id in
			  <foreach collection="productIds" index="index" item="productId" open="(" separator="," close=")">
				  #{productId}
			  </foreach>
		  </if>

	</select>
    <select id="findStoreCommodityByStoreIdAndCommodityCode"
            resultType="com.pinshang.qingyun.order.model.commodity.Commodity">

	</select>


	<select id="queryNewCommodityIdList" resultType="java.lang.Long">
		SELECT t.commodity_id
		FROM t_shop_new_commodity_period t
		where t.old_date_begin > #{nowDate}
		<if test="commodityIdList !=null and commodityIdList.size > 0">
			and t.commodity_id in
			<foreach collection="commodityIdList" index="index" item="productId" open="(" separator="," close=")">
				#{productId}
			</foreach>
		</if>
	</select>

</mapper>
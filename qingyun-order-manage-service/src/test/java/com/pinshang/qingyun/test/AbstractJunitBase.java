package com.pinshang.qingyun.test;

import com.pinshang.qingyun.ApplicationOrderManageService;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;
import org.springframework.test.context.web.WebAppConfiguration;
import org.springframework.transaction.annotation.Transactional;

@RunWith(SpringJUnit4ClassRunner.class)
@SpringBootTest(classes = ApplicationOrderManageService.class,properties = {"spring.profiles.active=dev","application.name.switch=tramy-"})
@WebAppConfiguration
@Transactional
public  abstract class AbstractJunitBase {}
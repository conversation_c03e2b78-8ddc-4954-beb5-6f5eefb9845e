package com.pinshang.qingyun;

import com.ctrip.framework.apollo.spring.annotation.EnableApolloConfig;
import com.pinshang.qingyun.base.spring.MainArgsPreHandler;
import com.pinshang.qingyun.infrastructure.apm.cat.springboot.EnableCatMetrics;
import com.pinshang.qingyun.infrastructure.exportfile.cache.starter.FileCacheConfiguration;
import com.pinshang.qingyun.infrastructure.mq.starter.EnableMqComponent;
import com.pinshang.qinyun.cache.service.RedisServiceDefinition;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.web.servlet.ServletComponentScan;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Import;
import org.springframework.kafka.annotation.EnableKafka;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurerAdapter;

import tk.mybatis.spring.annotation.MapperScan;

@Controller
@SpringBootApplication
@EnableDiscoveryClient
@EnableFeignClients
@MapperScan(basePackages = "com.pinshang.qingyun.order.manage.mapper")
@ComponentScan(basePackages = {"com.pinshang.qingyun","com.pinshang.qingyun.infrastructure"})
@EnableScheduling
@ServletComponentScan
@Import(value = {RedisServiceDefinition.class, FileCacheConfiguration.class})
@EnableKafka
@EnableAsync
@EnableApolloConfig
@EnableMqComponent
@EnableCatMetrics
public class ApplicationOrderManageService extends WebMvcConfigurerAdapter {
    private Logger logger = LoggerFactory.getLogger(getClass());
    public static void main(String[] args) {
        SpringApplication.run(ApplicationOrderManageService.class, MainArgsPreHandler.argsHandle(args));
    }

    @GetMapping(value={"","/"})
    public String index(){
        return "redirect:/chk.html";
    }

    @RequestMapping(value = "/test", method = RequestMethod.GET)
    @ResponseBody
    public String testLogLevel() {
        logger.debug("Logger Level ：DEBUG************************************");
        logger.info("Logger Level ：INFO************************************");
        logger.error("Logger Level ：ERROR************************************");
        return "success";
    }
}

package com.pinshang.qingyun.order.manage.service.auto;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.pinshang.qingyun.base.api.TokenInfo;
import com.pinshang.qingyun.base.configure.expand.QYAssert;
import com.pinshang.qingyun.base.enums.IogisticsModelEnums;
import com.pinshang.qingyun.base.enums.YesOrNoEnums;
import com.pinshang.qingyun.base.enums.shop.ShopTypeEnums;
import com.pinshang.qingyun.base.util.FastThreadLocalUtil;
import com.pinshang.qingyun.box.utils.DateUtil;
import com.pinshang.qingyun.box.utils.SpringUtil;
import com.pinshang.qingyun.box.utils.StringUtil;
import com.pinshang.qingyun.common.dto.DictionaryEditIDTO;
import com.pinshang.qingyun.common.dto.DictionaryODTO;
import com.pinshang.qingyun.common.service.DictionaryClient;
import com.pinshang.qingyun.order.dto.shop.MdShopOrderSettingODTO;
import com.pinshang.qingyun.order.dto.shop.MdShopOrderSettingQueryIDTO;
import com.pinshang.qingyun.order.manage.dto.auto.AutoOverZbCommodityDTO;
import com.pinshang.qingyun.order.manage.enums.AutoShopCommodityLogEnum;
import com.pinshang.qingyun.order.manage.mapper.ShopMapper;
import com.pinshang.qingyun.order.manage.mapper.auto.AutoCommodityMapper;
import com.pinshang.qingyun.order.manage.mapper.auto.AutoSettingMapper;
import com.pinshang.qingyun.order.manage.mapper.auto.AutoShopCommodityMapper;
import com.pinshang.qingyun.order.manage.model.Shop;
import com.pinshang.qingyun.order.manage.model.auto.AutoCommodity;
import com.pinshang.qingyun.order.manage.model.auto.AutoSetting;
import com.pinshang.qingyun.order.manage.model.auto.AutoShopCommodityLog;
import com.pinshang.qingyun.order.manage.vo.auto.AutoSettingPageVO;
import com.pinshang.qingyun.order.manage.vo.auto.AutoSettingRequestVO;
import com.pinshang.qingyun.order.manage.vo.auto.AutoShopCommodityListVO;
import com.pinshang.qingyun.order.manage.vo.auto.AutoShopCommodityRequestVO;
import com.pinshang.qingyun.order.service.MdShopOrderSettingClient;
import com.pinshang.qingyun.report.dto.LastMonthSaleDTO;
import com.pinshang.qingyun.report.dto.ShopCommoditySaleStatisticsODTO;
import com.pinshang.qingyun.report.service.XdCommodityTaxClient;
import com.pinshang.qingyun.shop.dto.SelectUserDepartmentShopDropdownInfoListIDTO;
import com.pinshang.qingyun.shop.dto.ShopDropdownInfoODTO;
import com.pinshang.qingyun.shop.service.ShopClient;
import com.pinshang.qingyun.smm.dto.user.SelectUserShopIdListIDTO;
import com.pinshang.qingyun.smm.service.SMMUserClient;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.entity.Example;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
public class AutoSettingService {

    @Autowired
    private AutoSettingMapper autoSettingMapper;

    @Autowired
    private ShopMapper shopMapper;

    @Autowired
    private XdCommodityTaxClient xdCommodityTaxClient;

    @Autowired
    private SMMUserClient smmUserClient;

    @Autowired
    private ShopClient shopClient;

    @Autowired
    private AutoCommodityMapper autoCommodityMapper;

    @Lazy
    @Autowired
    private AutoShopCommodityService autoShopCommodityService;

    @Autowired
    private AutoShopCommodityMapper autoShopCommodityMapper;

    @Autowired
    private MdShopOrderSettingClient mdShopOrderSettingClient;

    @Autowired
    private AutoShopCommodityLogService autoShopCommodityLogService;
    @Autowired
    private DictionaryClient dictionaryClient;
    /**
     * 开关 0关闭 1开启
     * @param shopId
     */
    @Transactional(rollbackFor = Exception.class)
    public Boolean updateStatus(Long shopId, Integer status) {
        TokenInfo tokenInfo = FastThreadLocalUtil.getQY();
        Date date = new Date();

        AutoSetting autoSetting = new AutoSetting();
        autoSetting.setUpdateId(tokenInfo.getUserId());
        autoSetting.setUpdateTime(date);
        //当启用得时候去初始化一下(job每个月初获取上个月销售额，只获取了启用得店铺)
        if (YesOrNoEnums.YES.getCode().equals(status)) {
            //初始化设置
            initSetting(shopId);
        }
        Example example = new Example(AutoSetting.class);
        example.createCriteria().andEqualTo("shopId", shopId);
        AutoSetting exitAutoSetting = autoSettingMapper.selectOneByExample(example);
        //添加
//        if (null == exitAutoSetting) {
//            exitAutoSetting = autoSettingMapper.selectOneByExample(example);
//        }
        //修改
        autoSetting.setId(exitAutoSetting.getId());
        autoSetting.setStatus(status);
        autoSettingMapper.updateByPrimaryKeySelective(autoSetting);
        //日志
        AutoShopCommodityLog log = new AutoShopCommodityLog();
        Shop shop = shopMapper.selectByPrimaryKey(shopId);
        log.setShopId(shop.getId());
        log.setShopCode(shop.getShopCode());
        log.setShopName(shop.getShopName());
        log.setCreateTime(new Date());
        log.setCreateId(tokenInfo.getUserId());
        log.setCreateName(tokenInfo.getRealName());
        if (YesOrNoEnums.NO.getCode().equals(status)) {
            log.setType(AutoShopCommodityLogEnum.END_AUTO.getCode());
        } else {
            log.setType(AutoShopCommodityLogEnum.START_AUTO.getCode());
        }
        List<AutoShopCommodityLog> list = new ArrayList<>();
        list.add(log);
        autoShopCommodityLogService.batchSave(list);

        autoShopCommodityService.deleteAutoShopCommodityRedis(shop.getId());
        //sendLogService.sendLog(list, "t_log_auto_shop_commodity");
        return Boolean.TRUE;
    }

    public Boolean updateSetting(AutoSetting autoSetting) {
        autoSettingMapper.updateByPrimaryKeySelective(autoSetting);
        return Boolean.TRUE;
    }


    public AutoSetting settingByShopId(Long shopId) {
        Example example = new Example(AutoSetting.class);
        example.createCriteria().andEqualTo("shopId", shopId);
        return autoSettingMapper.selectOneByExample(example);
    }

    /**
     * 更新所有总部品项数量
     * @param headItems
     * @return
     */
    public Integer updateHeadItems(Integer headItems) {
        return autoSettingMapper.updateHeadItems(headItems);
    }

    /**
     * 上个月有销售品项数量
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean lastMonthSale() {
        Date lastMonth = DateUtil.addMonth(-1);
        String startTime = DateUtil.get4yMd(DateUtil.getStartDateByMonth(lastMonth));
        String endTime = DateUtil.get4yMd(DateUtil.lastDayOfMonth(lastMonth));
        List<AutoSetting> settingList = autoSettingMapper.selectAll();
        //只获取自动订货启用的
        settingList = settingList.stream().filter(e -> e.getStatus().equals(YesOrNoEnums.YES.getCode())).collect(Collectors.toList());

        if (null != settingList && settingList.size() > 0) {
            LastMonthSaleDTO lastMonthSaleDTO = new LastMonthSaleDTO();
            lastMonthSaleDTO.setStartTime(startTime);
            lastMonthSaleDTO.setEndTime(endTime);
            List<Long> shopList = settingList.stream().map(AutoSetting::getShopId).collect(Collectors.toList());
            lastMonthSaleDTO.setShopIds(shopList);
            List<ShopCommoditySaleStatisticsODTO> list = xdCommodityTaxClient.lastMonthSale(lastMonthSaleDTO);

            Map<Long, List<ShopCommoditySaleStatisticsODTO>> map = new HashMap<>();
            if (null != list && list.size() > 0) {
                map = list.stream().collect(Collectors.groupingBy(ShopCommoditySaleStatisticsODTO::getShopId));
            }

            //AutoSetting update = null;
            Date date = new Date();
            for (AutoSetting autoSetting : settingList) {
                String lastMonthSaleInfo = null;
                List<ShopCommoditySaleStatisticsODTO> commodityIdList = null;
                autoSetting.setLastMonthSale(0);
                if (map.containsKey(autoSetting.getShopId())) {
                    commodityIdList = map.get(autoSetting.getShopId());
                    lastMonthSaleInfo = JSON.toJSONString(commodityIdList.stream().map(ShopCommoditySaleStatisticsODTO::getCommodityId).collect(Collectors.toList()));
                    autoSetting.setLastMonthSale(commodityIdList.size());
                }
                autoSetting.setLastMonthSaleInfo(lastMonthSaleInfo);
                autoSetting.setUpdateTime(date);
                autoSetting.setUpdateId(1L);
                autoSettingMapper.updateByPrimaryKey(autoSetting);
            }
        }
        return Boolean.TRUE;
    }


    /**
     * 期初新添加门店的数据
     */
    public void initSetting(Long shopId) {
        AutoSetting autoSetting = new AutoSetting();
        //获取总部品项数量
        List<AutoCommodity> autoCommodityAll = autoCommodityMapper.selectAll();
        Integer headItems = 0;
        if (null != autoCommodityAll && autoCommodityAll.size() > 0) {
            headItems = autoCommodityAll.size();
        }
        autoSetting.setHeadItems(headItems);

        //获取该门店上个月的销售品项
        Date lastMonth = DateUtil.addMonth(-1);
        String startTime = DateUtil.get4yMd(DateUtil.getStartDateByMonth(lastMonth));
        String endTime = DateUtil.get4yMd(DateUtil.lastDayOfMonth(lastMonth));
        LastMonthSaleDTO lastMonthSaleDTO = new LastMonthSaleDTO();
        lastMonthSaleDTO.setEndTime(endTime);
        lastMonthSaleDTO.setStartTime(startTime);
        List<Long> shopIds = new ArrayList<>();
        shopIds.add(shopId);
        lastMonthSaleDTO.setShopIds(shopIds);
        List<ShopCommoditySaleStatisticsODTO> list = xdCommodityTaxClient.lastMonthSale(lastMonthSaleDTO);
        autoSetting.setLastMonthSale(0);
        if (null != list && list.size() > 0) {
            autoSetting.setLastMonthSale(list.size());
            List<Long> commodityIds = list.stream().map(ShopCommoditySaleStatisticsODTO::getCommodityId).collect(Collectors.toList());
            autoSetting.setLastMonthSaleInfo(JSON.toJSONString(commodityIds));
        }
        //获取该门店的自动订货商品
        List<Long> shopIdList = new ArrayList<>();
        shopIdList.add(shopId);
        Map<Long, List<Long>> map = autoShopCommodityService.autoCommodityByShopIds(shopIdList);
        Integer setItems = 0;
        if (null != map && map.containsKey(shopId)) {
            setItems = map.get(shopId).size();
        }
        autoSetting.setShopId(shopId);
        autoSetting.setSetItems(setItems);
        autoSetting.setCreateId(1L);
        autoSetting.setCreateTime(new Date());
        autoSetting.setStatus(YesOrNoEnums.NO.getCode());
        autoSetting.setUpdateId(1L);
        autoSetting.setUpdateTime(new Date());
        Example example = new Example(AutoSetting.class);
        example.createCriteria().andEqualTo("shopId", shopId);
        AutoSetting oldData = autoSettingMapper.selectOneByExample(example);
        if (null == oldData) {
            autoSettingMapper.insert(autoSetting);
        } else {
            autoSetting.setId(oldData.getId());
            autoSetting.setCreateTime(oldData.getCreateTime());
            autoSettingMapper.updateByPrimaryKey(autoSetting);
        }

    }

    /**
     * 自动订货设置列表
     * @param resVO
     * @return
     */
    public PageInfo<AutoSettingPageVO> autoSettingPage(AutoSettingRequestVO resVO) {

        List<Long> shopIdList = smmUserClient.selectUserShopIdList( SelectUserShopIdListIDTO.firstCacheThenDb(FastThreadLocalUtil.getQY().getUserId()));
        //List<Long> shopIdList = smmUserClient.selectUserShopIdList( SelectUserShopIdListIDTO.firstCacheThenDb(7701L));
        //当用户没有门店权限时，不再查询数据
        if(SpringUtil.isEmpty( shopIdList )){
            return  new PageInfo<>();
        }
        if (resVO.getShopId() == null && StringUtils.isNotBlank(resVO.getDeptCode())){
            //根据部门查询门店
            SelectUserDepartmentShopDropdownInfoListIDTO idto = new SelectUserDepartmentShopDropdownInfoListIDTO();
            idto.setDepartmentCode(resVO.getDeptCode());
            idto.setUserShopIdList(shopIdList);
            idto.setLimitQuantity(9999);
            List<ShopDropdownInfoODTO> shopDropdownInfoODTOS = shopClient.selectUserDepartmentShopDropdownInfoList(idto);
            if (CollectionUtils.isEmpty(shopDropdownInfoODTOS)){
                return new PageInfo<>();
            }
            resVO.setShopIds(shopDropdownInfoODTOS.stream().map(ShopDropdownInfoODTO::getShopId).collect(Collectors.toList()));
        }else {
            resVO.setShopIds(shopIdList);
        }

        PageInfo<AutoSettingPageVO> pageInfo = PageHelper.startPage(resVO.getPageNo(), resVO.getPageSize()).doSelectPageInfo(() ->
                autoSettingMapper.autoSettingPage(resVO));

        if (pageInfo.getSize() > 0) {
            //查询总部规划的自动订货商品
            List<AutoCommodity> autoCommodityAll = autoCommodityMapper.selectAll();
            Integer headItems = 0;
            if (null != autoCommodityAll && autoCommodityAll.size() > 0) {
                headItems = autoCommodityAll.size();
            }
            List<Long> headItemsList = null;
            if (null != autoCommodityAll && autoCommodityAll.size() > 0) {
                headItemsList = autoCommodityMapper.selectAll().stream().map(AutoCommodity::getCommodityId).collect(Collectors.toList());
            }
            List<Long> shopIds = pageInfo.getList().stream().map(AutoSettingPageVO::getShopId).collect(Collectors.toList());
            Map<Long, List<Long>> autoCommodityMap = autoShopCommodityService.autoCommodityByShopIds(shopIds);
            //要求单店品项
            List<Long> requireShopItemsList = null;
            //已经设置的单店品项
            List<Long> autoShopCommodityList = null;
            //完成品项列表
            List<Long> finishItemsList = null;
            //上个月销售品项
            List<Long> lastMonthSaleList = null;
            //未完成品项列表
            List<Long> notFinishItemsList = null;
            for (AutoSettingPageVO vo : pageInfo.getList()) {
                vo.setHeadItems(headItems);
                if (!StringUtil.isNullOrEmpty(vo.getLastMonthSaleInfo())) {
                    lastMonthSaleList = JSONObject.parseObject(vo.getLastMonthSaleInfo(), List.class);
                } else {
                    lastMonthSaleList = null;
                }

                requireShopItemsList = getIntersection(headItemsList, lastMonthSaleList);
                vo.setRequireShopItems(requireShopItemsList.size());

                if (autoCommodityMap.containsKey(vo.getShopId())) {
                    autoShopCommodityList = autoCommodityMap.get(vo.getShopId());
                } else {
                    autoShopCommodityList = null;
                }

                finishItemsList = getIntersection(requireShopItemsList, autoShopCommodityList);
                vo.setFinishItems(finishItemsList.size());

                // 未完成品项
                if(CollectionUtils.isNotEmpty(finishItemsList)){
                    List<Long> finalFinishItemsList = finishItemsList;
                    notFinishItemsList = requireShopItemsList.stream().filter(p -> !finalFinishItemsList.contains(p)).collect(Collectors.toList());
                }else {
                    notFinishItemsList = requireShopItemsList;
                }
                vo.setNotFinishItems(notFinishItemsList.size());

                if (null != vo.getRequireShopItems() && vo.getRequireShopItems() != 0) {
                    BigDecimal ratio = new BigDecimal((float)vo.getFinishItems()/vo.getRequireShopItems()).setScale(2, BigDecimal.ROUND_HALF_UP);
                    vo.setFinishRatio(ratio.multiply(new BigDecimal(100)).intValue());
                } else {
                    vo.setFinishRatio(0);
                }
            }
        }

        return pageInfo;
    }

    /**
     * 获取两个List交集
     * @param list1
     * @param list2
     * @return
     */
    private List<Long> getIntersection(List<Long> list1, List<Long> list2) {
        List<Long> res = new ArrayList<Long>();
        if (list1 == null || list1.size() == 0 || null == list2 || list2.size() == 0) {
            return new ArrayList<>();
        }
//        list1.retainAll(list2);
//        return list1;
        list1.forEach(e -> {
            if(list2.contains(e)) {
                res.add(e);
            }
        });
        return res;
    }

    /**
     * 上个月有销售的品项
     * @param vo
     * @return
     */
    public PageInfo<AutoShopCommodityListVO> lastMonthSalePage(AutoShopCommodityRequestVO vo) {
        QYAssert.isTrue(null != vo.getShopId() , "门店不能为空");
        AutoSetting autoSetting = settingByShopId(vo.getShopId());
        if (null != autoSetting && !StringUtil.isBlank(autoSetting.getLastMonthSaleInfo())) {
            //JSONObject jsonObject = JSONObject.parseObject(autoSetting.getLastMonthSaleInfo());
            List<Long> lastMonthSaleList = JSONObject.parseObject(autoSetting.getLastMonthSaleInfo(), List.class);
            vo.setCommodityIdList(lastMonthSaleList);

            PageInfo<AutoShopCommodityListVO> pageInfo = PageHelper.startPage(vo.getPageNo(), vo.getPageSize()).doSelectPageInfo(() ->
                    autoShopCommodityMapper.commodityInfoList(vo));
            return pageInfo;
        } else {
            return new PageInfo<>();
        }
    }

    /**
     * 要求单店品项商品列表
     * @param shopId
     */
    public List<Long> requireShopCommodityList(Long shopId) {
        AutoSetting autoSetting = settingByShopId(shopId);
        List<Long> requireShopItemsList = null;
        if (null != autoSetting && !StringUtil.isBlank(autoSetting.getLastMonthSaleInfo())) {
            //上个月有销售的品项
            List<Long> lastMonthSaleList = new ArrayList<>();
            if (!StringUtil.isNullOrEmpty(autoSetting.getLastMonthSaleInfo())) {
                lastMonthSaleList = JSONObject.parseObject(autoSetting.getLastMonthSaleInfo(), List.class);
            }

            //总部规划品项
            List<Long> headItemsList = new ArrayList<>();
            List<AutoCommodity> autoCommodityList = autoCommodityMapper.selectAll();
            if (null != autoCommodityList && autoCommodityList.size() > 0) {
                headItemsList = autoCommodityList.stream().map(AutoCommodity::getCommodityId).collect(Collectors.toList());
            }
            //要求单店品项
            requireShopItemsList = getIntersection(headItemsList, lastMonthSaleList);
        }
        return requireShopItemsList;
    }

    /**
     * 要求单店品项：总部规划品项和上个月有销售的品项  交集
     * @param vo
     * @return
     */
    public PageInfo<AutoShopCommodityListVO> requireShopItemsPage(AutoShopCommodityRequestVO vo) {
        QYAssert.isTrue(null != vo.getShopId() , "门店不能为空");
        //要求单店品项列表
        List<Long> requireShopItemsList = requireShopCommodityList(vo.getShopId());
        if (null != requireShopItemsList && requireShopItemsList.size() > 0) {
            Map<Long, MdShopOrderSettingODTO> map = null;
            //有订货时间的，过滤订货时间
            if (!StringUtil.isNullOrEmpty(vo.getStartTime()) || !StringUtil.isNullOrEmpty(vo.getEndTime())) {
                List<MdShopOrderSettingODTO> orderSetting = commodityOrderFilterEndTime(vo.getStartTime(), vo.getEndTime(), vo.getShopId(), requireShopItemsList);
                if (orderSetting.size() > 0) {
                    requireShopItemsList = orderSetting.stream().map(MdShopOrderSettingODTO::getCommodityIdLong).collect(Collectors.toList());
                    map = orderSetting.stream().collect(Collectors.toMap(MdShopOrderSettingODTO::getCommodityIdLong, e -> e));
                } else {
                    requireShopItemsList = new ArrayList<>();
                }
            }
            if (null != requireShopItemsList && requireShopItemsList.size() > 0) {
                vo.setCommodityIdList(requireShopItemsList);

                PageInfo<AutoShopCommodityListVO> pageInfo = PageHelper.startPage(vo.getPageNo(), vo.getPageSize()).doSelectPageInfo(() ->
                        autoShopCommodityMapper.commodityInfoList(vo));
                if (pageInfo.getSize() > 0) {
                    Shop shop = shopMapper.selectByPrimaryKey(vo.getShopId());
                    if (null == map) {
                        List<Long> commodityIds = pageInfo.getList().stream().map(AutoShopCommodityListVO::getCommodityId).collect(Collectors.toList());
                        map = commodityOrderEndTime(vo.getShopId(), commodityIds);
                    }
                    MdShopOrderSettingODTO mdShopOrderSettingODTO = null;
                    for (AutoShopCommodityListVO commodity : pageInfo.getList()) {
                        if (null != map && map.containsKey(commodity.getCommodityId())) {
                            mdShopOrderSettingODTO = map.get(commodity.getCommodityId());
                            if (mdShopOrderSettingODTO.getLogisticsModel().equals(IogisticsModelEnums.DISPATCHING.getCode())) {
                                commodity.setEndTime(mdShopOrderSettingODTO.getDefaultWarehouseEndTime());
                            }
                            if (mdShopOrderSettingODTO.getLogisticsModel().equals(IogisticsModelEnums.DIRECT_CONNECTION.getCode()) || mdShopOrderSettingODTO.getLogisticsModel().equals(IogisticsModelEnums.DIRECT_SENDING.getCode())) {
                                commodity.setEndTime(mdShopOrderSettingODTO.getDefaultSupplierEndTime());
                            }
                        }
                        if (shop.getShopType().equals(ShopTypeEnums.XD.getCode())) {
                            commodity.setCommodityPackageSpec(commodity.getXdSalesBoxCapacity());
                        } else {
                            commodity.setCommodityPackageSpec(commodity.getSalesBoxCapacity());
                        }

                    }
                }
                return pageInfo;
            }
        }
        return new PageInfo<>();
    }

    /**
     * 完成品项列表 = 要求单店品项 和 已设置品项列表 交集
     * @param vo
     * @return
     */
    public PageInfo<AutoShopCommodityListVO> finishItemsPage(AutoShopCommodityRequestVO vo) {
        QYAssert.isTrue(null != vo.getShopId() , "门店不能为空");
        //要求单店品项列表
        List<Long> requireShopItemsList = requireShopCommodityList(vo.getShopId());

        //门店已设置品项列表
        List<Long> shopIdList = new ArrayList<>();
        shopIdList.add(vo.getShopId());
        Map<Long, List<Long>> autoCommodityMap = autoShopCommodityService.autoCommodityByShopIds(shopIdList);
        List<Long> setItems = autoCommodityMap.get(vo.getShopId());
        //已完成品项列表
        List<Long>  finishItemsCommodityList = getIntersection(requireShopItemsList, setItems);
        if (null != finishItemsCommodityList && finishItemsCommodityList.size() > 0) {
            Map<Long, MdShopOrderSettingODTO> map = null;
            //有订货时间的，过滤订货时间
            if (!StringUtil.isNullOrEmpty(vo.getStartTime()) || !StringUtil.isNullOrEmpty(vo.getEndTime())) {
                List<MdShopOrderSettingODTO> orderSetting = commodityOrderFilterEndTime(vo.getStartTime(), vo.getEndTime(), vo.getShopId(), finishItemsCommodityList);
                if (orderSetting.size() > 0) {
                    finishItemsCommodityList = orderSetting.stream().map(MdShopOrderSettingODTO::getCommodityIdLong).collect(Collectors.toList());
                    map = orderSetting.stream().collect(Collectors.toMap(MdShopOrderSettingODTO::getCommodityIdLong, e -> e));
                } else {
                    finishItemsCommodityList = new ArrayList<>();
                }
            }
            if (null != finishItemsCommodityList && finishItemsCommodityList.size() > 0) {
                vo.setCommodityIdList(finishItemsCommodityList);

                PageInfo<AutoShopCommodityListVO> pageInfo = PageHelper.startPage(vo.getPageNo(), vo.getPageSize()).doSelectPageInfo(() ->
                        autoShopCommodityMapper.commodityInfoList(vo));
                if (pageInfo.getSize() > 0) {
                    if (null == map) {
                        List<Long> commodityIds = pageInfo.getList().stream().map(AutoShopCommodityListVO::getCommodityId).collect(Collectors.toList());
                        map = commodityOrderEndTime(vo.getShopId(), commodityIds);
                    }
                    Shop shop = shopMapper.selectByPrimaryKey(vo.getShopId());
                    MdShopOrderSettingODTO mdShopOrderSettingODTO = null;
                    for (AutoShopCommodityListVO commodity : pageInfo.getList()) {
                        if (null != map && map.containsKey(commodity.getCommodityId())) {
                            mdShopOrderSettingODTO = map.get(commodity.getCommodityId());
                            if (mdShopOrderSettingODTO.getLogisticsModel().equals(IogisticsModelEnums.DISPATCHING.getCode())) {
                                commodity.setEndTime(mdShopOrderSettingODTO.getDefaultWarehouseEndTime());
                            }
                            if (mdShopOrderSettingODTO.getLogisticsModel().equals(IogisticsModelEnums.DIRECT_CONNECTION.getCode()) || mdShopOrderSettingODTO.getLogisticsModel().equals(IogisticsModelEnums.DIRECT_SENDING.getCode())) {
                                commodity.setEndTime(mdShopOrderSettingODTO.getDefaultSupplierEndTime());
                            }
                        }

                        if (shop.getShopType().equals(ShopTypeEnums.XD.getCode())) {
                            commodity.setCommodityPackageSpec(commodity.getXdSalesBoxCapacity());
                        } else {
                            commodity.setCommodityPackageSpec(commodity.getSalesBoxCapacity());
                        }

                    }
                }
                return pageInfo;
            }
        }
        return new PageInfo<>();
    }


    /**
     * 未完成品项：就是要求单店品项的商品池 剔除掉 已完成品项,剩下的就是未完成品项
     * @param vo
     * @return
     */
    public PageInfo<AutoShopCommodityListVO> notFinishedItemsPage(AutoShopCommodityRequestVO vo) {
        QYAssert.isTrue(null != vo.getShopId() , "门店不能为空");
        //要求单店品项列表
        List<Long> requireShopItemsList = requireShopCommodityList(vo.getShopId());

        //门店已设置品项列表
        List<Long> shopIdList = new ArrayList<>();
        shopIdList.add(vo.getShopId());
        Map<Long, List<Long>> autoCommodityMap = autoShopCommodityService.autoCommodityByShopIds(shopIdList);
        List<Long> setItems = autoCommodityMap.get(vo.getShopId());
        //已完成品项列表
        List<Long> finishItemsCommodityList = getIntersection(requireShopItemsList, setItems);
        if(CollectionUtils.isNotEmpty(finishItemsCommodityList)){
            // 要求单店品项的商品池 剔除掉 已完成品项
            requireShopItemsList = requireShopItemsList.stream().filter(p -> !finishItemsCommodityList.contains(p)).collect(Collectors.toList());
        }

        if (CollectionUtils.isNotEmpty(requireShopItemsList)) {
            Map<Long, MdShopOrderSettingODTO> map = null;
            //有订货时间的，过滤订货时间
            if (!StringUtil.isNullOrEmpty(vo.getStartTime()) || !StringUtil.isNullOrEmpty(vo.getEndTime())) {
                List<MdShopOrderSettingODTO> orderSetting = commodityOrderFilterEndTime(vo.getStartTime(), vo.getEndTime(), vo.getShopId(), requireShopItemsList);
                if (orderSetting.size() > 0) {
                    requireShopItemsList = orderSetting.stream().map(item -> Long.valueOf(item.getCommodityId())).collect(Collectors.toList());
                    map = orderSetting.stream().collect(Collectors.toMap(MdShopOrderSettingODTO::getCommodityIdLong, e -> e));
                } else {
                    requireShopItemsList = new ArrayList<>();
                }
            }
            if (null != requireShopItemsList && requireShopItemsList.size() > 0) {
                vo.setCommodityIdList(requireShopItemsList);

                PageInfo<AutoShopCommodityListVO> pageInfo = PageHelper.startPage(vo.getPageNo(), vo.getPageSize()).doSelectPageInfo(() ->
                        autoShopCommodityMapper.commodityInfoList(vo));
                if (pageInfo.getSize() > 0) {
                    Shop shop = shopMapper.selectByPrimaryKey(vo.getShopId());
                    if (null == map) {
                        List<Long> commodityIds = pageInfo.getList().stream().map(AutoShopCommodityListVO::getCommodityId).collect(Collectors.toList());
                        map = commodityOrderEndTime(vo.getShopId(), commodityIds);
                    }
                    MdShopOrderSettingODTO mdShopOrderSettingODTO = null;
                    for (AutoShopCommodityListVO commodity : pageInfo.getList()) {
                        if (null != map && map.containsKey(commodity.getCommodityId())) {
                            mdShopOrderSettingODTO = map.get(commodity.getCommodityId());
                            if (mdShopOrderSettingODTO.getLogisticsModel().equals(IogisticsModelEnums.DISPATCHING.getCode())) {
                                commodity.setEndTime(mdShopOrderSettingODTO.getDefaultWarehouseEndTime());
                            }
                            if (mdShopOrderSettingODTO.getLogisticsModel().equals(IogisticsModelEnums.DIRECT_CONNECTION.getCode()) || mdShopOrderSettingODTO.getLogisticsModel().equals(IogisticsModelEnums.DIRECT_SENDING.getCode())) {
                                commodity.setEndTime(mdShopOrderSettingODTO.getDefaultSupplierEndTime());
                            }
                        }
                        if (shop.getShopType().equals(ShopTypeEnums.XD.getCode())) {
                            commodity.setCommodityPackageSpec(commodity.getXdSalesBoxCapacity());
                        } else {
                            commodity.setCommodityPackageSpec(commodity.getSalesBoxCapacity());
                        }

                    }
                }
                return pageInfo;
            }
        }
        return new PageInfo<>();
    }

    /**
     * 获取商品的订货截止时间
     */
    public Map<Long, MdShopOrderSettingODTO> commodityOrderEndTime(Long shopId, List<Long> commodityIds) {
        Shop shop = shopMapper.selectByPrimaryKey(shopId);
        List<String> commodityIdStr = commodityIds.stream().map(e -> {
            return e.toString();
        }).collect(Collectors.toList());

        MdShopOrderSettingQueryIDTO queryIDTO = new MdShopOrderSettingQueryIDTO();
        queryIDTO.setShopType(shop.getShopType());
        queryIDTO.setCommodityIds(commodityIdStr);
        //获取商品的订货截止时间
        List<MdShopOrderSettingODTO> orderList = mdShopOrderSettingClient.queryMdShopOrderSettingListByShopType(queryIDTO);
        Map<Long, MdShopOrderSettingODTO> map = new HashMap<>(orderList.size());
        if (null != orderList && orderList.size() > 0) {
            orderList.forEach(e -> {
                map.put(Long.valueOf(e.getCommodityId()), e);
            });
            //map = orderList.stream().collect(Collectors.toMap(MdShopOrderSettingODTO::getCommodityId, e -> e));
        }
        return map;
    }

    /**
     * 根据订货截止时间，过滤商品
     * @param commodityIds
     */
    public List<MdShopOrderSettingODTO> commodityOrderFilterEndTime(String startTime, String endTime, Long shopId, List<Long> commodityIds) {
        List<MdShopOrderSettingODTO> res = new ArrayList<>();
        if (null == commodityIds || commodityIds.size() ==0 || (StringUtil.isNullOrEmpty(endTime) && StringUtil.isNullOrEmpty(startTime))) {
            return res;
         }
        Shop shop = shopMapper.selectByPrimaryKey(shopId);
        List<String> commodityIdStr = commodityIds.stream().map(e -> {
            return e.toString();
        }).collect(Collectors.toList());

        MdShopOrderSettingQueryIDTO queryIDTO = new MdShopOrderSettingQueryIDTO();
        queryIDTO.setShopType(shop.getShopType());
        queryIDTO.setCommodityIds(commodityIdStr);
        //获取商品的订货截止时间
        List<MdShopOrderSettingODTO> orderList = mdShopOrderSettingClient.queryMdShopOrderSettingListByShopType(queryIDTO);

        if (null != orderList && orderList.size() > 0) {
            for (MdShopOrderSettingODTO order : orderList) {
                String time = null;
                if (order.getLogisticsModel().equals(IogisticsModelEnums.DISPATCHING.getCode())) {
                    time = order.getDefaultWarehouseEndTime();
                } else if (order.getLogisticsModel().equals(IogisticsModelEnums.DIRECT_CONNECTION.getCode()) || order.getLogisticsModel().equals(IogisticsModelEnums.DIRECT_SENDING.getCode())) {
                    time = order.getDefaultSupplierEndTime();
                }
                if (!StringUtil.isNullOrEmpty(time)) {
//                    Boolean opinion = (!StringUtil.isNullOrEmpty(startTime) && time.compareTo(startTime) >= 0) &&
//                            (!StringUtil.isNullOrEmpty(endTime) && time.compareTo(endTime) <= 0);
                    Boolean start = StringUtil.isNullOrEmpty(startTime) || (!StringUtil.isNullOrEmpty(startTime) && time.compareTo(startTime) >= 0);
                    Boolean end = StringUtil.isNullOrEmpty(endTime) || (!StringUtil.isNullOrEmpty(endTime) && time.compareTo(endTime) <= 0);
                    if (start && end) {
                        res.add(order);
                    }
                }
            }
        }
        return res;
    }


    /**
     * 查询规划池限制开关
     * @return
     */
    public AutoOverZbCommodityDTO getAutoOverZbCommodity() {
        DictionaryODTO dictionaryODTO = dictionaryClient.getDictionaryByCode("autoOverZbCommodity");
        QYAssert.isTrue(dictionaryODTO != null , "请配置规划池限制开关");

        AutoOverZbCommodityDTO autoOverZbCommodityDTO = new AutoOverZbCommodityDTO();
        autoOverZbCommodityDTO.setId(dictionaryODTO.getId());
        autoOverZbCommodityDTO.setOptionValue(Integer.valueOf(dictionaryODTO.getOptionValue()));
        return autoOverZbCommodityDTO;
    }

    /**
     * 更新规划池限制开关
     * @param idto
     * @return
     */
    public Boolean updateAutoOverZbCommodity(AutoOverZbCommodityDTO idto) {
        TokenInfo tokenInfo = FastThreadLocalUtil.getQY();
        QYAssert.isTrue(idto != null , "参数错误");
        QYAssert.isTrue(idto.getId() != null , "参数错误");

        DictionaryEditIDTO editIDTO = new DictionaryEditIDTO();
        editIDTO.setId(Long.valueOf(idto.getId()));
        editIDTO.setOptionValue(idto.getOptionValue() + "");
        editIDTO.setOptionName("规划池限制开关");
        editIDTO.setMemo("1:允许超出规划品项  0:禁止超出规划品项");
        editIDTO.setSort(1);
        editIDTO.setUserId(tokenInfo.getUserId());
        dictionaryClient.editDictionary(editIDTO);

        return Boolean.TRUE;
    }
}

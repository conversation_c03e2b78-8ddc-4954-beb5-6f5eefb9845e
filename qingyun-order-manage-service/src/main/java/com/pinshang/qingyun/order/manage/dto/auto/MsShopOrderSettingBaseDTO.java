package com.pinshang.qingyun.order.manage.dto.auto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * @Author: sk
 * @Date: 2022/9/6
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class MsShopOrderSettingBaseDTO {
    private Long shopId;
    private Long storeId;
    private String orderTime;
    private Long commodityId;
    private BigDecimal quantity;
    private BigDecimal price;
    private Integer logisticsModel;
    private Long supplierId;
    private Long warehouseId;
    private String deleveryTimeRange;
    private Long enterpriseId;
    private Long userId;
    private String deliveryBatch;
    private String createName;
    private Long consignmentId; //代销商户
    private String commodityCode;
    private String commodityName;

    private Integer presaleStatus; // 是否预售  0 否   1 是
    private Integer stockType; // 库存依据  1=依据大仓, 2=不限量订货, 3=限量供应

    private Long originOrderId;
    private Long originCommodityId;

    /**
     * 原材料比例
     */
    private Integer sourceRatio;
    /**
     * 转成品比例
     */
    private Integer targetRatio;

    /**
     * 组合商品转换的最小单位商品ID
     */
    private Long targetCommodityId;

    /**
     * 组合商品转换状态：0=无转换，1=有转换
     */
    private Integer convertStatus;

    /**
     * 组合商品目标数量 : 向上取整 ( quantity / source_ratio * target_ratio)
     */
    private BigDecimal targetQuantity;



}

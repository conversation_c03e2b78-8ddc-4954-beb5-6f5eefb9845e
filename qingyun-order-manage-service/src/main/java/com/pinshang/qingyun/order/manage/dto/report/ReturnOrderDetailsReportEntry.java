package com.pinshang.qingyun.order.manage.dto.report;

import com.pinshang.qingyun.renderer.annotation.FieldRender;
import com.pinshang.qingyun.renderer.constant.RenderFieldHelper;
import com.pinshang.qingyun.renderer.enums.FieldTypeEnum;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@Data
public class ReturnOrderDetailsReportEntry {
	
	private Date createTime;
	private String orderCode;
	private Long commodityFirstId;
	@FieldRender(fieldType = FieldTypeEnum.COMMODITY_KIND,fieldName = RenderFieldHelper.CommodityKind.commodityFirstKindName,keyName = "commodityId")
	private String cateName;
	private String stallId;
	@FieldRender(fieldType = FieldTypeEnum.STALL,fieldName = RenderFieldHelper.Stall.stallName,keyName = "stallId")
	private String stallName;


	private String barCode;
	private String barCodes;
	private String shopName;
	private String commodityCode;
	private String commodityName;
	private String commoditySpec;
	private BigDecimal price;
	private BigDecimal returnQuantity;
	private BigDecimal totalPrice;
	private BigDecimal realReturnQuantity;

	//退货原因名称
	private String returnReasonName;

	private Integer returnReason;

	public String getReturnReasonName() {
		if(null != returnReason){
			if(returnReason.equals(1)){
				return  "临保";
			}else if(returnReason.equals(2)){
				return "过保";
			}else if(returnReason.equals(3)){
				return "破损";
			}else if(returnReason.equals(4)){
				return "丢失";
			}else if(returnReason.equals(5)){
				return "损坏";
			}else {
				return returnReasonName;
			}
		}
		return "";
	}

	//备注
	private String remark;
	//单位
	@FieldRender(fieldType = FieldTypeEnum.DICTIONARY,fieldName = RenderFieldHelper.Dictionary.optionName,keyName = "commodityUnitId")
	private String commodityUnit;

	private Long storeId;
	private Long commodityUnitId;
	private Long commodityId;

	public String getCateName() {
		return null == cateName ? "" : cateName;
	}
}

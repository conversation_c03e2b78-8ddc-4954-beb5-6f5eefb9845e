package com.pinshang.qingyun.order.manage.dto.freezeGroup;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.pinshang.qingyun.renderer.annotation.FieldRender;
import com.pinshang.qingyun.renderer.constant.RenderFieldHelper;
import com.pinshang.qingyun.renderer.enums.FieldTypeEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @ClassName CommodityFreezeGroupODTO
 * <AUTHOR>
 * @Date 2022/9/29 11:23
 * @Description CommodityFreezeGroupODTO
 * @Version 1.0
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class CommodityFreezeGroupODTO {
    @ExcelIgnore
    @ApiModelProperty("id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    @ExcelIgnore
    @ApiModelProperty("商品id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long commodityId;

    @ExcelProperty(value = "商品编码")
    @FieldRender(fieldName = RenderFieldHelper.Commodity.commodityCode, fieldType = FieldTypeEnum.COMMODITY, keyName = "commodityId")
    @ApiModelProperty("商品编码")
    private String commodityCode;

    @ExcelProperty(value = "商品名称")
    @FieldRender(fieldName = RenderFieldHelper.Commodity.commodityName, fieldType = FieldTypeEnum.COMMODITY, keyName = "commodityId")
    @ApiModelProperty("商品名称")
    private String commodityName;

    @ExcelProperty(value = "规格")
    @FieldRender(fieldName = RenderFieldHelper.Commodity.commoditySpec, fieldType = FieldTypeEnum.COMMODITY, keyName = "commodityId")
    @ApiModelProperty("规格")
    private String commoditySpec;

    @ExcelProperty(value = "条码")
    @ApiModelProperty("条码")
    private String barCode;

    @ExcelProperty(value = "计量单位")
    @FieldRender(fieldName = RenderFieldHelper.Commodity.commodityUnit, fieldType = FieldTypeEnum.COMMODITY, keyName = "commodityId")
    @ApiModelProperty(value = "计量单位")
    private String unitName;

    @ExcelIgnore
    @FieldRender(fieldName = RenderFieldHelper.Commodity.isWeight, fieldType = FieldTypeEnum.COMMODITY, keyName = "commodityId")
    @ApiModelProperty(value = "是否称重")
    private String isWeight;

    @ExcelProperty(value = "是否称重")
    @ApiModelProperty(value = "是否称重-文字")
    private String isWeightStr;

    @ExcelProperty(value = "生产组")
    @ApiModelProperty("生产组")
    private String workShopName;

    @ExcelProperty(value = "工厂")
    @ApiModelProperty("工厂")
    private String factoryName;

    public String getIsWeightStr() {
        return null == isWeight || "0".equals(isWeight) ? "否" : "是";
    }
}

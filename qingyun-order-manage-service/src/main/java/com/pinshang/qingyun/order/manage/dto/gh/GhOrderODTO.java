package com.pinshang.qingyun.order.manage.dto.gh;

import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @Date 2024/12/18 16:42
 */
@Data
public class GhOrderODTO {
    @ApiModelProperty(value = "需求单位（编码）- 高校分组")
    @ExcelProperty(value = "需求单位(编码)")
    private String ghGroupCode;

    @ApiModelProperty(value = "需求部门(编码) - 高校门店编码")
    @ExcelProperty(value = "需求部门(编码)")
    private String ghShopCode;

    @ApiModelProperty(value = "结算公司  - 固定为1")
    @ExcelProperty(value = "结算公司")
    private Integer settlementCompany = 1;

    @ApiModelProperty(value = "发货日期 - 订单的送货日期")
    @ExcelProperty(value = "发货日期")
    private String orderTime;

    @ApiModelProperty(value = "是否退货 - 固定为1")
    @ExcelProperty(value = "是否退货")
    private Integer returnOfGoods = 1;

    @ApiModelProperty(value = "采购员")
    @ExcelProperty(value = "采购员")
    private String purchasingAgent;

    @ApiModelProperty(value = "配送地址")
    @ExcelProperty(value = "配送地址")
    private String deliveryAddress;

    @ApiModelProperty(value = "备注")
    @ExcelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "送货员（司机）")
    @ExcelProperty(value = "送货员（司机）")
    private String deliveryMan;

    @ApiModelProperty(value = "送货员电话")
    @ExcelProperty(value = "送货员电话")
    private String deliveryManTel;

    @ApiModelProperty(value = "车牌号")
    @ExcelProperty(value = "车牌号")
    private String plateNum;

    @ApiModelProperty(value = "产品（编码）")
    @ExcelProperty(value = "产品（编码）")
    private String ghCommodityCode;


    @ApiModelProperty(value = "订单主数量")
    @ExcelProperty(value = "订单主数量")
    private BigDecimal orderNum;

    @ApiModelProperty(value = "主单位原价")
    @ExcelProperty(value = "主单位原价")
    private BigDecimal goodPrice;

    @ApiModelProperty(value = "主单位含税单价")
    @ExcelProperty(value = "主单位含税单价")
    private BigDecimal price;

    @ApiModelProperty(value = "税率")
    @ExcelProperty(value = "税率")
    private Integer taxRate;

    @ApiModelProperty(value = "生产日期")
    @ExcelProperty(value = "生产日期")
    private String manufactureDate;

    @ApiModelProperty(value = "生产厂商")
    @ExcelProperty(value = "生产厂商")
    private String manufacturer;

    @ApiModelProperty(value = "上级供货商")
    @ExcelProperty(value = "上级供货商")
    private String supplier;

    @ApiModelProperty(value = "供应商批次")
    @ExcelProperty(value = "供应商批次")
    private String supplierBatch;

    @ApiModelProperty(value = "供应商品牌")
    @ExcelProperty(value = "供应商品牌")
    private String supplierBrand;

    @ApiModelProperty(value = "下单日期")
    @ExcelProperty(value = "下单日期")
    private String createTime;

    @ApiModelProperty(value = "是否赠品")
    @ExcelProperty(value = "是否赠品")
    private String giftFlag;

    public BigDecimal getPrice(){
        return goodPrice;
    }
}

package com.pinshang.qingyun.order.manage.model.group;

import com.pinshang.qingyun.base.po.BaseSimplePO;

import javax.persistence.Entity;
import javax.persistence.Table;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 
 **/
@Entity
@Table(name = "t_groupon_order_business")
public class GrouponOrdeBusinessModel extends BaseSimplePO {


    /**订单时间 */
    private Date orderTime;

    /** 门店id*/
    private Long shopId;
    /** 商品id*/
    private Long commodityId;
    /** 团购数量*/
    private BigDecimal quantity;
    /** 团购价格 */
    private BigDecimal price;


    public Date getOrderTime() {
        return orderTime;
    }

    public void setOrderTime(Date orderTime) {
        this.orderTime = orderTime;
    }

    public Long getShopId() {
        return shopId;
    }

    public void setShopId(Long shopId) {
        this.shopId = shopId;
    }

    public Long getCommodityId() {
        return commodityId;
    }

    public void setCommodityId(Long commodityId) {
        this.commodityId = commodityId;
    }

    public BigDecimal getQuantity() {
        return quantity;
    }

    public void setQuantity(BigDecimal quantity) {
        this.quantity = quantity;
    }

    public BigDecimal getPrice() {
        return price;
    }

    public void setPrice(BigDecimal price) {
        this.price = price;
    }
}
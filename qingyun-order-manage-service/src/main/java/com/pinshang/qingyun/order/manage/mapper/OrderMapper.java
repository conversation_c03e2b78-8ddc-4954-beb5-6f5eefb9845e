package com.pinshang.qingyun.order.manage.mapper;

import com.pinshang.qingyun.order.manage.dto.settlement.SettlementDetailsReportEntry;
import com.pinshang.qingyun.order.manage.dto.settlement.SettlementDetailsReportVo;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.util.List;

@Repository
public interface OrderMapper{


	/**
	 * 结算明细报表
	 * @param vo
	 * @return
	 */
	List<SettlementDetailsReportEntry> settlementDetailsReport(SettlementDetailsReportVo vo);

	/**
	 * 查询结算总金额
	 * @param vo
	 * @return
	 */
	BigDecimal findTotalSettlePrice(SettlementDetailsReportVo vo);

}


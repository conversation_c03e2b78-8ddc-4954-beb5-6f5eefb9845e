package com.pinshang.qingyun.order.manage.controller.web;

import com.github.pagehelper.PageInfo;
import com.pinshang.qingyun.base.api.TokenInfo;
import com.pinshang.qingyun.base.configure.expand.QYAssert;
import com.pinshang.qingyun.base.util.ExcelUtil;
import com.pinshang.qingyun.base.util.FastThreadLocalUtil;
import com.pinshang.qingyun.infrastructure.exportfile.cache.starter.FileCacheQuery;
import com.pinshang.qingyun.order.manage.dto.government.FactoryRespODTO;
import com.pinshang.qingyun.order.manage.dto.government.GovernmentOrderTraceBackRepIDTO;
import com.pinshang.qingyun.order.manage.dto.government.GovernmentOrderTraceBackRespODTO;
import com.pinshang.qingyun.order.manage.service.GovernmentOrderTraceBackService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.OutputStream;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2021/2/4 14:36
 */
@Slf4j
@RestController
@RequestMapping(value = "/factory")
@Api(value = "工厂订单追溯相关接口",tags ="GovernmentOrderTraceBackController",description ="工厂订单追溯相关接口" )
public class GovernmentOrderTraceBackController {

    @Autowired
    private GovernmentOrderTraceBackService orderTraceBackService;

    @Value("${pinshang.governmentTemplateUrl}")
    private String governmentTemplateUrl;

    /***
     * 查询政府追溯订单列表数据
     * @param vo
     * @return
     */
    @PostMapping(value = "/order/list")
    @ApiOperation(value = "订单追溯列表数据")
    @ApiImplicitParam(name = "vo", value = "请求vo", required = true, paramType = "body", dataTypeClass = GovernmentOrderTraceBackRepIDTO.class)
    public PageInfo<GovernmentOrderTraceBackRespODTO> selectGovernmentOrderTraceBackList(@RequestBody GovernmentOrderTraceBackRepIDTO vo){
        QYAssert.isTrue(vo.getFactoryId()!=null,"请输入需要查询的工厂");
        QYAssert.isTrue(StringUtils.isNotBlank(vo.getDeliveryDate()),"送货日期不能为空,请输入");
        TokenInfo tokenInfo=FastThreadLocalUtil.getQY();
        Long userId=tokenInfo.getUserId();
        vo.setUserId(userId);
        return  orderTraceBackService.selectGovernmentOrderTraceBackList(vo);
    }

    /***
     * 获取用户工厂权限数据
     * @return
     */
    @GetMapping(value = "/factory/list")
    @ApiOperation(value = "获取搜索条件工厂数据")
    public List<FactoryRespODTO> selectFactoryByUserId(){
        TokenInfo tokenInfo=FastThreadLocalUtil.getQY();
        Long userId=tokenInfo.getUserId();
        return orderTraceBackService.selectFactoryByUserId(userId);
    }


    /***
     * 导出政府追溯订单列表数据
     * @return
     */
    @GetMapping(value = "/export/order/list")
    @ApiOperation(value = "订单追溯报表导出")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "factoryId", value = "工厂id", required = true, paramType = "query", dataType = "Long" ),
            @ApiImplicitParam(name = "deliveryDate", value = "时间", required = true, paramType = "query", dataType = "String" ),
            @ApiImplicitParam(name = "productManufacturer", value = "产商名称", required = true, paramType = "query", dataType = "String" )
    })
    @FileCacheQuery(bizCode = "ORDERMANAGE_GOVERNMENT_ORDER")
    public void exportSelectGovernmentOrderTraceBackList(@RequestParam(value = "factoryId",required = false)  Long factoryId,@RequestParam(value = "deliveryDate",required = false)  String deliveryDate,
                                                                 @RequestParam(value = "productManufacturer",required = false) String productManufacturer, HttpServletResponse response)throws Exception {

        QYAssert.isTrue(factoryId != null, "请输入需要查询的工厂");
        QYAssert.isTrue(StringUtils.isNotBlank(deliveryDate), "送货日期不能为空");
        QYAssert.isTrue(StringUtils.isNotBlank(productManufacturer), "产商名称不能为空");
        QYAssert.isTrue(StringUtils.isNotBlank(governmentTemplateUrl), "模板不能为空");
        TokenInfo tokenInfo = FastThreadLocalUtil.getQY();
        Long userId = tokenInfo.getUserId();
        GovernmentOrderTraceBackRepIDTO vo = new GovernmentOrderTraceBackRepIDTO();
        vo.setFactoryId(factoryId);
        vo.setDeliveryDate(deliveryDate);
        vo.setUserId(userId);
        Map<String, Object> map = orderTraceBackService.selectGovernmentOrderTraceBackExportList(vo);
        File newFile = null;
        try {
            newFile = orderTraceBackService.createLocalExcelFile(governmentTemplateUrl, "/var/local/governmentOrder", productManufacturer);
            XSSFWorkbook xssfWorkbook = orderTraceBackService.buildExcelDocument(map,newFile);
            ExcelUtil.setFileNameAndHead(response, newFile.getName().replace(".xlsx",""));
            OutputStream outputStream = response.getOutputStream();
            xssfWorkbook.write(outputStream);
            outputStream.flush();
        } catch (Exception e) {
            log.error("订单追溯报表-导出-导出报错:{}", e);
            ExcelUtil.setExceptionResponse(response);
        } finally {
            if (newFile != null) {
                newFile.delete();
            }
        }
    }

}

package com.pinshang.qingyun.order.manage.mapper.auto;


import com.pinshang.qingyun.base.mybatis.MyMapper;
import com.pinshang.qingyun.order.manage.model.auto.AutoShopCommodityLog;
import com.pinshang.qingyun.order.manage.vo.auto.AutoShopCommodityLogRequestVO;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;


/**
 * <AUTHOR>
 */
@Repository
public interface AutoShopCommodityLogMapper extends MyMapper<AutoShopCommodityLog> {

    /**
     * 批量添加日志
     * @param list
     * @return
     */
    Integer batchSave(@Param("list") List<AutoShopCommodityLog> list);

    /**
     * 查询日志
     * @param vo
     * @return
     */
    List<AutoShopCommodityLog> logList(@Param("vo") AutoShopCommodityLogRequestVO vo);
}
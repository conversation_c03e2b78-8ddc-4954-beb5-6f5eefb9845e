package com.pinshang.qingyun.order.manage.mapper;

import com.pinshang.qingyun.infrastructure.data.query.annotate.DataQuery;
import com.pinshang.qingyun.order.manage.dto.XsjmOrderBillSummaryReportDTO;
import com.pinshang.qingyun.order.manage.dto.report.*;
import com.pinshang.qingyun.order.manage.vo.report.ReturnOrderDetailsReportVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.util.List;

@Mapper
@Repository
public interface OrderReportMapper {

    List<ReturnOrderDetailsReportEntry> findReturnOrderDetails(ReturnOrderDetailsReportVo returnOrderDetailsReportVo);

    @DataQuery(value = "shortDeliveryCode")
    List<ShortDeliveryReportODto> shortDeliveryTodayReport(ShortDeliveryReportIDto vo);

    @DataQuery(value = "queryCode")
    List<RealDeliveryReportODto> realDeliveryReportCurrentDay(RealDeliveryReportDataQueryIDto vo);

    @DataQuery(value = "sumCode")
    BigDecimal realTotalDeliveryReportCurrentDay(RealDeliveryReportDataQueryIDto vo);

    @DataQuery(value = "comRealDeliveryCode")
    List<RealDeliveryReportODto> realDeliveryStoreTypeReportCurrentDay(RealDeliveryReportIDto vo);

    @DataQuery(value = "comRealDeliverySumCode")
    BigDecimal realTotalDeliveryStoreTypeReportCurrentDay(RealDeliveryReportIDto vo);

    @DataQuery(value = "shopOrderSummaryCode")
    List<ShopOrderGoodReportODto> shopOrderGoodReport(ShopOrderGoodReportIDto idto);

    @DataQuery(value = "shopRealReceiveCode")
    List<ActualReceiptAnalysisODto> actualReceiptAnalysisReport(ActualReceiptAnalysisIDto vo);




    List<ConsignmentOrderPageODTO> consignmentOrderReport(@Param("vo") ConsignmentOrderQueryIDTO vo);

    List<SaleReturnDetailPageODTO> saleReturnDetailPage(SaleReturnDetailPageIDTO idto);

    List<SaleReturnReportODTO> listReturnReport(@Param("vo")SaleReturnReportVo vo);

    List<ConsignmentReturnOrderPageODTO> consignmentReturnOrderReport(@Param("vo") ConsignmentReturnOrderQueryIDTO vo);

    List<AutoPreOrderODTO> listShopAutoOrder(@Param("vo") AutoPreOrderIDTO vo);

    SaleReturnReportSumODTO queryReturnReportSumEntry(@Param("vo")SaleReturnReportVo vo);

    List<XsjmOrderBillSummaryReportDTO> xsjmSummaryReport(@Param("shopId") Long shopId, @Param("queryDate") String queryDate);

    BigDecimal selectXsjmStoreBalance(@Param("queryDate")String queryDate,@Param("storeId")Long storeId);
}

package com.pinshang.qingyun.order.manage.service;

import com.alibaba.fastjson.JSONObject;
import com.pinshang.qingyun.base.api.QYApplicationContext;
import com.pinshang.qingyun.box.utils.JsonUtil;
import com.pinshang.qingyun.box.utils.SpringUtil;
import com.pinshang.qingyun.infrastructure.components.IMqSenderComponent;
import com.pinshang.qingyun.infrastructure.components.params.mq.MqMessage;
import com.pinshang.qingyun.kafka.base.KafkaMessageWrapper;
import com.pinshang.qingyun.kafka.base.KafkaTopicConstant;
import com.pinshang.qingyun.kafka.enums.KafkaMessageOperationTypeEnum;
import com.pinshang.qingyun.kafka.enums.KafkaMessageTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class SendLogService {


    @Autowired
    private IMqSenderComponent mqSenderComponent;


    public void sendLog(List<?> logList, String tableName) {
        if (SpringUtil.isEmpty(logList)) {
            return;
        }
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("tableName", tableName);
        jsonObject.put("data", logList);
        this.kafkaSendLog(jsonObject);
    }
    /**
     * 发送kafka日志消息
     * @param jsonObject
     */
    @Async
    public void kafkaSendLog(JSONObject jsonObject) {
        log.info("============================>>>>>>>>>>>>>>>日志消息发送数据:");
        String data = JsonUtil.java2json(jsonObject);
        KafkaMessageWrapper message = new KafkaMessageWrapper(KafkaMessageTypeEnum.LOG_CREATE, data, KafkaMessageOperationTypeEnum.INSERT);
        try {
            mqSenderComponent.send(QYApplicationContext.applicationNameSwitch + KafkaTopicConstant.LOG_CREATE_TOPIC,
                    data,
                    MqMessage.MQ_KAFKA,
                    KafkaMessageTypeEnum.LOG_CREATE.name(),
                    KafkaMessageOperationTypeEnum.INSERT.name());
        } catch (Exception e) {
            log.error("发送消息-出错 json={}", JsonUtil.java2json(message), e);
        }
        log.info("============================>>>>>>>>>>>>>>>日志消息发送成功");
    }
}

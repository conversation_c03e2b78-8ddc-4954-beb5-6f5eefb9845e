package com.pinshang.qingyun.order.manage.dto.report;

import com.pinshang.qingyun.base.page.Pagination;
import com.pinshang.qingyun.infrastructure.data.query.annotate.Change;
import com.pinshang.qingyun.infrastructure.data.query.constant.DataQueryConstant;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class ShortDeliveryReportIDto extends Pagination{

	private static final long serialVersionUID = 1L;

	private Long enterpriseId;
	
	private Long shopId;

	@ApiModelProperty("组织code")
	private String orgCode;

	/** 一级分类id */
	private Long    cateId1;
	/** 二级分类id */
	private Long    cateId2;
	/** 三级分类id */
	private Long    cateId3;
	
	private String beginDate;
	@Change(value = DataQueryConstant.NOW)
	private String endDate;

	private String commodityKey;

	private String barCode;

	private String orderCode;

	private String factoryCode;

	private Boolean differ;
	private Integer shopType;
	private  Long commodityId;

	private Integer managementMode;

	private Integer isHq;

	private List<Long> commodityIdList;
	private List<Long> storeIdList;
	private List<Long> shopIdList;

	private Long consignmentId; // 代销商户id

	private Long stallId;

	private List<Long> stallList;

	@ApiModelProperty("门店类型List")
	private List<Integer> shopTypeList;

	@ApiModelProperty("经营模式List：1-直营、2-外包")
	private List<Integer> managementModeList;
}

package com.pinshang.qingyun.order.manage.service;/**
 * @Author: sk
 * @Date: 2025/7/16
 */

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.pinshang.qingyun.base.api.TokenInfo;
import com.pinshang.qingyun.base.configure.expand.QYAssert;
import com.pinshang.qingyun.base.enums.YesOrNoEnums;
import com.pinshang.qingyun.base.enums.commodity.CommodityProductTypeEnum;
import com.pinshang.qingyun.base.util.FastThreadLocalUtil;
import com.pinshang.qingyun.box.utils.DateUtil;
import com.pinshang.qingyun.box.utils.JsonUtil;
import com.pinshang.qingyun.box.utils.SpringUtil;
import com.pinshang.qingyun.order.manage.dto.CommodityODTO;
import com.pinshang.qingyun.order.manage.dto.commodity.PlanCommodityLogODTO;
import com.pinshang.qingyun.order.manage.dto.commodity.PlanCommodityODTO;
import com.pinshang.qingyun.order.manage.dto.commodity.PlanCommodityQueryIDTO;
import com.pinshang.qingyun.order.manage.dto.commodity.PlanCommoditySaveIDTO;
import com.pinshang.qingyun.order.manage.enums.PlanCommodityOperateEnums;
import com.pinshang.qingyun.order.manage.mapper.CommodityMapper;
import com.pinshang.qingyun.order.manage.mapper.PlanCommodityMapper;
import com.pinshang.qingyun.order.manage.model.PlanCommodity;
import com.pinshang.qingyun.renderer.service.IRenderService;
import com.pinshang.qingyun.tob.integration.dto.ConfirmRequestDTO;
import com.pinshang.qingyun.tob.integration.dto.InnerCallErrorDTO;
import com.pinshang.qingyun.tob.integration.dto.SendRecordODTO;
import com.pinshang.qingyun.tob.integration.dto.SfSendRequestIDTO;
import com.pinshang.qingyun.tob.integration.dto.commodity.in.Item;
import com.pinshang.qingyun.tob.integration.enums.BusinessStatusEnum;
import com.pinshang.qingyun.tob.integration.enums.ConfirmStatusEnum;
import com.pinshang.qingyun.tob.integration.enums.SfInterFaceEnum;
import com.pinshang.qingyun.tob.integration.enums.SuccessEnum;
import com.pinshang.qingyun.tob.integration.service.TobItgInterfaceCallRecordClient;
import com.pinshang.qingyun.tob.integration.sfep.service.SfSendRequestClient;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.entity.Example;

import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025年07月16日 下午1:45
 */
@Slf4j
@Service
public class PlanCommodityService {

    @Autowired
    private PlanCommodityMapper planCommodityMapper;

    @Autowired
    private IRenderService renderService;
    @Autowired
    private CommodityMapper commodityMapper;
    @Autowired
    private SendLogService sendLogService;
    @Autowired
    private SfSendRequestClient sfSendRequestClient;
    @Autowired
    private TobItgInterfaceCallRecordClient tobItgInterfaceCallRecordClient;

    public PageInfo<PlanCommodityODTO> queryPlanCommodityList(PlanCommodityQueryIDTO vo) {

        PageInfo<PlanCommodityODTO> pageDate = PageHelper.startPage(vo.getPageNo(), vo.getPageSize()).doSelectPageInfo(() -> {
            planCommodityMapper.queryPlanCommodityList(vo);
        });


        if(CollectionUtils.isNotEmpty(pageDate.getList())) {
            renderService.render(pageDate.getList(), "/planCommodity/queryPlanCommodityList");
        }
        return pageDate;

    }


    /**
     * 批量添加商品
     * @param saveIDTO
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public Boolean batchSavePalnCommodity(PlanCommoditySaveIDTO saveIDTO) {
        TokenInfo ti = FastThreadLocalUtil.getQY();
        Date nowTime = new Date();

        // 校验，并且返回商品idList
        List<CommodityODTO> commodityList = savePlanCommodityCheck(saveIDTO);
        List<Long> commodityIdList = commodityList.stream().map(item -> item.getCommodityId()).collect(Collectors.toList());

        // 批量保存计算商品
        batchSavePlanCommodity(commodityIdList, nowTime, ti.getUserId());

        // 推送至顺丰
        pushToSf(commodityList, ti.getUserId());

        // 记录log
        sendLogList(commodityList, ti.getUserId(), ti.getRealName(),
                     nowTime, PlanCommodityOperateEnums.BATCH_ADD.getCode());

        return Boolean.TRUE;
    }


    private void sendLogList(List<CommodityODTO> commodityList, Long userId, String realName,
                             Date nowTime , Integer operateType){
        List<PlanCommodityLogODTO> logList = new ArrayList<>();
        for(CommodityODTO item : commodityList) {
            PlanCommodityLogODTO log = new PlanCommodityLogODTO();
            log.setCommodityId(item.getCommodityId());
            log.setCommodityCode(item.getCommodityCode());
            log.setCommodityName(item.getCommodityName());
            log.setOperateType(operateType);
            log.setCreateId(userId);
            log.setCreateName(realName);
            log.setCreateTime(DateUtil.getDateFormate(nowTime, "yyyy-MM-dd HH:mm:ss"));
            logList.add( log);
        }
        sendLogService.sendLog(logList, "t_log_plan_commodity");
    }

    /**
     * 批量保存计划商品
     * @param saveCommodityIdList
     * @param nowTime
     * @param ti
     */
    private void batchSavePlanCommodity(List<Long> commodityIdList, Date nowTime, Long userId) {
        List<PlanCommodity> insertList = new ArrayList<>();
        Example example = new Example(PlanCommodity.class);
        example.createCriteria().andIn("commodityId", commodityIdList);
        List<PlanCommodity> planCommodityList = planCommodityMapper.selectByExample(example);

        if(CollectionUtils.isNotEmpty(planCommodityList)) {
            List<Long> existIdList = planCommodityList.stream().map(item -> item.getCommodityId()).collect(Collectors.toList());
            List<Long> insertCommodityIdList = commodityIdList.stream().filter(p -> !existIdList.contains( p)).collect(Collectors.toList());

            if(CollectionUtils.isNotEmpty(insertCommodityIdList)) {
                setSavePlanCommodityList(insertCommodityIdList, nowTime, userId, insertList);
            }
        }else {
            setSavePlanCommodityList(commodityIdList, nowTime, userId, insertList);
        }

        // 批量新增
        if(CollectionUtils.isNotEmpty(insertList)) {
            planCommodityMapper.insertList(insertList);
        }
    }

    /**
     * 组装保存list
     * @param insertIdList
     * @param nowTime
     * @param ti
     * @param saveList
     */
    private void setSavePlanCommodityList(List<Long> insertCommodityIdList, Date nowTime,
                                          Long userId, List<PlanCommodity> insertList) {
        insertCommodityIdList.forEach(commodityId -> {
            PlanCommodity planCommodity = new PlanCommodity();
            planCommodity.setCommodityId(commodityId);
            planCommodity.setSyncStatus(YesOrNoEnums.YES.getCode());
            planCommodity.setSyncTime(nowTime);
            planCommodity.setCreateId(userId);
            planCommodity.setCreateTime(nowTime);
            planCommodity.setUpdateId(userId);
            planCommodity.setUpdateTime(nowTime);
            insertList.add(planCommodity);
        });
    }

    /**
     * 批量添加商品 -》 校验
     * @param saveIDTO
     * @return
     */
    private List<CommodityODTO> savePlanCommodityCheck(PlanCommoditySaveIDTO saveIDTO) {
        String commodityKey = saveIDTO.getCommodityKey();
        QYAssert.isTrue(StringUtils.isNotBlank(commodityKey), "商品编码不能为空!");

        List<String> codesList =  Arrays.asList(commodityKey.split("\n"));
        QYAssert.isTrue(SpringUtil.isNotEmpty(codesList), "请以回车分割商品编码!");

        // 去除空格
        codesList = codesList.stream()
                .map(String::trim) // 先去除首尾空格
                .map(s -> s.replaceAll("\\s", "")) // 去除所有空格
                .collect(Collectors.toList());

        QYAssert.isTrue(codesList.size() <= 200, "最多200个商品!");

        List<CommodityODTO> commodityODTOList = commodityMapper.queryCommodityByCodes(codesList);
        QYAssert.isTrue(CollectionUtils.isNotEmpty(commodityODTOList), "商品编码全部不存在!");

        Map<String, CommodityODTO> commodityMap = commodityODTOList.stream().collect(Collectors.toMap(CommodityODTO::getCommodityCode, Function.identity()));

        Set<String> notExistCodeList = new HashSet<>();
        Set<String> weightCodeList = new HashSet<>();
        Set<String> combCodeList = new HashSet<>();

        for(String code: codesList) {
            if(commodityMap.containsKey(code)) {
                CommodityODTO commodityODTO = commodityMap.get(code);
                if(YesOrNoEnums.YES.getCode().equals(commodityODTO.getIsWeight())) {
                    weightCodeList.add( code);
                }

                if(CommodityProductTypeEnum.GROUP.getCode().equals(commodityODTO.getProductType())) {
                    combCodeList.add( code);
                }
            }else {
                notExistCodeList.add(code);
            }
        }

        if(CollectionUtils.isNotEmpty(notExistCodeList)) {
            QYAssert.isFalse("以下商品编码不存在：" + getCommodityCodeErrMsg(new ArrayList<>(notExistCodeList)));
        }

        if(CollectionUtils.isNotEmpty(weightCodeList)) {
            QYAssert.isFalse("暂不支持称重商品，商品编码：" + getCommodityCodeErrMsg(new ArrayList<>(weightCodeList)));
        }

        if(CollectionUtils.isNotEmpty(combCodeList)) {
            QYAssert.isFalse("暂不支持组合商品，商品编码：" + getCommodityCodeErrMsg(new ArrayList<>(combCodeList)));
        }

        return commodityODTOList;
    }


    /**
     * 商品编码有误时的组织形式：多于5个则枚举5个其余省略
     * @param errList
     * @return
     */
    protected String getCommodityCodeErrMsg(List<String> errList) {
        String errMsg = null;
        if (SpringUtil.isNotEmpty(errList)) {
            if (errList.size() > 5) {
                String tempMsg = errList.subList(0, 5).toString();
                int length = tempMsg.length();
                errMsg = tempMsg.substring(0, length - 1) + ",..." + tempMsg.substring(length - 1);
            } else {
                errMsg = errList.toString();
            }
        }
        return errMsg;
    }


    /**
     * 批量删除计划商品
     * @param saveIDTO
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public Boolean batchDeletePalnCommodity(PlanCommoditySaveIDTO saveIDTO) {
        QYAssert.isTrue(CollectionUtils.isNotEmpty(saveIDTO.getCommodityIdList()), "请选择商品!");

        TokenInfo ti = FastThreadLocalUtil.getQY();
        Date nowTime = new Date();

        Example example = new Example(PlanCommodity.class);
        example.createCriteria().andIn("commodityId", saveIDTO.getCommodityIdList());
        List<PlanCommodity> planCommodityList = planCommodityMapper.selectByExample(example);
        if(CollectionUtils.isNotEmpty(planCommodityList)) {
            planCommodityMapper.deleteByExample( example);

            List<Long> commodityIdList = planCommodityList.stream().map(item -> item.getCommodityId()).collect(Collectors.toList());
            List<CommodityODTO> commodityList = commodityMapper.queryCommodityByIdList(commodityIdList);

            // 记录log
            sendLogList(commodityList, ti.getUserId(), ti.getRealName(),
                    nowTime, PlanCommodityOperateEnums.BATCH_DELETE.getCode());
        }

        return Boolean.TRUE;
    }

    /**
     * 手动推送至顺丰
     * @param saveIDTO
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public Boolean handPushToTms(PlanCommoditySaveIDTO saveIDTO) {
        QYAssert.isTrue(CollectionUtils.isNotEmpty(saveIDTO.getCommodityIdList()), "请选择商品!");

        TokenInfo ti = FastThreadLocalUtil.getQY();
        Date nowTime = new Date();

        Example example = new Example(PlanCommodity.class);
        example.createCriteria().andIn("commodityId", saveIDTO.getCommodityIdList());
        List<PlanCommodity> planCommodityList = planCommodityMapper.selectByExample(example);
        if(CollectionUtils.isNotEmpty(planCommodityList)) {
            List<Long> commodityIdList = planCommodityList.stream().map(item -> item.getCommodityId()).collect(Collectors.toList());
            List<CommodityODTO> commodityList = commodityMapper.queryCommodityByIdList(commodityIdList);

            // 记录log
            sendLogList(commodityList, ti.getUserId(), ti.getRealName(),
                    nowTime, PlanCommodityOperateEnums.HAND_SYNC.getCode());

            // 推送至顺丰
            pushToSf(commodityList, ti.getUserId());
        }

        return Boolean.TRUE;
    }

    /**
     * 推送至顺丰 同步字段：商品编码、商品名称、规格、计量单位、保质期
     * @param commodityList
     */
    private void pushToSf(List<CommodityODTO> commodityList, Long userId) {
        Date nowTime = new Date();
        List<Item> itemList = new ArrayList<>();
        Map<String,String> columns = new HashMap<>();
        commodityList.forEach(commodity -> {
            Item item = new Item();
            item.setSkuNo(commodity.getCommodityCode());
            item.setItemName(commodity.getCommodityName());
            item.setStandardDescription(commodity.getCommoditySpec());
            item.setQuantityUm(commodity.getCommodityUnit());
            if(commodity.getQualityDays() != null) {
                item.setShelfLife(new BigDecimal(commodity.getQualityDays()));
            }
            itemList.add( item);
            columns.put("column1", commodity.getCommodityCode());
            columns.put("column2", commodity.getCommodityName());
            columns.put("column3", commodity.getCommoditySpec());
            columns.put("column4", commodity.getCommodityUnit());
            columns.put("column5", commodity.getQualityDays() + "");
        });

        SfSendRequestIDTO sfSendRequestDTO = new SfSendRequestIDTO();
        sfSendRequestDTO.setInterFaceEnum(SfInterFaceEnum.COMMODITY_INTERFACE);
        sfSendRequestDTO.setDataValue(itemList);
        sfSendRequestDTO.setColumns(columns);
        log.info("推送商品至顺丰入参：{}", JsonUtil.java2json(sfSendRequestDTO));

        List<Long> idList = commodityList.stream().map(item -> item.getCommodityId()).collect(Collectors.toList());
        Example pcExample = new Example(PlanCommodity.class);
        pcExample.createCriteria().andIn("commodityId", idList);

        try{
            SendRecordODTO response = sfSendRequestClient.sendRequest(sfSendRequestDTO);
            log.info("推送商品至顺丰结果：{}", JsonUtil.java2json(response));
            if(response != null && SuccessEnum.SUCCESS.equals(response.getSuccess()) && response.getExtendId() != null) {
                ConfirmRequestDTO confirmRequestDTO = new ConfirmRequestDTO();
                confirmRequestDTO.setConfirmStatusEnum(ConfirmStatusEnum.CONFIRM);
                confirmRequestDTO.setBusinessStatusEnum(BusinessStatusEnum.SUCCESS);
                confirmRequestDTO.setId(response.getExtendId());
                confirmRequestDTO.setConfirmBody(JsonUtil.java2json(sfSendRequestDTO));
                tobItgInterfaceCallRecordClient.confirmRequest(confirmRequestDTO);

                // 标记同步成功
                syncSuccess(userId, nowTime, pcExample);
            }else {
                syncFail(pcExample, sfSendRequestDTO, null);
            }
        }catch (Exception e){
            log.error("推送商品至顺丰异常", e);
            // 标记同步失败
            syncFail(pcExample, sfSendRequestDTO, e.getMessage());
        }

    }

    private void syncSuccess(Long userId, Date nowTime, Example pcExample) {
        PlanCommodity update = new PlanCommodity();
        update.setUpdateId(userId);
        update.setUpdateTime(nowTime);
        update.setSyncStatus(YesOrNoEnums.YES.getCode());
        update.setSyncTime(nowTime);
        planCommodityMapper.updateByExampleSelective(update , pcExample);
    }

    private void syncFail(Example pcExample, SfSendRequestIDTO sfSendRequestDTO, String errorMsg) {
        // 标记同步失败
        PlanCommodity update = new PlanCommodity();
        update.setSyncStatus(YesOrNoEnums.NO.getCode());
        planCommodityMapper.updateByExampleSelective(update , pcExample);

        InnerCallErrorDTO innerCallErrorDTO = new InnerCallErrorDTO();
        innerCallErrorDTO.setSfInterFaceEnum(SfInterFaceEnum.COMMODITY_INTERFACE);
        innerCallErrorDTO.setRequestBody(JsonUtil.java2json(sfSendRequestDTO));
        innerCallErrorDTO.setErrorMsg(errorMsg);
        tobItgInterfaceCallRecordClient.saveCallError(innerCallErrorDTO);
    }
}

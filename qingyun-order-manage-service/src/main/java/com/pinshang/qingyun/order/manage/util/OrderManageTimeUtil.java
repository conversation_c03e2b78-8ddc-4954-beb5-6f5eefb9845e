package com.pinshang.qingyun.order.manage.util;

import com.pinshang.qingyun.box.utils.DateUtil;

import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;

/**
 * @Author: sk
 * @Date: 2024/12/4
 */
public class OrderManageTimeUtil {

    /**
     * 传入时分，返回时分（18:30）
     * @param time
     * @param amount
     * @return
     */
    public static String addMinute(String time, int amount){
        String currentDate = DateUtil.getDateFormate(new Date(),"yyyy-MM-dd");
        Date date = DateUtil.parseDate(currentDate + " " + time + ":00","yyyy-MM-dd HH:mm:ss");
        Calendar c = Calendar.getInstance();
        c.setTime(date);
        c.add(Calendar.MINUTE, amount);
        return DateUtil.getDateFormate(c.getTime(), "HH:mm");
    }

    public static String getBeginDeliveryTimeRange(String deliveryTimeRange, int addDay){
        String timeRange  = deliveryTimeRange.split("-")[0];
        Calendar calendar = Calendar.getInstance();
        if(Integer.valueOf(timeRange) == 0){
            calendar.add(Calendar.DATE,1);
        }else {
            calendar.add(Calendar.DATE,Integer.valueOf(timeRange) + addDay);
        }
        return DateUtil.getDateFormate(calendar.getTime(),"yyyy-MM-dd");
    }

    /**
     * //获取上个月的第一天
     * @return
     */
    public static String getLastMonthFirstDay(){
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd"); //格式化时间

        Calendar cal_1=Calendar.getInstance();//获取当前日期
        cal_1.add(Calendar.MONTH, -1);
        cal_1.set(Calendar.DAY_OF_MONTH,1);//设置为1号
        cal_1.set(Calendar.HOUR_OF_DAY,0);
        cal_1.set(Calendar.MINUTE,0);
        cal_1.set(Calendar.SECOND,0);
        String firstDay = format.format(cal_1.getTime());
        return firstDay;
    }

    /**
     * 获取上月最后一天
     * @return
     */
    public static String getLastMonthLastDay(){
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd"); //格式化时间
        Calendar cal_2 = Calendar.getInstance();
        cal_2.set(Calendar.DAY_OF_MONTH,0);//设置为1号,当前日期既为本月第一天
        cal_2.set(Calendar.HOUR_OF_DAY,23);
        cal_2.set(Calendar.MINUTE,59);
        cal_2.set(Calendar.SECOND,59);
        String lastDay = format.format(cal_2.getTime());
        return lastDay;
    }
}

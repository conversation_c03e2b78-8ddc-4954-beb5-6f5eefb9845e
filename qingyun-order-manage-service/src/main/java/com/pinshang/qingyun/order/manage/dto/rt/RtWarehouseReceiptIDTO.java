package com.pinshang.qingyun.order.manage.dto.rt;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @Date 2024/12/19 15:39
 */
@Data
public class RtWarehouseReceiptIDTO {
    /**
     * 分页组件
     */
    @ApiModelProperty(hidden = true)
    private Integer pageNo = 1;

    @ApiModelProperty(hidden = true)
    private Integer pageSize = 10;

    @ApiModelProperty(value = "送货时间")
    private String deliveryTime;

    @ApiModelProperty(value = "1、大润发 2、欧尚 3、乐购")
    private Integer storeShortName;

    public void initExportPage() {
        this.pageNo = 1;
        this.pageSize = 2147483647;
    }
}

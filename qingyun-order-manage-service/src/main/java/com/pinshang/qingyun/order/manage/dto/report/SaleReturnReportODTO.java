package com.pinshang.qingyun.order.manage.dto.report;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.pinshang.qingyun.base.configure.codec.DecimalSerializerKeep2;
import com.pinshang.qingyun.renderer.annotation.FieldRender;
import com.pinshang.qingyun.renderer.constant.RenderFieldHelper;
import com.pinshang.qingyun.renderer.enums.FieldTypeEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2024/09/05
 * @Version 1.0
 */
@Data
public class SaleReturnReportODTO {
    @ExcelIgnore
    private Long id; //退货明细表自增ID
    @ExcelIgnore
    private Long enterpriseId;
    @ExcelProperty("客户编码")
    private String storeCode;
    @ExcelProperty("门店名称")
    private String shopName;
    @ExcelIgnore
    private String storeName;
    @ExcelProperty("退货单号")
    private String orderCode;
    @ExcelIgnore
    @JsonFormat(pattern = "yyyy-MM-dd",timezone = "GMT+8")
    private Date updateTime;
    @ExcelProperty("退货确认日期")
    private String updateTimeStr;
    @ExcelProperty("责任方")
    private String rpTypeStr;
    @ExcelProperty("退货确认数量")
    private String realReturnQuantity;
    @ExcelProperty("单价")
    private BigDecimal price;
    @ExcelProperty("实退金额")
    @JsonSerialize(using = DecimalSerializerKeep2.class)
    private BigDecimal realTotalPrice;
    @ExcelProperty("商品编码")
    private String commodityCode;
    @ExcelProperty("条码")
    private String barCode;
    @ExcelProperty("商品名称")
    private String commodityName;
    @ExcelProperty("规格")
    private String commoditySpec;
    @JsonFormat(pattern = "yyyy-MM-dd",timezone = "GMT+8")
    @ExcelIgnore
    private Date createTime;
    @ExcelProperty("申请退货日期")
    private String createTimeStr;
    @ExcelProperty("申请退货数量")
    private String returnQuantity;
    @ExcelProperty("退货原因")
    private String returnReasonName;
    @ExcelProperty("差异数量")
    private String diffQuantity;
    @ExcelIgnore
    private Long commodityId;
    @ExcelIgnore
    private BigDecimal totalPrice;
    @ExcelIgnore
    private String barCodes;	// 子码列表
    @ExcelIgnore
    private Integer rpType;
    @ExcelIgnore
    private Integer returnReason;

    @ExcelIgnore
    private Long stallId;
    @ExcelProperty("档口")
    @ApiModelProperty("档口名称")
    @FieldRender(fieldType = FieldTypeEnum.STALL, fieldName = RenderFieldHelper.Stall.stallName, keyName = "stallId")
    private String stallName;
}

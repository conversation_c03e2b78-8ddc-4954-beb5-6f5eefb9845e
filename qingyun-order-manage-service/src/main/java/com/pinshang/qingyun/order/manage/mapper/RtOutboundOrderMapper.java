package com.pinshang.qingyun.order.manage.mapper;

import com.pinshang.qingyun.order.manage.dto.rt.RtOutboundOrderIDTO;
import com.pinshang.qingyun.order.manage.dto.rt.RtOutboundOrderODTO;
import org.apache.ibatis.annotations.Mapper;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2024/12/19 9:27
 */
@Repository
@Mapper
public interface RtOutboundOrderMapper {

    List<RtOutboundOrderODTO> selectRtOutboundOrderList(RtOutboundOrderIDTO rtOutboundOrderIDTO);
}

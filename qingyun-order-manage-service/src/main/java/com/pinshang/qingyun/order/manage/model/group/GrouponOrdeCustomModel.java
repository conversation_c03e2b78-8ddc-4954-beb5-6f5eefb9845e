package com.pinshang.qingyun.order.manage.model.group;

import com.pinshang.qingyun.base.po.BaseSimplePO;

import javax.persistence.Entity;
import javax.persistence.Table;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 
 **/
@Entity
@Table(name = "t_groupon_order_custom")
public class GrouponOrdeCustomModel extends BaseSimplePO {

    private Long grouponId;
    /** 团购编码*/
    private String grouponCode;
    /** 团购开始时间*/
    private Date grouponStartTime;
    /**团购结束时间 */
    private Date grouponEndTime;
    /**团购到货日期 对应B端订单order_time */
    private Date arrivalTime;

    /** 门店id*/
    private Long shopId;
    /** 商品id*/
    private Long commodityId;
    /** 团购数量*/
    private BigDecimal quantity;
    /** 团购价格 */
    private BigDecimal price;



    public Long getGrouponId() {
        return grouponId;
    }

    public void setGrouponId(Long grouponId) {
        this.grouponId = grouponId;
    }

    public String getGrouponCode() {
        return grouponCode;
    }

    public void setGrouponCode(String grouponCode) {
        this.grouponCode = grouponCode;
    }

    public Date getGrouponStartTime() {
        return grouponStartTime;
    }

    public void setGrouponStartTime(Date grouponStartTime) {
        this.grouponStartTime = grouponStartTime;
    }

    public Date getGrouponEndTime() {
        return grouponEndTime;
    }

    public void setGrouponEndTime(Date grouponEndTime) {
        this.grouponEndTime = grouponEndTime;
    }

    public Date getArrivalTime() {
        return arrivalTime;
    }

    public void setArrivalTime(Date arrivalTime) {
        this.arrivalTime = arrivalTime;
    }

    public Long getShopId() {
        return shopId;
    }

    public void setShopId(Long shopId) {
        this.shopId = shopId;
    }

    public Long getCommodityId() {
        return commodityId;
    }

    public void setCommodityId(Long commodityId) {
        this.commodityId = commodityId;
    }

    public BigDecimal getQuantity() {
        return quantity;
    }

    public void setQuantity(BigDecimal quantity) {
        this.quantity = quantity;
    }

    public BigDecimal getPrice() {
        return price;
    }

    public void setPrice(BigDecimal price) {
        this.price = price;
    }
}
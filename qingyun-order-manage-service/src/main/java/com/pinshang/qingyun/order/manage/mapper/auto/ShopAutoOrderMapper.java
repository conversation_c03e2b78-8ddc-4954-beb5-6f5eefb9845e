package com.pinshang.qingyun.order.manage.mapper.auto;


import com.pinshang.qingyun.order.manage.dto.auto.AutoShopCommodityODTO;
import com.pinshang.qingyun.order.manage.dto.auto.ShopOrderedQuantityODTO;
import com.pinshang.qingyun.order.manage.model.auto.AutoOrderLog;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface ShopAutoOrderMapper {

    List<AutoShopCommodityODTO> queryAutoShopList();

    List<AutoShopCommodityODTO> queryAutoShopCommodityList(@Param("shopId") Long shopId);

    List<Long> queryAutoOrderLogCommodityIds(@Param("beginDate") String beginDate, @Param("endDate") String endDate, @Param("storeId") Long storeId);

    int batchInsertAutoOrderLog(@Param("autoOrderLogList") List<AutoOrderLog> autoOrderLogList);

    List<ShopOrderedQuantityODTO> selectShopOrderQuantity(@Param("shopId") Long shopId, @Param("commodityIdList") List<Long> commodityIdList, @Param("orderTime") String orderTime);

}

package com.pinshang.qingyun.order.manage.enums;

/**
 * Created by <PERSON><PERSON><PERSON> on 2017/4/19.
 */
public enum ExcelSheetTitleEnum {

    SHOP_SHORT_DELIVERY_HQ("shop_short_delivery", "短交报表", new String[]{"门店类型", "门店名称", "送货日期", "订单号", "大类", "中类", "小类", "条形码", "商品编码", "商品名称", "规格", "计量单位", "订货价", "订货数量", "发货数量", "差异数量", "短交比例", "工厂", "生产组", "客户编号", "下单人", "线路组", "发货仓库", "经营模式"}),
    SHOP_SHORT_DELIVERY("shop_short_delivery", "短交报表", new String[]{"门店类型", "门店名称", "送货日期", "订单号", "大类", "中类", "小类", "条形码", "商品编码", "商品名称", "规格", "计量单位", "订货价", "订货数量", "发货数量", "差异数量", "短交比例", "工厂", "生产组", "客户编号", "下单人"}),
    ACTUAL_RECEIPT_ANALYSIS_REPORT("actual_receipt_analysis_report", "门店实收商品分析表", new String[]{"门店名称", "一级分类", "条形码", "商品编码", "商品名称", "规格", "订货数量", "实发数量", "实收数量", "实收差异数量", "供货价", "供货金额", "供应商", "采购员"}),
    SHOP_REAL_DELIVERY("shop_real_delivery", "商品实发汇总报表", new String[]{"商品编码", "商品名称", "规格", "条码", "一级品类", "计量单位", "订货数量", "实发数量", "实发金额", "差异数量", "工厂", "生产组", "车间", "税率"}),
    SHOP_REAL_DELIVERY_STORE_TYPE("shop_real_delivery_store_type", "商品实发汇总报表(客户类型)", new String[]{"客户类型", "商品编码", "商品名称", "规格", "条码", "一级品类", "计量单位", "订货数", "实发数量", "实发金额", "默认仓库", "工厂", "生产组", "车间"}),

    COMMODITY_GROUP_ORDER("commodity_group_order", "商品订货分析表", new String[]{"团购活动编号", "团购活动时间", "门店编码", "门店名称", "商品编码", "商品名称", "规格", "条形码", "单位", "团购数量", "团购销售金额", "订单数量", "向总部订货金额"}),
    SHOP_GROUP_ORDER("shop_group_order", "门店订货分析表", new String[]{"门店编码", "门店名称", "团购数量", "团购销售金额", "订单数量", "向总部订货金额"}),
    AUTO_COMMODITY_REPORT("auto_commodity_report", "总部规划自动订货商品", new String[]{"序号", "商品编码", "条形码", "商品名称", "规格", "单位", "大类", "中类", "小类", "是否冻品"}),
    SHOP_PRE_ORDER("shop_pre_order","门店加货申请单",new String[]{"序号","门店名称","物流模式","加货申请单号","申请金额","申请人","申请时间","处理人","处理时间","状态","订单编号","送货日期"}),

    RETURN_ORDER_DETAILS_REPORT_LIST("order_details_report_list","退单明细报表",new String[] {"门店","退货日期","退货编号","商品分类","条形码","商品编码","商品名称","规格","单位","单价","退货数量","退货金额","退货原因","备注","档口"}),

    ;
    private String code;
    private String name;
    private String[] titles;
    /**
     * 属性名
     */
    private String[] props;

    private ExcelSheetTitleEnum(String code, String name, String[] titles) {
        this.code = code;
        this.name = name;
        this.titles = titles;
    }

    ExcelSheetTitleEnum(String code, String name, String[] titles, String[] props) {
        this.code = code;
        this.name = name;
        this.titles = titles;
        this.props = props;
    }


    /**
     * 动态设置title
     *
     * @param code
     * @param titles
     */
    public static void setTitles(String code, String[] titles) {
        for (ExcelSheetTitleEnum est : ExcelSheetTitleEnum.values()) {
            if (code.equals(est.getCode())) {
                est.setTitles(titles);
                break;
            }
        }
    }


    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String[] getTitles() {
        return titles;
    }

    public void setTitles(String[] titles) {
        this.titles = titles;
    }

    public String[] getProps() {
        return props;
    }

    public void setProps(String[] props) {
        this.props = props;
    }
}

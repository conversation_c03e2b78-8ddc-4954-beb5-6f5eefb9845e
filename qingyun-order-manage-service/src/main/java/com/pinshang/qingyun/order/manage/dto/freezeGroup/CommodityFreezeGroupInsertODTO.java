package com.pinshang.qingyun.order.manage.dto.freezeGroup;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @ClassName CommodityFreezeGroupInsertODTO
 * <AUTHOR>
 * @Date 2022/10/20 16:46
 * @Description CommodityFreezeGroupInsertODTO
 * @Version 1.0
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class CommodityFreezeGroupInsertODTO {
    private Integer code;

    private List<String> noExitCode;
}

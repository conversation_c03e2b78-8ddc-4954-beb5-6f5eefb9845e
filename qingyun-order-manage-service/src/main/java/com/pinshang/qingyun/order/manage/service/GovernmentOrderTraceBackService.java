package com.pinshang.qingyun.order.manage.service;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.pinshang.qingyun.base.configure.expand.QYAssert;
import com.pinshang.qingyun.box.utils.SpringUtil;
import com.pinshang.qingyun.order.manage.dto.government.*;
import com.pinshang.qingyun.order.manage.mapper.GovernmentOrderTraceBackMapper;
import com.pinshang.qingyun.smm.dto.user.SelectUserFactoryIdListIDTO;
import com.pinshang.qingyun.smm.service.SMMUserClient;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.xssf.usermodel.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.*;
import java.net.URL;
import java.text.Collator;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2021/2/4 14:38
 */
@Slf4j
@Service
public class GovernmentOrderTraceBackService{

    @Autowired
    private GovernmentOrderTraceBackMapper orderTraceBackMapper;

    @Autowired
    private SMMUserClient smmUserClient;

    /***
     * 查询政府追溯订单列表数据
     * @param vo
     * @return
     */
    public PageInfo<GovernmentOrderTraceBackRespODTO> selectGovernmentOrderTraceBackList(GovernmentOrderTraceBackRepIDTO vo){

        if(this.checkUserFactoryPermission(vo.getUserId(),vo.getFactoryId())){
            QYAssert.isFalse("该用户:"+vo.getUserId()+"没有查看此数据权限，请确认");
        }
        PageInfo<GovernmentOrderTraceBackRespODTO> pageInfo= PageHelper.startPage(vo.getPageNo(),vo.getPageSize()).doSelectPageInfo(()-> {
            orderTraceBackMapper.selectGovernmentOrderTraceBackList(vo);
        });
        List<GovernmentOrderTraceBackRespODTO> list=pageInfo.getList();
        if(SpringUtil.isEmpty(list)){
            return pageInfo;
        }
        /***
         * 封装客户信息
         */
        this.processStoreData(list);
        /***
         * storeCode排序 ASC
         */
        pageInfo.setList(list.stream().sorted(Comparator.comparing(GovernmentOrderTraceBackRespODTO::getStoreCode)).collect(Collectors.toList()));
        return pageInfo;
    }

    /***
     * 封装客户信息
     * @param list
     */
    private void processStoreData( List<GovernmentOrderTraceBackRespODTO> list){
        if(SpringUtil.isNotEmpty(list)){
            List<Long> storeIds=list.stream().map(GovernmentOrderTraceBackRespODTO::getStoreId).collect(Collectors.toList());
            /***
             * 获取客户信息
             */
            List<GovernmentStoreRespODTO> storeIdList= orderTraceBackMapper.selectGovernmentStoreByStoreId(storeIds);
            if(storeIdList!=null && storeIdList.size()>0){
                Map<Long, GovernmentStoreRespODTO> map= storeIdList.stream().collect(Collectors.toMap(key -> key.getStoreId(), val-> val));
                list.forEach(g ->{
                    Long storeId=g.getStoreId();
                    if(map.containsKey(storeId)){
                        GovernmentStoreRespODTO entry= map.get(storeId);
                        g.setSku(entry.getSku());
                        g.setStoreCode(entry.getStoreCode());
                        //购货者名称
                        if(StringUtils.isBlank(entry.getOuterStoreName())){
                            g.setOuterStoreName(entry.getStoreName());
                        }else{
                            g.setOuterStoreName(entry.getOuterStoreName());
                        }

                        g.setDeliveryAddress(entry.getDeliveryAddress());
                        g.setLinkmanTel(entry.getLinkmanTel());
                       // String num=g.getCommodityCount();
                        /*if(StringUtils.isNotBlank(num)){
                           String[] numSplit= num.split(",");
                           BigDecimal sum=BigDecimal.ZERO;
                           BigDecimal numBig=null;
                           for(String s:numSplit){
                               if(StringUtils.isNotBlank(s)) {
                                   numBig = new BigDecimal(s);
                                   sum = sum.add(numBig);
                               }
                           }
                            g.setCommodityCount(sum.setScale(2,BigDecimal.ROUND_HALF_UP).toString());
                        }*/


                        String deliveryDate=g.getDeliveryDate();
                        if(StringUtils.isNotBlank(deliveryDate)){
                            /***
                             * 生产日期等于送货日子减一天
                             */
                            LocalDate localDate=LocalDate.parse(deliveryDate);
                            LocalDate localDateMinusDay=localDate.minusDays(1);
                            g.setManufactureDate(localDateMinusDay.toString());//生产日期
                            g.setManufactureBatch(localDateMinusDay.format(DateTimeFormatter.ofPattern("yyyyMMdd")));//生产批次
                        }
                    }
                });
            }
        }
    }


    /***
     * 获取用户工厂权限数据
     * @param userId
     * @return
     */
    public List<FactoryRespODTO> selectFactoryByUserId(Long userId){
        QYAssert.isTrue(userId!=null,"用户信息不能为空");
        List<Long> factoryIds= smmUserClient.selectUserFactoryIdList(SelectUserFactoryIdListIDTO.onlyDb(userId));
        if(SpringUtil.isEmpty(factoryIds)){
            return null;
        }

        List<FactoryRespODTO> factoryRespEntries= orderTraceBackMapper.selectFactoryById(factoryIds);
        if(SpringUtil.isEmpty(factoryRespEntries)){
            return null;
        }
        return factoryRespEntries.stream().sorted(Comparator.comparing(FactoryRespODTO::getFactoryName, Collator.getInstance(Locale.CHINA))).collect(Collectors.toList());
    }

    /****
     * 验证是否具有工厂权限
     * @param userId
     */
    public boolean checkUserFactoryPermission(Long userId,Long factoryId){
        List<FactoryRespODTO> factoryRespEntries=this.selectFactoryByUserId(userId);
        if(SpringUtil.isEmpty(factoryRespEntries)){
            return true;
        }
        Map<Long, FactoryRespODTO> factoryIds=factoryRespEntries.stream().collect(Collectors.toMap(k -> Long.valueOf(k.getId()), v -> v));
        if(!factoryIds.containsKey(factoryId)){
            return true;
        }

        return false;
    }


    /***
     * 获取导出政府追溯订单列表数据
     * @param vo
     * @return
     */
    public Map<String,Object> selectGovernmentOrderTraceBackExportList(GovernmentOrderTraceBackRepIDTO vo){
        Map<String,Object> map=new HashMap<>();
        if(this.checkUserFactoryPermission(vo.getUserId(),vo.getFactoryId())){
            log.error("该用户:{}没有查看此数据权限，请确认",vo.getUserId());
            return map;
        }
        List<GovernmentOrderTraceBackRespODTO> governmentOrderList= orderTraceBackMapper.selectGovernmentOrderTraceBackList(vo);
        /*** 封装客户信息 */
        this.processStoreData(governmentOrderList);
        /*** storeCode排序 ASC */
        map.put("order",governmentOrderList.stream().sorted(Comparator.comparing(GovernmentOrderTraceBackRespODTO::getStoreCode)).collect(Collectors.toList()));
        map.put("commodity",orderTraceBackMapper.selectGovernmentCommodityList(vo.getFactoryId()));
        return map;
    }

    /***
     * 给商品报表列表赋值
     * @param index
     * @param sxssfCell
     * @param entry
     */
    private void setCommoditySxssfCell(int index , XSSFCell sxssfCell, GovernmentCommdityRespODTO entry){
        switch (index){
            case 0:
                sxssfCell.setCellValue(entry.getProductName());
                break;
            case 1:
                sxssfCell.setCellValue(entry.getProductCode());
                break;
            case 2:
                sxssfCell.setCellValue(entry.getProductClassification());
                break;
            case 3:
                sxssfCell.setCellValue(entry.getProductQgp());
                break;
            case 4:
                sxssfCell.setCellValue(entry.getProductSpecifications());
                break;
            case 5:
                sxssfCell.setCellValue(entry.getProductBarcode());
                break;
            case 6:
                sxssfCell.setCellValue(entry.getProductManufacturer());
                break;
            case 7:
                break;
            case 8:
                sxssfCell.setCellValue(entry.getProductescribe());
                break;
            default:
                break;
        }

    }

    /***
     * 给订单报表列表赋值
     * @param index
     * @param sxssfCell
     * @param entry
     */
    private void setOrderSxssfCell(int index , XSSFCell sxssfCell, GovernmentOrderTraceBackRespODTO entry){
        switch (index){
            case 0:
                sxssfCell.setCellValue(entry.getCommodityName());
                break;
            case 1:
                sxssfCell.setCellValue(entry.getCommodityCode());
                break;
            case 2:
                sxssfCell.setCellValue(entry.getManufactureDate());
                break;
            case 3:
                sxssfCell.setCellValue(entry.getManufactureBatch());
                break;
            case 4:
                sxssfCell.setCellValue(entry.getInspectionCertificate());
                break;
            case 5:
                sxssfCell.setCellValue(entry.getCommodityCount());
                break;
            case 6:
                sxssfCell.setCellValue(entry.getCommodityUnit());
                break;
            case 7:
                sxssfCell.setCellValue(entry.getDeliveryDate());
                break;
            case 8:
                sxssfCell.setCellValue(entry.getOuterStoreName());
                break;
            case 9:
                sxssfCell.setCellValue(entry.getDeliveryAddress());
                break;
            case 10:
                sxssfCell.setCellValue(entry.getLinkmanTel());
                break;
            case 11:
                sxssfCell.setCellValue("上海市浦东新区");
                break;
            default:
                break;
        }

    }


    /***
     * 获取远程服务的政府追溯模板文件
     * 创建本地excel文件
     */
    public File createLocalExcelFile(String governmentTemplateUrl,String localParentUrl,String productManufacturer)throws IOException{
        InputStream in=null;
        FileOutputStream out=null;
        try {
            URL url=new URL(governmentTemplateUrl);
            in=url.openStream();
            File dir=new File(localParentUrl);
            if(!dir.exists()){
                dir.mkdirs();
            }
            File file=new File(localParentUrl,String.format("%s_生产_食品_%s.xlsx",productManufacturer,LocalDate.now().format(DateTimeFormatter.ofPattern("yyyyMMdd"))));
            out=new FileOutputStream(file);
            XSSFWorkbook xssfWorkbook=new XSSFWorkbook(in);
            XSSFSheet sheet=xssfWorkbook.getSheetAt(1);
            int num=sheet.getLastRowNum();
            if(num>0) {
                for (int i = 1; i <= num; i++) {
                     XSSFRow row=sheet.getRow(i);
                     sheet.removeRow(row);
                }
            }
            xssfWorkbook.write(out);
            return file;
        } catch (IOException e) {
            log.error("下载远程政府追溯模板文件生成本地模板文件失败 \n error:{}",e);
            throw e;
        }finally {
            if(in!=null){
                in.close();
            }
            if(out!=null){
                out.flush();
                out.close();
            }
        }

    }

    /***
     * 处理excel
     * @param map
     * @throws Exception
     */
    public XSSFWorkbook buildExcelDocument(Map<String, Object> map,File newFile) throws Exception {
        XSSFWorkbook xssfWorkbook = new XSSFWorkbook(newFile);
        if(SpringUtil.isEmpty(map)){
            return xssfWorkbook;
        }
        XSSFSheet productDescription = xssfWorkbook.getSheetAt(1);
        XSSFSheet governmentOrder = xssfWorkbook.getSheetAt(7);
        List<GovernmentOrderTraceBackRespODTO> governmentOrderItems = (List<GovernmentOrderTraceBackRespODTO>) map.get("order");
        /***
         * 处理订单销货数据
         */
        if (SpringUtil.isNotEmpty(governmentOrderItems)) {
            for (int i = 1; i <= governmentOrderItems.size(); i++) {
                XSSFRow rowOrder = governmentOrder.getRow(i);
                if (rowOrder == null) {
                    rowOrder = governmentOrder.createRow(i);
                }
                GovernmentOrderTraceBackRespODTO entry = governmentOrderItems.get(i - 1);
                for (int j = 0; j < 12; j++) {
                    XSSFCell xssfCell = rowOrder.createCell(j);
                    XSSFCellStyle cellStyle = xssfCell.getCellStyle();
                    cellStyle.setLocked(false);
                    this.setOrderSxssfCell(j, xssfCell, entry);
                }
            }

        }

        /***
         * 处理产品数据
         */
        List<GovernmentCommdityRespODTO> governmentCommodityItems = (List<GovernmentCommdityRespODTO>) map.get("commodity");
        if (SpringUtil.isNotEmpty(governmentCommodityItems)) {
            for (int i = 1; i <= governmentCommodityItems.size(); i++) {
                XSSFRow rowOrder = productDescription.getRow(i);
                if (rowOrder == null) {
                    rowOrder = productDescription.createRow(i);
                }
                GovernmentCommdityRespODTO entry = governmentCommodityItems.get(i - 1);
                for (int j = 0; j < 9; j++) {
                    XSSFCell xssfCell = rowOrder.createCell(j);
                    this.setCommoditySxssfCell(j, xssfCell, entry);
                }
            }

        }
        return xssfWorkbook;
    }
}

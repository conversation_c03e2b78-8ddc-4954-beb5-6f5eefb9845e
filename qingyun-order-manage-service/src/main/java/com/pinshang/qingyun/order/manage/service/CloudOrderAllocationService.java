package com.pinshang.qingyun.order.manage.service;

import com.github.pagehelper.PageInfo;
import com.pinshang.qingyun.base.configure.expand.QYAssert;
import com.pinshang.qingyun.base.enums.YesOrNoEnums;
import com.pinshang.qingyun.box.utils.DateUtil;
import com.pinshang.qingyun.order.dto.CloudOrderAllocationResultOrderODTO;
import com.pinshang.qingyun.order.dto.CloudOrderAllocationSaveIDTO;
import com.pinshang.qingyun.order.manage.dto.*;
import com.pinshang.qingyun.order.manage.mapper.CloudOrderAllocationLogMapper;
import com.pinshang.qingyun.order.manage.mapper.CloudOrderAllocationParamMapper;
import com.pinshang.qingyun.order.manage.mapper.CommodityMapper;
import com.pinshang.qingyun.order.manage.mapper.ShopMapper;
import com.pinshang.qingyun.order.manage.model.CloudOrderAllocationLog;
import com.pinshang.qingyun.order.manage.model.CloudOrderAllocationParam;
import com.pinshang.qingyun.order.service.OrderClient;
import com.pinshang.qingyun.price.dto.commodity.CommodityListRequestIDTO;
import com.pinshang.qingyun.price.dto.commodity.CommodityResultODTO;
import com.pinshang.qingyun.price.service.ProductPriceModelClient;
import com.pinshang.qingyun.report.dto.ThirdSummaryIDTO;
import com.pinshang.qingyun.report.dto.ThirdSummaryODTO;
import com.pinshang.qingyun.report.service.ReportClient;
import com.pinshang.qingyun.storage.dto.WarehouseODto;
import com.pinshang.qingyun.storage.dto.deliveryCargo.DeliveryCargoTaskSuccessCommodityReqDTO;
import com.pinshang.qingyun.storage.dto.deliveryCargo.DeliveryCargoTaskSuccessOrderReqDTO;
import com.pinshang.qingyun.storage.dto.deliveryCargo.DeliveryCargoTaskSuccessReqDTO;
import com.pinshang.qingyun.storage.service.WarehouseClient;
import com.pinshang.qingyun.storage.service.deliveryCargo.DeliveryCargoTaskClient;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.entity.Example;

import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @Author: sk
 * @Date: 2022/8/31
 */
@Slf4j
@Service
public class CloudOrderAllocationService {

    @Autowired
    private ProductPriceModelClient productPriceModelClient;
    @Autowired
    private CommodityMapper commodityMapper;
    @Autowired
    private ShopMapper shopMapper;
    @Autowired
    private CloudOrderAllocationLogMapper cloudOrderAllocationLogMapper;
    @Autowired
    private ReportClient reportClient;
    @Autowired
    private WarehouseClient warehouseClient;
    @Autowired
    private OrderClient orderClient;
    @Autowired
    private DeliveryCargoTaskClient deliveryCargoTaskClient;
    @Autowired
    private CloudOrderAllocationParamMapper cloudOrderAllocationParamMapper;
    @Autowired
    private CloudOrderAllocationSaveParamService cloudOrderAllocationSaveParamService;

    /**
     * 云超配货单分配客户
     * @param idto
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public Boolean allocationStores(CloudOrderAllocationIDTO idto) {
        // 判断重复
        Example ex = new Example(CloudOrderAllocationParam.class);
        ex.createCriteria().andEqualTo("taskId", idto.getTaskId()).andEqualTo("orderStatus", YesOrNoEnums.YES.getCode());
        List<CloudOrderAllocationParam> paramList = cloudOrderAllocationParamMapper.selectByExample(ex);
        if(CollectionUtils.isNotEmpty(paramList)){
            log.warn("云超配货单分配客户,此任务已下单成功。taskId {}", idto.getTaskId());
            return Boolean.FALSE;
        }

        // 异步记录入参
        cloudOrderAllocationSaveParamService.saveParam(idto);

        // 根据线路组或者storeIdList 查询门店信息
        List<ShopODTO> shopList = shopMapper.queryShopByStoreIdList(idto.getLineGroupIdList(), idto.getStoreIdList());
        QYAssert.isTrue(CollectionUtils.isNotEmpty(shopList), "无播种区域下的门店客户");
        Map<Long, ShopODTO> shopMap = shopList.stream().collect(Collectors.toMap(ShopODTO::getStoreId, Function.identity()));

        List<String> storeIdStrList = shopList.stream().map(item -> item.getStoreId() + "").collect(Collectors.toList());
        List<Long> storeIdList = shopList.stream().map(item -> item.getStoreId()).collect(Collectors.toList());
        // 查出来符合条件的storeIdList重新赋值
        idto.setStoreIdList(storeIdList);

        List<Long> commodityIdList = idto.getItemList().stream().map(item -> item.getCommodityId()).collect(Collectors.toList());
        List<CommodityODTO> commodityList = commodityMapper.queryCommodityByIdList(commodityIdList);
        Map<Long, CommodityODTO> commodityMap = commodityList.stream().collect(Collectors.toMap(CommodityODTO::getCommodityId, Function.identity()));


        //商品所在的配货单在选择的客户的产品价格方案中不存在，则整个配货单分配不成功，并报错：**商品在**门店无产品价格方案
        CommodityListRequestIDTO priceIto = new CommodityListRequestIDTO();
        priceIto.setStoreIdList(storeIdStrList);
        priceIto.setCommodityIdListAll(commodityIdList);
        priceIto.setPageNo(1);
        priceIto.setPageSize(Integer.MAX_VALUE);
        PageInfo<CommodityResultODTO> resultList = productPriceModelClient.findStoreCommodityListByPage(priceIto);
        List<CommodityResultODTO> pageList = resultList.getList();
        QYAssert.isTrue(CollectionUtils.isNotEmpty(pageList), "产品价格方案不存在");

        Map<String, List<CommodityResultODTO>> resultMap = resultList.getList().stream().collect(Collectors.groupingBy(item -> {
            String groupByField = item.getStoreId() + "" + item.getCommodityId();
            return groupByField;
        }));

        // 校验产品价格方案，数据组装
        List<CloudOrderAllocationLogDTO> cloudOrderAllocationLogList = getCloudOrderAllocationLogDTOSList(idto, shopMap, commodityMap, resultMap);


        // 校验默认仓库
        Map<Long, WarehouseODto> wareMap = warehouseClient.queryWarehouseListByCommodityIds(commodityIdList);
        QYAssert.isTrue(wareMap != null,"所有商品默认仓库都为空");
        idto.getItemList().forEach(itemIDTO->{
            WarehouseODto warehouseODto = wareMap.get(itemIDTO.getCommodityId());
            QYAssert.isTrue(warehouseODto != null,
                    commodityMap.get(itemIDTO.getCommodityId()).getCommodityName() +  "商品无首选仓库");
        });

        List<Long> commodityFirstIdList = commodityList.stream().distinct().map(item -> item.getCommodityFirstId()).collect(Collectors.toList());
        List<Long> shopIdList = shopList.stream().map(item -> item.getShopId()).collect(Collectors.toList());
        // 商品配货数量Map
        Map<Long, BigDecimal> commodityAllocationNumberMap = idto.getItemList().stream().collect(Collectors.toMap(CloudOrderAllocationItemIDTO::getCommodityId,CloudOrderAllocationItemIDTO::getAllocationNumberBigDecimal,(key1 , key2)-> key2));

        // 计算分配份数、创建订单、记录分配log
        allocationStoresCalculate(cloudOrderAllocationLogList, commodityAllocationNumberMap,commodityFirstIdList, shopIdList, idto);

        return Boolean.TRUE;
    }

    /**
     * 校验产品价格方案，数据组装
     */
    private List<CloudOrderAllocationLogDTO> getCloudOrderAllocationLogDTOSList(CloudOrderAllocationIDTO idto, Map<Long, ShopODTO> shopMap, Map<Long, CommodityODTO> commodityMap, Map<String, List<CommodityResultODTO>> resultMap) {
        List<CloudOrderAllocationLogDTO> cloudOrderAllocationLogList = new ArrayList<>();
        // 校验产品价格方案
        idto.getStoreIdList().forEach(storeId->{
            idto.getItemList().forEach(itemIDTO->{
                List<CommodityResultODTO> list = resultMap.get(storeId + "" + itemIDTO.getCommodityId());
                String commodityName = commodityMap.get(itemIDTO.getCommodityId()).getCommodityName();
                String shopName = shopMap.get(storeId).getShopName();
                QYAssert.isTrue(CollectionUtils.isNotEmpty(list),commodityName+ "商品在" + shopName + "门店无产品价格方案");
                QYAssert.isTrue(list.get(0).getCommodityPrice().compareTo(BigDecimal.ZERO) != 0,commodityName + "商品在" + shopName + "门店产品价格方案价格为0");

                CloudOrderAllocationLogDTO cloudOrderAllocationLog = new CloudOrderAllocationLogDTO();
                cloudOrderAllocationLog.setTaskId(idto.getTaskId());
                cloudOrderAllocationLog.setStoreId(storeId);
                cloudOrderAllocationLog.setShopId(shopMap.get(storeId).getShopId());
                cloudOrderAllocationLog.setOrderTime(idto.getOrderTime());
                cloudOrderAllocationLog.setCommodityId(itemIDTO.getCommodityId());
                cloudOrderAllocationLog.setAllocationNumber(itemIDTO.getAllocationNumber());
                cloudOrderAllocationLog.setCommodityFirstId(commodityMap.get(itemIDTO.getCommodityId()).getCommodityFirstId());
                cloudOrderAllocationLog.setCommodityPackageSpec(commodityMap.get(itemIDTO.getCommodityId()).getCommodityPackageSpec());
                cloudOrderAllocationLog.setPrice(list.get(0).getCommodityPrice());
                cloudOrderAllocationLogList.add(cloudOrderAllocationLog);
            });
        });
        return cloudOrderAllocationLogList;
    }


    /**
     * 计算分配份数、创建订单、记录分配log
     * @return
     */
    public void allocationStoresCalculate(List<CloudOrderAllocationLogDTO> cloudOrderAllocationLogList,
                                    Map<Long, BigDecimal> commodityAllocationNumberMap,
                                    List<Long> commodityFirstIdList, List<Long> shopIdList,CloudOrderAllocationIDTO idto) {
        String beginTime = idto.getBeginTime();
        String endTime = idto.getEndTime();
        // 大类计算
        firstCateAllocation(beginTime, endTime, commodityFirstIdList, shopIdList, cloudOrderAllocationLogList);

        List<CloudOrderAllocationLog> orderList = new ArrayList<>();
        Map<Long, List<CloudOrderAllocationLogDTO>> cloudOrderAllocationLogMap = cloudOrderAllocationLogList.stream().collect(Collectors.groupingBy(CloudOrderAllocationLogDTO::getCommodityId));
        for (Map.Entry<Long, List<CloudOrderAllocationLogDTO>> entry : cloudOrderAllocationLogMap.entrySet()) {
            List<CloudOrderAllocationLogDTO> list = entry.getValue();
            BigDecimal percent = commodityAllocationNumberMap.get(entry.getKey());
            BigDecimal allocationNumber = commodityAllocationNumberMap.get(entry.getKey());

            Collections.sort(list); // 按金额倒序排
            // 分到的份数为0也继续分配，就是为了记录数据
            for(CloudOrderAllocationLogDTO dto : list){

                BigDecimal orderQuantity;
                // 分配的所有门店的大类总销售额为0
                if(dto.getTotalAmount().compareTo(BigDecimal.ZERO) == 0){
                    orderQuantity = BigDecimal.ZERO;
                }else {
                    orderQuantity = dto.getAmount().divide(dto.getTotalAmount(),4,BigDecimal.ROUND_HALF_UP)
                            .multiply(percent).setScale(0,BigDecimal.ROUND_UP);
                }

                CloudOrderAllocationLog order = new CloudOrderAllocationLog();
                BeanUtils.copyProperties(dto, order);
                if(allocationNumber.compareTo(BigDecimal.ZERO) > 0){
                    if(allocationNumber.compareTo(orderQuantity) >= 0){
                        order.setRealAllocationNumber(orderQuantity.longValue());
                    }else {
                        order.setRealAllocationNumber(allocationNumber.longValue());
                    }
                }else {
                    order.setRealAllocationNumber(0L);
                }
                order.setCreateId(idto.getCreateId());
                order.setCreateTime(new Date());
                orderList.add(order);

                allocationNumber = allocationNumber.subtract(new BigDecimal(order.getRealAllocationNumber() + ""));
            }
        }

        //记录 分配log
        cloudOrderAllocationLogMapper.insertList(orderList);

        // 定义返回list
        List<DeliveryCargoTaskSuccessCommodityReqDTO> successCommodityList = new ArrayList<>();

        // 调用createOrder方法(数量大于0的创建订单)
        List<CloudOrderAllocationSaveIDTO> saveOrderList = new ArrayList<>();
        orderList.forEach(item->{
            CloudOrderAllocationSaveIDTO saveIDTO = new CloudOrderAllocationSaveIDTO();
            BeanUtils.copyProperties(item, saveIDTO);
            saveIDTO.setOrderTime(DateUtil.getDateFormate(item.getOrderTime(), "yyyy-MM-dd"));
            // 商品数量 = 份数*包装规格
            saveIDTO.setQuantity(item.getCommodityPackageSpec().multiply(new BigDecimal(item.getRealAllocationNumber() + "")));
            saveIDTO.setUserId(idto.getCreateId());
            if(saveIDTO.getQuantity().compareTo(BigDecimal.ZERO) > 0){
                saveOrderList.add(saveIDTO);
            }

            DeliveryCargoTaskSuccessCommodityReqDTO successCommodityReqDTO = new DeliveryCargoTaskSuccessCommodityReqDTO();
            BeanUtils.copyProperties(item, successCommodityReqDTO);
            successCommodityReqDTO.setRealAllocationNumber(Integer.valueOf(item.getRealAllocationNumber() + ""));
            successCommodityList.add(successCommodityReqDTO);
        });
        List<CloudOrderAllocationResultOrderODTO> orderResultList = orderClient.cloudOrderAllocationCreateOrder(saveOrderList);

        // 异步更新入参任务已下单
        cloudOrderAllocationSaveParamService.updateParam(idto.getCreateId(), idto.getTaskId());

        // 成功回调方法
        createSuccessTask(cloudOrderAllocationLogList.get(0).getTaskId(), successCommodityList, orderResultList);
    }

    /**
     * 成功回调方法
     */
    private void createSuccessTask(Long taskId,List<DeliveryCargoTaskSuccessCommodityReqDTO> successCommodityList, List<CloudOrderAllocationResultOrderODTO> orderResultList) {
        List<DeliveryCargoTaskSuccessOrderReqDTO> successOrderList = new ArrayList<>();
        orderResultList.forEach(item->{
            DeliveryCargoTaskSuccessOrderReqDTO successOrderReqDTO = new DeliveryCargoTaskSuccessOrderReqDTO();
            BeanUtils.copyProperties(item, successOrderReqDTO);
            successOrderList.add(successOrderReqDTO);
        });

        // 成功调用返回接口
        DeliveryCargoTaskSuccessReqDTO successReqDTO = new DeliveryCargoTaskSuccessReqDTO();
        successReqDTO.setTaskId(taskId);
        successReqDTO.setOrderList(successOrderList);
        successReqDTO.setCommodityList(successCommodityList);
        deliveryCargoTaskClient.createSuccessTask(successReqDTO);
    }

    /**
     * 大类计算
     * @param commodityFirstIdList
     * @param shopIdList
     * @param cloudOrderAllocationLogList
     * @return
     */
    private void firstCateAllocation(String beginTime, String endTime, List<Long> commodityFirstIdList, List<Long> shopIdList, List<CloudOrderAllocationLogDTO> cloudOrderAllocationLogList) {
        // 云超配货>查询大类销售额
        ThirdSummaryIDTO thirdSummaryIDTO = new ThirdSummaryIDTO();
        thirdSummaryIDTO.setBeginTime(beginTime);
        thirdSummaryIDTO.setEndTime(endTime);
        thirdSummaryIDTO.setShopIdList(shopIdList);
        thirdSummaryIDTO.setCommodityFirstIdList(commodityFirstIdList);
        List<ThirdSummaryODTO> thirdSummaryList = reportClient.queryPosCateSummary(thirdSummaryIDTO);
        QYAssert.isTrue(CollectionUtils.isNotEmpty(thirdSummaryList),"大类销售数据不存在");

        // 大类信息map (单个门店单个大类下的金额)
        Map<String, List<ThirdSummaryODTO>> thirdSummaryMap = thirdSummaryList.stream().collect(Collectors.groupingBy(item -> {
            String groupByField = item.getShopId() + "" + item.getCommodityFirstId();
            return groupByField;
        }));

        //计算每个大类的范围内所有门店的合计金额map (单个大类下所有门店的金额合计)
        Map<Long, BigDecimal> thirdSummaryTotalAmountMap = new HashMap<>();
        Map<Long, List<ThirdSummaryODTO>> firstCateAmountMap = thirdSummaryList.stream().collect(Collectors.groupingBy(ThirdSummaryODTO::getCommodityFirstId));
        for (Map.Entry<Long, List<ThirdSummaryODTO>> entry : firstCateAmountMap.entrySet()) {
            Long key = entry.getKey();
            List<ThirdSummaryODTO> value = entry.getValue();
            double amount = value.stream().mapToDouble(item -> item.getTotalAmount().doubleValue()).sum();
            thirdSummaryTotalAmountMap.put(key,new BigDecimal(amount + ""));
        }

        // 设置单个门店分类下的金额和所有门店分类的总金额
        for(CloudOrderAllocationLogDTO infoDTO : cloudOrderAllocationLogList){
            List<ThirdSummaryODTO> thirdSummaryODTOS = thirdSummaryMap.get(infoDTO.getShopId() + "" + infoDTO.getCommodityFirstId());
            infoDTO.setAmount(CollectionUtils.isNotEmpty(thirdSummaryODTOS) ? thirdSummaryODTOS.get(0).getTotalAmount() : BigDecimal.ZERO);
            infoDTO.setTotalAmount(thirdSummaryTotalAmountMap.get(infoDTO.getCommodityFirstId()) != null ? thirdSummaryTotalAmountMap.get(infoDTO.getCommodityFirstId()) : BigDecimal.ZERO);
        }
    }

}

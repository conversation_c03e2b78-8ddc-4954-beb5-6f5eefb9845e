package com.pinshang.qingyun.order.manage.dto;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * @Author: sk
 * @Date: 2022/8/31
 */
@Data
public class CloudOrderAllocationLogDTO implements Comparable<CloudOrderAllocationLogDTO>{

    /** 任务id */
    private Long taskId;

    private Long shopId;
    private Long storeId;

    private Date orderTime;

    /** 商品id */
    private Long commodityId;
    /** 包装规格 */
    private BigDecimal commodityPackageSpec;
    /** 配货份数 */
    private Long allocationNumber;


    private Long commodityFirstId;
    private BigDecimal price;

    /** 当前门店商品或者大类金额 */
    private BigDecimal amount;
    /** 所有门店商品或者大类下的金额 */
    private BigDecimal totalAmount;

    /** 计算出的实际配货份数 */
    private Long realAllocationNumber;

    @Override
    public int compareTo(CloudOrderAllocationLogDTO o) {
        // 倒序排
        return (o.getAmount().subtract(this.amount)).intValue();
    }

}

package com.pinshang.qingyun.order.manage.model;

import com.pinshang.qingyun.base.po.BaseIDPO;
import lombok.Getter;

import javax.persistence.Entity;
import javax.persistence.Table;
import javax.persistence.Transient;
import java.math.BigDecimal;
import java.util.Date;

@Entity
@Table(name="t_commodity")
public class Commodity extends BaseIDPO {

    /** 企业ID */
    private Long enterpriseId;

    /** 产品代码 */
    private String commodityCode;

    /** 产品名称 */
    private String commodityName;

    /** 产品描述 */
    private String commodityDescribe;

    /** 助记码 */
    private String commodityAid;

    /** 型号规格 */
    private String commoditySpec;

    /** 品牌ID */
    private BigDecimal brandId;

    /** 生命周期ID */
    private BigDecimal commodityCycleId;

    /** 小类 */
    private Long commodityThirdId;

    /** 小类 */
    private String commodityThirdKind;

	/**
	 * 包装类型
	 **/
	private String commodityPackageKind;

    /** 类型ID */
    private BigDecimal commodityTypeId;

    /** 净含量 */
    private String commodityWeight;

    /** 计量单位ID */
    private BigDecimal commodityUnitId;

    /** 0-停用,1-启用 */
    private String commodityState;

    /** 包装类型ID */
    private BigDecimal commodityPackageId;

	@Getter
	@Transient
	private String commodityPackageIdStr;

    /** 工厂code */
    private String commodityFactory;

    /** 生产组code */
    private String newWorkshop;

    /** 零售价 */
    private BigDecimal retailPrice;

    /** 成本价 */
    private BigDecimal costPrice;

    /** 生产成本价 */
    private BigDecimal firstCost;

    /** 税率id */
    private Long taxRateId;

    /** 税率 */
    private BigDecimal taxRate;

    /** 其他价 */
    private BigDecimal otherPrice;

    /** 上市日期 */
    private Date launchDate;

    /** 下市日期 */
    private Date delistingDate;

    /** 暂停开始时间 */
    private Date pauseBeginDate;

    /** 暂停结束时间 */
    private Date pauseEndDate;

    /** 暂停备注 */
    private String pauseRemark;

    /** 按时间段订货 */
    private Short orderIsTimeRange;

    /** 起订量 */
    private BigDecimal minOrder;

    /** 限时订货 */
    private Short timeLimitOrder;

    /** 计件单价 */
    private BigDecimal pieceRate;

    /** 是否速冻产品 */
    private Short commodityIsQuickFreeze;

    /** 是否即食 */
    private Short commodityIsInstant;

    /** 颜色 */
    private String commodityColor;

    /** 形状 */
    private String commodityShape;

    /** 保质期天数 */
    private Integer qualityDays;

    /** 保质期单位ID */
    private BigDecimal qualityUnitId;

    /** 保质期贮存条件 */
    private String storageCondition;

    /** 备注 */
    private String commodityRemark;

    /**  */
    private String companyStandard;

    /** 创建时间 */
    private Date createTime;

    /** 更新时间 */
    private Date updateTime;

    /** 贮存类型 */
    private Long storageTypeId;

    /** 净含量单位id */
    private Long weightUnitId;

    /** 公司标准id */
    private Long comStandardId;

    /** 执行标准 */
    private Long excuteStandardId;

    /** 等级id */
    private Long gradeId;

    /** 安全类别 */
    private Long safeTypeId;

    /** 下浮类型id */
    private Long downTypeId;

    /** 分类代码名称 */
    private String categoryCodeName;

    /** 是否定牌 */
    private Integer isBrandLocked;

    /** 是否称重 */
    private Integer isWeight;

    /** 是否框汇总 */
    private Integer isSummary;

    /** 是否B2B */
    private Integer isBtb;

    /** 是否O2O */
    private Integer isOto;

    /** 条形码 */
    private String barCode;

    /** 产地 */
    private String origin;

    /** 分类代码名称id */
    private Long categoryCodeId;

    /** 图片路径，下同 */
    private String picUrl1;

    /**  */
    private String picUrl2;

    /**  */
    private String picUrl3;

    /** 商品包装长度 */
    private Float length;

    /** 商品包装宽度 */
    private Float width;

    /** 商品包装高度 */
    private Float height;

    /** 商品毛重 */
    private Float weight;

    /*物流模式 0-直送, 1-配送, 2-直通*/
    private Integer logisticsModel;

	private BigDecimal commodityPackageSpec;

	@Transient
	private String idStr;

	@Transient
	private String orderTime;
	@Transient
	private String storeCode;
	@Transient
	private BigDecimal productNumber;

	private Integer productType;
	@Transient
	private String commodityIds;
	@Transient
	private String storeName;
	@Transient
	private String storeId;
	@Transient
	private BigDecimal amount;
	@Transient
	private BigDecimal commodityPrice;
	@Transient
	private String isRoundedGoods;
	@Transient
	private BigDecimal originalCommodityPrice;
	/**限制购买的数量*/
	@Transient
	private Integer limitNumber;
	/**
	 * 剩余可用的总限量
	 */
	@Transient
	private BigDecimal availableTotalLimit;
	/**
	 * 剩余可用的客户限购
	 */
	@Transient
	private BigDecimal availableStoreLimit;
	@Transient
	private String commodityUnitName;

	/**
	 * 特价标志:1-特价
	 * @return
	 */
	@Transient
	private Integer promotionFlag = 0;

	// 销售箱规（正数）：用于清美向门店销售
	private Double salesBoxCapacity = 1d;

	/**
	 * 特价标签
	 */
	@Transient
	private String tagName;

	/**
	 * 特价数量（修改订单展示用）
	 */
	@Transient
	private BigDecimal promotionCount;

	/**
	 * 原价数量(修改订单展示用)
	 */
	@Transient
	private BigDecimal normalCount;

	@Transient
	private BigDecimal productPrice;

	/**
	 * 单行价格（修改订单展示用）
	 */
	@Transient
	private BigDecimal commodityLinePrice;
	@Transient
	private Integer combType;

	@Getter
	@Transient
	private String productRemark;

	public String getIdStr() {
		return this.getId()==null?null:this.getId().toString();
	}

	public void setIdStr(String idStr) {
		this.idStr = idStr;
	}

	public Integer getCombType() {
		return combType;
	}

	public void setCombType(Integer combType) {
		this.combType = combType;
	}

	public BigDecimal getCommodityLinePrice() {
		return commodityLinePrice;
	}

	public void setCommodityLinePrice(BigDecimal commodityLinePrice) {
		this.commodityLinePrice = commodityLinePrice;
	}

	public BigDecimal getProductPrice() {
		return productPrice;
	}

	public void setProductPrice(BigDecimal productPrice) {
		this.productPrice = productPrice;
	}

	public String getCommodityUnitName() {
		return commodityUnitName;
	}

	public void setCommodityUnitName(String commodityUnitName) {
		this.commodityUnitName = commodityUnitName;
	}

	public String getCommodityPackageKind() {
		return commodityPackageKind;
	}

	public void setCommodityPackageKind(String commodityPackageKind) {
		this.commodityPackageKind = commodityPackageKind;
	}

	public BigDecimal getPromotionCount() {
		return promotionCount;
	}

	public void setPromotionCount(BigDecimal promotionCount) {
		this.promotionCount = promotionCount;
	}

	public BigDecimal getNormalCount() {
		return normalCount;
	}

	public void setNormalCount(BigDecimal normalCount) {
		this.normalCount = normalCount;
	}

	public Double getSalesBoxCapacity() {
		return salesBoxCapacity;
	}

	public void setSalesBoxCapacity(Double salesBoxCapacity) {
		this.salesBoxCapacity = salesBoxCapacity;
	}

	public String getTagName() {
		return tagName;
	}

	public void setTagName(String tagName) {
		this.tagName = tagName;
	}

	public Integer getLimitNumber() {
		return limitNumber;
	}

	public void setLimitNumber(Integer limitNumber) {
		this.limitNumber = limitNumber;
	}

	public BigDecimal getAvailableTotalLimit() {
		return availableTotalLimit;
	}

	public void setAvailableTotalLimit(BigDecimal availableTotalLimit) {
		this.availableTotalLimit = availableTotalLimit;
	}

	public BigDecimal getAvailableStoreLimit() {
		return availableStoreLimit;
	}

	public void setAvailableStoreLimit(BigDecimal availableStoreLimit) {
		this.availableStoreLimit = availableStoreLimit;
	}

	public Integer getPromotionFlag() {
		return promotionFlag;
	}

	public void setPromotionFlag(Integer promotionFlag) {
		this.promotionFlag = promotionFlag;
	}

	public BigDecimal getOriginalCommodityPrice() {
		return originalCommodityPrice;
	}

	public void setOriginalCommodityPrice(BigDecimal originalCommodityPrice) {
		this.originalCommodityPrice = originalCommodityPrice;
	}

	public String getIsRoundedGoods() {
		return isRoundedGoods;
	}

	public void setIsRoundedGoods(String isRoundedGoods) {
		this.isRoundedGoods = isRoundedGoods;
	}

	public Integer getProductType() {
		return productType;
	}

	public void setProductType(Integer productType) {
		this.productType = productType;
	}

	public String getCommodityIds() {
		return commodityIds;
	}

	public void setCommodityIds(String commodityIds) {
		this.commodityIds = commodityIds;
	}

	public String getStoreName() {
		return storeName;
	}

	public void setStoreName(String storeName) {
		this.storeName = storeName;
	}

	public String getStoreId() {
		return storeId;
	}

	public void setStoreId(String storeId) {
		this.storeId = storeId;
	}

	public BigDecimal getAmount() {
		return amount;
	}

	public void setAmount(BigDecimal amount) {
		this.amount = amount;
	}

	public BigDecimal getCommodityPrice() {
		return commodityPrice;
	}

	public void setCommodityPrice(BigDecimal commodityPrice) {
		this.commodityPrice = commodityPrice;
	}

	public String getOrderTime() {
		return orderTime;
	}

	public void setOrderTime(String orderTime) {
		this.orderTime = orderTime;
	}

	public String getStoreCode() {
		return storeCode;
	}

	public void setStoreCode(String storeCode) {
		this.storeCode = storeCode;
	}

	public BigDecimal getProductNumber() {
		return productNumber;
	}

	public void setProductNumber(BigDecimal productNumber) {
		this.productNumber = productNumber;
	}

	public Long getEnterpriseId() {
		return enterpriseId;
	}

	public void setEnterpriseId(Long enterpriseId) {
		this.enterpriseId = enterpriseId;
	}

	public String getCommodityCode() {
		return commodityCode;
	}

	public void setCommodityCode(String commodityCode) {
		this.commodityCode = commodityCode;
	}

	public String getCommodityName() {
		return commodityName;
	}

	public void setCommodityName(String commodityName) {
		this.commodityName = commodityName;
	}

	public String getCommodityDescribe() {
		return commodityDescribe;
	}

	public void setCommodityDescribe(String commodityDescribe) {
		this.commodityDescribe = commodityDescribe;
	}

	public String getCommodityAid() {
		return commodityAid;
	}

	public void setCommodityAid(String commodityAid) {
		this.commodityAid = commodityAid;
	}

	public String getCommoditySpec() {
		return commoditySpec;
	}

	public void setCommoditySpec(String commoditySpec) {
		this.commoditySpec = commoditySpec;
	}

	public BigDecimal getBrandId() {
		return brandId;
	}

	public void setBrandId(BigDecimal brandId) {
		this.brandId = brandId;
	}

	public BigDecimal getCommodityCycleId() {
		return commodityCycleId;
	}

	public void setCommodityCycleId(BigDecimal commodityCycleId) {
		this.commodityCycleId = commodityCycleId;
	}

	public Long getCommodityThirdId() {
		return commodityThirdId;
	}

	public void setCommodityThirdId(Long commodityThirdId) {
		this.commodityThirdId = commodityThirdId;
	}

	public String getCommodityThirdKind() {
		return commodityThirdKind;
	}

	public void setCommodityThirdKind(String commodityThirdKind) {
		this.commodityThirdKind = commodityThirdKind;
	}

	public BigDecimal getCommodityTypeId() {
		return commodityTypeId;
	}

	public void setCommodityTypeId(BigDecimal commodityTypeId) {
		this.commodityTypeId = commodityTypeId;
	}

	public String getCommodityWeight() {
		return commodityWeight;
	}

	public void setCommodityWeight(String commodityWeight) {
		this.commodityWeight = commodityWeight;
	}

	public BigDecimal getCommodityUnitId() {
		return commodityUnitId;
	}

	public void setCommodityUnitId(BigDecimal commodityUnitId) {
		this.commodityUnitId = commodityUnitId;
	}

	public String getCommodityState() {
		return commodityState;
	}

	public void setCommodityState(String commodityState) {
		this.commodityState = commodityState;
	}

	public BigDecimal getCommodityPackageId() {
		return commodityPackageId;
	}

	public void setCommodityPackageId(BigDecimal commodityPackageId) {
		this.commodityPackageId = commodityPackageId;
	}

	public String getCommodityFactory() {
		return commodityFactory;
	}

	public void setCommodityFactory(String commodityFactory) {
		this.commodityFactory = commodityFactory;
	}

	public String getNewWorkshop() {
		return newWorkshop;
	}

	public void setNewWorkshop(String newWorkshop) {
		this.newWorkshop = newWorkshop;
	}

	public BigDecimal getRetailPrice() {
		return retailPrice;
	}

	public void setRetailPrice(BigDecimal retailPrice) {
		this.retailPrice = retailPrice;
	}

	public BigDecimal getCostPrice() {
		return costPrice;
	}

	public void setCostPrice(BigDecimal costPrice) {
		this.costPrice = costPrice;
	}

	public BigDecimal getFirstCost() {
		return firstCost;
	}

	public void setFirstCost(BigDecimal firstCost) {
		this.firstCost = firstCost;
	}

	public Long getTaxRateId() {
		return taxRateId;
	}

	public void setTaxRateId(Long taxRateId) {
		this.taxRateId = taxRateId;
	}

	public BigDecimal getTaxRate() {
		return taxRate;
	}

	public void setTaxRate(BigDecimal taxRate) {
		this.taxRate = taxRate;
	}

	public BigDecimal getOtherPrice() {
		return otherPrice;
	}

	public void setOtherPrice(BigDecimal otherPrice) {
		this.otherPrice = otherPrice;
	}

	public Date getLaunchDate() {
		return launchDate;
	}

	public void setLaunchDate(Date launchDate) {
		this.launchDate = launchDate;
	}

	public Date getDelistingDate() {
		return delistingDate;
	}

	public void setDelistingDate(Date delistingDate) {
		this.delistingDate = delistingDate;
	}

	public Date getPauseBeginDate() {
		return pauseBeginDate;
	}

	public void setPauseBeginDate(Date pauseBeginDate) {
		this.pauseBeginDate = pauseBeginDate;
	}

	public Date getPauseEndDate() {
		return pauseEndDate;
	}

	public void setPauseEndDate(Date pauseEndDate) {
		this.pauseEndDate = pauseEndDate;
	}

	public String getPauseRemark() {
		return pauseRemark;
	}

	public void setPauseRemark(String pauseRemark) {
		this.pauseRemark = pauseRemark;
	}

	public Short getOrderIsTimeRange() {
		return orderIsTimeRange;
	}

	public void setOrderIsTimeRange(Short orderIsTimeRange) {
		this.orderIsTimeRange = orderIsTimeRange;
	}

	public BigDecimal getMinOrder() {
		return minOrder;
	}

	public void setMinOrder(BigDecimal minOrder) {
		this.minOrder = minOrder;
	}

	public Short getTimeLimitOrder() {
		return timeLimitOrder;
	}

	public void setTimeLimitOrder(Short timeLimitOrder) {
		this.timeLimitOrder = timeLimitOrder;
	}

	public BigDecimal getPieceRate() {
		return pieceRate;
	}

	public void setPieceRate(BigDecimal pieceRate) {
		this.pieceRate = pieceRate;
	}

	public Short getCommodityIsQuickFreeze() {
		return commodityIsQuickFreeze;
	}

	public void setCommodityIsQuickFreeze(Short commodityIsQuickFreeze) {
		this.commodityIsQuickFreeze = commodityIsQuickFreeze;
	}

	public Short getCommodityIsInstant() {
		return commodityIsInstant;
	}

	public void setCommodityIsInstant(Short commodityIsInstant) {
		this.commodityIsInstant = commodityIsInstant;
	}

	public String getCommodityColor() {
		return commodityColor;
	}

	public void setCommodityColor(String commodityColor) {
		this.commodityColor = commodityColor;
	}

	public String getCommodityShape() {
		return commodityShape;
	}

	public void setCommodityShape(String commodityShape) {
		this.commodityShape = commodityShape;
	}

	public Integer getQualityDays() {
		return qualityDays;
	}

	public void setQualityDays(Integer qualityDays) {
		this.qualityDays = qualityDays;
	}

	public BigDecimal getQualityUnitId() {
		return qualityUnitId;
	}

	public void setQualityUnitId(BigDecimal qualityUnitId) {
		this.qualityUnitId = qualityUnitId;
	}

	public String getStorageCondition() {
		return storageCondition;
	}

	public void setStorageCondition(String storageCondition) {
		this.storageCondition = storageCondition;
	}

	public String getCommodityRemark() {
		return commodityRemark;
	}

	public void setCommodityRemark(String commodityRemark) {
		this.commodityRemark = commodityRemark;
	}

	public String getCompanyStandard() {
		return companyStandard;
	}

	public void setCompanyStandard(String companyStandard) {
		this.companyStandard = companyStandard;
	}

	public Date getCreateTime() {
		return createTime;
	}

	public void setCreateTime(Date createTime) {
		this.createTime = createTime;
	}

	public Date getUpdateTime() {
		return updateTime;
	}

	public void setUpdateTime(Date updateTime) {
		this.updateTime = updateTime;
	}

	public Long getStorageTypeId() {
		return storageTypeId;
	}

	public void setStorageTypeId(Long storageTypeId) {
		this.storageTypeId = storageTypeId;
	}

	public Long getWeightUnitId() {
		return weightUnitId;
	}

	public void setWeightUnitId(Long weightUnitId) {
		this.weightUnitId = weightUnitId;
	}

	public Long getComStandardId() {
		return comStandardId;
	}

	public void setComStandardId(Long comStandardId) {
		this.comStandardId = comStandardId;
	}

	public Long getExcuteStandardId() {
		return excuteStandardId;
	}

	public void setExcuteStandardId(Long excuteStandardId) {
		this.excuteStandardId = excuteStandardId;
	}

	public Long getGradeId() {
		return gradeId;
	}

	public void setGradeId(Long gradeId) {
		this.gradeId = gradeId;
	}

	public Long getSafeTypeId() {
		return safeTypeId;
	}

	public void setSafeTypeId(Long safeTypeId) {
		this.safeTypeId = safeTypeId;
	}

	public Long getDownTypeId() {
		return downTypeId;
	}

	public void setDownTypeId(Long downTypeId) {
		this.downTypeId = downTypeId;
	}

	public String getCategoryCodeName() {
		return categoryCodeName;
	}

	public void setCategoryCodeName(String categoryCodeName) {
		this.categoryCodeName = categoryCodeName;
	}

	public Integer getIsBrandLocked() {
		return isBrandLocked;
	}

	public void setIsBrandLocked(Integer isBrandLocked) {
		this.isBrandLocked = isBrandLocked;
	}

	public Integer getIsWeight() {
		return isWeight;
	}

	public void setIsWeight(Integer isWeight) {
		this.isWeight = isWeight;
	}

	public Integer getIsSummary() {
		return isSummary;
	}

	public void setIsSummary(Integer isSummary) {
		this.isSummary = isSummary;
	}

	public Integer getIsBtb() {
		return isBtb;
	}

	public void setIsBtb(Integer isBtb) {
		this.isBtb = isBtb;
	}

	public Integer getIsOto() {
		return isOto;
	}

	public void setIsOto(Integer isOto) {
		this.isOto = isOto;
	}

	public String getBarCode() {
		return barCode;
	}

	public void setBarCode(String barCode) {
		this.barCode = barCode;
	}

	public String getOrigin() {
		return origin;
	}

	public void setOrigin(String origin) {
		this.origin = origin;
	}

	public Long getCategoryCodeId() {
		return categoryCodeId;
	}

	public void setCategoryCodeId(Long categoryCodeId) {
		this.categoryCodeId = categoryCodeId;
	}

	public String getPicUrl1() {
		return picUrl1;
	}

	public void setPicUrl1(String picUrl1) {
		this.picUrl1 = picUrl1;
	}

	public String getPicUrl2() {
		return picUrl2;
	}

	public void setPicUrl2(String picUrl2) {
		this.picUrl2 = picUrl2;
	}

	public String getPicUrl3() {
		return picUrl3;
	}

	public void setPicUrl3(String picUrl3) {
		this.picUrl3 = picUrl3;
	}

	public Float getLength() {
		return length;
	}

	public void setLength(Float length) {
		this.length = length;
	}

	public Float getWidth() {
		return width;
	}

	public void setWidth(Float width) {
		this.width = width;
	}

	public Float getHeight() {
		return height;
	}

	public void setHeight(Float height) {
		this.height = height;
	}

	public Float getWeight() {
		return weight;
	}

	public void setWeight(Float weight) {
		this.weight = weight;
	}

	public Integer getLogisticsModel() {
		return logisticsModel;
	}

	public void setLogisticsModel(Integer logisticsModel) {
		this.logisticsModel = logisticsModel;
	}

	public BigDecimal getCommodityPackageSpec() {
		return commodityPackageSpec;
	}

	public void setCommodityPackageSpec(BigDecimal commodityPackageSpec) {
		this.commodityPackageSpec = commodityPackageSpec;
	}

	public void setProductRemark(String productRemark) {
		this.productRemark = productRemark;
	}

	public String getCommodityPackageIdStr() {
		return getCommodityPackageId() == null ? null : getCommodityPackageId().toString();
	}
}
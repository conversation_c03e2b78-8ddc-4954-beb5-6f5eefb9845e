package com.pinshang.qingyun.order.manage.dto.settlement;

import com.alibaba.excel.annotation.ExcelProperty;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.pinshang.qingyun.base.configure.codec.DecimalSerializerKeep2;
import com.pinshang.qingyun.base.enums.order.OrderTypeEnum;
import com.pinshang.qingyun.renderer.annotation.FieldRender;
import com.pinshang.qingyun.renderer.constant.RenderFieldHelper;
import com.pinshang.qingyun.renderer.enums.FieldTypeEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * Created by hhf on 2018/6/12.
 * 结算明细报表
 */
@Data
public class SettlementDetailsReportEntry {

    /**门店id**/
    private Long shopId;

    /**门店名称**/
    private String shopName;

    /**送货日期**/
    private Date orderTime;

    /**结算日期**/
    private Date settleTime;

    /**订单来源**/
    private Integer orderType;

    /**订单来源-名称**/
    private String orderTypeName;

    /**订单编号**/
    private String subOrderCode;

    /**预订单编号**/
    private String preOrderCode;

    /**分类**/
    private String categoryName;

    /**条形码**/
    private String barCode;
    private String barCodes;
    private List<String> barCodeList;

    /**商品编码**/
    private String commodityCode;

    /**商品名称**/
    private String commodityName;

    /**规格**/
    private String commoditySpec;

    /**计量单位**/
    private String commodityUnitName;


    /**订货价**/
    private BigDecimal price;

    /**订货数量**/
    private BigDecimal quantity;

    /**实发数量**/
    private BigDecimal realDeliveryQuantity;

    /**结算金额**/
    private BigDecimal settlePrice;

    //物流模式
    private Integer logisticsModel;

    private Long subOrderId;



    /**档口ID**/
    private Long stallId;

    /**档口名称**/
    @FieldRender(fieldType = FieldTypeEnum.STALL, fieldName = RenderFieldHelper.Stall.stallName, keyName = "stallId")
    private String stallName;


    public BigDecimal getSettlePrice() {
        return settlePrice.setScale(2,BigDecimal.ROUND_HALF_UP);
    }

    public String getOrderTypeName() {
        if(null != orderType){
            OrderTypeEnum orderType1 = OrderTypeEnum.fromCode(orderType);
            if(null != orderType1){
                return orderType1.getDesc();
            }
        }
        return orderTypeName;
    }
}

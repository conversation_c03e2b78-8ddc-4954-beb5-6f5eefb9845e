package com.pinshang.qingyun.order.manage.controller;

import com.pinshang.qingyun.order.manage.dto.auto.AutoOverZbCommodityDTO;
import com.pinshang.qingyun.order.manage.service.auto.AutoSettingService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * @Author: sk
 * @Date: 2022/7/7
 */
@Slf4j
@RequestMapping("/autoSetting")
@RestController
public class AutoSettingController {

    @Autowired
    private AutoSettingService autoSettingService;

    /**
     * 查询规划池限制开关
     * @return
     */
    @GetMapping("getAutoOverZbCommodity")
    public AutoOverZbCommodityDTO getAutoOverZbCommodity() {
        return autoSettingService.getAutoOverZbCommodity();
    }

    /**
     * 更新规划池限制开关
     * @param idto
     * @return
     */
    @PostMapping("updateAutoOverZbCommodity")
    public Boolean updateAutoOverZbCommodity(@RequestBody AutoOverZbCommodityDTO idto) {
        return autoSettingService.updateAutoOverZbCommodity(idto);
    }
}

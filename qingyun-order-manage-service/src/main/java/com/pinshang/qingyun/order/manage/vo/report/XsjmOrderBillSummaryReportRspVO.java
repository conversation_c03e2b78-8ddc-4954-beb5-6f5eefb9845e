package com.pinshang.qingyun.order.manage.vo.report;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

@Data
public class XsjmOrderBillSummaryReportRspVO {

    @ApiModelProperty("期初余额")
    private BigDecimal beginningBalance = BigDecimal.ZERO;

    @ApiModelProperty("品鲜充值(品鲜门店充值功能充值+品鲜后台充值)")
    private BigDecimal rechargingMoney= BigDecimal.ZERO;

    @ApiModelProperty("商品少发退款充值：称重品、标品少货退款充值金额合计")
    private BigDecimal underDeliveryRefundMoney= BigDecimal.ZERO;

    @ApiModelProperty("商品变价退款：变价订单的退款合计")
    private BigDecimal changePriceRefundMoney= BigDecimal.ZERO;

    @ApiModelProperty("退货/少货退款：退货单少货单审核通过后的退款合计")
    private BigDecimal outOfStockRefundMoney= BigDecimal.ZERO;

    @ApiModelProperty("日结：日结退款合计")
    private BigDecimal daySettleRefundMoney= BigDecimal.ZERO;

    @ApiModelProperty("称重品多发扣款：称重品多发扣款合计")
    private BigDecimal overDeliveryDeduction= BigDecimal.ZERO;

    @ApiModelProperty("订货支付扣款：（订货扣款-取消订单退款）")
    private BigDecimal orderMoneyDeduction= BigDecimal.ZERO;

    @ApiModelProperty("商品变价扣款：变价订单的重新扣款合计")
    private BigDecimal changePriceDeduction= BigDecimal.ZERO;

    @ApiModelProperty("提现扣款：（申请扣款-取消扣款）")
    private BigDecimal withDrawCashDeduction= BigDecimal.ZERO;

    @ApiModelProperty("余额：期初余额+充值合计-扣款合计）")
    private BigDecimal balance= BigDecimal.ZERO;

    @ApiModelProperty("充值合计")
    private BigDecimal totalRechargeMoney = BigDecimal.ZERO;

    @ApiModelProperty("扣款合计")
    private BigDecimal totalDeductionMoney = BigDecimal.ZERO;

}

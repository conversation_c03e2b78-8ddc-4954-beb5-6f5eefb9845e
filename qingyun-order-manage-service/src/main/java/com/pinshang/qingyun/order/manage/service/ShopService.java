package com.pinshang.qingyun.order.manage.service;

import com.pinshang.qingyun.base.configure.expand.QYAssert;
import com.pinshang.qingyun.base.util.FastThreadLocalUtil;
import com.pinshang.qingyun.order.manage.mapper.ShopMapper;
import com.pinshang.qingyun.order.manage.mapper.entry.StoreEntry;
import com.pinshang.qingyun.order.manage.model.Shop;
import com.pinshang.qingyun.shop.dto.bigShop.StallODTO;
import com.pinshang.qingyun.shop.service.bigShop.StallClient;
import com.pinshang.qingyun.smm.dto.userstall.SelectUserStallIdListIDTO;
import com.pinshang.qingyun.smm.service.UserStallClient;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Example;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @Author: sk
 * @Date: 2024/12/3
 */
@Service
public class ShopService {

    @Autowired
    private ShopMapper shopMapper;
    @Autowired
    private UserStallClient userStallClient;
    @Autowired
    private StallClient stallClient;

    /**
     * 根据 storeId 获取门店信息
     * @return
     */
    public List<Shop> getShopByStoreIdList(List<Long> storeIdList){
        QYAssert.notNull(storeIdList,"客户ID不能为空");

        Example shopEx = new Example(Shop.class);
        shopEx.createCriteria().andIn("storeId", storeIdList);
        return shopMapper.selectByExample(shopEx);
    }

    /**
     * 根据 shopType 获取storeIdList
     * @return
     */
    public List<Long> getStoreIdListByShopType(Integer shopType){
        QYAssert.notNull(shopType,"shopType不能为空");

        Example shopEx = new Example(Shop.class);
        shopEx.createCriteria().andEqualTo("shopType", shopType);
        List<Shop> shopList = shopMapper.selectByExample(shopEx);
        return shopList.stream().map(item -> item.getStoreId()).collect(Collectors.toList());
    }

    /**
     * 根据 storeId 获取storeCode ,门店信息
     * @return
     */
    public List<StoreEntry> getStoreByStoreIdList(List<Long> storeIdList){
        QYAssert.notNull(storeIdList,"客户ID不能为空");
        return  shopMapper.selectStoreList(storeIdList);
    }

    /**
     * 根据 shopId 获取门店信息
     * @return
     */
    public List<Shop> getShopByIdList(List<Long> shopIdList){
        QYAssert.notNull(shopIdList,"ID不能为空");

        Example shopEx = new Example(Shop.class);
        shopEx.createCriteria().andIn("id", shopIdList);
        return shopMapper.selectByExample(shopEx);
    }


    /**
     * 根据shopId获取用户权限下面的档口list
     * @return
     */
    public List<Long> selectUserStallIdList(Long shopId){
        SelectUserStallIdListIDTO idto = SelectUserStallIdListIDTO.init(FastThreadLocalUtil.getQY().getUserId(), shopId);
        return userStallClient.selectUserStallIdList(idto);
    }

    /**
     * 根据档口ids获取档口名称 map
     * @param stallIdList
     * @return
     */
    public Map<Long, String> queryStallMapByIds(List<Long> stallIdList) {
        if(CollectionUtils.isEmpty(stallIdList)){
            return new HashMap<>();
        }
        Map<Long, String> stallMap = new HashMap<>(stallIdList.size());
        List<StallODTO> stallODTOList = stallClient.queryStallByIds(stallIdList);
        if(CollectionUtils.isNotEmpty(stallODTOList)){
            stallODTOList.forEach(stallODTO -> stallMap.put(stallODTO.getId(), stallODTO.getStallName()));
        }
        return stallMap;
    }
}

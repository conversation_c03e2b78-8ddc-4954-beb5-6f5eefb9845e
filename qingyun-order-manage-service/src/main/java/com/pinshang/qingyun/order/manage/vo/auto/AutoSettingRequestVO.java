package com.pinshang.qingyun.order.manage.vo.auto;

import com.pinshang.qingyun.base.page.Pagination;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class AutoSettingRequestVO extends Pagination {

    @ApiModelProperty("部门Code")
    private String deptCode;

    private Long shopId;

    private List<Long> shopIds;

    @ApiModelProperty("门店状态")
    private Integer shopStatus;

    @ApiModelProperty("设置启用启用")
    private Integer status;
}

package com.pinshang.qingyun.order.manage.model.promotion;

import com.pinshang.qingyun.base.po.BaseEnterprisePO;

import javax.persistence.Entity;
import javax.persistence.Table;
import javax.persistence.Transient;
import java.util.Date;

/**
 * 促销配货管理-商品特价方案
 */
@Entity
@Table(name = "t_promotion")
public class Promotion  extends BaseEnterprisePO {

    /**
     *产品特价方案名称
     */
    private String promotionName;
    /**
     * 特价方案备注
     */
    private String remark;
    /**
     *特价方案的开始时间
     */
    private Date startTime;
    /**
     * 特价方案的结束时间
     */
    private Date endTime;
    /**
     * 创建者ID
     */
    private Long createUserId;
    /**
     * 创建者名称
     */
    private String createUsername;

    /**
     * 状态 0表示启用 1停用
     */
    private Integer status;

    /**
     * 是否有效 0有效,1无效
     */
    @Transient
    private Integer valid;

    public String getPromotionName() {
        return promotionName;
    }

    public void setPromotionName(String promotionName) {
        this.promotionName = promotionName;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public Date getStartTime() {
        return startTime;
    }

    public void setStartTime(Date startTime) {
        this.startTime = startTime;
    }

    public Date getEndTime() {
        return endTime;
    }

    public void setEndTime(Date endTime) {
        this.endTime = endTime;
    }

    public Long getCreateUserId() {
        return createUserId;
    }

    public void setCreateUserId(Long createUserId) {
        this.createUserId = createUserId;
    }

    public String getCreateUsername() {
        return createUsername;
    }

    public void setCreateUsername(String createUsername) {
        this.createUsername = createUsername;
    }
     

    public void setStatus(Integer status) {
        this.status = status;
    }

    public void setValid(Integer valid) {
        this.valid = valid;
    }

    public Integer getStatus() {
        return status;
    }

    public Integer getValid() {
        return valid;
    }
}

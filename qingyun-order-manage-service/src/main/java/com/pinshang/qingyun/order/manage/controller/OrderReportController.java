package com.pinshang.qingyun.order.manage.controller;

import com.alibaba.excel.EasyExcel;
import com.github.pagehelper.PageInfo;
import com.pinshang.qingyun.base.api.TokenInfo;
import com.pinshang.qingyun.base.configure.expand.QYAssert;
import com.pinshang.qingyun.base.enums.shop.ManagementModeEnums;
import com.pinshang.qingyun.base.page.TablePageInfo;
import com.pinshang.qingyun.base.util.ExcelUtil;
import com.pinshang.qingyun.base.util.FastThreadLocalUtil;
import com.pinshang.qingyun.box.utils.BeanCloneUtils;
import com.pinshang.qingyun.box.utils.DateTimeUtil;
import com.pinshang.qingyun.box.utils.DateUtil;
import com.pinshang.qingyun.box.utils.SpringUtil;
import com.pinshang.qingyun.infrastructure.exportfile.cache.starter.FileCacheQuery;
import com.pinshang.qingyun.order.manage.dto.export.ActualReceiptAnalysisReportExportODTO;
import com.pinshang.qingyun.order.manage.dto.export.RealDeliveryStoreTypeReportExportODTO;
import com.pinshang.qingyun.order.manage.dto.export.ReturnOrderDetailsExportODTO;
import com.pinshang.qingyun.order.manage.dto.report.*;
import com.pinshang.qingyun.order.manage.enums.ExcelSheetTitleEnum;
import com.pinshang.qingyun.order.manage.enums.ResponsiblePartyEnum;
import com.pinshang.qingyun.order.manage.service.OrderReportService;
import com.pinshang.qingyun.order.manage.util.ViewExcel;
import com.pinshang.qingyun.order.manage.vo.report.ReturnOrderDetailsReportVo;
import com.pinshang.qingyun.order.manage.vo.report.XsjmOrderBillSummaryReportReqVO;
import com.pinshang.qingyun.order.manage.vo.report.XsjmOrderBillSummaryReportRspVO;
import com.pinshang.qingyun.renderer.annotation.MethodRender;
import com.pinshang.qingyun.renderer.service.IRenderService;
import com.pinshang.qingyun.smm.dto.user.SelectUserShopIdListIDTO;
import com.pinshang.qingyun.smm.service.SMMUserClient;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.joda.time.LocalDate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;

@RestController
@RequestMapping("/report")
@Api(value = "门店报表接口", tags = "shopReport", description = "门店报表相关接口")
@Slf4j
public class OrderReportController {

    @Autowired
    private OrderReportService orderReportService;

    @Autowired
    private IRenderService renderService;
    @Autowired
    private SMMUserClient smmUserClient;


    @MethodRender
    @ApiOperation(value = "查询退单明细表导出（门店）|（总部）")
    @PostMapping("/findReturnOrderDetailsReport")
    public PageInfo<ReturnOrderDetailsReportEntry> findReturnOrderDetailsReport(@RequestBody ReturnOrderDetailsReportVo returnOrderDetailsReportVo) throws ParseException {
        return orderReportService.findReturnOrderDetailsReport(returnOrderDetailsReportVo);
    }

    @ApiOperation(value = "导出退单明细表导出（门店）|（总部）")
    @RequestMapping(value="/exportInfo/findReturnOrderDetailsReport",method = RequestMethod.GET)
    @FileCacheQuery(bizCode = "MD_RETURN_REPORT_EXPORT_EXCEL")
    public void exportInfoFindAllReturnOrderDetailsReport(
            @RequestParam(value="orderCode",required=false) String orderCode,
            @RequestParam(value="commodityFirstId",required=false) Long commodityFirstId,
            @RequestParam(value="orderTimeStartStr",required=false) String orderTimeStartStr,
            @RequestParam(value="orderTimeEndStr",required=false) String orderTimeEndStr,
            @RequestParam(value="searchWord",required=false) String searchWord,
            @RequestParam(value="barCode",required=false) String barCode,
            @RequestParam(value="shopId",required=false) Long shopId,
            @RequestParam(value="returnReason",required=false) Integer returnReason,
            @RequestParam(value="consignmentId",required=false) Long consignmentId,
            @RequestParam(value="stallId",required=false) Long stallId,HttpServletResponse response

    ) throws IOException {
        TokenInfo ti = FastThreadLocalUtil.getQY();
        QYAssert.isTrue(StringUtils.isNotBlank(orderTimeEndStr), "请选择截至日期");
        QYAssert.isTrue(StringUtils.isNotBlank(orderTimeStartStr), "请选择起始日期");
        ReturnOrderDetailsReportVo returnOrderDetailsReportIDto = new ReturnOrderDetailsReportVo();
        returnOrderDetailsReportIDto.setPageNo(1);
        returnOrderDetailsReportIDto.setPageSize(Integer.MAX_VALUE);
        returnOrderDetailsReportIDto.setOrderCode(orderCode);
        returnOrderDetailsReportIDto.setCommodityFirstId(commodityFirstId);
        returnOrderDetailsReportIDto.setOrderTimeStartStr(orderTimeStartStr);
        returnOrderDetailsReportIDto.setOrderTimeEndStr(orderTimeEndStr);
        returnOrderDetailsReportIDto.setSearchWord(searchWord);
        returnOrderDetailsReportIDto.setShopId(shopId);
        returnOrderDetailsReportIDto.setBarCode(barCode);
        returnOrderDetailsReportIDto.setReturnReason(returnReason);
        returnOrderDetailsReportIDto.setConsignmentId(consignmentId);
        if (returnOrderDetailsReportIDto.getShopId() == null) {
            returnOrderDetailsReportIDto.setShopId(ti.getShopId());
        }
        returnOrderDetailsReportIDto.setStallId(stallId);
        PageInfo<ReturnOrderDetailsReportEntry> pageDate = orderReportService.findReturnOrderDetailsReport(returnOrderDetailsReportIDto);
        List<ReturnOrderDetailsReportEntry> list = pageDate.getList();

        List<ReturnOrderDetailsExportODTO> exportList = new ArrayList<>();
        if(null != list && !list.isEmpty()){
            renderService.render(list, "/exportInfo/findReturnOrderDetailsReport");
            for (ReturnOrderDetailsReportEntry dto : list) {
                ReturnOrderDetailsExportODTO exportDTO = new ReturnOrderDetailsExportODTO();
                exportDTO.setShopName(dto.getShopName());
                exportDTO.setCreateTime(toStr(dto.getCreateTime()));
                exportDTO.setOrderCode(dto.getOrderCode());
                exportDTO.setCateName(dto.getCateName());
                exportDTO.setBarCode(dto.getBarCode());
                exportDTO.setCommodityCode(dto.getCommodityCode());
                exportDTO.setCommodityName(dto.getCommodityName());
                exportDTO.setCommoditySpec(dto.getCommoditySpec());
                exportDTO.setCommodityUnit(dto.getCommodityUnit());
                exportDTO.setPrice(toStr(dto.getPrice()));
                exportDTO.setReturnQuantity(toStr(dto.getReturnQuantity()));
                exportDTO.setTotalPrice(toStr(dto.getTotalPrice()));
                exportDTO.setReturnReason(dto.getReturnReasonName());
                exportDTO.setRemark(dto.getRemark());
                exportDTO.setStallName(dto.getStallName());
                exportList.add(exportDTO);
            }
        }
        Map<String, Object> map = new HashMap<>();
        // 文件名
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmss");
        String filename = "退单明细查询" + "_" + sdf.format(new Date());

        try {
            ExcelUtil.setFileNameAndHead(response, filename);
            EasyExcel.write(response.getOutputStream(), ReturnOrderDetailsExportODTO.class).autoCloseStream(Boolean.TRUE).sheet("退单明细")
                    .doWrite(exportList);
        }catch (Exception e){
            log.error("退单明导出错误", e);
            ExcelUtil.setExceptionResponse( response );
        }

    }

    @MethodRender
    @ApiOperation(value = "门店短交报表", notes = "门店短交报表",produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @RequestMapping(value = "/shortDeliveryReport", method = RequestMethod.GET)
    public PageInfo<ShortDeliveryReportODto> shortDeliveryReport(ShortDeliveryReportIDto shortDeliveryReportIDto) {
        TokenInfo tokenInfo = FastThreadLocalUtil.getQY();
        shortDeliveryReportIDto.setEnterpriseId(tokenInfo.getEnterpriseId());
        if (shortDeliveryReportIDto.getShopId() == null) {
            shortDeliveryReportIDto.setShopId(tokenInfo.getShopId());
        }
        return orderReportService.shortDeliveryReport(shortDeliveryReportIDto);
    }
    @ApiOperation(value = "门店短交报表-导出", notes = "门店短交报表-导出", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @RequestMapping(value = "/exportInfo/shortDeliveryReport", method = RequestMethod.GET)
    @FileCacheQuery(bizCode = "SHORT_DELIVERY_REPORT")
    public void exportInfoShortDeliveryReport(ShortDeliveryReportIDto shortDeliveryReportIDto, HttpServletResponse response) throws IOException {
        TokenInfo tokenInfo = FastThreadLocalUtil.getQY();
        shortDeliveryReportIDto.setEnterpriseId(tokenInfo.getEnterpriseId());
        if (shortDeliveryReportIDto.getShopId() == null) {
            shortDeliveryReportIDto.setShopId(tokenInfo.getShopId());
        }
        QYAssert.isTrue(shortDeliveryReportIDto.getShopId() != null, "权限有问题或未选择门店");
        QYAssert.isTrue(shortDeliveryReportIDto.getBeginDate() != null, "请选择送货日期");
        QYAssert.isTrue(shortDeliveryReportIDto.getEndDate() != null, "请选择送货日期");
        QYAssert.isTrue(DateUtil.isAfter(DateUtil.addMonth(DateTimeUtil.parse(shortDeliveryReportIDto.getBeginDate(), "yyyy-MM-dd"), 1), DateTimeUtil.parse(shortDeliveryReportIDto.getEndDate(), "yyyy-MM-dd")), "送货日期范围不能超过一个月!");
        shortDeliveryReportIDto.setPageNo(1);
        shortDeliveryReportIDto.setPageSize(Integer.MAX_VALUE);

        PageInfo<ShortDeliveryReportODto> result = orderReportService.shortDeliveryReport(shortDeliveryReportIDto);
        List<ShortDeliveryReportODto> list = result.getList();
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmss");
        String fileName = "短交报表" + "_" + sdf.format(new Date());

        renderService.render(list, "/exportInfo/shortDeliveryReport");
        if(shortDeliveryReportIDto.getIsHq() != null && 1 == shortDeliveryReportIDto.getIsHq()) {
            // 导出ShortDeliveryReportIsHqODto
            try {
                List<ShortDeliveryReportIsHqODto> tableData = new ArrayList<>();
                if(SpringUtil.isNotEmpty(list)){
                    tableData =  BeanCloneUtils.copyTo(list, ShortDeliveryReportIsHqODto.class);
                }
                ExcelUtil.setFileNameAndHead(response, fileName);
                EasyExcel.write(response.getOutputStream(), ShortDeliveryReportIsHqODto.class).autoCloseStream(Boolean.TRUE).sheet("短交报表")
                        .doWrite(tableData);

            }catch (Exception e){
                log.error("短交报表导出错误", e);
                ExcelUtil.setExceptionResponse( response );
            }
        }else{
            // 导出 ShortDeliveryReportNotHqODto
            try {
                List<ShortDeliveryReportNoHqODto> tableData = new ArrayList<>();
                if(SpringUtil.isNotEmpty(list)){
                    tableData =  BeanCloneUtils.copyTo(list, ShortDeliveryReportNoHqODto.class);
                }
                ExcelUtil.setFileNameAndHead(response, fileName);
                EasyExcel.write(response.getOutputStream(),ShortDeliveryReportNoHqODto.class ).autoCloseStream(Boolean.TRUE).sheet("短交报表")
                        .doWrite(tableData);

            }catch (Exception e){
                log.error("短交报表导出错误", e);
                ExcelUtil.setExceptionResponse( response );
            }
        }
    }



    @MethodRender
    @ApiOperation(value = "商品实发汇总表(当日)", notes = "商品实发汇总表(当日)",produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @RequestMapping(value = "/realDeliveryReportCurrentDay", method = RequestMethod.GET)
    public TablePageInfo<RealDeliveryReportODto> realDeliveryReportCurrentDay(RealDeliveryReportIDto realDeliveryReportIDto) {
        return orderReportService.realDeliveryReportCurrentDay(realDeliveryReportIDto);
    }
    @ApiOperation(value = "商品实发汇总表-导出(当日)", notes = "商品实发汇总表-导出(当日)", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @RequestMapping(value = "/exportInfo/realDeliveryReportCurrentDay", method = RequestMethod.GET)
    @FileCacheQuery(bizCode = "REAL_DELIVERY_REPORT_CURRENT_DAY")
    public void exportInfoRealDeliveryReportCurrentDay(RealDeliveryReportIDto realDeliveryReportIDto, HttpServletResponse response) throws IOException {
         exportInfoRealDeliveryReport(realDeliveryReportIDto, response);
    }
    public void exportInfoRealDeliveryReport(RealDeliveryReportIDto realDeliveryReportIDto, HttpServletResponse response) throws IOException {
        realDeliveryReportIDto.setPageNo(1);
        realDeliveryReportIDto.setPageSize(65536);

        TablePageInfo<RealDeliveryReportODto> result = orderReportService.realDeliveryReportCurrentDay(realDeliveryReportIDto);

        List<RealDeliveryReportODto> list = result.getList();
        try {
            List<RealDeliveryReportODto> tableData = new ArrayList<>();

            if(!list.isEmpty()){
                renderService.render(list,"/exportInfo/realDeliveryReportCurrentDays");
                BigDecimal realTotalAmount = (BigDecimal) result.getHeader();
                RealDeliveryReportODto head = new RealDeliveryReportODto();
                head.setCommodityCode("实发金额合计:");
                head.setRealDeliveryAmount(realTotalAmount);
                tableData.add(head);
                tableData.addAll(list);
            }

            ExcelUtil.setFileNameAndHead(response, "商品实发汇总报表");
            EasyExcel.write(response.getOutputStream(),RealDeliveryReportODto.class ).autoCloseStream(Boolean.TRUE).sheet("商品实发汇总报表")
                    .doWrite(tableData);

        }catch (Exception e){
            log.error("短交报表导出错误", e);
            ExcelUtil.setExceptionResponse( response );
        }
    }

    @MethodRender
    @ApiOperation(value = "商品实发汇总表(按客户类型) 当日", notes = "商品实发汇总表(按客户类型) 当日",produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @RequestMapping(value = "/realDeliveryStoreTypeReportCurrentDay", method = RequestMethod.GET)
    public TablePageInfo<RealDeliveryReportODto> realDeliveryStoreTypeReportCurrentDay(RealDeliveryReportIDto realDeliveryReportIDto) {
        return orderReportService.realDeliveryStoreTypeReportCurrentDay(realDeliveryReportIDto);
    }
    @ApiOperation(value = "商品实发汇总表-导出(按客户类型) 当日", notes = "商品实发汇总表-导出(按客户类型) 当日", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @RequestMapping(value = "/exportInfo/realDeliveryStoreTypeReportCurrentDay", method = RequestMethod.GET)
    @FileCacheQuery(bizCode = "REAL_DELIVERY_STORETYPE_EXPORT_EXCEL")
    public void exportInfoRealDeliveryStoreTypeReportCurrentDay(RealDeliveryReportIDto realDeliveryReportIDto, HttpServletResponse response) throws IOException {
        realDeliveryReportIDto.setPageNo(1);
        realDeliveryReportIDto.setPageSize(65536);

        TablePageInfo<RealDeliveryReportODto> result = orderReportService.realDeliveryStoreTypeReportCurrentDay(realDeliveryReportIDto);

        List<RealDeliveryReportODto> list = result.getList();
        BigDecimal realTotalAmount = BigDecimal.ZERO;
        List<RealDeliveryStoreTypeReportExportODTO> exportList = new ArrayList<>();
        if (null != list && !list.isEmpty()) {
            renderService.render(list, "/exportInfo/realDeliveryStoreTypeReportCurrentDay");
            realTotalAmount = (BigDecimal) result.getHeader();
            // 设置合计行
            RealDeliveryStoreTypeReportExportODTO head = new RealDeliveryStoreTypeReportExportODTO();
            head.setStoreTypeName("合计");
            head.setRealDeliveryAmount(realTotalAmount + "");
            exportList.add(head);
            for (RealDeliveryReportODto dto : list) {
                RealDeliveryStoreTypeReportExportODTO exportDto = new RealDeliveryStoreTypeReportExportODTO();
                exportDto.setStoreTypeName(dto.getStoreTypeName());
                exportDto.setCommodityCode(dto.getCommodityCode());
                exportDto.setCommodityName(dto.getCommodityName());
                exportDto.setCommoditySpec(dto.getCommoditySpec());
                exportDto.setBarCode(dto.getBarCode());
                exportDto.setCommodityFirstName(dto.getCommodityFirstName());
                exportDto.setCommodityUnit(dto.getCommodityUnit());
                exportDto.setOrderNum(dto.getOrderNum() == null ? "" : dto.getOrderNum().toString());
                exportDto.setDeliveryNum(dto.getDeliveryNum() == null ? "" : dto.getDeliveryNum().toString());
                exportDto.setRealDeliveryAmount(dto.getRealDeliveryAmount() == null ? "" : dto.getRealDeliveryAmount().toString());
                exportDto.setWarehouseName(dto.getWarehouseName());
                exportDto.setFactoryName(dto.getFactoryName());
                exportDto.setWorkshopName(dto.getWorkshopName());
                exportDto.setFlowshopName(dto.getFlowshopName());
                exportList.add(exportDto);
            }
        }
        Map<String, Object> map = new HashMap<>();
        // 文件名
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmss");
        String filename = "商品实发汇总报表(客户类型)" + "_" + sdf.format(new Date());

        try {
            ExcelUtil.setFileNameAndHead(response, filename);
            EasyExcel.write(response.getOutputStream(), RealDeliveryStoreTypeReportExportODTO.class).autoCloseStream(Boolean.TRUE).sheet("商品实发汇总报表(客户类型)")
                    .doWrite(exportList);
        }catch (Exception e){
            log.error("门店盘点单导出错误", e);
            ExcelUtil.setExceptionResponse( response );
        }
    }




    @MethodRender
    @ApiOperation(value = "门店订货汇总表(当日)", notes = "门店订货汇总表(当日)",produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @RequestMapping(value = "/shopOrderGoodReportCurrentDay", method = RequestMethod.POST)
    public PageInfo<ShopOrderGoodReportODto> shopOrderGoodReportCurrentDay(@RequestBody ShopOrderGoodReportIDto idto) {
        return orderReportService.shopOrderGoodReportCurrentDay(idto);
    }


    @PostMapping("/exportShopOrderGoodReportCurrentDay")
    @ApiOperation("导出门店订货汇总表")
    @FileCacheQuery(bizCode = "SHOP_ORDER_GOOD_REPORT_DAY")
    public void exportShopOrderGoodReportCurrentDay(@RequestBody ShopOrderGoodReportIDto idto, HttpServletResponse response) throws IOException {
        idto.initExportPage();
        PageInfo<ShopOrderGoodReportODto> page = orderReportService.shopOrderGoodReportCurrentDay(idto);
        List<ShopOrderGoodReportODto> list = page.getList();
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmss");

        try {
            renderService.render(list, "/exportShopOrderGoodReportCurrentDay");
            List<ShopOrderGoodReportExportDTO> exportList = BeanCloneUtils.copyTo(list, ShopOrderGoodReportExportDTO.class);
            ExcelUtil.setFileNameAndHead(response,  "门店订货汇总表"+"_"+ sdf.format(new Date()));
            EasyExcel.write(response.getOutputStream(), ShopOrderGoodReportExportDTO.class)
                    .autoCloseStream(Boolean.TRUE).sheet("门店订货汇总表").doWrite(exportList);

        } catch (Exception e) {
            log.error("导出门店订货汇总表-导出报错",e);
            ExcelUtil.setExceptionResponse( response );
        }

    }




    @ApiOperation(value = "门店实收商品分析表", notes = "门店实收商品分析表", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @RequestMapping(value = "/actualReceiptAnalysisReport", method = RequestMethod.GET)
    public PageInfo<ActualReceiptAnalysisODto> actualReceiptAnalysisReport(ActualReceiptAnalysisIDto idto) throws Exception{
        return orderReportService.actualReceiptAnalysisReport(idto);
    }
    @ApiOperation(value = "门店实收商品分析表-导出", notes = "门店实收商品分析表-导出", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @RequestMapping(value = "/exportInfo/actualReceiptAnalysisReport", method = RequestMethod.GET)
    @FileCacheQuery(bizCode = "REAL_RECEIPT_ANALYSIS_EXPORT_EXCEL")
    public void exportInfoActualReceiptAnalysisReport(ActualReceiptAnalysisIDto actualReceiptAnalysisIDto, HttpServletResponse response)throws Exception {
        actualReceiptAnalysisIDto.setPageNo(1);
        actualReceiptAnalysisIDto.setPageSize(Integer.MAX_VALUE);
        PageInfo<ActualReceiptAnalysisODto> result = actualReceiptAnalysisReport(actualReceiptAnalysisIDto);
        List<ActualReceiptAnalysisODto> list = result.getList();
        int i = 0;
        String shopName = "";
        List<ActualReceiptAnalysisReportExportODTO> exportList = new ArrayList<>();
        if (null != list && !list.isEmpty()) {
            for (ActualReceiptAnalysisODto dto : list) {
                ActualReceiptAnalysisReportExportODTO exportODTO = new ActualReceiptAnalysisReportExportODTO();
                if (i == 0) {
                    if (actualReceiptAnalysisIDto.getShopId() != null) {
                        shopName = dto.getShopName();
                    } else {
                        shopName = "全部";
                    }
                }
                exportODTO.setShopName(dto.getShopName());
                exportODTO.setCateName(dto.getCateName());
                exportODTO.setBarCode(dto.getBarCode());
                exportODTO.setCommodityCode(dto.getCommodityCode());
                exportODTO.setCommodityName(dto.getCommodityName());
                exportODTO.setCommoditySpec(dto.getCommoditySpec());
                exportODTO.setTotalQuantity(toStr(dto.getTotalQuantity()));
                exportODTO.setTotalRealDeliveryQuantity(toStr(dto.getTotalRealDeliveryQuantity()));
                exportODTO.setTotalRealReceiveQuantity(toStr(dto.getTotalRealReceiveQuantity()));
                exportODTO.setQuantityDifference(toStr(dto.getQuantityDifference()));
                exportODTO.setSupplyPrice(toStr(dto.getSupplyPrice()));
                exportODTO.setTotalSupplyPrice(toStr(dto.getTotalSupplyPrice()));
                exportODTO.setSupplierName(dto.getSupplierName());
                exportODTO.setRealName(dto.getRealName());
                exportList.add(exportODTO);
            }
        }
        Map<String, Object> map = new HashMap<>();
        // 文件名
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmss");
        String filename = "门店实收商品分析表" + "_" + sdf.format(new Date());

        try {
            ExcelUtil.setFileNameAndHead(response, filename);
            EasyExcel.write(response.getOutputStream(), ActualReceiptAnalysisReportExportODTO.class).autoCloseStream(Boolean.TRUE).sheet("门店实收商品分析表(" + shopName + ")")
                    .doWrite(exportList);
        }catch (Exception e){
            log.error("门店实收商品分析表导出错误", e);
            ExcelUtil.setExceptionResponse( response );
        }
    }


    private String toStr(Object o) {
        if(o == null) {
            return "";
        }else if(o instanceof Date) {
            return DateFormatUtils.format((Date)o, "yyyy-MM-dd HH:mm:ss");
        }else {
            return o.toString();
        }
    }



    @ApiOperation(value = "代销订货明细", notes = "代销订货明细")
    @PostMapping("/consignmentOrderReport")
    @MethodRender
    public PageInfo<ConsignmentOrderPageODTO> consignmentOrderReport(@RequestBody ConsignmentOrderQueryIDTO vo) {
        return orderReportService.consignmentOrderReport(vo);
    }


    @ApiOperation(value = "导出代销订货明细", notes = "导出代销订货明细")
    @GetMapping("exportConsignmentOrderReport")
    public void exportConsignmentOrderReport(ConsignmentOrderQueryIDTO idto, HttpServletResponse httpServletResponse){
        idto.setPageNo(1);
        idto.setPageSize(Integer.MAX_VALUE);
        PageInfo<ConsignmentOrderPageODTO> pageInfo = orderReportService.consignmentOrderReport(idto);
        List<ConsignmentOrderPageODTO> list = pageInfo.getList();
        if(!SpringUtil.isEmpty(list)){
            renderService.render(list,"/exportConsignmentOrderReport");
        }
        try {
            ExcelUtil.setFileNameAndHead(httpServletResponse,"代销订货明细" + DateUtil.getDateFormate(new Date(), "yyyyMMddHHmmss"));
            EasyExcel.write(httpServletResponse.getOutputStream(),ConsignmentOrderPageODTO.class)
                    .autoCloseStream(Boolean.TRUE).sheet("代销订货明细").doWrite(list);
        } catch (Exception e) {
            e.printStackTrace();
        }

    }


    /**
     * 门店退库明细
     * @param idto
     * @return
     */
    @MethodRender
    @RequestMapping(value = "/saleReturnDetailPage", method = RequestMethod.POST)
    public PageInfo<SaleReturnDetailPageODTO> saleReturnDetailPage(@RequestBody SaleReturnDetailPageIDTO idto) {
        return  orderReportService.saleReturnDetailPage(idto);
    }

    @ApiOperation(value = "导出", notes = "导出门店退货明细")
    @RequestMapping(value = "/export/saleReturnDetailPage", method = RequestMethod.GET)
    public void saleReturnDetailPageExport(SaleReturnDetailPageIDTO idto, HttpServletResponse response) throws IOException {
        idto.initExportPage();
        PageInfo<SaleReturnDetailPageODTO> pageInfo = orderReportService.saleReturnDetailPage(idto);
        List<SaleReturnDetailPageODTO> list = pageInfo.getList();
        if(null == list){
            list = new ArrayList<>();
        }
        renderService.render(pageInfo.getList(),"/package/packageTrackPage");

        TokenInfo ti = FastThreadLocalUtil.getQY();
        Boolean bigShop = ManagementModeEnums.档口分包.getCode().equals(ti.getManagementMode());
        Boolean isZb = (ti.getShopId() == null);
        try {
            ExcelUtil.setFileNameAndHead(response, "门店退货明细" + LocalDate.now().toString("yyyyMMdd"));

            // 总部和大店返回档口列
            if(bigShop || isZb) {
                EasyExcel.write(response.getOutputStream(), SaleReturnDetailPageODTO.class).autoCloseStream(Boolean.TRUE).sheet("门店退货明细")
                        .doWrite(list);
            }else {
                EasyExcel.write(response.getOutputStream(), SaleReturnDetailNoStallPageODTO.class).autoCloseStream(Boolean.TRUE).sheet("门店退货明细")
                        .doWrite(list);
            }

        }catch (Exception e){
            log.error("门店退货明细", e);
            ExcelUtil.setExceptionResponse( response );
        }
    }


    /**
     * 退货差异报表实退金额合计
     * @param vo
     * @return
     */
    @PostMapping("/queryReturnReportSumEntry")
    public SaleReturnReportSumODTO queryReturnReportSumEntry(@RequestBody SaleReturnReportVo vo) {
        List<Long> shopIdList = smmUserClient.selectUserShopIdList( SelectUserShopIdListIDTO.firstCacheThenDb(FastThreadLocalUtil.getQY().getUserId()));
        if(SpringUtil.isEmpty(shopIdList)){
            return null;
        }
        vo.setShopIdList( shopIdList );
        return orderReportService.queryReturnReportSumEntry(vo);
    }

    /**
     * 分页查询退货差异报表
     * @param vo
     * @return
     */
    @MethodRender
    @PostMapping("/returnDifferentReport")
    public PageInfo<SaleReturnReportODTO> listReturnReport(@RequestBody SaleReturnReportVo vo) {
        List<Long> shopIdList = smmUserClient.selectUserShopIdList( SelectUserShopIdListIDTO.firstCacheThenDb(FastThreadLocalUtil.getQY().getUserId()));
        if(SpringUtil.isEmpty(shopIdList)){
            return null;
        }
        vo.setShopIdList( shopIdList );
        return orderReportService.listReturnReport(vo);
    }

    @FileCacheQuery(bizCode = "INFO_SHOP_RETURN_DIFFERENT")
    @ApiOperation(value = "门店退货差异报表导出", notes = "门店退货差异报表导出")
    @RequestMapping(value = "/exportInfo/returnDifferentReport", method = RequestMethod.GET)
    public void exportInfoShopReturnDifferent(SaleReturnReportVo saleReturnReportIDTO, HttpServletResponse httpServletResponse) throws IOException {
        SimpleDateFormat format =new SimpleDateFormat("yyyy-MM-dd");
//        TokenInfo tokenInfo = FastThreadLocalUtil.getQY();
        List<Long> shopIdList = smmUserClient.selectUserShopIdList( SelectUserShopIdListIDTO.firstCacheThenDb(FastThreadLocalUtil.getQY().getUserId()));
        Map<String, List<String>> data = new HashMap<>();
        PageInfo<SaleReturnReportODTO> result = null;
        if(SpringUtil.isNotEmpty(shopIdList )) {
            saleReturnReportIDTO.setShopIdList( shopIdList );
            saleReturnReportIDTO.setPageNo(1);
            saleReturnReportIDTO.setPageSize(Integer.MAX_VALUE);
            result = orderReportService.listReturnReport(saleReturnReportIDTO);
            if(CollectionUtils.isNotEmpty(result.getList())){
                renderService.render(result.getList(),"/saleReturnOrder/exportInfo/returnDifferentReport");
            }
        }
        List<SaleReturnReportODTO> list = new ArrayList<>();
        if(null !=result && !result.getList().isEmpty()){
            SaleReturnReportODTO odto ;
            list = new ArrayList<>(result.getSize());
            for (SaleReturnReportODTO dto : result.getList()) {
                odto = new SaleReturnReportODTO();

                if(null ==dto.getReturnQuantity()){
                    odto.setReturnQuantity("0.00");
                }
                if(null ==dto.getRealReturnQuantity()){
                    odto.setRealReturnQuantity("0.00");
                }
                odto.setDiffQuantity(new BigDecimal(dto.getReturnQuantity()).subtract(new BigDecimal(dto.getRealReturnQuantity())).toString());
                odto.setStoreCode(dto.getStoreCode());
                odto.setShopName(dto.getShopName());
                odto.setOrderCode(dto.getOrderCode());
                odto.setUpdateTime(dto.getUpdateTime());
                odto.setUpdateTimeStr(format.format(odto.getUpdateTime()));
                ResponsiblePartyEnum rpTypeEnum = ResponsiblePartyEnum.getEnumByCode(dto.getRpType());
                odto.setRpTypeStr(rpTypeEnum == null?"":rpTypeEnum.getDesc());
                odto.setRealReturnQuantity(dto.getRealReturnQuantity() ==null ?"":dto.getRealReturnQuantity().toString());
                odto.setPrice(dto.getPrice());
                BigDecimal realTotalPrice = dto.getRealReturnQuantity() != null && dto.getPrice() != null ? new BigDecimal(dto.getRealReturnQuantity()).multiply(dto.getPrice()).setScale(2, RoundingMode.HALF_UP).stripTrailingZeros() : null;
                odto.setRealTotalPrice(realTotalPrice);
                odto.setCommodityCode(dto.getCommodityCode());
                odto.setBarCode(dto.getBarCode());
                odto.setCommodityName(dto.getCommodityName());
                odto.setCommoditySpec(dto.getCommoditySpec());
                odto.setCreateTime(dto.getCreateTime());
                odto.setCreateTimeStr(format.format(odto.getCreateTime()));
                odto.setReturnQuantity(dto.getReturnQuantity() ==null ?"":dto.getReturnQuantity().toString());
                odto.setReturnReasonName(dto.getReturnReasonName());
                if(null ==dto.getReturnQuantity()){
                    dto.setReturnQuantity("0.00");
                }
                if(null ==dto.getRealReturnQuantity()){
                    dto.setRealReturnQuantity("0.00");
                }
                odto.setDiffQuantity(new BigDecimal(dto.getReturnQuantity()).subtract(new BigDecimal(dto.getRealReturnQuantity())).toString());
                odto.setStallName(dto.getStallName());
                list.add(odto);
            }
        }
        // 文件名
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmss");
        String filename = "退货差异报表导出"+"_"+ sdf.format(new Date());
        try {
            ExcelUtil.setFileNameAndHead(httpServletResponse, filename);
            EasyExcel.write(httpServletResponse.getOutputStream(),SaleReturnReportODTO.class).autoCloseStream(Boolean.TRUE).
                    sheet("退货差异报表").doWrite(list);;
        } catch (Exception e) {
            log.error("退货差异报表导出出错",e);
            ExcelUtil.setExceptionResponse(httpServletResponse);
        }

    }



    @ApiOperation(value = "代销退货明细", notes = "代销退货明细")
    @PostMapping("/consignmentReturnOrderReport")
    @MethodRender
    public PageInfo<ConsignmentReturnOrderPageODTO> consignmentReturnOrderReport(@RequestBody ConsignmentReturnOrderQueryIDTO vo) {
        return orderReportService.consignmentReturnOrderReport(vo);
    }

    @ApiOperation(value = "导出代销退货明细", notes = "导出代销退货明细")
    @GetMapping("exportConsignmentReturnOrderReport")
    public void exportConsignmentReturnOrderReport(ConsignmentReturnOrderQueryIDTO idto, HttpServletResponse httpServletResponse){
        idto.setPageNo(1);
        idto.setPageSize(Integer.MAX_VALUE);
        PageInfo<ConsignmentReturnOrderPageODTO> pageInfo = orderReportService.consignmentReturnOrderReport(idto);
        List<ConsignmentReturnOrderPageODTO> list = pageInfo.getList();
        if(!SpringUtil.isEmpty(list)){
            renderService.render(list,"/exportConsignmentReturnOrderReport");
        }
        try {
            ExcelUtil.setFileNameAndHead(httpServletResponse,"代销退货明细" + DateUtil.getDateFormate(new Date(), "yyyyMMddHHmmss"));
            EasyExcel.write(httpServletResponse.getOutputStream(),ConsignmentReturnOrderPageODTO.class)
                    .autoCloseStream(Boolean.TRUE).sheet("代销退货明细").doWrite(list);
        } catch (Exception e) {
            e.printStackTrace();
        }

    }


    /**
     * 门店加货申请单
     * @param idto
     * @return
     */
    @PostMapping("listShopAutoOrder")
    public PageInfo<AutoPreOrderODTO> List(@RequestBody AutoPreOrderIDTO idto) {
        return orderReportService.listShopAutoOrder(idto);
    }

    /**
     * 导出门店加货申请单
     *
     * @param vo
     * @return
     */
    @ApiOperation(value = "导出门店加货申请单")
    @PostMapping("/exportListShopAutoOrder")
    public List<AutoPreOrderODTO> exportList(@RequestBody AutoPreOrderIDTO vo) {
        vo.initExportPage();
        PageInfo<AutoPreOrderODTO> pageDate = orderReportService.listShopAutoOrder(vo);
        if (pageDate.getSize() > 0) {
            return pageDate.getList();
        }
        return new ArrayList<>();
    }


    @ApiOperation(value = "日日鲜充值扣款汇总表", notes = "日日鲜充值扣款汇总表")
    @PostMapping(value = "/xsjmSummaryReport")
    public XsjmOrderBillSummaryReportRspVO xsjmSummaryReport(@RequestBody XsjmOrderBillSummaryReportReqVO reqVO){
        return orderReportService.xsjmSummaryReport(reqVO);
    }
}

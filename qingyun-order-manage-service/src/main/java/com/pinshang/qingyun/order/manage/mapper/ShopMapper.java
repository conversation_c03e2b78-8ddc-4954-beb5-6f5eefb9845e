package com.pinshang.qingyun.order.manage.mapper;


import com.pinshang.qingyun.base.mybatis.MyMapper;
import com.pinshang.qingyun.order.manage.dto.ShopODTO;
import com.pinshang.qingyun.order.manage.mapper.entry.StoreEntry;
import com.pinshang.qingyun.order.manage.model.Shop;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface ShopMapper extends MyMapper<Shop> {

    List<ShopODTO> queryShopByStoreIdList(@Param("lineGroupIdList") List<Long> lineGroupIdList, @Param("storeIdList") List<Long> storeIdList);

    List<StoreEntry> selectStoreList(@Param("storeIdList") List<Long> storeIdList);

    List<Long> queryShopIdListByParam(@Param("shopTypeList") List<Integer> shopTypeList, @Param("managementModeList") List<Integer> managementModeList);


}
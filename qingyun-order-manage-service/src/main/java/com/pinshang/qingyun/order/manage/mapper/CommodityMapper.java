package com.pinshang.qingyun.order.manage.mapper;


import com.pinshang.qingyun.base.mybatis.MyMapper;
import com.pinshang.qingyun.order.manage.dto.CommodityBasicODTO;
import com.pinshang.qingyun.order.manage.dto.CommodityODTO;
import com.pinshang.qingyun.order.manage.dto.XDCommodityODTO;
import com.pinshang.qingyun.order.manage.model.Commodity;
import com.pinshang.qingyun.order.manage.vo.CommodityListRequestVO;
import com.pinshang.qingyun.order.manage.vo.CommodityVO;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface CommodityMapper extends MyMapper<Commodity> {

	List<CommodityODTO> queryCommodityByIdList(@Param("commodityIdList") List<Long> commodityIdList);

	List<XDCommodityODTO> findCommodityInfoByCodes(@Param("commodityCodes") List<String> commodityCodes);

	XDCommodityODTO getXDCommodityODTOById(@Param("commodityId")Long commodityId);

	List<Commodity> findCommodityBarCodeByParam(@Param("commodityIdList") List<Long> commodityIdList,@Param("barCode") String barCode);

	List<CommodityBasicODTO> findCommodityBasicListByParam(CommodityVO vo);

	List<Long> getCommodityFreezeGroup();

	List<Long> findShopCommodityPurchaseList(CommodityListRequestVO vo);

	List<String> getEndTimeList(@Param("nowHour") String nowHour);

	List<CommodityODTO> querySubCommodityByIdList(@Param("commodityIdList") List<Long> commodityIdList);

	List<CommodityODTO> queryCommodityByCodes(@Param("commodityCodes") List<String> commodityCodes);

}
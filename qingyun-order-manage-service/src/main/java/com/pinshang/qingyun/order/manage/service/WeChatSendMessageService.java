package com.pinshang.qingyun.order.manage.service;

import com.pinshang.qingyun.base.enums.SmsMessageTypeEnums;
import com.pinshang.qingyun.common.dto.SmsMessageClientVo;
import com.pinshang.qingyun.common.service.WeChatSendMessageClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

/**
 * @Author: sk
 * @Date: 2022/3/3
 */
@Service
public class WeChatSendMessageService {

    @Autowired
    private WeChatSendMessageClient weChatSendMessageClient;

    /**
     * 发送微信消息
     * @param content
     */
    @Async
    public void sendWeChatMessage(String content){
        //发送微信模板信息
        SmsMessageClientVo vo = new SmsMessageClientVo();
        vo.setContent(content);
        vo.setMessageTypeCode(SmsMessageTypeEnums.REPORT_INFO_WARN.getCode());
        weChatSendMessageClient.xdMessageWeiXinWarning(vo);
    }
}

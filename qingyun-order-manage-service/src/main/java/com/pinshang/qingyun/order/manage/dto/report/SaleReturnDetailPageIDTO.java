package com.pinshang.qingyun.order.manage.dto.report;

import com.pinshang.qingyun.base.page.Pagination;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @ClassName SaleReturnDetailPageIDTO
 * <AUTHOR>
 * @Date 2022/8/23 19:19
 * @Description SaleReturnDetailPageIDTO
 * @Version 1.0
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class SaleReturnDetailPageIDTO extends Pagination {
    @ApiModelProperty("退货日期-开始")
    private String beginTime;
    @ApiModelProperty("退货日期-结束")
    private String endTime;

    @ApiModelProperty("门店id")
    private Long shopId;

    @ApiModelProperty(value = "一级分类ID")
    private Long cateId1;

    @ApiModelProperty(value = "二级分类ID")
    private Long cateId2;

    @ApiModelProperty(value = "三级分类ID")
    private Long cateId3;

    @ApiModelProperty("责任方  1=配送，2=大仓，3=门店  ")
    private Integer rpType;

    @ApiModelProperty("退货单号")
    private String returnCode;

    @ApiModelProperty("退货原因下拉框:1=质量问题、2=已过保质期、3=包装破损、4=条码不符、5=其他")
    private Integer returnReason;

    @ApiModelProperty("退库类型 1-退库、2-少货")
    private Integer returnType;

    @ApiModelProperty("商品id")
    private Long commodityId;

    @ApiModelProperty("代销商id")
    private Long consignmentId;

    private Long stallId;

    private List<Long> stallIdList;
}

package com.pinshang.qingyun.order.manage.dto.settlement;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.pinshang.qingyun.base.configure.codec.DecimalSerializerKeep2;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * Created by hhf on 2018/6/12.
 * 结算明细报表
 */
@Data
public class SettlementDetailsReportODTO {

    /**门店id**/
    @ApiModelProperty(position = 1, value = "门店id")
    @ExcelIgnore
    private Long shopId;

    /**门店名称**/
    @ApiModelProperty(position = 2, value = "门店名称")
    @ExcelProperty("门店")
    private String shopName;

    /**送货日期**/
    @ApiModelProperty(position = 3, value = "送货日期")
    @JsonFormat(pattern = "yyyy-MM-dd",timezone = "GMT+8")
    @ExcelIgnore
    private Date orderTime;

    @ExcelProperty("送货日期")
    private String orderTimeStr;

    /**结算日期**/
    @ApiModelProperty(position = 4, value = "结算日期")
    @JsonFormat(pattern = "yyyy-MM-dd",timezone = "GMT+8")
    @ExcelIgnore
    private Date settleTime;

    @ExcelProperty("结算日期")
    private String settleTimeStr;

    @ExcelProperty("订单来源")
    /**订单来源-名称**/
    private String orderTypeName;

    /**订单编号**/
    @ApiModelProperty(position = 5, value = "订单编号")
    @ExcelProperty("订单编号")
    private String subOrderCode;

    /**预订单编号**/
    @ApiModelProperty(position = 6, value = "预订单编号")
    @ExcelProperty("预订单编号")
    private String preOrderCode;

    /**分类**/
    @ApiModelProperty(position = 7, value = "分类")
    @ExcelProperty("分类")
    private String categoryName;

    /**条形码**/
    @ApiModelProperty(position = 8, value = "条形码")
    @ExcelProperty("条形码")
    private String barCode;
    //private String barCodes;
    @ApiModelProperty(position = 9, value = "主副条码集合")
    @ExcelIgnore
    private List<String> barCodeList;

    /**商品编码**/
    @ApiModelProperty(position = 10, value = "商品编码")
    @ExcelProperty("商品编码")
    private String commodityCode;

    /**商品名称**/
    @ApiModelProperty(position = 11, value = "商品名称")
    @ExcelProperty("商品名称")
    private String commodityName;

    /**规格**/
    @ApiModelProperty(position = 12, value = "规格")
    @ExcelProperty("规格")
    private String commoditySpec;

    /**计量单位**/
    @ApiModelProperty(position = 13, value = "计量单位")
    @ExcelProperty("计量单位")
    private String commodityUnitName;


    /**订货价**/
    @ApiModelProperty(position = 14, value = "订货价")
    @JsonSerialize(using = DecimalSerializerKeep2.class)
    @ExcelProperty("订货价")
    private BigDecimal price;

    /**订货数量**/
    @ApiModelProperty(position = 15, value = "订货数量")
    @ExcelProperty("订货数量")
    private String quantity;

    /**实发数量**/
    @ApiModelProperty(position = 16, value = "实发数量")
    @ExcelProperty("实发数量")
    private String realDeliveryQuantity;

    /**结算金额**/
    @ApiModelProperty(position = 17, value = "结算金额")
    @JsonSerialize(using = DecimalSerializerKeep2.class)
    @ExcelProperty("结算金额")
    private BigDecimal settlePrice;


    /**档口名称**/
    @ApiModelProperty(position = 18, value = "档口名称")
    @JsonSerialize(using = DecimalSerializerKeep2.class)
    @ExcelProperty("档口")
    private String stallName;


}

package com.pinshang.qingyun.order.manage.dto.commodity;/**
 * @Author: sk
 * @Date: 2025/7/16
 */

import com.pinshang.qingyun.page.Pagination;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2025年07月16日 下午1:57
 */
@Data
public class PlanCommodityQueryIDTO extends Pagination {

    @ApiModelProperty("商品id")
    private Long commodityId;

    @ApiModelProperty("大类id")
    private Long cateId1;

    @ApiModelProperty("中类id")
    private Long cateId2;

    @ApiModelProperty("小类id")
    private Long cateId3;

    @ApiModelProperty("同步状态 1=已同步  0=未同步(同步失败)")
    private Integer syncStatus;
}

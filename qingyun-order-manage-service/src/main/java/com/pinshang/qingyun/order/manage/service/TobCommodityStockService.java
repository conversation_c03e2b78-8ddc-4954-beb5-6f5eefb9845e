package com.pinshang.qingyun.order.manage.service;

import com.pinshang.qingyun.base.configure.expand.QYAssert;
import com.pinshang.qingyun.base.enums.BusinessTypeEnums;
import com.pinshang.qingyun.base.enums.YesOrNoEnums;
import com.pinshang.qingyun.base.enums.commodity.CommodityProductTypeEnum;
import com.pinshang.qingyun.base.enums.storage.StockTypeEnum;
import com.pinshang.qingyun.box.utils.BeanCloneUtils;
import com.pinshang.qingyun.order.dto.tob.TobCommodityStockIDTO;
import com.pinshang.qingyun.order.manage.dto.CommodityODTO;
import com.pinshang.qingyun.order.manage.dto.XDCommodityODTO;
import com.pinshang.qingyun.order.manage.mapper.CommodityItemMapper;
import com.pinshang.qingyun.order.manage.mapper.CommodityMapper;
import com.pinshang.qingyun.order.manage.mapper.TobCommodityStockMapper;
import com.pinshang.qingyun.order.manage.mapper.entry.CommodityItemEntry;
import com.pinshang.qingyun.order.manage.model.TobCommodityStock;
import com.pinshang.qingyun.order.manage.vo.TobCommodityStockKafkaVo;
import com.pinshang.qingyun.storage.dto.tob.CommodityInventoryODTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.entity.Example;

import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * @Author: sk
 * @Date: 2024/6/24
 */
@Slf4j
@Service
public class TobCommodityStockService {


    @Autowired
    private TobCommodityStockMapper tobCommodityStockMapper;

    @Autowired
    private BStockService bStockService;

    @Autowired
    private CommodityMapper commodityMapper;

    @Autowired
    private CommodityItemMapper commodityItemMapper;

    @Autowired
    private RedissonClient redissonClient;

    @Transactional(rollbackFor = Exception.class)
    public void updateTobCommodityStock(TobCommodityStockKafkaVo vo, Long commodityId) {
        XDCommodityODTO xdCommodityODTOById = commodityMapper.getXDCommodityODTOById(commodityId);
        Integer productType = xdCommodityODTOById.getProductType();
        if(!CommodityProductTypeEnum.GROUP.getCode().equals(productType)){
            //非组合品（子品）的处理
            processKafkaCommodityStock(vo,commodityId);
        }else{
            //组合品的处理
            insertOrIgnoreUpdateTobCommodityStock(vo, commodityId);
        }

    }

    /**
     *
     * @param vo kafka消息转换vo
     * @param commodityId 发来消息的商品id
     */
    private void processKafkaCommodityStock(TobCommodityStockKafkaVo vo, Long commodityId) {

        insertOrUpdateTobCommodityStock(vo, commodityId);

        //查看子品下面是否存在父组合，并循环处理
        try {
            processCommodityItemMasterStock(vo,commodityId);
        }catch (Exception e){
            log.warn("处理组合品库存时发生异常，商品id为：{}",commodityId,e);
        }
    }


    private void insertOrIgnoreUpdateTobCommodityStock(TobCommodityStockKafkaVo vo, Long commodityId) {
        //组合品下面有子品没有库存
        //非组合品的处理
        Example example = new Example(TobCommodityStock.class);
        example.createCriteria().andEqualTo("commodityId", commodityId);
        TobCommodityStock tobCommodityStock = tobCommodityStockMapper.selectOneByExample(example);

        if (tobCommodityStock == null) {
            // 新增
            TobCommodityStock insert = BeanCloneUtils.copyTo(vo, TobCommodityStock.class);
            insert.setCreateId(-1L);
            insert.setCreateTime(new Date());
            insert.setUpdateId(-1L);
            insert.setUpdateTime(new Date());

            // 限量的鲜达和通达的库存一致
            if(StockTypeEnum.LIMIT.getCode().equals(vo.getStockType())){
                insert.setTdaStockStatus(vo.getStockStatus());
            }else {
                if(BusinessTypeEnums.TD_SALE.getCode() == vo.getType()){
                    insert.setTdaStockStatus(vo.getStockStatus());

                    // 通达的没有库存，鲜达的一定没有库存(通达库存 = 鲜达库存 +通达共享库存)
                    if(YesOrNoEnums.NO.getCode().equals(vo.getStockStatus())){
                        insert.setStockStatus(YesOrNoEnums.NO.getCode());
                    }
                }else {
                    insert.setTdaStockStatus(YesOrNoEnums.YES.getCode().equals(vo.getStockStatus()) ? YesOrNoEnums.YES.getCode() : YesOrNoEnums.NO.getCode());
                }
            }

            insert.setTdaStockStatus(insert.getTdaStockStatus() != null ? insert.getTdaStockStatus() : insert.getStockStatus());
            tobCommodityStockMapper.insert(insert);
        }
    }

    private void insertOrUpdateTobCommodityStock(TobCommodityStockKafkaVo vo, Long commodityId) {
        //组合品下面有子品没有库存
        //非组合品的处理
        Example example = new Example(TobCommodityStock.class);
        example.createCriteria().andEqualTo("commodityId", commodityId);
        TobCommodityStock tobCommodityStock = tobCommodityStockMapper.selectOneByExample(example);

        if (tobCommodityStock != null) {
            Integer dbStockStatus = (BusinessTypeEnums.TD_SALE.getCode().equals(vo.getType()) ? tobCommodityStock.getTdaStockStatus() : tobCommodityStock.getStockStatus());

            // 如果当前消息的库存和数据库里面一致就不更新了
            if(dbStockStatus.equals(vo.getStockStatus())){
                return;
            }

            TobCommodityStock update = BeanCloneUtils.copyTo(vo, TobCommodityStock.class);
            update.setUpdateTime(new Date());
            update.setUpdateId(-1L);

            // 限量的鲜达和通达的库存一致
            if(StockTypeEnum.LIMIT.getCode().equals(vo.getStockType())){
                update.setTdaStockStatus(vo.getStockStatus());
            }else {
                if(BusinessTypeEnums.TD_SALE.getCode().equals(vo.getType())){
                    update.setTdaStockStatus(vo.getStockStatus());

                    // 通达的没有库存，鲜达的一定没有库存(通达库存 = 鲜达库存 +通达共享库存)
                    if(YesOrNoEnums.NO.getCode().equals(vo.getStockStatus())){
                        update.setStockStatus(YesOrNoEnums.NO.getCode());
                    }else {
                        update.setStockStatus(null);
                    }
                }
            }

            // 兼容没有通达库存的情况
            if(tobCommodityStock.getTdaStockStatus() == null && update.getTdaStockStatus() == null){
                update.setTdaStockStatus(update.getStockStatus());
            }
            int updateCount = tobCommodityStockMapper.updateByExampleSelective(update, example);
            QYAssert.isTrue(updateCount == 1, "修改失败。");

        } else {
            // 新增
            TobCommodityStock insert = BeanCloneUtils.copyTo(vo, TobCommodityStock.class);
            insert.setCreateId(-1L);
            insert.setCreateTime(new Date());
            insert.setUpdateId(-1L);
            insert.setUpdateTime(new Date());

            // 限量的鲜达和通达的库存一致
            if(StockTypeEnum.LIMIT.getCode().equals(vo.getStockType())){
                insert.setTdaStockStatus(vo.getStockStatus());
            }else {
                if(BusinessTypeEnums.TD_SALE.getCode().equals(vo.getType()) ){
                    insert.setTdaStockStatus(vo.getStockStatus());

                    // 通达的没有库存，鲜达的一定没有库存(通达库存 = 鲜达库存 +通达共享库存)
                    if(YesOrNoEnums.NO.getCode().equals(vo.getStockStatus())){
                        insert.setStockStatus(YesOrNoEnums.NO.getCode());
                    }
                }else {
                    insert.setTdaStockStatus(YesOrNoEnums.YES.getCode().equals(vo.getStockStatus()) ? YesOrNoEnums.YES.getCode() : YesOrNoEnums.NO.getCode());
                }
            }

            insert.setTdaStockStatus(insert.getTdaStockStatus() != null ? insert.getTdaStockStatus() : insert.getStockStatus());
            tobCommodityStockMapper.insert(insert);
        }
    }

    private void processCommodityItemMasterStock(TobCommodityStockKafkaVo vo,
                                                 Long commodityId) {
        //查出包含子品的所有组合品id
        List<CommodityItemEntry> masterCommodityList = commodityItemMapper.findCommodityMasterListByCommodityItemId(commodityId);
        if(CollectionUtils.isNotEmpty(masterCommodityList)){
            //依据子品有无库存更新组合品
            masterCommodityList.forEach(
                    masterCommodity->{
                        TobCommodityStockKafkaVo tmp = null;
                        Map<Long, BigDecimal> orderQuantityMap  = new HashMap<>();
                        Long masterCommodityId = masterCommodity.getCommodityId();
                        orderQuantityMap.put(masterCommodityId,new BigDecimal(1));
                        Map<Long, CommodityInventoryODTO> longCommodityInventoryODTOMap =
                                bStockService.getbStockMap(null, orderQuantityMap);

                        CommodityInventoryODTO commodityInventoryODTO = longCommodityInventoryODTOMap.get(masterCommodityId);
                        tmp = new TobCommodityStockKafkaVo();
                        tmp.setCommodityId(masterCommodityId);
                        tmp.setWarehouseId(commodityInventoryODTO.getWarehouseId());
                        tmp.setStockType(commodityInventoryODTO.getStockType());
                        tmp.setEffectType(commodityInventoryODTO.getEffectType());
                        tmp.setStockStatus(vo.getStockStatus());
                        tmp.setType(vo.getType());
                        insertOrUpdateTobCommodityStock(tmp,masterCommodity.getCommodityId());
                    }
            );
        }
    }


    /**
     * 鲜达APP、批发APP上架、一键上架判断库存依据是否存在（返回存在库存依据的商品idList）
     * @param commodityIdList
     * @return
     */
    public List<Long> queryExistToBCommodityStockIds(List<Long> commodityIdList) {
        List<Long> existCommodityIdList = new ArrayList<>();
        QYAssert.isTrue(CollectionUtils.isNotEmpty(commodityIdList), "commodityIdList不能为空");
        List<Long> commodityIdListReq = new ArrayList<>(commodityIdList);

        // 查找商品信息
        List<CommodityODTO> commodityODTOList = commodityMapper.queryCommodityByIdList(commodityIdList);
        Map<Long, String> commodityMap = commodityODTOList.stream().collect(Collectors.toMap(CommodityODTO::getCommodityId, CommodityODTO::getCommodityCode));
        List<CommodityODTO> combList = commodityODTOList.stream().filter(commodityODTO -> CommodityProductTypeEnum.GROUP.getCode().equals(commodityODTO.getProductType())).collect(Collectors.toList());

        // combMap key:主品商品id   value:子品商品idList
        Map<Long, List<Long>> combMap = new HashMap<>(combList.size());
        // 如果当前商品存在组合主品
        if(CollectionUtils.isNotEmpty(combList)) {
            List<Long> combCommIdList = combList.stream().map(CommodityODTO::getCommodityId).collect(Collectors.toList());

            // 查找主商品下面的所有子商品
            List<CommodityODTO> subCommList = commodityMapper.querySubCommodityByIdList(combCommIdList);
            if(CollectionUtils.isNotEmpty(subCommList)) {
                List<Long> subCommIdList = subCommList.stream().map(CommodityODTO::getCommodityItemId).collect(Collectors.toList());

                commodityIdList.addAll(subCommIdList);

                combMap = subCommList.stream().collect(Collectors.groupingBy(CommodityODTO::getCommodityId, Collectors.mapping(CommodityODTO::getCommodityItemId, Collectors.toList())));
            }
        }

        // 查询库存依据
        Map<Long, Long> stockCommodityIdMap = queryToBCommodityStockMap(commodityIdList);

        Map<Long, Long> finalStockCommodityIdMap = stockCommodityIdMap;
        for(Long commodityId : commodityIdListReq) {

            // 主品存在库存依据
            Boolean masterContain = stockCommodityIdMap.containsKey(commodityId);

            // 子品都存在库存依据
            Boolean subContain = true;

            if(combMap.containsKey(commodityId)) {
                List<Long> subCommIdList = combMap.get(commodityId);
                subContain = subCommIdList.stream().allMatch(i->{
                    return finalStockCommodityIdMap.containsKey(i);
                });
            }

            if(masterContain && subContain) {
                existCommodityIdList.add(commodityId);
            }
        }
        return existCommodityIdList;
    }

    private Map<Long, Long> queryToBCommodityStockMap(List<Long> commodityIdList) {
        Map<Long, Long> stockCommodityIdMap = new HashMap<>(commodityIdList.size());
        List<Long> stockCommodityIdList = tobCommodityStockMapper.queryToBCommodityStock(commodityIdList);
        if(stockCommodityIdList != null) {
            stockCommodityIdMap = stockCommodityIdList.stream().collect(Collectors.toMap(Long::longValue, item -> item));
        }
        return stockCommodityIdMap;
    }
}

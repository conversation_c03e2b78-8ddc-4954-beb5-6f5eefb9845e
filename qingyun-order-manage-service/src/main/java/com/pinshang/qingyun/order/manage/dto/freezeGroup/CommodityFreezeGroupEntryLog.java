package com.pinshang.qingyun.order.manage.dto.freezeGroup;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @ClassName CommodityFreezeGroupEntryLog
 * <AUTHOR>
 * @Date 2022/9/30 13:51
 * @Description CommodityFreezeGroupEntryLog
 * @Version 1.0
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class CommodityFreezeGroupEntryLog{
    @ApiModelProperty("操作类型, 1-新增, 2-查看")
    private Integer operaType;

    @ApiModelProperty("操作人名称")
    private String operaUserName;

    @ApiModelProperty("操作时间")
    private String operaTime;

    @ApiModelProperty("id")
    private Long id;

    @ApiModelProperty("商品id")
    private Long commodityId;

    @ApiModelProperty("商品编码")
    private String commodityCode;

    @ApiModelProperty("商品名称")
    private String commodityName;

    @ApiModelProperty("规格")
    private String commoditySpec;

    @ApiModelProperty("条形码")
    private String barCode;

    @ApiModelProperty(value = "计量单位")
    private String unitName;

    @ApiModelProperty(value = "是否称重")
    private String isWeight;

    @ApiModelProperty("工作组")
    private String workShopName;

    @ApiModelProperty("工厂")
    private String factoryName;

}

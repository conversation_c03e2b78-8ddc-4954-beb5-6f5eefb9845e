package com.pinshang.qingyun.order.manage.service;

import com.pinshang.qingyun.base.enums.IogisticsModelEnums;
import com.pinshang.qingyun.base.enums.order.ProductTypeEnum;
import com.pinshang.qingyun.box.utils.BeanCloneUtils;
import com.pinshang.qingyun.box.utils.DateTimeUtil;
import com.pinshang.qingyun.box.utils.DateUtil;
import com.pinshang.qingyun.infrastructure.components.IMqSenderComponent;
import com.pinshang.qingyun.infrastructure.components.params.mq.MqMessage;
import com.pinshang.qingyun.kafka.base.KafkaTopicConstant;
import com.pinshang.qingyun.kafka.enums.KafkaMessageOperationTypeEnum;
import com.pinshang.qingyun.kafka.enums.KafkaMessageTypeEnum;
import com.pinshang.qingyun.order.dto.shop.MdShopOrderSettingODTO;
import com.pinshang.qingyun.order.dto.shop.MdShopOrderSettingQueryIDTO;
import com.pinshang.qingyun.order.manage.model.Shop;
import com.pinshang.qingyun.order.manage.vo.auto.BStockLackVO;
import com.pinshang.qingyun.order.manage.vo.auto.BStockShortResponseVO;
import com.pinshang.qingyun.order.service.MdShopOrderSettingClient;
import com.pinshang.qingyun.storage.dto.tob.CommodityInventoryDetailIDTO;
import com.pinshang.qingyun.storage.dto.tob.CommodityInventoryIDTO;
import com.pinshang.qingyun.storage.dto.tob.CommodityInventoryODTO;
import com.pinshang.qingyun.storage.service.tob.ToBClient;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.ArrayStack;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @Author: sk
 * @Date: 2024/12/4
 */

@Slf4j
@Service
public class BStockService {

    @Autowired
    private ToBClient toBClient;
    @Autowired
    private MdShopOrderSettingClient mdShopOrderSettingClient;
    @Value("${application.name.switch}")
    private String applicationNameSwitch;
    @Autowired
    private IMqSenderComponent mqSenderComponent;

    /**
     * 获取B端库存依据 map
     * @return
     */
    public Map<Long, CommodityInventoryODTO> getbStockMap(Date orderTime, Map<Long, BigDecimal> orderQuantityMap) {
        List<CommodityInventoryDetailIDTO> orderCommodityList = new ArrayList<>();
        orderQuantityMap.forEach((k,v)->{
            CommodityInventoryDetailIDTO commodityInventoryDetailIDTO = new CommodityInventoryDetailIDTO();
            commodityInventoryDetailIDTO.setCommodityId(k);
            commodityInventoryDetailIDTO.setQuantity(v);
            commodityInventoryDetailIDTO.setLevel(ProductTypeEnum.NORMAL.getCode());
            orderCommodityList.add(commodityInventoryDetailIDTO);
        });
        CommodityInventoryIDTO commodityInventoryIDTO = new CommodityInventoryIDTO();
        commodityInventoryIDTO.setOrderTime(orderTime != null ? orderTime : DateUtil.parseDate(DateTimeUtil.defaultDeliveryDate(), "yyyy-MM-dd"));
        commodityInventoryIDTO.setOrderCommodityList(orderCommodityList);
        List<CommodityInventoryODTO> toBStockList = toBClient.queryCommodityWithBomInventory(commodityInventoryIDTO);
        Map<Long, CommodityInventoryODTO> toBStockMap;
        if(CollectionUtils.isNotEmpty(toBStockList)){
            toBStockMap = toBStockList.stream().collect(Collectors.toMap(CommodityInventoryODTO::getCommodityId, Function.identity()));
        } else {
            toBStockMap = new HashMap<>();
        }
        return toBStockMap;
    }


    /**
     * 校验B端库存
     * @return
     */
    public List<BStockShortResponseVO> checkBStock(Shop shop, Integer orderType, Date orderTime, Map<Long, BigDecimal> orderQuantityMap,
                                                   String remark, Map<Long, CommodityInventoryODTO> toBStockMap, Long createId) {
        if(toBStockMap == null){
            toBStockMap = getbStockMap(orderTime, orderQuantityMap);
        }

        List<BStockShortResponseVO> responseVOList = new ArrayList<>();

        List<String> commodityIdList = new ArrayList();
        orderQuantityMap.forEach((key,value) -> {
            commodityIdList.add(key + "");
        });

        // 查询门店订货通用设置
        MdShopOrderSettingQueryIDTO queryIDTO = new MdShopOrderSettingQueryIDTO();
        queryIDTO.setShopType(shop.getShopType());
        queryIDTO.setCommodityIds(commodityIdList);
        List<MdShopOrderSettingODTO> settingEntries = mdShopOrderSettingClient.queryMdShopOrderSettingListByShopType(queryIDTO);
        if(CollectionUtils.isNotEmpty(settingEntries)){
            Map<Long, MdShopOrderSettingODTO> settingEntriesMap = settingEntries.stream().collect(Collectors.toMap(MdShopOrderSettingODTO::getCommodityIdLong, Function.identity()));

            // 排除直送的
            for(Map.Entry<Long, BigDecimal> entry : orderQuantityMap.entrySet()){
                Long commodityId = entry.getKey();
                MdShopOrderSettingODTO orderSettingEntry = settingEntriesMap.get(commodityId);
                // 只判断配送、直通
                if(orderSettingEntry != null && (orderSettingEntry.getLogisticsModel() == IogisticsModelEnums.DISPATCHING.getCode()
                        || orderSettingEntry.getLogisticsModel() == IogisticsModelEnums.DIRECT_CONNECTION.getCode())){

                    CommodityInventoryODTO inventoryODTO = toBStockMap.get(entry.getKey());
                    if(inventoryODTO != null && !inventoryODTO.getHaveInventory()){
                        BStockShortResponseVO responseVO = new BStockShortResponseVO();
                        responseVO.setCommodityId(entry.getKey() + "");
                        responseVO.setQuantity(entry.getValue());
                        responseVO.setInventoryQuantity(inventoryODTO.getInventoryQuantity());
                        responseVO.setStockType(inventoryODTO.getStockType());
                        responseVOList.add(responseVO);
                    }
                }
            }
        }

        if(CollectionUtils.isNotEmpty(responseVOList) && orderType != null){
            List<BStockLackVO> bStockLackList = getBStockLackList(shop.getStoreId(), orderType, remark, responseVOList, createId);
            //bStockShortService.stockShort(bStockLackList);
            commodityUnderStockSendKafkaMsg(bStockLackList);
        }
        return responseVOList;
    }

    private List<BStockLackVO> getBStockLackList(Long storeId, Integer orderType, String remark, List<BStockShortResponseVO> responseVOList,Long createId) {
        List<BStockLackVO> bStockLackList = new ArrayStack();
        for(BStockShortResponseVO responseVO : responseVOList){
            BStockLackVO lackVO = BeanCloneUtils.copyTo(responseVO, BStockLackVO.class);
            lackVO.setStoreId(storeId);
            lackVO.setCommodityId(Long.valueOf(responseVO.getCommodityId()));
            lackVO.setStockQuantity(responseVO.getInventoryQuantity());
            lackVO.setNeedQuantity(responseVO.getQuantity());
            lackVO.setOrderType(orderType);
            lackVO.setRemark(remark);
            lackVO.setCreateId(createId);
            bStockLackList.add(lackVO);
        }
        return bStockLackList;
    }

    /***
     * 大仓库存不足，记录库存消息的
     * @param list
     */
    private void commodityUnderStockSendKafkaMsg(List<BStockLackVO> list) {
        mqSenderComponent.send(applicationNameSwitch + KafkaTopicConstant.TDA_ORDER_SYNC_LOGISTICS_TOPIC,
                list,
                MqMessage.MQ_KAFKA,
                KafkaMessageTypeEnum.B_STOCK_LACK_TOPIC.name(),
                KafkaMessageOperationTypeEnum.UPDATE.name());
    }
}

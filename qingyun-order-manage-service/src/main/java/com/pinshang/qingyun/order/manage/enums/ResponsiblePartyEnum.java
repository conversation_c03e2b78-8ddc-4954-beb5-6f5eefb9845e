package com.pinshang.qingyun.order.manage.enums;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import com.pinshang.qingyun.base.enums.BaseEnum;

/**
 * @zll
 */
public enum ResponsiblePartyEnum implements BaseEnum {
    DELIVERY(1,"配送"),
    WAREHOUSE(2,"仓库"),
    SHOP(3,"门店"),
    WORKSHOP(4,"车间"),
    SUPPLY_CHAIN(5,"供应链"),
    CLOUD_WAREHOUSE(6,"云超仓"),
    ;
    private Integer code;
    private String desc;

    ResponsiblePartyEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    @JsonValue
    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    @Override
    public Integer convert() {
        return this.code;
    }

    @JsonCreator
    public static ResponsiblePartyEnum getEnumByCode(Integer code){
        if(code == null){
            return null;
        }
        for(ResponsiblePartyEnum e : ResponsiblePartyEnum.values()){
            if(e.getCode().intValue() == code){
                return e;
            }
        }
        return null;
    }
}

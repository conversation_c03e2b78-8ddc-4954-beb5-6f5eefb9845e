package com.pinshang.qingyun.order.manage.service;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.pinshang.qingyun.base.configure.expand.QYAssert;
import com.pinshang.qingyun.box.utils.DateUtil;
import com.pinshang.qingyun.box.utils.StringUtil;
import com.pinshang.qingyun.order.manage.dto.BStockShortPageIDTO;
import com.pinshang.qingyun.order.manage.dto.BStockShortPageODTO;
import com.pinshang.qingyun.order.manage.mapper.BStockShortMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2024/03/13
 * @Version 1.0
 */
@Slf4j
@Service
public class BStockShortService {

    @Autowired
    private BStockShortMapper bStockShortMapper;


    public PageInfo<BStockShortPageODTO> page(BStockShortPageIDTO idto){
        if(!StringUtil.isBlank(idto.getBeginTime()) && !StringUtil.isBlank(idto.getEndTime())){
            idto.setBeginTime(idto.getBeginTime()+ " 00:00:00");
            idto.setEndTime(idto.getEndTime()+ " 23:59:59");
            int diff = DateUtil.getDayDif(DateUtil.parseDate(idto.getEndTime(), DateUtil.DEFAULT_DATE_FORMAT), DateUtil.parseDate(idto.getBeginTime(), DateUtil.DEFAULT_DATE_FORMAT));
            QYAssert.isTrue(diff <= 7, "查询日期的跨度不能超过7天");
        }
        return PageHelper.startPage(idto.getPageNo(), idto.getPageSize()).doSelectPageInfo(() -> {
            bStockShortMapper.page(idto);
        });
    }
}

package com.pinshang.qingyun.order.manage.config;

import com.pinshang.qingyun.order.manage.constant.ThreadPoolBeanConstants;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import java.util.concurrent.Executor;
import java.util.concurrent.ThreadPoolExecutor;

/**
 * @Author: sk
 * @Date: 2024/12/4
 */
@Configuration
public class ThreadPoolConfig {

    /**
     * 自动订货线程池
     * @return
     */
    @Bean(name = ThreadPoolBeanConstants.AUTO_ORDER_THREADPOOL)
    public Executor autoOrderThreadPoolExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(4);
        executor.setMaxPoolSize(20);
        executor.setQueueCapacity(2048);
        executor.setThreadNamePrefix("auto-order-Executor-");
        executor.setKeepAliveSeconds(60);
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        executor.initialize();
        return executor;
    }
}

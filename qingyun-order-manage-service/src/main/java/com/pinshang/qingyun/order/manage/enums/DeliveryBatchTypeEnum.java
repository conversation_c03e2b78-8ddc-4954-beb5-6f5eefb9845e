package com.pinshang.qingyun.order.manage.enums;

/**
 * <AUTHOR>
 * 0-非批次配送, 1-1配，2-2配，3-3配，9-临时批次
 *
 */
public enum DeliveryBatchTypeEnum {
    NO_BATCH(0,"非批次配送"),
    ONE_BATCH(1,"1配"),
    TWO_BATCH(2,"2配"),
    THREE_BATCH(3,"3配"),
    NEW_SHOP_BATCH(8,"新开店"),
    TEMP_BATCH(9,"临时"),
    REPLENISHMENT(4,"补货");

    
    private Integer code;
    private String name;

    public Integer getCode() {
        return code;
    }

    DeliveryBatchTypeEnum(Integer code, String name) {
        this.code = code;
        this.name =name;
    }

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

    public static String getNameByCode(Integer code){
        if(code == null){
            return null;
        }
        for(DeliveryBatchTypeEnum e : DeliveryBatchTypeEnum.values()){
            if(e.getCode().intValue() == code){
                return e.getName();
            }
        }
        return null;
    }
}

package com.pinshang.qingyun.order.manage.vo.auto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 */
@Data
public class AutoCommodityVO {

    private Long commodityId;

    @ApiModelProperty("商品名称")
    private String commodityName;

    @ApiModelProperty("商品code")
    private String commodityCode;

    private String barCode;

    @ApiModelProperty("商品规格")
    private String commoditySpec;

    @ApiModelProperty("单位")
    private String commodityUnit;

    private Long commodityUnitId;

    private Integer logisticsModel;

    private BigDecimal commodityPackageSpec;

    @ApiModelProperty("安全库存")
    private BigDecimal stockQuantity;
}

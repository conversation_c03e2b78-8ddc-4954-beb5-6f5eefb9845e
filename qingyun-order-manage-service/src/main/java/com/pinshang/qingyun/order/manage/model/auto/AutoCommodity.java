package com.pinshang.qingyun.order.manage.model.auto;

import lombok.Data;

import javax.persistence.Entity;
import javax.persistence.Table;
import java.util.Date;

/**
 * @Author: liuZhen
 * @DateTime: 2022/5/10 18:12
 */
@Data
@Entity
@Table(name = "t_md_auto_commodity")
public class AutoCommodity {

    private Long id;
    /**
     * 商品ID
     */
    private Long commodityId;

    private Long createId;

    private Date createTime;

    private Long updateId;

    private Date updateTime;
}

package com.pinshang.qingyun.order.manage.mapper;

import com.pinshang.qingyun.base.mybatis.MyMapper;
import com.pinshang.qingyun.order.manage.mapper.entry.CommodityItemEntry;
import com.pinshang.qingyun.order.manage.model.CommodityItem;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * Created by hhf on 2019/9/3.
 */
@Mapper
@Repository
public interface CommodityItemMapper extends MyMapper<CommodityItem> {

    /**
     * 根据子商品id 查询主商品集合
     * @param commodityItemId
     * @return
     */
    List<CommodityItemEntry> findCommodityMasterListByCommodityItemId(@Param("commodityItemId")Long commodityItemId);

}

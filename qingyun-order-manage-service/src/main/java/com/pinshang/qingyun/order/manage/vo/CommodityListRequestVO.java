package com.pinshang.qingyun.order.manage.vo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class CommodityListRequestVO {

	private static final long serialVersionUID = 1L;

	private Long enterpriseId;
	
	private String storeId;
	private Long shopId;
	
	private String commodityKey;
	
	private Long categoryId;
	
	private Long supplerId;
	
	private boolean isPromotionProduct;//是否促销
	
	private String orderTime;

	private List<Long> commodityIdList;
	private List<String> commodityCodeList;

	private String barCode;//条形码
	private Long commodityId;//商品ID
	private boolean isWeight;//称重商品
	private boolean isNormal;//标品
	private boolean isNew;//新品

	private Integer logisticsModel;//物流模式

	private Long warehouseId;//仓库
	private List<Long> commodityIdList2;
	private List<Long> commodityIdList3;

	private boolean showZero;//是否展示数量为0的商品
	private List<Long> commodityIdListAll;

	private Long checkGroupId; //考核组id
	private List<Long> checkGroupCommodityIdList;

	private List<Long> commodityPurchaseIdList;

	private Boolean autoCommodity; // 是否自定订货

	/** app状态：0-上架，1-下架 */
	private Integer appStatus;

	private boolean freshCommodity; // 是否日日鲜商品

}

package com.pinshang.qingyun.order.manage.service;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.pinshang.qingyun.base.configure.expand.QYAssert;
import com.pinshang.qingyun.box.utils.ConcurrentDateUtil;
import com.pinshang.qingyun.box.utils.SpringUtil;
import com.pinshang.qingyun.order.manage.dto.rt.RtOutboundOrderIDTO;
import com.pinshang.qingyun.order.manage.dto.rt.RtOutboundOrderODTO;
import com.pinshang.qingyun.order.manage.mapper.RtOutboundOrderMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.text.DateFormat;
import java.text.DecimalFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2024/12/19 9:23
 */
@Service
public class RtOutboundOrderService {

    @Autowired
    private RtOutboundOrderMapper rtOutboundOrderMapper;

    public PageInfo<RtOutboundOrderODTO> selectRtOutboundOrderPageInfo(RtOutboundOrderIDTO rtOutboundOrderIDTO){
        QYAssert.isTrue(null != rtOutboundOrderIDTO, "参数异常");
        QYAssert.isTrue(null != rtOutboundOrderIDTO.getDeliveryTime(), "送货日期不能为空");
        QYAssert.isTrue(null != rtOutboundOrderIDTO.getStoreShortName(), "客户简称不能为空");
        PageInfo<RtOutboundOrderODTO> pageDate = PageHelper.startPage(rtOutboundOrderIDTO.getPageNo(), rtOutboundOrderIDTO.getPageSize()).doSelectPageInfo(() -> {
            rtOutboundOrderMapper.selectRtOutboundOrderList(rtOutboundOrderIDTO);
        });
        if(SpringUtil.isNotEmpty(pageDate.getList())){
            List<RtOutboundOrderODTO> list = pageDate.getList();
            try {
                DateFormat sdf = ConcurrentDateUtil.SDF_FULL_DATE_TIME.get();
                SimpleDateFormat dateFormat = new SimpleDateFormat("HH:mm:ss");
                String hms = dateFormat.format(new Date());

                Date parseDate = sdf.parse(rtOutboundOrderIDTO.getDeliveryTime() + " " + hms);
                Calendar begin=Calendar.getInstance();
                begin.setTime(parseDate);
                begin.add(Calendar.DATE,-1);
                System.out.println(begin.getTime());

                //获取所有的收货单位(去重)
                List<String> receivingUnitList = list.stream().map(RtOutboundOrderODTO::getReceivingUnit).distinct().collect(Collectors.toList());
                Collections.sort(receivingUnitList);
                Map<String,Date> map = new HashMap<>();

                //循环一个收货单位<->对应一个时间
                for(String receivingUnit : receivingUnitList){
                    begin.add(Calendar.MINUTE,1);
                    map.put(receivingUnit,begin.getTime());
                }
                for(RtOutboundOrderODTO entry : list) {
                    //处理时间
                    Date deliveryTime = map.get(entry.getReceivingUnit());
                    entry.setDeliveryTime(sdf.format(deliveryTime));
                    entry.setRegistrar("诸杰");
                    // 处理出库数量
                    BigDecimal rtCommodityNum = entry.getRtCommodityNum();
                    BigDecimal conversionRate = entry.getConversionRate();
                    if(null != rtCommodityNum && null != conversionRate){
                        BigDecimal multiply = rtCommodityNum.multiply(conversionRate);
                        DecimalFormat decimalFormat =new DecimalFormat("0.00");
                        entry.setDeliveryNumber(decimalFormat.format(multiply));
                    }
                }
            } catch (ParseException e) {
                e.printStackTrace();
            }

        }
        return pageDate;
    }

}

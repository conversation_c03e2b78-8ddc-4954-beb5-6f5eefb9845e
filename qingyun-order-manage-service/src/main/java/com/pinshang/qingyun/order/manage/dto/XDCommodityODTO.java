package com.pinshang.qingyun.order.manage.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * @Author: sk
 * @Date: 2019/12/2
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class XDCommodityODTO {
    //商品id
    private String commodityId;

    @ApiModelProperty("商品编码")
    private String commodityCode;

    @ApiModelProperty("商品名称")
    private String commodityName;

    @ApiModelProperty("规格型号")
    private String commoditySpec;

    @ApiModelProperty("单位")
    private String commodityUnit;

    @ApiModelProperty("是否新品标识")
    private String newProductFlag;

    @ApiModelProperty("包装规格")
    private BigDecimal commodityPackageSpec;

    @ApiModelProperty("箱规")
    private BigDecimal salesBoxCapacity;

    //物流模式
    private Integer logisticsModel;
    private Integer commodityStatus;
    private Integer frozen;//速冻
    private BigDecimal xdSalesBoxCapacity;//xd销售箱规
    /**
     * 商品类型：1-普通商品，2-组合商品
     */
    @ApiModelProperty(value ="商品类型",hidden = true)
    private Integer productType;

    /** 档口编码 */
    private String stallCode;
}

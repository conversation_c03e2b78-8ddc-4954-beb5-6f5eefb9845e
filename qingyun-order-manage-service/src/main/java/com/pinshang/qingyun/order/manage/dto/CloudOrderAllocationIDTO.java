package com.pinshang.qingyun.order.manage.dto;

import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * @Author: sk
 * @Date: 2022/8/31
 */
@Data
public class CloudOrderAllocationIDTO {

    /** 任务id */
    private Long taskId;

    private Long seedAreaId;

    private Date orderTime;

    private List<Long> lineGroupIdList;

    /** 配货客户idList */
    private List<Long> storeIdList;

    /** 配货时间开始 */
    private String beginTime;
    /** 配货时间结束 */
    private String endTime;

    /** 配货商品明细 */
    private List<CloudOrderAllocationItemIDTO> itemList;

    /** 是否自动： true:自动   false:手动 */
    private boolean ifAuto;

    /**
     * 配货任务创建人ID
     */
    private Long createId;
}

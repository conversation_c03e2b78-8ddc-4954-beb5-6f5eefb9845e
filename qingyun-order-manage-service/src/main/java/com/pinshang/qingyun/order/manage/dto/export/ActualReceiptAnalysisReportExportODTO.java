package com.pinshang.qingyun.order.manage.dto.export;/**
 * @Author: sk
 * @Date: 2025/7/10
 */

import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2025年07月10日 下午4:36
 */
@Data
public class ActualReceiptAnalysisReportExportODTO {

    @ExcelProperty("门店名称")
    private String shopName;

    @ExcelProperty("一级分类")
    private String cateName;

    @ExcelProperty("条形码")
    private String barCode;

    @ExcelProperty("商品编码")
    private String commodityCode;

    @ExcelProperty("商品名称")
    private String commodityName;

    @ExcelProperty("规格")
    private String commoditySpec;

    @ExcelProperty("订货数量")
    private String totalQuantity;

    @ExcelProperty("实发数量")
    private String totalRealDeliveryQuantity;

    @ExcelProperty("实收数量")
    private String totalRealReceiveQuantity;

    @ExcelProperty("实收差异数量")
    private String quantityDifference;

    @ExcelProperty("供货价")
    private String supplyPrice;

    @ExcelProperty("供货金额")
    private String totalSupplyPrice;

    @ExcelProperty("供应商")
    private String supplierName;

    @ExcelProperty("采购员")
    private String realName;
}

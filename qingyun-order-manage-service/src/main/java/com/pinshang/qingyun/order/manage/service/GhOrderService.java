package com.pinshang.qingyun.order.manage.service;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.pinshang.qingyun.base.configure.expand.QYAssert;
import com.pinshang.qingyun.box.utils.ConcurrentDateUtil;
import com.pinshang.qingyun.box.utils.SpringUtil;
import com.pinshang.qingyun.order.manage.dto.gh.GhOrderIDTO;
import com.pinshang.qingyun.order.manage.dto.gh.GhOrderODTO;
import com.pinshang.qingyun.order.manage.mapper.GhOrderMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.text.DateFormat;
import java.text.ParseException;
import java.util.Calendar;
import java.util.Date;

/**
 * <AUTHOR>
 * @Date 2024/12/18 16:41
 */
@Service
public class GhOrderService {

    @Autowired
    private GhOrderMapper ghOrderMapper;
    public PageInfo<GhOrderODTO> findGhOrderPageInfo(GhOrderIDTO ghOrderIDTO){
        QYAssert.isTrue(null != ghOrderIDTO, "参数异常");
        PageInfo<GhOrderODTO> pageDate = PageHelper.startPage(ghOrderIDTO.getPageNo(), ghOrderIDTO.getPageSize()).doSelectPageInfo(() -> {
            ghOrderMapper.findGhOrderPageInfo(ghOrderIDTO);
        });
        DateFormat dateFormat = ConcurrentDateUtil.SDF_FULL_DATE_WITHOUT_UNDERLINE.get();

        DateFormat dateFormat1 = ConcurrentDateUtil.SDF_FULL_DATE.get();

        if(SpringUtil.isNotEmpty(pageDate.getList())){
            for(GhOrderODTO entry :pageDate.getList()){
                try {
                    Date date= dateFormat1.parse(entry.getOrderTime());
                    Calendar calendar = Calendar.getInstance();
                    calendar.setTime(date);
                    calendar.add(Calendar.DAY_OF_MONTH, -1);
                    date = calendar.getTime();
                    entry.setManufactureDate(dateFormat1.format(date));
                    entry.setSupplierBatch(dateFormat.format(date));
                } catch (ParseException e) {
                    throw new RuntimeException(e);
                }
            }
        }
        return pageDate;
    }
}

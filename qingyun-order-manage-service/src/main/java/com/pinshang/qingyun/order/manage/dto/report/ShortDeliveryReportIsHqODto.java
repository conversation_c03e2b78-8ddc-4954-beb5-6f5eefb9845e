package com.pinshang.qingyun.order.manage.dto.report;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.pinshang.qingyun.base.enums.shop.ShopTypeEnums;
import com.pinshang.qingyun.box.utils.DateUtil;
import com.pinshang.qingyun.renderer.annotation.FieldRender;
import com.pinshang.qingyun.renderer.constant.RenderFieldHelper;
import com.pinshang.qingyun.renderer.enums.FieldTypeEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.Date;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class ShortDeliveryReportIsHqODto {
	@ApiModelProperty("门店类型")
	@ExcelIgnore
	private Integer shopType;

	@ExcelProperty("门店类型")
	private String shopTypeName;

	@ExcelIgnore
	private Long storeId;

	@ApiModelProperty("门店名称")
	@ExcelProperty("门店")
	private String shopName;

	@JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
	@ExcelIgnore
	private Date deliveryDate;

	@ExcelProperty("送货日期")
	private String deliveryDateStr;

	@ExcelProperty("订单号")
	private String orderCode;

	@ExcelIgnore
	private String shopCode;

	@ExcelProperty("大类")
	@FieldRender(fieldType = FieldTypeEnum.COMMODITY_KIND,fieldName = RenderFieldHelper.CommodityKind.commodityFirstKindName,keyName = "commodityId")
	private String categoryName;

	@ExcelProperty("中类")
	@FieldRender(fieldType = FieldTypeEnum.COMMODITY_KIND,fieldName = RenderFieldHelper.CommodityKind.commoditySecondKindName,keyName = "commodityId")
	private String secondCategoryName;

	@ApiModelProperty("小类")
	@ExcelProperty("小类")
	@FieldRender(fieldType = FieldTypeEnum.COMMODITY_KIND,fieldName = RenderFieldHelper.CommodityKind.commodityThirdKindName,keyName = "commodityId")
	private String thirdCategoryName;

	@ExcelProperty("条形码")
	private String barCode;

	@ExcelIgnore
	private String barCodes;	// 子码列表

	@ExcelIgnore
	private String commodityId;

	@ExcelProperty("商品编码")
	private String commodityCode;

	@ExcelProperty("商品名称")
	private String commodityName;

	@ExcelProperty("规格")
	private String commoditySpec;

	@ExcelProperty("计量单位")
	@FieldRender(fieldType = FieldTypeEnum.DICTIONARY,fieldName = RenderFieldHelper.Dictionary.optionName,keyName = "commodityUnitId")
	private String commodityUnitName;

	@ExcelIgnore
	private String commodityUnitId; // 单位id

	@ExcelProperty("订货价")
	private BigDecimal price;

	@ExcelProperty("订货数量")
	private BigDecimal orderNum;

	@ExcelProperty("发货数量")
	private BigDecimal deliveryNum;

	@ExcelProperty("差异数量")
	private BigDecimal differNum;

	@ExcelProperty("短交比例")
	private String rate;

	@ExcelProperty("工厂")
	private String factoryName;

	@ExcelProperty("生产组")
	private String workshopName;

	@ExcelProperty("客户编号")
	private String storeCode;

	@ExcelProperty("下单人")
	@FieldRender(fieldType = FieldTypeEnum.USER,fieldName = RenderFieldHelper.User.realName,keyName = "createId")
	private String createName;

	@ApiModelProperty("线路组")
	@ExcelProperty("线路组")
	private String storeLineGroupName;

	@ExcelProperty("发货仓库")
	@ApiModelProperty("发货仓库")
	private String warehouseName;

	@ExcelIgnore
	private Long createId;

	@ExcelProperty("经营模式")
	private String managementModeName;

	@ExcelIgnore
	private Long stallId;

	@ExcelProperty("档口名称")
	@FieldRender(fieldType = FieldTypeEnum.STALL, fieldName = RenderFieldHelper.Stall.stallName, keyName = "stallId")
	private String stallName;


	public String getShopTypeName() {
		return ShopTypeEnums.getName(this.shopType);
	}

	public String getDeliveryDateStr() {
		return DateUtil.get4yMd(this.deliveryDate);
	}
}

package com.pinshang.qingyun.order.manage.model;

import com.pinshang.qingyun.base.po.BaseSimplePO;

import javax.persistence.Entity;
import javax.persistence.Table;
import java.math.BigDecimal;
import java.util.Date;

/**
 * @Author: sk
 * @Date: 2022/9/1
 */
@Entity
@Table(name="t_delivery_cargo_order_log")
public class CloudOrderAllocationLog extends BaseSimplePO {

    /** 任务id */
    private Long taskId;

    private Long shopId;
    private Long storeId;

    private Date orderTime;

    /** 商品id */
    private Long commodityId;

    /** 配货份数 */
    private Long allocationNumber;


    private Long commodityFirstId;
    private BigDecimal price;

    /** 包装规格 */
    private BigDecimal commodityPackageSpec;

    /** 当前门店商品或者大类金额 */
    private BigDecimal amount;
    /** 所有门店商品或者大类下的金额 */
    private BigDecimal totalAmount;

    /** 计算出的实际配货份数 */
    private Long realAllocationNumber;

    public BigDecimal getPrice() {
        return price;
    }

    public void setPrice(BigDecimal price) {
        this.price = price;
    }

    public Long getTaskId() {
        return taskId;
    }

    public void setTaskId(Long taskId) {
        this.taskId = taskId;
    }

    public Long getShopId() {
        return shopId;
    }

    public void setShopId(Long shopId) {
        this.shopId = shopId;
    }

    public Long getStoreId() {
        return storeId;
    }

    public void setStoreId(Long storeId) {
        this.storeId = storeId;
    }

    public Date getOrderTime() {
        return orderTime;
    }

    public void setOrderTime(Date orderTime) {
        this.orderTime = orderTime;
    }

    public Long getCommodityId() {
        return commodityId;
    }

    public void setCommodityId(Long commodityId) {
        this.commodityId = commodityId;
    }

    public Long getAllocationNumber() {
        return allocationNumber;
    }

    public void setAllocationNumber(Long allocationNumber) {
        this.allocationNumber = allocationNumber;
    }

    public Long getCommodityFirstId() {
        return commodityFirstId;
    }

    public void setCommodityFirstId(Long commodityFirstId) {
        this.commodityFirstId = commodityFirstId;
    }

    public BigDecimal getAmount() {
        return amount;
    }

    public void setAmount(BigDecimal amount) {
        this.amount = amount;
    }

    public BigDecimal getTotalAmount() {
        return totalAmount;
    }

    public void setTotalAmount(BigDecimal totalAmount) {
        this.totalAmount = totalAmount;
    }

    public Long getRealAllocationNumber() {
        return realAllocationNumber;
    }

    public void setRealAllocationNumber(Long realAllocationNumber) {
        this.realAllocationNumber = realAllocationNumber;
    }

    public BigDecimal getCommodityPackageSpec() {
        return commodityPackageSpec;
    }

    public void setCommodityPackageSpec(BigDecimal commodityPackageSpec) {
        this.commodityPackageSpec = commodityPackageSpec;
    }
}

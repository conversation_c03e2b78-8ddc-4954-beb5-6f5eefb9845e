package com.pinshang.qingyun.order.manage.mapper.entry;

import lombok.Data;

@Data
public class StoreEntry {
    //客户id
    private String scopeId;

    //客户编码
    private String storeCode;

    //客户名称
    private String storeName;

    //客户状态--状态\r\n(0启用,1停用,-1刚创建了客户,待指定线路,2待指定结账客户
    private String storeStatus;

    //门店名称
    private String shopName;
    private String shopCode;

    private String storeLineGroupName;
    private Integer shopType;
    private Long storeId;
    private Long shopId;

    private Integer businessType; // 业务类型：10-通达销售
}

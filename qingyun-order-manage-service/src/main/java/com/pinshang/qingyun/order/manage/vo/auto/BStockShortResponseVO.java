package com.pinshang.qingyun.order.manage.vo.auto;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * @Author: sk
 * @Date: 2024/3/5
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class BStockShortResponseVO {

    private String commodityId;

    private String commodityCode;

    private String commodityName;

    @ApiModelProperty("当前商品数量")
    private BigDecimal quantity;

    @ApiModelProperty("可用库存数量")
    private BigDecimal inventoryQuantity;

    private Long shoppingCartId;

    @ApiModelProperty("库存依据类型:1=依据大仓, 2=不限量订货,3=限量供应")
    private Integer stockType;

}

package com.pinshang.qingyun.order.manage.dto.export;/**
 * @Author: sk
 * @Date: 2025/7/10
 */

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2025年07月10日 下午2:13
 */
@Data
public class ReturnOrderDetailsExportODTO {

    @ExcelProperty("门店")
    private String shopName;

    @ExcelProperty("退货日期")
    private String createTime;

    @ExcelProperty("退货编号")
    private String orderCode;

    @ExcelProperty("商品分类")
    private String cateName;

    @ExcelProperty("条形码")
    private String barCode;

    @ExcelProperty("商品编码")
    private String commodityCode;

    @ExcelProperty("商品名称")
    private String commodityName;

    @ExcelProperty("规格")
    private String commoditySpec;

    @ExcelProperty("单位")
    private String commodityUnit;

    @ExcelProperty("单价")
    private String price;

    @ExcelProperty("退货数量")
    private String returnQuantity;

    @ExcelProperty("退货金额")
    private  String totalPrice;

    @ExcelProperty("退货原因")
    private String returnReason;

    @ExcelProperty("备注")
    private String remark;

    @ExcelProperty("档口")
    private String stallName;
}

package com.pinshang.qingyun.order.manage.enums;

import com.pinshang.qingyun.box.utils.EnumUtils;

import java.util.EnumSet;

/**
 * 门店自动订货日志
 * <AUTHOR>
 */
public enum AutoShopCommodityLogEnum {

	START_AUTO(1,"启用自动订货"),
	END_AUTO(2,"停用自动订货"),
	INSERT_COMMODITY(3,"导入添加商品"),
	UPDATE_COMMODITY(4,"导入修改安全库存"),
	DELETE_COMMODITY(5,"删除商品"),
	DELETE_DIRECT_COMMODITY(6, "删除直送商品"),
	DELETE_WEIGHT_COMMODITY(7, "删除称重冻品");

	private Integer code;
	private String remark;

	private AutoShopCommodityLogEnum(Integer code, String remark) {
		this.setCode(code);
		this.setRemark(remark);
	}

	public static AutoShopCommodityLogEnum fromName(Integer code) {
		return EnumUtils.fromEnumProperty(AutoShopCommodityLogEnum.class, "code", code);
	}

	public static EnumSet<AutoShopCommodityLogEnum> allList() {
		EnumSet<AutoShopCommodityLogEnum> adverPositionList = EnumSet.allOf(AutoShopCommodityLogEnum.class);
		return adverPositionList;
	}

	public Integer getCode() {
		return code;
	}

	public void setCode(Integer code) {
		this.code = code;
	}

	public String getRemark() {
		return remark;
	}

	public void setRemark(String remark) {
		this.remark = remark;
	}
	
}

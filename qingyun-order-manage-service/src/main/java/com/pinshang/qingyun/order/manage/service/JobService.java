package com.pinshang.qingyun.order.manage.service;

import com.pinshang.qingyun.box.utils.DateUtil;
import com.pinshang.qingyun.common.dto.XxlJobInfoODTO;
import com.pinshang.qingyun.common.dto.XxlJobQueryVoIDTO;
import com.pinshang.qingyun.common.dto.XxlJobVoIDTO;
import com.pinshang.qingyun.common.service.XxlJobClient;
import com.pinshang.qingyun.order.manage.mapper.CommodityMapper;
import com.pinshang.qingyun.order.manage.service.auto.ShopAutoOrderService;
import com.pinshang.qingyun.order.manage.util.OrderManageTimeUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * @Author: sk
 * @Date: 2024/12/4
 */

@Slf4j
@Service
public class JobService {
    @Autowired
    private ShopAutoOrderService shopAutoOrderService;
    @Autowired
    private XxlJobClient xxlJobClient;
    @Autowired
    private CommodityMapper commodityMapper;

    /**
     * 门店自动订货job创建
     * @return
     */
    public Boolean createShopAutoOrderJob() {

        String nowHour = DateUtil.getDateFormate(new Date(),"HH:mm");
        log.info("创建门店自动订货job 开始种植任务--------------------------------------------------------------" + new Date());
        List<String> orderTimeList = commodityMapper.getEndTimeList(nowHour);
        if(CollectionUtils.isEmpty(orderTimeList)){
            return Boolean.TRUE;
        }

        // 种植job时间提前30分钟
        List<String> minusTimeList = new ArrayList<>();
        for(String time : orderTimeList){
            minusTimeList.add(OrderManageTimeUtil.addMinute(time,-30));
        }

        XxlJobVoIDTO xxlJobVoIDTO = new XxlJobVoIDTO();
        xxlJobVoIDTO.setJobHandler("executeShopAutoOrderJobHandler");
        xxlJobVoIDTO.setJobDesc("创建门店自动订货job");
        xxlJobVoIDTO.setAuthor("苏坤");
        xxlJobVoIDTO.setRunTimes(minusTimeList);
        xxlJobClient.replaceJob(xxlJobVoIDTO);

        XxlJobQueryVoIDTO vo = new XxlJobQueryVoIDTO();
        vo.setExecutorHandler("executeShopAutoOrderJobHandler");
        List<XxlJobInfoODTO> jobList = xxlJobClient.queryJobList(vo);
        if(CollectionUtils.isNotEmpty(jobList)){
            for(XxlJobInfoODTO info : jobList){
                log.info("创建门店自动订货job jobGroup：" + info.getJobGroup() + " 描述：" + info.getJobDesc() + " jobHandler："+ info.getExecutorHandler() + " cron：" + info.getJobCron());
            }
        }
        return Boolean.TRUE;
    }

    /**
     * 门店自动订货job执行
     * @param orderTime
     * @return
     * @throws Throwable
     */
    public Boolean executeShopAutoOrderJob(String orderTime){
        log.info("门店自动订货job执行提交时间：" + orderTime);
        return shopAutoOrderService.createShopAutoOrder(orderTime);
    }
}

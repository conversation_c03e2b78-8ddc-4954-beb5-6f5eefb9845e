package com.pinshang.qingyun.order.manage.controller;

import com.pinshang.qingyun.order.manage.dto.CloudOrderAllocationIDTO;
import com.pinshang.qingyun.order.manage.service.CloudOrderAllocationAsyncService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * @Author: sk
 * @Date: 2022/8/31
 */

@RestController
@RequestMapping("/cloudOrderAllocation")
public class CloudOrderAllocationController {

    @Autowired
    private CloudOrderAllocationAsyncService cloudOrderAllocationAsyncService;


    /**
     * 云超配货单分配客户
     * @param idto
     * @return
     */
    @PostMapping("/allocationStores")
    public Boolean allocationStores(@RequestBody CloudOrderAllocationIDTO idto){
        return cloudOrderAllocationAsyncService.allocationStores(idto);
    }
}

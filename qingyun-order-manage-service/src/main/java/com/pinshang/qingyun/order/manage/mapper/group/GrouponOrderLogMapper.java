package com.pinshang.qingyun.order.manage.mapper.group;

import com.pinshang.qingyun.base.mybatis.MyMapper;
import com.pinshang.qingyun.order.manage.dto.group.GroupOrderIDTO;
import com.pinshang.qingyun.order.manage.dto.group.GroupOrderODTO;
import com.pinshang.qingyun.order.manage.model.group.GrouponOrderLogModel;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2018/10/10 10:23
 */
@Repository
public interface GrouponOrderLogMapper extends MyMapper<GrouponOrderLogModel> {

    List<Long> queryGrouponOrderLog(@Param("beginTime") String beginTime, @Param("endTime") String endTime, @Param("groupType") Integer groupType);

    int deleteGrouponOrderDay(@Param("orderTime") String orderTime);
    int insertGrouponOrderDay(@Param("orderTime") String orderTime);

    List<GroupOrderODTO> commodityGroupOrder(GroupOrderIDTO idto);
    GroupOrderODTO commodityGroupOrderSum(GroupOrderIDTO idto);
}

package com.pinshang.qingyun.order.manage.dto;

import lombok.Data;

import java.math.BigDecimal;

/**
 * @Author: sk
 * @Date: 2022/8/31
 */
@Data
public class CloudOrderAllocationItemIDTO {

    /** 商品id */
    private Long commodityId;

    /** 配货份数 */
    private Long allocationNumber;

    public BigDecimal getAllocationNumberBigDecimal(){
        return allocationNumber != null ? new BigDecimal(allocationNumber + "") : BigDecimal.ZERO;
    }
}

package com.pinshang.qingyun.order.manage.mapper;

import com.pinshang.qingyun.base.mybatis.MyMapper;
import com.pinshang.qingyun.order.manage.dto.ShopOrderNumODTO;
import com.pinshang.qingyun.order.manage.dto.shopCount.ShopCountStockDetailIDTO;
import com.pinshang.qingyun.order.manage.dto.shopCount.ShopCountStockDetailItemODTO;
import com.pinshang.qingyun.order.manage.dto.shopCount.ShopCountStockPageIDTO;
import com.pinshang.qingyun.order.manage.dto.shopCount.ShopCountStockPageODTO;
import com.pinshang.qingyun.order.manage.model.SubOrderItemCount;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 */
@Mapper
@Repository
public interface SubOrderCountMapper extends MyMapper<SubOrderItemCount> {

    /**
     * 查询门店的订货数量和已点数量
     * @param storeId
     * @param date
     * @return
     */
    ShopOrderNumODTO shopOrderNum(@Param("storeId") Long storeId, @Param("date") String date, @Param("shopId") Long shopId);


    /**
     * 分页查询送货点数
     * @param shopIdList
     * @param commodityIdList
     * @param beginTime
     * @param endTime
     * @return
     */
    List<ShopCountStockPageODTO> countCommodityListForPage(@Param("shopIdList") List<Long> shopIdList,
                                                           @Param("beginTime") String beginTime, @Param("endTime") String endTime);

    /**
     * 分页查询门店下商品送货点数
     * @param shopIdList
     * @param commodityIdList
     * @param beginTime
     * @param endTime
     * @return
     */
    List<ShopCountStockDetailItemODTO> countCommodityListForDetail(ShopCountStockDetailIDTO idto);


    List<ShopCountStockPageODTO> selectShopCommodityVarietyTotal(ShopCountStockPageIDTO idto);

    List<ShopCountStockDetailItemODTO> selectShopCommodityVarietyDetail(ShopCountStockDetailIDTO idto);
}

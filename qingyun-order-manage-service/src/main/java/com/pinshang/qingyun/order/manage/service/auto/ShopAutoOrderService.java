package com.pinshang.qingyun.order.manage.service.auto;

import com.github.pagehelper.PageInfo;
import com.pinshang.qingyun.base.configure.expand.QYAssert;
import com.pinshang.qingyun.base.enums.IogisticsModelEnums;
import com.pinshang.qingyun.base.enums.YesOrNoEnums;
import com.pinshang.qingyun.base.enums.order.OrderTypeEnum;
import com.pinshang.qingyun.base.enums.storage.StockTypeEnum;
import com.pinshang.qingyun.base.spring.SpringBeanFinder;
import com.pinshang.qingyun.box.utils.BeanCloneUtils;
import com.pinshang.qingyun.box.utils.DateTimeUtil;
import com.pinshang.qingyun.box.utils.DateUtil;
import com.pinshang.qingyun.box.utils.SpringUtil;
import com.pinshang.qingyun.common.service.DictionaryClient;
import com.pinshang.qingyun.order.dto.auto.AutoSaveOrderIDTO;
import com.pinshang.qingyun.order.dto.auto.AutoSaveOrderItemIDTO;
import com.pinshang.qingyun.order.dto.shop.MdShopOrderSettingODTO;
import com.pinshang.qingyun.order.dto.shop.MdShopOrderSettingQueryIDTO;
import com.pinshang.qingyun.order.manage.constant.ThreadPoolBeanConstants;
import com.pinshang.qingyun.order.manage.dto.auto.AutoShopCommodityODTO;
import com.pinshang.qingyun.order.manage.dto.auto.ShopAutoOrderODTO;
import com.pinshang.qingyun.order.manage.dto.auto.ShopOrderedQuantityODTO;
import com.pinshang.qingyun.order.manage.enums.DeliveryBatchTypeEnum;
import com.pinshang.qingyun.order.manage.mapper.CommodityMapper;
import com.pinshang.qingyun.order.manage.mapper.ShopMapper;
import com.pinshang.qingyun.order.manage.mapper.auto.ShopAutoOrderMapper;
import com.pinshang.qingyun.order.manage.mapper.entry.ProductLimitEntry;
import com.pinshang.qingyun.order.manage.model.Shop;
import com.pinshang.qingyun.order.manage.model.promotion.Promotion;
import com.pinshang.qingyun.order.manage.model.promotion.PromotionProduct;
import com.pinshang.qingyun.order.manage.service.BStockService;
import com.pinshang.qingyun.order.manage.service.CommodityService;
import com.pinshang.qingyun.order.manage.service.WeChatSendMessageService;
import com.pinshang.qingyun.order.manage.util.OrderManageTimeUtil;
import com.pinshang.qingyun.order.manage.vo.CommodityListRequestVO;
import com.pinshang.qingyun.order.manage.vo.auto.BStockShortResponseVO;
import com.pinshang.qingyun.order.service.AutoOrderSaveClient;
import com.pinshang.qingyun.order.service.MdShopOrderSettingClient;
import com.pinshang.qingyun.price.dto.commodity.CommodityListRequestIDTO;
import com.pinshang.qingyun.price.dto.commodity.CommodityResultODTO;
import com.pinshang.qingyun.price.service.ProductPriceModelClient;
import com.pinshang.qingyun.report.dto.ShopAutoCommodityTaxDTO;
import com.pinshang.qingyun.report.service.MdCommodityTaxClient;
import com.pinshang.qingyun.shop.dto.shopCommodity.ShopCommodityInfoODTO;
import com.pinshang.qingyun.storage.dto.tob.CommodityInventoryODTO;
import com.pinshang.qingyun.xd.wms.dto.ShopCommodityStockDTO;
import com.pinshang.qingyun.xd.wms.dto.StockQueryIDTO;
import com.pinshang.qingyun.xd.wms.service.XdStockClient;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.redisson.api.RAtomicLong;
import org.redisson.api.RBucket;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Example;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.concurrent.ArrayBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @Author: sk
 * @Date: 2022/5/11
 */

@Slf4j
@Service
public class ShopAutoOrderService {

    @Autowired
    private RedissonClient redissonClient;
    @Autowired
    private ShopAutoOrderMapper shopAutoOrderMapper;
    @Autowired
    private CommodityMapper commodityMapper;
    @Autowired
    private ProductPriceModelClient productPriceModelClient;
    @Autowired
    private MdShopOrderSettingClient mdShopOrderSettingClient;

    @Autowired
    private MdCommodityTaxClient mdCommodityTaxClient;

    @Autowired
    private DictionaryClient dictionaryClient;

    @Autowired
    private WeChatSendMessageService weChatSendMessageService;
    @Autowired
    private CommodityService commodityService;

    @Autowired
    private BStockService bStockService;
    private static String SHOP_AUTO_ORDER = "门店自动订货提交订单: ";
    @Autowired
    private ShopMapper shopMapper;
    private static AtomicInteger count = new AtomicInteger(0);
    @Autowired
    private XdStockClient xdStockClient;
    @Autowired
    private AutoOrderSaveClient autoOrderSaveClient;

    /**
     * 自动订货失败之后，异步重新跑一次
     * @param autoStr = shopId + "," + storeId + "," + orderTime
     */
    @Async
    public void againAutoOrderAsync(String autoStr) {
        try{
            log.warn("自动订货失败，重新跑一次：{}", autoStr);

            // 自动订货报错才会进来，不会有太多的错误
            // 线程并发过来,进行随机sleep.最多sleep 150秒，count会重新置为0
            Thread.sleep((count.getAndIncrement() + 1)* 3000L);

            if(StringUtils.isNotBlank(autoStr)){
                // 获取冻品凑整商品组
                List<Long> freezeGroupList = commodityService.getCommodityFreezeGroup();

                String [] str = autoStr.split(",");
                Long shopId = Long.valueOf(str[0]);
                Long storeId = Long.valueOf(str[1]);
                String orderTime = str[2];

                String Key =  "shop_auto_async" + shopId + orderTime.replaceAll(":","");
                RBucket<String> bucket = redissonClient.getBucket(Key);
                String auto = bucket.get();
                if(StringUtils.isEmpty(auto)){
                    bucket.set(autoStr,30, TimeUnit.MINUTES);
                    createShopAutoOrder(shopId, storeId, orderTime, freezeGroupList);
                }else {
                    log.warn("门店自动订货异步执行失败2 {}", autoStr);
                }
            }
        }catch (Exception e){
            log.error("门店自动订货异步执行失败 {}", autoStr, e);
        }finally {
            if(count.get() >= 50){
                count.set(0);
            }
        }
    }

    /**
     * 手动输入参数跑自动订货
     * @param orderTime
     * @param shopIdList
     * @return
     */
    public Boolean handAutoOrder(String orderTime, List<Long> shopIdList){
        QYAssert.isTrue(StringUtils.isNotBlank(orderTime), "截单时间不能为空");
        QYAssert.isTrue(CollectionUtils.isNotEmpty(shopIdList), "门店idList不能为空");

        Example ex = new Example(Shop.class);
        ex.createCriteria().andIn("id", shopIdList);
        List<Shop> shopList = shopMapper.selectByExample(ex);

        // 打乱门店顺序
        Collections.shuffle(shopList);
        // 获取冻品凑整商品组
        List<Long> freezeGroupList = commodityService.getCommodityFreezeGroup();

        // 构造一个线程池
        ThreadPoolTaskExecutor threadPool = (ThreadPoolTaskExecutor) SpringBeanFinder.getBean(ThreadPoolBeanConstants.AUTO_ORDER_THREADPOOL);


        for(Shop shop : shopList){
            // 创建任务并提交到线程池中
            threadPool.execute(new Runnable() {
                @Override
                public void run() {
                    try {
                        createShopAutoOrder(shop.getId(), shop.getStoreId(), orderTime, freezeGroupList);
                    } catch (Exception e) {
                        log.error("门店自动订货提交订单异常 shopId: {} orderTime: {} Exception: {}", shop.getId(), orderTime, e);

                        StringBuffer sb = new StringBuffer();
                        sb.append("门店自动订货提交订单异常,门店id: " + shop.getId());
                        sb.append(" 截单时间: " + orderTime);
                        weChatSendMessageService.sendWeChatMessage(sb.toString());
                    }
                }
            });
        }

        return Boolean.TRUE;
    }

    /**
     * 门店自动订货截单时间前30分钟计算提交订单
     * @param orderTime
     * @return
     */
    @Async
    public Boolean createShopAutoOrder(String orderTime){
        // 校验重复任务
        String lockKey =  "shop_auto_create_order" + orderTime.replaceAll(":","");
        RAtomicLong lockKeyRa = redissonClient.getAtomicLong(lockKey);
        long lock = lockKeyRa.incrementAndGet();
        if (lock == 1) {
            lockKeyRa.expire(10, TimeUnit.MINUTES);
        }
        if (lock > 1) {
            return false;
        }

        // 查询开启的自动订货门店
        List<AutoShopCommodityODTO> autoShopList = shopAutoOrderMapper.queryAutoShopList();
        if(CollectionUtils.isEmpty(autoShopList)){
            return false;
        }

        // 打乱门店顺序
        Collections.shuffle(autoShopList);

        // 获取冻品凑整商品组
        List<Long> freezeGroupList = commodityService.getCommodityFreezeGroup();

        // 构造一个线程池
        ThreadPoolTaskExecutor threadPool = (ThreadPoolTaskExecutor) SpringBeanFinder.getBean(ThreadPoolBeanConstants.AUTO_ORDER_THREADPOOL);

        for(AutoShopCommodityODTO autoShopODTO : autoShopList){
            // 创建任务并提交到线程池中
            threadPool.execute(new Runnable() {
                @Override
                public void run() {
                    try {
                         createShopAutoOrder(autoShopODTO.getShopId(), autoShopODTO.getStoreId(), orderTime, freezeGroupList);
                    } catch (Exception e) {
                        log.error("门店自动订货提交订单异常 shopId: {} orderTime: {} Exception: {}", autoShopODTO.getShopId(), orderTime, e);

                        StringBuffer sb = new StringBuffer();
                        sb.append("门店自动订货提交订单异常,门店id: " + autoShopODTO.getShopId());
                        sb.append(" 截单时间: " + orderTime);
                        weChatSendMessageService.sendWeChatMessage(sb.toString());
                    }
                }
            });
        }

        return Boolean.TRUE;
    }


    /**
     * 门店自动订货提交订单
     * @return
     */
    public Boolean createShopAutoOrder(Long shopId, Long storeId, String orderTime, List<Long> freezeGroupList) {
        List<AutoShopCommodityODTO> autoShopCommodityList = shopAutoOrderMapper.queryAutoShopCommodityList(shopId);
        if(CollectionUtils.isEmpty(autoShopCommodityList)){
            log.warn(SHOP_AUTO_ORDER + "门店下无商品池,门店id: {} 截单时间: {}", shopId, orderTime);
            return false;
        }

        List<Long> autoShopCommodityIdList = autoShopCommodityList.stream().map(item -> item.getCommodityId()).collect(Collectors.toList());
        // 查询当前门店当天已经订的商品
        List<Long> alreadyCommodityIdList = shopAutoOrderMapper.queryAutoOrderLogCommodityIds(DateUtil.getDateFormate(new Date(), "yyyy-MM-dd") + " 00:00:00",
                                                            DateUtil.getDateFormate(new Date(), "yyyy-MM-dd") + " 23:59:59", storeId);
        if(CollectionUtils.isNotEmpty(alreadyCommodityIdList)){
            autoShopCommodityIdList = autoShopCommodityIdList.stream().filter(p -> !alreadyCommodityIdList.contains(p)).collect(Collectors.toList());
        }

        if(CollectionUtils.isEmpty(autoShopCommodityIdList)){
            log.warn(SHOP_AUTO_ORDER + "商品已经全部自动订货,门店id: {} 截单时间: {}", shopId, orderTime);
            return false;
        }

        // 查询门店可采、可售状态的商品
        CommodityListRequestVO commodityListRequestVO = new CommodityListRequestVO();
        commodityListRequestVO.setShopId(shopId);
        commodityListRequestVO.setCommodityIdList(autoShopCommodityIdList);
        List<Long> commodityPurchaseList = commodityMapper.findShopCommodityPurchaseList(commodityListRequestVO);
        if(CollectionUtils.isEmpty(commodityPurchaseList)){
            log.warn(SHOP_AUTO_ORDER + "门店无可采、可售商品.门店id: {} 截单时间: {} 商品idList: {}", shopId, orderTime, autoShopCommodityIdList);
            return false;
        }

        autoShopCommodityIdList = new ArrayList<>();
        autoShopCommodityIdList.addAll(commodityPurchaseList);

        // 调用client 去 qingyun-price 查询
        CommodityListRequestIDTO idto = new CommodityListRequestIDTO();
        idto.setStoreId(storeId + "");
        idto.setCommodityIdListAll(autoShopCommodityIdList);
        idto.setPageNo(1);
        idto.setPageSize(Integer.MAX_VALUE);
        PageInfo<CommodityResultODTO> resultPageData = productPriceModelClient.findStoreCommodityListByPage(idto);
        List<CommodityResultODTO> pageList = resultPageData.getList();
        if(CollectionUtils.isEmpty(pageList)){
            log.warn(SHOP_AUTO_ORDER + "客户价格方案列表为空,门店id: {} 截单时间: {} 商品idList: {}", shopId, orderTime, autoShopCommodityIdList);
            return false;
        }

        // 过滤掉价格为null或者0的订单商品
        // 过滤订单价格为0的商品
        pageList = pageList.stream().filter(p -> p.getCommodityPrice() != null
                                            && p.getCommodityPrice().compareTo(BigDecimal.ZERO) > 0).collect(Collectors.toList());


        if(CollectionUtils.isEmpty(pageList)){
            log.warn(SHOP_AUTO_ORDER + "所有商品价格为0，门店id: {} 截单时间: {}", shopId, orderTime);
            return false;
        }
        List<String> productPriceCommodityIdStrList = pageList.stream().map(item -> item.getCommodityId()).collect(Collectors.toList());
        List<Long> productPriceCommodityIdList = pageList.stream().map(item -> Long.valueOf(item.getCommodityId())).collect(Collectors.toList());
        Map<Long, AutoShopCommodityODTO> autoShopCommodityMap = getAutoShopCommodityMap(autoShopCommodityList, pageList);

        // 获取鲜到库存(这里的库存已经加上了在途数量)
        Map<Long, ShopCommodityInfoODTO> xdStockMap = getXdShopCommodityStockWithOrdered(shopId,productPriceCommodityIdList);
        // 获取门店订货通用设置
        Shop shop = shopMapper.selectByPrimaryKey(shopId);
        MdShopOrderSettingQueryIDTO queryIDTO = new MdShopOrderSettingQueryIDTO();
        queryIDTO.setShopType(shop.getShopType());
        queryIDTO.setCommodityIds(productPriceCommodityIdStrList);
        List<MdShopOrderSettingODTO> mdShopOrderSettingList = mdShopOrderSettingClient.queryMdShopOrderSettingListByShopType(queryIDTO);
        if(CollectionUtils.isEmpty(mdShopOrderSettingList)){
            log.warn(SHOP_AUTO_ORDER + "门店订货通用设置为空,门店id: {} 截单时间: {} 商品idList: {}", shopId, orderTime, productPriceCommodityIdStrList);
            return false;
        }

        // 此次截单范围内的 filterSettingList
        List<MdShopOrderSettingODTO> cutOffSettingList = new ArrayList<>(pageList.size());
        for(MdShopOrderSettingODTO settingEntry : mdShopOrderSettingList){
            Integer logisticsModel = settingEntry.getLogisticsModel().intValue();
            // 配送比较仓库时间
            if( logisticsModel == IogisticsModelEnums.DISPATCHING.getCode()){
                if(DateTimeUtil.compareTime(OrderManageTimeUtil.addMinute(settingEntry.getDefaultWarehouseEndTime(), -30),orderTime)
                        && DateTimeUtil.compareTime(DateUtil.getDateFormate(new Date(), "HH:mm"), settingEntry.getDefaultWarehouseEndTime())){
                    cutOffSettingList.add(settingEntry);
                }
            } else if(logisticsModel == IogisticsModelEnums.DIRECT_CONNECTION.getCode()){
                //直通比较供应商时间
                if(DateTimeUtil.compareTime(OrderManageTimeUtil.addMinute(settingEntry.getDefaultSupplierEndTime(),-30),orderTime)
                        &&  DateTimeUtil.compareTime(DateUtil.getDateFormate(new Date(), "HH:mm"), settingEntry.getDefaultSupplierEndTime())){
                    cutOffSettingList.add(settingEntry);
                }
            }
        }
        if(CollectionUtils.isEmpty(cutOffSettingList)){
            List<Long> commodityIdList = mdShopOrderSettingList.stream().map(item -> item.getCommodityIdLong()).collect(Collectors.toList());
            log.warn(SHOP_AUTO_ORDER + "门店id: {} 截单时间: {} 商品idList: {} 没有可提交订单数据", shopId, orderTime, commodityIdList);
            return false;
        }

        List<Long> cutOffCommodityIdList = cutOffSettingList.stream().map(item -> item.getCommodityIdLong()).collect(Collectors.toList());
        log.warn(SHOP_AUTO_ORDER + "门店id: {} 截单时间: {} 商品idList: {} 当前满足截单时间的数据", shopId, orderTime, cutOffCommodityIdList);

        // shopAutoOrderList 为所有订单商品(包括截单的和没有截单的)
        // 根据门店订货通用设置按照orderTime分组，并过滤掉限量商品
        // 并且是否速冻根据凑整商品组来定
        List<ShopAutoOrderODTO> shopAutoOrderList = getFilterShopOrderList(shopId, storeId, autoShopCommodityMap, mdShopOrderSettingList, freezeGroupList);
        if(CollectionUtils.isEmpty(shopAutoOrderList)){
            List<Long> filterLimitIdList = shopAutoOrderList.stream().distinct().map(item -> item.getCommodityId()).collect(Collectors.toList());
            log.warn(SHOP_AUTO_ORDER + "特价限量、客户/产品价格方案限量.商品被过滤完了,门店id: {} 截单时间: {} 商品idList: {}", shopId, orderTime, filterLimitIdList);
            return false;
        }else {
            // 判断原始商品个数是否和过滤后的商品个数一样
            List<Long> filterLimitIdList = shopAutoOrderList.stream().distinct().map(item -> item.getCommodityId()).collect(Collectors.toList());
            List<Long> commodityIdList = mdShopOrderSettingList.stream().map(item -> item.getCommodityIdLong()).collect(Collectors.toList());
            if(filterLimitIdList.size() < commodityIdList.size()){
                List<Long> filterIdList = getShortCommodityIdList(commodityIdList, filterLimitIdList);
                log.warn(SHOP_AUTO_ORDER + "特价限量、客户/产品价格方案限量被过滤的商品信息如下 ,门店id: {} 截单时间: {} 商品idList: {}", shopId, orderTime, filterIdList);
            }
        }

        // B端库存依据，冻品不判断30的倍数。和普通商品一样按照缺货来定。
        // 如果当前截单时间里面冻品，,则把所有冻品在当前截单时间内提交.
        List<ShopAutoOrderODTO> resultShopAutoOrderList = getResultShopAutoOrderList(shopAutoOrderList, cutOffCommodityIdList, xdStockMap); // 最终符合的数据list


        if(CollectionUtils.isEmpty(resultShopAutoOrderList)){
            List<Long> idList = shopAutoOrderList.stream().distinct().map(item -> item.getCommodityId()).collect(Collectors.toList());
            log.warn(SHOP_AUTO_ORDER + "门店id: {} 截单时间: {} 商品idList: {}  冻品、非冻品,无缺货数据", shopId, orderTime, idList);
            return false;
        }

        // 按orderTime分组，判断库存依据。不足库存的按照剩余可用库存订货
        Map<String, List<ShopAutoOrderODTO>> shopAutoOrderMap = resultShopAutoOrderList.stream().collect(Collectors.groupingBy(ShopAutoOrderODTO::getOrderTime));
        shopAutoOrderMap.forEach((entry, list)->{
            String orderDate = entry;
            // 校验B端库存依据
            Map<Long, BigDecimal> orderQuantityMap = new HashMap<>();
            list.forEach(item->{
                orderQuantityMap.put(item.getCommodityId(), item.getQuantity());
            });
            Map<Long, CommodityInventoryODTO> toBStockMap = bStockService.getbStockMap(DateUtil.parseDate(orderDate, "yyyy-MM-dd"), orderQuantityMap);
            List<BStockShortResponseVO> responseVOList = bStockService.checkBStock(shop, OrderTypeEnum.SHOP_AUTO_ORDER.getCode(),
                    DateUtil.parseDate(orderDate, "yyyy-MM-dd"), orderQuantityMap, "自动订货", toBStockMap,-1L);
            Map<String, BigDecimal> shortStockMap;
            if(CollectionUtils.isNotEmpty(responseVOList)){
                //库存不足的，按照剩余库存来定
                shortStockMap = responseVOList.stream().collect(Collectors.toMap(BStockShortResponseVO::getCommodityId,BStockShortResponseVO::getInventoryQuantity,(key1 , key2)-> key2));
            }else {
                shortStockMap = new HashMap<>();
            }

            list.forEach(item->{
                item.setPresaleStatus(YesOrNoEnums.NO.getCode());
                CommodityInventoryODTO commodityInventoryODTO = toBStockMap.get(item.getCommodityId());
                item.setStockType(commodityInventoryODTO != null ? commodityInventoryODTO.getStockType() : StockTypeEnum.UN_LIMIT.getCode());
                if (Objects.nonNull(commodityInventoryODTO)) {
                    Integer sourceRatio = commodityInventoryODTO.getSourceRatio();
                    item.setSourceRatio(sourceRatio);
                    Integer targetRatio = commodityInventoryODTO.getTargetRatio();
                    item.setTargetRatio(targetRatio);
                    Long targetCommodityId = commodityInventoryODTO.getTargetCommodityId();
                    item.setTargetCommodityId(targetCommodityId);
                    Integer convertStatus = commodityInventoryODTO.getCanConvert() ? 1 : 0;
                    item.setConvertStatus(convertStatus);
                    BigDecimal quantity = item.getQuantity();
                    // 组合商品目标数量 : 向上取整 ( quantity / source_ratio * target_ratio)
                    if (commodityInventoryODTO.getCanConvert()) {
                        BigDecimal targetQuantity = quantity.divide(new BigDecimal(sourceRatio), 0, RoundingMode.UP).multiply(new BigDecimal(targetRatio));
                        item.setTargetQuantity(targetQuantity);
                    }
                }

                if(shortStockMap.get(item.getCommodityId() + "") != null){
                    // 可用库存数量
                    BigDecimal inventoryQuantity = shortStockMap.get(item.getCommodityId() + "");
                    item.setQuantity(inventoryQuantity.divide(item.getSalesBoxCapacity(),0, BigDecimal.ROUND_DOWN)
                            .multiply(item.getSalesBoxCapacity()));
                }
            });

        });

        List<Long> idList = resultShopAutoOrderList.stream().distinct().map(item -> item.getCommodityId()).collect(Collectors.toList());
        resultShopAutoOrderList = resultShopAutoOrderList.stream().filter(p -> p.getQuantity().compareTo(BigDecimal.ZERO) > 0 ).collect(Collectors.toList());
        if(CollectionUtils.isEmpty(resultShopAutoOrderList)){
            log.warn(SHOP_AUTO_ORDER + "门店id: {} 截单时间: {} 商品idList: {}  库存全部不足了", shopId, orderTime, idList);
            return false;
        }

        // 调用order 保存订单订货订单
        AutoSaveOrderIDTO autoSaveOrderIDTO = new AutoSaveOrderIDTO();
        List<AutoSaveOrderItemIDTO> autoOrderList = BeanCloneUtils.copyTo(resultShopAutoOrderList, AutoSaveOrderItemIDTO.class);
        autoSaveOrderIDTO.setShopId(shopId);
        autoSaveOrderIDTO.setStoreId(storeId);
        autoSaveOrderIDTO.setOrderTime(orderTime);
        autoOrderSaveClient.saveAutoOrder(autoSaveOrderIDTO);

        return Boolean.TRUE;
    }

    private List<ShopAutoOrderODTO> getResultShopAutoOrderList(List<ShopAutoOrderODTO> shopAutoOrderList, List<Long> cutOffCommodityIdList, Map<Long, ShopCommodityInfoODTO> xdStockMap) {
        List<ShopAutoOrderODTO> resultShopAutoOrderList = new ArrayList<>();
        // 判断当前截单时间的商品是否有冻品
        List<ShopAutoOrderODTO> shopAutoFreezeList = shopAutoOrderList.stream().filter(p -> p.getCommodityIsQuickFreeze().equals(YesOrNoEnums.YES.getCode())
                        && cutOffCommodityIdList.contains(p.getCommodityId())).collect(Collectors.toList());

        Boolean containFreeze = CollectionUtils.isNotEmpty(shopAutoFreezeList);
        for(ShopAutoOrderODTO orderODTO : shopAutoOrderList){
            if(cutOffCommodityIdList.contains(orderODTO.getCommodityId())
                    || (containFreeze && orderODTO.getCommodityIsQuickFreeze().equals(YesOrNoEnums.YES.getCode())) ){
                ShopCommodityInfoODTO shopCommodityInfo = xdStockMap.get(orderODTO.getCommodityId());
                if(shopCommodityInfo != null){
                    // 库存数小于安全库存
                    if(shopCommodityInfo.getStockQuantity().compareTo(orderODTO.getSafeQuantity()) < 0){
                        // (安全库存-库存数) / 销售箱规 向上取整 *总部的销售箱规
                        BigDecimal quantity = orderODTO.getSafeQuantity().subtract(shopCommodityInfo.getStockQuantity())
                                .divide(orderODTO.getSalesBoxCapacity(),0, BigDecimal.ROUND_UP)
                                .multiply(orderODTO.getSalesBoxCapacity());
                        orderODTO.setQuantity(quantity);
                        resultShopAutoOrderList.add(orderODTO);
                    }
                }
            }
        }
        return resultShopAutoOrderList;
    }

    /**
     * 对比获取第一个list有，第二个list没有的返回
     * @param commodityIdList
     * @param filterLimitIdList
     * @return
     */
    @NotNull
    private List<Long> getShortCommodityIdList(List<Long> commodityIdList, List<Long> filterLimitIdList) {
        List<Long> filterIdList = new ArrayList<>();
        commodityIdList.forEach(i -> {
           if(!filterLimitIdList.contains(i)){
               filterIdList.add(i);
           }
        });
        return filterIdList;
    }

    /**
     *过滤特价限量、客户/产品价格方案限量、库存限量商品
     * @return
     */
    private List<ShopAutoOrderODTO> getFilterShopOrderList(Long shopId, Long storeId, Map<Long, AutoShopCommodityODTO> autoShopCommodityMap, List<MdShopOrderSettingODTO> settingList, List<Long> freezeGroupList) {
        List<ShopAutoOrderODTO> shopAutoOrderList = new ArrayList<>();
        for(MdShopOrderSettingODTO settingEntry : settingList){
            ShopAutoOrderODTO shopAutoOrderODTO = getShopAutoOrderODTO(shopId, storeId, settingEntry, autoShopCommodityMap, freezeGroupList);
            shopAutoOrderList.add(shopAutoOrderODTO);
        }
        // 按orderTime分组
        Map<String, List<ShopAutoOrderODTO>> shopAutoOrderMap = shopAutoOrderList.stream().collect(Collectors.groupingBy(ShopAutoOrderODTO::getOrderTime));

        // 需要过滤限量的商品list
        List<String> filterCommodityCodeList = new ArrayList<>();
        List<String> filterCommodityIdList = new ArrayList<>();
        for(Map.Entry<String, List<ShopAutoOrderODTO>> entry : shopAutoOrderMap.entrySet()){
            String orderDate = entry.getKey();
            // 获取 特价限量商品编码list
            List<String> specialPriceCommodityCodeList = getSpecialPriceCommodity(storeId, orderDate);
            if(CollectionUtils.isNotEmpty(specialPriceCommodityCodeList)){
                filterCommodityCodeList.addAll(specialPriceCommodityCodeList);
            }
            // 获取 客户/产品价格方案限量商品id信息
            List<String> productLimitCommodityIdList = getProductLimitCommodityList(storeId, orderDate);
            if(CollectionUtils.isNotEmpty(productLimitCommodityIdList)){
                filterCommodityIdList.addAll(productLimitCommodityIdList);
            }
        }
        if(CollectionUtils.isNotEmpty(filterCommodityCodeList)){
            shopAutoOrderList = shopAutoOrderList.stream().filter(p -> !filterCommodityCodeList.contains(p.getCommodityCode())).collect(Collectors.toList());
        }
        if(CollectionUtils.isNotEmpty(filterCommodityIdList)){
            shopAutoOrderList = shopAutoOrderList.stream().filter(p -> !filterCommodityIdList.contains(p.getCommodityId() + "")).collect(Collectors.toList());
        }
        return shopAutoOrderList;
    }

    /**
     * 获取特价限量的商品信息
     * @return
     */
    public List<String> getSpecialPriceCommodity(Long storeId, String orderTime){
        List<String> commodityCodeList = new ArrayList<>();
        // 设置特价和限量
        List<Promotion> promotionList = commodityService.findCommodityPromotionByStoreId(storeId + "", orderTime);
        if(CollectionUtils.isNotEmpty(promotionList)){
            promotionList.forEach(p->{ //多特价
                List<PromotionProduct> promotionProductList = commodityService.findPromotionProductByPromotionId(p.getId().toString());
                if(SpringUtil.isNotEmpty(promotionProductList)){
                    promotionProductList.forEach(pp->{
                        // 如果限购才过滤(不管总限量还是客户限购，有一个限购就算限购)
                        Boolean isLimit = (pp.getTotalLimitNumber() > 0 || pp.getLimitNumber() > 0);
                        if(isLimit && !commodityCodeList.contains(pp.getProductCode())){
                            commodityCodeList.add(pp.getProductCode());
                        }
                    });
                }
            });
        }
        return commodityCodeList;
    }

    /**
     * 获取 客户/产品价格方案限量商品信息
     * @return
     */
    public List<String> getProductLimitCommodityList(Long storeId, String orderTime){
        List<String> productLimitCommodityIdList = new ArrayList<>();

        // 客户/产品价格方案限量
        List<ProductLimitEntry> limitProductList = commodityService.findLimitProductByStoreId(storeId + "");
        if(CollectionUtils.isNotEmpty(limitProductList)){
            limitProductList.forEach(pp->{
                if(!productLimitCommodityIdList.contains(pp.getCommodityId())){
                    productLimitCommodityIdList.add(pp.getCommodityId());
                }
            });
        }

        return productLimitCommodityIdList;
    }

    /**
     * 门店订货商品池转map，并赋值价格
     */
    @NotNull
    private Map<Long, AutoShopCommodityODTO> getAutoShopCommodityMap(List<AutoShopCommodityODTO> autoShopCommodityList, List<CommodityResultODTO> pageList) {
        Map<String, CommodityResultODTO> productPriceResultMap = pageList.stream().collect(Collectors.toMap(CommodityResultODTO::getCommodityId, Function.identity()));
        // 给价格赋值
        for(AutoShopCommodityODTO autoShopCommodityODTO : autoShopCommodityList){
            CommodityResultODTO commodityResult = productPriceResultMap.get(autoShopCommodityODTO.getCommodityId() + "");
            if(commodityResult != null){
                autoShopCommodityODTO.setPrice(commodityResult.getCommodityPrice());
            }
        }

        Map<Long, AutoShopCommodityODTO> autoShopCommodityMap = autoShopCommodityList.stream().collect(Collectors.toMap(AutoShopCommodityODTO::getCommodityId, Function.identity()));
        return autoShopCommodityMap;
    }


    /**
     *获取上个月的销量从高往低，最多分配销量top30的且当前在自动订货池里的冻品
     * 按orderTime进行分组
     */
    private Map<String, List<ShopAutoOrderODTO>> getTop30GroupMap(Long shopId, List<Long> commodityIdList, Map<Long, ShopAutoOrderODTO> shopAutoOrderMap, List<Long> freezeGroupList) {
        Map<String, List<ShopAutoOrderODTO>> top30GroupMap = new HashMap<>();// 查询上月销售top30冻品
        commodityIdList.retainAll(freezeGroupList);
        if(CollectionUtils.isEmpty(commodityIdList)){
            return top30GroupMap;
        }
        ShopAutoCommodityTaxDTO oto = new ShopAutoCommodityTaxDTO(shopId, OrderManageTimeUtil.getLastMonthFirstDay(), OrderManageTimeUtil.getLastMonthLastDay(), commodityIdList);
        List<Long> topCommodityIdList = mdCommodityTaxClient.queryTopShopCommoditySale(oto);
        if(CollectionUtils.isNotEmpty(topCommodityIdList)){
            List<ShopAutoOrderODTO> top30GroupList = new ArrayList<>();
            for(Long commodityId :topCommodityIdList){
                ShopAutoOrderODTO topShopAutoOrder  = shopAutoOrderMap.get(commodityId);
                top30GroupList.add(topShopAutoOrder);
            }
            top30GroupMap =  top30GroupList.stream().collect(Collectors.groupingBy(ShopAutoOrderODTO::getOrderTime));
        }
        return top30GroupMap;
    }

    /**
     * 设置非冻品下单数量
     * @return
     */
    private List<ShopAutoOrderODTO> getNoQuickFreezeList(List<ShopAutoOrderODTO> noQuickFreezeList, Map<Long, ShopCommodityInfoODTO> xdStockMap) {
        List<ShopAutoOrderODTO> list = new ArrayList<>();
        // 非冻品处理
        for(ShopAutoOrderODTO orderODTO : noQuickFreezeList){
            ShopCommodityInfoODTO shopCommodityInfo = xdStockMap.get(orderODTO.getCommodityId());
            if(shopCommodityInfo != null){
                // 库存数小于安全库存
                if(shopCommodityInfo.getStockQuantity().compareTo(orderODTO.getSafeQuantity()) < 0){
                    // (安全库存-库存数) / 销售箱规 向上取整 *总部的销售箱规
                    BigDecimal quantity = orderODTO.getSafeQuantity().subtract(shopCommodityInfo.getStockQuantity())
                            .divide(orderODTO.getSalesBoxCapacity(),0, BigDecimal.ROUND_UP)
                            .multiply(orderODTO.getSalesBoxCapacity());
                    orderODTO.setQuantity(quantity);
                    list.add(orderODTO);
                }
            }
        }
        return list;
    }


    /**
     * 根据门店订货通用设置，赋值供应商、仓库等信息
     * @return
     */
    @NotNull
    private ShopAutoOrderODTO getShopAutoOrderODTO(Long shopId, Long storeId, MdShopOrderSettingODTO settingEntry, Map<Long, AutoShopCommodityODTO> autoShopCommodityMap, List<Long> freezeGroupList) {
        ShopAutoOrderODTO shopAutoOrderODTO = new ShopAutoOrderODTO();
        shopAutoOrderODTO.setShopId(shopId);
        shopAutoOrderODTO.setStoreId(storeId);
        shopAutoOrderODTO.setCommodityId(settingEntry.getCommodityIdLong());
        shopAutoOrderODTO.setOrderTime(OrderManageTimeUtil.getBeginDeliveryTimeRange(settingEntry.getDeleveryTimeRange(), 0));
        shopAutoOrderODTO.setLogisticsModel(settingEntry.getLogisticsModel().intValue());
        shopAutoOrderODTO.setSupplierId(Long.valueOf(settingEntry.getSupplierId()));
        shopAutoOrderODTO.setWarehouseId(Long.valueOf(settingEntry.getWarehouseId()));
        shopAutoOrderODTO.setDeliveryBatch(DeliveryBatchTypeEnum.ONE_BATCH.getCode().toString());
        shopAutoOrderODTO.setDeleveryTimeRange(settingEntry.getDeleveryTimeRange());
        shopAutoOrderODTO.setEnterpriseId(78L);
        shopAutoOrderODTO.setUserId(-1L);
        shopAutoOrderODTO.setCreateName("系统");
        shopAutoOrderODTO.setDefaultSupplierEndTime(settingEntry.getDefaultSupplierEndTime());
        shopAutoOrderODTO.setDefaultWarehouseEndTime(settingEntry.getDefaultWarehouseEndTime());

        // 设置是否是冻品、安全库存、销售箱规
        AutoShopCommodityODTO autoShopCommodityODTO = autoShopCommodityMap.get(settingEntry.getCommodityId());
        shopAutoOrderODTO.setCommodityIsQuickFreeze(freezeGroupList.contains(settingEntry.getCommodityId()) ? YesOrNoEnums.YES.getCode() : YesOrNoEnums.NO.getCode());
        shopAutoOrderODTO.setSafeQuantity(autoShopCommodityODTO.getSafeQuantity());
        shopAutoOrderODTO.setSalesBoxCapacity(autoShopCommodityODTO.getSalesBoxCapacity());
        shopAutoOrderODTO.setCommodityPackageSpec(autoShopCommodityODTO.getCommodityPackageSpec());
        shopAutoOrderODTO.setPrice(autoShopCommodityODTO.getPrice());
        shopAutoOrderODTO.setCommodityCode(autoShopCommodityODTO.getCommodityCode());
        shopAutoOrderODTO.setCommodityName(autoShopCommodityODTO.getCommodityName());
        return shopAutoOrderODTO;
    }


    /**
     * 获取鲜到库存(这里的库存已经加上了在途数量)
     * @param shopId
     * @param commodityList
     * @return
     */
    public Map<Long, ShopCommodityInfoODTO> getXdShopCommodityStockWithOrdered(Long shopId, List<Long> commodityList){
        // 查询门店商品在途数量
        String orderTime = DateUtil.getDateFormate(new Date(),"yyyy-MM-dd");
        List<ShopOrderedQuantityODTO> orderedQuantityList = shopAutoOrderMapper.selectShopOrderQuantity(shopId,commodityList,orderTime);
        Map<Long, ShopOrderedQuantityODTO> orderedQuantityMap = orderedQuantityList.stream().collect(Collectors.toMap(ShopOrderedQuantityODTO::getCommodityId, Function.identity()));


        Map<Long, ShopCommodityInfoODTO> xdStockMap = getXdShopCommodityStock(shopId, commodityList);
        for (Map.Entry<Long, ShopCommodityInfoODTO> entry : xdStockMap.entrySet()) {
            ShopCommodityInfoODTO shopCommodityInfoODTO = entry.getValue();
            if(shopCommodityInfoODTO.getStockQuantity() == null
                    || shopCommodityInfoODTO.getStockQuantity().compareTo(BigDecimal.ZERO) < 0){
                shopCommodityInfoODTO.setStockQuantity(BigDecimal.ZERO);
            }
            // 库存加上在途数量
            ShopOrderedQuantityODTO shopOrderedQuantity = orderedQuantityMap.get(shopCommodityInfoODTO.getCommodityId());
            if(shopOrderedQuantity != null){
                shopCommodityInfoODTO.setStockQuantity(shopCommodityInfoODTO.getStockQuantity().add(shopOrderedQuantity.getQuantity()));
            }
        }
        return xdStockMap;
    }

    /**
     * 获取鲜到库存、份数
     * @param shopId
     * @param commodityList
     * @return
     */
    public Map<Long, ShopCommodityInfoODTO> getXdShopCommodityStock(Long shopId,List<Long> commodityList){
        Map<Long, ShopCommodityInfoODTO> xdStockMap = new HashMap<>();
        StockQueryIDTO queryDTO = new StockQueryIDTO();
        queryDTO.setWarehouseId(shopId);
        queryDTO.setCommodityList(commodityList);
        List<ShopCommodityStockDTO> stockList = xdStockClient.queryShopCommodityStock2(queryDTO);
        if(org.springframework.util.CollectionUtils.isEmpty(stockList)){
            return new HashMap<>(1024);
        }
        for(ShopCommodityStockDTO stock : stockList){
            ShopCommodityInfoODTO infoODTO = new ShopCommodityInfoODTO();
            infoODTO.setCommodityId(stock.getCommodityId());
            infoODTO.setStockQuantity(stock.getStockQuantity());
            infoODTO.setStockNumber(stock.getStockNumber());

            xdStockMap.put(stock.getCommodityId(),infoODTO);
        }
        return xdStockMap;
    }
}

package com.pinshang.qingyun.order.manage.controller;

import com.pinshang.qingyun.order.manage.service.TobCommodityStockService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * @Author: sk
 * @Date: 2024/12/2
 */
@Log4j2
@RestController
@RequestMapping("/tobCommoditytock")
@Api(tags = "ToB库存接口服务")
public class TobCommodityStockController {

    @Autowired
    private TobCommodityStockService tobCommodityStockService;


    /**
     * 鲜达APP、批发APP上架、一键上架判断库存依据是否存在（返回存在库存依据的商品idList）
     * @param commodityIdList
     * @return
     */
    @RequestMapping(value = "/queryExistToBCommodityStockIds", method = RequestMethod.POST)
    @ApiOperation("查询库存依据存在的商品idlist")
    public List<Long> queryExistToBCommodityStockIds(@RequestBody List<Long> commodityIdList){
        return tobCommodityStockService.queryExistToBCommodityStockIds(commodityIdList);
    }
}

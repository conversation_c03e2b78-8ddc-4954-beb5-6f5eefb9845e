package com.pinshang.qingyun.order.manage.service;

import com.pinshang.qingyun.base.service.UserStallServiceInterface;
import com.pinshang.qingyun.smm.dto.userstall.SelectUserStallIdListIDTO;
import com.pinshang.qingyun.smm.service.UserStallClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @Author: sk
 * @Date: 2024/10/9
 */
@Service
public class UserStallServiceImpl implements UserStallServiceInterface {

    @Autowired
    private UserStallClient userStallClient;

    @Override
    public List<Long> selectUserStallIdList(Long userId, Long shopId) {
        SelectUserStallIdListIDTO selectUserStallIdListIDTO = new SelectUserStallIdListIDTO();
        selectUserStallIdListIDTO.setUserId(userId);
        selectUserStallIdListIDTO.setShopId(shopId);
        selectUserStallIdListIDTO.setStallStatus(1);
        return userStallClient.selectUserStallIdList(selectUserStallIdListIDTO);
    }
}

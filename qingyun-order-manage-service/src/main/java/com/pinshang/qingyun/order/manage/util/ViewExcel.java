package com.pinshang.qingyun.order.manage.util;

import com.pinshang.qingyun.order.manage.enums.ExcelSheetTitleEnum;
import org.apache.poi.hssf.usermodel.*;
import org.apache.poi.ss.usermodel.CellType;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.util.ObjectUtils;
import org.springframework.web.servlet.view.document.AbstractXlsView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.OutputStream;
import java.util.*;

/**
 * ViewExcel
 *
 * <AUTHOR>
 * @date 2017/4/19
 **/
public class ViewExcel extends AbstractXlsView {

    protected void buildExcelDocument(Map<String, Object> map, HSSFWorkbook hssfWorkbook, HttpServletRequest request, HttpServletResponse response) throws Exception {
        ExcelSheetTitleEnum sheetTitle = (ExcelSheetTitleEnum) map.get("sheetTitle");
        Map<String, List<String>> list = (Map<String, List<String>>)map.get("data");
        String filename = (String) map.get("filename");
        if (sheetTitle != null && !ObjectUtils.isEmpty(list)) {
            HSSFSheet sheet = hssfWorkbook.createSheet(sheetTitle.getName());
            HSSFCellStyle centerstyle = hssfWorkbook.createCellStyle();
            //centerstyle.setAlignment(HSSFCellStyle.ALIGN_CENTER);
            centerstyle.setAlignment(HorizontalAlignment.CENTER);
            HSSFCellStyle leftStyle = hssfWorkbook.createCellStyle();
            //leftStyle.setAlignment(HSSFCellStyle.ALIGN_LEFT);
            leftStyle.setAlignment(HorizontalAlignment.LEFT);
            // 创建表头
            sheet.setDefaultColumnWidth(18);

            int i = 0;
            int k = 0;
            //设置表title
            if(map.containsKey("title")){
                short cellNum =map.get("titleCells") !=null?(short)map.get("titleCells"):7;
                String title =map.get("title").toString();
                HSSFRow row = sheet.createRow(k++);
                HSSFCell cell =row.createCell(0);
                cell.setCellStyle(centerstyle);
               // cell.setCellNum(cellNum);
                cell.setCellValue(title);
            }
            // 表头提示信息
            if(map.containsKey("tableHeader")){
                String tableHeader =map.get("tableHeader").toString();
                HSSFRow row = sheet.createRow(k++);
                HSSFCell cell = row.createCell(0);
                cell.setCellStyle(leftStyle);
                cell.setCellValue(tableHeader);
            }
            //设置header
            if(map.containsKey("header")){
                String header =map.get("header").toString();
                HSSFRow row = sheet.createRow(k++);
                HSSFCell cell =row.createCell(0);
                cell.setCellStyle(leftStyle);
                cell.setCellValue(header);
            }
            // 设置单元格
            HSSFCell cell = null;
            HSSFRow rowHead = sheet.createRow(k++);
            for (String head : sheetTitle.getTitles()) {
                cell = rowHead.createCell(i++);
                cell.setCellValue(head);
            }
            // 设置body数据

//            for (String key : list.keySet()) {
//                HSSFRow row = sheet.createRow(k++);
//                int j = 0;
//                for (String data : list.get(key)) {
//                        cell.setCellType(Cell.CELL_TYPE_STRING);
//                        row.createCell(j).setCellValue(null ==data?"":data);
//                    j++;
//                }
//            }

            //修改导出排序;
            Set<String> sets =list.keySet();
            Set<String> newSets =new TreeSet<String>(new Comparator<String>() {

                @Override
                public int compare(String o1, String o2) {
                    Integer a1=0;
                    Integer a2 =1;
                    if(o1.startsWith("key_") && o2.startsWith("key_")){
                        a1 =Integer.valueOf(o1.substring(4));
                        a2 =Integer.valueOf(o2.substring(4));
                    }
                    return a1.compareTo(a2);
                }
            });
            newSets.addAll(sets);
            Iterator<String> its =newSets.iterator();
            while(its.hasNext()){
                String key =its.next();
                HSSFRow row = sheet.createRow(k++);
                int j = 0;
                for (String data : list.get(key)) {
                  //  cell.setCellType(Cell.CELL_TYPE_STRING);
                    cell.setCellType(CellType.STRING);
                    row.createCell(j).setCellValue(null ==data?"":data);
                    j++;
                }
            }
        } else {
            hssfWorkbook.createSheet(sheetTitle.getName());
        }
        filename=new String(filename.getBytes("utf-8"),"iso8859-1");//处理中文文件名
        response.setContentType("application/vnd.ms-excel");
        response.setHeader("content-disposition", "attachment;filename="
                + filename + ".xls");
        OutputStream outputStream = response.getOutputStream();
        hssfWorkbook.write(outputStream);
        outputStream.flush();
        outputStream.close();
    }

    @Override
    protected void buildExcelDocument(Map<String, Object> map, Workbook workbook, HttpServletRequest httpServletRequest, HttpServletResponse httpServletResponse) throws Exception {
        buildExcelDocument(map,(HSSFWorkbook)workbook,httpServletRequest,httpServletResponse);
    }
}

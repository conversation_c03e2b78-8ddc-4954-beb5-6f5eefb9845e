package com.pinshang.qingyun.order.manage.enums;
/*
 * 操作类型 1=批量新增商品 2=批量删除商品 3=手动同步至顺丰
 */
public enum PlanCommodityOperateEnums {

	BATCH_ADD("批量新增商品", 1),
	BATCH_DELETE("批量删除商品", 2),
    HAND_SYNC("手动同步至顺丰", 3);

    private String name;
    private Integer code;

    PlanCommodityOperateEnums(String name, Integer code) {
        this.name = name;
        this.code = code;
    }

    public static String getName(Integer code) {
        for (PlanCommodityOperateEnums es : PlanCommodityOperateEnums.values()) {
            if (code == es.getCode()) {
                return es.name;
            }
        }
        return null;
    }

    public String getName() {
        return name;
    }

    public Integer getCode() {
        return code;
    }
}

package com.pinshang.qingyun.order.manage.dto.report;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class ShopOrderGoodReportExportDTO {

    @ExcelProperty("门店类型")
    private String shopTypeName;

    @ExcelProperty("所属部门")
    private String orgName;

    @ExcelProperty("门店")
    private String shopName;

    @ExcelProperty("大类")
    private String commodityFirstName;

    @ExcelProperty("中类")
    private String commoditySecondName;

    @ExcelProperty("小类")
    private String commodityThirdName;

    @ExcelProperty("条形码")
    private String barCode;

    @ExcelProperty("商品编码")
    private String commodityCode;

    @ExcelProperty("商品名称")
    private String commodityName;

    @ExcelProperty("规格")
    private String commoditySpec;

    @ExcelProperty("计量单位")
    private String commodityUnit;

    @ExcelProperty("订货数量")
    private BigDecimal orderNum;

    @ExcelProperty("发货数量")
    private BigDecimal deliveryNum;

    @ExcelProperty("差异数量")
    private BigDecimal differNum;

    @ExcelProperty("实发金额")
    private BigDecimal realDeliveryAmount;

    @ExcelProperty("门店编码")
    private String shopCode;

    @ExcelProperty("工厂")
    private String factoryName;
    @ExcelProperty("生产组")
    private String workshopName;

}

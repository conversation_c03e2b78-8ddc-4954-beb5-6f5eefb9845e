package com.pinshang.qingyun.order.manage.service;

import com.alibaba.fastjson.JSON;
import com.pinshang.qingyun.box.utils.BeanCloneUtils;
import com.pinshang.qingyun.box.utils.SpringUtil;
import com.pinshang.qingyun.order.manage.mapper.CommodityMapper;
import com.pinshang.qingyun.order.manage.mapper.entry.ProductLimitEntry;
import com.pinshang.qingyun.order.manage.model.Commodity;
import com.pinshang.qingyun.order.manage.model.promotion.Promotion;
import com.pinshang.qingyun.order.manage.model.promotion.PromotionProduct;
import com.pinshang.qingyun.price.dto.commodity.ProductLimitODTO;
import com.pinshang.qingyun.price.dto.commodity.PromotionODTO;
import com.pinshang.qingyun.price.dto.commodity.PromotionProductODTO;
import com.pinshang.qingyun.price.service.CommodityPriceClient;
import com.pinshang.qingyun.product.dto.category.CategoryInfoODTO;
import com.pinshang.qingyun.product.dto.commodity.CommodityDetailODTO;
import com.pinshang.qingyun.product.service.CategoryClient;
import com.pinshang.qingyun.product.service.CommodityClient;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Example;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @Author: sk
 * @Date: 2024/12/3
 */
@Slf4j
@Service
public class CommodityService {


    @Autowired
    private CommodityMapper commodityMapper;

    @Autowired
    private CategoryClient categoryClient;

    @Autowired
    private CommodityClient commodityClient;
    @Autowired
    private CommodityPriceClient commodityPriceClient;

    /**
     * 批量查询商品信息并转为Map
     */
    public Map<Long, Commodity> findCommodityInfoByIdMap(List<Long> commodityIds) {
        Example selectExample = new Example(Commodity.class);
        selectExample.createCriteria().andIn("id", commodityIds);
        List<Commodity> commodityInfoByIdList = commodityMapper.selectByExample(selectExample);
        log.info("根据商品id集合查询商品详情,commodityIds:{},返回:{}", JSON.toJSONString(commodityIds), JSON.toJSONString(commodityInfoByIdList));
        if (CollectionUtils.isEmpty(commodityInfoByIdList)) {
            return Collections.emptyMap();
        }
        return commodityInfoByIdList.stream().collect(Collectors.toMap(Commodity::getId, Function.identity()));
    }


    /**
     * 获取商品条码map
     * @return
     */
    public Map<Long,String> getCommodityBarCodeMap(List<Long> commodityIdList, String barcode){
        List<Commodity> list = commodityMapper.findCommodityBarCodeByParam(commodityIdList,barcode);
        Map<Long, String> map = list.stream().collect(
                Collectors.toMap(Commodity::getId, Commodity::getBarCode));
        return map;
    }

    /**
     * 获取类别信息
     * @param cateIdList
     * @return
     */
    public Map<String, String> getCategroyMap(List<Long> cateIdList){
        Map<String, String> cateMap = new HashMap<>();
        List<CategoryInfoODTO> cateList = categoryClient.findCategoryByIds(cateIdList);
        if(CollectionUtils.isNotEmpty(cateList)){
            cateMap = cateList.stream().collect(Collectors.toMap(CategoryInfoODTO::getId,CategoryInfoODTO::getCateName,(key1 , key2)-> key2));
        }
        return cateMap;
    }


    /**
     * 获取工厂、生产组、车间信息
     * @param commodityIdList
     * @return
     */
    public Map<String, CommodityDetailODTO> getFactoryMap(List<Long> commodityIdList){
        Map<String, CommodityDetailODTO> factoryCommodityMap = new HashMap<>();
        List<CommodityDetailODTO> factoryCommodityList = commodityClient.findCommodityInfoByIdList(commodityIdList);
        if(CollectionUtils.isNotEmpty(factoryCommodityList)){
            factoryCommodityMap = factoryCommodityList.stream().collect(Collectors.toMap(CommodityDetailODTO::getCommodityId, Function.identity()));
        }
        return factoryCommodityMap;
    }


    /**
     * 获取冻品凑整商品组
     * @return
     */
    public List<Long> getCommodityFreezeGroup(){
        List<Long> freezeGroupList = commodityMapper.getCommodityFreezeGroup();
        if(CollectionUtils.isEmpty(freezeGroupList)){
            freezeGroupList.add(999999999L);
        }
        return freezeGroupList;
    }


    /**
     * 促销价格
     * @param storeId
     * @param orderTime
     * @return
     */
    public List<Promotion> findCommodityPromotionByStoreId(String storeId,String orderTime){
        List<Promotion> promotionList = new ArrayList<>();
        List<PromotionODTO> list = commodityPriceClient.findCommodityPromotionByStoreId(storeId,orderTime);
        if(CollectionUtils.isNotEmpty(list)){
            promotionList = BeanCloneUtils.copyTo(list,Promotion.class);
        }
        return promotionList;
    }

    /**
     * 促销价格
     * @param promotionId
     * @return
     */
    public List<PromotionProduct> findPromotionProductByPromotionId(String promotionId){
        List<PromotionProduct> promotionProductList = new ArrayList<>();
        List<PromotionProductODTO> list = commodityPriceClient.findPromotionProductByPromotionId(promotionId);
        if(CollectionUtils.isNotEmpty(list)){
            promotionProductList = BeanCloneUtils.copyTo(list,PromotionProduct.class);
        }
        return promotionProductList;
    }

    /**
     *查找产品限量(创建订单检查限量,购物车详情查询限量,订货列表限量)
     * @param storeId
     * @return
     */
    public String findProductPriceModelIdByStoreId(String storeId){
        return commodityPriceClient.findProductPriceModelIdByStoreId(storeId);
    }
    /**
     *查找客户价格方案限量(创建订单检查限量,购物车详情查询限量,订货列表限量)
     * @param storeId
     * @return
     */
    public List<ProductLimitEntry> findCustomerLimitByStoreId(String storeId){
        List<ProductLimitEntry> productLimitList = new ArrayList<>();
        List<ProductLimitODTO> list = commodityPriceClient.findCustomerLimitByStoreId(storeId);
        if(CollectionUtils.isNotEmpty(list)){
            productLimitList = BeanCloneUtils.copyTo(list,ProductLimitEntry.class);
        }
        return productLimitList;
    }

    /**
     * 产品价格方案限量(创建订单检查限量,购物车详情查询限量,订货列表限量)
     * @param productPriceModelId
     * @return
     */
    public List<ProductLimitEntry> findProductPriceModelLimitByPriceModelId(String productPriceModelId){
        List<ProductLimitEntry> productLimitList = new ArrayList<>();
        List<ProductLimitODTO> list = commodityPriceClient.findProductPriceModelLimitByPriceModelId(productPriceModelId);
        if(CollectionUtils.isNotEmpty(list)){
            productLimitList = BeanCloneUtils.copyTo(list,ProductLimitEntry.class);
        }
        return productLimitList;
    }

    public List<ProductLimitEntry> findLimitProductByStoreId(String storeId) {

        List<ProductLimitEntry> resultDto = new ArrayList<ProductLimitEntry>();

        if (StringUtils.isBlank(storeId)) {
            return null;
        }

        String productPriceModelId = findProductPriceModelIdByStoreId(storeId);
        //客户价格方案限量
        List<ProductLimitEntry> customerLimitProductList = findCustomerLimitByStoreId(storeId);
        //产品价格方案限量
        List<ProductLimitEntry> priceModelLimitProductList = new ArrayList<ProductLimitEntry>();

        if (null != productPriceModelId) {
            priceModelLimitProductList = findProductPriceModelLimitByPriceModelId(productPriceModelId);
        }

        if (SpringUtil.isNotEmpty(priceModelLimitProductList)) {
            priceModelLimitProductList.forEach(p -> {
                ProductLimitEntry dto = new ProductLimitEntry();
                dto.setCommodityId(p.getCommodityId());
                dto.setCommodityName(p.getCommodityName());
                dto.setLimitNumber(p.getLimitNumber());
                resultDto.add(dto);
            });
        }

        if (SpringUtil.isNotEmpty(customerLimitProductList)) {
            if (SpringUtil.isEmpty(resultDto)) {
                customerLimitProductList.forEach(c -> {
                    ProductLimitEntry dto = new ProductLimitEntry();
                    dto.setCommodityId(c.getCommodityId());
                    dto.setCommodityName(c.getCommodityName());
                    dto.setLimitNumber(c.getLimitNumber());
                    resultDto.add(dto);
                });
            } else {
                List<ProductLimitEntry> existProductList = customerLimitProductList.stream().filter(c -> {
                    return resultDto.stream().anyMatch(i -> {
                        return i.getCommodityId().equals(c.getCommodityId());
                    });
                }).collect(Collectors.toList());

                if (SpringUtil.isNotEmpty(existProductList)) {
                    existProductList.forEach(p -> {
                        resultDto.forEach(i -> {
                            if (i.getCommodityId().equals(p.getCommodityId())) {
                                i.setLimitNumber(p.getLimitNumber());
                            }
                        });
                    });
                }

                List<ProductLimitEntry> noExistProductList = customerLimitProductList.stream().filter(c -> {
                    return !resultDto.stream().anyMatch(i -> {
                        return i.getCommodityId().equals(c.getCommodityId());
                    });
                }).collect(Collectors.toList());

                if (SpringUtil.isNotEmpty(noExistProductList)) {
                    noExistProductList.forEach(p -> {
                        ProductLimitEntry dto = new ProductLimitEntry();
                        dto.setCommodityId(p.getCommodityId());
                        dto.setCommodityName(p.getCommodityName());
                        dto.setLimitNumber(p.getLimitNumber());
                        resultDto.add(dto);
                    });
                }
            }
        }
        return resultDto;
    }
}

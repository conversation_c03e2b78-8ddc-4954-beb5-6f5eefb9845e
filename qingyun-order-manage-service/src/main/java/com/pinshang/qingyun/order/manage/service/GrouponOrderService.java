package com.pinshang.qingyun.order.manage.service;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.pinshang.qingyun.base.page.TablePageInfo;
import com.pinshang.qingyun.box.utils.BeanUtil;
import com.pinshang.qingyun.order.manage.dto.CommodityBasicODTO;
import com.pinshang.qingyun.order.manage.dto.group.GroupOrderIDTO;
import com.pinshang.qingyun.order.manage.dto.group.GroupOrderODTO;
import com.pinshang.qingyun.order.manage.mapper.CommodityMapper;
import com.pinshang.qingyun.order.manage.mapper.ShopMapper;
import com.pinshang.qingyun.order.manage.mapper.group.GrouponOrderCMapper;
import com.pinshang.qingyun.order.manage.mapper.group.GrouponOrderLogMapper;
import com.pinshang.qingyun.order.manage.vo.CommodityVO;
import com.pinshang.qingyun.xd.wms.service.GrouponClient;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @Author: sk
 * @Date: 2020/12/18
 */
@Service
@Slf4j
public class GrouponOrderService {

    @Autowired
    private GrouponClient grouponClient;
    @Autowired
    private GrouponOrderLogMapper grouponOrderLogMapper;
    @Autowired
    private GrouponOrderCMapper grouponOrderCMapper;
    @Autowired
    private ShopMapper shopMapper;

    @Autowired
    private CommodityMapper commodityMapper;

    @Autowired
    private WeChatSendMessageService weChatSendMessageService;
    @Autowired
    private CommodityService commodityService;


    /**
     * B 端团购订单日汇总
     * @param orderTime
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public Boolean grouponOrderDay(String orderTime) {
        // 先删除
        grouponOrderLogMapper.deleteGrouponOrderDay(orderTime);
        // 新增
        grouponOrderLogMapper.insertGrouponOrderDay(orderTime);

        return Boolean.TRUE;
    }

    /**
     * 商品、门店订货分析表(团购)
     * @param idto
     * @return
     */
    public TablePageInfo<GroupOrderODTO> shopCommodityGroupOrder(GroupOrderIDTO idto) {

        PageInfo<GroupOrderODTO> pageInfo = PageHelper.startPage(idto.getPageNo(), idto.getPageSize()).doSelectPageInfo(() ->{
            grouponOrderLogMapper.commodityGroupOrder(idto);
        });

        TablePageInfo tablePageInfo = BeanUtil.pageInfo2TablePageInfo(pageInfo, TablePageInfo.class);
        List<GroupOrderODTO> list = pageInfo.getList();

        if(CollectionUtils.isNotEmpty(list)){
            Map<String, CommodityBasicODTO> commMap = new HashMap<>();
            Map<Long,String> barCodeMap = new HashMap<>();

            if(idto.getGroupType() == 1){
                List<Long> commodityIdList = list.stream().map(item -> Long.valueOf(item.getCommodityId())).collect(Collectors.toList());
                CommodityVO vo = new CommodityVO();
                vo.setCommodityIdList(commodityIdList);
                List<CommodityBasicODTO> basicEntryList = commodityMapper.findCommodityBasicListByParam(vo);
                commMap = basicEntryList.stream().collect(Collectors.toMap(CommodityBasicODTO::getCommodityId, Function.identity()));
                barCodeMap = commodityService.getCommodityBarCodeMap(commodityIdList,null);
            }

            if(idto.getGroupType() == 1) {
                for(GroupOrderODTO odto : list){
                    CommodityBasicODTO basicEntry = commMap.get(odto.getCommodityId());
                    if (basicEntry != null) {
                        BeanUtils.copyProperties(basicEntry, odto);
                    }

                    String barCodes = barCodeMap.get(Long.valueOf(odto.getCommodityId()));
                    odto.setBarCodes(barCodes);
                    odto.setBarCode(barCodes != null ? barCodes.split(",")[0] : "");
                }
            }
            GroupOrderODTO header = grouponOrderLogMapper.commodityGroupOrderSum(idto);
            tablePageInfo.setHeader(header);
        }
        return tablePageInfo;
    }
}

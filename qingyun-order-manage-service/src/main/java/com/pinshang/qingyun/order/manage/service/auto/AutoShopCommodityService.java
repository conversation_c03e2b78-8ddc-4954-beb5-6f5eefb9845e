package com.pinshang.qingyun.order.manage.service.auto;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.pinshang.qingyun.base.api.TokenInfo;
import com.pinshang.qingyun.base.configure.expand.QYAssert;
import com.pinshang.qingyun.base.enums.IogisticsModelEnums;
import com.pinshang.qingyun.base.enums.YesOrNoEnums;
import com.pinshang.qingyun.base.enums.shop.ShopTypeEnums;
import com.pinshang.qingyun.base.util.FastThreadLocalUtil;
import com.pinshang.qingyun.box.utils.DateUtil;
import com.pinshang.qingyun.box.utils.StringUtil;
import com.pinshang.qingyun.common.dto.DictionaryODTO;
import com.pinshang.qingyun.common.service.DictionaryClient;
import com.pinshang.qingyun.order.dto.shop.MdShopOrderSettingODTO;
import com.pinshang.qingyun.order.dto.shop.MdShopOrderSettingQueryIDTO;
import com.pinshang.qingyun.order.manage.dto.XDCommodityODTO;
import com.pinshang.qingyun.order.manage.dto.auto.AutoOverZbCommodityDTO;
import com.pinshang.qingyun.order.manage.dto.auto.AutoShopCommodityODTO;
import com.pinshang.qingyun.order.manage.enums.AutoCommodityOperationTypeEnums;
import com.pinshang.qingyun.order.manage.enums.AutoShopCommodityLogEnum;
import com.pinshang.qingyun.order.manage.mapper.CommodityMapper;
import com.pinshang.qingyun.order.manage.mapper.ShopMapper;
import com.pinshang.qingyun.order.manage.mapper.auto.AutoCommodityMapper;
import com.pinshang.qingyun.order.manage.mapper.auto.AutoShopCommodityMapper;
import com.pinshang.qingyun.order.manage.model.Commodity;
import com.pinshang.qingyun.order.manage.model.Shop;
import com.pinshang.qingyun.order.manage.model.auto.AutoCommodity;
import com.pinshang.qingyun.order.manage.model.auto.AutoSetting;
import com.pinshang.qingyun.order.manage.model.auto.AutoShopCommodity;
import com.pinshang.qingyun.order.manage.model.auto.AutoShopCommodityLog;
import com.pinshang.qingyun.order.manage.service.CommodityService;
import com.pinshang.qingyun.order.manage.vo.auto.AutoCommodityVO;
import com.pinshang.qingyun.order.manage.vo.auto.AutoShopCommodityListVO;
import com.pinshang.qingyun.order.manage.vo.auto.AutoShopCommodityRequestVO;
import com.pinshang.qingyun.order.manage.vo.auto.ImportStockQuantityVO;
import com.pinshang.qingyun.order.service.MdShopOrderSettingClient;
import org.apache.commons.collections.CollectionUtils;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.redisson.api.RBucket;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.entity.Example;

import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
public class AutoShopCommodityService {

    @Autowired   
    private AutoShopCommodityMapper autoShopCommodityMapper;

    @Autowired  
    private ShopMapper shopMapper;

    @Lazy
    @Autowired
    private AutoSettingService autoSettingService;

    @Autowired
    private AutoShopCommodityLogService autoShopCommodityLogService;

    @Autowired
    private MdShopOrderSettingClient mdShopOrderSettingClient;

    @Autowired
    private CommodityMapper commodityMapper;
    @Autowired
    private AutoCommodityMapper autoCommodityMapper;

    //大于0，且保留三位小数
    private static final Pattern pattern = Pattern.compile("^(([1-9]{1}\\d*)|^0\\.([0-9]){1,3}$)(\\.\\d{1,3})?$");
    private static String AUTO_ORDER_SHOP_ID = "AUTOORDER:SHOP_ID:";

    @Autowired
    private RedissonClient redissonClient;

    @Autowired
    private DictionaryClient dictionaryClient;

    @Autowired
    private AutoCommodityOrderService autoCommodityOrderService;
    @Autowired
    private CommodityService commodityService;

    /**
     * 导入门店门店自动订货 安全库存
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public List<String> importStockQuantity(Workbook wb, Long shopId) {
        QYAssert.notNull(wb, "模板不正确");
        Sheet sheet = wb.getSheetAt(0);
        QYAssert.notNull(sheet, "模板不正确");

        //错误提示信息
        List<String> warnMsg = new ArrayList<>();

        List<ImportStockQuantityVO> importStockQuantity = validateImportStockQuantity(warnMsg, sheet);

        if (warnMsg.size() == 0 && CollectionUtils.isEmpty(importStockQuantity)) {
            QYAssert.isTrue(Boolean.FALSE,"导入数据不能为空");
        }

        if (null == importStockQuantity || importStockQuantity.size() == 0) {
            return warnMsg;
        }

        //重复的商品编码
        List<String> distinctCommodityCode = importStockQuantity.stream().map(ImportStockQuantityVO::getCommodityCode)
                .collect(Collectors.toMap(e -> e, e -> 1, (a, b) -> a + b))
                .entrySet().stream()
                .filter(entry -> entry.getValue() > 1)
                .map(entry -> entry.getKey())
                .collect(Collectors.toList());

        //获取商品信息
        List<String> commodityCodes = importStockQuantity.stream().map(ImportStockQuantityVO::getCommodityCode).collect(Collectors.toList());
        List<ImportStockQuantityVO> commodityInfo = autoShopCommodityMapper.listByShopAndCommodityCode(shopId, commodityCodes);
        Map<String, ImportStockQuantityVO> commodityMap = new HashMap<>();
        if (null != commodityInfo && commodityInfo.size() > 0) {
            commodityMap = commodityInfo.stream().collect(Collectors.toMap(ImportStockQuantityVO::getCommodityCode, e -> e));
        }

        //获取当前门店已经导入的商品
        Example example = new Example(AutoShopCommodity.class);
        example.createCriteria().andEqualTo("shopId", shopId);
        List<AutoShopCommodity> exitedAutoShopCommodity = autoShopCommodityMapper.selectByExample(example);
        Map<Long, AutoShopCommodity> autoShopCommodityMap = new HashMap<>();
        if (null != exitedAutoShopCommodity && exitedAutoShopCommodity.size() > 0) {
            autoShopCommodityMap = exitedAutoShopCommodity.stream().collect(Collectors.toMap(AutoShopCommodity::getCommodityId, e -> e));
        }

        List<AutoShopCommodity> updateList = new ArrayList<>();
        List<AutoShopCommodity> insertList = new ArrayList<>();

        ImportStockQuantityVO importCommodity = null;
        AutoShopCommodity autoShopCommodity = null;

        TokenInfo tokenInfo = FastThreadLocalUtil.getQY();
        Date date = new Date();
        List<AutoShopCommodityLog> logList = new ArrayList<>();
        AutoShopCommodityLog log = null;
        Shop shop = shopMapper.selectByPrimaryKey(shopId);
        //获取导入商品里面得直送商品
        List<Long> directCommodityIdList = getDirectCommodity(shop, commodityInfo);

        // 当“规划池限制开关”为“禁止超出规划池”时，在导入自动订货商品设置时，需要增加校验并提示：
        //xxxxx商品编码，总部规划品项中无此商品
        AutoOverZbCommodityDTO autoOverZbCommodity = autoSettingService.getAutoOverZbCommodity();
        List<String> commodityCodeList = new ArrayList<>();
        Boolean zbForbid = YesOrNoEnums.NO.getCode().equals(autoOverZbCommodity.getOptionValue());
        if(zbForbid){
            //查询总部规划的自动订货商品
            commodityCodeList = autoCommodityMapper.queryAutoCommodityCodes();
        }

        for (ImportStockQuantityVO vo : importStockQuantity) {
            if (!commodityMap.containsKey(vo.getCommodityCode())) {
                warnMsg.add(vo.getCommodityCode()+"商品编码，当前门店无此商品");
                continue;
            }
            if (distinctCommodityCode.contains(vo.getCommodityCode())) {
                warnMsg.add(vo.getCommodityCode()+"商品编码，excel中有重复数据");
                continue;
            }
            importCommodity = commodityMap.get(vo.getCommodityCode());
//            if (importCommodity.getLogisticsModel().equals(IogisticsModelEnums.DIRECT_SENDING.getCode())) {
//                warnMsg.add(vo.getCommodityCode()+"商品编码，自动订货不支持直送商品");
//                continue;
//            }
            if (directCommodityIdList.contains(importCommodity.getCommodityId())) {
                warnMsg.add(vo.getCommodityCode()+"商品编码，自动订货不支持直送商品");
                continue;
            }
            if(zbForbid && !commodityCodeList.contains(vo.getCommodityCode())){
                warnMsg.add(vo.getCommodityCode()+"商品编码，总部规划品项中无此商品");
                continue;
            }
            log = new AutoShopCommodityLog();
            log.setShopId(shopId);
            log.setShopCode(shop.getShopCode());
            log.setShopName(shop.getShopName());
            log.setCommodityId(importCommodity.getCommodityId());
            log.setCommodityCode(importCommodity.getCommodityCode());
            log.setCommodityName(importCommodity.getCommodityName());
            log.setCreateId(tokenInfo.getUserId());
            log.setCreateName(tokenInfo.getRealName());
            log.setCreateTime(date);
            autoShopCommodity = new AutoShopCommodity();
            if (autoShopCommodityMap.containsKey(importCommodity.getCommodityId())) {
                autoShopCommodity.setId(autoShopCommodityMap.get(importCommodity.getCommodityId()).getId());
                autoShopCommodity.setStockQuantity(vo.getStockQuantity());
                autoShopCommodity.setUpdateId(tokenInfo.getUserId());
                autoShopCommodity.setUpdateTime(date);
                updateList.add(autoShopCommodity);
                log.setType(AutoShopCommodityLogEnum.UPDATE_COMMODITY.getCode());
            } else {
                autoShopCommodity.setShopId(shopId);
                autoShopCommodity.setCommodityId(importCommodity.getCommodityId());
                autoShopCommodity.setStockQuantity(vo.getStockQuantity());
                autoShopCommodity.setCreateId(tokenInfo.getUserId());
                autoShopCommodity.setCreateTime(date);
                autoShopCommodity.setUpdateId(tokenInfo.getUserId());
                autoShopCommodity.setUpdateTime(date);
                insertList.add(autoShopCommodity);
                log.setType(AutoShopCommodityLogEnum.INSERT_COMMODITY.getCode());
            }
            logList.add(log);
            
        }
        if (insertList.size() > 0) {
           autoShopCommodityMapper.insertList(insertList);
        }
        if (updateList.size() > 0) {
            updateList.forEach(e -> {
                autoShopCommodityMapper.updateByPrimaryKeySelective(e);
            });
        }
        autoShopCommodityLogService.batchSave(logList);
        //sendLogService.sendLog(logList, "t_log_auto_shop_commodity");
        //删除门店的自动订货商品缓存
        deleteAutoShopCommodityRedis(shopId);

        //更新门店已设置品项数
        updateSetItems(shopId);
        return warnMsg;
    }

    /**
     * 获取导入门店商品里面是否包含直送商品
     * @param shop
     * @param commodityInfo
     * @return
     */
    private List<Long> getDirectCommodity(Shop shop, List<ImportStockQuantityVO> commodityInfo) {
        List<Long> directCommodityIdList = new ArrayList<>();
        if (null != commodityInfo && commodityInfo.size() > 0) {
            List<String> commodityIds = commodityInfo.stream().map(e -> {
                return e.getCommodityId().toString();
            }).collect(Collectors.toList());

            MdShopOrderSettingQueryIDTO queryIDTO = new MdShopOrderSettingQueryIDTO();
            queryIDTO.setShopType(shop.getShopType());
            queryIDTO.setCommodityIds(commodityIds);
            List<MdShopOrderSettingODTO> mdShopOrderSettingEntries = mdShopOrderSettingClient.queryMdShopOrderSettingListByShopType(queryIDTO);
            if (null != mdShopOrderSettingEntries && mdShopOrderSettingEntries.size() > 0) {
                List<MdShopOrderSettingODTO> directCommodityList = mdShopOrderSettingEntries.stream().filter(e -> e.getLogisticsModel().equals(IogisticsModelEnums.DIRECT_SENDING.getCode())).collect(Collectors.toList());
                if (null != directCommodityList && directCommodityList.size() > 0) {
                    directCommodityIdList = directCommodityList.stream().map(MdShopOrderSettingODTO::getCommodityIdLong).collect(Collectors.toList());
                }
            }
        }
        return directCommodityIdList;
    }


    /**
     * 更新门店已设置品项数
     * @param shopId
     */
    @Transactional(rollbackFor = Exception.class)
    public void updateSetItems(Long shopId) {
        TokenInfo tokenInfo = FastThreadLocalUtil.getQY();
        Example example = new Example(AutoShopCommodity.class);
        example.createCriteria().andEqualTo("shopId", shopId);
        Integer setItemsCount = autoShopCommodityMapper.selectCountByExample(example);
        AutoSetting autoSetting = autoSettingService.settingByShopId(shopId);
        if (null != autoSetting) {
            AutoSetting update = new AutoSetting();
            update.setId(autoSetting.getId());
            update.setSetItems(setItemsCount);
            if (null == tokenInfo) {
                update.setUpdateId(1L);
            } else {
                update.setUpdateId(tokenInfo.getUserId());
            }
            update.setUpdateTime(new Date());
            autoSettingService.updateSetting(update);
        } else {
            autoSettingService.initSetting(shopId);
        }
    }

    private List<ImportStockQuantityVO> validateImportStockQuantity(List<String> warnMsg, Sheet sheet) {
        List<ImportStockQuantityVO> res = new ArrayList<>();

        for (Row row : sheet) {
            int rowNum = row.getRowNum();
            if (rowNum == 0) {
                QYAssert.isTrue(checkRowNumZero(row, 0, "商品编码"), "模板不正确");
                QYAssert.isTrue(checkRowNumZero(row, 1, "安全库存"), "模板不正确");
            }

            ImportStockQuantityVO importStockQuantityVO = null;
            if (rowNum > 0) {
                Cell c0 = row.getCell(0);
                Cell c1 = row.getCell(1);
                if (null == c0 && null == c1) {
                    //            warnMsg.add("第"+(rowNum + 1) +"行不能为空");
                    continue;
                }

                if (null == c0 ||
                        (c0 != null && (c0.getCellType().name().equals("STRING") || c0.getCellType().name().equals("BLANK")) && StringUtil.isBlank(c0.getStringCellValue().trim()))) {
                    warnMsg.add("第"+(rowNum + 1) +"行不能为空");
                    continue;
                }
                if (null == c1 ||
                        (c1 != null && (c1.getCellType().name().equals("STRING") || c1.getCellType().name().equals("BLANK")) && StringUtil.isBlank(c1.getStringCellValue().trim()))) {
                    warnMsg.add("第"+(rowNum + 1) +"行不能为空");
                    continue;
                }
                if (c0 != null && !c0.getCellType().name().equals("STRING")) {
                    warnMsg.add("第"+(rowNum + 1) +"行，不是文本格式，所以导入失败");
                    continue;
                }
                if (c1 != null && !c1.getCellType().name().equals("STRING")) {
                    warnMsg.add("第"+(rowNum + 1) +"行，不是文本格式，所以导入失败");
                    continue;
                }

                importStockQuantityVO = new ImportStockQuantityVO();
                importStockQuantityVO.setCommodityCode(c0.getStringCellValue().trim());
                String stockQuantity = isText(c1);
                if (!pattern.matcher(stockQuantity).matches()) {
                    warnMsg.add("第"+(rowNum + 1) +"行，安全库存大于0且最多保留三位小数");
                    continue;
                }
                importStockQuantityVO.setStockQuantity(new BigDecimal(stockQuantity));
                res.add(importStockQuantityVO);
            }
        }
        return res;
    }

    /**
     * 判断模板格式：根据Excel表头来判断
     * @param row
     * @param index
     * @param cellName
     * @return
     */
    private boolean checkRowNumZero(Row row, int index, String cellName) {
        boolean result = true;
            if (row.getCell(index) == null || !cellName.equals(row.getCell(index).getStringCellValue())) {
                result = false;
            }
            return result;
    }

    private String isText(Cell c){
        if(null == c){
            return "";
        }
        String value = c.getStringCellValue().trim();
        return value == null ? "" : value;
    }

    /**
     * 删除门店自动订货的商品
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public Integer  deleteAutoShopCommodity(Long shopCommodityId) {
       AutoShopCommodity autoShopCommodity = autoShopCommodityMapper.selectByPrimaryKey(shopCommodityId);
       QYAssert.isTrue(null != autoShopCommodity, "该商品不存在");
       Integer res = autoShopCommodityMapper.deleteByPrimaryKey(shopCommodityId);
       deleteAutoShopCommodityRedis(autoShopCommodity.getShopId());

       //日志
       Shop shop = shopMapper.selectByPrimaryKey(autoShopCommodity.getShopId());
       Commodity commodity = commodityMapper.selectByPrimaryKey(autoShopCommodity.getCommodityId());
       AutoShopCommodityLog log = new AutoShopCommodityLog();
       log.setShopId(autoShopCommodity.getShopId());
       log.setShopCode(shop.getShopCode());
       log.setShopName(shop.getShopName());
       log.setCommodityId(commodity.getId());
       log.setCommodityCode(commodity.getCommodityCode());
       log.setCommodityName(commodity.getCommodityName());
       log.setType(AutoShopCommodityLogEnum.DELETE_COMMODITY.getCode());
       TokenInfo tokenInfo = FastThreadLocalUtil.getQY();    
       log.setCreateId(tokenInfo.getUserId());
       log.setCreateName(tokenInfo.getRealName());
       log.setCreateTime(new Date());
       List<AutoShopCommodityLog>  logList = new ArrayList<>();
       logList.add(log);
        autoShopCommodityLogService.batchSave(logList);
       //sendLogService.sendLog(logList, "t_log_auto_shop_commodity");

        //更新门店已设置品项数
        updateSetItems(autoShopCommodity.getShopId());
       return res;
    }


    /**
     * 删除门店下所有自动订货商品
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public Integer  deleteAllAutoShopCommodity(Long shopId) {
        QYAssert.notNull(shopId, "门店id不能为空");

        Example autoShopCommodityEx = new Example(AutoShopCommodity.class);
        autoShopCommodityEx.createCriteria().andEqualTo("shopId", shopId);
        List<AutoShopCommodity> autoShopCommodityList = autoShopCommodityMapper.selectByExample(autoShopCommodityEx);
        if(CollectionUtils.isEmpty(autoShopCommodityList)) {
            QYAssert.isFalse("门店下不存在自动订货商品");
        }

        // 删除
        autoShopCommodityMapper.deleteByExample(autoShopCommodityEx);

        // 删除缓存
        deleteAutoShopCommodityRedis(shopId);

        //日志
        List<Long> commodityIdList = autoShopCommodityList.stream().map(item -> item.getCommodityId()).collect(Collectors.toList());
        Map<Long, Commodity> commMap = commodityService.findCommodityInfoByIdMap(commodityIdList);
        Shop shop = shopMapper.selectByPrimaryKey(shopId);

        List<AutoShopCommodityLog>  logList = new ArrayList<>();
        for(Long commodityId : commodityIdList) {
            AutoShopCommodityLog log = new AutoShopCommodityLog();
            log.setShopId(shopId);
            log.setShopCode(shop.getShopCode());
            log.setShopName(shop.getShopName());
            log.setCommodityId(commodityId);
            Commodity commodity = commMap.get(commodityId);
            log.setCommodityCode(commodity.getCommodityCode());
            log.setCommodityName(commodity.getCommodityName());
            log.setType(AutoShopCommodityLogEnum.DELETE_COMMODITY.getCode());
            TokenInfo tokenInfo = FastThreadLocalUtil.getQY();
            log.setCreateId(tokenInfo.getUserId());
            log.setCreateName(tokenInfo.getRealName());
            log.setCreateTime(new Date());

            logList.add(log);
        }

        autoShopCommodityLogService.batchSave(logList);

        //更新门店已设置品项数
        updateSetItems(shopId);

        return autoShopCommodityList.size();
    }

    /**
     * 自动订货设置根据门店查询商品列表
     *
     * 要求品项：总部规划品项 和 上个月有销售品项  交集
     * 不要求品项：已设置品项 减去 要求品项
     * @param vo
     * @return
     */
    public PageInfo<AutoShopCommodityListVO> commodityPageByShopId(AutoShopCommodityRequestVO vo) {
        //获取当前门店设置的自动订货商品
        QYAssert.isTrue(null != vo.getShopId(), "门店不能为空");
        List<Long> shopIdList = new ArrayList<>();
        shopIdList.add(vo.getShopId());
        //已设置品项
        List<Long> autoShopItemsList = autoCommodityByShopIds(shopIdList).get(vo.getShopId());

        //要求单店品项商品列表
        List<Long> requireShopCommodityList = autoSettingService.requireShopCommodityList(vo.getShopId());

        if (null != autoShopItemsList && autoShopItemsList.size() > 0) {

            //计算出来不要求的品项
            if (null != vo.getShowNoRequire() && vo.getShowNoRequire() == 2 && null != requireShopCommodityList && requireShopCommodityList.size() > 0) {
                autoShopItemsList.removeAll(requireShopCommodityList);
                vo.setCommodityIdList(autoShopItemsList);
            }

            Map<Long, MdShopOrderSettingODTO> map = null;
            if ((!StringUtil.isNullOrEmpty(vo.getStartTime()) || !StringUtil.isNullOrEmpty(vo.getEndTime())) && autoShopItemsList.size() > 0) {
                List<MdShopOrderSettingODTO> mdShopOrderSettingEntries = autoSettingService.commodityOrderFilterEndTime(vo.getStartTime(), vo.getEndTime(), vo.getShopId(),autoShopItemsList);
                if (null != mdShopOrderSettingEntries && mdShopOrderSettingEntries.size() > 0) {
                    autoShopItemsList = mdShopOrderSettingEntries.stream().map(MdShopOrderSettingODTO::getCommodityIdLong).collect(Collectors.toList());
                    map = mdShopOrderSettingEntries.stream().collect(Collectors.toMap(MdShopOrderSettingODTO::getCommodityIdLong, e -> e));
                } else {
                    autoShopItemsList = new ArrayList<>();
                }
                vo.setCommodityIdList(autoShopItemsList);
            }
            if (null != autoShopItemsList && autoShopItemsList.size() > 0) {
                PageInfo<AutoShopCommodityListVO> pageDate = PageHelper.startPage(vo.getPageNo(), vo.getPageSize()).doSelectPageInfo(() -> {
                    autoShopCommodityMapper.commodityByShopId(vo);
                });
                if (pageDate.getSize() > 0) {
                    Shop shop = shopMapper.selectByPrimaryKey(vo.getShopId());
                    if (null == map) {
                        List<Long> commodityIds = pageDate.getList().stream().map(AutoShopCommodityListVO::getCommodityId).collect(Collectors.toList());
                        map = autoSettingService.commodityOrderEndTime(vo.getShopId(), commodityIds);
                    }
                    if (map != null) {
                        MdShopOrderSettingODTO mdShopOrderSettingODTO = null;
                        for (AutoShopCommodityListVO autoShopCommodityListVO : pageDate.getList()) {
                            if (map.containsKey(autoShopCommodityListVO.getCommodityId())) {
                                mdShopOrderSettingODTO = map.get(autoShopCommodityListVO.getCommodityId());
                                if (mdShopOrderSettingODTO.getLogisticsModel().equals(IogisticsModelEnums.DISPATCHING.getCode())) {
                                    autoShopCommodityListVO.setEndTime(mdShopOrderSettingODTO.getDefaultWarehouseEndTime());
                                }
                                if (mdShopOrderSettingODTO.getLogisticsModel().equals(IogisticsModelEnums.DIRECT_CONNECTION.getCode()) || mdShopOrderSettingODTO.getLogisticsModel().equals(IogisticsModelEnums.DIRECT_SENDING.getCode())) {
                                    autoShopCommodityListVO.setEndTime(mdShopOrderSettingODTO.getDefaultSupplierEndTime());
                                }
                            }
                            if (shop.getShopType().equals(ShopTypeEnums.XD.getCode())) {
                                autoShopCommodityListVO.setCommodityPackageSpec(autoShopCommodityListVO.getXdSalesBoxCapacity());
                            } else {
                                autoShopCommodityListVO.setCommodityPackageSpec(autoShopCommodityListVO.getSalesBoxCapacity());
                            }

                            if (null != requireShopCommodityList && requireShopCommodityList.contains(autoShopCommodityListVO.getCommodityId())) {
                                autoShopCommodityListVO.setIsRequiredItem(1);
                            } else {
                                autoShopCommodityListVO.setIsRequiredItem(2);
                            }
                        }
                    }
                }
                return pageDate;
            }
        }

        return new PageInfo<>();
    };

    /**
     * 查询门店设置的商品
     * @param shopIds
     */
    public Map<Long, List<Long>> autoCommodityByShopIds(List<Long> shopIds) {
        Example example = new Example(AutoShopCommodity.class);
        example.createCriteria().andIn("shopId", shopIds);
        List<AutoShopCommodity> list = autoShopCommodityMapper.selectByExample(example);
        Map<Long, List<Long>> res = new HashMap<>();
        if (null != list && list.size() > 0) {
            Map<Long, List<AutoShopCommodity>> map = list.stream().collect(Collectors.groupingBy(AutoShopCommodity::getShopId));
            List<Long> commodityIds = null;
            for (Long o : map.keySet()) {
                commodityIds = map.get(o).stream().map(AutoShopCommodity::getCommodityId).collect(Collectors.toList());
                res.put(o, commodityIds);
            }
        }
        return res;
    }


    /**
     * 删除门店自动订货商品池
     * @param shopId
     */
    public void deleteAutoShopCommodityRedis(Long shopId){
        RBucket<List<Long>> bucket = redissonClient.getBucket(AUTO_ORDER_SHOP_ID + shopId);
        bucket.delete();
    }

    /**
     * 根据storeId获取门店下自动订货商品池
     * @param storeId
     * @return
     */
    public List<Long> getAutoCommodityList(Long storeId){
        Example shopEx = new Example(Shop.class);
        shopEx.createCriteria().andEqualTo("storeId", storeId);
        List<Shop> shopList = shopMapper.selectByExample(shopEx);
        QYAssert.isTrue(CollectionUtils.isNotEmpty(shopList), "客户没有关联门店");

        Long shopId = shopList.get(0).getId();
        RBucket<List<Long>> bucket = redissonClient.getBucket(AUTO_ORDER_SHOP_ID + shopId);
        List<Long> autoCommodityIdList  = bucket.get();
        if(CollectionUtils.isNotEmpty(autoCommodityIdList)){
            return autoCommodityIdList;
        }

        autoCommodityIdList = autoShopCommodityMapper.getAutoCommodityList(shopId);
        if(CollectionUtils.isEmpty(autoCommodityIdList)){
            autoCommodityIdList.add(999999999L);
        }
        bucket.set(autoCommodityIdList, DateUtil.getSurplusSeconds(), TimeUnit.SECONDS);

        return autoCommodityIdList;
    }

    /**
     * 删除直送商品
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
     public Boolean deleteDirectSendingCommodity() {
         List<AutoShopCommodityODTO> list = autoShopCommodityMapper.queryAllAutoShopCommodity();
        if (null != list && list.size() > 0) {
            Map<Long, List<AutoShopCommodityODTO>> map = list.stream().collect(Collectors.groupingBy(AutoShopCommodityODTO::getShopId));

            for (Long shopId : map.keySet()) {
                List<AutoShopCommodityLog> logList = new ArrayList<>(); // 日志List
                List<Long> deleteCommodityIds = new ArrayList<>(); // 要删除的商品idList

                Shop shop = shopMapper.selectByPrimaryKey(shopId);
                List<AutoShopCommodityODTO> shopList = map.get(shopId);
                List<String> commodityIds = shopList.stream().map(item -> String.valueOf(item.getCommodityId())).collect(Collectors.toList());

                MdShopOrderSettingQueryIDTO queryIDTO = new MdShopOrderSettingQueryIDTO();
                queryIDTO.setShopType(shop.getShopType());
                queryIDTO.setCommodityIds(commodityIds);
                List<MdShopOrderSettingODTO> mdShopOrderSettingEntries = mdShopOrderSettingClient.queryMdShopOrderSettingListByShopType(queryIDTO);

                // 判断直送
                if (null != mdShopOrderSettingEntries && mdShopOrderSettingEntries.size() > 0) {
                    List<MdShopOrderSettingODTO> directCommodityList = mdShopOrderSettingEntries.stream().filter(e -> e.getLogisticsModel().equals(IogisticsModelEnums.DIRECT_SENDING.getCode())).collect(Collectors.toList());
                    if (null != directCommodityList && directCommodityList.size() > 0) {
                        for (MdShopOrderSettingODTO entry : directCommodityList) {
                            setAutoShopCommodityLog(logList, deleteCommodityIds, AutoShopCommodityLogEnum.DELETE_DIRECT_COMMODITY.getCode(),
                                    shopId, shop.getShopCode(), shop.getShopName(), entry.getCommodityIdLong(), entry.getCommodityCode(), entry.getCommodityName());
                        }
                    }
                }

                // 判断称重并且速冻
                List<AutoShopCommodityODTO> weightFreezeList = shopList.stream().filter(p -> p.isWeightFreeze() && !deleteCommodityIds.contains(p.getCommodityId())).collect(Collectors.toList());
                if(CollectionUtils.isNotEmpty(weightFreezeList)){
                    for (AutoShopCommodityODTO entry : weightFreezeList) {
                        setAutoShopCommodityLog(logList, deleteCommodityIds, AutoShopCommodityLogEnum.DELETE_WEIGHT_COMMODITY.getCode(),
                                shopId, shop.getShopCode(), shop.getShopName(), entry.getCommodityId(), entry.getCommodityCode(), entry.getCommodityName());
                    }
                }

                if(CollectionUtils.isNotEmpty(deleteCommodityIds)){
                    //删除数据
                    Example example = new Example(AutoShopCommodity.class);
                    example.createCriteria().andEqualTo("shopId", shopId)
                            .andIn("commodityId", deleteCommodityIds);
                    autoShopCommodityMapper.deleteByExample(example);
                    //添加日志
                    autoShopCommodityLogService.batchSave(logList);

                    //更新门店已设置品项数
                    updateSetItems(shopId);
                }
            }
        }
        return Boolean.TRUE;
     }

     private void setAutoShopCommodityLog(List<AutoShopCommodityLog> logList, List<Long> deleteCommodityIds,
                 Integer type, Long shopId, String shopCode, String shopName, Long commodityId,
                 String commodityCode, String commodityName){
         AutoShopCommodityLog log = new AutoShopCommodityLog();
         log.setType(type);
         log.setShopId(shopId);
         log.setShopCode(shopCode);
         log.setShopName(shopName);
         log.setCommodityId(commodityId);
         log.setCommodityCode(commodityCode);
         log.setCommodityName(commodityName);
         log.setCreateId(1L);
         log.setCreateName("系统");
         log.setCreateTime(new Date());
         logList.add(log);
         deleteCommodityIds.add(commodityId);
     }

     @Transactional
     public AutoCommodityVO commodityInfo(String barCode) {

         boolean isWeightCode = barCode.length() == 18 && barCode.startsWith("2");
         if(isWeightCode){
             // 获取条形码
             barCode = barCode.substring(1, 7);
         }

         TokenInfo tokenInfo = FastThreadLocalUtil.getQY();
         //AutoCommodityVO autoCommodityVO = shopCommodityMapper.commodityInfo(tokenInfo.getShopId() ,barCode);
         AutoCommodityVO autoCommodityVO = autoShopCommodityMapper.commodityInfo(tokenInfo.getShopId() ,barCode);
         QYAssert.isTrue(null != autoCommodityVO, "当前门店无此商品");
         QYAssert.isTrue(autoCommodityVO.getLogisticsModel() != IogisticsModelEnums.DIRECT_SENDING.getCode(), "自动订货不支持直送商品");

         DictionaryODTO dictionaryODTO = dictionaryClient.getDictionaryById(autoCommodityVO.getCommodityUnitId());
         autoCommodityVO.setCommodityUnit(dictionaryODTO.getOptionName());

         Example example = new Example(AutoShopCommodity.class);
         example.createCriteria().andEqualTo("shopId", tokenInfo.getShopId())
         .andEqualTo("commodityId", autoCommodityVO.getCommodityId());
         List<AutoShopCommodity> exitedAutoShopCommodity = autoShopCommodityMapper.selectByExample(example);
         if (null != exitedAutoShopCommodity && exitedAutoShopCommodity.size() > 0) {
             autoCommodityVO.setStockQuantity(exitedAutoShopCommodity.get(0).getStockQuantity());
         }
         autoCommodityVO.setBarCode(barCode);
         return autoCommodityVO;
     }

     @Transactional
     public Boolean insertAutoOrderCommodity(Long commodityId, BigDecimal stockQuantity) {
         QYAssert.isTrue(stockQuantity.compareTo(BigDecimal.ZERO) > 0, "安全库存数量必须大于0");
         Example example = new Example(AutoCommodity.class);
         example.createCriteria().andEqualTo("commodityId", commodityId);
         AutoCommodity autoCommodity = autoCommodityMapper.selectOneByExample(example);

         TokenInfo tokenInfo = FastThreadLocalUtil.getQY();
         //如果为空，先添加自动订货总部池子
         XDCommodityODTO commodityODTO = commodityMapper.getXDCommodityODTOById(commodityId);
         if (null == autoCommodity) {
             autoCommodityMapper.saveList(Arrays.asList(commodityId.toString()), tokenInfo.getUserId().toString());

             autoCommodityOrderService.recordCommodityLog(Arrays.asList(commodityODTO), AutoCommodityOperationTypeEnums.ADD.getCode(), Boolean.FALSE);
         }

         Example autoExample = new Example(AutoShopCommodity.class);
         autoExample.createCriteria().andEqualTo("shopId", tokenInfo.getShopId())
                 .andEqualTo("commodityId", commodityId);
         List<AutoShopCommodity> exitedAutoShopCommodity = autoShopCommodityMapper.selectByExample(autoExample);

         Date date = new Date();
         AutoShopCommodity autoShopCommodity = new AutoShopCommodity();
         autoShopCommodity.setShopId(tokenInfo.getShopId());
         autoShopCommodity.setCommodityId(commodityId);
         autoShopCommodity.setStockQuantity(stockQuantity);
         autoShopCommodity.setUpdateId(tokenInfo.getUserId());
         autoShopCommodity.setUpdateTime(date);

         //保存日志
         Shop shop = shopMapper.selectByPrimaryKey(tokenInfo.getShopId());
         AutoShopCommodityLog log = new AutoShopCommodityLog();
         log.setShopId(tokenInfo.getShopId());
         log.setShopCode(shop.getShopCode());
         log.setShopName(shop.getShopName());
         log.setCommodityId(commodityId);
         log.setCommodityCode(commodityODTO.getCommodityCode());
         log.setCommodityName(commodityODTO.getCommodityName());
         log.setCreateId(tokenInfo.getUserId());
         log.setCreateName(tokenInfo.getRealName());
         log.setCreateTime(date);

         if (null == exitedAutoShopCommodity || exitedAutoShopCommodity.size() == 0) {
             autoShopCommodity.setCreateId(tokenInfo.getUserId());
             autoShopCommodity.setCreateTime(date);
             autoShopCommodityMapper.insert(autoShopCommodity);

             log.setType(AutoShopCommodityLogEnum.INSERT_COMMODITY.getCode());
         } else {
             autoShopCommodity.setId(exitedAutoShopCommodity.get(0).getId());
             autoShopCommodityMapper.updateByPrimaryKeySelective(autoShopCommodity);

             log.setType(AutoShopCommodityLogEnum.UPDATE_COMMODITY.getCode());
         }

         autoShopCommodityLogService.batchSave(Arrays.asList(log));
         //删除门店的自动订货商品缓存
         deleteAutoShopCommodityRedis(tokenInfo.getShopId());

         //更新门店已设置品项数
         updateSetItems(tokenInfo.getShopId());

         return Boolean.TRUE;
     }
}

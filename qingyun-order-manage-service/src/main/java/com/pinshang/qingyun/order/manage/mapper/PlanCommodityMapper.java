package com.pinshang.qingyun.order.manage.mapper;

import com.pinshang.qingyun.base.mybatis.MyMapper;
import com.pinshang.qingyun.order.manage.dto.commodity.PlanCommodityODTO;
import com.pinshang.qingyun.order.manage.dto.commodity.PlanCommodityQueryIDTO;
import com.pinshang.qingyun.order.manage.model.PlanCommodity;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface PlanCommodityMapper extends MyMapper<PlanCommodity> {

	List<PlanCommodityODTO> queryPlanCommodityList(PlanCommodityQueryIDTO vo);
}


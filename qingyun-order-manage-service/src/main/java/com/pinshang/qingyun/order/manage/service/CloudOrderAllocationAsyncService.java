package com.pinshang.qingyun.order.manage.service;

import com.pinshang.qingyun.base.configure.expand.BizLogicException;
import com.pinshang.qingyun.order.manage.dto.CloudOrderAllocationIDTO;
import com.pinshang.qingyun.storage.dto.deliveryCargo.DeliveryCargoTaskErrorReqDTO;
import com.pinshang.qingyun.storage.service.deliveryCargo.DeliveryCargoTaskClient;
import feign.codec.DecodeException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

/**
 * @Author: sk
 * @Date: 2022/9/2
 */
@Slf4j
@Service
public class CloudOrderAllocationAsyncService {

    @Autowired
    private CloudOrderAllocationService cloudOrderAllocationService;
    @Autowired
    private DeliveryCargoTaskClient deliveryCargoTaskClient;
    @Autowired
    private WeChatSendMessageService weChatSendMessageService;
    /**
     * 云超配货单分配客户(异步方法)
     * @param idto
     * @return
     */
    @Async
    public Boolean allocationStores(CloudOrderAllocationIDTO idto) {
        DeliveryCargoTaskErrorReqDTO errorReqDTO = new DeliveryCargoTaskErrorReqDTO();
        errorReqDTO.setTaskId(idto.getTaskId());
        errorReqDTO.setIfAuto(idto.isIfAuto());
        try{
            cloudOrderAllocationService.allocationStores(idto);
        }catch (Throwable ex){
            log.error("云超配货单分配客户异常", ex);

            if(ex instanceof BizLogicException){ // 业务异常
                createErrorTask(errorReqDTO, ex.getMessage());
            }else if(ex instanceof DecodeException
                    && ex.getCause() != null && ex.getCause() instanceof  BizLogicException){
                createErrorTask(errorReqDTO, ex.getMessage());
            } else {
                createErrorTask(errorReqDTO, ex.getMessage());
                //发送微信提醒
                weChatSendMessageService.sendWeChatMessage("云超配货单分配客户异常" + ex.getMessage());
            }
        }
        return Boolean.TRUE;
    }


    private void createErrorTask(DeliveryCargoTaskErrorReqDTO errorReqDTO, String message) {
        errorReqDTO.setErrorReason(message);
        deliveryCargoTaskClient.createErrorTask(errorReqDTO);
    }
}

package com.pinshang.qingyun.order.manage.dto.report;

import com.pinshang.qingyun.base.page.Pagination;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class SaleReturnReportVo extends Pagination {

    private Long shopId;
    private List<Long> shopIdList;
    private String startTime;
    private String endTime;
    private String updateStartTime;
    private String updateEndTime;
    private String orderCode;
    private Long commodityId;
    private String commodityCode;
    private String barCode;
    private Long consignmentId;
    private Long stallId;

}
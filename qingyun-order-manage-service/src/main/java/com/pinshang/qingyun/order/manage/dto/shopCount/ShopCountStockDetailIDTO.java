package com.pinshang.qingyun.order.manage.dto.shopCount;

import com.pinshang.qingyun.base.page.Pagination;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @ClassName ShopCountStockDetailIDTO
 * <AUTHOR>
 * @Date 2022/12/14 13:59
 * @Description ShopCountStockDetailIDTO
 * @Version 1.0
 */
@Data
public class ShopCountStockDetailIDTO extends Pagination {
    @ApiModelProperty("门店id")
    private Long shopId;

    @ApiModelProperty("送货时间-yyyy-MM-dd")
    private String orderTime;

    @ApiModelProperty("商品id")
    private Long commodityId;

    @ApiModelProperty("商品查询key")
    private String commodityKey;

    @ApiModelProperty("操作人id")
    private Long updateId;

    @ApiModelProperty("商品条码-精准查询")
    private String barCode;
}

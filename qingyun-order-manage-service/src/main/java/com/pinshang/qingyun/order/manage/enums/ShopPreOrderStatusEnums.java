package com.pinshang.qingyun.order.manage.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * @Author: liu<PERSON>hen
 * @DateTime: 2022/5/16 14:04
 */
@Getter
@AllArgsConstructor
public enum ShopPreOrderStatusEnums {
    //1-待审核, 2-审核未通过,3-审核通过, 4-已取消
    AUDIT_WAIT(1,"待审核"),
    AUDIT_FAIL(2,"审核未通过"),
    AUDIT_PASS(3,"审核通过"),
    CANCEL(4,"已取消"),

    ;
    private Integer code;
    private String desc;
    public static String getDescByCode(Integer code) {
        if (code == null) {
            return null;
        } else {
            ShopPreOrderStatusEnums[] var1 = values();
            int var2 = var1.length;

            for(int var3 = 0; var3 < var2; ++var3) {
                ShopPreOrderStatusEnums value = var1[var3];
                if (value.getCode().equals(code)) {
                    return value.getDesc();
                }
            }

            return null;
        }
    }
}

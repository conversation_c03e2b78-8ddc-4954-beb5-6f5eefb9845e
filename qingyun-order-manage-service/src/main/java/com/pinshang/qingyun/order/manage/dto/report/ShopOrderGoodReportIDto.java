package com.pinshang.qingyun.order.manage.dto.report;

import com.pinshang.qingyun.base.configure.expand.QYAssert;
import com.pinshang.qingyun.base.page.Pagination;
import com.pinshang.qingyun.box.utils.DateUtil;
import com.pinshang.qingyun.box.utils.StringUtil;
import com.pinshang.qingyun.infrastructure.data.query.annotate.Change;
import com.pinshang.qingyun.infrastructure.data.query.constant.DataQueryConstant;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.collections.CollectionUtils;

import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class ShopOrderGoodReportIDto extends Pagination{

	@ApiModelProperty("门店类型")
	private Integer shopType;

	@ApiModelProperty("门店id")
	private Long shopId;

	@ApiModelProperty("送货日期开始 yyyy-MM-dd")
	private String beginDate;

	@ApiModelProperty("送货日期结束 yyyy-MM-dd")
	@Change(value = DataQueryConstant.NOW)
	private String endDate;

	@ApiModelProperty("大类id")
	private Long cate1;

	@ApiModelProperty("中类id")
	private Long cate2;

	@ApiModelProperty("小类id")
	private Long cate3;

	@ApiModelProperty("商品idlist")
	private List<Long> commodityIdList;

	@ApiModelProperty("条形码")
	private String barCode;

	@ApiModelProperty("工厂code")
	private String factoryCode;

	@ApiModelProperty("true:查询差异数据")
	private Boolean differ = false;

	private List<Long> storeIdList;

	public void check(){
		QYAssert.isTrue(!StringUtil.isBlank(beginDate), "请选择送货日期");
		QYAssert.isTrue(!StringUtil.isBlank(endDate), "请选择送货日期");
		QYAssert.isTrue(CollectionUtils.isNotEmpty(commodityIdList), "商品不能为空");

		int diff = DateUtil.getDayDif(DateUtil.parseDate(endDate, DateUtil.DEFAULT_DATE_FORMAT), DateUtil.parseDate(beginDate, DateUtil.DEFAULT_DATE_FORMAT));
		QYAssert.isTrue(diff <= 9, "送货日期的跨度不能超过10天");
	}
}

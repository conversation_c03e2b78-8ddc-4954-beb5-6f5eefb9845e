package com.pinshang.qingyun.order.manage.dto.shopCount;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.pinshang.qingyun.renderer.annotation.FieldRender;
import com.pinshang.qingyun.renderer.constant.RenderFieldHelper;
import com.pinshang.qingyun.renderer.enums.FieldTypeEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @ClassName ShopCountStockPageODTO
 * <AUTHOR>
 * @Date 2022/12/8 18:22
 * @Description ShopCountStockPageODTO
 * @Version 1.0
 */
@Data
public class ShopCountStockPageODTO {
    @ApiModelProperty("门店名称")
    private String shopName;

    @ApiModelProperty("部门名称")
    @FieldRender(fieldType = FieldTypeEnum.SHOP_ORG,fieldName = RenderFieldHelper.Org.parentOrgName,keyName = "shopId")
    private String orgName;

    @ApiModelProperty("送货时间")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
    private String orderTime;

    @ApiModelProperty("实发商品")
    private Integer varietyTotal;

    @ApiModelProperty("已点商品")
    private Integer countNum;

    @ApiModelProperty("门店id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long shopId;

    @ApiModelProperty("仓库id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long storeId;
}

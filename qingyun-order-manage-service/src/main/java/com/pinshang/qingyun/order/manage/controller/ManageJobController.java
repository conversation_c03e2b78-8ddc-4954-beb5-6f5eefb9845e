package com.pinshang.qingyun.order.manage.controller;

import com.pinshang.qingyun.order.manage.service.JobService;
import com.pinshang.qingyun.order.manage.service.auto.ShopAutoOrderService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * @Author: sk
 * @Date: 2024/12/4
 */

@RestController
@RequestMapping("/orderManageJob")
public class ManageJobController {


    @Autowired
    private JobService jobService;
    @Autowired
    private ShopAutoOrderService shopAutoOrderService;

    /**
     * 门店自动订货job创建
     * @return
     */
    @PostMapping("/createShopAutoOrderJob")
    public Boolean createShopAutoOrderJob(){
        return jobService.createShopAutoOrderJob();
    }

    /**
     * 门店自动订货job执行
     * @param orderTime
     * @return
     * @throws Throwable
     */
    @PostMapping("/executeShopAutoOrderJob")
    public Boolean executeShopAutoOrderJob(@RequestParam(value = "orderTime",required = false) String orderTime){
        return jobService.executeShopAutoOrderJob(orderTime);
    }


    /**
     * 手动输入参数跑自动订货
     * @param orderTime
     * @param shopIdList
     * @return
     */
    @PostMapping("/testAutoOrder")
    public Boolean handAutoOrder(@RequestParam(value = "orderTime") String orderTime, @RequestParam(value = "shopIdList") List<Long> shopIdList){
        return shopAutoOrderService.handAutoOrder(orderTime, shopIdList);
    }


    /**
     * 自动订货的如果报错就当前截单时间重新异步跑一次
     * @return
     */
    @PostMapping("/autoOrderAgain")
    public Boolean autoOrderAgain(@RequestParam(value = "autoStr",required = false) String autoStr){
        shopAutoOrderService.againAutoOrderAsync(autoStr);
        return true;
    }

}

package com.pinshang.qingyun.order.manage.model;

import com.pinshang.qingyun.base.po.BasePO;

import javax.persistence.Entity;
import javax.persistence.Table;

/**
 * @Author: sk
 * @Date: 2022/9/1
 */
@Entity
@Table(name="t_delivery_cargo_order_param")
public class CloudOrderAllocationParam extends BasePO {

    /** 云超配送单分配入参,json保存 */
    private String param;

    /** 任务id */
    private Long taskId;

    /** 是否已下单 0 否  1 是 */
    private Integer orderStatus;


    public String getParam() {
        return param;
    }
    public void setParam(String param) {
        this.param = param;
    }

    public Long getTaskId() {
        return taskId;
    }

    public void setTaskId(Long taskId) {
        this.taskId = taskId;
    }

    public Integer getOrderStatus() {
        return orderStatus;
    }

    public void setOrderStatus(Integer orderStatus) {
        this.orderStatus = orderStatus;
    }
}

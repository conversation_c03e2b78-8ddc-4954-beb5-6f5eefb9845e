package com.pinshang.qingyun.order.manage.dto.export;/**
 * @Author: sk
 * @Date: 2025/7/10
 */

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2025年07月10日 下午2:49
 */
@Data
public class RealDeliveryStoreTypeReportExportODTO {

    @ExcelProperty("客户类型")
    private String storeTypeName;

    @ExcelProperty("商品编码")
    private String commodityCode;
    @ExcelProperty("商品名称")
    private String commodityName;
    @ExcelProperty("规格")
    private String commoditySpec;
    @ExcelProperty("条码")
    private String barCode;

    @ExcelProperty("一级品类")
    private String commodityFirstName;

    @ExcelProperty("计量单位")
    private String commodityUnit;

    @ExcelProperty("订货数")
    private String orderNum;

    @ExcelProperty("实发数量")
    private String deliveryNum;

    @ExcelProperty("实发金额")
    private String realDeliveryAmount;

    @ExcelProperty("默认仓库")
    private String warehouseName;

    @ExcelProperty("工厂")
    private String factoryName;
    @ExcelProperty("生产组")
    private String workshopName;
    @ExcelProperty("车间")
    private String flowshopName;
}

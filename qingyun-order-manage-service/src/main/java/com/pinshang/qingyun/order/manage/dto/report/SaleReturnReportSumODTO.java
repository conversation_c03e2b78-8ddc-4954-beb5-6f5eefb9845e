package com.pinshang.qingyun.order.manage.dto.report;

import lombok.Data;

import java.math.BigDecimal;
import java.math.RoundingMode;

@Data
public class SaleReturnReportSumODTO {

    private BigDecimal totalReturnPrice;//实退金额小计


    public BigDecimal getTotalReturnPrice() {
        return totalReturnPrice!=null?
                totalReturnPrice.setScale(2, RoundingMode.HALF_UP).stripTrailingZeros()
                : BigDecimal.ZERO;
    }
}

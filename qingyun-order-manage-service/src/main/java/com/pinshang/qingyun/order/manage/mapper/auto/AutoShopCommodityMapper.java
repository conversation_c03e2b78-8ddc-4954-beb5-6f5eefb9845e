package com.pinshang.qingyun.order.manage.mapper.auto;


import com.pinshang.qingyun.base.mybatis.MyMapper;
import com.pinshang.qingyun.order.manage.dto.auto.AutoShopCommodityODTO;
import com.pinshang.qingyun.order.manage.model.auto.AutoShopCommodity;
import com.pinshang.qingyun.order.manage.vo.auto.*;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 */
@Repository
public interface AutoShopCommodityMapper extends MyMapper<AutoShopCommodity> {

    /**
     * 查询门店自动订货的商品
     * @param vo
     * @return
     */
    List<AutoShopCommodityListVO> commodityByShopId(@Param("vo") AutoShopCommodityRequestVO vo);

    Long checkCommodityAuto(@Param("storeId") Long storeId, @Param("commodityId")Long commodityId);

    List<Long> getAutoCommodityList(@Param("shopId") Long shopId);

    /**
     * 商品基本信息
     * @param vo
     * @return
     */
    List<AutoShopCommodityListVO> commodityInfoList(@Param("vo") AutoShopCommodityRequestVO vo);

    /**
     * 获取商品信息
     * @param iogisticsModel
     * @return
     */
    List<DirectSendingCommodityVO> directSendingCommodityList(@Param("iogisticsModel") Integer iogisticsModel);

    List<AutoShopCommodityODTO> queryAllAutoShopCommodity();

    AutoCommodityVO commodityInfo(@Param("shopId") Long shopId, @Param("barCode") String barCode);

    /**
     * 根据门店和商品code查询商品信息
     * @param shopId
     * @param commodityCodes
     * @return
     */
    List<ImportStockQuantityVO> listByShopAndCommodityCode(@Param("shopId") Long shopId, @Param("list") List<String> commodityCodes);


}
package com.pinshang.qingyun.order.manage.controller;

import com.github.pagehelper.PageInfo;
import com.pinshang.qingyun.order.manage.dto.auto.AutoCommodityIDTO;
import com.pinshang.qingyun.order.manage.dto.auto.AutoCommodityODTO;
import com.pinshang.qingyun.order.manage.service.auto.AutoCommodityOrderService;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.ss.usermodel.WorkbookFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.InputStream;
import java.util.ArrayList;
import java.util.List;

/**
 * @Author: liuZhen
 * @DateTime: 2022/5/10 18:30
 */
@Slf4j
@RequestMapping("/autoOrder")
@RestController
public class AutoCommodityOrderController {
    @Autowired
    private AutoCommodityOrderService autoCommodityOrderService;

    /**
     * 导入商品
     *
     * @param file
     * @return
     */
    @ApiOperation(value = "导入商品列表")
    @RequestMapping(value = "/commodityIdsExcelImport", method = RequestMethod.POST)
    public List<String> commodityIdsExcelImport(@RequestParam(value = "file", required = true) MultipartFile file) {
        Workbook wb = null;
        try {
            InputStream in = file.getInputStream();
            wb = WorkbookFactory.create(in);
        } catch (Exception e) {
            log.error("导入门店自动订货商品池",e);
        }
        return autoCommodityOrderService.commodityIdsExcelImport(wb);
    }

    /**
     * 删除
     *
     * @param commodityId
     * @return
     */
    @GetMapping("deleteByCommodityId/{commodityId}")
    public Boolean deleteByCommodityId(@PathVariable("commodityId") String commodityId) {
        return autoCommodityOrderService.deleteByCommodityId(commodityId);
    }

    /**
     * 查询
     *
     * @param idto
     * @return
     */
    @PostMapping("queryAutoCommodity")
    public PageInfo<AutoCommodityODTO> queryAutoCommodity(@RequestBody AutoCommodityIDTO idto) {
        return autoCommodityOrderService.queryAutoCommodity(idto);
    }
    @ApiOperation(value = "导出门店自动订货商品列表")
    @PostMapping("/exportList")
    public List<AutoCommodityODTO> exportList(@RequestBody AutoCommodityIDTO vo) {
        vo.initExportPage();
        PageInfo<AutoCommodityODTO> pageDate = autoCommodityOrderService.queryAutoCommodity(vo);
        if (pageDate.getSize() > 0) {
            return pageDate.getList();
        }
        return new ArrayList<>();
    }

}

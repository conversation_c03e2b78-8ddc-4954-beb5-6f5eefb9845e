package com.pinshang.qingyun.order.manage.dto.report;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.pinshang.qingyun.base.enums.IogisticsModelEnums;
import com.pinshang.qingyun.order.manage.enums.ShopPreOrderStatusEnums;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.Date;

/**
 * @Author: liuZhen
 * @DateTime: 2022/5/12 17:17
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class AutoPreOrderODTO {
    private Long id;
    @ApiModelProperty("门店")
    private String shopName;
    @ApiModelProperty("物流模式")
    private Integer logisticsModel;
    private String logisticsModelStr;
    @ApiModelProperty("加货申请单号")
    private String orderCode;
    @ApiModelProperty("申请单金额")
    private BigDecimal orderAmount;
    @ApiModelProperty("申请人")
    private String createName;
    @ApiModelProperty("申请时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date createTime;
    @ApiModelProperty("处理人")
    private String auditName;
    @ApiModelProperty("处理时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date auditTime;
    @ApiModelProperty("状态")
    private Integer status;
    private String statusStr;
    @ApiModelProperty("订单编号")
    private String orderId;
    @ApiModelProperty("送货日期")
    @JsonFormat(pattern = "yyyy-MM-dd",timezone = "GMT+8")
    private Date deliveryTime;
    @ApiModelProperty("订货时间")
    private  String storeOrderTime;
    private Long shopId;

    public void setLogisticsModel(Integer logisticsModel) {
        this.logisticsModel = logisticsModel;
        this.logisticsModelStr= IogisticsModelEnums.getName(logisticsModel);
    }

    public void setStatus(Integer status) {
        this.status = status;
        this.statusStr = ShopPreOrderStatusEnums.getDescByCode(status);
    }
}

package com.pinshang.qingyun.order.manage.dto.settlement;

import com.pinshang.qingyun.base.page.Pagination;
import com.pinshang.qingyun.renderer.annotation.FieldRender;
import com.pinshang.qingyun.renderer.constant.RenderFieldHelper;
import com.pinshang.qingyun.renderer.enums.FieldTypeEnum;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * Created by hhf on 2018/6/12.
 * 结算明细报表
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class SettlementDetailsReportVo extends Pagination {

    /**门店**/
    private Long shopId;

    /**送货日期-开始**/
    private String deliveryTimeStart;

    /**送货日期-结束**/
    private String deliveryTimeEnd;

    /**结算日期-开始**/
    private String settleTimeStart;

    /**结算日期-结束**/
    private String settleTimeEnd;

    /**物流模式 0-直送, 1-配送, 2-直通**/
    private Integer logisticsModel;

    /**分类**/
    private Long categoryId;

    /**条码**/
    private String barCode;

    /**商品**/
    private String commodityCode;

    /**订单编号**/
    private String subOrderCode;

    /**预订单编号**/
    private String preOrderCode;

    /**客户类型**/
    private Long storeTypeId;

    //门店id集合:当前登录用户所关联门店列表
    private List<Long> shopIdList;

    /**订单编号集合**/
    private List<Long> subOrderIdList;

    //客户类型id集合
    private List<Long> storeTypeIdList;

    /**代销商id**/
    private Long consignmentId;

    /**订单来源: 参考: OrderType  枚举**/
    private Integer orderType;


    /**档口id**/
    private Long stallId;

    /**档口名称**/
    @FieldRender(fieldType = FieldTypeEnum.STALL, fieldName = RenderFieldHelper.Stall.stallName, keyName = "stallId")
    private String stallName;


}

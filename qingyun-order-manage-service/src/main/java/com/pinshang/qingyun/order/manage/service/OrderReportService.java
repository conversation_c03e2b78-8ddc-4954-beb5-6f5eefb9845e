package com.pinshang.qingyun.order.manage.service;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.pinshang.qingyun.base.api.TokenInfo;
import com.pinshang.qingyun.base.configure.expand.QYAssert;
import com.pinshang.qingyun.base.enums.StoreBillTypeEnums;
import com.pinshang.qingyun.base.enums.shop.ManagementModeEnums;
import com.pinshang.qingyun.base.enums.shop.ShopTypeEnums;
import com.pinshang.qingyun.base.page.TablePageInfo;
import com.pinshang.qingyun.base.service.DdTokenShopIdService;
import com.pinshang.qingyun.base.util.FastThreadLocalUtil;
import com.pinshang.qingyun.box.utils.*;
import com.pinshang.qingyun.common.dto.DictionaryODTO;
import com.pinshang.qingyun.common.service.DictionaryClient;
import com.pinshang.qingyun.order.manage.dto.CommodityBasicODTO;
import com.pinshang.qingyun.order.manage.dto.XsjmOrderBillSummaryReportDTO;
import com.pinshang.qingyun.order.manage.dto.report.*;
import com.pinshang.qingyun.order.manage.enums.SaleReturnStatusEnums;
import com.pinshang.qingyun.order.manage.mapper.CommodityMapper;
import com.pinshang.qingyun.order.manage.mapper.OrderReportMapper;
import com.pinshang.qingyun.order.manage.mapper.SaleReturnOrderPicMapper;
import com.pinshang.qingyun.order.manage.mapper.ShopMapper;
import com.pinshang.qingyun.order.manage.mapper.entry.StoreEntry;
import com.pinshang.qingyun.order.manage.model.SaleReturnOrderPic;
import com.pinshang.qingyun.order.manage.model.Shop;
import com.pinshang.qingyun.order.manage.vo.CommodityVO;
import com.pinshang.qingyun.order.manage.vo.report.ReturnOrderDetailsReportVo;
import com.pinshang.qingyun.order.manage.vo.report.XsjmOrderBillSummaryReportReqVO;
import com.pinshang.qingyun.order.manage.vo.report.XsjmOrderBillSummaryReportRspVO;
import com.pinshang.qingyun.price.service.CommodityPriceClient;
import com.pinshang.qingyun.product.dto.commodity.CommodityDetailODTO;
import com.pinshang.qingyun.product.service.CommodityClient;
import com.pinshang.qingyun.shop.dto.ShopDto;
import com.pinshang.qingyun.shop.service.ShopClient;
import com.pinshang.qingyun.smm.dto.user.SelectUserShopIdListIDTO;
import com.pinshang.qingyun.smm.dto.userstall.SelectUserStallIdListIDTO;
import com.pinshang.qingyun.smm.service.SMMUserClient;
import com.pinshang.qingyun.smm.service.UserStallClient;
import com.pinshang.qingyun.storage.dto.CommoditySupplierODto;
import com.pinshang.qingyun.storage.dto.CommodityWarehouseODto;
import com.pinshang.qingyun.storage.service.CommoditySupplierClient;
import com.pinshang.qingyun.storage.service.CommodityWarehouseClient;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.joda.time.DateTime;
import org.joda.time.format.DateTimeFormat;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Example;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.ParseException;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static java.util.stream.Collectors.toList;

@Service
public class OrderReportService {
    @Autowired
    OrderReportMapper orderReportMapper;

    @Autowired
    private DictionaryClient dictionaryClient;
    @Autowired
    private ShopService shopService;

    @Autowired
    private CommodityMapper commodityMapper;
    @Autowired
    private CommodityWarehouseClient commodityWarehouseClient;
    @Autowired
    private CommodityClient commodityClient;
    @Autowired
    private CommodityPriceClient commodityPriceClient;
    @Autowired
    private CommoditySupplierClient commoditySupplierClient;
    @Autowired
    private CommodityService commodityService;
    @Autowired
    private ShopClient shopClient;
    @Autowired
    private UserStallClient userStallClient;
    @Autowired
    private SMMUserClient sMMUserClient;
    @Autowired
    private SaleReturnOrderPicMapper saleReturnOrderPicMapper;
    @Value("${pinshang.img-server-url}")
    private String imgServerUrl;
    @Autowired
    private SMMUserClient smmUserClient;
    @Autowired
    private DdTokenShopIdService ddTokenShopIdService;
    @Autowired
    private ShopMapper shopMapper;

    public PageInfo<ReturnOrderDetailsReportEntry> findReturnOrderDetailsReport(ReturnOrderDetailsReportVo returnOrderDetailsReportVo)  {
        TokenInfo ti = FastThreadLocalUtil.getQY();
        ddTokenShopIdService.processReadDdTokenShopId(ti.getShopId(), returnOrderDetailsReportVo.getStallId());

        QYAssert.isTrue(StringUtils.isNotBlank(returnOrderDetailsReportVo.getOrderTimeEndStr()), "请选择截至日期");
        QYAssert.isTrue(StringUtils.isNotBlank(returnOrderDetailsReportVo.getOrderTimeStartStr()), "请选择起始日期");
        if (returnOrderDetailsReportVo.getShopId() == null) {
            returnOrderDetailsReportVo.setShopId(ti.getShopId());
        }

        String orderTimeStartStr = returnOrderDetailsReportVo.getOrderTimeStartStr();
        String orderTimeEndStr = returnOrderDetailsReportVo.getOrderTimeEndStr();

        if(StringUtils.isNotBlank(orderTimeStartStr) && StringUtils.isNotBlank(orderTimeEndStr)){
            returnOrderDetailsReportVo.setOrderTimeStartStr(orderTimeStartStr + " 00:00:00");
            returnOrderDetailsReportVo.setOrderTimeEndStr(orderTimeEndStr + " 23:59:59");
        }

        PageInfo<ReturnOrderDetailsReportEntry> pageData = PageHelper.startPage(returnOrderDetailsReportVo.getPageNo(), returnOrderDetailsReportVo.getPageSize()).doSelectPageInfo(() -> {
            orderReportMapper.findReturnOrderDetails(returnOrderDetailsReportVo);
        });
        List<ReturnOrderDetailsReportEntry> list = pageData.getList();
        if(CollectionUtils.isNotEmpty(list)){
            List<Long> storeIdList = list.stream().distinct().map(item -> item.getStoreId()).collect(Collectors.toList());
            List<Long> commodityIdList = list.stream().distinct().map(item -> item.getCommodityId()).collect(Collectors.toList());

            List<DictionaryODTO> dictList = dictionaryClient.listDictionaryByOptionName("门店退货原因");
            Map<String, String> dictMap = dictList.stream().collect(Collectors.toMap(DictionaryODTO::getOptionCode,DictionaryODTO::getOptionName,(key1 , key2)-> key2));

            List<Shop> shopList = shopService.getShopByStoreIdList(storeIdList);
            Map<Long, String> shopMap = shopList.stream().collect(Collectors.toMap(Shop::getStoreId,Shop::getShopName,(key1 , key2)-> key2));

            Map<Long, String> commodityBasicMap = commodityService.getCommodityBarCodeMap(commodityIdList, null);

            for(ReturnOrderDetailsReportEntry entry : list){
                entry.setShopName(shopMap.get(entry.getStoreId()));
                entry.setReturnReasonName(dictMap.get(entry.getReturnReason() + ""));
                entry.setBarCodes(commodityBasicMap.get(entry.getCommodityId()));

                BigDecimal price = entry.getPrice();
                BigDecimal quantity = entry.getReturnQuantity();
                if(price != null && quantity != null) {
                    entry.setTotalPrice(price.multiply(quantity).setScale(2,BigDecimal.ROUND_HALF_UP));
                }
            }
        }
        return pageData;
    }


    /**
     * 短交报表
     * @param vo
     * @return
     */
    public PageInfo<ShortDeliveryReportODto> shortDeliveryReport(ShortDeliveryReportIDto vo) {
        TokenInfo tokenInfo = FastThreadLocalUtil.getQY();
        PageInfo<ShortDeliveryReportODto> pageDate = new PageInfo<>();

        QYAssert.isTrue(!StringUtil.isBlank(vo.getBeginDate()), "请选择送货日期");
        QYAssert.isTrue(!StringUtil.isBlank(vo.getEndDate()), "请选择送货日期");
        QYAssert.isTrue(DateUtil.isAfter(DateUtil.addMonth(DateTimeUtil.parse(vo.getBeginDate(), "yyyy-MM-dd"),1), DateTimeUtil.parse(vo.getEndDate(), "yyyy-MM-dd")), "送货日期范围不能超过一个月!");
        vo.setShopId((vo.getShopId() != null && vo.getShopId() == 0 ) ? null : vo.getShopId());
        /*if(!StringUtil.isBlank(vo.getBeginDate()) && !StringUtil.isBlank(vo.getEndDate())){
            vo.setBeginDate(vo.getBeginDate()+ " 00:00:00");
            vo.setEndDate(vo.getEndDate()+ " 23:59:59");
        }*/

        if (!StringUtil.isNullOrEmpty(vo.getOrgCode())) {
            List<ShopDto> shopDtos = shopClient.selectShopListByParentOrgCode(vo.getOrgCode());
            if (!org.springframework.util.CollectionUtils.isEmpty(shopDtos)) {
                List<Long> orgShopIdList = shopDtos.stream().map(ShopDto::getId).collect(Collectors.toList());
                if(CollectionUtils.isNotEmpty(orgShopIdList)){
                    vo.setShopIdList(orgShopIdList);
                }else {
                    return pageDate;
                }
            }else {
                return pageDate;
            }
        }

        if(vo.getShopType() != null){
           List<Long> storeIdList = shopService.getStoreIdListByShopType(vo.getShopType());
           if(CollectionUtils.isNotEmpty(storeIdList)){
               vo.setStoreIdList(storeIdList);
           }else {
               return pageDate;
           }
        }

        if(CollectionUtils.isNotEmpty(vo.getShopTypeList()) || CollectionUtils.isNotEmpty(vo.getManagementModeList())) {
            List<Long> shopIdList = shopMapper.queryShopIdListByParam(vo.getShopTypeList(), vo.getManagementModeList());
            if(CollectionUtils.isEmpty(shopIdList)) {
                return new TablePageInfo<>();
            }else {
                List<Long> ids = vo.getShopIdList();
                if(CollectionUtils.isEmpty(ids)) {
                    vo.setShopIdList(shopIdList);
                }else {
                    ids.retainAll(shopIdList);
                    if(CollectionUtils.isEmpty(ids)) {
                        return new TablePageInfo<>();
                    }else {
                        vo.setShopIdList(ids);
                    }
                }
            }
        }

        // 单门店登录，如果是大店判断档口权限
        if(tokenInfo.getShopId() != null && tokenInfo.getShopId() > 0){
            //判断登录人门店是不是工坊店
            ShopDto shopDto = shopClient.findShopById(vo.getShopId());
            QYAssert.isTrue(shopDto!=null,"没有查到登录人门店权限");
            if(ShopTypeEnums.GF.getCode().equals(shopDto.getShopType())){
                //非总部的档口查询需要判断用户档口权限
                SelectUserStallIdListIDTO userStallIdListIDTO = new SelectUserStallIdListIDTO();
                userStallIdListIDTO.setUserId(FastThreadLocalUtil.getQY().getUserId());
                userStallIdListIDTO.setShopId(FastThreadLocalUtil.getQY().getShopId());
                userStallIdListIDTO.setStallStatus(1);
                List<Long> stallList = userStallClient.selectUserStallIdList(userStallIdListIDTO);
                if(CollectionUtils.isNotEmpty(stallList)){
                    if(vo.getStallId() == null){
                        vo.setStallList(stallList);
                    }
                    if(vo.getStallId() != null && !stallList.contains(vo.getStallId())){
                        return new PageInfo();
                    }
                }else {
                    //沒有档口权限直接返回空
                    return pageDate;
                }
            }
        }
        List<Long> commodityIdListAll = new ArrayList<>();
        Boolean isCheck = false;
        if(StringUtils.isNotBlank(vo.getFactoryCode())){
            isCheck = true;
            List<Long> commodityIdList = commodityClient.findCommodityIdListByParam(vo.getFactoryCode(),"","");
            if(CollectionUtils.isNotEmpty(commodityIdList)){
                if(CollectionUtils.isNotEmpty(commodityIdListAll)){
                    commodityIdListAll.retainAll(commodityIdList);
                }else {
                    commodityIdListAll.addAll(commodityIdList);
                }
            }else {
                return pageDate;
            }
        }
        if(vo.getCateId1() != null || vo.getCateId2() != null || vo.getCateId3() != null
                || StringUtils.isNotBlank(vo.getCommodityKey()) || StringUtils.isNotBlank(vo.getBarCode())){
            isCheck = true;
            CommodityVO commodityVO = new CommodityVO();
            BeanUtils.copyProperties(vo,commodityVO);
            List<CommodityBasicODTO> commList = commodityMapper.findCommodityBasicListByParam(commodityVO);
            if(CollectionUtils.isNotEmpty(commList)){
                List<Long> commodityIdList = commList.stream().map(item -> Long.valueOf(item.getCommodityId())).collect(Collectors.toList());
                if(CollectionUtils.isNotEmpty(commodityIdListAll)){
                    commodityIdListAll.retainAll(commodityIdList);
                }else {
                    commodityIdListAll.addAll(commodityIdList);
                }
            }else {
                return  pageDate;
            }
        }
        if(isCheck && CollectionUtils.isEmpty(commodityIdListAll)){
            return  pageDate;
        }
        vo.setCommodityIdList(commodityIdListAll);

        pageDate  = PageHelper.startPage(vo.getPageNo(), vo.getPageSize()).doSelectPageInfo(() -> {
            orderReportMapper.shortDeliveryTodayReport(vo);
        });

        List<ShortDeliveryReportODto> list = pageDate.getList();
        // 设置返回值
        setShortDelivery(list);
        return pageDate ;
    }

    /**
     * 短交报表
     * @param list
     */
    private void setShortDelivery(List<ShortDeliveryReportODto> list) {
        if(CollectionUtils.isNotEmpty(list)){
            List<Long> commodityIdList = list.stream().map(item -> Long.valueOf(item.getCommodityId())).distinct().collect(Collectors.toList());
            Map<Long, CommodityWarehouseODto> warehouseMap = commodityWarehouseClient.queryCommodityDefaultWarehouse(commodityIdList);
            Map<Long,String> barCodeMap = commodityService.getCommodityBarCodeMap(commodityIdList,null);

            /*CommodityVO commodityVO = new CommodityVO();
            commodityVO.setCommodityIdList(commodityIdList);
            List<CommodityBasicEntry> commList = commodityMapper.findCommodityBasicListByParam(commodityVO);
            Map<String, CommodityBasicEntry> commMap = commList.stream().collect(Collectors.toMap(CommodityBasicEntry::getCommodityId, Function.identity()));

            List<Long> cateIdList = new ArrayList<>();
            cateIdList.addAll(commList.stream().map(item -> Long.valueOf(item.getCommodityFirstId())).collect(Collectors.toList()));
            cateIdList.addAll(commList.stream().map(item -> Long.valueOf(item.getCommoditySecondId())).collect(Collectors.toList()));
            cateIdList.addAll(commList.stream().map(item -> Long.valueOf(item.getCommodityThirdId())).collect(Collectors.toList()));
            Map<String, String> cateMap = commonService.getCategroyMap(cateIdList);*/

            //Map<String, String> dictionaryMap = commonService.getDictionaryMap();
            Map<String, CommodityDetailODTO> factoryCommodityMap = commodityService.getFactoryMap(commodityIdList);

           /* List<Long> createIdList = list.stream().map(item -> item.getCreateId()).distinct().collect(Collectors.toList());
            Map<Long, User> userMap = commonService.getUserMap(createIdList);*/

            List<Long> storeIdList = list.stream().map(item -> item.getStoreId()).distinct().collect(Collectors.toList());
            List<StoreEntry> storeList = shopService.getStoreByStoreIdList(storeIdList);
            Map<Long, StoreEntry> storeMap = storeList.stream().collect(Collectors.toMap(StoreEntry::getStoreId, Function.identity()));

            for(ShortDeliveryReportODto entry : list){
                CommodityWarehouseODto commodityWarehouseODto = warehouseMap.get(Long.valueOf(entry.getCommodityId()));
                if(commodityWarehouseODto != null){
                    entry.setWarehouseName(commodityWarehouseODto.getWarehouseName());
                }
                /*CommodityBasicEntry commodityBasic = commMap.get(entry.getCommodityId());
                if(commodityBasic != null){
                    BeanUtils.copyProperties(commodityBasic,entry);
                    entry.setCategoryName(cateMap.get(commodityBasic.getCommodityFirstId() + ""));
                    entry.setSecondCategoryName(cateMap.get(commodityBasic.getCommoditySecondId() + ""));
                    entry.setThirdCategoryName(cateMap.get(commodityBasic.getCommodityThirdId() + ""));
                }*/
                CommodityDetailODTO commodityDetail = factoryCommodityMap.get(entry.getCommodityId());
                if(commodityDetail != null){
                    BeanUtils.copyProperties(commodityDetail,entry);
                }
               /* User user = userMap.get(entry.getCreateId());
                if(user != null){
                    entry.setCreateName(user.getEmployeeName());
                }*/
                StoreEntry storeEntry = storeMap.get(entry.getStoreId());
                if(storeEntry != null){
                    BeanUtils.copyProperties(storeEntry,entry);
                }
                String barCodes = barCodeMap.get(Long.valueOf(entry.getCommodityId()));
                entry.setBarCodes(barCodes);
                entry.setBarCode(barCodes != null ? barCodes.split(",")[0] : "");

                // entry.setCommodityUnitName(dictionaryMap.get(entry.getCommodityUnitId() + ""));

            }
        }
    }




    /**
     * 商品实发汇总表(当日)
     * @param vo
     * @return
     */
    public TablePageInfo<RealDeliveryReportODto> realDeliveryReportCurrentDay(RealDeliveryReportIDto vo) {
        TablePageInfo tablePageInfo = new TablePageInfo();
        QYAssert.isTrue(!StringUtil.isBlank(vo.getBeginDate()), "请选择送货日期");
        QYAssert.isTrue(!StringUtil.isBlank(vo.getEndDate()), "请选择送货日期");

        List<Long> commodityIdListAll = new ArrayList<>();
        Boolean isCheck = false;

        if(StringUtils.isNotBlank(vo.getFactoryCode())
                || StringUtils.isNotBlank(vo.getWorkshopCodeOrName())
                || StringUtils.isNotBlank(vo.getFlowshopCodeOrName())){
            isCheck = true;
            List<Long> commodityIdList = commodityClient.findCommodityIdListByParam(vo.getFactoryCode(),vo.getWorkshopCodeOrName(),vo.getFlowshopCodeOrName());
            if(CollectionUtils.isNotEmpty(commodityIdList)){
                if(CollectionUtils.isNotEmpty(commodityIdListAll)){
                    commodityIdListAll.retainAll(commodityIdList);
                }else {
                    commodityIdListAll.addAll(commodityIdList);
                }
            }else {
                return tablePageInfo;
            }
        }
        if(vo.getCategoryId() != null || StringUtils.isNotBlank(vo.getCommodityKey()) || StringUtils.isNotBlank(vo.getBarCode())){
            isCheck = true;
            CommodityVO commodityVO = new CommodityVO();
            commodityVO.setCommodityKey(vo.getCommodityKey());
            commodityVO.setCateId1(vo.getCategoryId());
            commodityVO.setBarCode(vo.getBarCode());
            List<CommodityBasicODTO> commList = commodityMapper.findCommodityBasicListByParam(commodityVO);
            if(CollectionUtils.isNotEmpty(commList)){
                List<Long> commodityIdList = commList.stream().map(item -> Long.valueOf(item.getCommodityId())).collect(Collectors.toList());
                if(CollectionUtils.isNotEmpty(commodityIdListAll)){
                    commodityIdListAll.retainAll(commodityIdList);
                }else {
                    commodityIdListAll.addAll(commodityIdList);
                }
            }else {
                return  tablePageInfo;
            }
        }
        if(isCheck && CollectionUtils.isEmpty(commodityIdListAll)){
            return  tablePageInfo;
        }
        vo.setCommodityIdList(commodityIdListAll);

        RealDeliveryReportDataQueryIDto iDto = new RealDeliveryReportDataQueryIDto();
        iDto.setBeginDate(vo.getBeginDate());
        iDto.setEndDate(vo.getEndDate());
        iDto.setDiffer(vo.getDiffer());
        iDto.setCommodityIdList(vo.getCommodityIdList());
        iDto.setPageNo(vo.getPageNo());
        iDto.setPageSize(vo.getPageSize());
        iDto.setConsignmentId(vo.getConsignmentId());

        PageInfo<RealDeliveryReportODto>  pageDate = PageHelper.startPage(vo.getPageNo(), vo.getPageSize()).doSelectPageInfo(() -> {
            orderReportMapper.realDeliveryReportCurrentDay(iDto);
        });   // 设置返回值
        BigDecimal realTotalAmount = setRealDeliveryReport(iDto, pageDate.getList());
        tablePageInfo = BeanUtil.pageInfo2TablePageInfo(pageDate, TablePageInfo.class);
        tablePageInfo.setHeader(realTotalAmount);
        return tablePageInfo;
    }

    /**
     * 商品实发汇总表(当日)
     * @param vo
     * @param list
     * @return
     */
    private BigDecimal setRealDeliveryReport(RealDeliveryReportDataQueryIDto vo, List<RealDeliveryReportODto> list) {
        BigDecimal realTotalAmount = BigDecimal.ZERO;
        if(null !=list && !list.isEmpty()){
            List<Long> commodityIdList = list.stream().map(item -> Long.valueOf(item.getCommodityId())).distinct().collect(Collectors.toList());
            Map<Long,String> barCodeMap = commodityService.getCommodityBarCodeMap(commodityIdList,null);

            CommodityVO commodityVO = new CommodityVO();
            commodityVO.setCommodityIdList(commodityIdList);
            List<CommodityBasicODTO> commList = commodityMapper.findCommodityBasicListByParam(commodityVO);
            Map<String, CommodityBasicODTO> commMap = commList.stream().collect(Collectors.toMap(CommodityBasicODTO::getCommodityId, Function.identity()));

            List<Long> cateIdList = new ArrayList<>();
            cateIdList.addAll(commList.stream().map(item -> Long.valueOf(item.getCommodityFirstId())).collect(Collectors.toList()));
            //Map<String, String> cateMap = commonService.getCategroyMap(cateIdList);
            Map<String, CommodityDetailODTO> factoryCommodityMap = commodityService.getFactoryMap(commodityIdList);
           // Map<String, String> dictionaryMap = commonService.getDictionaryMap();

            for(RealDeliveryReportODto entry:list){
                CommodityBasicODTO commodityBasic = commMap.get(entry.getCommodityId() + "");
                if(commodityBasic != null){
                    BeanUtils.copyProperties(commodityBasic,entry);
                }
                CommodityDetailODTO commodityDetail = factoryCommodityMap.get(entry.getCommodityId() + "");
                if(commodityDetail != null){
                    entry.setFactoryName(commodityDetail.getFactoryName());
                    entry.setWorkshopName(commodityDetail.getWorkshopName());
                    entry.setFlowshopName(commodityDetail.getFlowshopName());
                }

                String barCodes = barCodeMap.get(Long.valueOf(entry.getCommodityId()));
                entry.setBarCodes(barCodes);
                entry.setBarCode(barCodes != null ? barCodes.split(",")[0] : "");
                entry.setRealDeliveryAmount(null != entry.getRealDeliveryAmount() ? entry.getRealDeliveryAmount().setScale(2, BigDecimal.ROUND_HALF_UP) : BigDecimal.ZERO);

            }
            //获取实发总金额
            realTotalAmount = orderReportMapper.realTotalDeliveryReportCurrentDay(vo);
            if(realTotalAmount==null){
                realTotalAmount = BigDecimal.ZERO;
            }
            realTotalAmount = realTotalAmount.setScale(2, BigDecimal.ROUND_HALF_UP);
        }
        return realTotalAmount;
    }




    /**
     * 商品实发汇总表(当日)---------------(按客户类型)
     * @param vo
     * @return
     */
    public TablePageInfo<RealDeliveryReportODto> realDeliveryStoreTypeReportCurrentDay(RealDeliveryReportIDto vo) {
        TablePageInfo tablePageInfo = new TablePageInfo();
        QYAssert.isTrue(!StringUtil.isBlank(vo.getBeginDate()), "请选择送货日期");
        QYAssert.isTrue(!StringUtil.isBlank(vo.getEndDate()), "请选择送货日期");

        List<Long> commodityIdListAll = new ArrayList<>();
        Boolean isCheck = false;
        if(vo.getWarehouseId() != null){
            isCheck = true;
            List<Long> commodityIdList = commodityWarehouseClient.queryCommodityIdsByDefaultWarehouseid(vo.getWarehouseId());
            if(CollectionUtils.isNotEmpty(commodityIdList)){
                if(CollectionUtils.isNotEmpty(commodityIdListAll)){
                    commodityIdListAll.retainAll(commodityIdList);
                }else {
                    commodityIdListAll.addAll(commodityIdList);
                }
            }else {
                return tablePageInfo;
            }
        }

        if(StringUtils.isNotBlank(vo.getFactoryCode())
                || StringUtils.isNotBlank(vo.getWorkshopCodeOrName())
                || StringUtils.isNotBlank(vo.getFlowshopCodeOrName())){
            isCheck = true;
            List<Long> commodityIdList = commodityClient.findCommodityIdListByParam(vo.getFactoryCode(),vo.getWorkshopCodeOrName(),vo.getFlowshopCodeOrName());
            if(CollectionUtils.isNotEmpty(commodityIdList)){
                if(CollectionUtils.isNotEmpty(commodityIdListAll)){
                    commodityIdListAll.retainAll(commodityIdList);
                }else {
                    commodityIdListAll.addAll(commodityIdList);
                }
            }else {
                return tablePageInfo;
            }
        }
        if(vo.getCategoryId() != null || StringUtils.isNotBlank(vo.getCommodityKey()) || StringUtils.isNotBlank(vo.getBarCode())){
            isCheck = true;
            CommodityVO commodityVO = new CommodityVO();
            commodityVO.setCommodityKey(vo.getCommodityKey());
            commodityVO.setCateId1(vo.getCategoryId());
            commodityVO.setBarCode(vo.getBarCode());
            List<CommodityBasicODTO> commList = commodityMapper.findCommodityBasicListByParam(commodityVO);
            if(CollectionUtils.isNotEmpty(commList)){
                List<Long> commodityIdList = commList.stream().map(item -> Long.valueOf(item.getCommodityId())).collect(Collectors.toList());
                if(CollectionUtils.isNotEmpty(commodityIdListAll)){
                    commodityIdListAll.retainAll(commodityIdList);
                }else {
                    commodityIdListAll.addAll(commodityIdList);
                }
            }else {
                return  tablePageInfo;
            }
        }

        if(isCheck && CollectionUtils.isEmpty(commodityIdListAll)){
            return  tablePageInfo;
        }
        vo.setCommodityIdList(commodityIdListAll);

        PageInfo<RealDeliveryReportODto> pageDate = null;
        pageDate = PageHelper.startPage(vo.getPageNo(), vo.getPageSize()).doSelectPageInfo(() -> {
            orderReportMapper.realDeliveryStoreTypeReportCurrentDay(vo);
        });
        List<RealDeliveryReportODto> list = pageDate.getList();
        // 设置返回值
        BigDecimal realTotalAmount = setRealDeliveryStoreTypeReport(vo, list);
        tablePageInfo = BeanUtil.pageInfo2TablePageInfo(pageDate, TablePageInfo.class);
        tablePageInfo.setHeader(realTotalAmount);
        return tablePageInfo;
    }

    /**
     * 商品实发汇总表(当日)---------------(按客户类型)
     * @param vo
     * @param list
     * @return
     */
    private BigDecimal setRealDeliveryStoreTypeReport(RealDeliveryReportIDto vo, List<RealDeliveryReportODto> list) {
        BigDecimal realTotalAmount = BigDecimal.ZERO;
        if(CollectionUtils.isNotEmpty(list)){
            List<Long> commodityIdList = list.stream().map(item -> Long.valueOf(item.getCommodityId())).distinct().collect(Collectors.toList());
            Map<Long, CommodityWarehouseODto> wareHouseMap = commodityWarehouseClient.queryCommodityDefaultWarehouse(commodityIdList);
            Map<Long,String> barCodeMap = commodityService.getCommodityBarCodeMap(commodityIdList,null);

            CommodityVO commodityVO = new CommodityVO();
            commodityVO.setCommodityIdList(commodityIdList);
            List<CommodityBasicODTO> commList = commodityMapper.findCommodityBasicListByParam(commodityVO);
            Map<String, CommodityBasicODTO> commMap = commList.stream().collect(Collectors.toMap(CommodityBasicODTO::getCommodityId, Function.identity()));

            /*List<Long> cateIdList = new ArrayList<>();
            cateIdList.addAll(commList.stream().map(item -> Long.valueOf(item.getCommodityFirstId())).collect(Collectors.toList()));
            Map<String, String> cateMap = commonService.getCategroyMap(cateIdList);*/

            Map<String, CommodityDetailODTO> factoryCommodityMap = commodityService.getFactoryMap(commodityIdList);

            // Map<String, String> dictionaryMap = commonService.getDictionaryMap();

            for(RealDeliveryReportODto entry:list){
                CommodityBasicODTO commodityBasic = commMap.get(entry.getCommodityId() + "");
                if(commodityBasic != null){
                    BeanUtils.copyProperties(commodityBasic,entry);
                }
                CommodityDetailODTO commodityDetail = factoryCommodityMap.get(entry.getCommodityId() + "");
                if(commodityDetail != null){
                    entry.setFactoryName(commodityDetail.getFactoryName());
                    entry.setWorkshopName(commodityDetail.getWorkshopName());
                    entry.setFlowshopName(commodityDetail.getFlowshopName());
                }

                // entry.setStoreTypeName(dictionaryMap.get(entry.getStoreTypeId() + ""));
                String barCodes = barCodeMap.get(Long.valueOf(entry.getCommodityId()));
                entry.setBarCodes(barCodes);
                entry.setBarCode(barCodes != null ? barCodes.split(",")[0] : "");
                entry.setRealDeliveryAmount(null != entry.getRealDeliveryAmount() ? entry.getRealDeliveryAmount().setScale(2, BigDecimal.ROUND_HALF_UP) : BigDecimal.ZERO);

                CommodityWarehouseODto warehouseODto = wareHouseMap.get(entry.getCommodityId());
                if(warehouseODto != null){
                    entry.setWarehouseName(warehouseODto.getWarehouseName());
                }
            }
            //获取实发总金额
            realTotalAmount = orderReportMapper.realTotalDeliveryStoreTypeReportCurrentDay(vo);
            if(realTotalAmount != null) {
                realTotalAmount = realTotalAmount.setScale(2, BigDecimal.ROUND_HALF_UP);
            }
        }
        return realTotalAmount;
    }





    /**
     * 门店订货汇总表(当日)
     * @param idto
     * @return
     */
    public PageInfo<ShopOrderGoodReportODto> shopOrderGoodReportCurrentDay(ShopOrderGoodReportIDto idto) {
        idto.check();

        if(idto.getShopType() != null){
            List<Long> storeIdList = shopService.getStoreIdListByShopType(idto.getShopType());
            if(CollectionUtils.isNotEmpty(storeIdList)){
                idto.setStoreIdList(storeIdList);
            }else {
                return new PageInfo<>();
            }
        }

        List<Long> commodityIdListAll = idto.getCommodityIdList();
        if(idto.getCate1() != null || idto.getCate2() != null || idto.getCate3() != null || StringUtils.isNotBlank(idto.getBarCode())){
            CommodityVO commodityVO = new CommodityVO();
            commodityVO.setBarCode(idto.getBarCode());
            commodityVO.setCateId1(idto.getCate1());
            commodityVO.setCateId2(idto.getCate2());
            commodityVO.setCateId3(idto.getCate3());
            List<CommodityBasicODTO> commList = commodityMapper.findCommodityBasicListByParam(commodityVO);
            if(CollectionUtils.isNotEmpty(commList)){
                List<Long> commodityIdList = commList.stream().map(item -> Long.valueOf(item.getCommodityId())).collect(Collectors.toList());
                if(CollectionUtils.isNotEmpty(commodityIdListAll)){
                    commodityIdListAll.retainAll(commodityIdList);
                }else {
                    commodityIdListAll.addAll(commodityIdList);
                }
            }else {
                return  new PageInfo<>();
            }
        }
        if(StringUtils.isNotBlank(idto.getFactoryCode())){
            List<Long> commodityIdList = commodityClient.findCommodityIdListByParam(idto.getFactoryCode(),"","");
            if(CollectionUtils.isNotEmpty(commodityIdList)){
                if(CollectionUtils.isNotEmpty(commodityIdListAll)){
                    commodityIdListAll.retainAll(commodityIdList);
                }else {
                    commodityIdListAll.addAll(commodityIdList);
                }
            }else {
                return new PageInfo<>();
            }
        }
        if(CollectionUtils.isEmpty(commodityIdListAll)){
            return  new PageInfo<>();
        }
        idto.setCommodityIdList(commodityIdListAll);

        PageInfo<ShopOrderGoodReportODto> pageDate = null;
        pageDate = PageHelper.startPage(idto.getPageNo(), idto.getPageSize()).doSelectPageInfo(() -> {
            orderReportMapper.shopOrderGoodReport(idto);
        });

        List<ShopOrderGoodReportODto> list = pageDate.getList();
        // 设置 门店订货汇总表(当日) 返回值
        setShopOrderGoodReportData(list);
        return pageDate;
    }

    /**
     * 门店订货汇总表(当日)
     * @param list
     */
    private void setShopOrderGoodReportData(List<ShopOrderGoodReportODto> list) {
        if(CollectionUtils.isNotEmpty(list)){
            List<Long> commodityIdList = list.stream().map(item -> Long.valueOf(item.getCommodityId())).distinct().collect(Collectors.toList());
            Map<Long,String> barCodeMap = commodityService.getCommodityBarCodeMap(commodityIdList,null);

            CommodityVO commodityVO = new CommodityVO();
            commodityVO.setCommodityIdList(commodityIdList);
            List<CommodityBasicODTO> commList = commodityMapper.findCommodityBasicListByParam(commodityVO);
            Map<String, CommodityBasicODTO> commMap = commList.stream().collect(Collectors.toMap(CommodityBasicODTO::getCommodityId, Function.identity()));

            /*List<Long> cateIdList = new ArrayList<>();
            cateIdList.addAll(commList.stream().map(item -> Long.valueOf(item.getCommodityFirstId())).distinct().collect(Collectors.toList()));
            cateIdList.addAll(commList.stream().map(item -> Long.valueOf(item.getCommoditySecondId())).distinct().collect(Collectors.toList()));
            cateIdList.addAll(commList.stream().map(item -> Long.valueOf(item.getCommodityThirdId())).distinct().collect(Collectors.toList()));
            Map<String, String> cateMap = commonService.getCategroyMap(cateIdList);*/

            List<Long> storeIdList = list.stream().map(item -> item.getStoreId()).distinct().collect(Collectors.toList());
            List<StoreEntry> storeList = shopService.getStoreByStoreIdList(storeIdList);
            Map<Long, StoreEntry> storeMap = storeList.stream().collect(Collectors.toMap(StoreEntry::getStoreId, Function.identity()));

            /*List<Long> shopIdList = storeList.stream().map(item -> item.getShopId()).distinct().collect(Collectors.toList());
            Map<Long, OrgAndParentInfoODTO> orgMap = commonService.getOrgMap(shopIdList);*/
            Map<String, CommodityDetailODTO> factoryCommodityMap = commodityService.getFactoryMap(commodityIdList);
            // Map<String, String> dictionaryMap = commonService.getDictionaryMap();

            for(ShopOrderGoodReportODto entry : list){
                CommodityBasicODTO commodityBasic = commMap.get(entry.getCommodityId() + "");
                if(commodityBasic != null){
                    BeanUtils.copyProperties(commodityBasic,entry);
                   /* entry.setCommodityFirstName(cateMap.get(commodityBasic.getCommodityFirstId() + ""));
                    entry.setCommoditySecondName(cateMap.get(commodityBasic.getCommoditySecondId() + ""));
                    entry.setCommodityThirdName(cateMap.get(commodityBasic.getCommodityThirdId() + ""));
                    entry.setCommodityUnit(dictionaryMap.get(commodityBasic.getCommodityUnitId() + ""));*/
                }
                CommodityDetailODTO commodityDetail = factoryCommodityMap.get(entry.getCommodityId() + "");
                if(commodityDetail != null){
                    entry.setFactoryName(commodityDetail.getFactoryName());
                    entry.setWorkshopName(commodityDetail.getWorkshopName());
                }

                StoreEntry storeEntry = storeMap.get(entry.getStoreId());
                if(storeEntry != null){
                    entry.setShopId(storeEntry.getShopId());
                    entry.setShopName(storeEntry.getShopName());
                    entry.setShopType(storeEntry.getShopType());
                    entry.setShopCode(storeEntry.getShopCode());
                }

                String[] barCode = barCodeMap.get(Long.valueOf(entry.getCommodityId())).split(",");
                entry.setBarCode(barCode[0]);
                List barCodeList = java.util.Arrays.asList(barCode);
                entry.setBarCodeList(barCodeList);
                entry.setRealDeliveryAmount(null != entry.getRealDeliveryAmount() ? entry.getRealDeliveryAmount().setScale(2, BigDecimal.ROUND_HALF_UP) : BigDecimal.ZERO);

                /*OrgAndParentInfoODTO orgODTO = orgMap.get(entry.getShopId());
                if(orgODTO != null){
                    entry.setOrgName(orgODTO.getParentOrgName());
                }*/
            }
        }
    }





    /**
     * 门店实收商品分析表
     * @param vo
     * @return
     * @throws ParseException
     */
    public PageInfo<ActualReceiptAnalysisODto> actualReceiptAnalysisReport(ActualReceiptAnalysisIDto vo) throws ParseException{
        PageInfo<ActualReceiptAnalysisODto> pageDate = new PageInfo<>();
        QYAssert.isTrue(!StringUtil.isBlank(vo.getBeginDate()) && !StringUtil.isBlank(vo.getEndDate()), "请选择日期");
        //vo.setOrderBeginDate(DateUtils.parseDate(vo.getBeginDate() + " 00:00:00", "yyyy-MM-dd HH:mm:ss"));
        //vo.setOrderEndDate(DateUtils.parseDate(vo.getEndDate() + " 23:59:59", "yyyy-MM-dd HH:mm:ss"));

        if(vo.getCateId1() != null || vo.getCateId2() != null || vo.getCateId3() != null
                || StringUtils.isNotBlank(vo.getSearchWord())
                || StringUtils.isNotBlank(vo.getBarCode())){
            CommodityVO commodityVO = new CommodityVO();
            BeanUtils.copyProperties(vo,commodityVO);
            commodityVO.setCommodityKey(vo.getSearchWord());
            List<CommodityBasicODTO> commList = commodityMapper.findCommodityBasicListByParam(commodityVO);
            if(CollectionUtils.isNotEmpty(commList)){
                List<Long> commodityIdList = commList.stream().map(item -> Long.valueOf(item.getCommodityId())).collect(Collectors.toList());
                vo.setCommodityIdList(commodityIdList);
            }else {
                return  pageDate;
            }
        }

        pageDate = PageHelper.startPage(vo.getPageNo(), vo.getPageSize()).doSelectPageInfo(() -> {
            orderReportMapper.actualReceiptAnalysisReport(vo);
        });
        setActualReceptList(pageDate);

        return pageDate;
    }

    /**
     * 门店实收商品分析表
     * @param pageDate
     */
    private void setActualReceptList(PageInfo<ActualReceiptAnalysisODto> pageDate) {
        if(CollectionUtils.isNotEmpty(pageDate.getList())){
            List<Long> commodityIdList = pageDate.getList().stream().map(item -> item.getCommodityId()).distinct().collect(Collectors.toList());
            Map<Long,String> barCodeMap = commodityService.getCommodityBarCodeMap(commodityIdList,null);
            Map<Long,BigDecimal> priceMap = commodityPriceClient.getCommoditySupplyPrice();
            Map<Long, CommoditySupplierODto> commodityIdAndSupplierODTOMap = commoditySupplierClient.queryCommodityDefaultSupplier(commodityIdList);

            CommodityVO commodityVO = new CommodityVO();
            commodityVO.setCommodityIdList(commodityIdList);
            List<CommodityBasicODTO> commList = commodityMapper.findCommodityBasicListByParam(commodityVO);
            Map<String, CommodityBasicODTO> commMap = commList.stream().collect(Collectors.toMap(CommodityBasicODTO::getCommodityId, Function.identity()));

            List<Long> cateIdList = new ArrayList<>();
            cateIdList.addAll(commList.stream().map(item -> Long.valueOf(item.getCommodityFirstId())).distinct().collect(Collectors.toList()));
            Map<String, String> cateMap = commodityService.getCategroyMap(cateIdList);

            List<Long> storeIdList = pageDate.getList().stream().distinct().map(item -> item.getStoreId()).collect(Collectors.toList());
            List<Shop> shopList = shopService.getShopByStoreIdList(storeIdList);
            Map<Long, String> shopMap = shopList.stream().collect(Collectors.toMap(Shop::getStoreId,Shop::getShopName,(key1 , key2)-> key2));

            pageDate.getList().forEach(a -> {
                CommodityBasicODTO commodityBasic = commMap.get(a.getCommodityId() + "");
                if(commodityBasic != null){
                    a.setCommodityCode(commodityBasic.getCommodityCode());
                    a.setCommodityName(commodityBasic.getCommodityName());
                    a.setCommoditySpec(commodityBasic.getCommoditySpec());
                    a.setCateName(cateMap.get(commodityBasic.getCommodityFirstId() + ""));
                }
                a.setShopName(shopMap.get(a.getStoreId()));

                if(null != priceMap){
                    a.setSupplyPrice(priceMap.get(a.getCommodityId()));
                }
                BigDecimal supplyPrice = a.getSupplyPrice();
                BigDecimal totalRealReceiveQuantity = a.getTotalRealReceiveQuantity();
                if(null != supplyPrice) {
                    a.setTotalSupplyPrice(supplyPrice.multiply(totalRealReceiveQuantity).setScale(2, BigDecimal.ROUND_HALF_UP));
                }
                BigDecimal totalRealDeliveryQuantity = a.getTotalRealDeliveryQuantity();
                a.setQuantityDifference(totalRealDeliveryQuantity.subtract(totalRealReceiveQuantity));

                CommoditySupplierODto commoditySupplierODto = commodityIdAndSupplierODTOMap.get(a.getCommodityId());
                if(commoditySupplierODto != null){
                    a.setSupplierName(commoditySupplierODto.getSupplierName());
                    a.setRealName(commoditySupplierODto.getRealName());
                }

                String barCodes = barCodeMap.get(Long.valueOf(a.getCommodityId()));
                a.setBarCodes(barCodes);
                a.setBarCode(barCodes!=null?barCodes.split(",")[0]:"");
            });
        }
    }





    /**
     * 代销订货明细
     * @param vo
     * @return
     */
    public PageInfo<ConsignmentOrderPageODTO> consignmentOrderReport(ConsignmentOrderQueryIDTO vo) {
        PageInfo<ConsignmentOrderPageODTO> pageInfo = new PageInfo();

        QYAssert.isTrue(org.apache.commons.lang3.StringUtils.isNotBlank(vo.getBeginDate()), "请选择送货日期");
        QYAssert.isTrue(org.apache.commons.lang3.StringUtils.isNotBlank(vo.getEndDate()), "请选择送货日期");

        DateTime beginDate = DateTime.parse(vo.getBeginDate(), DateTimeFormat.forPattern("yyyy-MM-dd"));
        DateTime endDate = DateTime.parse(vo.getEndDate(), DateTimeFormat.forPattern("yyyy-MM-dd"));
        QYAssert.isTrue(!beginDate.plusDays(30).isBefore(endDate), "送货日期不能大于31天");

        if(vo.getCommodityId() != null || org.apache.commons.lang3.StringUtils.isNotBlank(vo.getBarCode())
                || vo.getCateId1() != null || vo.getCateId2() != null || vo.getCateId3() != null){
            CommodityVO commodityVO = BeanCloneUtils.copyTo(vo, CommodityVO.class);
            List<CommodityBasicODTO> basicEntryList = commodityMapper.findCommodityBasicListByParam(commodityVO);
            if(org.apache.commons.collections.CollectionUtils.isNotEmpty(basicEntryList)){
                List<Long> commodityIdList = basicEntryList.stream().map(item -> Long.valueOf(item.getCommodityId())).collect(Collectors.toList());
                vo.setCommodityIdList(commodityIdList);
            }else {
                return pageInfo;
            }
        }


        List<Long> shopIdList = sMMUserClient.selectUserShopIdList( SelectUserShopIdListIDTO.onlyDb(FastThreadLocalUtil.getQY().getUserId()));
        if (org.apache.commons.collections.CollectionUtils.isEmpty(shopIdList)){
            return pageInfo;
        }

        if (org.apache.commons.lang3.StringUtils.isNotBlank(vo.getOrgCode())) {
            List<ShopDto> shopDtos = shopClient.selectShopListByParentOrgCode(vo.getOrgCode());
            if (org.apache.commons.collections.CollectionUtils.isNotEmpty(shopDtos)) {
                List<Long> orgShopIdList = shopDtos.stream().map(ShopDto::getId).collect(Collectors.toList());
                shopIdList.retainAll(orgShopIdList);

                if (org.apache.commons.collections.CollectionUtils.isEmpty(shopIdList)) {
                    return pageInfo;
                }
            }else {
                return pageInfo;
            }
        }
        vo.setShopIdList(shopIdList);

        pageInfo= PageHelper.startPage(vo.getPageNo(), vo.getPageSize()).doSelectPageInfo(() -> {
            orderReportMapper.consignmentOrderReport(vo);
        });

        List<ConsignmentOrderPageODTO> list = pageInfo.getList();
        for(ConsignmentOrderPageODTO odto : list){
            //odto.setAuditQuantity(odto.getRealReceiveQuantity());
            if(odto.getAuditQuantity() != null){
                odto.setDifferQuantity(odto.getRequireQuantity().subtract(odto.getAuditQuantity()));
            }
        }
        return pageInfo;
    }


    /**
     * 门店退库明细
     * @param idto
     * @return
     */
    public PageInfo<SaleReturnDetailPageODTO> saleReturnDetailPage(SaleReturnDetailPageIDTO idto) {
        if(!StringUtil.isBlank(idto.getBeginTime()) && !StringUtil.isBlank(idto.getEndTime())){
            idto.setBeginTime(idto.getBeginTime()+ " 00:00:00");
            idto.setEndTime(idto.getEndTime()+ " 23:59:59");
            int diff = DateUtil.getDayDif(DateUtil.parseDate(idto.getEndTime(), DateUtil.DEFAULT_DATE_FORMAT), DateUtil.parseDate(idto.getBeginTime(), DateUtil.DEFAULT_DATE_FORMAT));
            QYAssert.isTrue(diff <= 30, "退货日期的跨度不能超过31天");
        }

        TokenInfo tokenInfo = FastThreadLocalUtil.getQY();
        if(ManagementModeEnums.档口分包.getCode().equals(tokenInfo.getManagementMode())){
            List<Long> stallIdList = shopService.selectUserStallIdList(tokenInfo.getShopId());
            if(CollectionUtils.isEmpty(stallIdList)){
                return new PageInfo<>();
            }else {
                idto.setStallIdList(stallIdList);
            }
        }
        PageInfo<SaleReturnDetailPageODTO> pageInfo = PageHelper.startPage(idto.getPageNo(), idto.getPageSize()).doSelectPageInfo(() -> {
            orderReportMapper.saleReturnDetailPage(idto);
        });

        List<SaleReturnDetailPageODTO> list = pageInfo.getList();
        if(CollectionUtils.isNotEmpty(list)){
            List<DictionaryODTO> returnReasonDic = dictionaryClient.querySubDictionaryByParentOptionCode("7890");
            Map<String, String> returnReasonDicMap = returnReasonDic.stream().collect(Collectors.toMap(DictionaryODTO::getOptionCode, DictionaryODTO::getOptionName));
//            List<Long> shopIdList = list.stream().map(SaleReturnDetailPageODTO::getShopId).collect(Collectors.toList());
//            Map<Long, OrgAndParentInfoODTO> orgMap = commonService.getOrgMap(shopIdList);
            List<Long> returnIdList = list.stream().map(SaleReturnDetailPageODTO::getReturnId).collect(Collectors.toList());
            Map<String, List<String>> saleReturnPicMap = this.getSaleReturnPicMap(returnIdList);
            // 查询责任方字典
            List<DictionaryODTO> responsiblePartyTypeList = dictionaryClient.querySubDictionaryByParentOptionCode("ResponsiblePartyType");
            Map<String, String> responsiblePartyTypeMap = responsiblePartyTypeList.stream().collect(
                    Collectors.toMap(DictionaryODTO::getOptionValue, DictionaryODTO::getOptionName));

            List<Long> stallIdList = list.stream().filter(p -> p.getStallId() != null && p.getStallId() > 0).collect(Collectors.toList())
                    .stream().map(item -> item.getStallId()).collect(Collectors.toList());
            Map<Long, String> stallIdAndNameMap = shopService.queryStallMapByIds(stallIdList);
            for(SaleReturnDetailPageODTO oto:list){
                if(null != oto.getReTypeId()) {
                    oto.setReType(responsiblePartyTypeMap.get(oto.getReTypeId().toString()));
                }
                if(null != oto.getTotalPrice()){
                    oto.setTotalPrice(oto.getTotalPrice().setScale(2, RoundingMode.HALF_UP));
                }else {
                    oto.setTotalPrice(BigDecimal.ZERO);
                }
                if(null != oto.getReturnReasonId() && null != returnReasonDicMap.get(oto.getReturnReasonId()+"")) {
                    oto.setReturnReason(returnReasonDicMap.get(oto.getReturnReasonId()+""));
                }
                if(null != oto.getPrice() && null != oto.getRealReturnQuantity()) {
                    oto.setReturnAmount(oto.getPrice().multiply(oto.getRealReturnQuantity()).setScale(2, RoundingMode.HALF_UP));
                }

                if(SpringUtil.isNotEmpty(saleReturnPicMap)){
                    String picKey = oto.getReturnId() + "-" + oto.getCommodityId() + "-" + SaleReturnOrderPic.PicTypeEnums.PIC.getCode();
                    if(SpringUtil.isEmpty(saleReturnPicMap.get(picKey))){
                        oto.setHasPic("无");
                    }else{
                        oto.setHasPic("有");
                        oto.setPicUrl(this.buildPicList(saleReturnPicMap.get(picKey)));
                    }
                    String videoKey = oto.getReturnId() + "-" + oto.getCommodityId() + "-" + SaleReturnOrderPic.PicTypeEnums.VIDEO.getCode();
                    List<String> videoList = saleReturnPicMap.get(videoKey);
                    if(SpringUtil.isEmpty(videoList)){
                        oto.setHasVideo("无");
                    }else{
                        oto.setHasVideo("有");
                        oto.setVideoUrl(videoList.get(videoList.size()-1));
                        oto.setVisitVideoUrl(imgServerUrl + oto.getVideoUrl());
                    }
                }

                oto.setStatusStr(null == oto.getStatus() ? "" : SaleReturnStatusEnums.getName(oto.getStatus()));
                oto.setStallName(stallIdAndNameMap.get(oto.getStallId()));
            }
        }

        return pageInfo;
    }

    /**
     * 获取照片和视频
     * @param returnIdList
     * @return key = sales_order_return.id + "-" + commodity_id + "-" + 类型
     */
    private  Map<String, List<String>> getSaleReturnPicMap(List<Long> returnIdList){
        Example saleReturnOrderPicEx = new Example(SaleReturnOrderPic.class);
        saleReturnOrderPicEx.createCriteria().andIn("saleReturnOrderId", returnIdList);
        List<SaleReturnOrderPic> saleReturnPicList = saleReturnOrderPicMapper.selectByExample(saleReturnOrderPicEx);
        if(SpringUtil.isEmpty(saleReturnPicList)){
            return new HashMap<>();
        }
        Map<String, List<String>> saleReturnPicMap = new HashMap<>(saleReturnPicList.size());

        for (SaleReturnOrderPic pic : saleReturnPicList) {
            String key = pic.getSaleReturnOrderId() + "-" + pic.getCommodityId() + "-" + pic.getPicType();
            if(null == saleReturnPicMap.get(key)){
                List<String> picItemList = new ArrayList<>();
                picItemList.add(pic.getPicUrl());
                saleReturnPicMap.put(key, picItemList);
            }else{
                List<String> picItemList = saleReturnPicMap.get(key);
                picItemList.add(pic.getPicUrl());
            }
        }

        return saleReturnPicMap;
    }

    private List<SaleReturnOrderPicVO> buildPicList(List<String> picUrlList) {
        return picUrlList.stream().map(it ->  new SaleReturnOrderPicVO(it, imgServerUrl + it)).collect(Collectors.toList());
    }


    /**
     * 退货差异报表实退金额合计
     * @param vo
     * @return
     */
    public SaleReturnReportSumODTO queryReturnReportSumEntry(SaleReturnReportVo vo) {
        return orderReportMapper.queryReturnReportSumEntry(vo);

    }
    /**
     * 门店退货差异报表
     * @param vo
     * @return
     */
    public PageInfo<SaleReturnReportODTO> listReturnReport(SaleReturnReportVo vo) {
        //校验档口权限
        ddTokenShopIdService.processReadDdTokenShopId(vo.getShopId(), vo.getStallId());

        PageInfo<SaleReturnReportODTO> pageInfo = PageHelper.startPage(vo.getPageNo(), vo.getPageSize()).doSelectPageInfo(() -> {
            orderReportMapper.listReturnReport(vo);
        });
        if (pageInfo.getList().size() > 0) {
            List<Long> commodityIds = pageInfo.getList().stream().map(SaleReturnReportODTO::getCommodityId).collect(toList());
            Map<Long, String> barCodeMap = commodityService.getCommodityBarCodeMap(commodityIds, null);
            pageInfo.getList().forEach(e -> {
                if (barCodeMap.containsKey(e.getCommodityId())) {
                    e.setBarCodes(barCodeMap.get(e.getCommodityId()));
                }
                e.setRealTotalPrice(e.getRealReturnQuantity() != null && e.getPrice() != null ?
                        new BigDecimal(e.getRealReturnQuantity()).multiply(e.getPrice()).setScale(2, RoundingMode.HALF_UP).stripTrailingZeros() : null);
            });
        }
        return pageInfo;
    }

    /**
     * 代销退货明细
     * @param vo
     * @return
     */
    public PageInfo<ConsignmentReturnOrderPageODTO> consignmentReturnOrderReport(ConsignmentReturnOrderQueryIDTO vo) {

        PageInfo<ConsignmentReturnOrderPageODTO> pageInfo = new PageInfo();

        QYAssert.isTrue(org.apache.commons.lang3.StringUtils.isNotBlank(vo.getBeginDate()), "请选择退货日期");
        QYAssert.isTrue(org.apache.commons.lang3.StringUtils.isNotBlank(vo.getEndDate()), "请选择退货日期");

        DateTime beginDate = DateTime.parse(vo.getBeginDate(), DateTimeFormat.forPattern("yyyy-MM-dd"));
        DateTime endDate = DateTime.parse(vo.getEndDate(), DateTimeFormat.forPattern("yyyy-MM-dd"));
        QYAssert.isTrue(!beginDate.plusDays(30).isBefore(endDate), "退货日期不能大于31天");

        vo.setBeginDate(vo.getBeginDate() + " 00:00:00");
        vo.setEndDate(vo.getEndDate() + " 23:59:59");

        if(vo.getCommodityId() != null || org.apache.commons.lang3.StringUtils.isNotBlank(vo.getBarCode())
                || vo.getCateId1() != null || vo.getCateId2() != null || vo.getCateId3() != null){
            CommodityVO commodityVO = BeanCloneUtils.copyTo(vo, CommodityVO.class);
            List<CommodityBasicODTO> basicEntryList = commodityMapper.findCommodityBasicListByParam(commodityVO);
            if(org.apache.commons.collections.CollectionUtils.isNotEmpty(basicEntryList)){
                List<Long> commodityIdList = basicEntryList.stream().map(item -> Long.valueOf(item.getCommodityId())).collect(Collectors.toList());
                vo.setCommodityIdList(commodityIdList);
            }else {
                return pageInfo;
            }
        }


        List<Long> shopIdList = sMMUserClient.selectUserShopIdList( SelectUserShopIdListIDTO.onlyDb(FastThreadLocalUtil.getQY().getUserId()));
        if (org.apache.commons.collections.CollectionUtils.isEmpty(shopIdList)){
            return pageInfo;
        }

        if (org.apache.commons.lang3.StringUtils.isNotBlank(vo.getOrgCode())) {
            List<ShopDto> shopDtos = shopClient.selectShopListByParentOrgCode(vo.getOrgCode());
            if (org.apache.commons.collections.CollectionUtils.isNotEmpty(shopDtos)) {
                List<Long> orgShopIdList = shopDtos.stream().map(ShopDto::getId).collect(Collectors.toList());
                shopIdList.retainAll(orgShopIdList);

                if (org.apache.commons.collections.CollectionUtils.isEmpty(shopIdList)) {
                    return pageInfo;
                }
            }else {
                return pageInfo;
            }
        }
        vo.setShopIdList(shopIdList);

        pageInfo= PageHelper.startPage(vo.getPageNo(), vo.getPageSize()).doSelectPageInfo(() -> {
            orderReportMapper.consignmentReturnOrderReport(vo);
        });

        List<ConsignmentReturnOrderPageODTO> list = pageInfo.getList();
        for(ConsignmentReturnOrderPageODTO odto : list){
            if(odto.getCheckQuantity() != null){
                odto.setDifferQuantity(odto.getReturnQuantity().subtract(odto.getCheckQuantity()));
            }
        }
        return pageInfo;
    }

    /**
     * 门店加货申请单
     * @param idto
     * @return
     */
    public PageInfo<AutoPreOrderODTO> listShopAutoOrder(AutoPreOrderIDTO idto) {
        // 代销商判断
        TokenInfo tokenInfo = FastThreadLocalUtil.getQY();
        idto.setIsInternal(tokenInfo.getIsInternal());
        idto.setConsignmentId(tokenInfo.getConsignmentId());

        List<Long> shopIdList = smmUserClient.selectUserShopIdList(SelectUserShopIdListIDTO.onlyDb(FastThreadLocalUtil.getQY().getUserId()));
        if(CollectionUtils.isEmpty(shopIdList)){
            PageInfo info = new PageInfo();
            info.setList(null);
            return info;
        }
        idto.setShopIdList(shopIdList);

        if (idto.getOrderCode() != null && idto.getOrderCode().length() > 30) {
            idto.setOrderCode(idto.getOrderCode().substring(0, 30));
        }
        if(!StringUtil.isBlank(idto.getDeliveryStartTime()) && !StringUtil.isBlank(idto.getDeliveryEndTime())){
            idto.setDeliveryStartTime(idto.getDeliveryStartTime()+" 00:00:00");
            idto.setDeliveryEndTime(idto.getDeliveryEndTime()+" 23:59:59");
        }
        PageInfo<AutoPreOrderODTO> pageInfo = PageHelper.startPage(idto.getPageNo(), idto.getPageSize()).doSelectPageInfo(
                () -> orderReportMapper.listShopAutoOrder(idto));
        return pageInfo;
    }

    public XsjmOrderBillSummaryReportRspVO xsjmSummaryReport(XsjmOrderBillSummaryReportReqVO reqVO) {

        XsjmOrderBillSummaryReportRspVO result = new XsjmOrderBillSummaryReportRspVO();
        QYAssert.isTrue(Objects.nonNull(reqVO.getShopId()),"门店不能为空");
        QYAssert.isTrue(Objects.nonNull(reqVO.getQueryDate()),"查询日期不能为空");

        List<Shop> shopList = shopService.getShopByIdList(Arrays.asList(reqVO.getShopId()));
        if(SpringUtil.isEmpty(shopList)){
            QYAssert.isFalse("门店不存在");
        }

        Long storeId = shopList.get(0).getStoreId();

        /**
         * 获取期初余额
         */
        BigDecimal storeBalance = orderReportMapper.selectXsjmStoreBalance(reqVO.getQueryDate(),storeId);
        if(Objects.nonNull(storeBalance)){
            result.setBeginningBalance(storeBalance);
        }

        List<XsjmOrderBillSummaryReportDTO> xsjmOrderBillSummaryReportDTOList = orderReportMapper.xsjmSummaryReport(storeId,reqVO.getQueryDate());
        if(SpringUtil.isEmpty(xsjmOrderBillSummaryReportDTOList)){
            result.setBalance(result.getBeginningBalance());
            return result;
        }

        Map<Integer, XsjmOrderBillSummaryReportDTO> billTypeMap = xsjmOrderBillSummaryReportDTOList.stream().collect(
                Collectors.toMap(XsjmOrderBillSummaryReportDTO::getBillType, e -> e)
        );

        BigDecimal shopWebDeposit = Optional.ofNullable(billTypeMap.get(StoreBillTypeEnums.SHOP_WEB_DEPOSIT.getCode())).map(XsjmOrderBillSummaryReportDTO::getSumPaAmount).orElse(BigDecimal.ZERO);
        BigDecimal deposit = Optional.ofNullable(billTypeMap.get(StoreBillTypeEnums.DEPOSIT.getCode())).map(XsjmOrderBillSummaryReportDTO::getSumPaAmount).orElse(BigDecimal.ZERO);
        BigDecimal shopDiscrepancyDeposit = Optional.ofNullable(billTypeMap.get(StoreBillTypeEnums.SHOP_DISCREPANCY_DEPOSIT.getCode())).map(XsjmOrderBillSummaryReportDTO::getSumPaAmount).orElse(BigDecimal.ZERO);
        BigDecimal shopOrderChangePriceRefund = Optional.ofNullable(billTypeMap.get(StoreBillTypeEnums.SHOP_ORDER_CHANGE_PRICE_REFUNDMENT.getCode())).map(XsjmOrderBillSummaryReportDTO::getSumPaAmount).orElse(BigDecimal.ZERO);
        BigDecimal shopLackDeposit = Optional.ofNullable(billTypeMap.get(StoreBillTypeEnums.SHOP_LACK_DEPOSIT.getCode())).map(XsjmOrderBillSummaryReportDTO::getSumPaAmount).orElse(BigDecimal.ZERO);
        BigDecimal shopReturnDeposit = Optional.ofNullable(billTypeMap.get(StoreBillTypeEnums.SHOP_RETURN_DEPOSIT.getCode())).map(XsjmOrderBillSummaryReportDTO::getSumPaAmount).orElse(BigDecimal.ZERO);
        BigDecimal daySettleRefund = Optional.ofNullable(billTypeMap.get(StoreBillTypeEnums.SHOP_DAY_SETTLE.getCode())).map(XsjmOrderBillSummaryReportDTO::getSumPaAmount).orElse(BigDecimal.ZERO);
        BigDecimal shopOutRunDeduction = Optional.ofNullable(billTypeMap.get(StoreBillTypeEnums.SHOP_OUTRUN_DEDUCTION.getCode())).map(XsjmOrderBillSummaryReportDTO::getSumArAmount).orElse(BigDecimal.ZERO);
        BigDecimal shopOrderDeduction = Optional.ofNullable(billTypeMap.get(StoreBillTypeEnums.SHOP_ORDER_DEDUCTION.getCode())).map(XsjmOrderBillSummaryReportDTO::getSumArAmount).orElse(BigDecimal.ZERO);
        BigDecimal shopOrderDeposit = Optional.ofNullable(billTypeMap.get(StoreBillTypeEnums.SHOP_ORDER_DEPOSIT.getCode())).map(XsjmOrderBillSummaryReportDTO::getSumPaAmount).orElse(BigDecimal.ZERO);
        BigDecimal shopOrderChangePriceDeduction = Optional.ofNullable(billTypeMap.get(StoreBillTypeEnums.SHOP_ORDER_CHANGE_PRICE_DEDUCTION.getCode())).map(XsjmOrderBillSummaryReportDTO::getSumArAmount).orElse(BigDecimal.ZERO);
        BigDecimal shopDrawMoneyApply = Optional.ofNullable(billTypeMap.get(StoreBillTypeEnums.SHOP_DRAW_MONEY_APPLY.getCode())).map(XsjmOrderBillSummaryReportDTO::getSumArAmount).orElse(BigDecimal.ZERO);
        BigDecimal shopDrawMoneyCancel = Optional.ofNullable(billTypeMap.get(StoreBillTypeEnums.SHOP_DRAW_MONEY_CANCEL.getCode())).map(XsjmOrderBillSummaryReportDTO::getSumPaAmount).orElse(BigDecimal.ZERO);
        BigDecimal shopDrawMoneyReject = Optional.ofNullable(billTypeMap.get(StoreBillTypeEnums.SHOP_DRAW_MONEY_REJECT.getCode())).map(XsjmOrderBillSummaryReportDTO::getSumPaAmount).orElse(BigDecimal.ZERO);

        //品鲜充值(品鲜门店充值功能充值+品鲜后台充值)
        result.setRechargingMoney(shopWebDeposit.add(deposit));
        result.setUnderDeliveryRefundMoney(shopDiscrepancyDeposit);
        result.setChangePriceRefundMoney(shopOrderChangePriceRefund);
        result.setOutOfStockRefundMoney(shopLackDeposit.add(shopReturnDeposit));
        result.setDaySettleRefundMoney(daySettleRefund);

        result.setOverDeliveryDeduction(shopOutRunDeduction);
        result.setOrderMoneyDeduction(shopOrderDeduction.subtract(shopOrderDeposit));
        result.setChangePriceDeduction(shopOrderChangePriceDeduction);
        result.setWithDrawCashDeduction(shopDrawMoneyApply.subtract(shopDrawMoneyCancel).subtract(shopDrawMoneyReject));

        BigDecimal totalRechargingMoney = result.getRechargingMoney()
                .add(result.getUnderDeliveryRefundMoney())
                .add(result.getChangePriceRefundMoney())
                .add(result.getOutOfStockRefundMoney())
                .add(result.getDaySettleRefundMoney());


        BigDecimal totalDeductionMoney =  result.getOverDeliveryDeduction()
                .add(result.getOrderMoneyDeduction())
                .add(result.getChangePriceDeduction())
                .add(result.getWithDrawCashDeduction());

        BigDecimal balance = result.getBeginningBalance()
                        .add(totalRechargingMoney).subtract(totalDeductionMoney);
        result.setBalance(balance);
        result.setTotalRechargeMoney(totalRechargingMoney);
        result.setTotalDeductionMoney(totalDeductionMoney);

        return result;
    }
}

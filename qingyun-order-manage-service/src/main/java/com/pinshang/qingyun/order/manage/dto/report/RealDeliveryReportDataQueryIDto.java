package com.pinshang.qingyun.order.manage.dto.report;

import com.pinshang.qingyun.base.page.Pagination;
import com.pinshang.qingyun.infrastructure.data.query.annotate.Change;
import com.pinshang.qingyun.infrastructure.data.query.constant.DataQueryConstant;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class RealDeliveryReportDataQueryIDto extends Pagination {

	private String beginDate;
	//如果结束时间小于今天，去大数据查询
	@Change(value = DataQueryConstant.NOW)
	private String endDate;

	private Boolean differ;

	private List<Long> commodityIdList;
	private Long consignmentId;
}

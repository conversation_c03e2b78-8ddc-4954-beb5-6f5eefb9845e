package com.pinshang.qingyun.order.manage.service;

import com.alibaba.fastjson.JSON;
import com.pinshang.qingyun.base.configure.expand.QYAssert;
import com.pinshang.qingyun.base.enums.YesOrNoEnums;
import com.pinshang.qingyun.order.manage.dto.CloudOrderAllocationIDTO;
import com.pinshang.qingyun.order.manage.mapper.CloudOrderAllocationParamMapper;
import com.pinshang.qingyun.order.manage.model.CloudOrderAllocationParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Example;

import java.util.Date;

/**
 * @Author: sk
 * @Date: 2023/5/11
 */
@Slf4j
@Service
public class CloudOrderAllocationSaveParamService {

    @Autowired
    private CloudOrderAllocationParamMapper cloudOrderAllocationParamMapper;

    /**
     * 保存入参表
     * @param idto
     */
    @Async
    public void saveParam(CloudOrderAllocationIDTO idto) {
        // 记录入参
        CloudOrderAllocationParam param = new CloudOrderAllocationParam();
        param.setCreateId(idto.getCreateId());
        param.setCreateTime(new Date());
        param.setUpdateId(idto.getCreateId());
        param.setUpdateTime(new Date());
        param.setTaskId(idto.getTaskId());
        param.setOrderStatus(YesOrNoEnums.NO.getCode());
        param.setParam(JSON.toJSONString(idto));
        cloudOrderAllocationParamMapper.insert(param);
    }

    /**
     * 异步更新入参任务已下单
     * @param createId
     * @param taskId
     */
    @Async
    public void updateParam(Long createId, Long taskId){
        try {
            Thread.sleep(3000L);

            // 更新任务成功
            CloudOrderAllocationParam updateParam = new CloudOrderAllocationParam();
            updateParam.setUpdateId(createId);
            updateParam.setUpdateTime(new Date());
            updateParam.setOrderStatus(YesOrNoEnums.YES.getCode());
            Example updateExample = new Example(CloudOrderAllocationParam.class);
            updateExample.createCriteria().andEqualTo("taskId", taskId);
            int count = cloudOrderAllocationParamMapper.updateByExampleSelective(updateParam, updateExample);
            QYAssert.isTrue(count > 0 , "任务更新失败,taskId: " + taskId);
        } catch (Exception e) {
           log.warn("更新taskId已经下单异常",e);
        }
    }
}

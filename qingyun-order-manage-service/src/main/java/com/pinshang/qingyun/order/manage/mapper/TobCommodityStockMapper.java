package com.pinshang.qingyun.order.manage.mapper;

import com.pinshang.qingyun.base.mybatis.MyMapper;
import com.pinshang.qingyun.order.dto.tob.ToBStockRepairIDTO;
import com.pinshang.qingyun.order.manage.model.TobCommodityStock;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface TobCommodityStockMapper extends MyMapper<TobCommodityStock> {

    List<Long> queryToBCommodityStock(@Param("commodityIdList") List<Long> commodityIdList);

}

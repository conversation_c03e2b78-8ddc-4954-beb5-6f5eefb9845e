package com.pinshang.qingyun.order.manage.controller;

import com.alibaba.excel.EasyExcel;
import com.github.pagehelper.PageInfo;
import com.pinshang.qingyun.base.util.ExcelUtil;
import com.pinshang.qingyun.order.manage.dto.BStockShortPageIDTO;
import com.pinshang.qingyun.order.manage.dto.BStockShortPageODTO;
import com.pinshang.qingyun.order.manage.service.BStockShortService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2024/03/13
 * @Version 1.0
 */
@Slf4j
@RequestMapping("/bStockShort")
@RestController
@Api(value = "bStockShort")
public class BStockShortController {
    @Autowired
    private BStockShortService bStockShortService;

    @ApiOperation(value = "b端库存短交日志分页查询")
    @PostMapping("/page")
    public PageInfo<BStockShortPageODTO> page(@RequestBody BStockShortPageIDTO idto){
        return bStockShortService.page(idto);
    }

    @ApiOperation(value = "b端库存短交日志分页查询")
    @GetMapping("/export")
    public void export(BStockShortPageIDTO idto, HttpServletResponse response) throws IOException {
        idto.initExportPage();
        PageInfo<BStockShortPageODTO> pageInfo = bStockShortService.page(idto);
        List<BStockShortPageODTO> list = pageInfo.getList();
        if(null == list){
            list = new ArrayList<>();
        }

        try {
            ExcelUtil.setFileNameAndHead(response, "b端库存短交日志" + System.currentTimeMillis());
            EasyExcel.write(response.getOutputStream(), BStockShortPageODTO.class).autoCloseStream(Boolean.FALSE).sheet("b端库存短交日志")
                    .doWrite(list);

        }catch (Exception e){
            log.error("b端库存短交日志导出错误", e);
            ExcelUtil.setExceptionResponse( response );
        }
    }
}

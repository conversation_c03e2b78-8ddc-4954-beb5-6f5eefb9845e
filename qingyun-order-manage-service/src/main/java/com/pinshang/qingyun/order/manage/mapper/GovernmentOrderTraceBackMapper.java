package com.pinshang.qingyun.order.manage.mapper;

import com.pinshang.qingyun.order.manage.dto.government.*;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/2/4 14:39
 */
@Repository
@Mapper
public interface GovernmentOrderTraceBackMapper {

    List<GovernmentOrderTraceBackRespODTO> selectGovernmentOrderTraceBackList(GovernmentOrderTraceBackRepIDTO vo);

    List<GovernmentStoreRespODTO> selectGovernmentStoreByStoreId(@Param("storeIds") List<Long> storeIds);

    List<FactoryRespODTO> selectFactoryById(@Param("factoryIds") List<Long> factoryIds);

    List<GovernmentCommdityRespODTO> selectGovernmentCommodityList(@Param("factoryId") Long factoryId);
}

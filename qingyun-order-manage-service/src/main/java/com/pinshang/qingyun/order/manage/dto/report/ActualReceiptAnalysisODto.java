package com.pinshang.qingyun.order.manage.dto.report;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class ActualReceiptAnalysisODto {
	private Long storeId;
	@ApiModelProperty("门店名称")
	private String shopName;
	@ApiModelProperty("分类名称")
	private String cateName;
	@ApiModelProperty("条形码")
	private String barCode;

	private String barCodes;	// 子码列表
	private Long commodityId;

	@ApiModelProperty("商品编码")
	private String commodityCode;
	@ApiModelProperty("商品名称")
	private String commodityName;
	@ApiModelProperty("规格")
	private String commoditySpec;
	@ApiModelProperty("订货数量")
	private BigDecimal totalQuantity;
	@ApiModelProperty("实收数量")
	private BigDecimal totalRealReceiveQuantity;
	@ApiModelProperty("实收差异")
	private BigDecimal quantityDifference;

	@ApiModelProperty("实发数量")
	private BigDecimal totalRealDeliveryQuantity;

	/*@ApiModelProperty("当前库存")
	private BigDecimal stockQuantity;*/
	@ApiModelProperty("供货价")
	private BigDecimal supplyPrice;
	@ApiModelProperty("供应商")
	private String supplierName;
	@ApiModelProperty("采购员")
	private String realName;
	@ApiModelProperty("供货金额")
	private BigDecimal totalSupplyPrice;
}

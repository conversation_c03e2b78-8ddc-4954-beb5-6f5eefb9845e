package com.pinshang.qingyun.order.manage.dto;

import com.pinshang.qingyun.renderer.annotation.FieldRender;
import com.pinshang.qingyun.renderer.constant.RenderFieldHelper;
import com.pinshang.qingyun.renderer.enums.FieldTypeEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * @Author: sk
 * @Date: 2022/8/31
 */
@Data
public class CommodityODTO {

    private Long commodityId;

    /** 产品代码 */
    private String commodityCode;

    /** 产品名称 */
    private String commodityName;

    /** 型号规格 */
    private String commoditySpec;

    /** 包装规格 */
    private BigDecimal commodityPackageSpec;

    private Long commodityFirstId;

    /** 商品类型-1-普通商品，2-组合商品 */
    private Integer productType;

    /** 子商品id */
    private Long commodityItemId;

    /** 是否称重0-不称量,1-称重' */
    private Integer isWeight;


    @ApiModelProperty("保质期天数")
    private Integer qualityDays;

    @ApiModelProperty("保质期单位")
    private String qualityUnit;

    /** 计量单位 */
    private String commodityUnit;
}

<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.pinshang.qingyun.order.manage.mapper.TobCommodityStockMapper" >
    <select id="queryToBCommodityStock" resultType="java.lang.Long">
        SELECT
        t.commodity_id
        FROM t_dc_tob_commodity_stock t
        where  t.`commodity_id` IN
        <foreach collection="commodityIdList" index="index" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>
</mapper>
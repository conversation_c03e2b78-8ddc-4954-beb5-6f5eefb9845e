<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.pinshang.qingyun.order.manage.mapper.auto.AutoShopCommodityMapper">

    <select id = "commodityByShopId" resultType = "com.pinshang.qingyun.order.manage.vo.auto.AutoShopCommodityListVO">
        SELECT masc.id AS autoShopCommodityId,
               masc.commodity_id,
               masc.stock_quantity,
               tc.commodity_code,
               tc.bar_code,
               tc.commodity_name,
               tc.commodity_spec,
               tc.commodity_is_quick_freeze,
               tc.commodity_package_spec,
               tc.is_weight,
               tc.sales_box_capacity,
               tc.xd_sales_box_capacity,
               td.option_name AS commodityUnitName,
               fcate.cate_name AS firstCateName
        FROM t_md_auto_shop_commodity  masc
        LEFT JOIN t_commodity tc ON  masc.commodity_id = tc.id
        LEFT JOIN t_dictionary td ON tc.commodity_unit_id = td.id
        LEFT JOIN t_category fcate ON tc.commodity_first_id = fcate.id
--         LEFT JOIN t_category scate ON tc.commodity_second_id = scate.id
--         LEFT JOIN t_category tcate ON tc.commodity_third_id = tcate.id
        <where>
          <if test = "vo.shopId != null and vo.shopId !=''">
              AND masc.shop_id = #{vo.shopId}
          </if>
          <if test = "vo.commodityId != null and vo.commodityId !=''">
              AND masc.commodity_id = #{vo.commodityId}
          </if>
          <if test = "vo.commodityIdList != null and vo.commodityIdList.size > 0">
            AND masc.commodity_id IN
            <foreach collection="vo.commodityIdList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
          </if>
          <if test = "vo.barCode != null and vo.barCode !=''">
              AND tc.bar_code = #{vo.barCode}
          </if>
          <if test="vo.cate1 != null ">
              AND  tc.commodity_first_id = #{vo.cate1}
          </if>
          <if test="vo.cate2 != null ">
              AND  tc.commodity_second_id = #{vo.cate2}
          </if>
          <if test="vo.cate3 != null ">
              AND  tc.commodity_third_id = #{vo.cate3}
          </if>
          <if test="vo.commodityIsQuickFreeze != null ">
              AND  tc.commodity_is_quick_freeze = #{vo.commodityIsQuickFreeze}
          </if>
          <if test = "vo.isWeight != null">
              AND tc.is_weight = #{vo.isWeight}
          </if>
        </where>
        ORDER BY tc.commodity_code ASC
    </select>



    <select id = "checkCommodityAuto" resultType = "java.lang.Long">
        SELECT
            count(1)
        FROM t_md_auto_setting  mas
        LEFT JOIN t_md_auto_shop_commodity masc ON  masc.shop_id = mas.shop_id
        left join t_md_shop md on md.id = mas.shop_id
        where mas.status = 1 and md.store_id = #{storeId}
        and masc.commodity_id = #{commodityId}
    </select>

    <select id = "getAutoCommodityList" resultType = "java.lang.Long">
        SELECT
            masc.commodity_id
        FROM t_md_auto_setting  mas
         LEFT JOIN t_md_auto_shop_commodity masc ON  masc.shop_id = mas.shop_id
        where mas.status = 1 and mas.shop_id = #{shopId}
    </select>

    <select id = "commodityInfoList" resultType = "com.pinshang.qingyun.order.manage.vo.auto.AutoShopCommodityListVO">
        SELECT
               tc.id AS commodityId,
               tc.commodity_code,
               tc.bar_code,
               tc.commodity_name,
               tc.commodity_spec,
               tc.commodity_is_quick_freeze,
               tc.is_weight,
               tc.commodity_package_spec,
               tc.sales_box_capacity,
               tc.xd_sales_box_capacity,
               td.option_name AS commodityUnitName,
               fcate.cate_name AS firstCateName
        from t_commodity tc
        LEFT JOIN t_dictionary td ON tc.commodity_unit_id = td.id
        LEFT JOIN t_category fcate ON tc.commodity_first_id = fcate.id
--         LEFT JOIN t_category scate ON tc.commodity_second_id = scate.id
--         LEFT JOIN t_category tcate ON tc.commodity_third_id = tcate.id
        <where>
            <if test = "vo.commodityIdList != null and vo.commodityIdList.size > 0">
                AND tc.id IN
                <foreach collection="vo.commodityIdList" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
            <if test = "vo.commodityId != null and vo.commodityId !=''">
                AND tc.id = #{vo.commodityId}
            </if>
            <if test = "vo.barCode != null and vo.barCode !=''">
                AND tc.bar_code = #{vo.barCode}
            </if>
            <if test="vo.cate1 != null ">
                AND  tc.commodity_first_id = #{vo.cate1}
            </if>
            <if test="vo.cate2 != null ">
                AND  tc.commodity_second_id = #{vo.cate2}
            </if>
            <if test="vo.cate3 != null ">
                AND  tc.commodity_third_id = #{vo.cate3}
            </if>
            <if test="vo.commodityIsQuickFreeze != null ">
                AND  tc.commodity_is_quick_freeze = #{vo.commodityIsQuickFreeze}
            </if>
            <if test = "vo.isWeight != null">
                AND tc.is_weight = #{vo.isWeight}
            </if>
        </where>
    </select>

    <select id="directSendingCommodityList" resultType = "com.pinshang.qingyun.order.manage.vo.auto.DirectSendingCommodityVO">
        SELECT sc.id as autoShopCommodityId,
               sc.commodity_id,
               c.logistics_model,
               sc.shop_id,
               c.commodity_code,
               c.commodity_name,
               ms.shop_code,
               ms.shop_name
        FROM t_md_auto_shop_commodity sc
        LEFT JOIN t_commodity c ON sc.commodity_id = c.id
        LEFT JOIN t_md_shop ms ON ms.id = sc.shop_id
        WHERE c.logistics_model = #{iogisticsModel}
    </select>


    <select id="queryAllAutoShopCommodity" resultType = "com.pinshang.qingyun.order.manage.dto.auto.AutoShopCommodityODTO">
        SELECT
           sc.commodity_id,
           sc.shop_id,
           c.commodity_code,
           c.commodity_name,
           c.commodity_is_quick_freeze,
           c.is_weight
        FROM t_md_auto_shop_commodity sc
          LEFT JOIN t_commodity c ON  c.id = sc.commodity_id
    </select>


    <select id="commodityInfo" resultType = "com.pinshang.qingyun.order.manage.vo.auto.AutoCommodityVO">
        SELECT
            c.id AS commodityId,
            c.commodity_code AS commodityCode,
            c.commodity_name AS commodityName,
            c.commodity_spec AS commoditySpec,
            c.commodity_unit_id AS commodityUnitId,
            c.logistics_model AS logisticsModel,
            c.commodity_package_spec AS commodityPackageSpec
        FROM t_xs_shop_commodity sc
                 INNER JOIN t_commodity c ON sc.commodity_id = c.id AND sc.shop_id = #{shopId}
        WHERE sc.commodity_id = (SELECT commodity_id FROM t_commodity_bar_code WHERE bar_code = #{barCode} )
    </select>

    <select id="listByShopAndCommodityCode" resultType = "com.pinshang.qingyun.order.manage.vo.auto.ImportStockQuantityVO">
        SELECT c.id AS commodityId,
        c.commodity_code AS commodityCode,
        c.commodity_name AS commodityName,
        c.logistics_model AS logisticsModel
        FROM t_xs_shop_commodity sc
        INNER JOIN t_commodity c ON sc.commodity_id = c.id AND sc.shop_id = #{shopId}
        <where>
            and c.product_type != 2
            AND c.commodity_code IN
            <foreach collection="list" item="commodityCode" open="(" close=")" separator=",">
                #{commodityCode}
            </foreach>
        </where>
    </select>

</mapper>
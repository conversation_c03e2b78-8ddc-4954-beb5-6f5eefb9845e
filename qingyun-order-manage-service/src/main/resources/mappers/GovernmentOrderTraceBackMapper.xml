<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.pinshang.qingyun.order.manage.mapper.GovernmentOrderTraceBackMapper">

    <select id="selectGovernmentOrderTraceBackList" resultType="com.pinshang.qingyun.order.manage.dto.government.GovernmentOrderTraceBackRespODTO" parameterType="com.pinshang.qingyun.order.manage.dto.government.GovernmentOrderTraceBackRepIDTO">

        SELECT
        <![CDATA[
            commodity_code,
            commodity_name,
            deliveryDate,
            store_id,
            order_code,
            commodityCount,
            commodityUnit
        FROM
            (
                SELECT
                    c.commodity_code,
                    c.commodity_name,
                    o.order_time AS deliveryDate,
                    o.store_id,
                    o.order_code,
                    SUM(g.commodity_num) AS commodityCount,
                    d.option_name AS commodityUnit,
                    gc.factory_id
                    ]]>
                FROM t_government_commodity gc
                INNER JOIN t_commodity c ON c.id = gc.commodity_id
                LEFT JOIN t_order_list_gift g ON g.commodity_id = gc.commodity_id
                RIGHT JOIN (
                    SELECT
                        id,
                        order_time,
                        store_id,
                        order_code
                    FROM
                        t_order
                    WHERE
                        order_time =STR_TO_DATE(#{deliveryDate},'%Y-%m-%d')
                ) AS o ON o.id = g.order_id
                LEFT JOIN t_dictionary d ON d.id = c.commodity_unit_id
                GROUP BY
                    gc.commodity_id,
                    o.store_id
     )as t  WHERE factory_id =#{factoryId}
    </select>

    <select id="selectGovernmentStoreByStoreId" resultType="com.pinshang.qingyun.order.manage.dto.government.GovernmentStoreRespODTO">
        <![CDATA[
        SELECT
        s.id AS storeId,
        s.store_name,
        s.store_code,
        s.delivery_address,
        s.linkman_mobile AS linkman_tel,
        gs.store_shop_name AS outerStoreName,
        gs.store_shop_code AS sku
        ]]>
        FROM  t_store s
        LEFT JOIN t_government_store gs ON gs.store_id = s.id
        WHERE s.id in
        <foreach collection="storeIds" item="s" separator="," open="(" close=")" index="index">
            #{s}
        </foreach>
    </select>

    <select id="selectFactoryById" resultType="com.pinshang.qingyun.order.manage.dto.government.FactoryRespODTO">

        SELECT
        <![CDATA[
            id,
            factory_code,
            factory_name,
            (
            CASE
            WHEN product_manufacturer IS NULL THEN
                factory_name
            WHEN product_manufacturer = '' THEN
                factory_name
            ELSE
                product_manufacturer
            END
	        ) AS productManufacturer
            ]]>
        FROM
            t_factory
        WHERE
        id IN
        <foreach collection="factoryIds" index="index" item="f" separator="," open="(" close=")">
            #{f}
        </foreach>
    </select>


    <select id="selectGovernmentCommodityList" resultType="com.pinshang.qingyun.order.manage.dto.government.GovernmentCommdityRespODTO">

        SELECT
        <![CDATA[
            tgc.product_name AS productName,
            tgc.product_code AS productCode,
            tgc.product_classification AS productClassification,
            tgc.product_qgp AS productQgp,
            tgc.product_specifications AS productSpecifications,
            substring(tgc.product_barcode, 1, 16) AS productBarcode,
            tgc.product_manufacturer AS productManufacturer,
            substring(tgc.productescribe, 1, 4) AS productescribe
            ]]>
        FROM
            t_government_commodity tgc
            where tgc.factory_id = #{factoryId}
        ORDER BY
            length(tgc.product_code),
            tgc.product_code ASC
    </select>

</mapper>

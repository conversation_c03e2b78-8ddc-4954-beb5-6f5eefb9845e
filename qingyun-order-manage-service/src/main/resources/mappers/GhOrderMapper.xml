<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pinshang.qingyun.order.manage.mapper.GhOrderMapper">

    <!--查询列表-->
    <select id="findGhOrderPageInfo" resultType="com.pinshang.qingyun.order.manage.dto.gh.GhOrderODTO">
        SELECT
            gs.gh_group_code AS ghGroupCode,
            gs.gh_shop_code AS ghShopCode,
            gc.gh_commodity_code AS ghCommodityCode,
            DATE_FORMAT(o.order_time, '%Y-%m-%d') AS orderTime,
            om.delivery_man_name AS deliveryMan,
            tdl.plate_num AS plateNum,
            ste.employee_phone AS deliveryManTel,
            ol.commodity_price AS goodPrice,
            gc.tax_rate AS taxRate,
            DATE_FORMAT(o.create_time, '%Y-%m-%d %T') AS createTime,
            Sum(ol.commodity_num) AS orderNum
        FROM
            t_gh_shop gs
                LEFT JOIN t_order o ON gs.qm_store_id = o.store_id
                LEFT JOIN t_order_list_gift ol ON o.id = ol.order_id
                INNER JOIN t_gh_commodity gc ON ol.commodity_id = gc.qm_commodity_id
                LEFT JOIN t_order_mirror om ON om.order_id = o.id
                LEFT JOIN t_employee_user ste ON om.delivery_man_id = ste.employee_id
                LEFT JOIN t_store s on s.id = o.store_id
                LEFT JOIN t_distribution_line tdl ON tdl.id = s.store_line_id
        WHERE o.order_status = 0 and o.order_time = #{orderTime}
        GROUP BY
            gs.gh_shop_code,
            gc.gh_commodity_code,
            om.delivery_man_id
        ORDER BY
            gs.gh_group_code,
            gs.gh_shop_code,
            length(gh_commodity_code),
            gh_commodity_code
    </select>
</mapper>

<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.pinshang.qingyun.order.manage.mapper.RtOutboundOrderMapper">
    <select id="selectRtOutboundOrderList" resultType="com.pinshang.qingyun.order.manage.dto.rt.RtOutboundOrderODTO">
        SELECT
        rs.receiving_unit AS receivingUnit,
        DATE_FORMAT(o.order_time, '%Y-%m-%d %T') AS orderTime,
        rc.rt_commodity_code AS rtCommodityCode,
        rc.rt_commodity_name AS rtCommodityName,
        sum(olg.commodity_num) AS rtCommodityNum,
        rc.conversion_rate as conversionRate
        FROM
        t_order_list_gift olg
        LEFT JOIN t_order o ON o.id = olg.order_id
        INNER JOIN t_rt_shop rs ON rs.qm_store_id = o.store_id
        INNER JOIN t_rt_commodity rc ON rc.qm_commodity_id = olg.commodity_id
        <where>
            rc.is_weight = 1
            AND o.order_status = 0
            AND rs.receiving_unit !='' AND rs.receiving_unit IS NOT NULL
            <if test="null != deliveryTime and  deliveryTime != ''">
                AND o.order_time = #{deliveryTime}
            </if>
            <if test="storeShortName != null">
                AND rc.store_short_name = #{storeShortName}
                AND rs.store_short_name = #{storeShortName}
            </if>
        </where>
        GROUP BY
        rs.receiving_unit,
        rc.qm_commodity_id
    </select>


</mapper>
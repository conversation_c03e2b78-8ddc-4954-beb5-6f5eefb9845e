<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.pinshang.qingyun.order.manage.mapper.OrderMapper">


    <!--结算明细报表-->
    <select id="settlementDetailsReport" resultType="com.pinshang.qingyun.order.manage.dto.settlement.SettlementDetailsReportEntry"
    parameterType="com.pinshang.qingyun.order.manage.dto.settlement.SettlementDetailsReportVo">

        SELECT
        *
        FROM
        (
        (
        SELECT
        ms.id AS shopId,
        ms.shop_code AS shopCode,
        ms.shop_name AS shopName,
        o.order_time AS orderTime,
        NULL AS settleTime,
        ord.order_type,
        ord.stall_id,
        o.id as subOrderId,
        o.sub_order_code AS subOrderCode,
        NULL AS preOrderCode,
        ca.cate_name AS categoryName,
        c.id AS commodityId,
        c.bar_code AS barCode,
        c.commodity_code AS commodityCode,
        c.commodity_name AS commodityName,
        c.commodity_spec AS commoditySpec,
        c.commodity_first_id AS commodityFirstId,
        c.commodity_second_id AS commoditySecondId,
        c.commodity_third_id AS commodityThirdId,
        d.option_name AS commodityUnitName,
        oi.price AS price,
        oi.quantity AS quantity,
        oi.real_delivery_quantity AS realDeliveryQuantity,
        (oi.price * oi.real_delivery_quantity)AS settlePrice,
        o.logistics_model AS logisticsModel,
        s.store_type_id AS storeTypeId,
        (
        SELECT
        group_concat(cbc.bar_code ORDER BY cbc.id)
        FROM
        t_commodity co
        LEFT JOIN t_commodity_bar_code cbc ON cbc.commodity_id = co.id
        WHERE
        cbc.commodity_id = c.id
        ) AS barCodes
        FROM
        t_sub_order o
        INNER JOIN t_sub_order_item oi ON oi.sub_order_id = o.id
        INNER JOIN t_order ord ON ord.id = o.order_id
        INNER JOIN t_store s ON s.id = ord.store_id
        INNER JOIN t_md_shop ms ON ms.store_id = s.id
        INNER JOIN t_commodity c ON c.id = oi.commodity_id
        LEFT JOIN t_dictionary d ON d.id = c.commodity_unit_id
        LEFT JOIN t_category ca ON ca.id = c.commodity_first_id
        WHERE
        oi.real_delivery_quantity IS NOT NULL
        <include refid="searchConditions"></include>
        <if test="null != subOrderIdList and subOrderIdList.size > 0">
            and o.id in
            <foreach collection="subOrderIdList" item="code" open="(" close=")" separator=",">
                #{code}
            </foreach>
        </if>
        )
        UNION All
        (
        SELECT
        ms.id AS shopId,
        ms.shop_code AS shopCode,
        ms.shop_name AS shopName,
        o.order_time AS orderTime,
        o.approving_time AS settleTime,
        ord.order_type,
        ord.stall_id,
        null as subOrderId,
        o.real_order_code AS subOrderCode,
        o.order_code AS preOrderCode,
        ca.cate_name AS categoryName,
        c.id AS commodityId,
        c.bar_code AS barCode,
        c.commodity_code AS commodityCode,
        c.commodity_name AS commodityName,
        c.commodity_spec AS commoditySpec,
        c.commodity_first_id AS commodityFirstId,
        c.commodity_second_id AS commoditySecondId,
        c.commodity_third_id AS commodityThirdId,
        d.option_name AS commodityUnitName,
        oi.price AS price,
        oi.require_quantity AS quantity,
        oi.real_receive_quantity AS realDeliveryQuantity,
        (oi.price * oi.real_receive_quantity) AS settlePrice,
        o.logistics_model AS logisticsModel,
        s.store_type_id AS storeTypeId,
        (
        SELECT
        group_concat(cbc.bar_code ORDER BY cbc.id)
        FROM
        t_commodity co
        LEFT JOIN t_commodity_bar_code cbc ON cbc.commodity_id = co.id
        WHERE
        cbc.commodity_id = c.id
        ) AS barCodes
        FROM
        t_md_preorder o
        INNER JOIN t_md_preorder_item oi ON oi.preorder_id = o.id
        INNER JOIN t_store s ON s.id = o.store_id
        INNER JOIN t_md_shop ms ON ms.store_id = s.id
        LEFT JOIN t_order ord ON ord.id = o.order_id
        INNER JOIN t_commodity c ON c.id = oi.commodity_id
        LEFT JOIN t_dictionary d ON d.id = c.commodity_unit_id
        LEFT JOIN t_category ca ON ca.id = c.commodity_first_id
        WHERE
        o.receive_status = 3
        AND o.order_status != 0
        AND oi. STATUS = 2
        <include refid="searchConditions"></include>
        <if test="settleTimeStart !=null and settleTimeEnd != null and settleTimeStart != '' and settleTimeEnd != '' ">
            and o.approving_time BETWEEN #{settleTimeStart} and #{settleTimeEnd}
        </if>
        )
        ) a
        <include refid="settlementDetailsReportSearchConditions"></include>
        ORDER BY
        a.shopCode ASC,a.orderTime DESC,
        a.subOrderCode desc ,length (a.commodityCode) asc ,a.commodityCode asc
    </select>


    <!--查询结算总金额-->
    <select id="findTotalSettlePrice" resultType="java.math.BigDecimal"
            parameterType="com.pinshang.qingyun.order.manage.dto.settlement.SettlementDetailsReportVo">
        SELECT
        SUM(a.settlePrice)
        FROM
        (
        (
        SELECT
        o.sub_order_code AS subOrderCode,
        NULL AS preOrderCode,
        ROUND((oi.price * oi.real_delivery_quantity),2) AS settlePrice
        FROM
        t_sub_order o
        INNER JOIN t_sub_order_item oi ON oi.sub_order_id = o.id
        INNER JOIN t_order ord ON ord.id = o.order_id
        INNER JOIN t_store s ON s.id = ord.store_id
        INNER JOIN t_md_shop ms ON ms.store_id = s.id
        INNER JOIN t_commodity c ON c.id = oi.commodity_id
        WHERE
        oi.real_delivery_quantity IS NOT NULL
        <include refid="searchConditions"></include>
        <if test="null != subOrderIdList and subOrderIdList.size > 0">
            and o.id in
            <foreach collection="subOrderIdList" item="code" open="(" close=")" separator=",">
                #{code}
            </foreach>
        </if>
        )
        UNION All
        (
        SELECT
        o.real_order_code AS subOrderCode,
        o.order_code AS preOrderCode,
        ROUND((oi.price * oi.real_receive_quantity),2) AS settlePrice
        FROM
        t_md_preorder o
        INNER JOIN t_md_preorder_item oi ON oi.preorder_id = o.id
        INNER JOIN t_commodity c ON c.id = oi.commodity_id
        INNER JOIN t_store s ON s.id = o.store_id
        INNER JOIN t_md_shop ms ON ms.store_id = s.id
        LEFT JOIN t_order ord ON ord.id = o.order_id
        WHERE
        o.receive_status = 3
        AND o.order_status != 0
        AND oi.STATUS = 2
        <include refid="searchConditions"></include>
        <if test="settleTimeStart !=null and settleTimeEnd != null and settleTimeStart != '' and settleTimeEnd != '' ">
            and o.approving_time BETWEEN #{settleTimeStart} and #{settleTimeEnd}
        </if>
        )
        ) a
        <include refid="settlementDetailsReportSearchConditions"></include>
    </select>

    <!--结算清单报表-搜索条件-->
    <sql id="settlementDetailsReportSearchConditions">
        <where>
            <if test="subOrderCode != null and subOrderCode != ''">
                and a.subOrderCode like concat('%',#{subOrderCode},'%')
            </if>
            <if test="preOrderCode != null and preOrderCode != ''">
                and a.preOrderCode like concat('%',#{preOrderCode},'%')
            </if>
        </where>
    </sql>

    <!--公共搜索条件-->
    <sql id="searchConditions">
        <if test="shopId != null">
            and ms.id = #{shopId}
        </if>
        <if test="deliveryTimeStart !=null and deliveryTimeEnd != null and deliveryTimeStart != '' and deliveryTimeEnd != '' ">
            and o.order_time BETWEEN #{deliveryTimeStart} and #{deliveryTimeEnd}
        </if>
        <if test="logisticsModel != null">
            and o.logistics_model = #{logisticsModel}
        </if>
        <if test="categoryId != null">
            and ((c.commodity_first_id = #{categoryId}) or (c.commodity_second_id = #{categoryId}) or (c.commodity_third_id = #{categoryId}))
        </if>
        <if test="barCode!=null and barCode !='' ">
            AND c.id = (select tc.commodity_id as id from t_commodity_bar_code  tc where tc.bar_code = #{barCode})
        </if>

        <if test="commodityCode != null and commodityCode != ''">
            and
            (
            c.commodity_code like concat('%',#{commodityCode},'%')
            or
            c.commodity_name like concat('%',#{commodityCode},'%')
            or
            c.commodity_spec like concat('%',#{commodityCode},'%')
            )
        </if>
        <if test="null != consignmentId">
            and ord.consignment_id = #{consignmentId}
        </if>
        <if test="null != orderType">
            <choose>
                <when test="orderType == 0">
                    and (ord.order_type is null or ord.order_type = '')
                </when>
                <when test="orderType != 0">
                    and ord.order_type = #{orderType}
                </when>
            </choose>
        </if>
        <if test="null != stallId">
            and ord.stall_id = #{stallId}
        </if>
    </sql>



</mapper>
<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.pinshang.qingyun.order.manage.mapper.OrderReportMapper">

	<select id="findReturnOrderDetails" parameterType="com.pinshang.qingyun.order.manage.vo.report.ReturnOrderDetailsReportVo" resultType="com.pinshang.qingyun.order.manage.dto.report.ReturnOrderDetailsReportEntry">
		SELECT
		sro.create_time,
		sro.order_code,
		sro.stall_id,
		c.commodity_first_id,
		-- cate.cate_name,
		c.bar_code,
		-- cbc.bar_codes,
		-- ms.shop_name,
		c.commodity_code,
		c.commodity_name,
		c.commodity_spec,
		sroi.price,
		sroi.return_quantity,
		sroi.total_price,
		sroi.real_return_quantity,
		sroi.return_reason,
		-- ddd.option_name AS returnReasonName,
		sroi.remark,
		-- d.option_name as commodityUnit,
		sro.store_id,c.commodity_unit_id,c.commodity_first_id,sroi.return_reason,sroi.commodity_id
		FROM
		t_sale_return_order_item AS sroi
		INNER JOIN t_sale_return_order AS sro ON sro.id = sroi.sale_return_order_id
		-- LEFT JOIN t_md_shop AS ms ON ms.store_id = sro.store_id
		LEFT JOIN t_commodity AS c ON sroi.commodity_id = c.id
		-- LEFT JOIN t_dictionary d ON d.id = c.commodity_unit_id
		-- LEFT JOIN t_category AS cate ON c.commodity_first_id = cate.id
		/*LEFT JOIN (
			SELECT
				d.option_code,
				d.option_name
			FROM
			t_dictionary d
			WHERE
			d.dictionary_id = (
				SELECT
				dd.id
				FROM
				t_dictionary dd
				WHERE
				dd.option_name = '门店退货原因'
			)
		) ddd on ddd.option_code=sroi.return_reason*/
		/*LEFT JOIN (
			SELECT
				GROUP_CONCAT(bar_code ORDER BY default_state desc) AS bar_codes,
				commodity_id
			FROM
			t_commodity_bar_code
			GROUP BY
			commodity_id
		) AS cbc ON cbc.commodity_id = c.id*/
		WHERE
		sro.`status` != 0
		<if test="shopId != null and shopId != 0">
		    and sro.store_id =  (select md.store_id from t_md_shop md where md.id = #{shopId} limit 1)
		</if>
		<if test="orderCode != null and orderCode != ''">
			and sro.order_code = #{orderCode}
		</if>
		<if test="commodityFirstId != null">
			and ((c.`commodity_first_id`=#{commodityFirstId}) or (c.`commodity_second_id`=#{commodityFirstId}) or (c.`commodity_third_id`=#{commodityFirstId}))
		</if>
		<if test="searchWord != null and searchWord != ''">
			and
			(
			c.commodity_code like  concat('%',#{searchWord},'%')
			or
			c.commodity_name like  concat('%',#{searchWord},'%')
			or
			c.commodity_aid like  concat('%',#{searchWord},'%')
			)
		</if>
		<if test="barCode !=null and barCode !=''">
			and c.id = (
				SELECT
				commodity_id AS id
				FROM
				t_commodity_bar_code
				WHERE
				bar_code = #{barCode}
			)
		</if>
		<if test="orderTimeStartStr != null and orderTimeStartStr != '' and orderTimeEndStr != null and orderTimeEndStr != '' ">
			and sro.create_time between #{orderTimeStartStr} and  #{orderTimeEndStr}
		</if>
		<if test="returnReason != null">
			and sroi.return_reason = #{returnReason}
		</if>
		<if test="consignmentId != null">
			and sro.consignment_Id = #{consignmentId}
		</if>
		<if test="stallId != null">
			and sro.stall_id = #{stallId}
		</if>
	</select>




	<!-- 短交报表今天的数据 -->
	<select id="shortDeliveryTodayReport" resultType="com.pinshang.qingyun.order.manage.dto.report.ShortDeliveryReportODto" parameterType="com.pinshang.qingyun.order.manage.dto.report.ShortDeliveryReportIDto" >

     SELECT tt.* from (
		SELECT
		    o.`store_id`,
			o.order_time delivery_date,
			o.order_code,
			soi.commodity_id,
			soi.quantity orderNum,
		    soi.price,
			soi.real_delivery_quantity deliveryNum,
			soi.quantity-IFNULL(soi.real_delivery_quantity,0) as differNum,
		    concat(FORMAT((soi.quantity-soi.real_delivery_quantity)/soi.quantity*100,2),'%') as rate,
			o.create_id,
		   (CASE WHEN md.management_mode=1 THEN '直营' WHEN md.management_mode=2 THEN '外包' WHEN md.management_mode=3 THEN '档口分包' ELSE '' END) AS managementModeName,
		    o.stall_id AS stallId
		from t_order o
		 inner join `t_sub_order` so on so.`order_id` =  o.`id`
		 inner join `t_sub_order_item` soi on soi.`sub_order_id` =  so.`id`
		 inner JOIN t_md_shop md ON md.store_id = o.store_id
		WHERE so.status != 2 and o.order_status = 0
		and soi.real_delivery_quantity is not null
		<if test="beginDate != null and endDate != '' and beginDate != null and endDate != '' ">
		  and o.order_time BETWEEN #{beginDate} and #{endDate}
		</if>
		<if test="orderCode != null and orderCode !=''">
			AND o.order_code = #{orderCode}
		</if>
		<if test="differ != null and differ == true">
			and soi.quantity != soi.real_delivery_quantity
		</if>
		<if test="shopId != null and shopId != 0 ">
			AND o.`store_id` = (select store_id from t_md_shop where id = #{shopId})
		</if>
		<if test="managementMode != null and managementMode != ''">
			and md.management_mode = #{managementMode}
		</if>
		<if test="commodityIdList != null and commodityIdList.size() > 0">
			and soi.commodity_id IN
			<foreach collection="commodityIdList" index="index" item="id" open="(" separator="," close=")">
				#{id}
			</foreach>
		</if>
		<if test="storeIdList != null and storeIdList.size() > 0">
			and o.`store_id` IN
			<foreach collection="storeIdList" index="index" item="id" open="(" separator="," close=")">
				#{id}
			</foreach>
		</if>
		<if test="shopIdList != null and shopIdList.size() > 0">
			and md.`id` IN
			<foreach collection="shopIdList" index="index" item="id" open="(" separator="," close=")">
				#{id}
			</foreach>
		</if>
		<if test="stallList != null and stallList.size() > 0">
			and o.`stall_id` IN
			<foreach collection="stallList" index="index" item="id" open="(" separator="," close=")">
				#{id}
			</foreach>
		</if>
		<if test="consignmentId != null">
			AND o.`consignment_id`  = #{consignmentId}
		</if>
		<if test="stallId != null">
			AND o.`stall_id`  = #{stallId}
		</if>

		union all

		SELECT
			so.`store_id`,
			o.order_time delivery_date,
			so.order_code,
			soi.commodity_id,
			soi.require_quantity orderNum,
			soi.price,
		    soi.real_receive_quantity deliveryNum,
			soi.require_quantity-IFNULL(soi.real_receive_quantity,0) as differNum,
		    concat(FORMAT((soi.require_quantity-soi.real_receive_quantity)/soi.require_quantity*100,2),'%') as rate,
		    so.create_id,
		    (CASE WHEN md.management_mode=1 THEN '直营' WHEN md.management_mode=2 THEN '外包' WHEN md.management_mode=3 THEN '档口分包' ELSE '' END) AS managementModeName,
		    o.stall_Id AS stallId
		from t_md_preorder so
			 inner join `t_md_preorder_item` soi on soi.`preorder_id` =  so.`id`
			 INNER JOIN t_order o ON o.id = so.order_id
		     inner JOIN t_md_shop md ON md.store_id = so.store_id
		     left join t_stall st on o.`stall_id` = st.`id`
		WHERE  so.receive_status = 3 and so.order_status in (1,2)
		and soi.real_receive_quantity is not null
		<if test="beginDate != null and endDate != '' and beginDate != null and endDate != '' ">
			and o.order_time BETWEEN #{beginDate} and #{endDate}
		</if>
		<if test="orderCode != null and orderCode !=''">
			AND so.order_code = #{orderCode}
		</if>
		<if test="differ != null and differ == true">
			and soi.require_quantity != soi.real_receive_quantity
		</if>
		<if test="shopId != null and shopId != 0 ">
			AND so.`store_id` = (select store_id from t_md_shop where id = #{shopId})
		</if>
		<if test="managementMode != null and managementMode != ''">
			and md.management_mode = #{managementMode}
		</if>
		<if test="commodityIdList != null and commodityIdList.size() > 0">
			and soi.commodity_id IN
			<foreach collection="commodityIdList" index="index" item="id" open="(" separator="," close=")">
				#{id}
			</foreach>
		</if>
		<if test="storeIdList != null and storeIdList.size() > 0">
			and so.`store_id` IN
			<foreach collection="storeIdList" index="index" item="id" open="(" separator="," close=")">
				#{id}
			</foreach>
		</if>
		<if test="shopIdList != null and shopIdList.size() > 0">
			and md.`id` IN
			<foreach collection="shopIdList" index="index" item="id" open="(" separator="," close=")">
				#{id}
			</foreach>
		</if>
		<if test="consignmentId != null and consignmentId > 0">
			AND 1 = 2
		</if>
		<if test="stallList != null and stallList.size() > 0">
			and o.`stall_id` IN
			<foreach collection="stallList" index="index" item="id" open="(" separator="," close=")">
				#{id}
			</foreach>
		</if>
		<if test="stallId != null">
			AND o.`stall_id`  = #{stallId}
		</if>
	  ) tt WHERE 1=1 order by tt.store_id ASC,tt.delivery_date DESC,tt.commodity_id
	</select>





	<!--  商品实发汇总表 公用-->
	<sql id="realDeliveryReportWhere">
		select
			soi.`commodity_id`,
			soi.quantity quantity,
			IFNULL(soi.real_delivery_quantity,0) real_delivery_quantity,
			(soi.price*IFNULL(soi.real_delivery_quantity,0)) real_delivery_amount,
			soi.quantity-IFNULL(soi.real_delivery_quantity,0) differ_num
		from t_order o
		INNER JOIN `t_sub_order` so ON so.`order_id` = o.`id`
		INNER JOIN `t_sub_order_item` soi ON soi.`sub_order_id` = so.`id`
		inner JOIN t_md_shop md ON md.store_id = o.store_id
		where 1=1
		and so. STATUS != 2 and o.order_status = 0 and soi.real_delivery_quantity >= 0

		<if test="beginDate !=null and endDate != '' and beginDate != null and endDate != '' ">
			and o.order_time BETWEEN #{beginDate} and #{endDate}
		</if>
		<if test="commodityIdList != null and commodityIdList.size >0 ">
			and soi.commodity_id in
			<foreach collection="commodityIdList" index="index" item="commodityId" open="(" separator="," close=")">
				#{commodityId}
			</foreach>
		</if>
		<if test="differ != null and differ == true">
			and (soi.real_delivery_quantity is null or soi.quantity != soi.real_delivery_quantity)
		</if>
		<if test="consignmentId != null">
			AND o.`consignment_id`  = #{consignmentId}
		</if>

		UNION ALL

		select
			soi.`commodity_id`,
			soi.require_quantity quantity,
		    soi.real_receive_quantity real_delivery_quantity,
			(soi.price*IFNULL(soi.real_receive_quantity,0)) real_delivery_amount,
		    soi.require_quantity-IFNULL(soi.real_receive_quantity,0) differ_num
		from t_md_preorder so
		INNER JOIN `t_md_preorder_item` soi ON soi.`preorder_id` = so.`id`
		INNER JOIN t_order o ON o.id = so.order_id
		inner JOIN t_md_shop md ON md.store_id = so.store_id
		where 1=1
		and so.order_status in (1,2) and so.receive_status = 3 and soi.real_receive_quantity >= 0

		<if test="beginDate !=null and endDate != '' and beginDate != null and endDate != '' ">
			and o.order_time BETWEEN #{beginDate} and #{endDate}
		</if>
		<if test="commodityIdList != null and commodityIdList.size >0 ">
			and soi.commodity_id in
			<foreach collection="commodityIdList" index="index" item="commodityId" open="(" separator="," close=")">
				#{commodityId}
			</foreach>
		</if>
		<if test="differ != null and differ == true">
			and (soi.real_receive_quantity is null or soi.require_quantity != soi.real_receive_quantity)
		</if>
		<if test="consignmentId != null and consignmentId > 0">
			AND 1 = 2
		</if>
	</sql>

	<!--  商品实发汇总表 -->
	<select id="realDeliveryReportCurrentDay" resultType="com.pinshang.qingyun.order.manage.dto.report.RealDeliveryReportODto" parameterType="com.pinshang.qingyun.order.manage.dto.report.RealDeliveryReportDataQueryIDto" >
		SELECT
			t.commodity_id,
			sum(t.quantity) orderNum,
			sum(IFNULL(t.real_delivery_quantity,0)) deliveryNum,
			sum(IFNULL(t.real_delivery_amount,0)) realDeliveryAmount,
			sum(t.quantity-IFNULL(t.real_delivery_quantity,0)) differNum
		FROM (
		<include refid="realDeliveryReportWhere"></include>
		) t GROUP BY t.commodity_id
		ORDER  BY  t.commodity_id
	</select>
	<!--  商品实发汇总表  合计 -->
	<select id="realTotalDeliveryReportCurrentDay" resultType="java.math.BigDecimal" parameterType="com.pinshang.qingyun.order.manage.dto.report.RealDeliveryReportDataQueryIDto" >
		SELECT
			sum(IFNULL(t.real_delivery_amount,0))
		FROM
		(
		<include refid="realDeliveryReportWhere"></include>
		) t
	</select>





	<!--  商品实发汇总表(按客户类型) 公用 -->
	<sql id="realDeliveryStoreTypeReportWhere">
		select
			ts.store_type_id store_type_id,
			soi.`commodity_id`,
			soi.quantity quantity,
			IFNULL(soi.real_delivery_quantity,0) real_delivery_quantity,
			(soi.price*IFNULL(soi.real_delivery_quantity,0)) real_delivery_amount,
			soi.quantity-IFNULL(soi.real_delivery_quantity,0) differ_num
		from t_order o
		INNER JOIN `t_sub_order` so ON so.`order_id` = o.`id`
		INNER JOIN `t_sub_order_item` soi ON soi.`sub_order_id` = so.`id`
		LEFT join t_store ts on ts.id = o.`store_id`
		inner JOIN t_md_shop md ON md.store_id = o.store_id
		where so. STATUS != 2 and o.order_status = 0 and soi.real_delivery_quantity >= 0

		<if test="beginDate !=null and endDate != '' and beginDate != null and endDate != '' ">
			and o.order_time BETWEEN #{beginDate} and #{endDate}
		</if>
		<if test="commodityIdList != null and commodityIdList.size >0 ">
			and soi.commodity_id in
			<foreach collection="commodityIdList" index="index" item="commodityId" open="(" separator="," close=")">
				#{commodityId}
			</foreach>
		</if>
		<if test="differ != null and differ == true">
			and (soi.real_delivery_quantity is null or soi.quantity != soi.real_delivery_quantity)
		</if>
		<if test="storeTypeId != null">
			AND ts.store_type_id =  #{storeTypeId}
		</if>
		<if test="consignmentId != null">
			AND o.`consignment_id`  = #{consignmentId}
		</if>

		UNION ALL

		select
			ts.store_type_id store_type_id,
			soi.`commodity_id`,
			soi.require_quantity quantity,
			soi.real_receive_quantity real_delivery_quantity,
			(soi.price*IFNULL(soi.real_receive_quantity,0)) real_delivery_amount,
			soi.require_quantity-IFNULL(soi.real_receive_quantity,0) differ_num
		from t_md_preorder so
		INNER JOIN `t_md_preorder_item` soi ON soi.`preorder_id` = so.`id`
		INNER JOIN t_order o ON o.id = so.order_id
		LEFT join t_store ts on ts.id = so.`store_id`
		inner JOIN t_md_shop md ON md.store_id = so.store_id
		where  so.receive_status = 3 and so.order_status in (1,2) and soi.real_receive_quantity >= 0

		<if test="beginDate !=null and endDate != '' and beginDate != null and endDate != '' ">
			and o.order_time BETWEEN #{beginDate} and #{endDate}
		</if>
		<if test="commodityIdList != null and commodityIdList.size >0 ">
			and soi.commodity_id in
			<foreach collection="commodityIdList" index="index" item="commodityId" open="(" separator="," close=")">
				#{commodityId}
			</foreach>
		</if>
		<if test="differ != null and differ == true">
			and (soi.real_receive_quantity is null or soi.require_quantity != soi.real_receive_quantity)
		</if>
		<if test="storeTypeId != null">
			AND ts.store_type_id =  #{storeTypeId}
		</if>
		<if test="consignmentId != null and consignmentId > 0">
			AND 1 = 2
		</if>
	</sql>

	<!--  商品实发汇总表 (按客户类型)-->
	<select id="realDeliveryStoreTypeReportCurrentDay" resultType="com.pinshang.qingyun.order.manage.dto.report.RealDeliveryReportODto" parameterType="com.pinshang.qingyun.order.manage.dto.report.RealDeliveryReportIDto" >
		SELECT
			t.store_type_id,
			t.commodity_id,
			sum(t.quantity) orderNum,
			sum(IFNULL(t.real_delivery_quantity,0)) deliveryNum,
			sum(IFNULL(t.real_delivery_amount,0)) realDeliveryAmount,
			sum(t.quantity-IFNULL(t.real_delivery_quantity,0)) differNum
		FROM (
		<include refid="realDeliveryStoreTypeReportWhere"></include>
		) t GROUP BY t.store_type_id,t.commodity_id
		ORDER  BY  t.store_type_id,t.commodity_id
	</select>

	<!--  商品实发汇总表 (按客户类型) 合计 -->
	<select id="realTotalDeliveryStoreTypeReportCurrentDay" resultType="java.math.BigDecimal" parameterType="com.pinshang.qingyun.order.manage.dto.report.RealDeliveryReportIDto" >
		SELECT
			sum(IFNULL(t.real_delivery_amount,0))
		FROM
		(
		<include refid="realDeliveryStoreTypeReportWhere"></include>
		) t
	</select>





	<!--  门店订货汇总表 -->
	<select id="shopOrderGoodReport" resultType="com.pinshang.qingyun.order.manage.dto.report.ShopOrderGoodReportODto" parameterType="com.pinshang.qingyun.order.manage.dto.report.ShopOrderGoodReportIDto" >
		SELECT
			t.store_id,
			t.commodity_id,
			sum(t.quantity) orderNum,
			sum(IFNULL(t.real_delivery_quantity,0)) deliveryNum,
			sum(IFNULL(t.real_delivery_amount,0)) realDeliveryAmount,
			sum(t.quantity-IFNULL(t.real_delivery_quantity,0)) differNum
		FROM (

		select
			o.`store_id`,
			soi.`commodity_id`,
			soi.quantity quantity,
			IFNULL(soi.real_delivery_quantity,0) real_delivery_quantity,
			(soi.price*IFNULL(soi.real_delivery_quantity,0)) real_delivery_amount,
			soi.quantity-IFNULL(soi.real_delivery_quantity,0) differ_num
		from t_order o
		INNER JOIN `t_sub_order` so ON so.`order_id` = o.`id`
		INNER JOIN `t_sub_order_item` soi ON soi.`sub_order_id` = so.`id`
		inner join t_md_shop md on md.store_id = o.`store_id`
		where so. STATUS != 2
		and o.order_status = 0
		<if test="beginDate !=null and endDate != '' and beginDate != null and endDate != '' ">
			and o.order_time BETWEEN #{beginDate} and #{endDate}
		</if>
		<if test="commodityIdList != null and commodityIdList.size >0 ">
			and soi.`commodity_id` in
			<foreach collection="commodityIdList" index="index" item="commodityId" open="(" separator="," close=")">
				#{commodityId}
			</foreach>
		</if>
		<if test="differ != null and differ == true">
			and soi.quantity != soi.real_delivery_quantity
		</if>
		<if test="shopId != null and shopId != 0 ">
			AND o.`store_id` = (select store_id from t_md_shop where id = #{shopId})
		</if>
		<if test="storeIdList != null and storeIdList.size() > 0">
			and o.`store_id` IN
			<foreach collection="storeIdList" index="index" item="id" open="(" separator="," close=")">
				#{id}
			</foreach>
		</if>

		UNION ALL

		select
			so.`store_id`,
			soi.`commodity_id`,
			soi.require_quantity quantity,
			soi.real_receive_quantity real_delivery_quantity,
			(soi.price*IFNULL(soi.real_receive_quantity,0)) real_delivery_amount,
			soi.require_quantity-IFNULL(soi.real_receive_quantity,0) differ_num
		from t_md_preorder so
		INNER JOIN `t_md_preorder_item` soi ON soi.`preorder_id` = so.`id`
		INNER JOIN t_order o ON o.id = so.order_id
		inner join t_md_shop md on md.store_id = so.`store_id`
		where 1=1
		and so.order_status in (1,2) AND so.receive_status = 3
		<if test="beginDate !=null and endDate != '' and beginDate != null and endDate != '' ">
			and o.order_time BETWEEN #{beginDate} and #{endDate}
		</if>
		<if test="commodityIdList != null and commodityIdList.size >0 ">
			and soi.`commodity_id` in
			<foreach collection="commodityIdList" index="index" item="commodityId" open="(" separator="," close=")">
				#{commodityId}
			</foreach>
		</if>
		<if test="differ != null and differ == true">
			and soi.require_quantity != soi.real_receive_quantity
		</if>
		<if test="shopId != null and shopId != 0 ">
			AND so.`store_id` = (select store_id from t_md_shop where id = #{shopId})
		</if>
		<if test="storeIdList != null and storeIdList.size() > 0">
			and so.`store_id` IN
			<foreach collection="storeIdList" index="index" item="id" open="(" separator="," close=")">
				#{id}
			</foreach>
		</if>

		) t GROUP BY t.store_id,t.commodity_id
		ORDER  BY  t.store_id,t.commodity_id
	</select>




	<!-- 门店实收商品分析表 -->
	<select id="actualReceiptAnalysisReport" resultType="com.pinshang.qingyun.order.manage.dto.report.ActualReceiptAnalysisODto" parameterType="com.pinshang.qingyun.order.manage.dto.report.ActualReceiptAnalysisIDto">
		select
			tmp.store_id,
			tmp.commodity_id,
			IFNULL(sum(tmp.totalQuantity),0) as totalQuantity,
			IFNULL(sum(tmp.totalRealReceiveQuantity),0) as totalRealReceiveQuantity,
			IFNULL(sum(tmp.totalRealDeliveryQuantity),0) as totalRealDeliveryQuantity
		from (
			SELECT
				o.`store_id`,
				soi.`commodity_id`,
				sum(soi.quantity) as totalQuantity,
				sum(soi.real_receive_quantity) as totalRealReceiveQuantity,
				sum(soi.real_delivery_quantity) as totalRealDeliveryQuantity
			FROM
		       t_order o
			inner join `t_sub_order` so on so.`order_id` =  o.`id`
			inner join `t_sub_order_item` soi on soi.`sub_order_id` =  so.`id`
			LEFT JOIN t_md_receive_order AS mro ON mro.sub_order_id = so.id
			inner JOIN t_md_shop md ON md.store_id = o.store_id
			WHERE so.status != 2 and
			(
				mro.`status` != 0  AND mro.`status` != 4
			)
			<if test="shopId != null and shopId != 0 ">
				AND o.`store_id` = (select store_id from t_md_shop where id = #{shopId})
			</if>
			<if test="beginDate != null and endDate != null ">
				and o.order_time between #{beginDate} and #{endDate}
			</if>
			<if test="commodityIdList != null and commodityIdList.size >0 ">
				and soi.`commodity_id` in
				<foreach collection="commodityIdList" index="index" item="commodityId" open="(" separator="," close=")">
					#{commodityId}
				</foreach>
			</if>
			GROUP BY soi.`commodity_id`,o.`store_id`

			UNION ALL

			SELECT
				so.`store_id`,
				soi.`commodity_id`,
				sum(soi.require_quantity) as totalQuantity,
				sum(soi.real_receive_quantity) as totalRealReceiveQuantity,
				sum(soi.real_receive_quantity) as totalRealDeliveryQuantity
			FROM
				t_md_preorder AS so
			inner join `t_md_preorder_item` soi on soi.`preorder_id` =  so.`id`
			INNER JOIN t_order o ON o.id = so.order_id
			inner JOIN t_md_shop md ON md.store_id = so.store_id
			where so.receive_status = 3
			<if test="shopId != null and shopId != 0 ">
				AND so.`store_id` = (select store_id from t_md_shop where id = #{shopId})
			</if>
			<if test="beginDate != null and endDate != null ">
				and o.order_time between #{beginDate} and #{endDate}
			</if>
			<if test="commodityIdList != null and commodityIdList.size >0 ">
				and soi.`commodity_id` in
				<foreach collection="commodityIdList" index="index" item="commodityId" open="(" separator="," close=")">
					#{commodityId}
				</foreach>
			</if>
			GROUP BY soi.`commodity_id`,o.`store_id`
		)  as tmp
		GROUP BY tmp.store_id,tmp.commodity_id
		ORDER BY
		tmp.store_id
	</select>





	<select id="consignmentOrderReport" resultType="com.pinshang.qingyun.order.manage.dto.report.ConsignmentOrderPageODTO">
		SELECT
			DATE_FORMAT(mp.order_time,'%Y-%m-%d') orderTime,
			mp.order_code,
			mp.store_id,
			md.id shopId,
			mpi.commodity_id,
			mpi.require_quantity,
			mpi.receive_quantity realReceiveQuantity,
			mpi.check_quantity auditQuantity
		from t_md_preorder mp
		LEFT JOIN t_md_preorder_item mpi on mpi.preorder_id = mp.id
		LEFT JOIN t_md_shop md on md.store_id = mp.store_id
		where mp.consignment_id > 0 and mp.receive_status != 4
		<if test="vo.shopId != null" >
			AND md.id = #{vo.shopId}
		</if>

		<if test="vo.shopIdList != null and vo.shopIdList.size > 0">
			AND md.id IN
			<foreach collection="vo.shopIdList" index="index" item="id" open="(" separator="," close=")">
				#{id}
			</foreach>
		</if>

		<if test="vo.beginDate != null and vo.beginDate != '' and vo.endDate != null and vo.endDate != '' ">
			and mp.order_time BETWEEN #{vo.beginDate} and #{vo.endDate}
		</if>

		<if test="vo.orderCode != null and vo.orderCode != '' ">
			and mp.order_code = #{vo.orderCode}
		</if>

		<if test="vo.consignmentId != null" >
			AND mp.consignment_id = #{vo.consignmentId}
		</if>


		<if test="vo.commodityIdList != null and vo.commodityIdList.size() > 0">
			AND mpi.commodity_id in <foreach collection="vo.commodityIdList" item="id" open="(" separator="," close=")"> #{id} </foreach>
		</if>

		order by orderTime desc,md.shop_code,mp.order_code
	</select>

	<select id="saleReturnDetailPage" resultType="com.pinshang.qingyun.order.manage.dto.report.SaleReturnDetailPageODTO"
			parameterType="com.pinshang.qingyun.order.manage.dto.report.SaleReturnDetailPageIDTO">
		SELECT
			r.id returnId,
			r.`store_id`,
			ms.`id` shopId,
			ms.`shop_name` shopName,
			r.`create_time` returnDate,
			r.`order_code` returnCode,
			c.`commodity_code` commodityCode,
			c.`commodity_name` commodityName,
			c.`bar_code` barCode,
			tc.cate_name AS cateName,
			c.commodity_spec AS commoditySpec,
			td.`option_name` commodityUnit,
			ri.`price`,
			ri.`return_quantity` returnQuantity,
			ri.`return_reason` returnReasonId,
			ri.`remark`,
			ri.`real_return_quantity` realReturnQuantity,
			ri.total_price totalPrice,
			ri.rp_type reTypeId,
			ri.`compensate_price` compensatePrice,
			ri.`audit_remark` auditRemark,
			c.id commodityId,
			r.`status`,
			r.`create_id` returnUserId,
			DATE_FORMAT(r.`create_time`,'%Y-%m-%d %H:%i:%S') AS returnTime,
			r.`update_id` auditUserId,
			r.`update_id` cancelUserId,
			DATE_FORMAT(r.`update_time`,'%Y-%m-%d %H:%i:%S') AS auditTime,
			DATE_FORMAT(r.`update_time`,'%Y-%m-%d %H:%i:%S') AS cancelTime,
			r.stall_id
		FROM
		t_sale_return_order_item ri
		LEFT JOIN t_sale_return_order r
		ON r.`id` = ri.`sale_return_order_id`
		LEFT JOIN t_commodity c
		ON c.`id` = ri.`commodity_id`
		LEFT JOIN t_md_shop ms
		ON ms.`store_id` = r.`store_id`
		LEFT JOIN t_dictionary td ON c.commodity_unit_id = td.id
		LEFT JOIN t_category tc ON c.commodity_first_id = tc.id
		WHERE 1=1
		<if test="null != shopId">
			AND ms.id = #{shopId}
		</if>
		<if test="null != rpType">
			AND ri.`rp_type` = #{rpType}
		</if>
		<if test="null != returnCode and '' != returnCode">
			AND r.`order_code` = #{returnCode}
		</if>
		<if test="null != returnReason">
			AND ri.`return_reason` = #{returnReason}
		</if>
		<if test="null != returnType and returnType == 2">
			AND ri.`return_reason` = 50
		</if>
		<if test="null != returnType and returnType == 1">
			AND ri.`return_reason` != 50
		</if>
		<if test="null != commodityId">
			AND ri.`commodity_id` = #{commodityId}
		</if>
		<if test="null != beginTime and '' != beginTime and null != endTime and '' != endTime">
			AND r.create_time between #{beginTime} and #{endTime}
		</if>
		<if test="cateId1 != null">
			AND c.commodity_first_id = #{cateId1}
		</if>
		<if test="cateId2 != null">
			AND c.commodity_second_id = #{cateId2}
		</if>
		<if test="cateId3 != null">
			AND c.commodity_third_id = #{cateId3}
		</if>
		<if test="consignmentId != null">
			AND r.consignment_id = #{consignmentId}
		</if>
		<if test="stallId != null">
			and r.stall_id = #{stallId}
		</if>
		<if test="stallIdList != null and stallIdList.size > 0">
			AND r.stall_id IN
			<foreach collection="stallIdList" index="index" item="stallid" open="(" separator="," close=")">
				#{stallid}
			</foreach>
		</if>
		ORDER BY r.`order_code` DESC, ri.`id` ASC
	</select>


	<sql id="queryReportCondition">
		WHERE ro.`status` = 2
		<choose>
			<when test="vo.shopId != null and vo.shopId !='' " > AND ms.id = #{vo.shopId} </when>
			<otherwise>
				AND ms.id IN
				<foreach collection="vo.shopIdList" index="index" item="id" open="(" separator="," close=")">
					#{id}
				</foreach>
			</otherwise>
		</choose>
		<if test=" vo.orderCode != null and vo.orderCode != ''">
			AND ro.order_code = #{vo.orderCode}
		</if>
		<if test="vo.startTime !=null and vo.startTime !=''">
			AND ro.create_time <![CDATA[>=]]> CONCAT(#{vo.startTime},' 00:00:00')
		</if>
		<if test="vo.endTime !=null and vo.endTime !=''">
			AND ro.create_time <![CDATA[<=]]> CONCAT(#{vo.endTime},' 23:59:59')
		</if>
		<if test="vo.updateStartTime !=null and vo.updateStartTime !=''">
			AND ro.update_time <![CDATA[>=]]> CONCAT(#{vo.updateStartTime},' 00:00:00')
		</if>
		<if test="vo.updateEndTime !=null and vo.updateEndTime !=''">
			AND ro.update_time <![CDATA[<=]]> CONCAT(#{vo.updateEndTime},' 23:59:59')
		</if>
		<if test="vo.commodityId !=null">
			AND tc.id = #{vo.commodityId}
		</if>
		<if test="vo.commodityCode !=null and vo.commodityCode != ''">
			AND (
			tc.commodity_code like  concat('%',trim(#{vo.commodityCode}),'%')
			or tc.commodity_name like  concat('%',trim(#{vo.commodityCode}),'%')
			or tc.commodity_aid like  concat('%',trim(#{vo.commodityCode}),'%')
			)
		</if>
		<if test="vo.barCode !=null and vo.barCode != ''">
			AND tc.id = (SELECT commodity_id FROM t_commodity_bar_code WHERE bar_code = #{vo.barCode})
		</if>
		<if test="vo.consignmentId !=null">
			AND ro.consignment_id = #{vo.consignmentId}
		</if>
		<if test="vo.stallId !=null">
			AND ro.stall_id = #{vo.stallId}
		</if>
	</sql>

	<!-- 退货差异报表实退金额合计 -->
	<select id="queryReturnReportSumEntry" resultType="com.pinshang.qingyun.order.manage.dto.report.SaleReturnReportSumODTO">
		SELECT
		sum(roi.real_return_quantity * roi.price) AS totalReturnPrice
		FROM t_sale_return_order_item roi
		INNER JOIN t_sale_return_order ro ON roi.sale_return_order_id = ro.id
		INNER JOIN t_commodity tc ON roi.commodity_id = tc.id
		LEFT JOIN t_md_shop ms on ms.store_id=ro.store_id
		<include refid="queryReportCondition" />
	</select>

	<!-- 分页查询退货差异报表 -->
	<select id="listReturnReport" resultType="com.pinshang.qingyun.order.manage.dto.report.SaleReturnReportODTO">
		SELECT
			roi.id,
			ro.enterprise_id,
			ro.stall_id,
			ts.store_code,
			ts.store_name,
			ro.order_code,
			ro.create_time,
			ro.update_time,
			roi.commodity_id,
			tc.commodity_code,
			tc.bar_code,
			tc.commodity_name,
			tc.commodity_spec,
			roi.return_quantity,
			roi.real_return_quantity,
			roi.price,
			roi.total_price,
			--         bar_codes,
			ms.shop_name shopName,
			roi.return_reason,
			ddd.option_name returnReasonName,
			roi.rp_type
		FROM t_sale_return_order_item roi
		INNER JOIN t_sale_return_order ro ON roi.sale_return_order_id = ro.id
		INNER JOIN t_store ts ON ro.store_id = ts.id
		INNER JOIN t_commodity tc ON roi.commodity_id = tc.id
		--         left join ( SELECT GROUP_CONCAT(bar_code) as bar_codes, commodity_id FROM t_commodity_bar_code group by commodity_id) AS cbc on cbc.commodity_id = tc.id
		LEFT JOIN t_md_shop ms on ms.store_id=ro.store_id
		LEFT JOIN (
			SELECT
				d.option_code,
				d.option_name
			FROM
			t_dictionary d
			WHERE
			d.dictionary_id = (
			SELECT
			dd.id
			FROM
			t_dictionary dd
			WHERE
			dd.option_name = '门店退货原因'
			)
		) ddd on ddd.option_code=roi.return_reason
		<include refid="queryReportCondition" />
		ORDER BY ro.create_time DESC
	</select>

	<select id="consignmentReturnOrderReport" resultType="com.pinshang.qingyun.order.manage.dto.report.ConsignmentReturnOrderPageODTO">
		SELECT
			DATE_FORMAT(csro.create_time,'%Y-%m-%d') orderTime,
			csro.order_code,
			csro.store_id,
			md.id shopId,
			csroi.commodity_id,
			csroi.return_quantity,
			csroi.confirm_quantity,
			csroi.check_quantity
		from t_consignment_sale_return_order csro
		LEFT JOIN t_consignment_sale_return_order_item csroi on csroi.consignment_sale_return_order_id = csro.id
		LEFT JOIN t_md_shop md on md.store_id = csro.store_id
		where csro.status != 0
		<if test="vo.shopId != null" >
			AND md.id = #{vo.shopId}
		</if>

		<if test="vo.shopIdList != null and vo.shopIdList.size > 0">
			AND md.id IN
			<foreach collection="vo.shopIdList" index="index" item="id" open="(" separator="," close=")">
				#{id}
			</foreach>
		</if>

		<if test="vo.beginDate != null and vo.beginDate != '' and vo.endDate != null and vo.endDate != '' ">
			and csro.create_time BETWEEN #{vo.beginDate} and #{vo.endDate}
		</if>

		<if test="vo.orderCode != null and vo.orderCode != '' ">
			and csro.order_code = #{vo.orderCode}
		</if>

		<if test="vo.consignmentId != null" >
			AND csro.supplier_id = #{vo.consignmentId}
		</if>


		<if test="vo.commodityIdList != null and vo.commodityIdList.size() > 0">
			AND csroi.commodity_id in <foreach collection="vo.commodityIdList" item="id" open="(" separator="," close=")"> #{id} </foreach>
		</if>

		order by orderTime desc,md.shop_code,csro.order_code
	</select>


	<select id="listShopAutoOrder" resultType="com.pinshang.qingyun.order.manage.dto.report.AutoPreOrderODTO">
		SELECT
			apo.id as id,
			ms.shop_name as shopName,
			apo.logistics_model as logisticsModel,
			apo.order_code as orderCode,
			apo.order_amount as orderAmount,
			u.employee_name as createName,
			apo.create_time as createTime,
			u1.employee_name as auditName,
			apo.audit_time as auditTime,
			apo.status as status,
			apo.order_id as orderId,
			apo.delivery_time as deliveryTime
		FROM t_md_auto_pre_order apo
		LEFT JOIN t_md_shop ms ON apo.shop_id = ms.id
		LEFT JOIN t_employee_user u ON apo.create_id = u.user_id
		LEFT JOIN t_employee_user u1 on apo.audit_id = u1.user_id
		where 1=1
		<if test="vo.shopId != null">
			and apo.shop_id = #{vo.shopId}
		</if>
		<if test="vo.shopIdList != null and vo.shopIdList.size > 0">
			AND apo.shop_id IN
			<foreach collection="vo.shopIdList" index="index" item="id" open="(" separator="," close=")">
				#{id}
			</foreach>
		</if>
		<if test="vo.orderCode != null  and vo.orderCode != '' ">
			and apo.order_code like CONCAT("%", #{vo.orderCode} ,"%")
		</if>
		<if test="vo.createId != null">
			AND apo.create_id = #{vo.createId}
		</if>
		<if test="vo.status != null">
			AND apo.status = #{vo.status}
		</if>
		<if test="vo.deliveryStartTime !=null and vo.deliveryStartTime !='' and vo.deliveryEndTime !=null and vo.deliveryEndTime !=''">
			AND apo.delivery_time BETWEEN date(#{vo.deliveryStartTime}) AND date(#{vo.deliveryEndTime})
		</if>
		<if test="!vo.isInternal and (vo.consignmentId == null or vo.consignmentId == 0)">
			and apo.consignment_id = -1
		</if>
		<if test="!vo.isInternal and vo.consignmentId != null and vo.consignmentId > 0 ">
			and apo.consignment_id = #{vo.consignmentId}
		</if>
		order by apo.create_time DESC
	</select>
	<select id="xsjmSummaryReport"
			resultType="com.pinshang.qingyun.order.manage.dto.XsjmOrderBillSummaryReportDTO">
		select sum(ar_amount) sumArAmount,sum(pa_amount) sumPaAmount,bill_type from t_order_bill
		where store_id=#{shopId}
		and create_time between concat(#{queryDate},' 00:00:00') and concat(#{queryDate},' 23:59:59')
		group by bill_type
	</select>
	<select id="selectXsjmStoreBalance" resultType="java.math.BigDecimal">
		select store_balance from t_order_bill
		where  store_id = #{storeId}
		and create_time &lt; concat(#{queryDate},' 00:00:00')
		order by create_time desc limit 1
	</select>
</mapper>
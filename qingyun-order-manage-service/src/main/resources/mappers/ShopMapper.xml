<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.pinshang.qingyun.order.manage.mapper.ShopMapper">

    <select id="queryShopByStoreIdList" resultType="com.pinshang.qingyun.order.manage.dto.ShopODTO">
        SELECT
            s.id AS storeId,
            md.id shopId,
            md.shop_code,
            md.shop_name,
            s.store_code,
            s.store_name
        FROM t_store s
        LEFT JOIN t_distribution_line dl ON dl.id = s.store_line_id
        LEFT JOIN t_dictionary dd ON dd.id = dl.delivery_time_id
        INNER JOIN t_md_shop md on md.store_id = s.id
        WHERE 1 = 1 and md.shop_status in(2,1) and md.management_mode != 3
        <if test="lineGroupIdList != null and lineGroupIdList.size() > 0">
            AND dd.id in <foreach collection="lineGroupIdList" item="id" open="(" separator="," close=")"> #{id} </foreach>
        </if>
        <if test="storeIdList != null and storeIdList.size() > 0">
            AND s.id in <foreach collection="storeIdList" item="id" open="(" separator="," close=")"> #{id} </foreach>
        </if>
    </select>


    <select id="selectStoreList" resultType="com.pinshang.qingyun.order.manage.mapper.entry.StoreEntry">
        SELECT
            s.id storeId,
            s.store_code,
            s.store_name,
            dl.line_name store_line_group_name,
            tms.shop_type,
            tms.shop_code,
            tms.shop_name,
            tms.id shopId,
            s.business_type
        FROM t_store s
        left join t_md_shop tms on s.id = tms.store_id
        left join t_distribution_line dl on dl.id = s.store_line_id
        where 1=1
        and s.`id` IN
        <foreach collection="storeIdList" index="index" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>

    <select id = "queryShopIdListByParam" resultType="java.lang.Long">
        select t.id from t_md_shop t
        where 1 = 1
        <if test="shopTypeList != null and shopTypeList.size > 0">
            AND t.shop_type in
            <foreach collection="shopTypeList" index="index" item="shopType" open="(" separator="," close=")">
                #{shopType}
            </foreach>
        </if>

        <if test="managementModeList != null and managementModeList.size > 0">
            AND t.management_mode in
            <foreach collection="managementModeList" index="index" item="managementMode" open="(" separator="," close=")">
                #{managementMode}
            </foreach>
        </if>
    </select>

</mapper>
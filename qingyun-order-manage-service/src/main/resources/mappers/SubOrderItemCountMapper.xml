<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.pinshang.qingyun.order.manage.mapper.SubOrderCountMapper" >

    <select id = "shopOrderNum" resultType="com.pinshang.qingyun.order.manage.dto.ShopOrderNumODTO">
        SELECT IFNULL(COUNT(DISTINCT(oi.commodity_id)), 0) AS 'reserveNum',
               IFNULL(COUNT(DISTINCT(ic.commodity_id)), 0) AS 'countNum'
        FROM t_order o
        LEFT JOIN t_sub_order so ON o.id = so.order_id
        LEFT JOIN t_sub_order_item oi on oi.sub_order_id = so.id
        LEFT JOIN t_sub_order_item_count ic ON (ic.send_date = o.order_time and ic.shop_id = #{shopId} )
        WHERE o.store_id = #{storeId}
        AND o.order_time = #{date}
        AND so.status != 2
        AND oi.real_delivery_quantity IS NOT NULL AND oi.real_delivery_quantity > 0
    </select>


    <select id="countCommodityListForPage" resultType="com.pinshang.qingyun.order.manage.dto.shopCount.ShopCountStockPageODTO">
        select
        c.shop_id,
        c.send_date orderTime,
        count(c.commodity_id) countNum
        from t_sub_order_item_count c
        where c.shop_id IN
        <foreach collection="shopIdList" item="shopId" open="(" close=")" separator=",">
            #{shopId}
        </foreach>
        AND c.send_date >= #{beginTime}
        AND c.send_date &lt; #{endTime}
        GROUP BY c.shop_id, c.send_date
    </select>

    <select id="countCommodityListForDetail" resultType="com.pinshang.qingyun.order.manage.dto.shopCount.ShopCountStockDetailItemODTO"
            parameterType="com.pinshang.qingyun.order.manage.dto.shopCount.ShopCountStockDetailIDTO">
        select
            c.shop_id,
            c.send_date orderTime,
            c.`commodity_id`,
            c.update_id updateId,
            c.count_quantity countNum,
            c.update_time updateUserTime
        from t_sub_order_item_count c
        where c.shop_id = #{shopId}
        AND c.send_date = #{orderTime}
        <if test="null != commodityId">
            AND c.commodity_id = #{commodityId}
        </if>
        <if test="barCode != null and barCode !='' ">
            and c.commodity_id = (SELECT  commodity_id FROM t_commodity_bar_code WHERE  bar_code = #{barCode})
        </if>
        GROUP BY c.shop_id, c.send_date, c.`commodity_id`
        order by c.`commodity_id`
    </select>


    <select id="selectShopCommodityVarietyTotal" resultType="com.pinshang.qingyun.order.manage.dto.shopCount.ShopCountStockPageODTO"
            parameterType="com.pinshang.qingyun.order.manage.dto.shopCount.ShopCountStockPageIDTO">
        SELECT
            shopName,
            shopId,
            storeId,
            orderTime,
            COUNT(commodity_id) AS varietyTotal
        FROM
        (
            SELECT
                ms.`shop_name` AS shopName,
                ms.`id` AS shopId,
                o.`store_id` AS storeId,
                o.`order_time` AS orderTime,
                soi.`commodity_id`
            FROM
            t_sub_order_item soi
            INNER JOIN t_sub_order so
            ON so.`id` = soi.`sub_order_id`
            INNER JOIN t_order o
            ON o.`id` = so.`order_id`
            LEFT JOIN t_md_shop ms
            ON ms.store_id = o.store_id
            WHERE o.`order_time` >= #{beginTime}
            AND o.`order_time` &lt; #{endTime}
            <if test="null != shopId">
                AND ms.`id` = #{shopId}
            </if>
            <if test=" null == shopId and null != shopIdList and shopIdList.size() > 0">
                AND ms.`id` IN
                <foreach collection="shopIdList" separator="," open="(" close=")" item="item">
                    #{item}
                </foreach>
            </if>
            AND o.order_status = 0
            AND (soi.real_delivery_quantity IS NOT NULL AND soi.real_delivery_quantity > 0)
            GROUP BY
            ms.`id`,
            o.`order_time`,
            soi.`commodity_id`
        ) a
        GROUP BY shopId,orderTime
        ORDER BY orderTime DESC, shopId DESC
    </select>

    <select id="selectShopCommodityVarietyDetail" resultType="com.pinshang.qingyun.order.manage.dto.shopCount.ShopCountStockDetailItemODTO"
            parameterType="com.pinshang.qingyun.order.manage.dto.shopCount.ShopCountStockDetailIDTO">
        SELECT
            ms.`id` AS shopId,
            o.`order_time` AS orderTime,
            soi.`commodity_id` commodityId,
            IFNULL(sum(soi.`real_delivery_quantity`), 0) AS varietyTotal
        FROM
        t_sub_order_item soi
        INNER JOIN t_sub_order so
        ON so.`id` = soi.`sub_order_id`
        INNER JOIN t_order o
        ON o.`id` = so.`order_id`
        LEFT JOIN t_md_shop ms
        ON ms.store_id = o.store_id
        WHERE o.`order_time` = #{orderTime}
        AND ms.`id` = #{shopId}
        <if test="null != commodityId">
            AND soi.`commodity_id` = #{commodityId}
        </if>
        <if test="barCode != null and barCode !='' ">
            and soi.commodity_id = (SELECT  commodity_id FROM t_commodity_bar_code WHERE  bar_code = #{barCode})
        </if>
        AND o.order_status = 0
        GROUP BY
        ms.`id`,
        o.`order_time`,
        soi.`commodity_id`
        HAVING SUM(soi.`real_delivery_quantity`) > 0
        ORDER BY soi.`commodity_id`
    </select>

</mapper>
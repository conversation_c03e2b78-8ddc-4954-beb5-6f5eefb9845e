<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.pinshang.qingyun.order.manage.mapper.CommodityMapper">

    <select id="queryCommodityByIdList" resultType="com.pinshang.qingyun.order.manage.dto.CommodityODTO">
        SELECT
           t.id commodityId,
           t.commodity_code commodityCode,
           t.commodity_name commodityName,
           t.commodity_first_id commodityFirstId,
           IFNULL(t.commodity_package_spec,1) commodityPackageSpec,
           t.product_type,
           t.`commodity_spec`,
           t.quality_days,
           t.quality_unit,
           di.`option_name` commodityUnit
        FROM t_commodity t
        left  join t_dictionary di on di.`id` = t.`commodity_unit_id`
        where  t.`id` IN
        <foreach collection="commodityIdList" index="index" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>

    <select id="findCommodityInfoByCodes" resultType="com.pinshang.qingyun.order.manage.dto.XDCommodityODTO">
        SELECT
            t.id commodityId,
            t.commodity_code,
            t.commodity_name,
            t.`commodity_spec`,
            IFNULL(t.commodity_package_spec,1) commodityPackageSpec,
            t.`commodity_is_quick_freeze` frozen,
            t.`sales_box_capacity`,
            t.`xd_sales_box_capacity`,
            t.`commodity_state` commodityStatus,
            t.logistics_model,
            td.option_name commodityUnit
        FROM t_commodity t
        LEFT JOIN t_dictionary td
        ON t.commodity_unit_id = td.id
        WHERE t.product_type != 2 and t.commodity_code IN
        <foreach collection="commodityCodes" item="commodityCode" open="(" separator="," close=")">
            #{commodityCode}
        </foreach>
    </select>

    <select id="getXDCommodityODTOById" resultType="com.pinshang.qingyun.order.manage.dto.XDCommodityODTO">
        select
            c.`id` commodityId,
            c.`commodity_code`,
            c.`commodity_name`,
            c.`commodity_spec`,
            c.`product_type` productType,
            di.`option_name` commodityUnit,
            c.`xd_sales_box_capacity`,
            c.`sales_box_capacity`,
            d.`option_code` newProductFlag,
            IFNULL(c.commodity_package_spec,1) commodityPackageSpec,
            c.logistics_model
        from  t_commodity c
                  left  join `t_dictionary` di on di.`id` = c.`commodity_unit_id`
                  left  join `t_dictionary` d on d.`id` = c.`commodity_cycle_id`
        WHERE c.id = #{commodityId}
    </select>

    <select id="findCommodityBarCodeByParam" resultType="com.pinshang.qingyun.order.manage.model.Commodity">
        SELECT  t.commodity_id id,
        GROUP_CONCAT(t.bar_code ORDER BY t.default_state desc) barCode
        from t_commodity_bar_code t
        where 1=1
        <if test="barCode != null and barCode !='' ">
            and  t.commodity_id = (SELECT tt.commodity_id from t_commodity_bar_code tt where tt.bar_code=#{barCode})
        </if>
        <if test="commodityIdList != null and commodityIdList.size >0 ">
            AND t.commodity_id IN
            <foreach collection="commodityIdList" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
        GROUP BY t.commodity_id
    </select>


    <select id="findCommodityBasicListByParam" parameterType="com.pinshang.qingyun.order.manage.vo.CommodityVO" resultType="com.pinshang.qingyun.order.manage.dto.CommodityBasicODTO">
        SELECT
            c.`id` commodityId,
            c.`commodity_code`,
            c.`bar_code`,
            c.`commodity_name`,
            c.`commodity_spec`,
            IFNULL(c.commodity_package_spec,1) commodityPackageSpec,
            c.`commodity_unit_id` commodityUnitId,

            c.`commodity_cycle_id` commodityCycleId,
            c.`xd_sales_box_capacity`,
            c.`sales_box_capacity`,

            c.commodity_third_id commodityThirdId,
            c.commodity_first_id commodityFirstId,
            c.commodity_second_id commoditySecondId,

            c.tax_rate taxRate,
            c.`commodity_type`,
            c.`commodity_package_kind` packedType,
            c.`commodity_package_kind` commodityPackageKind,
            c.`commodity_is_quick_freeze` frozen ,
            c.`storage_condition` storageCondition,
            c.`logistics_model`,
            c.is_weight,
            c.box_capacity
        FROM t_commodity c
        WHERE 1=1
        <if test="commodityId != null">
            AND c.id = #{commodityId}
        </if>
        <if test="commodityKey!=null and commodityKey !='' ">
            and (c.`commodity_name` like concat('%',#{commodityKey},'%') or c.`commodity_aid` like concat('%',#{commodityKey},'%') or c.`commodity_code` like concat('%',#{commodityKey},'%') or c.`bar_code` like concat('%',#{commodityKey},'%')  )
        </if>
        <if test="barCode != null and barCode != '' ">
            and c.`id` = (SELECT  commodity_id FROM t_commodity_bar_code WHERE  bar_code = #{barCode})
        </if>
        <if test="cateId1 != null">
            AND c.commodity_first_id = #{cateId1}
        </if>
        <if test="cateId2 != null">
            AND c.commodity_second_id = #{cateId2}
        </if>
        <if test="cateId3 != null">
            AND c.commodity_third_id = #{cateId3}
        </if>
        <if test="commodityIdList != null and commodityIdList.size() > 0">
            AND c.id in <foreach collection="commodityIdList" item="id" open="(" separator="," close=")"> #{id} </foreach>
        </if>

    </select>


    <select id="getCommodityFreezeGroup" resultType="java.lang.Long">
        SELECT t.commodity_id FROM t_commodity_freeze_group t
    </select>


    <select id="findShopCommodityPurchaseList" resultType="java.lang.Long" parameterType="com.pinshang.qingyun.order.manage.vo.CommodityListRequestVO">
        SELECT
            t.commodity_id
        FROM
        t_xs_shop_commodity_purchase_status t
        LEFT JOIN t_commodity c on c.id = t.commodity_id
        WHERE t.shop_id = #{shopId} and t.commodity_purchase_status = 1
        and c.`commodity_state` = 1
        and c.status = 1 and c.product_type != 2
        <if test="isWeight == true and isNormal != true">
            and c.is_weight = 1
        </if>
        <if test="isNormal == true and isWeight != true">
            and c.is_weight = 0
        </if>
        <if test="isNew == true">
            and c.`commodity_cycle_id`= 348815514434650944
        </if>
        <if test="commodityId != null">
            and c.`id` = #{commodityId}
        </if>
        <if test="barCode != null and barCode !='' ">
            and c.`id` = (SELECT  commodity_id FROM t_commodity_bar_code WHERE  bar_code = #{barCode})
        </if>
        <if test="categoryId != null and categoryId != '' ">
            and ((c.`commodity_first_id`= #{categoryId}) or (c.`commodity_second_id`= #{categoryId}) or (c.`commodity_third_id`= #{categoryId}))
        </if>
        <if test="commodityKey != null and commodityKey != '' ">
            and (c.`commodity_code` like concat('%',#{commodityKey},'%') or c.`commodity_name` like concat('%',#{commodityKey},'%') or c.`commodity_aid` like concat('%',#{commodityKey},'%') or c.`bar_code` like concat('%',#{commodityKey},'%')  )
        </if>
        <if test="commodityCodeList != null and commodityCodeList.size() > 0 ">
            and c.`commodity_code` IN <foreach collection="commodityCodeList" item="commodityCode" open="(" separator="," close=")"> #{commodityCode} </foreach>
        </if>
        <if test="commodityIdList != null and commodityIdList.size() > 0 ">
            and c.`id` IN <foreach collection="commodityIdList" item="commodityId" open="(" separator="," close=")"> #{commodityId} </foreach>
        </if>
    </select>

    <select id="getEndTimeList" resultType="java.lang.String">
        SELECT
            tt.endTime
        FROM
            (
                SELECT DISTINCT
                    t.default_supplier_end_time endTime
                FROM
                    t_md_shop_order_setting t
                WHERE
                    t.default_supplier_end_time IS NOT NULL
                UNION
                SELECT DISTINCT
                    t.default_warehouse_end_time endTime
                FROM
                    t_md_shop_order_setting t
                WHERE
                    t.default_warehouse_end_time IS NOT NULL
            ) tt
        WHERE 1=1
			<![CDATA[ and tt.endTime > #{nowHour} ]]>
		  ORDER BY endTime
    </select>



    <select id="querySubCommodityByIdList" resultType="com.pinshang.qingyun.order.manage.dto.CommodityODTO">
        SELECT
            t.commodity_id commodityId,
            t.commodity_item_id commodityItemId
        FROM t_commodity_item t
        where  t.`commodity_id` IN
        <foreach collection="commodityIdList" index="index" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>


    <select id="queryCommodityByCodes" resultType="com.pinshang.qingyun.order.manage.dto.CommodityODTO">
        SELECT
            t.id commodityId,
            t.commodity_code,
            t.commodity_name,
            t.`commodity_spec`,
            t.is_weight,
            t.quality_days,
            t.quality_unit,
            t.product_type,
            di.`option_name` commodityUnit
        FROM t_commodity t
        left  join t_dictionary di on di.`id` = t.`commodity_unit_id`
        WHERE  t.commodity_code IN
        <foreach collection="commodityCodes" item="commodityCode" open="(" separator="," close=")">
            #{commodityCode}
        </foreach>
    </select>
</mapper>
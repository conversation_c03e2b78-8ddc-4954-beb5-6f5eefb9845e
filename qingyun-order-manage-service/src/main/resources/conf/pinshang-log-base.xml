<?xml version="1.0" encoding="UTF-8"?>
<included>
	<include resource="org/springframework/boot/logging/logback/defaults.xml" />
	<!--<property name="LOG_FILE" value="${LOG_FILE:-${LOG_PATH:-${LOG_TEMP:-${java.io.tmpdir:-/tmp}}/}spring.log}"/>-->
	<include resource="org/springframework/boot/logging/logback/console-appender.xml" />
	<!--<include resource="org/springframework/boot/logging/logback/file-appender.xml" />-->
	<property name="FILE_LOG_PATTERN" value="%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{80} - %msg%n"/>
	<property name="FILE_LOG_PATTERN_JSON" value="{&quot;dateTime&quot;:&quot;%d{yyyy-MM-dd HH:mm:ss.SSS}&quot;,&quot;threadId&quot;:&quot;%thread&quot;,&quot;level&quot;:&quot;%-5level&quot;,&quot;class&quot;:&quot;%logger{80}&quot;,&quot;message&quot;:&quot; %msg&quot;}%n"/>
	<property name="NON_ERROR_MAX_HISTORY" value="7"/>
	<property name="NON_ERROR_MAX_FILE_SIZE" value="50MB"/>
	<property name="MAX_HISTORY" value="7"/>
	<property name="MAX_FILE_SIZE" value="50MB"/>
	<springProperty scope="context" name="springAppName" source="spring.application.name"/>

	<appender name="FILE_ERROR" class="ch.qos.logback.core.rolling.RollingFileAppender">
		<filter class="ch.qos.logback.classic.filter.LevelFilter">
			<level>ERROR</level>
			<onMatch>ACCEPT</onMatch>
			<onMismatch>DENY</onMismatch>
		</filter>
		<rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
			<!-- rollover daily -->
			<fileNamePattern>${LOG_PATH}/error/${LOG_FILE}-error-%d{yyyy-MM-dd}.%i.log</fileNamePattern>
			<!--  每个日志文件的最大值-->
			<maxFileSize>30MB</maxFileSize>
			<!--  日志最大保留天数-->
			<maxHistory>20</maxHistory>
		</rollingPolicy>
		<encoder class="ch.qos.logback.classic.encoder.PatternLayoutEncoder">
			<pattern>${FILE_LOG_PATTERN}</pattern>
		</encoder>
	</appender>
	<appender name="FILE_INFO" class="ch.qos.logback.core.rolling.RollingFileAppender">
		<filter class="ch.qos.logback.classic.filter.LevelFilter">
			<level>INFO</level>
			<onMatch>ACCEPT</onMatch>
			<onMismatch>DENY</onMismatch>
		</filter>
		<rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
			<!-- rollover daily -->
			<fileNamePattern>${LOG_PATH}/info/${LOG_FILE}-info-%d{yyyy-MM-dd}.%i.log</fileNamePattern>
			<!--  每个日志文件的最大值-->
			<maxFileSize>${NON_ERROR_MAX_FILE_SIZE}</maxFileSize>
			<!--  日志最大保留时间-->
			<maxHistory>${NON_ERROR_MAX_HISTORY}</maxHistory>
		</rollingPolicy>
		<encoder class="ch.qos.logback.classic.encoder.PatternLayoutEncoder">
			<pattern>${FILE_LOG_PATTERN}</pattern>
		</encoder>
	</appender>

	<appender name="FILE_DEBUG" class="ch.qos.logback.core.rolling.RollingFileAppender">
		<filter class="ch.qos.logback.classic.filter.LevelFilter">
			<level>DEBUG</level>
			<onMatch>ACCEPT</onMatch>
			<onMismatch>DENY</onMismatch>
		</filter>
		<rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
			<!-- rollover daily -->
			<fileNamePattern>${LOG_PATH}/debug/${LOG_FILE}-debug-%d{yyyy-MM-dd}.%i.log</fileNamePattern>
			<!--  每个日志文件的最大值-->
			<maxFileSize>${NON_ERROR_MAX_FILE_SIZE}</maxFileSize>
			<!--  日志最大保留时间-->
			<maxHistory>${NON_ERROR_MAX_HISTORY}</maxHistory>
		</rollingPolicy>
		<encoder class="ch.qos.logback.classic.encoder.PatternLayoutEncoder">
			<pattern>${FILE_LOG_PATTERN}</pattern>
		</encoder>
	</appender>

	<appender name="FILE_WARN" class="ch.qos.logback.core.rolling.RollingFileAppender">
		<filter class="ch.qos.logback.classic.filter.LevelFilter">
			<level>WARN</level>
			<onMatch>ACCEPT</onMatch>
			<onMismatch>DENY</onMismatch>
		</filter>
		<rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
			<!-- rollover daily -->
			<fileNamePattern>${LOG_PATH}/warn/${LOG_FILE}-warn-%d{yyyy-MM-dd}.%i.log</fileNamePattern>
			<!--  每个日志文件的最大值-->
			<maxFileSize>${NON_ERROR_MAX_FILE_SIZE}</maxFileSize>
			<!--  日志最大保留时间-->
			<maxHistory>${NON_ERROR_MAX_HISTORY}</maxHistory>
		</rollingPolicy>
		<encoder class="ch.qos.logback.classic.encoder.PatternLayoutEncoder">
			<pattern>${FILE_LOG_PATTERN}</pattern>
		</encoder>
	</appender>
	<!-- https://blog.csdn.net/lkforce/article/details/76637071 -->
	<appender name="FILE_APPENDER" class="ch.qos.logback.core.rolling.RollingFileAppender">
		<rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
			<!-- rollover daily -->
			<fileNamePattern>${LOG_PATH}/${LOG_FILE}-%d{yyyy-MM-dd}.%i.log</fileNamePattern>
			<!--  每个日志文件的最大值-->
			<maxFileSize>${MAX_FILE_SIZE}</maxFileSize>
			<!--  日志最大保留时间-->
			<maxHistory>${MAX_HISTORY}</maxHistory>
		</rollingPolicy>
		<encoder class="ch.qos.logback.classic.encoder.PatternLayoutEncoder">
			<pattern>${FILE_LOG_PATTERN_JSON}</pattern>
			<immediateFlush>false</immediateFlush>
			<charset>utf8</charset>
		</encoder>
	</appender>
	<appender name ="ASYNC_APPENDER" class= "ch.qos.logback.classic.AsyncAppender">
		<!-- 不丢失日志.默认的,如果队列的80%已满,则会丢弃TRACT、DEBUG、INFO级别的日志 -->
		<discardingThreshold >0</discardingThreshold>
		<queueSize>1024</queueSize>									<!-- 更改默认的队列的深度,该值会影响性能.默认值为256 -->
		<neverBlock>true</neverBlock>								<!-- 队列满了也不卡线程	-->
		<appender-ref ref ="FILE_APPENDER"/>						<!-- 添加附加的appender,最多只能添加一个 -->
	</appender>
</included>

package com.pinshang.qingyun.order.manage.hystrix;

import com.pinshang.qingyun.order.manage.service.AutoOrderAgainClient;
import com.pinshang.qingyun.order.manage.service.ManageJobClient;
import feign.hystrix.FallbackFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * @Author: sk
 * @Date: 2022/5/11
 */
@Component
@Slf4j
public class ManageJobClientHystrix implements FallbackFactory<ManageJobClient> {
    @Override
    public ManageJobClient create(Throwable throwable) {
        return new ManageJobClient() {

            @Override
            public Boolean createShopAutoOrderJob() {
                return null;
            }

            @Override
            public Boolean executeShopAutoOrderJob(String orderTime) {
                return null;
            }

            @Override
            public boolean lastMonthSale() {
                return false;
            }

            @Override
            public boolean deleteDirectSendingCommodity() {
                return false;
            }

            @Override
            public Boolean grouponOrderDay(String timeStamp) {
                return null;
            }
        };
    }
}

package com.pinshang.qingyun.order.manage.hystrix;

import com.pinshang.qingyun.order.manage.dto.CloudOrderAllocationIDTO;
import com.pinshang.qingyun.order.manage.service.CloudOrderAllocationClient;
import feign.hystrix.FallbackFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @Date 2018/10/10 10:54
 */
@Component
@Slf4j
public class CloudOrderAllocationClientHystrix implements FallbackFactory<CloudOrderAllocationClient> {
    @Override
    public CloudOrderAllocationClient create(Throwable throwable) {
        return new CloudOrderAllocationClient() {

            @Override
            public Boolean allocationStores(CloudOrderAllocationIDTO idto) {
                return null;
            }
        };
    }
}

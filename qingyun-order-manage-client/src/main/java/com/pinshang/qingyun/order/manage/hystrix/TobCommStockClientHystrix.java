package com.pinshang.qingyun.order.manage.hystrix;

import com.pinshang.qingyun.order.manage.dto.CloudOrderAllocationIDTO;
import com.pinshang.qingyun.order.manage.service.CloudOrderAllocationClient;
import com.pinshang.qingyun.order.manage.service.TobCommStockClient;
import feign.hystrix.FallbackFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2024/12/02 10:52
 */
@Component
@Slf4j
public class TobCommStockClientHystrix implements FallbackFactory<TobCommStockClient> {
    @Override
    public TobCommStockClient create(Throwable throwable) {
        return new TobCommStockClient() {

            @Override
            public List<Long> queryExistToBCommodityStockIds(List<Long> commodityIdList) {
                return Collections.emptyList();
            }
        };
    }
}

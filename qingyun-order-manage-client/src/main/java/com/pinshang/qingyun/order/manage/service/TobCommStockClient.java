package com.pinshang.qingyun.order.manage.service;

import com.pinshang.qingyun.base.configure.FeignClientConfiguration;
import com.pinshang.qingyun.base.constant.ApplicationNameConstant;
import com.pinshang.qingyun.order.manage.dto.CloudOrderAllocationIDTO;
import com.pinshang.qingyun.order.manage.hystrix.TobCommStockClientHystrix;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2024/12/02 10:52
 */
@FeignClient(value = ApplicationNameConstant.QINGYUN_ORDER_MANAGE_SERVICE, fallbackFactory = TobCommStockClientHystrix.class, configuration = FeignClientConfiguration.class)
public interface TobCommStockClient {

    /**
     * 鲜达APP、批发APP上架、一键上架判断库存依据是否存在（返回存在库存依据的商品idList）
     * @param commodityIdList
     * @return
     */
    @RequestMapping(value = "/tobCommoditytock/queryExistToBCommodityStockIds",method = RequestMethod.POST)
    List<Long> queryExistToBCommodityStockIds(@RequestBody List<Long> commodityIdList);

}

package com.pinshang.qingyun.order.manage.hystrix;

import com.pinshang.qingyun.order.manage.dto.CloudOrderAllocationIDTO;
import com.pinshang.qingyun.order.manage.service.AutoOrderAgainClient;
import com.pinshang.qingyun.order.manage.service.CloudOrderAllocationClient;
import feign.hystrix.FallbackFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * @Author: sk
 * @Date: 2022/5/11
 */
@Component
@Slf4j
public class AutoOrderAgainClientHystrix implements FallbackFactory<AutoOrderAgainClient> {
    @Override
    public AutoOrderAgainClient create(Throwable throwable) {
        return new AutoOrderAgainClient() {

            @Override
            public Boolean autoOrderAgain(String autoStr) {
                return null;
            }
        };
    }
}

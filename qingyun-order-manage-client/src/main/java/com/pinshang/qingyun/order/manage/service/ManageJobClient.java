package com.pinshang.qingyun.order.manage.service;

import com.pinshang.qingyun.base.configure.FeignClientConfiguration;
import com.pinshang.qingyun.base.constant.ApplicationNameConstant;
import com.pinshang.qingyun.order.manage.hystrix.ManageJobClientHystrix;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * @Author: sk
 * @Date: 2022/5/11
 */
@FeignClient(value = ApplicationNameConstant.QINGYUN_ORDER_MANAGE_SERVICE, fallbackFactory = ManageJobClientHystrix.class, configuration = FeignClientConfiguration.class)
public interface ManageJobClient {

    /**
     * 创建门店自动订货job
     * @return
     */
    @RequestMapping(value = "/orderManageJob/createShopAutoOrderJob",method = RequestMethod.POST)
    Boolean createShopAutoOrderJob();

    /**
     * 执行门店自动订货job
     * @return
     */
    @RequestMapping(value = "/orderManageJob/executeShopAutoOrderJob",method = RequestMethod.POST)
    Boolean executeShopAutoOrderJob(@RequestParam("orderTime") String orderTime);


    /**
     * 上个月有销售品项
     * @return
     */
    @GetMapping("/autoShopCommodity/lastMonthSale")
    boolean lastMonthSale();

    /**
     * 删除直送商品
     * @return
     */
    @GetMapping("/autoShopCommodity/deleteDirectSendingCommodity")
    boolean deleteDirectSendingCommodity();


    /**
     *  B 端团购订单日汇总
     * @param timeStamp
     * @return
     */
    @RequestMapping(value = "/grouponOrder/grouponOrderDay", method = RequestMethod.POST)
    Boolean grouponOrderDay(@RequestParam("timeStamp") String timeStamp);
}

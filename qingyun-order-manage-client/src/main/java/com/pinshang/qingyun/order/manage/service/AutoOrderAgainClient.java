package com.pinshang.qingyun.order.manage.service;

import com.pinshang.qingyun.base.configure.FeignClientConfiguration;
import com.pinshang.qingyun.base.constant.ApplicationNameConstant;
import com.pinshang.qingyun.order.manage.dto.CloudOrderAllocationIDTO;
import com.pinshang.qingyun.order.manage.hystrix.AutoOrderAgainClientHystrix;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * @Author: sk
 * @Date: 2022/5/11
 */
@FeignClient(value = ApplicationNameConstant.QINGYUN_ORDER_MANAGE_SERVICE, fallbackFactory = AutoOrderAgainClientHystrix.class, configuration = FeignClientConfiguration.class)
public interface AutoOrderAgainClient {

    /**
     * 自动订货的如果报错就当前截单时间重新异步跑一次(order调用冻结失败再次调用一次)
     * @return
     */
    @RequestMapping(value = "/orderManageJob/autoOrderAgain",method = RequestMethod.POST)
    Boolean autoOrderAgain(@RequestParam("autoStr") String autoStr);

}

package com.pinshang.qingyun.order.manage.service;

import com.pinshang.qingyun.base.configure.FeignClientConfiguration;
import com.pinshang.qingyun.base.constant.ApplicationNameConstant;
import com.pinshang.qingyun.order.manage.dto.CloudOrderAllocationIDTO;
import com.pinshang.qingyun.order.manage.hystrix.CloudOrderAllocationClientHystrix;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

/**
 * <AUTHOR>
 * @Date 2018/10/10 10:52
 */
@FeignClient(value = ApplicationNameConstant.QINGYUN_ORDER_MANAGE_SERVICE, fallbackFactory = CloudOrderAllocationClientHystrix.class, configuration = FeignClientConfiguration.class)
public interface CloudOrderAllocationClient {
    /**
     * 云超配货单分配客户---》下单
     * @return
     */
    @RequestMapping(value = "/cloudOrderAllocation/allocationStores",method = RequestMethod.POST)
    Boolean allocationStores(@RequestBody CloudOrderAllocationIDTO idto);

}

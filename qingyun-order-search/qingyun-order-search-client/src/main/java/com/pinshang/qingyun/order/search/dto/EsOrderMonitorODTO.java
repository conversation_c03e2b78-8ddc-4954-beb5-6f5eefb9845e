package com.pinshang.qingyun.order.search.dto;

import lombok.Data;

import java.math.BigDecimal;

/**
 * 订单统计查询--订单比较返回结果
 */
@Data
public class EsOrderMonitorODTO {
    //订单总数
    private Integer totalOrderCount;

    //正常订单数（orderStatus=0）
    private Integer normalOrderCount;

    //总金额数(取得最终金额)
    private BigDecimal totalOrderAmount;

    public Integer getTotalOrderCount() {
        return totalOrderCount==null?0:totalOrderCount;
    }

    public Integer getNormalOrderCount() {
        return normalOrderCount==null?0:normalOrderCount;
    }

    public BigDecimal getTotalOrderAmount() {
        return totalOrderAmount==null?BigDecimal.ZERO:totalOrderAmount;
    }
}

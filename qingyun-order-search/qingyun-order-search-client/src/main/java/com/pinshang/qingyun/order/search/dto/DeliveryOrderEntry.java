package com.pinshang.qingyun.order.search.dto;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @Date 2019/3/5 16:45
 */
@Data
public class DeliveryOrderEntry {
    /**
     * 订单id
     */
    private String id;
    /**
     * 送货日期
     */
    private Date deliveryDate;
    /**
     * 客户编号
     */
    private String storeCode;
    /**
     * 客户名称
     */
    private String storeName;
    /**
     * 订单金额
     */
    private BigDecimal finalAmount;
    /**
     * 送货员
     */
    private String deliveryManName;
    /**
     * 客户类型
     */
    private String storeTypeName;
    /**
     * 督导
     */
    private String supervisorName;
    /**
     * 结账客户
     */
    private String settlementCustomer;
    /**
     * 操作员
     */
    private String operatorName;
    /**
     * 订单编码
     */
    private String orderCode;
    /**
     * 班组长
     */
    private String teamLeaderName;
}

package com.pinshang.qingyun.order.search.service;

import com.pinshang.qingyun.base.configure.FeignClientConfiguration;
import com.pinshang.qingyun.base.constant.ApplicationNameConstant;
import com.pinshang.qingyun.order.search.dto.EsProductSaleStatisticsODTO;
import com.pinshang.qingyun.order.search.hystrix.EsProductSaleStatisticsClientHystrix;
import com.pinshang.qingyun.orderreport.dto.ProductSaleStatisticsIDTO;
import com.pinshang.qingyun.orderreport.dto.ProductSaleStatisticsODTO;
import com.pinshang.qingyun.orderreport.dto.ProductSaleStatisticsSumODTO;
import com.pinshang.qingyun.orderreport.dto.ProductSaleStatisticsTempODTO;
import com.pinshang.qingyun.orderreport.hystrix.ProductSaleStatisticsClientHystrix;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import java.util.List;

/**
 * 产品销售汇总ES
 */
@FeignClient(value = ApplicationNameConstant.QINGYUN_ORDER_SEARCH_SERVICE, fallbackFactory = EsProductSaleStatisticsClientHystrix.class, configuration = FeignClientConfiguration.class)
public interface EsProductSaleStatisticsClient {

    /**
     * 产品销售汇总
     * @param idto
     * @return
     */
    @RequestMapping(value = "/esStatistical/productSaleStatistics/list", method = RequestMethod.POST)
    List<ProductSaleStatisticsTempODTO> queryList(@RequestBody ProductSaleStatisticsIDTO idto);

    /**
     * 产品销售汇总
     * @param idto
     * @return
     */
    @RequestMapping(value = "/esStatistical/productSaleStatistics/querySum", method = RequestMethod.POST)
    ProductSaleStatisticsSumODTO querySum(@RequestBody ProductSaleStatisticsIDTO idto);

}
package com.pinshang.qingyun.order.search.service;

import com.pinshang.qingyun.base.configure.FeignClientConfiguration;
import com.pinshang.qingyun.base.constant.ApplicationNameConstant;
import com.pinshang.qingyun.order.search.dto.EsOrderMonitorIDTO;
import com.pinshang.qingyun.order.search.dto.EsOrderMonitorODTO;
import com.pinshang.qingyun.order.search.hystrix.EsOrderClientHystrix;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import java.util.List;


/**
 * 统计查询--订单监控
 *
 */
@FeignClient(value = ApplicationNameConstant.QINGYUN_ORDER_SEARCH_SERVICE, fallbackFactory = EsOrderClientHystrix.class, configuration = FeignClientConfiguration.class)
public interface EsOrderClient {

    /**
     * 订单比较：查询订单统计信息
     * @param vo
     * @return
     */
    @RequestMapping(value = "/esOrder/queryEsOrderStatisticsInfo", method = RequestMethod.POST)
    EsOrderMonitorODTO queryEsOrderStatisticsInfo(@RequestBody EsOrderMonitorIDTO vo);

    /**
     * ES差异订单
     * @param idto
     * @return
     */
    @RequestMapping(value = "/esOrder/queryEsOrderDiffList", method = RequestMethod.POST)
    List<String> queryEsOrderDiffList(@RequestBody EsOrderMonitorIDTO idto);

    /**
     * ES订单批量同步
     * @param idto
     * @return
     */
    @RequestMapping(value = "/esOrder/syncEsOrder", method = RequestMethod.POST)
    Integer syncEsOrder(@RequestBody EsOrderMonitorIDTO idto);

}

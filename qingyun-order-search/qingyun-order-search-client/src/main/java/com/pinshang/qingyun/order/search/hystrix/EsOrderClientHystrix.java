package com.pinshang.qingyun.order.search.hystrix;


import com.pinshang.qingyun.order.search.dto.EsOrderMonitorIDTO;
import com.pinshang.qingyun.order.search.dto.EsOrderMonitorODTO;
import com.pinshang.qingyun.order.search.service.EsOrderClient;
import feign.hystrix.FallbackFactory;
import org.springframework.stereotype.Component;

import java.util.List;


@Component
public class EsOrderClientHystrix implements FallbackFactory<EsOrderClient> {
    @Override
    public EsOrderClient create(Throwable throwable) {
        return new EsOrderClient() {


            @Override
            public EsOrderMonitorODTO queryEsOrderStatisticsInfo(EsOrderMonitorIDTO vo) {
                return null;
            }

            @Override
            public List<String> queryEsOrderDiffList(EsOrderMonitorIDTO idto) {
                return null;
            }

            @Override
            public Integer syncEsOrder(EsOrderMonitorIDTO idto) {
                return null;
            }
        };
    }

}

package com.pinshang.qingyun.order.search.hystrix;

import com.pinshang.qingyun.order.search.service.EsFreshProductShipmentsClient;
import com.pinshang.qingyun.orderreport.dto.FreshProductShipmentsIDTO;
import com.pinshang.qingyun.orderreport.dto.FreshProductShipmentsTempODTO;
import feign.hystrix.FallbackFactory;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 产品发货总量(鲜食)
 */
@Component
public class EsFreshProductShipmentsClientHystrix implements FallbackFactory<EsFreshProductShipmentsClient> {
    @Override
    public EsFreshProductShipmentsClient create(Throwable cause) {
        return new EsFreshProductShipmentsClient() {


            @Override
            public List<FreshProductShipmentsTempODTO> queryList(FreshProductShipmentsIDTO idto) {
                return null;
            }
        };
    }
}

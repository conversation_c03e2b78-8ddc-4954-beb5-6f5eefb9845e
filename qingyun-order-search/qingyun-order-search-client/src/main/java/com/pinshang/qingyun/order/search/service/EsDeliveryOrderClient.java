package com.pinshang.qingyun.order.search.service;

import com.github.pagehelper.PageInfo;
import com.pinshang.qingyun.base.configure.FeignClientConfiguration;
import com.pinshang.qingyun.base.constant.ApplicationNameConstant;
import com.pinshang.qingyun.order.search.dto.DeliveryOrderDetailRespDTO;
import com.pinshang.qingyun.order.search.dto.DeliveryOrderEntry;
import com.pinshang.qingyun.order.search.dto.DeliveryOrderReqDTO;
import com.pinshang.qingyun.order.search.hystrix.EsDeliveryOrderClientHystrix;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;

@FeignClient(value = ApplicationNameConstant.QINGYUN_ORDER_SEARCH_SERVICE, fallbackFactory = EsDeliveryOrderClientHystrix.class, configuration = FeignClientConfiguration.class)
public interface EsDeliveryOrderClient {

    @RequestMapping(value = "/statistical/deliveryOrder/queryList", method = RequestMethod.POST)
    PageInfo<DeliveryOrderEntry> queryList(DeliveryOrderReqDTO reqDTO);

    @RequestMapping(value = "/statistical/deliveryOrder/orderDetail", method = RequestMethod.GET)
    DeliveryOrderDetailRespDTO queryDeliveryOrderDetail(@RequestParam("id") Long orderId);
}

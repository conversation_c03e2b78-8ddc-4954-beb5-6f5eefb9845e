package com.pinshang.qingyun.order.search.dto;

import lombok.Data;

import java.math.BigDecimal;

/**
 * 订单商品详情
 */
@Data
public class DeliveryOrderGiftDTO {
    private Long id;
    /** 订单id **/
    private Long orderId;
    /** 商品id **/
    private Long commodityId;
    /** 商品数量 **/
    private BigDecimal commodityNum;
    /** 单价 **/
    private BigDecimal commodityPrice;
    /**
     * 促销前的源始金额
     */
    private BigDecimal totalPrice;
    /** 类型 **/
    private Integer type;
    /** 备注 **/
    private String remark;
    /**
     * 商品名称
     */
    private String commodityName;
    /**
     * 商品编码
     */
    private String commodityCode;
    /**
     * 商品规格
     */
    private String commoditySpec;

}

package com.pinshang.qingyun.order.search.hystrix;

import com.pinshang.qingyun.order.search.dto.DifferenceInfoDTO;
import com.pinshang.qingyun.order.search.dto.SyncInfoByCodesIDTO;
import com.pinshang.qingyun.order.search.service.EsStoreClient;
import feign.hystrix.FallbackFactory;
import org.springframework.stereotype.Component;

import java.util.List;


@Component
public class EsStoreClientHystrix implements FallbackFactory<EsStoreClient> {
    @Override
    public EsStoreClient create(Throwable throwable) {
        return new EsStoreClient() {

            @Override
            public Integer syncStoreInfo(SyncInfoByCodesIDTO idto) {
                return null;
            }

            @Override
            public DifferenceInfoDTO selectStoreDifferenceInfo() {
                return null;
            }

            @Override
            public List<String> selectMissingStoreCodeList() {
                return null;
            }
        };
    }

}

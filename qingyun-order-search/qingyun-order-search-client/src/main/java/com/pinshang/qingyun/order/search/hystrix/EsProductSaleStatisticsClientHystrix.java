package com.pinshang.qingyun.order.search.hystrix;

import com.pinshang.qingyun.order.search.dto.EsProductSaleStatisticsODTO;
import com.pinshang.qingyun.order.search.service.EsProductSaleStatisticsClient;
import com.pinshang.qingyun.orderreport.dto.ProductSaleStatisticsIDTO;
import com.pinshang.qingyun.orderreport.dto.ProductSaleStatisticsODTO;
import com.pinshang.qingyun.orderreport.dto.ProductSaleStatisticsSumODTO;
import com.pinshang.qingyun.orderreport.dto.ProductSaleStatisticsTempODTO;
import feign.hystrix.FallbackFactory;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 产品销售汇总
 */
@Component
public class EsProductSaleStatisticsClientHystrix implements FallbackFactory<EsProductSaleStatisticsClient> {
    @Override
    public EsProductSaleStatisticsClient create(Throwable cause) {
        return new EsProductSaleStatisticsClient() {


            @Override
            public List<ProductSaleStatisticsTempODTO> queryList(ProductSaleStatisticsIDTO idto) {
                return null;
            }

            @Override
            public ProductSaleStatisticsSumODTO querySum(ProductSaleStatisticsIDTO idto) {
                return null;
            }

        };
    }
}

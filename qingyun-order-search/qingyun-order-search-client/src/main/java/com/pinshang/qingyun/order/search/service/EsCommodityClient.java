package com.pinshang.qingyun.order.search.service;

import com.pinshang.qingyun.base.configure.FeignClientConfiguration;
import com.pinshang.qingyun.base.constant.ApplicationNameConstant;
import com.pinshang.qingyun.order.search.dto.DifferenceInfoDTO;
import com.pinshang.qingyun.order.search.dto.SyncInfoByCodesIDTO;
import com.pinshang.qingyun.order.search.hystrix.EsCommodityClientHystrix;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import java.util.List;

@FeignClient(value = ApplicationNameConstant.QINGYUN_ORDER_SEARCH_SERVICE, fallbackFactory = EsCommodityClientHystrix.class, configuration = FeignClientConfiguration.class)
public interface EsCommodityClient {


    /**
     * 同步主库商品相关信息到从库
     *
     * @param idto
     * @return
     */
    @RequestMapping(value = "/sync/commodityInfoTo/syncCommodityInfo", method = RequestMethod.POST)
    Integer syncCommodityInfo(@RequestBody SyncInfoByCodesIDTO idto);

    /**
     * 查询主/es商品差异信息
     *
     * @return
     */
    @RequestMapping(value = "/es/commodity/selectCommodityDifferenceInfo",method = RequestMethod.POST)
    DifferenceInfoDTO selectCommodityDifferenceInfo();

    /**
     * 查询es缺失的商品编码集合
     *
     * @return
     */
    @RequestMapping(value = "/es/commodity/selectMissingCommodityCodeList",method = RequestMethod.POST)
    List<String> selectMissingCommodityCodeList();
}

package com.pinshang.qingyun.order.search.service;

import com.pinshang.qingyun.base.configure.FeignClientConfiguration;
import com.pinshang.qingyun.base.constant.ApplicationNameConstant;
import com.pinshang.qingyun.order.search.dto.EsFreshProductShipmentsODTO;
import com.pinshang.qingyun.order.search.dto.EsProductShipmentsODTO;
import com.pinshang.qingyun.order.search.hystrix.EsFreshProductShipmentsClientHystrix;
import com.pinshang.qingyun.order.search.hystrix.EsProductShipmentsClientHystrix;
import com.pinshang.qingyun.orderreport.dto.FreshProductShipmentsIDTO;
import com.pinshang.qingyun.orderreport.dto.ProductShipmentsRequestIDTO;
import com.pinshang.qingyun.orderreport.dto.ProductShipmentsTempODTO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import java.util.List;

/**
 * 产品发货总量
 */
@FeignClient(value = ApplicationNameConstant.QINGYUN_ORDER_SEARCH_SERVICE, fallbackFactory = EsProductShipmentsClientHystrix.class, configuration = FeignClientConfiguration.class)
public interface EsProductShipmentsClient {

    /**
     * 产品发货总量
     * @param idto
     * @return
     */
    @RequestMapping(value = "/esStatistical/productShipments/list", method = RequestMethod.POST)
    List<ProductShipmentsTempODTO> queryList(@RequestBody ProductShipmentsRequestIDTO idto);


}
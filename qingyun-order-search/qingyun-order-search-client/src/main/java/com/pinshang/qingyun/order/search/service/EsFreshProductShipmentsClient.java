package com.pinshang.qingyun.order.search.service;

import com.pinshang.qingyun.base.configure.FeignClientConfiguration;
import com.pinshang.qingyun.base.constant.ApplicationNameConstant;
import com.pinshang.qingyun.order.search.hystrix.EsFreshProductShipmentsClientHystrix;
import com.pinshang.qingyun.orderreport.dto.FreshProductShipmentsIDTO;
import com.pinshang.qingyun.orderreport.dto.FreshProductShipmentsTempODTO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import java.util.List;

/**
 * 产品发货总量(鲜食)
 */
@FeignClient(value = ApplicationNameConstant.QINGYUN_ORDER_SEARCH_SERVICE, fallbackFactory = EsFreshProductShipmentsClientHystrix.class, configuration = FeignClientConfiguration.class)
public interface EsFreshProductShipmentsClient {

    /**
     * 产品发货总量(鲜食)
     * @param idto
     * @return
     */
    @RequestMapping(value = "/esStatistical/freshProductShipments/list", method = RequestMethod.POST)
    List<FreshProductShipmentsTempODTO> queryList(@RequestBody FreshProductShipmentsIDTO idto);


}
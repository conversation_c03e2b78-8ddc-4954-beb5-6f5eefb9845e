package com.pinshang.qingyun.order.search.hystrix;

import com.github.pagehelper.PageInfo;
import com.pinshang.qingyun.order.search.dto.DeliveryOrderDetailRespDTO;
import com.pinshang.qingyun.order.search.dto.DeliveryOrderEntry;
import com.pinshang.qingyun.order.search.dto.DeliveryOrderReqDTO;
import com.pinshang.qingyun.order.search.service.EsDeliveryOrderClient;
import feign.hystrix.FallbackFactory;
import org.springframework.stereotype.Component;

/**
 * @Description: 送货单
 * @Author: fangting
 * @CreateDate: 2020/9/8 15:24
 */
@Component
public class EsDeliveryOrderClientHystrix implements FallbackFactory<EsDeliveryOrderClient> {
    @Override
    public EsDeliveryOrderClient create(Throwable cause) {
        return new EsDeliveryOrderClient() {
            @Override
            public PageInfo<DeliveryOrderEntry> queryList(DeliveryOrderReqDTO reqDTO) {
                return null;
            }

            @Override
            public DeliveryOrderDetailRespDTO queryDeliveryOrderDetail(Long orderId) {
                return null;
            }
        };
    }
}

package com.pinshang.qingyun.order.search.hystrix;

import com.github.pagehelper.PageInfo;
import com.pinshang.qingyun.order.search.service.EsOrderDetailClient;
import com.pinshang.qingyun.orderreport.dto.OrderDetailIDTO;
import com.pinshang.qingyun.orderreport.dto.OrderDetailODTO;
import feign.hystrix.FallbackFactory;
import org.springframework.stereotype.Component;

import java.util.List;


@Component
public class EsOrderDetailClientHystrix implements FallbackFactory<EsOrderDetailClient> {
    @Override
    public EsOrderDetailClient create(Throwable cause) {
        return new EsOrderDetailClient() {


            @Override
            public PageInfo<OrderDetailODTO> queryList(OrderDetailIDTO idto) {
                return null;
            }

            @Override
            public List<OrderDetailODTO> exportList(OrderDetailIDTO idto) {
                return null;
            }
        };
    }
}

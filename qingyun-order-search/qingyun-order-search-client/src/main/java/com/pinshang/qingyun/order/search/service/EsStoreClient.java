package com.pinshang.qingyun.order.search.service;

import com.pinshang.qingyun.base.configure.FeignClientConfiguration;
import com.pinshang.qingyun.base.constant.ApplicationNameConstant;
import com.pinshang.qingyun.order.search.dto.DifferenceInfoDTO;
import com.pinshang.qingyun.order.search.dto.SyncInfoByCodesIDTO;
import com.pinshang.qingyun.order.search.hystrix.EsStoreClientHystrix;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import java.util.List;


/**
 * 统计查询--订单监控
 *
 */
@FeignClient(value = ApplicationNameConstant.QINGYUN_ORDER_SEARCH_SERVICE, fallbackFactory = EsStoreClientHystrix.class, configuration = FeignClientConfiguration.class)
public interface EsStoreClient {
    /**
     * 同步主库客户相关信息到从库
     *
     * @param idto
     * @return
     */
    @RequestMapping(value = "/sync/storeInfoTo/syncStoreInfo", method = RequestMethod.POST)
    Integer syncStoreInfo(@RequestBody SyncInfoByCodesIDTO idto);

    /**
     * 查询主从客户差异信息
     *
     * @return
     */
    @RequestMapping(value = "/esStore/selectStoreDifferenceInfo", method = RequestMethod.POST)
    DifferenceInfoDTO selectStoreDifferenceInfo();

    /**
     * 查询从库缺失的客户编码集合
     *
     * @return
     */
    @RequestMapping(value = "/esStore/selectMissingStoreCodeList", method = RequestMethod.POST)
    List<String> selectMissingStoreCodeList();


}

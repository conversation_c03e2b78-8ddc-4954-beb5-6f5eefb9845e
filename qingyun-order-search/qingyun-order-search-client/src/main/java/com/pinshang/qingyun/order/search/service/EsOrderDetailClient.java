package com.pinshang.qingyun.order.search.service;

import com.github.pagehelper.PageInfo;
import com.pinshang.qingyun.base.configure.FeignClientConfiguration;
import com.pinshang.qingyun.base.constant.ApplicationNameConstant;
import com.pinshang.qingyun.order.search.hystrix.EsOrderDetailClientHystrix;
import com.pinshang.qingyun.orderreport.dto.OrderDetailIDTO;
import com.pinshang.qingyun.orderreport.dto.OrderDetailODTO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import java.util.List;


@FeignClient(value = ApplicationNameConstant.QINGYUN_ORDER_SEARCH_SERVICE, fallbackFactory = EsOrderDetailClientHystrix.class, configuration = FeignClientConfiguration.class)
public interface EsOrderDetailClient {


    @RequestMapping(value = "/esStatistical/orderDetail/list", method = RequestMethod.POST)
    PageInfo<OrderDetailODTO> queryList(@RequestBody OrderDetailIDTO idto);

    @RequestMapping(value = "/esStatistical/orderDetail/exportList", method = RequestMethod.POST)
    List<OrderDetailODTO> exportList(@RequestBody OrderDetailIDTO idto);

}
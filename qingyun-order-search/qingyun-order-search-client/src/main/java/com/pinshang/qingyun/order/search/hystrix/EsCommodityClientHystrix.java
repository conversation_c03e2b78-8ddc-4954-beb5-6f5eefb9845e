package com.pinshang.qingyun.order.search.hystrix;

import com.pinshang.qingyun.order.search.dto.DifferenceInfoDTO;
import com.pinshang.qingyun.order.search.dto.SyncInfoByCodesIDTO;
import com.pinshang.qingyun.order.search.service.EsCommodityClient;
import feign.hystrix.FallbackFactory;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @Description: es商品
 * @Author: fangting
 * @CreateDate: 2020/8/27 10:10
 */
@Component
public class EsCommodityClientHystrix implements FallbackFactory<EsCommodityClient> {

    @Override
    public EsCommodityClient create(Throwable cause) {
        return new EsCommodityClient() {
            @Override
            public Integer syncCommodityInfo(SyncInfoByCodesIDTO idto) {
                return null;
            }

            @Override
            public DifferenceInfoDTO selectCommodityDifferenceInfo() {
                return null;
            }

            @Override
            public List<String> selectMissingCommodityCodeList() {
                return null;
            }
        };
    }
}

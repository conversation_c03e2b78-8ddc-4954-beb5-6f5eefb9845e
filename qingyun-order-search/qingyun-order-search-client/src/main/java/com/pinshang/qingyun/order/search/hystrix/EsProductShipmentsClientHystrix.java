package com.pinshang.qingyun.order.search.hystrix;

import com.pinshang.qingyun.order.search.dto.EsProductShipmentsODTO;
import com.pinshang.qingyun.order.search.service.EsProductShipmentsClient;
import com.pinshang.qingyun.orderreport.dto.ProductShipmentsRequestIDTO;
import com.pinshang.qingyun.orderreport.dto.ProductShipmentsTempODTO;
import feign.hystrix.FallbackFactory;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 产品发货总量
 */
@Component
public class EsProductShipmentsClientHystrix implements FallbackFactory<EsProductShipmentsClient> {
    @Override
    public EsProductShipmentsClient create(Throwable cause) {
        return new EsProductShipmentsClient() {


            @Override
            public List<ProductShipmentsTempODTO> queryList(ProductShipmentsRequestIDTO idto) {
                return null;
            }
        };
    }
}

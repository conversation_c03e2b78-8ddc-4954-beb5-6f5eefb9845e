POST http://***************:9019/posAppWarn/execute
Content-Type: application/json

###
POST http://*************:9200/qingyun-data/_search
Content-Type: application/json

{   "query": {     "match_all": {}   } }
###

#POST http://*************:9200/qingyun-data/action-log/_search
POST http://************:9200/qingyun-data/action-log/_search
Content-Type: application/json

{"query":{"match":{ "uri":"/888888/999999"  }}}

###

GET http://localhost:9008/search/es/demo/save
###
#POST http://*************:9200/qingyun-data/_search
POST http://*************:9200/qingyun-data/action-log/_search
Content-Type: application/json

{"query":{"match":{ "uri":"/v1/storage/carton/queryCartonList"  }},"from":1,"size":1 }

###
POST http://*************:9200/qingyun-data/_search
Content-Type: application/json

{"query":{"match":{ "id":1176013274900516866  }},"from":1,"size":1 }

###　关键字搜索
POST http://localhost:9061/esSearch/keyWordSearchList
Content-Type: application/json

{"keyWord": "清美迷你奶黄包","shopId": 3351,"pageNo": 1,"pageSize": 200}

###
POST http://localhost:9061/esSearch/keyWordSearchList
Content-Type: application/json

{"kw": "清美内酯豆腐前台","lid": 2414,"p": 1,"ps": 200}

###　一级分类搜索
POST http://localhost:9061/esSearch/xdFirstCategoryList
Content-Type: application/json

{"shopId": 3351,"sourceType": 1,"userName": "13472872667"}

### 移动端分类搜索
GET http://localhost:9061/recipe/cates/d?lid=2414&pid=11
Content-Type: application/json
{"lid": 2414,"pid":11}

### 移动端分类搜索
GET http://localhost:9061/recipe/cates/d?lid=3321&pid=18
Content-Type: application/json

### 移动端关键字搜索
GET http://localhost:9061/recipe/s?lid=3321&kw=葱油
Content-Type: application/json

###
POST http://***************:9200/xd-shop-commodity/_delete_by_query
Content-Type: application/json

{
"query": {
"match": {
"shopId": "3401"
}
}
}
###
POST http://***************:9200/xd-shop-commodity/_delete_by_query
Content-Type: application/json

{
  "query": {
    "match_all": {
    }
  }
}
###
POST http://***************:9200/xd-price-promotion/_delete_by_query
Content-Type: application/json

{
  "query": {
    "match_all": {
    }
  }
}
###
POST http://**************:9200/tj-order-item/_delete_by_query
Content-Type: application/json

{
"query": {
"match_all": {
}
}
}

### 生产　－－　删除所有前置仓门店商品
POST http://***************:9200/xd-shop-commodity/_delete_by_query
Content-Type: application/json

{
  "query": {
    "match_all": {
    }
  }
}

### 灰度　－－　删除所有前置仓门店商品
POST http://***************:9200/xd-shop-commodity/_delete_by_query
Content-Type: application/json

{
  "query": {
    "match_all": {
    }
  }
}

### 灰度
POST http://***************:9200/xd-shop-commodity/_delete_by_query
Content-Type: application/json

{"query" : {"bool" : {"must" : [{"terms" : {"shopId" : [3401,3411,3541,3561,3711,3721,3851,4071,4161,4181,4311,4461,4481,4571]}}]}}}

###  灰度
POST http://***************:9200/xd-shop-commodity/_search
Content-Type: application/json

{"query" : {"bool" : {"must" : [{"terms" : {"shopId" : [3321,3331,3501]}}]}}}

### 生产 按门店 查询
POST http://***************:9200/xd-shop-commodity/_search
Content-Type: application/json

{"query" : {"bool" : {"must" : [{"terms" : {"shopId" : [4081,4091,4151,4171,4191,4411,4441,4451,4471]}}]}}}

### 生产 按门店 删除
POST http://***************:9200/xd-shop-commodity/_delete_by_query
Content-Type: application/json

{"query" : {"bool" : {"must" : [{"terms" : {"shopId" : [4081,4091,4151,4171,4191,4411,4441,4451,4471]}}]}}}



### 生产 按门店 删除
POST http://192.168.102.14:9061/search/findCommoditiesByIds
Content-Type: application/json

{
  "shopId": 4471,
  "sourceTypeEnum": 1,
  "commodityList":[999965182752711624]
}



### 生产 按门店 删除
POST http://192.168.0.62:9061/search/findCommoditiesByIds
Content-Type: application/json

{
  "shopId": 4471,
  "sourceTypeEnum": 1,
  "commodityList":[999965182752711624]
}

###   index.max_result_window = max_result_window 一样效果,也就是加不加"index."都可以改参数值
PUT http://************:9200/xd-category/_settings
Content-Type: application/json

{ "index.requests.cache.enable": false ,"index.max_terms_count":229579 ,"index.max_result_window": 1500
  ,"refresh_interval": "1s"
}
### "refresh_interval": -1     ### 关闭自动刷新

###
PUT http://************:9200/tj-order*/_settings
Content-Type: application/json

{  "refresh_interval": "1s"
}
###
POST http://************:9200/tj-order/_cache/clear

### 只清理 query cache
POST http://************:9200/tj-order/_cache/clear?query=true

### 只清理 request cache
POST http://************:9200/tj-order/_cache/clear?request=true

### 只清理 request cache
POST http://************:9200/tj-order/_cache/clear?fielddata=true

### 刷新ALL索引
POST http://************:9200/_refresh

### 刷新指定索引
POST http://************:9200/tj-order/_refresh

###
PUT http://***************:9200/tj-order*/_settings
Content-Type: application/json

{"max_result_window":"2147483647"}

###
PUT http://**************:9200/tj-order*/_settings
Content-Type: application/json

{"max_result_window":"2147483647"}

###

POST http://**************:9006/shopQrCode/web/page
Content-Type: application/json

{"shopId":"5631"}

###
GET http://localhost:9053/gateApi/xdReport/xdReport/exportSalesStatisticsReport?shopId=&barCode=&commodityCode=&startTime=2021-12-23%2000:00:00&endTime=2021-12-23%2023:59:59&commodityAppName=&commodityName=&sourceType=&shopType=&secondCategoryId=&commodityFirstKindId=&commoditySecondKindId=&commodityThirdKindId=&timestamp=1640318042368&token=61ab2c10-067d-498a-a87e-9faa9c7b0621&signature=e9ab311515f63ecc5144d4ebd2cd02ef&orderType=

###
POST http://***************:9004/warehouse/selectUserWarehouseIdList
Content-Type: application/json

{"userId":7701}

###
POST ***************:9046/xdPromotionV2/queryCloudAppCommodityPromotion/5761
Content-Type: application/json

[
  {"id":999965182752843417,"categoryId": 63,"isWeight": 0}
]

###
POST http://localhost:9031/tjOrderMonitor/syncTJOrder
Content-Type: application/json

{"orderCodes": "1647312025025456485"}

###

POST http://localhost:9031/statistical/deliveryList/print
Content-Type: application/json

{"deliveryManId":"999771252982078965","lineCode":null,"lineId":53,"printStatus":1,"type":0
,"orderDate":"2022-03-16","printDeliveryBatchId":"","userId": 7701
}

###
POST http://localhost:9005/ycPs/createTask
Content-Type: application/json

{ "deliveryBatchId": 9131078242860601477,"orderDate": "2022-03-18","dispatchType": true}

###
POST http://localhost:9005/ycPs/awaitPsTaskList
Content-Type: application/json

{ "deliveryBatchId": 9131078242860601477,"orderDate": "2022-03-20","dispatchType": true}

###
POST http://**************:9009/ycPush/countUserSum
Content-Type: application/json

{"startDate": "2022-06-22","endDate": "2022-06-22"
}

###
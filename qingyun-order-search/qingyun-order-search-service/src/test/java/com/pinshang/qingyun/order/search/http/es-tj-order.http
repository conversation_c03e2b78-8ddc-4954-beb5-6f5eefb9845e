###
PUT http://192.168.0.68:9200/_settings
Content-Type: application/json

{"max_result_window":"2147483647"}

###
PUT http://192.168.0.68:9200/_settings
Content-Type: application/json

{"max_inner_result_window":"2147483647"}

###
POST http://192.168.0.68:9200/tj-commodity/_delete_by_query
Content-Type: application/json

{
  "query": {
    "match_all": {
    }
  }
}

###
POST http://192.168.0.68:9200/tj-store/_delete_by_query
Content-Type: application/json

{
  "query": {
    "match_all": {
    }
  }
}
###
POST http://192.168.0.68:9200/tj-order/_delete_by_query
Content-Type: application/json

{
  "query": {
    "match_all": {
    }
  }
}
###
POST http://192.168.0.68:9200/tj-order-item/_delete_by_query
Content-Type: application/json

{
  "query": {
    "match_all": {
    }
  }
}


###
POST http://192.168.0.68:9200/tj-commodity/_delete_by_query
Content-Type: application/json

{"query" : {"bool" : {"must" : [{"terms" : {"id" : [468788174651305600,999965182752695664]}}]}}}


###
POST http://192.168.0.68:9200/tj-store/_delete_by_query
Content-Type: application/json

{"query" : {"bool" : {"must" : [{"terms" : {"id" : [999872035591520201,999872035591549331,999872035591509085]}}]}}}

###
POST http://192.168.0.68:9200/tj-order/_search
Content-Type: application/json

{"query" : {"bool" : {"must" : [{"terms" : {"id" : [1565539,1631182]}}]
}},"from":0,"size":229579}

###
###
POST http://192.168.0.68:9200/tj-order/_search
Content-Type: application/json

{"query":{"bool":{"must":[{"term":{"orderTime":"2018-01-01"}}],"must_not":[],"should":[]}},
  "from":0,"size":10,"sort":[],"aggs":{}}

###
POST http://192.168.0.68:9200/tj-order/_search
Content-Type: application/json

{"query":{"bool":{"must":[{"range":{"orderTime":{"gte":"2018-01-01","lte":"2018-01-31"}}}],"must_not":[],"should":[]}}
,"from":0,"size":229579,"sort":[],"aggs":{}}

###
POST http://192.168.0.68:9200/tj-order/_search?request_cache=false
Content-Type: application/json

{"query":{"bool":{"must":[{"range":{"orderTime":{"gte":"2018-01-01","lte":"2018-01-31"}}}],
  "must_not":[],"should":[]}},"sort":[],"aggs":{}}

###
GET http://192.168.0.68:9200/_cache/clear?pretty
Content-Type: application/json

{
  "_shards" : {
    "total" : 72,
    "successful" : 72,
    "failed" : 0
  }
}

###
###
GET http://192.168.0.68:9200/_settings
Content-Type: application/json

{
  "refresh_interval":"10s"
}

###
POST http://192.168.0.68:9200/_refresh
Content-Type: application/json



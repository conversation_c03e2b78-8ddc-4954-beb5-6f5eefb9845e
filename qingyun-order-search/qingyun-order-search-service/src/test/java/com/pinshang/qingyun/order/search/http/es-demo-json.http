DELETE http://************:9200/qingyun-es-demo

### --------------------------   插入
###
GET http://localhost:9008/search/es/demo/json/insert
###
GET http://localhost:9008/search/es/demo/insertAll
###
GET http://localhost:9008/search/es/demo/json/insertInit

### --------------------------   基本查询
GET http://localhost:9008/search/es/demo/count
###
GET http://localhost:9008/search/es/demo/findAndSort
###
GET http://localhost:9008/search/es/demo/findAll
###
GET http://localhost:9008/search/es/demo/findById/1176765102595751937
###
GET http://localhost:9008/search/es/demo/existsById/1176731560532746243
###
POST http://localhost:9008/search/es/demo/findAllById
Content-Type: application/json

["1176753466422001666","1176765102595751937"]

###
GET http://localhost:9008/search/es/demo/findByPage

### --------------------------   高级查询

###
GET http://localhost:9008/search/es/demo/search/fuzzy
###
GET http://localhost:9008/search/es/demo/search/ids
###
GET http://localhost:9008/search/es/demo/search/term
###
GET http://localhost:9008/search/es/demo/search/range
###
GET http://localhost:9008/search/es/demo/search/phrase
###
GET http://localhost:9008/search/es/demo/search/multi
###
GET http://localhost:9008/search/es/demo/search/query
###
GET http://localhost:9008/search/es/demo/search/page
###
GET http://localhost:9008/search/es/demo/search/native
###
GET http://localhost:9008/search/es/demo/search/aggregation/metrics
###
GET http://localhost:9008/search/es/demo/search/aggregation/bucket





### --------------------------   删除
###
GET http://localhost:9008/search/es/demo/deleteById/1176705553654325250

###
GET http://localhost:9008/search/es/demo/deleteAll
###
POST http://localhost:9008/search/es/demo/deleteByEntity
Content-Type: application/json

{"id":"1176731560532746243"}

###
POST http://localhost:9008/search/es/demo/deleteByEntityList
Content-Type: application/json

[{"id":"1176731550269284353"},{"id":"1176731550269284354"}]


### --------------------------   更新
POST http://localhost:9008/search/es/demo/updateAll
Content-Type: application/json
###
POST http://localhost:9008/search/es/demo/update
Content-Type: application/json

//{"id":"1177130625766412290","name": "wei can","aliasName": "张一山","age": 26,"sex": true,"birthday": "1999-09-25T16:00:00","money": 40.25}
//{"id":"1177130625766412290","name": "张三","aliasName": "张一山","age": 26,"sex": true,"birthday": "1999-09-25T16:00:00","money": 40.25}
//{"id":"1177130629776166915","name": "weican","aliasName": "weican111","age": 26,"sex": true,"birthday": "1969-09-25T16:00:00","money": 660.25}
{"id":"1177130618636095491","name": "王五","aliasName": "wang wu ","age": 26,"sex": false,"birthday": "1987-07-18T13:00:00","money": 360}


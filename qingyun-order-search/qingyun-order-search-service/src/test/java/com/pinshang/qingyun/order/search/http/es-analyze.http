### https://www.cnblogs.com/xiaobaozi-95/p/9328948.html
###  Elasticsearch中，内置了很多分词器（analyzers），例如standard （标准分词器）、english （英文分词）和 chinese （中文分词）。
###  standard 一个一个词（汉字）切分，适用范围广，但是精准度低；english 对英文更加智能，可以识别单数负数，大小写，过滤stopwords（例如“the”这个词）等；chinese 效果很差
### 清美内酯豆腐,清美男豆腐
### 小鲈鱼（活）,鲈鱼（活）,小黑鱼，花鲢鱼（活），小桂鱼（活），白水鱼，昂刺鱼，斑点叉尾鮰鱼，鲜活鲫鱼
GET http://192.168.0.68:9200/_analyze
Content-Type: application/json

{
  "analyzer": "ik_smart",
  "text": "昂刺鱼，斑点叉尾鮰鱼，鲜活鲫鱼"
}
### 小鲈鱼（活）,鲈鱼（活）,小黑鱼，花鲢鱼（活），小桂鱼（活），白水鱼，昂刺鱼，斑点叉尾鮰鱼，鲜活鲫鱼
GET http://192.168.0.68:9200/_analyze
Content-Type: application/json

{
  "analyzer": "ik_max_word",
  "text": "昂刺鱼，斑点叉尾鮰鱼，鲜活鲫鱼"
}

###
GET http://127.0.0.1:9200/_analyze
Content-Type: application/json

{
  "analyzer": "ik_max_word",
  "text": "桂林好是好地方"
}
###
GET http://127.0.0.1:9200/_analyze
Content-Type: application/json

{
  "analyzer": "standard",
  "text": "this is a test适用范围广"
}

###
GET http://192.168.10.9:9200/_analyze
Content-Type: application/json

{
  "analyzer": "english",
  "text": "this is a test"
}

###
GET http://192.168.10.9:9200/_analyze
Content-Type: application/json

{
  "analyzer": "chinese",
  "text": "this is a test"
}

###
GET http://192.168.10.9:9200/_analyze
Content-Type: application/json

{
  "analyzer": "chinese",
  "text": "我和我的祖国一刻也不能分割"
}

###
GET http://192.168.10.9:9200/_analyze
Content-Type: application/json

{
  "analyzer": "ik_max_word",
  "text": "我和我的祖国一刻也不能分割 I cannot be separated from my country for a moment"
}
###
GET http://192.168.0.105:9200/_analyze
Content-Type: application/json

{
  "analyzer": "ik_max_word",
  "text": "我和我的祖国一刻也不能分割 I cannot be separated from my country for a moment"
}
###
GET http://192.168.10.9:9200/_analyze
Content-Type: application/json

{
  "analyzer": "ik_smart",
  "text": "我和我的祖国一刻也不能分割 I cannot be separated from my country for a moment"
}

###
GET http://192.168.10.9:9200/_analyze
Content-Type: application/json

{
  "analyzer": "whitespace",
  "text": "我和我的祖国一刻也不能分割 陈凯歌 张一白 管虎 徐峥、宁浩等"
}

###
GET http://192.168.10.9:9200/_analyze
Content-Type: application/json

{
  "analyzer": "pinyin",
  "text": "我和我的祖国一刻也不能分割 陈凯歌 张一白 管虎 徐峥、宁浩等"
}

###

PUT http://192.168.10.9:9200/analyze-index
Content-Type: application/json

{
  "settings": {
     "refresh_interval": "5s",
     "number_of_shards" :   3,
     "number_of_replicas" : 1
  },
  "mappings": {
    "resource": {
      "dynamic": false,
      "properties": {
        "title": {
          "type": "text",
          "analyzer": "ik_max_word",
          "search_analyzer": "ik_smart",
          "fields": {
            "cn": {
              "type": "text",
              "analyzer": "ik_max_word",
              "search_analyzer": "ik_smart"
            },
            "en": {
              "type": "text",
              "analyzer": "english"
            }
          }
        }
      }
    }
  }
}
###
POST http://192.168.10.9:9200/analyze-index/_search
Content-Type: application/json

{
  "query": {
    "multi_match": {
      "type":     "most_fields",
      "query":    "最新",
      "fields": [ "title", "title.cn", "title.en" ]
    }
  }
}
###
POST http://192.168.10.9:9200/analyze-index/_search
Content-Type: application/json

{
  "query": {
    "multi_match": {
      "type":     "most_fields",
      "query":    "最新",
      "fields": [ "title", "title.en" ]
    }
  }
}
###
POST http://192.168.10.9:9200/analyze-index/_search
Content-Type: application/json

{
  "query": {
    "multi_match": {
      "type":     "most_fields",
      "query":    "最新",
      "fields": ["title.en" ]
    }
  }
}
###
POST http://192.168.10.9:9200/analyze-index/_search
Content-Type: application/json

{
  "query": {
    "multi_match": {
      "type":     "most_fields",
      "query":    "fox",
      "fields": [ "title", "title.cn", "title.en" ]
    }
  }
}
###
POST http://192.168.10.9:9200/analyze-index/_search
Content-Type: application/json

{
  "query": {
    "multi_match": {
      "type":     "most_fields",
      "query":    "fox",
      "fields": [ "title", "title.cn" ]
    }
  }
}
###
POST http://192.168.10.9:9200/analyze-index/_search
Content-Type: application/json

{
  "query": {
    "multi_match": {
      "type":     "most_fields",
      "query":    "foxes",
      "fields": [ "title", "title.cn" ]
    }
  }
}
###
POST http://192.168.10.9:9200/analyze-index/_search
Content-Type: application/json

{
  "query": {
    "multi_match": {
      "type":     "most_fields",
      "query":    "fox",
      "fields": [ "title.en" ]
    }
  }
}





### ------------------------------------------   初始数据  -------------------------------------------------------------
###
PUT http://192.168.10.9:9200/analyze-index/_doc/1
Content-Type: application/json

{
  "title": "周星驰最新电影"
}
###
PUT http://192.168.10.9:9200/analyze-index/_doc/2
Content-Type: application/json

{
  "title": "周星驰最好看的新电影"
}
###
PUT http://192.168.10.9:9200/analyze-index/_doc/3
Content-Type: application/json

{
  "title": "周星驰最新电影，最好，新电影"
}
###
PUT http://192.168.10.9:9200/analyze-index/_doc/4
Content-Type: application/json

{
  "title": "最最最最好的新新新新电影"
}
###
PUT http://192.168.10.9:9200/analyze-index/_doc/5
Content-Type: application/json

{
  "title": "I'm not happy about the foxes"
}
###
POST http://192.168.10.9:9200/_bulk -H 'Content-Type: application/json'
{ "index" : {"_index" : "analyze-index", "_id" : "1" } }
{"doc":{"title" : "中华人民共和国，成立于1949年10月1日，位于亚洲东部，太平洋西岸" }}
{ "index" : {"_index" : "analyze-index", "_id" : "2" } }
{"doc":{"title" : "中国政府网_中央人民政府门户网站官网" }}
{ "index" : {"_index" : "analyze-index", "_id" : "3" } }
{"doc":{"title" : "中国(世界四大文明古国之一)_百度百科" }}
{ "index" : {"_index" : "analyze-index", "_id" : "4" } }
{"doc":{"title" : "“金蓝领”为“中国创造”添彩--社会--人民网" }}
{ "index" : {"_index" : "analyze-index", "_id" : "5" } }
{ "title" : "在4月29日播出的《这就是中国》节目中，复旦大学中国研究院院长张维为教授就“西方中心论”进行了解构。" }

###
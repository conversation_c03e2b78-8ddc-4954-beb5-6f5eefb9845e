package com.pinshang.qingyun.order.search;

import com.pinshang.qingyun.ApplicationOrderSearchService;
import org.junit.BeforeClass;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

@RunWith(SpringJUnit4ClassRunner.class)
@SpringBootTest(classes = ApplicationOrderSearchService.class,properties = {
        "application.name.switch=tramy-",
        "spring.profiles.active=dev"
})
public  abstract class AbstractJunitBase {
    @BeforeClass
    public static void init (){
        System.setProperty("es.set.netty.runtime.available.processors","false");
    }
}
package com.pinshang.qingyun.order.search.order;

import com.github.pagehelper.PageInfo;
import com.netflix.discovery.converters.Auto;
import com.pinshang.qingyun.box.utils.DateTimeUtil;
import com.pinshang.qingyun.box.utils.SpringUtil;
import com.pinshang.qingyun.order.dto.orderStatistics.OrderStatisticsMonitorIDTO;
import com.pinshang.qingyun.order.dto.orderStatistics.OrderSyncODTO;
import com.pinshang.qingyun.order.search.AbstractJunitBase;
import com.pinshang.qingyun.order.search.dto.EsOrderMonitorDTO;
import com.pinshang.qingyun.order.search.repository.EsOrderRepository;
import com.pinshang.qingyun.order.search.service.EsOrderService;
import com.pinshang.qingyun.order.search.service.EsSettingService;
import com.pinshang.qingyun.order.search.vo.EsOrderMonitorVo;
import com.pinshang.qingyun.order.service.OrderStatisticsMonitorClient;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.elasticsearch.core.ElasticsearchTemplate;
import org.springframework.test.annotation.Rollback;

import java.util.List;

/**
 * @ClassName EsOrderServiceTest
 * @Description TODO
 * <AUTHOR>
 * @Date 2020/10/10 9:58
 **/
public class EsOrderServiceTest extends AbstractJunitBase {
    @Autowired
    private EsOrderService esOrderService ;
    @Autowired
    private EsOrderRepository esOrderRepository;
    @Autowired
    private EsSettingService esSettingService;
    @Autowired
    private ElasticsearchTemplate elasticsearchTemplate;
    @Autowired
    private OrderStatisticsMonitorClient orderStatisticsMonitorClient;
    @Test
    public void client(){
        OrderStatisticsMonitorIDTO idto = new OrderStatisticsMonitorIDTO();
        idto.setStartTime(DateTimeUtil.parse("2018-01-01",DateTimeUtil.YYYY_MM_DD)  );
        idto.setEndTime( DateTimeUtil.parse("2018-01-31",DateTimeUtil.YYYY_MM_DD) );
        idto.setPageNo(2);//分页请求订单系统，查询订单
        idto.setPageSize(500);
        PageInfo<OrderSyncODTO> orderResult = orderStatisticsMonitorClient.queryOrderSyncList(idto);

        System.out.println( orderResult.toString().getBytes().length );
    }
    @Test
    public void aa(){
        esOrderRepository.refresh();
    }
    @Test
    public void queryEsOrderStatisticsInfo(){

        EsOrderMonitorVo vo = new EsOrderMonitorVo();
        vo.setStartTime(DateTimeUtil.parse("2018-01-01",DateTimeUtil.YYYY_MM_DD));
        vo.setEndTime(DateTimeUtil.parse("2018-01-31",DateTimeUtil.YYYY_MM_DD));
        long start = System.currentTimeMillis();
        EsOrderMonitorDTO dto = esOrderService.queryEsOrderStatisticsInfo(vo) ;
        long end = System.currentTimeMillis();
        long time = (end-start )/1000 ;
        System.out.println("start-end "+ time);
        System.out.println( dto.toString());
        // EsOrderMonitorDTO(totalOrderCount=229579, normalOrderCount=229216, totalOrderAmount=117985237.12)
    }
    @Test
    public void queryEsOrderStatisticsInfoV2(){
        EsOrderMonitorVo vo = new EsOrderMonitorVo();
        vo.setStartTime(DateTimeUtil.parse("2018-01-01",DateTimeUtil.YYYY_MM_DD));
        vo.setEndTime(DateTimeUtil.parse("2018-01-31",DateTimeUtil.YYYY_MM_DD));
        long start = System.currentTimeMillis();
        EsOrderMonitorDTO dto = esOrderService.queryEsOrderStatisticsInfoV2(vo) ;
        long end = System.currentTimeMillis();
        long time = (end-start )/1000 ;
        System.out.println("V2-start-end "+ time);
        System.out.println( dto.toString());
        // EsOrderMonitorDTO(totalOrderCount=229579, normalOrderCount=229216, totalOrderAmount=117985237.12000000476837158203125)
        // EsOrderMonitorDTO(totalOrderCount=229579, normalOrderCount=229216, totalOrderAmount=117985237.12)
    }
    @Test
    public void queryEsOrderDiffList(){
        EsOrderMonitorVo vo = new EsOrderMonitorVo();
        vo.setStartTime(DateTimeUtil.parse("2018-01-01",DateTimeUtil.YYYY_MM_DD));
        vo.setEndTime(DateTimeUtil.parse("2018-01-31",DateTimeUtil.YYYY_MM_DD));
        long start = System.currentTimeMillis();
        List<String> codeList  = esOrderService.queryEsOrderDiffList(vo) ;
        long end = System.currentTimeMillis();
        long time = (end-start ) ;
        System.out.println("queryEsOrderDiffList-start-end "+ time);
        System.out.println("codeListSize= "+ codeList.size() + " list "+codeList.toString());
        // 105557 13493 33343
    }
    @Test
    public void queryEsOrderDiffList2(){
        EsOrderMonitorVo vo = new EsOrderMonitorVo();
        vo.setStartTime(DateTimeUtil.parse("2018-01-01",DateTimeUtil.YYYY_MM_DD));
        vo.setEndTime(DateTimeUtil.parse("2018-01-31",DateTimeUtil.YYYY_MM_DD));
        long start = System.currentTimeMillis();
        List<String> codeList  = esOrderService.queryEsOrderDiffListV2(vo) ;
        long end = System.currentTimeMillis();
        long time = (end-start ) ;
        System.out.println("V2-queryEsOrderDiffListV2-start-end "+ time);
        System.out.println("codeListSize= "+ codeList.size() + " list "+codeList.toString());
        // 12376
        // 35520
    }


    @Test
    public void testRefresh(){
        esSettingService.esRefreshSwitch(true);
    }

}

<html>
<head>
    <title>手动执行索引</title>
    <script src="${request.contextPath}/static/js/jquery-1.11.1.min.js"></script>
    <link href="${request.contextPath}/static/css/style.css" rel="stylesheet" type="text/css"/>
    <style type="text/css">
        .pageDetail {
            display: none;
        }

        .show {
            display: table-row;
        }
    </style>
    <script>
        $(function () {
            $('#list').click(function () {
                $('.pageDetail').toggleClass('show');
            });
        });

    </script>
</head>
<body>
<div class="wrapper">
    <div class="middle">
    <h1 style="padding: 50px 0 20px;">手动执行索引列表</h1>
    <table class="gridtable" style="width:100%;">
        <thead>
            <tr>
                <th>名称</th>
                <th>操作</th>
            </tr>
        </thead>
        <tbody>
            <tr>
                <td>重建全部促销活动索引</td>
                <td><button onclick="executeIndex('/manual/createAllEsXdSalesPromotionIndex')">全部促销活动</button></td>
            </tr>
            <tr>
                <td>重建全部前台分类索引</td>
                <td><button onclick="executeIndex('/manual/createAllCategoryIndex')">全部前台分类</button></td>
            </tr>
            <tr>
                <td>重建全部商品索引</td>
                <td><button onclick="executeIndex('/manual/createAllCommodityIndex')">全部商品</button></td>
            </tr>
            <tr>
                <td>更新指定商品(参数必传)<br/>
                    商品IDS：<input type="text" id="updateCommodityIds" size="80"/> 以逗号分隔<br>
                </td>
                <td><button onclick="updateCommodityIds('/manual/createIndexByCommodityIdList')">更新指定商品</button></td>
            </tr>

            <tr>
                <td>重建全部前置仓商品索引</td>
                <td><button onclick="executeIndex('/manual/createAllShopCommodityIndex')">全部前置仓商品</button></td>
            </tr>
            <tr>
                <td>更新指定前置仓的指定商品(门店不传则更新所有门店,商品不传更新所有商品)<br/>
                    门店IDS：<input type="text" id="updateAssignShopIds" size="80"/> 以逗号分隔<br>
                    商品IDS：<input type="text" id="updateAssignCommodityIds" size="80"/> 以逗号分隔<br>
                </td>
                <td><button onclick="updateAssign('/manual/updateAssignShopCommodity')">更新指定前置仓的指定商品</button></td>
            </tr>
            <tr>
                <td>重建当天所有的特价索引</td>
                <td><button onclick="executeIndex('/manual/createAllPricePromotion')">当天所有的特价</button></td>
            </tr>
            <tr>
                <td>更新前置仓商品特价(门店ID不传则更新所有门店)<br/>
                    门店IDS：<input type="text" id="updateXdPricePromotionShopIds" size="80"/> 以逗号分隔<br>
                </td>
                <td><button onclick="updateXdPricePromotion('/manual/updateXdPricePromotion')">更新前置仓商品特价</button></td>
            </tr>
            <tr>
                <td>更新指定门店库存不足的商品(门店ID不传则更新所有门店)<br/>
                    门店IDS：<input type="text" id="updateSoldOutCommodityShopIds" size="80"/> 以逗号分隔<br>
                </td>
                <td><button onclick="updateSoldOutCommodity('/manual/updateSoldOutCommodity')">更新指定门店库存不足的商品</button></td>
            </tr>
            <tr>
                <td></td>
                <td><button id="shopBtn" onclick="queryAllXdShop('/manual/queryXdShopList')">展示所有前置仓列表</button></td>
            </tr>
            </tbody>
        </table>
    </div>

    <#--前置仓   DIV-->
    <div id="xdShopListDiv" class="middle" style="display:none;"></div>

    <div class="push"></div>
</div>
<script type="text/javascript">
    function executeIndex(url){
        $.get(url,function(data){
            if(data.success){
                alert("操作成功！ ")
            }else {
                alert(data.msg)
            }
        });
    }
    function updateCommodityIds(url){
        var ids = $("#updateCommodityIds").val();
        //兼容前端网页通用
//        url = url + "?ids=" + ids ;
//        $.get(url,function(data){
//            if(data.success){
//                alert("操作成功！ ")
//            }else {
//                alert(data.msg)
//            }
//        });
        $.post(url,{"commodityIds":ids},function(data){
            if(data.success){
                alert("操作成功！ ")
            }else {
                alert(data.msg)
            }
        });
    }
    function updateAssign(url){
        $.post(url,{"shopIds":$("#updateAssignShopIds").val(),"commodityIds":$("#updateAssignCommodityIds").val()},function(data){
            if(data.success){
                alert("操作成功！ "+data.msg)
            }else {
                alert(data.msg)
            }
        })
    }
    //查询前置仓列表
    function queryAllXdShop(url){
        var shopDiv = document.getElementById("xdShopListDiv");
        var flage = shopDiv.style.display == "none";
        if(flage){
            shopDiv.style.display = "block" ;
            jQuery("#shopBtn").html('隐藏所有前置仓信息');

            $.post(url,function(data){
                $("#xdShopListDiv").html(data);
            },"html");
        }else{
            shopDiv.style.display = "none";
            jQuery("#shopBtn").html('展示所有前置仓信息');
        }
    }

    //更新库存不足的商品
    function updateSoldOutCommodity(url){
        $.post(url,{"shopIds":$("#updateSoldOutCommodityShopIds").val()},function(data){
            if(data.success){
                alert("操作成功！ ")
            }else {
                alert(data.msg)
            }
        })
    }
    //更新指定前置仓特价
    function updateXdPricePromotion(url){
        $.post(url,{"shopIds":$("#updateXdPricePromotionShopIds").val()},function(data){
            if(data.success){
                alert("操作成功！ ")
            }else {
                alert(data.msg)
            }
        })
    }
</script>
</body>
</html>
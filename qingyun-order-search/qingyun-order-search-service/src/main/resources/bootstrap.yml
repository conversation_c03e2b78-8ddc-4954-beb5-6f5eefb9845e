pinshang:
  application-prefix:
  application-name: qingyun-order-search-service
spring:
  application:
    name: ${application.name.switch:${pinshang.application-prefix}}${pinshang.application-name}
  profiles:
    active: dev
  mvc:
    view:
      prefix: /templates/
      suffix: .ftl
  freemarker:
    cache: false
    request-context-attribute: request
server:
  tomcat:
    uri-encoding: UTF-8
    basedir: ./logs/${spring.application.name}
  port: 9064
logging:
    file: ${pinshang.application-name}
    path: ./logs/${spring.application.name}
#logstash:
#  service: *************:4560
#-----------------------------------  me  环境配置开始－－－－－－－－－－－－－－－－－－－
---
# 配置中心的地址
spring:
  profiles: me
  cloud:
    config:
      uri: http://localhost:9001/
      username: admin
      password: 111111
#-----------------------------------  dev 各环境配置开始－－－－－－－－－－－－－－－－－－－
---
# 配置中心的地址
spring:
  profiles: dev
  cloud:
    config:
      uri: http://*************:9001/
      username: admin
      password: 111111
#-----------------------------------  test  环境配置开始－－－－－－－－－－－－－－－－－－－
---
# 配置中心的地址
spring:
  profiles: test
  cloud:
    config:
      uri: http://*************:9001/
      username: admin
      password: 111111
#-----------------------------------  wg-prod  环境配置开始－－－－－－－－－－－－－－－－－－－
---
# 配置中心的地址
spring:
  profiles: wg-prod
  cloud:
    config:
      uri: http://**************:9001/
      username: admin
      password: qingyun123
#-----------------------------------  wg-hd-prod  环境配置开始－－－－－－－－－－－－－－－－－－－
---
# 配置中心的地址
spring:
  profiles: wg-hd-prod
  cloud:
    config:
      uri: http://192.168.100.65:9001/
      username: admin
      password: qingyun123
mybatis:
  type-aliases-package: com.pinshang.qingyun.sync.model
  mapper-locations: classpath*:mappers/*.xml
  config-location: classpath:conf/mybatis-typehandler.xml
mapper:
  mappers:
    - com.pinshang.qingyun.base.mybatis.MyMapper
  not-empty: false
  identity: MYSQL
  enum-as-simple-type: true

spring:
  http:
    encoding:
      charset: UTF-8
  session:
    store-type=none
feign:
  httpclient:
    enabled: true
  hystrix:
    enabled: true
eureka:
  client:
    healthcheck:
      enabled: true

management:
  endpoints:
    web:
      exposure:
        include: '*'
${AnsiColor.BRIGHT_GREEN}
        _____                   ______                                  ______                          ______
___________(_)______     __________  /_______ ______________ _   __________  /____________________ _    ___  /_____  _______
___  __ \_  /__  __ \    __  ___/_  __ \  __ `/_  __ \_  __ `/   __  ___/_  __ \  _ \_  __ \_  __ `/    __  __ \  / / /  __ \
__  /_/ /  / _  / / /    _(__  )_  / / / /_/ /_  / / /  /_/ /    _(__  )_  / / /  __/  / / /  /_/ /     _  / / / /_/ // /_/ /
_  .___//_/  /_/ /_/     /____/ /_/ /_/\__,_/ /_/ /_/_\__, /     /____/ /_/ /_/\___//_/ /_/_\__, /      /_/ /_/\__,_/ \____/
/_/                                                  /____/                                /____/
${AnsiColor.BRIGHT_RED}
Application Version: ${application.version}${application.formatted-version}
Spring Boot Version: ${spring-boot.version}${spring-boot.formatted-version}
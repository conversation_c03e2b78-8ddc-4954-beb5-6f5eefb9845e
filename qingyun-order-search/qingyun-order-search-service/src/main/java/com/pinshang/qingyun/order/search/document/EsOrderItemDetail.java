package com.pinshang.qingyun.order.search.document;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.pinshang.qingyun.box.utils.SpringUtil;
import com.pinshang.qingyun.order.dto.orderStatistics.OrderListGiftODTO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang.StringUtils;
import org.springframework.data.elasticsearch.annotations.DateFormat;
import org.springframework.data.elasticsearch.annotations.Document;
import org.springframework.data.elasticsearch.annotations.Field;
import org.springframework.data.elasticsearch.annotations.FieldType;

import javax.persistence.Id;
import javax.persistence.Transient;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 订单明细详细信息：
 *  订单主表信息+订单明细+下单客户信息+商品信息
 */
@Data
@Document(indexName = "tj-order-item-detail", type = "tj-order-item-detail",replicas = 2)
@NoArgsConstructor
@AllArgsConstructor
public class EsOrderItemDetail extends EsOrderItem{

    //订单详细信息
    @Field(type = FieldType.keyword)
    private String orderCode;
    @Field(type = FieldType.Long)
    private Long createId;
    @Field(type = FieldType.keyword,index = false)
    private String createName;
    @Field(type = FieldType.Date, format = DateFormat.custom,
            pattern ="yyyy-MM-dd HH:mm:ss || yyyy-MM-dd || yyyy/MM/dd HH:mm:ss|| yyyy/MM/dd ||epoch_millis"
    )
    @JsonFormat(shape =JsonFormat.Shape.STRING,pattern ="yyyy-MM-dd HH:mm:ss",timezone ="GMT+8")
    protected Date createTime;
    @Field(type = FieldType.Long)
    private Long updateId;
    @Field(type = FieldType.keyword,index = false)
    private String updateName;
    @Field(type = FieldType.Date, format = DateFormat.custom,
            pattern = "yyyy-MM-dd HH:mm:ss || yyyy-MM-dd || yyyy/MM/dd HH:mm:ss|| yyyy/MM/dd ||epoch_millis"
    )
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    protected Date updateTime;

    //商品详细信息
    @Field(type = FieldType.text)
    private String commodityName;
    @Field(type = FieldType.text)
    private String commoditySpec;
    @Field(type = FieldType.text)
    private String commodityUnitName;
    @Field(type = FieldType.text)
    private String commodityFirstKindName;
    @Field(type = FieldType.text)
    private String commoditySecondKindName;
    @Field(type = FieldType.text)
    private String commodityThirdKindName;
    @Field(type = FieldType.text)
    private String commodityFactoryName;
    @Field(type = FieldType.keyword)
    private String commodityFactoryCode;
    @Field(type = FieldType.text)
    private String commodityWorkshopName;
    @Field(type = FieldType.keyword)
    private String commodityWorkshopCode;
    @Field(type = FieldType.text)
    private String commodityFlowshopName;

    //客户信息
    @Field(type = FieldType.text)
    private String storeName;
    @Field(type = FieldType.text,index = false)
    private String storeShortName;
    @Field(type = FieldType.text,index = false)
    private String storeTypeName;

    public static EsOrderItemDetail convert(OrderListGiftODTO dto, EsOrder esOrder,EsStore esStore) {
        EsOrderItemDetail orderItemDetail = new EsOrderItemDetail();
        SpringUtil.copyProperties(dto,orderItemDetail);
        Long id = orderItemDetail.getId();
        SpringUtil.copyProperties(esOrder,orderItemDetail);
        if(esStore!=null){
            SpringUtil.copyProperties(esStore,orderItemDetail);
        }
        orderItemDetail.setId(id);
        orderItemDetail.setCommodityNum(dto.getCommodityNum()==null?null:dto.getCommodityNum().doubleValue());
        orderItemDetail.setCommodityPrice(dto.getCommodityPrice()==null?null:dto.getCommodityPrice().doubleValue());
        orderItemDetail.setTotalPriceCent(dto.getTotalPrice()==null?null:dto.getTotalPrice().multiply(BigDecimal.valueOf(100)).longValue());
        orderItemDetail.setRemark(StringUtils.isEmpty(dto.getRemark())?"":dto.getRemark());
        orderItemDetail.setCarportName(esOrder.getCarportName());
        return orderItemDetail;
    }
    public static EsOrderItemDetail processCommodity(EsOrderItemDetail orderItemDetail, EsTjCommodity esCommodity) {
        Long id = orderItemDetail.getId();
        SpringUtil.copyProperties(esCommodity,orderItemDetail);
        orderItemDetail.setId(id);
        return orderItemDetail;
    }

}

package com.pinshang.qingyun.order.search.controller;

import com.pinshang.qingyun.order.search.dto.FreshProductShipmentsEntry;
import com.pinshang.qingyun.order.search.dto.ProductSaleStatisticsSumEntry;
import com.pinshang.qingyun.order.search.dto.ProductSaleStatisticsTempEntry;
import com.pinshang.qingyun.order.search.service.EsFreshProductShipmentsService;
import com.pinshang.qingyun.order.search.service.EsProductSaleStatisticsService;
import com.pinshang.qingyun.order.search.vo.FreshProductShipmentsVo;
import com.pinshang.qingyun.order.search.vo.ProductSaleStatisticsVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 产品发货总量(鲜食)
 */
@RestController
@RequestMapping(value = "/esStatistical/freshProductShipments")
public class EsFreshProductShipmentsController {

    @Autowired
    private EsFreshProductShipmentsService esFreshProductShipmentsService;


    /**
     * 产品发货总量(鲜食)列表
     * @param vo
     * @return
     */
    @PostMapping("/list")
    public List<FreshProductShipmentsEntry> queryList(@RequestBody FreshProductShipmentsVo vo) {
        return esFreshProductShipmentsService.queryList(vo);
    }

}
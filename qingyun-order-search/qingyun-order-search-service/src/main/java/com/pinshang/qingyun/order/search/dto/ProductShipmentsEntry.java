package com.pinshang.qingyun.order.search.dto;

import com.pinshang.qingyun.order.search.document.EsTjCommodity;
import lombok.Data;
import org.apache.commons.lang.StringUtils;

import java.text.Collator;
import java.util.Comparator;
import java.util.Locale;

/**
 * 产品发货总量
 */
@Data
public class ProductShipmentsEntry extends EsStatisticsBaseEntry{

//    private Long commodityId;
//    private String commodityName;
//    private String factoryCode;
//    private String factoryName;
//    private String commodityCode;
//    private String workshopName;
//    private String flowshopName;
//    private String unit;
//    private Long workshopId;
    private Long storeId;
    private Long storeTypeId;

    private Double storeTypeTotal;
    private Double commodityTotal;


//    @Override
//    public int compareTo(ProductShipmentsEntry o) {
//        Comparator<ProductShipmentsEntry> comparator = new Comparator<ProductShipmentsEntry>() {
//            @Override
//            public int compare(ProductShipmentsEntry o1, ProductShipmentsEntry o2) {
//                int c ;
//                if(o1.getFactoryCode()==null){
//                    if(o2.getFactoryCode()==null){
//                        c = 0;
//                    }else{
//                        return -1;
//                    }
//                }else{
//                    if(o2.getFactoryCode()==null){
//                        return  1;
//                    }else{
//                        c = o1.getFactoryCode().compareTo(o2.getFactoryCode());
//                    }
//                }
//                if (c == 0) {
//                    if (StringUtils.isEmpty(o1.getWorkshopName())) {
//                        if (StringUtils.isEmpty(o2.getWorkshopName())) {
//                            c = 0;
//                        } else {
//                            return -1;
//                        }
//                    } else {
//                        if (StringUtils.isEmpty(o2.getWorkshopName())) {
//                            return 1;
//                        } else {
//                            Collator instance = Collator.getInstance(Locale.CHINA);
//                            c = instance.compare(o1.getWorkshopName(), o2.getWorkshopName());
//                        }
//                    }
//                }
//                if(c == 0){
//                    if(StringUtils.isEmpty(o1.getCommodityCode())){
//                        if(StringUtils.isEmpty(o2.getCommodityCode())){
//                            c = 0;
//                        }else{
//                            return -1;
//                        }
//                    }else{
//                        if(StringUtils.isEmpty(o2.getCommodityCode())){
//                            return  1;
//                        }else{
//                            c = o1.getCommodityCode().compareTo(o2.getCommodityCode());
//                        }
//                    }
//                }
//                return c;
//            }
//        };
//        return comparator.compare(this,o);
//    }
}

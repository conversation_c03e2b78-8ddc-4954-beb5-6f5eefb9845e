package com.pinshang.qingyun.order.search.service;

import com.pinshang.qingyun.base.api.ApiResult;
import com.pinshang.qingyun.base.constant.QingyunConstant;
import com.pinshang.qingyun.base.enums.OperateTypeEnums;
import com.pinshang.qingyun.base.enums.settlement.OperateTypeEnum;
import com.pinshang.qingyun.box.utils.BeanCloneUtils;
import com.pinshang.qingyun.box.utils.DateUtil;
import com.pinshang.qingyun.box.utils.ListUtil;
import com.pinshang.qingyun.box.utils.SpringUtil;
import com.pinshang.qingyun.order.dto.orderStatistics.OrderListGiftODTO;
import com.pinshang.qingyun.order.dto.orderStatistics.OrderSyncODTO;
import com.pinshang.qingyun.order.search.document.*;
import com.pinshang.qingyun.order.search.dto.EsOrderMonitorDTO;
import com.pinshang.qingyun.order.search.repository.*;
import com.pinshang.qingyun.order.search.vo.EsOrderMonitorVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.search.aggregations.Aggregation;
import org.elasticsearch.search.aggregations.AggregationBuilders;
import org.elasticsearch.search.aggregations.metrics.sum.InternalSum;
import org.elasticsearch.search.aggregations.metrics.valuecount.InternalValueCount;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.elasticsearch.core.ElasticsearchTemplate;
import org.springframework.data.elasticsearch.core.ScrolledPage;
import org.springframework.data.elasticsearch.core.aggregation.AggregatedPage;
import org.springframework.data.elasticsearch.core.query.NativeSearchQueryBuilder;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;


@Service
@Slf4j
public class EsOrderService {
    @Autowired
    private EsOrderRepository orderRepository;
    @Autowired
    private EsOrderItemRepository orderItemRepository;
    @Autowired
    private EsStoreService esStoreService;
    @Autowired
    private EsCommodityService esCommodityService;
    @Autowired
    private ElasticsearchTemplate elasticsearchTemplate;
    @Autowired
    private EsOrderBaseRepository esOrderBaseRepository;
    @Autowired
    private EsOrderItemBaseRepository esOrderItemBaseRepository;
    @Autowired
    private EsOrderItemDetailRepository esOrderItemDetailRepository;

    /**
     * 新增订单
     * @param orderKafkaVo
     * @return
     */
    public ApiResult insertEsTjOrderIndex(OrderSyncODTO orderKafkaVo) {
        ApiResult apiResult = new ApiResult().initSuccess();
        try{
            if(orderKafkaVo==null || CollectionUtils.isEmpty(orderKafkaVo.getItemList())){
                return ApiResult.fail("订单同步到ES数据异常");
            }
            this.processEsOrderKafka(orderKafkaVo,OperateTypeEnum.ADD,null);
        } catch (Exception e) {
            log.error("新增订单更新索引异常：订单数据={}",orderKafkaVo,e);
        }
        return apiResult;
    }

    /**
     * 修改订单
     * @param orderKafkaVo
     * @return
     */
    public ApiResult updateEsTjOrderIndex(OrderSyncODTO orderKafkaVo) {
        ApiResult apiResult = new ApiResult().initSuccess();
        try{
            Optional<EsOrder> esTjOrderOptional = orderRepository.findById(orderKafkaVo.getId());
            EsOrder esOrder;
            if (esTjOrderOptional.isPresent()) {
                esOrder = esTjOrderOptional.get();
                this.processEsOrderKafka(orderKafkaVo,OperateTypeEnum.EDIT,esOrder);
            }else{
                this.processEsOrderKafka(orderKafkaVo,OperateTypeEnum.ADD,null);
            }
        } catch (Exception e) {
            log.error("修改订单更新索引异常：订单数据={}",orderKafkaVo,e);
        }
        return apiResult;
    }

    /**
     * 取消订单
     * @param orderKafkaVo
     * @return
     */
    public ApiResult cancelEsTjOrderIndex(OrderSyncODTO orderKafkaVo) {
        ApiResult apiResult = new ApiResult().initSuccess();
        try{
            Optional<EsOrder> esTjOrderOptional = orderRepository.findById(orderKafkaVo.getId());
            if (esTjOrderOptional.isPresent()) {
                EsOrder esOrder = esTjOrderOptional.get();
                if(esOrder.getOrderStatus()!=orderKafkaVo.getOrderStatus()){
                   this.deleteOrderStatus(Arrays.asList(esOrder.getId()));
                }
            }
//            else{
//                this.insertEsTjOrderIndex(orderKafkaVo);
//            }
        } catch (Exception e) {
            log.error("取消订单更新索引异常：订单数据={}",orderKafkaVo,e);
        }
        return apiResult;
    }

    private Map<Long,EsStore> buildStoreMap(List<Long> storeIdList){
        if(CollectionUtils.isEmpty(storeIdList)){
            return Collections.EMPTY_MAP;
        }
        Map<Long,EsStore> esStoreMap = new HashMap<>();
        List<EsStore> storeList = esStoreService.processStoreInfoByIdList(storeIdList);
        storeList.forEach(item->{
            esStoreMap.put(item.getId(),item);
        });
        return esStoreMap;
    }

    /**
     * 同步订单、订单基础信息、订单明细基础信息、订单明细+客户、订单明细+客户+商品
     * @param orderKafkaVo
     * @param operateTypeEnum
     * @param oldEsOrder
     */
    public void processEsOrderKafka(OrderSyncODTO orderKafkaVo, OperateTypeEnum operateTypeEnum,EsOrder oldEsOrder){
        EsOrder esOrder = EsOrder.convert(orderKafkaVo,oldEsOrder);
        EsStore esStore = null;
        if(operateTypeEnum.equals(OperateTypeEnum.ADD)){
            Map<Long,EsStore> storeMap = this.buildStoreMap(Collections.singletonList(orderKafkaVo.getStoreId()));
            esStore = storeMap.isEmpty()?null:storeMap.get(orderKafkaVo.getStoreId());
            //如果冲突以主干为主
            EsOrder.processStore(esOrder,esStore);
        }
        this.saveEsOrder(Collections.singletonList(esOrder));

        //保存订单明细
        List<EsOrderItemDetail> orderItemDetailList = new ArrayList<>(orderKafkaVo.getItemList().size());
        for(OrderListGiftODTO giftODTO : orderKafkaVo.getItemList()){
            EsOrderItemDetail orderItemDetail = EsOrderItemDetail.convert(giftODTO,esOrder,esStore);
            orderItemDetailList.add(orderItemDetail);
        }
        this.saveEsOrderItem(orderItemDetailList);
    }

    private void saveEsOrder(List<EsOrder> orderList){
        orderRepository.saveAll(orderList);
        List<EsOrderBase> orderBaseList = BeanCloneUtils.copyTo(orderList,EsOrderBase.class);
        esOrderBaseRepository.saveAll(orderBaseList);
    }

    private void saveEsOrderItem(List<EsOrderItemDetail> detailList){
        List<List<EsOrderItemDetail>> splitSubList = ListUtil.splitSubList(detailList,500);
        Map<Long,EsTjCommodity> esCommodityMap = new HashMap<>();
        splitSubList.forEach(subList->{
            List<Long> commodityIds = subList.stream().map(EsOrderItemDetail::getCommodityId).distinct()
                    .filter(cId->!esCommodityMap.keySet().contains(cId)).collect(Collectors.toList());
            if(SpringUtil.isNotEmpty(commodityIds)){
                List<EsTjCommodity> commodityList = esCommodityService.processCommodityInfoByIdList(commodityIds);
                commodityList.forEach(item->{
                    esCommodityMap.put(item.getId(),item);
                });
            }
            List<EsOrderItemDetail> orderItemDetailList = new ArrayList<>(subList.size());
            for(EsOrderItemDetail itemDetail : subList){
                orderItemDetailList.add(EsOrderItemDetail.processCommodity(itemDetail,esCommodityMap.get(itemDetail.getCommodityId())));
            }
            esOrderItemDetailRepository.saveAll(orderItemDetailList);
            List<EsOrderItem> orderItemList = BeanCloneUtils.copyTo(orderItemDetailList,EsOrderItem.class);
            orderItemRepository.saveAll(orderItemList);
            esOrderItemBaseRepository.saveAll(BeanCloneUtils.copyTo(orderItemList,EsOrderItemBase.class));
        });
    }

    /**
     * ES订单数量
     * @param vo
     * @return
     * @see #queryEsOrderStatisticsInfoV2
     */
    @Deprecated
    public EsOrderMonitorDTO queryEsOrderStatisticsInfo(EsOrderMonitorVo vo){
        Iterable<EsOrder> iterable = this.queryEsOrder(vo);
        int totalOrderCount = 0;
        int normalOrderCount = 0;
        BigDecimal totalOrderAmount = BigDecimal.ZERO;
        if(iterable!=null){
            Iterator<EsOrder> iterator = iterable.iterator();
            while (iterator.hasNext()){
                EsOrder esOrder = iterator.next();
                totalOrderCount++;
                if(esOrder.getOrderStatus()== QingyunConstant.NUM_INT_ZERO){
                    normalOrderCount++;
                }
                totalOrderAmount = totalOrderAmount.add(esOrder.getFinalAmount()==null?BigDecimal.ZERO:BigDecimal.valueOf(esOrder.getFinalAmount()));
            }
        }
        EsOrderMonitorDTO monitorDTO = new EsOrderMonitorDTO(totalOrderCount,normalOrderCount,totalOrderAmount);
        return monitorDTO;
    }
    public EsOrderMonitorDTO queryEsOrderStatisticsInfoV2(EsOrderMonitorVo vo){
        EsOrderMonitorDTO.EsAggrOrderMonitorDTO dto1 = queryEsOrderV2(vo );
        vo.setIsNormalOrder( Boolean.TRUE );
        EsOrderMonitorDTO.EsAggrOrderMonitorDTO dto2 = queryEsOrderV2(vo );
        return new EsOrderMonitorDTO(dto1.getOrderCount(),dto2.getOrderCount(),dto1.getTotalOrderAmount());
    }
    private EsOrderMonitorDTO.EsAggrOrderMonitorDTO queryEsOrderV2(EsOrderMonitorVo vo){
        BoolQueryBuilder boolQuery = constructCondition(vo);
        if( vo.getIsNormalOrder() ){
            boolQuery.must(QueryBuilders.termQuery("orderStatus", QingyunConstant.NUM_INT_ZERO) );
        }
        Pageable pageable = PageRequest.of(QingyunConstant.NUM_INT_ZERO,QingyunConstant.NUM_INT_ONE);
        NativeSearchQueryBuilder searchQuery = new NativeSearchQueryBuilder()
                .withQuery(boolQuery).withPageable(pageable)
                .addAggregation(AggregationBuilders.count("count_id").field("id"))
               ;
        if( !vo.getIsNormalOrder() ){
            searchQuery.addAggregation(AggregationBuilders.sum("sum_final_amount").field("finalAmount")) ;
        }
        AggregatedPage<EsOrderBase> page = elasticsearchTemplate.queryForPage(searchQuery.build(),EsOrderBase.class);
        if( page.hasAggregations() ){
            Map<String, Aggregation> mapAggr = page.getAggregations().asMap();
            long  count = ((InternalValueCount)mapAggr.get("count_id")).getValue();
            double sum = !vo.getIsNormalOrder() ?((InternalSum)mapAggr.get("sum_final_amount")).getValue()
                                    : 0;
            return new EsOrderMonitorDTO.EsAggrOrderMonitorDTO( Long.valueOf(count).intValue(), new BigDecimal( sum ).setScale( 2, BigDecimal.ROUND_HALF_DOWN)) ;
        }
        return null ;
    }
    private Iterable<EsOrder> queryEsOrder(EsOrderMonitorVo vo){
        Iterable<EsOrder> iterable = orderRepository.search(constructCondition(vo));
        return iterable;
    }
    private BoolQueryBuilder constructCondition(EsOrderMonitorVo vo){
        BoolQueryBuilder boolQuery = QueryBuilders.boolQuery();
        if(SpringUtil.isNotEmpty(vo.getOrderCodeList())){
            boolQuery.must(QueryBuilders.termsQuery("orderCode",vo.getOrderCodeList()));
        }
        if(vo.getOrderTime()!=null){
            boolQuery.must(QueryBuilders.termQuery("orderTime",DateUtil.getDateFormate(vo.getOrderTime(),"yyyy-MM-dd")));
        }
        if(vo.getStartTime()!=null && vo.getEndTime()!=null){
            QueryBuilder query1 = QueryBuilders.rangeQuery("orderTime").gte(DateUtil.getDateFormate(vo.getStartTime(),"yyyy-MM-dd"));
            QueryBuilder query2 = QueryBuilders.rangeQuery("orderTime").lte(DateUtil.getDateFormate(vo.getEndTime(),"yyyy-MM-dd"));
            boolQuery.must(query1).must(query2);
        }
        return boolQuery ;
    }

    /**
     * 查询ES已存在的订单号，比较差异订单
     * @param vo
     * @return
     */
    @Deprecated
    public List<String> queryEsOrderDiffList(EsOrderMonitorVo vo) {
        List<String> orderCodeList = new ArrayList<>();
        Iterable<EsOrder> iterable = this.queryEsOrder(vo);
        if(iterable!=null){
            Iterator<EsOrder> iterator = iterable.iterator();
            while (iterator.hasNext()){
                EsOrder esOrder = iterator.next();
                orderCodeList.add(esOrder.getOrderCode());
            }
        }
        return orderCodeList;
    }
    public List<String> queryEsOrderDiffListV2(EsOrderMonitorVo vo) {
        List<String> orderCodeList = new ArrayList<>();
        NativeSearchQueryBuilder searchQuery = new NativeSearchQueryBuilder().withQuery( constructCondition(vo) )
                .withFields("orderCode").withPageable(PageRequest.of(QingyunConstant.NUM_INT_ZERO, QingyunConstant.NUM_INT_5000)) ;
        ScrolledPage<EsOrderBase> scroll =(ScrolledPage<EsOrderBase>)  elasticsearchTemplate.startScroll(QingyunConstant.LONG_30000, searchQuery.build(),EsOrderBase.class);
        while (scroll.hasContent()) {
            for (EsOrderBase dto : scroll.getContent()) {
                orderCodeList.add( dto.getOrderCode() );
            }
            //取下一页，scrollId在es服务器上可能会发生变化，需要用最新的。发起continueScroll请求会重新刷新快照保留时间
            scroll = (ScrolledPage<EsOrderBase>) elasticsearchTemplate.continueScroll(scroll.getScrollId(), QingyunConstant.LONG_30000, EsOrderBase.class);
        }
        //及时释放es服务器资源
        elasticsearchTemplate.clearScroll(scroll.getScrollId());
//        BoolQueryBuilder boolQuery = constructCondition(vo);
//
//        Pageable pageable = PageRequest.of(0,1);
//        NativeSearchQueryBuilder searchQuery = new NativeSearchQueryBuilder()
//                .withQuery(boolQuery).withPageable(pageable)
//                .addAggregation( AggregationBuilders.terms("term_orderCode").field("orderCode")
//                        .size(Integer.MAX_VALUE)
//                        .subAggregation(  AggregationBuilders.count("count_orderCode").field("orderCode")  )   )
//                ;
//        AggregatedPage<EsOrder> page = elasticsearchTemplate.queryForPage(searchQuery.build(),EsOrder.class);
//        if( page.hasAggregations() ){
//            Map<String, Aggregation> mapAggr = page.getAggregations().asMap();
//            long  count = ((InternalValueCount)mapAggr.get("count_id")).getValue();
//            double sum = !vo.getIsNormalOrder() ?((InternalSum)mapAggr.get("sum_final_amount")).getValue()
//                    : 0;
//        }
        return orderCodeList;
    }

    /**
     * 订单同步
     * @param orderSyncODTOList
     * @param storeMap
     */
    public void syncEsOrder(List<OrderSyncODTO> orderSyncODTOList,Map<Long,EsStore> storeMap) {
        if(SpringUtil.isEmpty(orderSyncODTOList)){
            return;
        }
        Set<Long> orderIds = new HashSet<>(orderSyncODTOList.size());
        List<EsOrder> orderList = new ArrayList<>(orderSyncODTOList.size());
        List<EsOrderItemDetail> orderItemDetailList = new ArrayList<>();
        orderSyncODTOList.forEach(orderEntry->{
            orderIds.add(orderEntry.getId());
            EsOrder esOrder = EsOrder.convert(orderEntry,null);
            EsStore esStore = null;
            if(SpringUtil.isNotEmpty(storeMap) && storeMap.get(orderEntry.getStoreId())!=null){
                esStore = storeMap.get(orderEntry.getStoreId());
                EsOrder.processStore(esOrder,esStore);
            }
            orderList.add(esOrder);
            for(OrderListGiftODTO item : orderEntry.getItemList()){
                orderItemDetailList.add(EsOrderItemDetail.convert(item,esOrder,esStore));
            }
        });
        this.saveEsOrder(orderList);
        this.saveEsOrderItem(orderItemDetailList);
    }

    public void deleteOrderStatus(List<Long> orderIdList) {
        if(SpringUtil.isEmpty(orderIdList)){
            return;
        }
        Iterable<EsOrder> orderIterable = orderRepository.findAllById(orderIdList);
        if(orderIterable!=null && orderIterable.iterator().hasNext()){
            orderIterable.forEach(item->item.setOrderStatus(2));
            orderRepository.saveAll(orderIterable);
        }
        Iterable<EsOrderItem> orderItemIterable = orderItemRepository.search(QueryBuilders.termsQuery("orderId",orderIdList));
        if(orderItemIterable!=null){
            orderItemIterable.forEach(item->item.setOrderStatus(2));
            orderItemRepository.saveAll(orderItemIterable);
        }
    }

}

package com.pinshang.qingyun.order.search.util;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.HttpEntity;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.entity.UrlEncodedFormEntity;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.client.methods.HttpPut;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClientBuilder;
import org.apache.http.message.BasicNameValuePair;
import org.apache.http.util.EntityUtils;

import java.io.*;
import java.util.*;

public class HttpClientUtil {
    private final static String encode = "utf-8";

    public static String httpPost(String url, Map<String, String> params) throws Exception {
        CloseableHttpClient httpClient = null;
        HttpPost httpPost = null;
        CloseableHttpResponse response = null;
        String result = null;
        try {
            httpClient = HttpClientBuilder.create().build();
            httpPost = new HttpPost(url);
            if ((params == null) || (params.isEmpty())) return null;
            List ps = new ArrayList();
            for (Iterator localIterator = params.entrySet().iterator(); localIterator.hasNext(); ) {
                Map.Entry entry = (Map.Entry)localIterator.next();
                ps.add(new BasicNameValuePair((String)entry.getKey(),(String)entry.getValue()));
            }
            RequestConfig requestConfig = RequestConfig.custom().setSocketTimeout(30000).setConnectTimeout(30000).build();
            httpPost.setConfig(requestConfig);
            httpPost.setEntity(new UrlEncodedFormEntity(ps, encode));
            httpPost.addHeader("Content-type",
                    "application/x-www-form-urlencoded");
            response = httpClient.execute(httpPost);

           result = EntityUtils.toString(response.getEntity(), encode);
        }
        catch (UnsupportedEncodingException e)
        {
            throw new Exception("调用接口异常!");
        } finally {
            if (response != null)
                response.close();

            if (httpClient != null)
                httpClient.close();
        }
        return result;
    }

    public static String httpPostJson(String url, String json) throws Exception {
        CloseableHttpClient httpClient = null;
        HttpPost httpPost = null;
        CloseableHttpResponse response = null;
        String result = null;
        try {
            httpClient = HttpClientBuilder.create().build();
            httpPost = new HttpPost(url);
            RequestConfig requestConfig = RequestConfig.custom().setSocketTimeout(30000).setConnectTimeout(30000).build();
            httpPost.setConfig(requestConfig);
            if (StringUtils.isNotBlank(json)) {
                httpPost.setEntity(new StringEntity(json, encode));
                httpPost.addHeader("Content-type", "application/json");
            }
            response = httpClient.execute(httpPost);
            result = EntityUtils.toString(response.getEntity(), encode);
        } catch (UnsupportedEncodingException e) {
            throw new Exception("调用接口异常!");
        } finally {
            if (response != null)
                response.close();

            if (httpClient != null)
                httpClient.close();
        }
        return result;
    }

    public static String httpGet(String url, Map<String, Object> params) throws Exception {
        CloseableHttpClient httpClient = null;
        CloseableHttpResponse response = null;
        String result = null;
        try {
            httpClient = HttpClientBuilder.create().build();
            StringBuilder uri = new StringBuilder(url);
            if ((params != null) && (!(params.isEmpty()))) {
                if (url.indexOf(63) > 0)
                    uri.append("&");
                else
                    uri.append("?");

                for (Iterator localIterator = params.entrySet().iterator(); localIterator.hasNext(); ) { Map.Entry entry = (Map.Entry)localIterator.next();
                    if (entry.getValue() == null) break;
                    String value = entry.getValue().toString();
                    uri.append((String)entry.getKey()).append("=");

                    uri.append(value).append("&");
                }

                uri.deleteCharAt(uri.length() - 1);
            }
            HttpGet httpGet = new HttpGet(uri.toString());
            RequestConfig requestConfig = RequestConfig.custom().setSocketTimeout(30000).setConnectTimeout(30000).build();
            httpGet.setConfig(requestConfig);
            response = httpClient.execute(httpGet);
            result = EntityUtils.toString(response.getEntity(), "UTF-8");
        } catch (UnsupportedEncodingException e) {
            throw new Exception("调用接口异常!");
        } finally {
            if (response != null)
                response.close();

            if (httpClient != null)
                httpClient.close();
        }
        return result;
    }

    public static String httpPut(String url,Map<String,Object> map) {
        CloseableHttpClient closeableHttpClient = HttpClientBuilder.create().build();
        HttpPut httpput = new HttpPut(url);
        /**header中通用属性*/
        httpput.setHeader("Accept","*/*");
        httpput.setHeader("Accept-Encoding","gzip, deflate");
        httpput.setHeader("Cache-Control","no-cache");
        httpput.setHeader("Connection", "keep-alive");
        httpput.setHeader("Content-Type", "application/json;charset=UTF-8");

        //组织请求参数
        StringEntity stringEntity = new StringEntity(JSON.toJSONString(map), encode);
        httpput.setEntity(stringEntity);
        String content = null;
        CloseableHttpResponse  httpResponse = null;
        try {
            httpResponse = closeableHttpClient.execute(httpput);
            HttpEntity entity = httpResponse.getEntity();
            content = EntityUtils.toString(entity, encode);
        } catch (Exception e) {
            e.printStackTrace();
        }finally{
            try {
                httpResponse.close();
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
        try {
            closeableHttpClient.close();
        } catch (IOException e) {
            e.printStackTrace();
        }
        return content;
    }
}
package com.pinshang.qingyun.order.search.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * 订单统计查询--订单监控查询参数
 */
@Data
public class EsOrderMonitorVo {
    private List<String> orderCodeList;
    private Date orderTime;
    private Date startTime;
    private Date endTime;
    /**
     * 是否只查正常订单 ,也就是只查 订单状态 =0 的订单
     * 订单状态(0正常,1删除,2取消)
     */
    private Boolean isNormalOrder = Boolean.FALSE;
}

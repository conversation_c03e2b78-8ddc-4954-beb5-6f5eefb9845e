package com.pinshang.qingyun.order.search.service;

import com.pinshang.qingyun.base.api.ApiResult;
import com.pinshang.qingyun.base.constant.QingyunConstant;
import com.pinshang.qingyun.box.utils.SpringUtil;
import com.pinshang.qingyun.order.search.document.EsTjCommodity;
import com.pinshang.qingyun.order.search.repository.EsTjCommodityRepository;
import com.pinshang.qingyun.product.dto.sync.CommodityInfoODTO;
import com.pinshang.qingyun.product.dto.sync.SelectSyncInfoByCodeListIDTO;
import com.pinshang.qingyun.product.service.sync.SyncCommodityInfoClient;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.elasticsearch.index.query.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * @Description: 商品es
 * @Author: fangting
 * @CreateDate: 2020/8/26 10:51
 */
@Service
@Slf4j
public class EsCommodityService {


    @Autowired
    private EsTjCommodityRepository esTjCommodityRepository;
    @Autowired
    private SyncCommodityInfoClient syncCommodityInfoClient;

    public ApiResult saveEsTjCommodity(List<EsTjCommodity>  esTjCommodityList){
        ApiResult apiResult = new ApiResult().initSuccess();

        if (CollectionUtils.isNotEmpty(esTjCommodityList)){

            List<Long> commodityIds = esTjCommodityList.stream().map(esTjCommodity -> esTjCommodity.getId()).collect(Collectors.toList());
            //根据请求商品id 查询es中的数据

            Iterable<EsTjCommodity> esTjCommodityIterable = esTjCommodityRepository.findAllById(commodityIds);
            if(esTjCommodityIterable!=null){
                //将数据按id存入map中
                Map<Long, EsTjCommodity> map = new HashMap<>();
                esTjCommodityIterable.forEach(item->{
                    map.put(item.getId(), item);
                });

                //es需要同步的数据List
                List<EsTjCommodity> syncEsTjCommodity = new ArrayList<>();

                //请求要同步的数据 对比es 中的数据  如果es中不存在，或者数据不一致，则数据需要更新，放入syncEsTjCommodity中
                esTjCommodityList.forEach(requestCommodity -> {
                    EsTjCommodity esCommodity = map.get(requestCommodity.getId());
                    if (esCommodity == null || !esCommodity.equals(requestCommodity)) {
                        syncEsTjCommodity.add(requestCommodity);
                    }
                });
                //保存
                if (CollectionUtils.isNotEmpty(syncEsTjCommodity)) {
                    esTjCommodityRepository.saveAll(syncEsTjCommodity);
                }
            }else {
                //如果es中查不到数据  直接全量更新
                esTjCommodityRepository.saveAll(esTjCommodityList);
            }
        }
        return apiResult;

    }

    /**
     * 查询es商品总数量
     * @return
     */
    public Long selectAllCommodityQuantity(){
        return esTjCommodityRepository.count();
    }

    /**
     * 查询es所有商品id
     * @return
     */
    public List<Long> selectAllCommodityIdList(){
        //总条数
        int total = (int) esTjCommodityRepository.count();
        if (total == 0){
            return Collections.EMPTY_LIST;
        }
        List<Long> commodityIds = new ArrayList<>(total);
        //分页查询
        int pageSize = total%QingyunConstant.ES_DEFAULT_PAGE_SIZE == 0 ? total/QingyunConstant.ES_DEFAULT_PAGE_SIZE : total/QingyunConstant.ES_DEFAULT_PAGE_SIZE+1;
        Long minId = 0L;
        /**
         * 根据id 升序，每次查询大于最后一条id 数据
         */
        Sort sort = new Sort(Sort.Direction.ASC, "id");
        for ( int i = 0; i < pageSize; i++){
            //条件
            RangeQueryBuilder queryBuilder = QueryBuilders.rangeQuery("id").gt(minId);
            //分页
            Pageable pageable = PageRequest.of( 0, QingyunConstant.ES_DEFAULT_PAGE_SIZE, sort);
            Iterable<EsTjCommodity> commodities = esTjCommodityRepository.search(queryBuilder,pageable);
            //组装id
            compileIds(commodityIds,commodities.iterator());
            //已查询出数据的最后一条的id值
            minId = commodityIds.get(commodityIds.size()-1);
        }

        return commodityIds;
    }

    /**
     * 提取商品id
     * @param commodityIds
     * @param esTjCommodities
     */
    private void compileIds(List<Long> commodityIds,Iterator<EsTjCommodity> esTjCommodities){
        if (esTjCommodities != null){
            while (esTjCommodities.hasNext()){
                commodityIds.add(esTjCommodities.next().getId());
            }
        }
    }



    /**
     * 订单同步冗余商品信息，针对缺失商品重新同步
     * @param commodityIdList
     * @return
     */
    public List<EsTjCommodity> processCommodityInfoByIdList(List<Long> commodityIdList){
        if(SpringUtil.isEmpty(commodityIdList)){
            return Collections.EMPTY_LIST;
        }
        List<EsTjCommodity> esCommodityList = new ArrayList<>();
        Iterable<EsTjCommodity> esTjCommodityIterable = esTjCommodityRepository.findAllById(commodityIdList);
        Set<Long> exsitIds = new HashSet<>();
        if(esTjCommodityIterable!=null){
            esTjCommodityIterable.forEach(item->{
                esCommodityList.add(item);
                exsitIds.add(item.getId());
            });
        }
        if(SpringUtil.isEmpty(exsitIds)){
            esCommodityList.addAll(this.syncEsCommodity(commodityIdList));
        }else{
            List<Long> lackIds = commodityIdList.stream().filter(id->!exsitIds.contains(id)).collect(Collectors.toList());
            if(SpringUtil.isNotEmpty(lackIds)){
                esCommodityList.addAll(this.syncEsCommodity(lackIds));
            }
        }
        return esCommodityList;
    }

    public List<EsTjCommodity> syncEsCommodity(List<Long> commodityIdList) {
        if(SpringUtil.isEmpty(commodityIdList)){
            return Collections.EMPTY_LIST;
        }
        try{
            SelectSyncInfoByCodeListIDTO idto = new SelectSyncInfoByCodeListIDTO();
            idto.setIdList(commodityIdList);
            CommodityInfoODTO commodityInfoODTO = syncCommodityInfoClient.selectCommodityInfo(idto);

            if(commodityInfoODTO!=null && SpringUtil.isNotEmpty(commodityInfoODTO.getCommodityList())){
                //未处理商品的条形码列表，只同步了主条码
                List<EsTjCommodity> esTjCommodityList = commodityInfoODTO.getCommodityList().stream().map(EsTjCommodity::convert).collect(Collectors.toList());
                esTjCommodityRepository.saveAll(esTjCommodityList);
                return esTjCommodityList;
            }
        }catch(Exception e){
            log.error("订单同步到ES，同步缺失的商品信息调用product服务异常：商品ID={}\",commodityIdList,e");
        }
        return Collections.EMPTY_LIST;
    }

}

package com.pinshang.qingyun;

import com.pinshang.qinyun.cache.service.RedisServiceDefinition;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.web.servlet.ServletComponentScan;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.FilterType;
import org.springframework.context.annotation.Import;
import org.springframework.kafka.annotation.EnableKafka;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurerAdapter;
import tk.mybatis.spring.annotation.MapperScan;

@Controller
@SpringBootApplication
@EnableDiscoveryClient
@EnableFeignClients
@Import(value = {RedisServiceDefinition.class})
@MapperScan(basePackages = {"com.pinshang.qingyun.sync.mapper"})
@ComponentScan(excludeFilters = {@ComponentScan.Filter(type = FilterType.ANNOTATION,
        value = com.pinshang.qingyun.base.configure.expand.annotation.Write.class)},basePackages = "com.pinshang.qingyun")
@ServletComponentScan
@EnableKafka
@EnableAsync
public class ApplicationOrderSearchService extends WebMvcConfigurerAdapter {
    public static void main(String[] args) {
//        System.setProperty("es.set.netty.runtime.available.processors","false");
        SpringApplication.run(ApplicationOrderSearchService.class, args);
    }

    @GetMapping(value={"","/"})
    public String index(){
        System.out.println("hello world");
        return "redirect:/chk.html";
    }
}

package com.pinshang.qingyun.order.search.conf;

import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.databind.JsonSerializer;
import com.fasterxml.jackson.databind.SerializerProvider;

import java.io.IOException;
import java.text.DecimalFormat;

/**
 * <AUTHOR>
 * @Version 0.1.0
 * @Date 2020/5/20 11:23
 * @Copyright © 2019-2020 qm
 * @Description -- 类说明 数字类型字符串转为保留两位小数的字符串
 **/
public class NumberStringSerializer extends JsonSerializer<String> {

    @Override
    public void serialize(String doubleStr, JsonGenerator jsonGenerator, SerializerProvider serializerProvider) throws IOException {
        if (doubleStr == null) {
            jsonGenerator.writeNull();
        } else {
            // "0.00"不足补0，"0.##"不足为空
            jsonGenerator.writeString(new DecimalFormat("0.00").format(doubleStr));
        }
    }



    public NumberStringSerializer() {
    }
}

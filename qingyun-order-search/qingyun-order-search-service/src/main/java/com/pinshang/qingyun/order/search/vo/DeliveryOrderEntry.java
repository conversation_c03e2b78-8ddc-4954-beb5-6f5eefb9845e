package com.pinshang.qingyun.order.search.vo;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.fastjson.annotation.JSONField;
import com.pinshang.qingyun.box.utils.BeanUtil;
import com.pinshang.qingyun.order.search.document.EsOrder;
import lombok.Data;
import org.springframework.beans.BeanUtils;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @Date 2019/3/5 16:45
 */
@Data
public class DeliveryOrderEntry {
    /**
     * 订单id
     */
    @ExcelIgnore
    private String id;
    /**
     * 送货日期
     */
    @ExcelProperty(value = "送货日期")
    private Date deliveryDate;
    /**
     * 客户编号
     */
    @ExcelProperty(value = "客户编号")
    private String storeCode;
    /**
     * 客户名称
     */
    @ExcelProperty(value = "客户名称")
    private String storeName;
    /**
     * 订单金额
     */
    @ExcelProperty(value = "订单金额")
    private BigDecimal finalAmount;
    /**
     * 送货员
     */
    @ExcelProperty(value = "送货员")
    private String deliveryManName;
    /**
     * 客户类型
     */
    @ExcelProperty(value = "客户类型")
    private String storeTypeName;
    /**
     * 督导
     */
    @ExcelProperty(value = "督导")
    private String supervisorName;
    /**
     * 结账客户
     */
    @ExcelProperty(value = "结账客户")
    private String settlementCustomer;
    /**
     * 操作员
     */
    @ExcelProperty(value = "操作员")
    private String operatorName;
    /**
     * 订单编码
     */
    @ExcelProperty(value = "订单编码")
    private String orderCode;
    /**
     * 班组长
     */
    @ExcelProperty(value = "班组长")
    private String teamLeaderName;

    /**
     * 订单类型
     */
    @ExcelIgnore
    private Integer orderType;
    /**
     * 创建人
     */
    @ExcelIgnore
    private String createName;

    public String getOperatorName() {
        if (this.orderType == 1 || this.orderType == 10){
            return this.createName;
        }else if (this.orderType == 2){
            return storeName;
        }
        return operatorName;
    }


    public static DeliveryOrderEntry convert(EsOrder esOrder){
        DeliveryOrderEntry deliveryOrderEntry = new DeliveryOrderEntry();
        if (esOrder == null){
            return deliveryOrderEntry;
        }
        BeanUtils.copyProperties(esOrder, deliveryOrderEntry);
        deliveryOrderEntry.setId(esOrder.getId().toString());
        deliveryOrderEntry.setDeliveryDate(esOrder.getOrderTime());
        deliveryOrderEntry.setSettlementCustomer(esOrder.getStoreSettName());
        deliveryOrderEntry.setDeliveryManName(esOrder.getDeliverymanName());
        //double直接转BigDecimal会精度缺失，所以先转成String
        deliveryOrderEntry.setFinalAmount(new BigDecimal(esOrder.getFinalAmount().toString()));

        return deliveryOrderEntry;
    }
}

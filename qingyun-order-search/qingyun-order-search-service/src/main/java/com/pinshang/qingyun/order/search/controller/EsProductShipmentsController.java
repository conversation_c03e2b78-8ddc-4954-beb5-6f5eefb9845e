package com.pinshang.qingyun.order.search.controller;


import com.pinshang.qingyun.order.search.dto.ProductShipmentsEntry;
import com.pinshang.qingyun.order.search.service.EsProductShipmentsService;
import com.pinshang.qingyun.order.search.vo.ProductShipmentsVo;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;


import java.util.List;

/**
 * 产品发货总量
 */
@RestController
@RequestMapping(value = "/esStatistical/productShipments")
public class EsProductShipmentsController {

    @Autowired
    private EsProductShipmentsService esProductShipmentsService;


    /**
     * 产品发货总量列表
     * @param vo
     * @return
     */
    @PostMapping("/list")
    public List<ProductShipmentsEntry> queryList(@RequestBody ProductShipmentsVo vo) {
        return esProductShipmentsService.queryList(vo);
    }



}
package com.pinshang.qingyun.order.search.service;

import com.pinshang.qingyun.box.utils.SpringUtil;
import com.pinshang.qingyun.order.search.document.EsTjCommodity;
import com.pinshang.qingyun.order.search.dto.EsStatisticsBaseEntry;
import com.pinshang.qingyun.order.search.repository.EsTjCommodityRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;

@Service
@Slf4j
public class EsStatisticsCommonService {

    @Autowired
    private EsTjCommodityRepository esTjCommodityRepository;

    public   <T extends EsStatisticsBaseEntry> List<T> processCommodityInfo(List<T> entryList,Set<Long> commodityIdList){
        if(SpringUtil.isEmpty(entryList)){
            return Collections.EMPTY_LIST;
        }
        Iterable<EsTjCommodity> esTjCommodityIterable = esTjCommodityRepository.findAllById(commodityIdList);
        Map<Long,EsTjCommodity> commodityMap = new HashMap<>();
        esTjCommodityIterable.forEach(item->{
            commodityMap.put(item.getId(),item);
        });
        entryList.forEach(entry->{
            if(commodityMap.get(entry.getCommodityId())!=null){
                EsTjCommodity esTjCommodity = commodityMap.get(entry.getCommodityId());
                entry.setCommodityCode(esTjCommodity.getCommodityCode());
                entry.setCommodityName(esTjCommodity.getCommodityName()+"("+esTjCommodity.getCommoditySpec()+")");
                entry.setFactoryId(esTjCommodity.getCommodityFactoryId());
                entry.setFactoryCode(esTjCommodity.getCommodityFactoryCode());
                entry.setFactoryName(esTjCommodity.getCommodityFactoryName());
                entry.setWorkshopId(esTjCommodity.getCommodityWorkshopId());
                entry.setWorkshopName(esTjCommodity.getCommodityWorkshopName());
                entry.setFlowshopName(esTjCommodity.getCommodityFlowshopName());
                entry.setUnit(esTjCommodity.getCommodityUnitName());
            }
        });
        return entryList;
    }

}

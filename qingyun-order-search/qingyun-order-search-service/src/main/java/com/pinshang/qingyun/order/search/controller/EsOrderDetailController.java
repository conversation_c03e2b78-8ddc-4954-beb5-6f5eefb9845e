package com.pinshang.qingyun.order.search.controller;

import com.github.pagehelper.PageInfo;
import com.pinshang.qingyun.order.search.document.EsOrder;
import com.pinshang.qingyun.order.search.service.EsOrderDetailService;
import com.pinshang.qingyun.order.search.vo.OrderDetailEntry;
import com.pinshang.qingyun.order.search.vo.OrderDetailVo;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;

/**
 * 订单明细查询搜索
 */
@RestController
@RequestMapping(value = "/esStatistical/orderDetail")
public class EsOrderDetailController {

    @Autowired
    private EsOrderDetailService esOrderDetailService;


    /**
     * 订单明细查询
     * @param vo
     * @return
     */
    @PostMapping("/list")
    public PageInfo<OrderDetailEntry> queryList(@RequestBody OrderDetailVo vo) {
        return esOrderDetailService.queryListV2(vo);
    }

    /**
     * 查询导出列表
     * @param vo
     * @return
     */
    @PostMapping("/exportList")
    public List<OrderDetailEntry> exportList(@RequestBody OrderDetailVo vo) {
        return esOrderDetailService.exportList(vo);
    }

    /**
     * 分析搜索请求时间测试使用
     * @param vo
     * @return
     */
    @PostMapping("/queryOrder")
    public Map<Long, EsOrder> queryOrderList(@RequestBody OrderDetailVo vo) {
        return esOrderDetailService.queryEsOrderMap(vo);
    }

    /**
     * 测试使用，对比不同索引查询速度
     * @param vo
     * @return
     */
    @PostMapping("/list1")
    public PageInfo<OrderDetailEntry> queryListV1(@RequestBody OrderDetailVo vo) {
        return esOrderDetailService.queryList(vo);
    }

}
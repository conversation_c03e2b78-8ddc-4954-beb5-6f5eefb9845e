package com.pinshang.qingyun.order.search.controller;

import com.alibaba.excel.EasyExcel;
import com.github.pagehelper.PageInfo;
import com.pinshang.qingyun.base.util.ExcelUtil;
import com.pinshang.qingyun.order.search.dto.DeliveryOrderDetailRespDTO;
import com.pinshang.qingyun.order.search.service.EsDeliveryOrderService;
import com.pinshang.qingyun.order.search.vo.DeliveryOrderEntry;
import com.pinshang.qingyun.order.search.vo.DeliveryOrderReqVO;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

/**
 * @Description: 送货单
 * @Author: fangting
 * @CreateDate: 2020/9/8 14:54
 */
@RestController
@RequestMapping("/statistical/deliveryOrder")
@Slf4j
public class EsDeliveryOrderController {
    @Autowired
    private EsDeliveryOrderService esDeliveryOrderService;

    /**
     * 送货单列表查询
     * @param reqVO
     * @return
     */
    @PostMapping(value = "/queryList")
    public PageInfo<DeliveryOrderEntry> queryList(@RequestBody DeliveryOrderReqVO reqVO){
        return esDeliveryOrderService.queryList(reqVO);
    }

    /**
     * 送货单详情
     * @param orderId
     * @return
     */
    @GetMapping("/orderDetail")
    public DeliveryOrderDetailRespDTO queryDeliveryOrderDetail(@RequestParam("id") Long orderId){
        return esDeliveryOrderService.queryDeliveryOrderDetail(orderId);
    }

    @ApiOperation(value = "导出列表", notes = "导出列表")
    @RequestMapping(value = "/export", method = RequestMethod.GET)
    public void exportExcel(DeliveryOrderReqVO reqVO, HttpServletResponse response) throws IOException {
        long qS = System.currentTimeMillis();
        reqVO.initExportPage();
        PageInfo<DeliveryOrderEntry> pageInfo = esDeliveryOrderService.queryList(reqVO);
        long qE = System.currentTimeMillis();
        log.info("送货单-导出--查询时间=" + ((qE - qS) / 1000));

        try {
            ExcelUtil.setFileNameAndHead(response, "送货单" + System.currentTimeMillis());
            EasyExcel.write(response.getOutputStream(), DeliveryOrderEntry.class).autoCloseStream(Boolean.FALSE).sheet("调拨出库明细")
                    .doWrite(pageInfo.getList());

        }catch (Exception e){
            log.error("送货单导出错误", e);
            ExcelUtil.setExceptionResponse( response );
        }
        long eE = System.currentTimeMillis();
        log.info("送货单-导出--excel处理时间="+ ( (eE -qE) /1000 )  );
    }

}

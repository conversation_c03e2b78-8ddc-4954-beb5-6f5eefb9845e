package com.pinshang.qingyun.order.search.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.pinshang.qingyun.base.page.Pagination;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * 产品发货总量(鲜食)请求参数
 */
@Data
@ApiModel
public class FreshProductShipmentsVo  {
    @ApiModelProperty("工厂id")
    private Long factoryId;
    @ApiModelProperty("送货日期")
    private Date orderDate;
    @ApiModelProperty("线路组")
    private Long lineGroupId;
    @ApiModelProperty("配送批次")
    private Integer deliveryBatch;
}

package com.pinshang.qingyun.order.search.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.format.DateTimeFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.pinshang.qingyun.base.configure.codec.DecimalSerializerKeep2;
import com.pinshang.qingyun.box.utils.SpringUtil;
import com.pinshang.qingyun.order.search.document.EsOrder;
import com.pinshang.qingyun.order.search.document.EsOrderItem;
import com.pinshang.qingyun.order.search.document.EsOrderItemDetail;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.swing.*;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Date;
import java.util.Map;

/**
 * 订单明细查询返回对象
 */
@Data
public class OrderDetailEntry {
    /**
     * 订单日期
     */
    @DateTimeFormat("yyyy-MM-dd")
    @ExcelProperty(value = "订单日期")
    private Date orderTime;
    /**
     *订单编码
     */
    @ExcelProperty(value = "订单编码")
    private String orderCode;
    /**
     *客户编码
     */
    @ExcelProperty(value = "客户编码")
    private String storeCode;
    /**
     *客户名称
     */
    @ExcelProperty(value = "客户名称")
    private String storeName;
    /**
     *商品编码
     */
    @ExcelProperty(value = "商品编码")
    private String commodityCode;
    /**
     *商品名称
     */
    @ExcelProperty(value = "商品名称")
    private String commodityName;
    /**
     *品类
     */
    @ExcelProperty(value = "一级品类")
    private String cateName;
    /**
     *规格
     */
    @ExcelProperty(value = "规格")
    private String commoditySpec;
    /**
     *单价
     */
    @ExcelProperty(value = "单价")
    @JsonSerialize(using = DecimalSerializerKeep2.class)
    private BigDecimal commodityPrice;
    /**
     *订货数量
     */
    @ExcelProperty(value = "订货数量")
    @JsonSerialize(using = DecimalSerializerKeep2.class)
    private BigDecimal orderGoodsNum;
    /**
     *商品金额
     */
    @ExcelProperty(value = "商品金额")
    @JsonSerialize(using = DecimalSerializerKeep2.class)
    private BigDecimal commodityAmount;
    /**
     *商品备注
     */
    @ExcelProperty(value = "商品备注")
    private String commodityRemark;
    /**
     *操作人
     */
    @ExcelProperty(value = "操作人")
    private String operator;
    @ApiModelProperty("订单创建时间")
    @ExcelProperty(value = "订单创建时间")
    private Date orderCreateTime;
    @ApiModelProperty("订单修改时间")
    @ExcelProperty(value = "订单修改时间")
    private Date orderUpdateTime;

    private Long commodityId;

    @ApiModelProperty("公司id")
    private Long companyId;

    public void setCommodityAmount(BigDecimal commodityAmount) {
        if(commodityAmount != null){
            this.commodityAmount = commodityAmount.setScale(2, RoundingMode.HALF_UP);
        }
    }

    public static OrderDetailEntry convert(EsOrderItem esOrderItem, Map<Long, EsOrder> orderMap){
        EsOrder esOrder = orderMap.get(esOrderItem.getOrderId());
        if(esOrder==null){
            return null;
        }
        OrderDetailEntry resEntry = new OrderDetailEntry();
        SpringUtil.copyProperties(esOrderItem,resEntry);
        resEntry.setOrderTime(esOrderItem.getOrderTime());
        resEntry.setOrderCode(esOrder.getOrderCode());
        resEntry.setStoreCode(esOrder.getStoreCode());
        resEntry.setStoreName(esOrder.getStoreName());
        resEntry.setOperator(esOrder.getUpdateName());
        resEntry.setOrderCreateTime(esOrder.getCreateTime());
        resEntry.setOrderUpdateTime(esOrder.getUpdateTime());
        resEntry.setCommodityPrice(new BigDecimal(Double.toString(esOrderItem.getCommodityPrice())));
        resEntry.setOrderGoodsNum(new BigDecimal(Double.toString(esOrderItem.getCommodityNum())));
        resEntry.setCommodityAmount(new BigDecimal(Double.toString(Double.valueOf(esOrderItem.getTotalPriceCent())/100)));
        resEntry.setCommodityRemark(esOrderItem.getRemark());
        resEntry.setCommodityId(esOrderItem.getCommodityId());
        resEntry.setCompanyId(esOrderItem.getCompanyId());
        return resEntry;
    }

    public static OrderDetailEntry convert(EsOrderItemDetail orderItemDetail){
        OrderDetailEntry resEntry = new OrderDetailEntry();
        SpringUtil.copyProperties(orderItemDetail,resEntry);
        resEntry.setStoreCode(orderItemDetail.getStoreCode());
        resEntry.setOperator(orderItemDetail.getUpdateName());
        resEntry.setOrderCreateTime(orderItemDetail.getCreateTime());
        resEntry.setOrderUpdateTime(orderItemDetail.getUpdateTime());
        resEntry.setCommodityPrice(new BigDecimal(Double.toString(orderItemDetail.getCommodityPrice())));
        resEntry.setOrderGoodsNum(new BigDecimal(Double.toString(orderItemDetail.getCommodityNum())));
        resEntry.setCommodityAmount(new BigDecimal(Double.toString(Double.valueOf(orderItemDetail.getTotalPriceCent())/100)));
        resEntry.setCommodityRemark(orderItemDetail.getRemark());
        resEntry.setCateName(orderItemDetail.getCommodityFirstKindName());
        resEntry.setCompanyId(orderItemDetail.getCompanyId());
        return resEntry;
    }
}

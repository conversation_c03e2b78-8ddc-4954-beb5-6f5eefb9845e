package com.pinshang.qingyun.order.search.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.math.BigDecimal;

/**
 * 订单统计查询--订单比较返回结果
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ToString
public class EsOrderMonitorDTO {
    //订单总数
    private Integer totalOrderCount;

    //正常订单数（orderStatus=0）
    private Integer normalOrderCount;

    //总金额数(取得最终金额)
    private BigDecimal totalOrderAmount;

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class EsAggrOrderMonitorDTO{
        private Integer orderCount;
        private BigDecimal totalOrderAmount;
    }
}

package com.pinshang.qingyun.order.search.document;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.pinshang.qingyun.box.utils.SpringUtil;
import com.pinshang.qingyun.order.dto.orderStatistics.OrderListGiftODTO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang.StringUtils;
import org.springframework.data.elasticsearch.annotations.DateFormat;
import org.springframework.data.elasticsearch.annotations.Document;
import org.springframework.data.elasticsearch.annotations.Field;
import org.springframework.data.elasticsearch.annotations.FieldType;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 订单明细 + 订单部分信息(搜索条件使用)+ 下单客户信息 + 商品部分信息(搜索条件使用)
 */
@Data
@Document(indexName = "tj-order-item", type = "tj-order-item",replicas = 2)
@NoArgsConstructor
@AllArgsConstructor
public class EsOrderItem extends EsOrderItemBase{

    //冗余订单信息
    @Field(type = FieldType.Integer)
    private Integer orderStatus;
    @Field(type = FieldType.Date, format = DateFormat.custom,
            pattern = "yyyy-MM-dd HH:mm:ss || yyyy-MM-dd || yyyy/MM/dd HH:mm:ss|| yyyy/MM/dd ||epoch_millis"
    )
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date orderTime;
    @Field(type = FieldType.Integer)
    private Integer deliveryBatch;

    //冗余商品信息
    @Field(type = FieldType.keyword)
    private String commodityCode;
    @Field(type = FieldType.Long)
    private Long commodityFirstKindId;
    @Field(type = FieldType.Long)
    private Long commoditySecondKindId;
    @Field(type = FieldType.Long)
    private Long commodityThirdKindId;
    @Field(type = FieldType.Long)
    private Long commodityFactoryId;
    @Field(type = FieldType.Long)
    private Long commodityWorkshopId;
    @Field(type = FieldType.Long)
    private Long commodityFlowshopId;
    @Field(type = FieldType.Integer)
    private Integer isSummary;

    //冗余客户信息
    @Field(type = FieldType.Long)
    private Long storeId;
    @Field(type = FieldType.text,fielddata = true)
    private String storeCode;
    @Field(type = FieldType.Long)
    private Long storeTypeId;
    //送货员
    @Field(type = FieldType.Long)
    private Long deliverymanId;
    //销售员
    @Field(type = FieldType.Long)
    private Long salesmanId;
    //督导
    @Field(type = FieldType.Long)
    private Long supervisorId;
    //大区经理
    @Field(type = FieldType.Long)
    private Long regionManagerId;
    //主任
    @Field(type = FieldType.Long)
    private Long officeDirectorId;
    //班组长
    @Field(type = FieldType.Long)
    private Long teamLeaderId;
    /**
     * 线路
     */
    @Field(type = FieldType.Long)
    private Long storeLineId;
    @Field(type = FieldType.text)
    private String storeLineCode;
    /**
     * 线路组
     */
    @Field(type = FieldType.Long)
    private Long storeLineGroupId;
    @Field(type = FieldType.text)
    private String storeLineGroupName;
    /**
     * 发货时间
     */
    @Field(type = FieldType.Long)
    private Long deliveryTimeId;
    @Field(type = FieldType.keyword)
    private String deliveryTime;
    /**
     * 发货仓库
     */
    @Field(type = FieldType.Long)
    private Long deliveryWarehouseId;
    /**
     * 结账客户
     */
    @Field(type = FieldType.Long)
    private Long storeSettId;
    /**
     * 客户所属公司
     */
    @Field(type = FieldType.Long)
    private Long storeCompanyId;
    /**
     * 渠道
     */
    @Field(type = FieldType.Long)
    private Long storeChannelId;

    @Field(type = FieldType.text)
    private String carportName;
    @Field(type = FieldType.Integer)
    private Integer printDeliveryQueue;


    public static EsOrderItem convert(OrderListGiftODTO dto,EsOrder esOrder) {
        EsOrderItem esOrderItem = new EsOrderItem();
        SpringUtil.copyProperties(dto,esOrderItem);
        Long id = esOrderItem.getId();
        SpringUtil.copyProperties(esOrder,esOrderItem);
        esOrderItem.setId(id);
        esOrderItem.setCommodityNum(dto.getCommodityNum()==null?null:dto.getCommodityNum().doubleValue());
        esOrderItem.setCommodityPrice(dto.getCommodityPrice()==null?null:dto.getCommodityPrice().doubleValue());
        esOrderItem.setTotalPriceCent(dto.getTotalPrice()==null?null:dto.getTotalPrice().multiply(BigDecimal.valueOf(100)).longValue());
        esOrderItem.setRemark(StringUtils.isEmpty(dto.getRemark())?"":dto.getRemark());
        esOrderItem.setCarportName(esOrder.getCarportName());
        return esOrderItem;
    }
    public static EsOrderItem processCommodity(EsOrderItem orderItem,EsTjCommodity esCommodity) {
        Long id = orderItem.getId();
        SpringUtil.copyProperties(esCommodity,orderItem);
        orderItem.setId(id);
        return orderItem;
    }

}

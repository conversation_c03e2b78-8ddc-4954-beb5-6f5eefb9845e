package com.pinshang.qingyun.order.search.vo;

import com.pinshang.qingyun.base.page.Pagination;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * 产品销售汇总请求参数
 */
@Data
public class ProductSaleStatisticsVo {

    private Long storeSettId;
    private List<Long> storeSettIds;
    private String storeSettName;

    private Long storeId;
    private List<Long> storeIds;
    private String storeCode;

    private Date startDate;

    private Date endDate;

    private Long factoryId;

    private String factoryName;

    private Long workshopId;

    private String workshopName;

    private Long commodityId;

    private String commodityCode;

    private Long storeCompanyId;

    private String storeCompanyName;

    private Long storeTypeId;

    private String storeTypeName;

    private Long supervisorId;

    private String supervisorName;

    private Long regionManagerId;

    private String regionManagerName;

    private Long lineGroupId;

    private String lineGroupName;

    private Long deliveryManId;

    private String deliveryManName;

    private Long cateId1;

    private Long cateId2;

    private Long cateId3;

    private String cateName;

    private Long userId;

    private List<Long> commodityIds;

    private Long companyId;

}

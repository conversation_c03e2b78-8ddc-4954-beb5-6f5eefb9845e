package com.pinshang.qingyun.order.search.listener;

import com.alibaba.fastjson.JSON;
import com.pinshang.qingyun.base.annotations.OnlineSwitchWatcher;
import com.pinshang.qingyun.base.api.QYApplicationContext;
import com.pinshang.qingyun.box.utils.SpringUtil;
import com.pinshang.qingyun.kafka.base.BaseKafkaOnlineSwitchProcessor;
import com.pinshang.qingyun.kafka.base.KafkaMessageWrapper;
import com.pinshang.qingyun.kafka.base.KafkaTopicConstant;
import com.pinshang.qingyun.kafka.enums.KafkaMessageOperationTypeEnum;
import com.pinshang.qingyun.order.dto.orderStatistics.OrderSyncODTO;
import com.pinshang.qingyun.order.search.document.EsStore;
import com.pinshang.qingyun.order.search.service.EsOrderService;
import com.pinshang.qingyun.order.search.service.EsStoreService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.List;

@Component
@Slf4j
@OnlineSwitchWatcher
public class OrderSearchListener extends BaseKafkaOnlineSwitchProcessor {

    @Autowired
    private EsOrderService esOrderService;
    @Autowired
    private EsStoreService esStoreService;

    /**
     * 订单同步到搜索
     * @param message
     */
    @KafkaListener(id="${application.name.switch}"+ KafkaTopicConstant.ES_STATISTICAL_ORDER_TOPIC+"qingyunOrderSearchKafkaListenerContainerFactory",
            topics = "${application.name.switch}"+KafkaTopicConstant.ES_STATISTICAL_ORDER_TOPIC
            ,containerFactory="qingyunOrderSearchKafkaListenerContainerFactory")
    public void esTjOrderListener(String message) {
        KafkaMessageWrapper wrapper = JSON.parseObject(message, KafkaMessageWrapper.class);
        if(wrapper.getData()!=null) {
            String string = wrapper.getData().toString();
            if(wrapper.getOptionType().equals(KafkaMessageOperationTypeEnum.DELETE)){
                List<Long> orderIdList = JSON.parseArray(string,Long.class);
                esOrderService.deleteOrderStatus(orderIdList);
                return;
            }
            OrderSyncODTO orderKafkaVo = JSON.parseObject(string, OrderSyncODTO.class);
            if(orderKafkaVo==null || orderKafkaVo.getId()==null || orderKafkaVo.getStoreId()==null ){
                log.error("订单同步到ES，数据异常={}",orderKafkaVo);
                return;
            }
            if(orderKafkaVo.getOrderStatus()==0) {
                if (SpringUtil.isEmpty(orderKafkaVo.getItemList())) {
                    log.error("订单同步到ES，数据异常={}",orderKafkaVo);
                    return;
                }
            }
            switch (wrapper.getOptionType()) {
                case INSERT:
                    esOrderService.insertEsTjOrderIndex(orderKafkaVo);
                    break;
                case UPDATE:
                    esOrderService.updateEsTjOrderIndex(orderKafkaVo);
                    break;
                case CANCEL:
                    esOrderService.cancelEsTjOrderIndex(orderKafkaVo);
                    break;
                default:
                    log.error("不能处理该消息:{}", message);
                    break;
            }
        }
    }

    /**
     * 客户同步到搜索
     * @param message
     */
    @KafkaListener(id="${application.name.switch}"+ KafkaTopicConstant.ES_TJ_STORE_TOPIC+"qingyunOrderSearchKafkaListenerContainerFactory",
            topics = "${application.name.switch}"+KafkaTopicConstant.ES_TJ_STORE_TOPIC
            ,containerFactory="qingyunOrderSearchKafkaListenerContainerFactory")
    public void esTjStoreListener(String message) {
        KafkaMessageWrapper wrapper = JSON.parseObject(message, KafkaMessageWrapper.class);
        if(wrapper.getData()!=null) {
            List<EsStore>  storeList= JSON.parseArray(wrapper.getData().toString(), EsStore.class);
            esStoreService.updateEsStoreIndex(storeList);
        }
    }

    @Override
    public List<String> getKafkaIds() {
        return Arrays.asList(
                QYApplicationContext.applicationNameSwitch + KafkaTopicConstant.ES_STATISTICAL_ORDER_TOPIC+"qingyunOrderSearchKafkaListenerContainerFactory",
                QYApplicationContext.applicationNameSwitch + KafkaTopicConstant.ES_TJ_STORE_TOPIC+"qingyunOrderSearchKafkaListenerContainerFactory"
        );
    }
}

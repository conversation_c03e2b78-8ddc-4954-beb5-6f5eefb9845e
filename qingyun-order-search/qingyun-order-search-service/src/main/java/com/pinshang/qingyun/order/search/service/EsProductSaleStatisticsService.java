package com.pinshang.qingyun.order.search.service;

import com.pinshang.qingyun.box.utils.DateUtil;
import com.pinshang.qingyun.box.utils.SpringUtil;
import com.pinshang.qingyun.order.search.document.EsOrderItem;
import com.pinshang.qingyun.order.search.document.EsTjCommodity;
import com.pinshang.qingyun.order.search.dto.ProductSaleStatisticsSumEntry;
import com.pinshang.qingyun.order.search.dto.ProductSaleStatisticsTempEntry;
import com.pinshang.qingyun.order.search.repository.EsTjCommodityRepository;
import com.pinshang.qingyun.order.search.vo.ProductSaleStatisticsVo;
import lombok.extern.slf4j.Slf4j;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.search.aggregations.AggregationBuilders;
import org.elasticsearch.search.aggregations.bucket.terms.Terms;
import org.elasticsearch.search.aggregations.metrics.sum.InternalSum;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.elasticsearch.core.ElasticsearchTemplate;
import org.springframework.data.elasticsearch.core.aggregation.AggregatedPage;
import org.springframework.data.elasticsearch.core.query.NativeSearchQueryBuilder;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;

/**
 * 产品销售汇总
 */
@Service
@Slf4j
public class EsProductSaleStatisticsService {

    @Autowired
    private ElasticsearchTemplate elasticsearchTemplate;
    @Autowired
    private EsTjCommodityRepository esTjCommodityRepository;


    /**
     * 产品销售汇总
     * @param vo
     * @return
     */
    public List<ProductSaleStatisticsTempEntry> queryList(ProductSaleStatisticsVo vo) {
        List<ProductSaleStatisticsTempEntry> tempEntryList = this.queryEsOrderItemByParam(vo);
        if(SpringUtil.isEmpty(tempEntryList)){
            log.debug("产品销售汇总查询，搜索中没有满足条件的订单明细数据。");
            return Collections.EMPTY_LIST;
        }
        Collections.sort(tempEntryList);
        return tempEntryList;
    }

    /**
     * 查询合计金额
     * @param vo
     * @return
     */
    public ProductSaleStatisticsSumEntry querySum(ProductSaleStatisticsVo vo) {
        BoolQueryBuilder orderItemQuery = this.processQueryParam(vo);
        NativeSearchQueryBuilder nativeSearchQueryBuilder = new NativeSearchQueryBuilder()
                .withQuery(orderItemQuery).withFields("totalPriceCent")
                .addAggregation(AggregationBuilders.sum("sum_totalPrice").field("totalPriceCent"))
                ;
        AggregatedPage<EsOrderItem> orderItemPage = elasticsearchTemplate.queryForPage(nativeSearchQueryBuilder.build(),EsOrderItem.class);
        InternalSum sumPrice =  orderItemPage.getAggregations().get("sum_totalPrice");
        ProductSaleStatisticsSumEntry sumEntry = new ProductSaleStatisticsSumEntry();
        sumEntry.setTotalCommodityPrice(new BigDecimal(Double.toString(sumPrice.getValue()/100)));
        return sumEntry;
    }

    private List<ProductSaleStatisticsTempEntry> queryEsOrderItemByParam(ProductSaleStatisticsVo vo){
        BoolQueryBuilder orderItemQuery = this.processQueryParam(vo);
        Long commodityCount = esTjCommodityRepository.count();
        NativeSearchQueryBuilder nativeSearchQueryBuilder = new NativeSearchQueryBuilder()
                .withQuery(orderItemQuery).withFields("commodityId","storeTypeId","commodityNum","totalPriceCent")
                .addAggregation(
                        AggregationBuilders.terms("term_commodityId").field("commodityId").size(commodityCount.intValue())
                                .subAggregation(AggregationBuilders.terms("term_storeTypeId").field("storeTypeId").size(Integer.MAX_VALUE)
                                        .subAggregation(AggregationBuilders.sum("sum_commodityNum").field("commodityNum"))
                                        .subAggregation(AggregationBuilders.sum("sum_totalPrice").field("totalPriceCent"))
                                )
                )
                ;
        AggregatedPage<EsOrderItem> orderItemPage = elasticsearchTemplate.queryForPage(nativeSearchQueryBuilder.build(),EsOrderItem.class);
        List<ProductSaleStatisticsTempEntry> entryList = new ArrayList<>();
        Set<Long> commodityIdList = new HashSet<>();
        if(orderItemPage.hasAggregations()){
            Terms commodityIdTerms = (Terms) orderItemPage.getAggregation("term_commodityId");
            Iterator<Terms.Bucket> iterator = (Iterator<Terms.Bucket>) commodityIdTerms.getBuckets().iterator();
            while (iterator.hasNext()) {
                Terms.Bucket bucket = iterator.next();
                commodityIdList.add(Long.valueOf(bucket.getKeyAsString()));
                Terms storeTerms = (Terms) bucket.getAggregations().asMap().get("term_storeTypeId");
                Iterator<Terms.Bucket> subIterator = (Iterator<Terms.Bucket>) storeTerms.getBuckets().iterator();
                while (subIterator.hasNext()) {
                    Terms.Bucket subBucket = subIterator.next();
                    InternalSum sumNum = subBucket.getAggregations().get("sum_commodityNum");
                    InternalSum sumPrice = subBucket.getAggregations().get("sum_totalPrice");

                    ProductSaleStatisticsTempEntry entry = new ProductSaleStatisticsTempEntry();
                    entry.setCommodityId(Long.valueOf(bucket.getKeyAsString()));
                    entry.setStoreTypeId(Long.valueOf(subBucket.getKeyAsString()));
                    entry.setCommodityNum(new BigDecimal(Double.toString(sumNum.getValue())));
                    entry.setTotalPrice(new BigDecimal(Double.toString(sumPrice.getValue()/100)));
                    entryList.add(entry);
                }
            }
        }
        this.processCommodityInfo(entryList,commodityIdList);
        return entryList;
    }

    private BoolQueryBuilder processQueryParam(ProductSaleStatisticsVo vo){
        BoolQueryBuilder boolQuery = QueryBuilders.boolQuery();
        if(vo.getStartDate()!=null && vo.getEndDate()!=null){
            QueryBuilder query1 = QueryBuilders.rangeQuery("orderTime").gte(DateUtil.getDateFormate(vo.getStartDate(),"yyyy-MM-dd"));
            QueryBuilder query2 = QueryBuilders.rangeQuery("orderTime").lte(DateUtil.getDateFormate(vo.getEndDate(),"yyyy-MM-dd"));
            boolQuery.must(query1).must(query2);
        }
        if(vo.getStoreId()!=null){
            boolQuery.must(QueryBuilders.termQuery("storeId",vo.getStoreId()));
        }
        if(SpringUtil.isNotEmpty(vo.getStoreIds())){
            boolQuery.must(QueryBuilders.termsQuery("storeId",vo.getStoreIds()));
        }
        if(vo.getStoreSettId()!=null){
            boolQuery.must(QueryBuilders.termQuery("storeSettId",vo.getStoreSettId()));
        }
        if(SpringUtil.isNotEmpty(vo.getStoreSettIds())){
            boolQuery.must(QueryBuilders.termsQuery("storeSettId",vo.getStoreSettIds()));
        }
        if(vo.getStoreCompanyId()!=null){
            boolQuery.must(QueryBuilders.termQuery("storeCompanyId",vo.getStoreCompanyId()));
        }
        if(vo.getStoreTypeId()!=null){
            boolQuery.must(QueryBuilders.termQuery("storeTypeId",vo.getStoreTypeId()));
        }
        if(vo.getSupervisorId()!=null){
            boolQuery.must(QueryBuilders.termQuery("supervisorId",vo.getSupervisorId()));
        }
        if(vo.getRegionManagerId()!=null){
            boolQuery.must(QueryBuilders.termQuery("regionManagerId",vo.getRegionManagerId()));
        }
        if(vo.getLineGroupId()!=null){
            boolQuery.must(QueryBuilders.termQuery("storeLineGroupId",vo.getLineGroupId()));
        }
        if(vo.getDeliveryManId()!=null){
            boolQuery.must(QueryBuilders.termQuery("deliverymanId",vo.getDeliveryManId()));
        }
        boolQuery.must(QueryBuilders.termQuery("orderStatus",0));

        if(vo.getCommodityId()!=null){
            boolQuery.must(QueryBuilders.termQuery("commodityId",vo.getCommodityId()));
        }
        if(SpringUtil.isNotEmpty(vo.getCommodityIds())){
            boolQuery.must(QueryBuilders.termsQuery("commodityId",vo.getCommodityIds()));
        }
        if(vo.getFactoryId()!=null){
            boolQuery.must(QueryBuilders.termQuery("commodityFactoryId",vo.getFactoryId()));
        }
        if(vo.getWorkshopId()!=null){
            boolQuery.must(QueryBuilders.termQuery("commodityWorkshopId",vo.getWorkshopId()));
        }
        if(vo.getCateId1()!=null){
            boolQuery.must(QueryBuilders.termQuery("commodityFirstKindId",vo.getCateId1()));
        }
        if(vo.getCateId2()!=null){
            boolQuery.must(QueryBuilders.termQuery("commoditySecondKindId",vo.getCateId2()));
        }
        if(vo.getCateId3()!=null){
            boolQuery.must(QueryBuilders.termQuery("commodityThirdKindId",vo.getCateId3()));
        }
        if(vo.getCompanyId()!=null){
            boolQuery.must(QueryBuilders.termQuery("companyId",vo.getCompanyId()));
        }
        return boolQuery;
    }

    private List<ProductSaleStatisticsTempEntry> processCommodityInfo(List<ProductSaleStatisticsTempEntry> entryList,Set<Long> commodityIdList){
        if(SpringUtil.isEmpty(entryList)){
            return Collections.EMPTY_LIST;
        }
        Iterable<EsTjCommodity> esTjCommodityIterable = esTjCommodityRepository.findAllById(commodityIdList);
        Map<Long,EsTjCommodity> commodityMap = new HashMap<>();
        esTjCommodityIterable.forEach(item->{
            commodityMap.put(item.getId(),item);
        });
        entryList.forEach(entry->{
            if(commodityMap.get(entry.getCommodityId())!=null){
                EsTjCommodity esTjCommodity = commodityMap.get(entry.getCommodityId());
                entry.setCommodityCode(esTjCommodity.getCommodityCode());
                entry.setCommodityName(esTjCommodity.getCommodityName()+"("+esTjCommodity.getCommoditySpec()+")");
                entry.setFactoryId(esTjCommodity.getCommodityFactoryId());
                entry.setFactoryCode(esTjCommodity.getCommodityFactoryCode());
                entry.setFactoryName(esTjCommodity.getCommodityFactoryName());
                entry.setWorkshopName(esTjCommodity.getCommodityWorkshopName());
            }
        });
        return entryList;
    }

}

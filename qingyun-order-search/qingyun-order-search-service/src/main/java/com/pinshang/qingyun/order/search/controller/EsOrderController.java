package com.pinshang.qingyun.order.search.controller;

import com.github.pagehelper.PageInfo;
import com.pinshang.qingyun.base.configure.expand.QYAssert;
import com.pinshang.qingyun.box.utils.SpringUtil;
import com.pinshang.qingyun.order.dto.orderStatistics.OrderStatisticsMonitorIDTO;
import com.pinshang.qingyun.order.dto.orderStatistics.OrderSyncODTO;
import com.pinshang.qingyun.order.search.document.EsStore;
import com.pinshang.qingyun.order.search.dto.EsOrderMonitorDTO;
import com.pinshang.qingyun.order.search.service.EsOrderService;
import com.pinshang.qingyun.order.search.service.EsSettingService;
import com.pinshang.qingyun.order.search.service.EsStoreService;
import com.pinshang.qingyun.order.search.vo.EsOrderMonitorVo;
import com.pinshang.qingyun.order.service.OrderStatisticsMonitorClient;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;


@RestController
@RequestMapping("/esOrder")
@Slf4j
public class EsOrderController {
    @Autowired
    private EsOrderService esOrderService;
    @Autowired
    private EsStoreService esStoreService;
    @Autowired
    private OrderStatisticsMonitorClient orderStatisticsMonitorClient;
    @Autowired
    private EsSettingService esSettingService;


    /**
     * 订单比较：查询订单统计信息
     * @param vo
     * @return
     */
    @RequestMapping(value = "/queryEsOrderStatisticsInfo", method = RequestMethod.POST)
    public EsOrderMonitorDTO queryEsOrderStatisticsInfo(@RequestBody EsOrderMonitorVo vo) {
        return esOrderService.queryEsOrderStatisticsInfoV2(vo);
    }

    /**
     * 差异订单：查询订单编号
     * @param vo
     * @return
     */
    @RequestMapping(value = "/queryEsOrderDiffList", method = RequestMethod.POST)
    public List<String> queryEsOrderDiffList(@RequestBody EsOrderMonitorVo vo) {
        return esOrderService.queryEsOrderDiffListV2(vo);
    }

    /**
     * 订单同步
     * @param vo
     * @return
     */
    @RequestMapping(value = "/syncEsOrder", method = RequestMethod.POST)
    public Integer syncEsOrder(@RequestBody EsOrderMonitorVo vo) {
        QYAssert.notNull(vo,"参数不能为空");
        OrderStatisticsMonitorIDTO idto = new OrderStatisticsMonitorIDTO();
        SpringUtil.copyProperties(vo,idto);
        int pageNo = 1;
        idto.setPageNo(pageNo);
        idto.setPageSize(500);
        int retry = 0;
        PageInfo<OrderSyncODTO> orderResult = null;
        esSettingService.esRefreshSwitch(false);
        Map<Long,EsStore> esStoreMap = new HashMap<>();
        while(pageNo == 1 || (orderResult != null && orderResult.isHasNextPage())) {
            try {
                orderResult = orderStatisticsMonitorClient.queryOrderSyncList(idto);
            }catch (Exception e){
                log.error("订单补偿到ES系统，调用qingyun-order-service服务异常：",e);
            }
            if (orderResult != null && SpringUtil.isNotEmpty(orderResult.getList())) {
                List<Long> newStoreIdList = orderResult.getList().stream().map(OrderSyncODTO::getStoreId).distinct()
                        .filter(storeId->!esStoreMap.keySet().contains(storeId)).collect(Collectors.toList());
                if(SpringUtil.isNotEmpty(newStoreIdList)){
                    List<EsStore> storeList = esStoreService.processStoreInfoByIdList(newStoreIdList);
                    storeList.forEach(item->{
                        esStoreMap.put(item.getId(),item);
                    });
                }
                esOrderService.syncEsOrder(orderResult.getList(),esStoreMap);
            }else {
                if (retry++ > 6) {
                    break;
                }
            }
            idto.setPageNo(++pageNo);
        }
        esSettingService.esRefreshSwitch(true);
        return 1;
    }


}

package com.pinshang.qingyun.order.search.dto;

import lombok.Data;
import org.apache.commons.lang.StringUtils;

import java.text.Collator;
import java.util.Comparator;
import java.util.Locale;


@Data
public class EsStatisticsBaseEntry implements Comparable<EsStatisticsBaseEntry>  {
    //商品
    private Long commodityId;
    private String commodityCode;
    private String commodityName;

    //工厂
    private Long factoryId;
    private String factoryCode;
    private String factoryName;
    //生产组
    private Long workshopId;
    private String workshopName;
    private String flowshopName;
    private String unit;


    @Override
    public int compareTo(EsStatisticsBaseEntry tempEntry) {
        Comparator<EsStatisticsBaseEntry> comparator = new Comparator<EsStatisticsBaseEntry>() {
            @Override
            public int compare(EsStatisticsBaseEntry o1, EsStatisticsBaseEntry o2) {
                int c ;
                if(o1.getFactoryCode()==null){
                    if(o2.getFactoryCode()==null){
                        c = 0;
                    }else{
                        return -1;
                    }
                }else{
                    if(o2.getFactoryCode()==null){
                        return  1;
                    }else{
                        c = o1.getFactoryCode().compareTo(o2.getFactoryCode());
                    }
                }
                if (c == 0) {
                    if (StringUtils.isEmpty(o1.getWorkshopName())) {
                        if (StringUtils.isEmpty(o2.getWorkshopName())) {
                            c = 0;
                        } else {
                            return -1;
                        }
                    } else {
                        if (StringUtils.isEmpty(o2.getWorkshopName())) {
                            return 1;
                        } else {
                            Collator instance = Collator.getInstance(Locale.CHINA);
                            c = instance.compare(o1.getWorkshopName(), o2.getWorkshopName());
                        }
                    }
                }
                if(c == 0){
                    if(StringUtils.isEmpty(o1.getCommodityCode())){
                        if(StringUtils.isEmpty(o2.getCommodityCode())){
                            c = 0;
                        }else{
                            return -1;
                        }
                    }else{
                        if(StringUtils.isEmpty(o2.getCommodityCode())){
                            return  1;
                        }else{
                            c = o1.getCommodityCode().compareTo(o2.getCommodityCode());
                        }
                    }
                }
                return c;
            }
        };
        return comparator.compare(this,tempEntry);
    }
}

package com.pinshang.qingyun.order.search.service;

import com.pinshang.qingyun.base.api.ApiResult;
import com.pinshang.qingyun.base.constant.QingyunConstant;
import com.pinshang.qingyun.box.utils.SpringUtil;
import com.pinshang.qingyun.order.search.document.EsStore;
import com.pinshang.qingyun.order.search.document.EsTjCommodity;
import com.pinshang.qingyun.order.search.repository.EsStoreRepository;
import com.pinshang.qingyun.product.dto.sync.SelectSyncInfoByCodeListIDTO;
import com.pinshang.qingyun.product.dto.sync.StoreInfoODTO;
import com.pinshang.qingyun.product.service.sync.SyncStoreInfoClient;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.index.query.RangeQueryBuilder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 客户同步到搜索
 */
@Service
@Slf4j
public class EsStoreService {

    @Autowired
    private EsStoreRepository esStoreRepository;
    @Autowired
    private SyncStoreInfoClient syncStoreInfoClient;

    public ApiResult updateEsStoreIndex(List<EsStore>  esStoreList){
        ApiResult apiResult = new ApiResult().initSuccess();
        if (CollectionUtils.isNotEmpty(esStoreList)){
            List<Long> storeId = esStoreList.stream().map(esStore -> esStore.getId()).collect(Collectors.toList());
            Iterable<EsStore> esStores = esStoreRepository.findAllById(storeId);
            if (esStores != null){
                //将数据按id存入map中
                Map<Long, EsStore> map = new HashMap<>();
                esStores.forEach(esStore -> map.put(esStore.getId(),esStore));

                //把需要同步到es的数据筛选到syncEsStores
                List<EsStore> syncEsStores = new ArrayList<>();
                esStoreList.forEach(requestStore->{
                    EsStore esStore = map.get(requestStore.getId());
                    if (esStore == null || !esStore.equals(requestStore)){
                        syncEsStores.add(requestStore);
                    }
                });

                if(CollectionUtils.isNotEmpty(syncEsStores)) {
                    esStoreRepository.saveAll(syncEsStores);
                }
            }else {
                esStoreRepository.saveAll(esStoreList);
            }
        }
        return apiResult;
    }

    /**
     * 查询es客户数量
     * @return
     */
    public Long selectEsStoreCount(){
        return esStoreRepository.count();
    }

    /**
     * 查询es客户所有id
     * @return
     */
    public List<Long> queryEsStoreDiffList() {
        //总条数
        int total = (int) esStoreRepository.count();
        if (total == 0){
            return Collections.EMPTY_LIST;
        }
        List<Long> storeIds = new ArrayList<>(total);
        //分页查询
        int pageSize = total% QingyunConstant.ES_DEFAULT_PAGE_SIZE == 0 ? total/QingyunConstant.ES_DEFAULT_PAGE_SIZE : total/QingyunConstant.ES_DEFAULT_PAGE_SIZE+1;
        Long minId = 0L;
        /**
         * 根据id 升序，每次查询大于最后一条id 数据
         */
        Sort sort = new Sort(Sort.Direction.ASC, "id");
        for ( int i = 0; i < pageSize; i++) {
            //条件
            RangeQueryBuilder queryBuilder = QueryBuilders.rangeQuery("id").gt(minId);
            //分页
            Pageable pageable = PageRequest.of(0, QingyunConstant.ES_DEFAULT_PAGE_SIZE, sort);
            Iterable<EsStore> stores = esStoreRepository.search(queryBuilder, pageable);
            Iterator<EsStore> storeIterator = stores.iterator();
            //组装id
            while (storeIterator.hasNext()) {
                storeIds.add(storeIterator.next().getId());
            }
            //已查询出数据的最后一条的id值
            minId = storeIds.get(storeIds.size() - 1);
        }

        return storeIds;
    }

    /**
     * 订单冗余客户信息，针对缺失客户重新同步
     * @param storeIdList
     * @return
     */
    public List<EsStore> processStoreInfoByIdList(List<Long> storeIdList){
        if(SpringUtil.isEmpty(storeIdList)){
            return Collections.EMPTY_LIST;
        }
        List<EsStore> esStoreList = new ArrayList<>(storeIdList.size());
        Set<Long> exsitIds = new HashSet<>();
        Iterable<EsStore> esStoreIterable = esStoreRepository.findAllById(storeIdList);
        if(esStoreIterable!=null){
            esStoreIterable.forEach(item->{
                exsitIds.add(item.getId());
                esStoreList.add(item);
            });
        }
        if(SpringUtil.isEmpty(exsitIds)){
            esStoreList.addAll(this.syncEsStore(storeIdList));
        }else{
            List<Long> lackIds = storeIdList.stream().filter(id->!exsitIds.contains(id)).collect(Collectors.toList());
            if(SpringUtil.isNotEmpty(lackIds)){
                esStoreList.addAll(this.syncEsStore(lackIds));
            }
        }
        return esStoreList;
    }

    public List<EsStore> syncEsStore(List<Long> storeIdList) {
        if(SpringUtil.isEmpty(storeIdList)){
            return Collections.EMPTY_LIST;
        }
       try{
           SelectSyncInfoByCodeListIDTO idto = new SelectSyncInfoByCodeListIDTO();
           idto.setIdList(storeIdList);
           StoreInfoODTO storeInfoODTO = syncStoreInfoClient.selectStoreInfo(idto);
           if(storeInfoODTO!=null && SpringUtil.isNotEmpty(storeInfoODTO.getStoreList())){
               List<EsStore> esStoreList = storeInfoODTO.getStoreList().stream().map(EsStore::convert).collect(Collectors.toList());
               esStoreRepository.saveAll(esStoreList);
               return esStoreList;
           }
       } catch (Exception e){
           log.error("订单同步到ES，同步缺失的客户信息调用product服务异常：客户ID={}",storeIdList,e);
       }
        return Collections.EMPTY_LIST;
    }
}

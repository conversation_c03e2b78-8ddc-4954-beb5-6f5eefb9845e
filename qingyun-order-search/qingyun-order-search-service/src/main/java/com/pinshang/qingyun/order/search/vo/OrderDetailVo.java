package com.pinshang.qingyun.order.search.vo;

import com.pinshang.qingyun.base.page.Pagination;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;
import java.util.List;

/**
 * 订单明细查询请求参数
 */
@Data
public class OrderDetailVo extends Pagination {

    /**
     * 结账客户Id
     */
    private Long settlementId;
    /**
     *客户编码
     */
    private String storeCode;
    /**
     *起始时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date startOrderDate;
    /**
     *截止日期
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date endOrderDate;
    /**
     *商品品类
     */
    private Long categoryId;
    /**
     *客户类型
     */
    private Long storeTypeId;
    /**
     *商品id
     */
    private Long commodityId;
    /**
     *操作人
     */
    private Long operatorId;

    private List<Long> orderIds;
    private Long companyId;
    /***
     * 订单编码
     */
    private String orderCode;

}

package com.pinshang.qingyun.order.search.service;

import com.pinshang.qingyun.order.search.conf.EsHttpConfig;
import com.pinshang.qingyun.order.search.repository.EsOrderItemRepository;
import com.pinshang.qingyun.order.search.repository.EsOrderRepository;
import com.pinshang.qingyun.order.search.util.HttpClientUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;

@Service
@Slf4j
public class EsSettingService {
    @Autowired
    private EsHttpConfig esHttpConfig;
    @Autowired
    private EsOrderRepository esOrderRepository;
    @Autowired
    private EsOrderItemRepository esOrderItemRepository;

    /**
     * 修改es刷新时间
     * @param flag
     */
    public void esRefreshSwitch(Boolean flag) {
        String esServerUrl = "http://" + esHttpConfig.getEsNodeUrl().split(":")[0] + ":9200/tj-order*";
        String updateUrl = esServerUrl + "/_settings?";
        Map<String, Object> map = new HashMap<>();
        map.put("refresh_interval", flag ? "10s" : -1);
        try {
            HttpClientUtil.httpPut(updateUrl, map);
            if(flag){
//                updateUrl = esServerUrl + "/_refresh";
//                HttpClientUtil.httpPostJson(updateUrl, null);
                esOrderRepository.refresh();
                esOrderItemRepository.refresh();
            }
        } catch (Exception e) {
            log.error("手动补偿订单同步，修改es时间间隔异常：",e);
        }
    }

}

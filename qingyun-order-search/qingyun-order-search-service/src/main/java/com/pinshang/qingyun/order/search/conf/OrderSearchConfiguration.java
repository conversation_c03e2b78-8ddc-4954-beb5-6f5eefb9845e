package com.pinshang.qingyun.order.search.conf;

import com.pinshang.qingyun.base.interceptor.QYServiceInterceptor;
import com.pinshang.qingyun.base.interceptor.XdServiceInterceptor;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.handler.HandlerInterceptorAdapter;

/**
 * <AUTHOR> weican on 2018-11-14.
 */
@Configuration
public class OrderSearchConfiguration {
    @Bean("qyServiceInterceptor")
    public HandlerInterceptorAdapter qyServiceInterceptor(){
        return new QYServiceInterceptor();
    }

}

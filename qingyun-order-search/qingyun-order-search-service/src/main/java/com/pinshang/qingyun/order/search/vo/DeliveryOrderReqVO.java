package com.pinshang.qingyun.order.search.vo;

import com.pinshang.qingyun.base.page.Pagination;
import lombok.Data;

import java.util.Date;

/**
 * 送货单
 * <AUTHOR>
 * @Date 2019/3/5 16:24
 */
@Data
public class DeliveryOrderReqVO extends Pagination {
    /**
     * 客户编码
     */
    private String storeCode;
    /**
     * 客户名称
     */
//    private String storeName;
    /**
     * 客户类型
     */
    private Long storeTypeId;
    /**
     * 结账客户Id
     */
    private Long settlementCustomerId;
    /**
     * 结账客户Code
     */
    private String settlementCustomerCode;
    /**
     * 送货员
     */
    private Long deliveryManId;
    /**
     * 线路组
     */
    private Long lineGroupId;
    /**
     * 送货起始日期
     */
    private Date startOrderDate;
    /**
     * 送货截止日期
     */
    private Date endOrderDate;
    /**
     * 渠道
     */
    private Long storeChannelId;
    /**
     * 订单编码
     */
    private String orderCode;
    /**
     * 督导
     */
    private Long supervisorId;
    /**
     * 操作员
     */
    private Long operatorId;
    /**
     * 配送批次
     */
    private Integer deliveryBatch;
    /**
     * 排序规则
     */
    private Integer sortRule;

    /**
     * 查询标识
     */
    private transient Boolean latestFlag;
}

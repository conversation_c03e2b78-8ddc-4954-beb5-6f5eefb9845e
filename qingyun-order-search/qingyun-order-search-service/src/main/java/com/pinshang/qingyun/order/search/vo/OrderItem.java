package com.pinshang.qingyun.order.search.vo;

import lombok.Data;

import javax.persistence.Id;
import java.math.BigDecimal;

@Data
public class OrderItem {

    @Id
    private Long id;

    /** 订单id **/
    private Long orderId;
    /** 商品id **/
    private Long commodityId;
    /** 商品数量 **/
    private BigDecimal commodityNum;
    /** 单价 **/
    private BigDecimal commodityPrice;
    /**
     * 促销前的源始金额
     */
    private BigDecimal totalPrice;
    /** 类型 **/
    private Integer type;
    /** 备注 **/
    private String remark;


}

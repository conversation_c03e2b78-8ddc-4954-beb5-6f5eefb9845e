package com.pinshang.qingyun.order.search.dto;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.pinshang.qingyun.base.configure.codec.DecimalSerializerKeep2;
import com.pinshang.qingyun.box.utils.SpringUtil;
import com.pinshang.qingyun.order.search.document.EsOrderItem;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang.StringUtils;

import java.math.BigDecimal;
import java.text.Collator;
import java.util.Comparator;
import java.util.Locale;

/**
 * 产品销售汇总
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ProductSaleStatisticsTempEntry implements Comparable<ProductSaleStatisticsTempEntry> {
    //商品ID
    private Long commodityId;
    //客户类型ID
    private Long storeTypeId;

    //工厂
    private Long factoryId;//排序使用
    private String factoryCode;
    private String factoryName;

    //生产组
    private String workshopName;

    //商品编码
    private String commodityCode;

    //商品名称
    private String commodityName;

    //数量
    private BigDecimal commodityNum;

    //金额
    @JsonSerialize(using = DecimalSerializerKeep2.class)
    private BigDecimal totalPrice;

    @Override
    public int compareTo(ProductSaleStatisticsTempEntry tempEntry) {
        Comparator<ProductSaleStatisticsTempEntry> comparator = new Comparator<ProductSaleStatisticsTempEntry>() {
            @Override
            public int compare(ProductSaleStatisticsTempEntry o1, ProductSaleStatisticsTempEntry o2) {
                int c ;
                if(o1.getFactoryId()==null){
                    if(o2.getFactoryId()==null){
                        c = 0;
                    }else{
                        return -1;
                    }
                }else{
                    if(o2.getFactoryId()==null){
                        return  1;
                    }else{
                        c = o1.getFactoryId().compareTo(o2.getFactoryId());
                    }
                }
                if (c == 0) {
                    if (StringUtils.isEmpty(o1.getWorkshopName())) {
                        if (StringUtils.isEmpty(o2.getWorkshopName())) {
                            c = 0;
                        } else {
                            return -1;
                        }
                    } else {
                        if (StringUtils.isEmpty(o2.getWorkshopName())) {
                            return 1;
                        } else {
                            Collator instance = Collator.getInstance(Locale.CHINA);
                            c = instance.compare(o1.getWorkshopName(), o2.getWorkshopName());
                        }
                    }
                }
                if(c == 0){
                    if(StringUtils.isEmpty(o1.getCommodityCode())){
                        if(StringUtils.isEmpty(o2.getCommodityCode())){
                            c = 0;
                        }else{
                            return -1;
                        }
                    }else{
                        if(StringUtils.isEmpty(o2.getCommodityCode())){
                            return  1;
                        }else{
                            c = o1.getCommodityCode().compareTo(o2.getCommodityCode());
                        }
                    }
                }
                return c;
            }
        };
        return comparator.compare(this,tempEntry);
    }


}

package com.pinshang.qingyun.order.search.document;

import com.pinshang.qingyun.box.utils.SpringUtil;
import com.pinshang.qingyun.product.dto.sync.CommodityInfoODTO;
import com.pinshang.qingyun.product.dto.sync.CommodityODTO;
import com.pinshang.qingyun.product.dto.sync.StoreODTO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.data.annotation.Id;
import org.springframework.data.elasticsearch.annotations.Document;
import org.springframework.data.elasticsearch.annotations.Field;
import org.springframework.data.elasticsearch.annotations.FieldType;

/**
 * 统计查询商品索引
 */
@Data
@Document(indexName = "tj-commodity", type = "tj-commodity",replicas = 2)
@NoArgsConstructor
@AllArgsConstructor
public class EsTjCommodity {
    /** 1. 商品ID 主键　**/
    @Id
    private Long id;
    /**
     * 商品编码
     */
    @Field(type = FieldType.keyword)
    private String commodityCode;
    /**
     * 商品名称
     */
    @Field(type = FieldType.text)
    private String commodityName;
    /**
     * 商品规格
     */
    @Field(type = FieldType.text)
    private String commoditySpec;
    /**
     * 计量单位
     */
    @Field(type = FieldType.text)
    private String commodityUnitName;
    /** 包装类型：整装/散装 **/
    @Field(type = FieldType.text)
    private String commodityPackageName;
    /** 包装规格**/
    @Field(type = FieldType.Double)
    private Double commodityPackageSpec;
    /** 7. 是否称重：0-否、1-是 **/
    @Field(type = FieldType.Integer)
    private Integer isWeight;
    /**
     * 一级分类
     */
    @Field(type = FieldType.Long)
    private Long commodityFirstKindId;
    @Field(type = FieldType.text)
    private String commodityFirstKindName;
    /**
     * 二级分类
     */
    @Field(type = FieldType.Long)
    private Long commoditySecondKindId;
    @Field(type = FieldType.text)
    private String commoditySecondKindName;
    /**
     * 三级分类
     */
    @Field(type = FieldType.Long)
    private Long commodityThirdKindId;
    @Field(type = FieldType.text)
    private String commodityThirdKindName;
    /**
     * 工厂
     */
    @Field(type = FieldType.Long)
    private Long commodityFactoryId;
    @Field(type = FieldType.text)
    private String commodityFactoryName;
    @Field(type = FieldType.keyword)
    private String commodityFactoryCode;
    /**
     * 生产组
     */
    @Field(type = FieldType.Long)
    private Long commodityWorkshopId;
    @Field(type = FieldType.text)
    private String commodityWorkshopName;
    @Field(type = FieldType.keyword)
    private String commodityWorkshopCode;
    @Field(type = FieldType.text)
    private String commodityWorkshopDirector;
    /**
     * 车间
     */
    @Field(type = FieldType.Long)
    private Long commodityFlowshopId;
    @Field(type = FieldType.text)
    private String commodityFlowshopName;
    /**
     * 是否框汇总(指定商品送货到指定客户时才切隔分拣，主要是用于排序，此商品靠后排序，无其它用途)
     */
    @Field(type = FieldType.Integer)
    private Integer isSummary;
    /**
     * 主条码
     */
    @Field(type = FieldType.text)
    private String barCode;
    /**
     * 所有条码，逗号分隔
     */
    @Field(type = FieldType.text)
    private String barCodeList;

    public static EsTjCommodity convert(CommodityODTO dto) {
        EsTjCommodity esCommodity = new EsTjCommodity();
        SpringUtil.copyProperties(dto,esCommodity);
        return esCommodity;
    }
}

package com.pinshang.qingyun.order.search.document;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.data.elasticsearch.annotations.DateFormat;
import org.springframework.data.elasticsearch.annotations.Document;
import org.springframework.data.elasticsearch.annotations.Field;
import org.springframework.data.elasticsearch.annotations.FieldType;

import javax.persistence.Id;
import java.util.Date;


/**
 * 订单基本信息，未冗余其他信息。与统计库t_tj_order表信息一致
 */
@Data
@Document(indexName = "tj-order-base", type = "tj-order-base",replicas = 2)
@NoArgsConstructor
@AllArgsConstructor
public class EsOrderBase {
    @Id
    private Long id;
    @Field(type = FieldType.keyword)
    private String orderCode;
    @Field(type = FieldType.Long)
    private Long storeId;
    /**
     * 送货日期
     */
    @Field(type = FieldType.Date, format = DateFormat.custom,
            pattern = "yyyy-MM-dd HH:mm:ss || yyyy-MM-dd || yyyy/MM/dd HH:mm:ss|| yyyy/MM/dd ||epoch_millis"
    )
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date orderTime;
    /**
     * 订单类型
     */
    @Field(type = FieldType.Integer)
    private Integer orderType;
    /**
     * 订单来源
     */
    @Field(type = FieldType.Integer,index = false)
    private Integer modeType;
    /**
     * 打印份数
     */
    @Field(type = FieldType.Integer)
    private Integer printNum;
    /**
     * 打印类型(1：本地,2：送货员,3：不打印)
     */
    @Field(type = FieldType.Integer)
    private Integer printType;
    /**
     * 应付金额（参与促销活动之前的源始金额）
     */
    @Field(type = FieldType.Double)
    private Double totalAmount;
    /**
     * 订单金额
     */
    @Field(type = FieldType.Double)
    private Double orderAmount;
    /**
     * 最终金额
     */
    @Field(type = FieldType.Double)
    private Double finalAmount;
    /**
     * 订单备注
     */
    @Field(type = FieldType.keyword)
    private String orderRemark;
    /**
     * 订单状态(0正常,1删除,2取消)
     */
    @Field(type = FieldType.Integer)
    private Integer orderStatus;
    /**
     *  0:正常   1：未结算
     */
    @Field(type = FieldType.Integer)
    private Integer settleStatus;
    /**
     * 配送批次
     */
    @Field(type = FieldType.Integer)
    private Integer deliveryBatch;
    /**
     * 创建者
     */
    @Field(type = FieldType.Long)
    private Long createId;
    @Field(type = FieldType.keyword,index = false)
    private String createName;
    /**
     * 创建时间
     */
    @Field(type = FieldType.Date, format = DateFormat.custom,
            pattern ="yyyy-MM-dd HH:mm:ss || yyyy-MM-dd || yyyy/MM/dd HH:mm:ss|| yyyy/MM/dd ||epoch_millis"
    )
    @JsonFormat(shape =JsonFormat.Shape.STRING,pattern ="yyyy-MM-dd HH:mm:ss",timezone ="GMT+8")
    private Date createTime;
    /**
     * 更新者
     */
    @Field(type = FieldType.Long)
    private Long updateId;
    @Field(type = FieldType.keyword,index = false)
    private String updateName;
    /**
     * 修改时间
     */
    @Field(type = FieldType.Date, format = DateFormat.custom,
            pattern = "yyyy-MM-dd HH:mm:ss || yyyy-MM-dd || yyyy/MM/dd HH:mm:ss|| yyyy/MM/dd ||epoch_millis"
    )
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateTime;

    /***
     * 公司
     */
    @Field(type = FieldType.Long)
    private Long companyId;

}

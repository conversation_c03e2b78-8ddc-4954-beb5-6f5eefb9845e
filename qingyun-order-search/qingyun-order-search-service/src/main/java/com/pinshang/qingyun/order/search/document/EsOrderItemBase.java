package com.pinshang.qingyun.order.search.document;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.data.elasticsearch.annotations.Document;
import org.springframework.data.elasticsearch.annotations.Field;
import org.springframework.data.elasticsearch.annotations.FieldType;

import javax.persistence.Id;
import javax.persistence.Transient;
import java.math.BigDecimal;

/**
 * 订单明细表信息基类，未冗余其他信息，与统计库t_tj_order_list表数据一致
 */
@Data
@Document(indexName = "tj-order-item-base", type = "tj-order-item-base",replicas = 2)
@NoArgsConstructor
@AllArgsConstructor
public class EsOrderItemBase {

    @Id
    private Long id;

    @Field(type = FieldType.Long)
    private Long orderId;

    @Field(type = FieldType.Long)
    private Long commodityId;

    /** 商品数量 **/
    @Field(type = FieldType.Double)
    private Double commodityNum;

    /** 单价 **/
    @Field(type = FieldType.Double)
    private Double commodityPrice;

    /**
     * 促销前的源始金额
     */
    @Field(type = FieldType.Long)
    private Long totalPriceCent;
    @Transient
    private BigDecimal totalPrice;

    /** 类型 **/
    @Field(type = FieldType.Integer)
    private Integer type;

    /** 订单明细备注 **/
    @Field(type = FieldType.text)
    private String remark;

    /***
     * 公司
     */
    @Field(type = FieldType.Long)
    private Long companyId;


}

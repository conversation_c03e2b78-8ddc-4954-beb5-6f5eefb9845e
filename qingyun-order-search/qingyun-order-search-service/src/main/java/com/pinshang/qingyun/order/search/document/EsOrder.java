package com.pinshang.qingyun.order.search.document;

import com.pinshang.qingyun.box.utils.SpringUtil;
import com.pinshang.qingyun.order.dto.orderStatistics.OrderSyncODTO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.data.elasticsearch.annotations.*;

import javax.persistence.Transient;

/**
 * 订单主表信息+下单客户信息
 */
@Data
@Document(indexName = "tj-order", type = "tj-order",replicas = 2)
@NoArgsConstructor
@AllArgsConstructor
public class EsOrder extends EsOrderBase{

    //冗余客户信息
    @Field(type = FieldType.keyword)
    private String storeCode;
    @Field(type = FieldType.keyword)
    private String storeName;
    @Field(type = FieldType.keyword)
    private String storeShortName;
    //客户类型
    @Field(type = FieldType.Long)
    private Long storeTypeId;
    @Field(type = FieldType.keyword)
    private String storeTypeName;
    //送货员
    @Field(type = FieldType.Long)
    private Long deliverymanId;
    @Field(type = FieldType.keyword,index = false)
    private String deliverymanName;
    //销售员
    @Field(type = FieldType.Long)
    private Long salesmanId;
    @Field(type = FieldType.keyword,index = false)
    private String salesmanName;
    //督导
    @Field(type = FieldType.Long)
    private Long supervisorId;
    @Field(type = FieldType.keyword,index = false)
    private String supervisorName;
    //大区经理
    @Field(type = FieldType.Long)
    private Long regionManagerId;
    @Field(type = FieldType.keyword,index = false)
    private String regionManagerName;
    //主任
    @Field(type = FieldType.Long)
    private Long officeDirectorId;
    @Field(type = FieldType.keyword,index = false)
    private String officeDirectorName;
    //班组长
    @Field(type = FieldType.Long)
    private Long teamLeaderId;
    @Field(type = FieldType.keyword,index = false)
    private String teamLeaderName;
    /**
     * 线路
     */
    @Field(type = FieldType.Long)
    private Long storeLineId;
    @Field(type = FieldType.keyword,index = false)
    private String storeLineCode;
    @Field(type = FieldType.keyword,index = false)
    private String storeLineName;
    /**
     * 线路组
     */
    @Field(type = FieldType.Long)
    private Long storeLineGroupId;
    @Field(type = FieldType.keyword,index = false)
    private String storeLineGroupName;
    /**
     * 发货时间
     */
    @Field(type = FieldType.Long)
    private Long deliveryTimeId;
    @Field(type = FieldType.keyword)
    private String deliveryTime;
    /**
     * 发货仓库
     */
    @Field(type = FieldType.Long)
    private Long deliveryWarehouseId;
    @Field(type = FieldType.keyword,index = false)
    private String deliveryWarehouseName;
    /**
     * 结账客户
     */
    @Field(type = FieldType.Long)
    private Long storeSettId;
    @Field(type = FieldType.keyword)
    private String storeSettCode;
    @Field(type = FieldType.keyword,index = false)
    private String storeSettName;
    /**
     * 客户所属公司
     */
    @Field(type = FieldType.Long)
    private Long storeCompanyId;
    /**
     * 渠道
     */
    @Field(type = FieldType.Long)
    private Long storeChannelId;
    @Field(type = FieldType.Integer,index = false)
    private Integer printDeliveryQueue;
    @Transient
    private String carportName;

    /***
     * 公司
     */
    @Field(type = FieldType.Long)
    private Long companyId;



    /**
     * 订单同步类型转换
     * @param dto
     * @param order
     * @return
     */
    public static EsOrder convert(OrderSyncODTO dto,EsOrder order) {
        if(order==null){
            order = new EsOrder();
        }
        SpringUtil.copyProperties(dto,order);
        order.setTotalAmount(dto.getTotalAmount()==null?null:dto.getTotalAmount().doubleValue());
        order.setOrderAmount(dto.getOrderAmount()==null?null:dto.getOrderAmount().doubleValue());
        order.setFinalAmount(dto.getFinalAmount()==null?null:dto.getFinalAmount().doubleValue());
        return order;
    }

    public static EsOrder processStore(EsOrder esOrder,EsStore store) {
        Long id = esOrder.getId();
        SpringUtil.copyProperties(store,esOrder);
        esOrder.setId(id);
        esOrder.setStoreId(store.getId());
        esOrder.setCarportName(store.getCarportName());
        return esOrder;
    }

}

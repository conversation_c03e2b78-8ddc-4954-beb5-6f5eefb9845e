package com.pinshang.qingyun.order.search.controller;

import com.pinshang.qingyun.box.utils.SpringUtil;
import com.pinshang.qingyun.order.search.service.EsCommodityService;
import com.pinshang.qingyun.order.search.vo.DifferenceInfoVO;
import com.pinshang.qingyun.product.dto.sync.SelectMissingCodeListIDTO;
import com.pinshang.qingyun.product.service.sync.SyncCommodityInfoClient;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * @Description: es商品
 * @Author: fangting
 * @CreateDate: 2020/8/26 18:05
 */
@RequestMapping("/es/commodity")
@RestController
@Slf4j
public class EsCommodityController {

    @Autowired
    private SyncCommodityInfoClient syncCommodityInfoClient;

    @Autowired
    private EsCommodityService esCommodityService;

    /**
     * 查询主从商品差异信息
     *
     * @return
     */
    @PostMapping("/selectCommodityDifferenceInfo")
    public DifferenceInfoVO selectCommodityDifferenceInfo() {
        Integer masterQuantity = syncCommodityInfoClient.selectAllCommodityQuantity();
        Long slaveQuantity = esCommodityService.selectAllCommodityQuantity();
        return new DifferenceInfoVO(masterQuantity, slaveQuantity.intValue());
    }


    /**
     * 查询从库缺失的商品编码集合
     *
     * @return
     */
    @PostMapping("/selectMissingCommodityCodeList")
    public List<String> selectMissingCommodityCodeList() {
        List<Long> slaveCommodityIdList = esCommodityService.selectAllCommodityIdList();
        SelectMissingCodeListIDTO idto = SpringUtil.isNotEmpty(slaveCommodityIdList)? new SelectMissingCodeListIDTO(slaveCommodityIdList): new SelectMissingCodeListIDTO();
        return syncCommodityInfoClient.selectMissingCommodityCodeList(idto);
    }


}

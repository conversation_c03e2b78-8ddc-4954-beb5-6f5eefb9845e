package com.pinshang.qingyun.order.search.listener;

import com.alibaba.fastjson.JSON;
import com.pinshang.qingyun.base.annotations.OnlineSwitchWatcher;
import com.pinshang.qingyun.base.api.QYApplicationContext;
import com.pinshang.qingyun.kafka.base.BaseKafkaOnlineSwitchProcessor;
import com.pinshang.qingyun.kafka.base.KafkaMessageWrapper;
import com.pinshang.qingyun.kafka.base.KafkaTopicConstant;
import com.pinshang.qingyun.order.search.document.EsTjCommodity;
import com.pinshang.qingyun.order.search.service.EsCommodityService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.List;

/**
 * @Description: es商品监听
 * @Author: fangting
 * @CreateDate: 2020/8/26 10:18
 */
@Component
@Slf4j
@OnlineSwitchWatcher
public class CommoditySearchListener extends BaseKafkaOnlineSwitchProcessor {

    @Autowired
    private EsCommodityService esCommodityService;
    /**
     * 订单同步到搜索
     * @param message
     */
    @KafkaListener(id="${application.name.switch}"+ KafkaTopicConstant.ES_TJ_COMMODITY_TOPIC+"qingyunCommoditySearchKafkaListenerContainerFactory",
            topics = "${application.name.switch}"+KafkaTopicConstant.ES_TJ_COMMODITY_TOPIC
            ,containerFactory="qingyunCommoditySearchKafkaListenerContainerFactory")
    public void esTjCommodityListener(String message) {
        KafkaMessageWrapper wrapper = JSON.parseObject(message, KafkaMessageWrapper.class);
        if(wrapper.getData()!=null) {
            List<EsTjCommodity>  esTjCommodityList= JSON.parseArray(wrapper.getData().toString(), EsTjCommodity.class);
            esCommodityService.saveEsTjCommodity(esTjCommodityList);
        }
    }



    @Override
    public List<String> getKafkaIds() {
        return Arrays.asList(
                QYApplicationContext.applicationNameSwitch + KafkaTopicConstant.ES_TJ_COMMODITY_TOPIC+"qingyunCommoditySearchKafkaListenerContainerFactory"
        );
    }
}

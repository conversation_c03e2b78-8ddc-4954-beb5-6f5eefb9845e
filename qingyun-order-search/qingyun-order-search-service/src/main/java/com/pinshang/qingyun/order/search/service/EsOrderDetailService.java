package com.pinshang.qingyun.order.search.service;

import com.github.pagehelper.PageInfo;
import com.pinshang.qingyun.base.constant.QingyunConstant;
import com.pinshang.qingyun.box.utils.DateUtil;
import com.pinshang.qingyun.box.utils.SpringUtil;
import com.pinshang.qingyun.order.search.document.*;
import com.pinshang.qingyun.order.search.repository.EsOrderItemDetailRepository;
import com.pinshang.qingyun.order.search.repository.EsOrderItemRepository;
import com.pinshang.qingyun.order.search.repository.EsOrderRepository;
import com.pinshang.qingyun.order.search.repository.EsTjCommodityRepository;
import com.pinshang.qingyun.order.search.vo.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.elasticsearch.action.search.SearchType;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.search.aggregations.AggregationBuilders;
import org.elasticsearch.search.aggregations.metrics.sum.InternalSum;
import org.elasticsearch.search.sort.FieldSortBuilder;
import org.elasticsearch.search.sort.SortBuilder;
import org.elasticsearch.search.sort.SortBuilders;
import org.elasticsearch.search.sort.SortOrder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.elasticsearch.core.ElasticsearchTemplate;
import org.springframework.data.elasticsearch.core.ResultsMapper;
import org.springframework.data.elasticsearch.core.ScrolledPage;
import org.springframework.data.elasticsearch.core.SearchResultMapper;
import org.springframework.data.elasticsearch.core.aggregation.AggregatedPage;
import org.springframework.data.elasticsearch.core.query.NativeSearchQueryBuilder;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;

/**
 * 订单明细统计查询
 */
@Service
@Slf4j
public class EsOrderDetailService {

    @Autowired
    private EsTjCommodityRepository esTjCommodityRepository;
    @Autowired
    private EsOrderRepository orderRepository;
    @Autowired
    private EsOrderItemRepository orderItemRepository;
    @Autowired
    private ElasticsearchTemplate elasticsearchTemplate;
    @Autowired
    private EsOrderItemDetailRepository orderItemDetailRepository;

    /**
     * 订单明细搜索分页查询V1
     * 1.因此时使用的订单明细索引没有冗余更多的订单信息，先根据条件查询得到order
     * 2.根据查询得到的订单ID，查询订单明细
     * 3.根据分页得到的订单明细商品，查询商品索引，获取商品基础信息
     * @param vo
     * @return
     * {@link #queryListV2}
     */
    @Deprecated
    public PageInfo<OrderDetailEntry> queryList(OrderDetailVo vo) {
        Map<Long,EsOrder> orderMap = this.queryEsOrderMap(vo);
        if(SpringUtil.isEmpty(orderMap) || CollectionUtils.isEmpty(orderMap.keySet())){
            return null;
        }
        BoolQueryBuilder boolQuery = this.processOrderItemQueryParam(vo,new ArrayList<>(orderMap.keySet()));
        //查询商品金额合计
        OrderDetailEntry sumEntry = this.querySum(boolQuery);
        if(sumEntry == null){
            return null;
        }
        sumEntry.setCommoditySpec("合计:");
        //分页查询明细
        Long totalCount = orderItemRepository.count();
        if (vo.getPageSize()>totalCount){
            vo.setPageSize(totalCount.intValue());
        }
        List<Sort.Order> sortList = new ArrayList<>(2);
        sortList.add(new Sort.Order(Sort.Direction.DESC,"orderTime"));
        sortList.add(new Sort.Order(Sort.Direction.ASC,"storeCode"));
        sortList.add(new Sort.Order(Sort.Direction.ASC,"commodityCode"));
        Sort sort = Sort.by(sortList);
        Pageable pageable = PageRequest.of(vo.getPageNo()-1,vo.getPageSize(),sort);
        Page<EsOrderItem> orderItemPage = orderItemRepository.search(boolQuery, pageable);
        if (orderItemPage != null && CollectionUtils.isNotEmpty(orderItemPage.getContent())) {
            List<OrderDetailEntry> entryList = new ArrayList<>(orderItemPage.getContent().size());
            Set<Long> commodityIdList = new HashSet<>();
            orderItemPage.getContent().forEach(orderItem -> {
                OrderDetailEntry entry = OrderDetailEntry.convert(orderItem,orderMap);
                if(entry!=null){
                    entryList.add(entry);
                    commodityIdList.add(orderItem.getCommodityId());
                }
            });
            if(CollectionUtils.isNotEmpty(entryList)){
                this.processCommodityInfo(entryList,commodityIdList);
            }
            entryList.add(0, sumEntry);
            PageInfo<OrderDetailEntry> pageInfo = new PageInfo();
            pageInfo.setList(entryList);
            pageInfo.setTotal(orderItemPage.getTotalElements());
            return pageInfo;
        }
        return null;

    }

    /**
     * 订单明细金额合计
     * @param boolQuery
     * @return
     */
    public OrderDetailEntry querySum(BoolQueryBuilder boolQuery) {
        NativeSearchQueryBuilder nativeSearchQueryBuilder = new NativeSearchQueryBuilder()
                .withQuery(boolQuery).withFields("commodityNum","totalPriceCent")
                .addAggregation(AggregationBuilders.sum("sum_commodityNum").field("commodityNum"))
                .addAggregation(AggregationBuilders.sum("sum_totalPrice").field("totalPriceCent"));
        AggregatedPage<EsOrderItemDetail> orderItemPage = elasticsearchTemplate.queryForPage(nativeSearchQueryBuilder.build(),EsOrderItemDetail.class);
        InternalSum sumNum =  orderItemPage.getAggregations().get("sum_commodityNum");
        InternalSum sumPrice =  orderItemPage.getAggregations().get("sum_totalPrice");
        OrderDetailEntry sumEntry = new OrderDetailEntry();
        sumEntry.setOrderGoodsNum(new BigDecimal(Double.toString(sumNum.getValue())));
        sumEntry.setCommodityAmount(new BigDecimal(Double.toString(sumPrice.getValue()/100)));
        return sumEntry;
    }

    /**
     * 查询订单
     * @param vo
     * @return
     */
    public Map<Long,EsOrder> queryEsOrderMap(OrderDetailVo vo){
        BoolQueryBuilder boolQuery = this.processOrderQueryParam(vo);
        Map<Long,EsOrder> orderMap = new HashMap<>();
        Iterable<EsOrder> orderIterable =orderRepository.search(boolQuery);
        if(orderIterable != null){
            Iterator<EsOrder> orderIterator = orderIterable.iterator();
            while (orderIterator.hasNext()){
                EsOrder esOrder = orderIterator.next();
                orderMap.put(esOrder.getId(),esOrder);
            }
        }
        if(SpringUtil.isEmpty(orderMap)){
            return null;
        }
        return orderMap;
    }

    /**
     * 查询商品
     * @param entryList
     * @param commodityIdList
     * @return
     */
    private List<OrderDetailEntry> processCommodityInfo(List<OrderDetailEntry> entryList,Set<Long> commodityIdList){
        if(CollectionUtils.isEmpty(entryList)){
            return Collections.EMPTY_LIST;
        }
        Iterable<EsTjCommodity> esTjCommodityIterable = esTjCommodityRepository.findAllById(commodityIdList);
        Map<Long,EsTjCommodity> commodityMap = new HashMap<>();
        esTjCommodityIterable.forEach(item->{
            commodityMap.put(item.getId(),item);
        });
        entryList.forEach(entry->{
            if(commodityMap.get(entry.getCommodityId())!=null){
                EsTjCommodity esTjCommodity = commodityMap.get(entry.getCommodityId());
                entry.setCommodityCode(esTjCommodity.getCommodityCode());
                entry.setCommodityName(esTjCommodity.getCommodityName());
                entry.setCommoditySpec(esTjCommodity.getCommoditySpec());
                entry.setCateName(esTjCommodity.getCommodityFirstKindName());
            }
        });
        return entryList;
    }

    /**
     * 构建查询参数
     * @param vo
     * @return
     */
    private BoolQueryBuilder processOrderQueryParam(OrderDetailVo vo){
        BoolQueryBuilder boolQuery = QueryBuilders.boolQuery();
        if(vo.getStartOrderDate()!=null && vo.getEndOrderDate()!=null){
            QueryBuilder query1 = QueryBuilders.rangeQuery("orderTime").gte(DateUtil.getDateFormate(vo.getStartOrderDate(),"yyyy-MM-dd"));
            QueryBuilder query2 = QueryBuilders.rangeQuery("orderTime").lte(DateUtil.getDateFormate(vo.getEndOrderDate(),"yyyy-MM-dd"));
            boolQuery.must(query1).must(query2);
        }
        if(vo.getSettlementId()!=null){
            boolQuery.must(QueryBuilders.termQuery("storeSettId",vo.getSettlementId()));
        }
        if(StringUtils.isNotEmpty(vo.getStoreCode())){
            boolQuery.must(QueryBuilders.termsQuery("storeCode",vo.getStoreCode()));
        }
        if(vo.getStoreTypeId()!=null){
            boolQuery.must(QueryBuilders.termQuery("storeTypeId",vo.getStoreTypeId()));
        }
        if(vo.getOperatorId()!=null){
            boolQuery.must(QueryBuilders.termQuery("updateId",vo.getOperatorId()));
        }
        if(vo.getCompanyId()!=null){
            boolQuery.must(QueryBuilders.termQuery("companyId",vo.getCompanyId()));
        }
        boolQuery.must(QueryBuilders.termQuery("orderStatus",0));
        return boolQuery;
    }

    /**
     * 构建查询参数，查询订单明细
     * @param vo
     * @param orderIdList
     * @return
     */
    private BoolQueryBuilder processOrderItemQueryParam(OrderDetailVo vo,List<Long> orderIdList){
        BoolQueryBuilder boolQuery = QueryBuilders.boolQuery();
        if(CollectionUtils.isNotEmpty(orderIdList)){
            boolQuery.must(QueryBuilders.termsQuery("orderId",orderIdList));
        }
        if(vo.getCommodityId()!=null){
            boolQuery.must(QueryBuilders.termQuery("commodityId",vo.getCommodityId()));
        }
        if(vo.getCategoryId()!=null){
            QueryBuilder subQuery = QueryBuilders.boolQuery()
                    .should(QueryBuilders.termQuery("commodityFirstKindId",vo.getCategoryId()))
                    .should(QueryBuilders.termQuery("commoditySecondKindId",vo.getCategoryId()))
                    .should(QueryBuilders.termQuery("commodityThirdKindId",vo.getCategoryId()));
            boolQuery.must(subQuery);
        }
        return boolQuery;
    }

    /**
     *  分页查询订单明细
     *  订单明细详情索引冗余了订单+客户+商品，不需要再单独查询订单和商品，比V1速度稍快
     * @param vo
     * @return
     * {@link #queryList}
     */
    public PageInfo<OrderDetailEntry> queryListV2(OrderDetailVo vo) {
        BoolQueryBuilder boolQuery = this.processQueryParam(vo);
        //查询商品金额合计
        OrderDetailEntry sumEntry = this.querySum(boolQuery);
        if(sumEntry == null){
            return null;
        }
        sumEntry.setCommoditySpec("合计:");
        //分页查询明细
        Long totalCount = orderItemDetailRepository.count();
        if (vo.getPageSize()>totalCount){
            vo.setPageSize(totalCount.intValue());
        }
        List<Sort.Order> sortList = new ArrayList<>(2);
        sortList.add(new Sort.Order(Sort.Direction.DESC,"orderTime"));
        sortList.add(new Sort.Order(Sort.Direction.ASC,"storeCode"));
        sortList.add(new Sort.Order(Sort.Direction.ASC,"commodityCode"));
        Sort sort = Sort.by(sortList);
        Pageable pageable = PageRequest.of(vo.getPageNo()-1,vo.getPageSize(),sort);
        Page<EsOrderItemDetail> orderItemPage = orderItemDetailRepository.search(boolQuery, pageable);
        if (orderItemPage != null && CollectionUtils.isNotEmpty(orderItemPage.getContent())) {
            List<OrderDetailEntry> entryList = new ArrayList<>(orderItemPage.getContent().size());
            orderItemPage.getContent().forEach(orderItem -> {
                OrderDetailEntry entry = OrderDetailEntry.convert(orderItem);
                if(entry!=null){
                    entryList.add(entry);
                }
            });
            entryList.add(0, sumEntry);
            PageInfo<OrderDetailEntry> pageInfo = new PageInfo();
            pageInfo.setList(entryList);
            pageInfo.setTotal(orderItemPage.getTotalElements());
            return pageInfo;
        }
        return null;

    }

    private BoolQueryBuilder processQueryParam(OrderDetailVo vo){
        BoolQueryBuilder boolQuery = this.processOrderQueryParam(vo);
        if(vo.getCommodityId()!=null){
            boolQuery.must(QueryBuilders.termQuery("commodityId",vo.getCommodityId()));
        }
        if(vo.getCategoryId()!=null){
            QueryBuilder subQuery = QueryBuilders.boolQuery()
                    .should(QueryBuilders.termQuery("commodityFirstKindId",vo.getCategoryId()))
                    .should(QueryBuilders.termQuery("commoditySecondKindId",vo.getCategoryId()))
                    .should(QueryBuilders.termQuery("commodityThirdKindId",vo.getCategoryId()));
            boolQuery.must(subQuery);
        }
        if(StringUtils.isNotBlank(vo.getOrderCode())){
            boolQuery.must(QueryBuilders.termQuery("orderCode",vo.getOrderCode()));
        }
        return boolQuery;
    }


    public List<OrderDetailEntry> exportList(OrderDetailVo vo){
        BoolQueryBuilder boolQuery = this.processQueryParam(vo);
        NativeSearchQueryBuilder searchQuery = new NativeSearchQueryBuilder().withQuery(boolQuery)
                .withPageable(PageRequest.of(QingyunConstant.NUM_INT_ZERO, QingyunConstant.NUM_INT_5000))
//                .withSort(SortBuilders.fieldSort("orderTime").order(SortOrder.DESC))
//                .withSort(SortBuilders.fieldSort("storeCode").order(SortOrder.ASC))
//                .withSort(SortBuilders.fieldSort("commodityCode").order(SortOrder.ASC))
                ;
        ScrolledPage<EsOrderItemDetail> scroll =(ScrolledPage<EsOrderItemDetail>)  elasticsearchTemplate.startScroll(QingyunConstant.LONG_30000, searchQuery.build(),EsOrderItemDetail.class);
        List<OrderDetailEntry> entryList = new ArrayList<>();
        while (scroll.hasContent()) {
            for (EsOrderItemDetail dto : scroll.getContent()) {
                entryList.add( OrderDetailEntry.convert(dto));
            }
            scroll = (ScrolledPage<EsOrderItemDetail>) elasticsearchTemplate.continueScroll(scroll.getScrollId(), QingyunConstant.LONG_30000, EsOrderItemDetail.class);
        }
        elasticsearchTemplate.clearScroll(scroll.getScrollId());
        return entryList;
    }

}

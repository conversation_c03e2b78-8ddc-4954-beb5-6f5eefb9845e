package com.pinshang.qingyun.order.search.controller;

import com.pinshang.qingyun.box.utils.SpringUtil;
import com.pinshang.qingyun.order.search.service.EsStoreService;
import com.pinshang.qingyun.product.dto.sync.SelectMissingCodeListIDTO;
import com.pinshang.qingyun.product.service.sync.SyncStoreInfoClient;
import com.pinshang.qingyun.sync.entry.DifferenceInfoEntry;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * @Description: es客户
 * @Author: fangting
 * @CreateDate: 2020/9/2 10:29
 */
@RestController
@RequestMapping("/esStore")
@Slf4j
public class EsStoreController {
    @Autowired
    private SyncStoreInfoClient syncStoreInfoClient;

    @Autowired
    private EsStoreService esStoreService;

    /**
     * 查询  主库、es 客户差异信息
     *
     * @return
     */
    @PostMapping("/selectStoreDifferenceInfo")
    public DifferenceInfoEntry selectStoreDifferenceInfo() {
        Integer masterQuantity = syncStoreInfoClient.selectAllStoreQuantity();
        Integer slaveQuantity = esStoreService.selectEsStoreCount().intValue();
        return new DifferenceInfoEntry(masterQuantity, slaveQuantity);
    }

    /**
     * 查询  es缺失的客户编码集合
     *
     * @return
     */
    @PostMapping("/selectMissingStoreCodeList")
    public List<String> selectMissingStoreCodeList() {
        List<Long> slaveStoreIdList = esStoreService.queryEsStoreDiffList();

        SelectMissingCodeListIDTO idto = SpringUtil.isNotEmpty(slaveStoreIdList)? new SelectMissingCodeListIDTO(slaveStoreIdList): new SelectMissingCodeListIDTO();
        return syncStoreInfoClient.selectMissingStoreCodeList(idto);
    }
}

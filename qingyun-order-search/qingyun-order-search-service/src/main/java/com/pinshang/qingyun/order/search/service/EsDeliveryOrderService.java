package com.pinshang.qingyun.order.search.service;

import com.github.pagehelper.PageInfo;
import com.pinshang.qingyun.box.utils.BeanCloneUtils;
import com.pinshang.qingyun.box.utils.DateUtil;
import com.pinshang.qingyun.order.search.document.EsOrder;
import com.pinshang.qingyun.order.search.document.EsOrderItem;
import com.pinshang.qingyun.order.search.document.EsStore;
import com.pinshang.qingyun.order.search.document.EsTjCommodity;
import com.pinshang.qingyun.order.search.dto.DeliveryOrderDetailEntry;
import com.pinshang.qingyun.order.search.dto.DeliveryOrderDetailRespDTO;
import com.pinshang.qingyun.order.search.dto.DeliveryOrderGiftEntry;
import com.pinshang.qingyun.order.search.repository.EsOrderItemRepository;
import com.pinshang.qingyun.order.search.repository.EsOrderRepository;
import com.pinshang.qingyun.order.search.repository.EsStoreRepository;
import com.pinshang.qingyun.order.search.repository.EsTjCommodityRepository;
import com.pinshang.qingyun.order.search.vo.DeliveryOrderEntry;
import com.pinshang.qingyun.order.search.vo.DeliveryOrderReqVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.RequestParam;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import java.util.Optional;

/**
 * @Description:
 * @Author: fangting
 * @CreateDate: 2020/9/7 15:46
 */
@Slf4j
@Service
public class EsDeliveryOrderService {
    @Autowired
    private EsOrderRepository esOrderRepository;
    @Autowired
    private EsOrderItemRepository esOrderItemRepository;
    @Autowired
    private EsStoreRepository esStoreRepository;
    @Autowired
    private EsTjCommodityRepository esTjCommodityRepository;


    /**
     * 送货单列表查询
     * @param reqVO
     * @return
     */
    public PageInfo<DeliveryOrderEntry> queryList(DeliveryOrderReqVO reqVO){
        PageInfo<DeliveryOrderEntry> pageInfo = new PageInfo();

        //查询条件
        BoolQueryBuilder boolQuery = QueryBuilders.boolQuery();
        conditionsHandle(reqVO, boolQuery);

        //排序
        Sort sort = new Sort(Sort.Direction.DESC, "orderTime","createTime");
        if (reqVO.getSortRule() == 2){
            sort = new Sort(Sort.Direction.ASC, "storeSettCode","storeCode","orderTime");
        }
        //分页，如果pageSize> 总条数，则另pageSize=总条数，（pageSize设置过大会报错，例 导出列表查询初始的值）
        Long totalCount = esOrderRepository.count();
        if (reqVO.getPageSize()>totalCount){
            reqVO.setPageSize(totalCount.intValue());
        }
        Pageable pageable = PageRequest.of(reqVO.getPageNo()-1, reqVO.getPageSize(), sort);
        Page<EsOrder> esOrderPage = esOrderRepository.search(boolQuery, pageable);

        if (esOrderPage != null && CollectionUtils.isNotEmpty(esOrderPage.getContent())) {
            List<DeliveryOrderEntry> deliveryOrderEntries = new ArrayList<>(esOrderPage.getContent().size());
            //对象转换
            esOrderPage.getContent().forEach(esOrder -> deliveryOrderEntries.add(DeliveryOrderEntry.convert(esOrder)));
            pageInfo.setList(deliveryOrderEntries);
            pageInfo.setTotal(esOrderPage.getTotalElements());
        }
        return pageInfo;
    }

    /**
     * 查询条件处理
     * @param reqVO
     * @param boolQuery
     */
    private void conditionsHandle(DeliveryOrderReqVO reqVO, BoolQueryBuilder boolQuery){
        boolQuery.must(QueryBuilders.termQuery("orderStatus", 0));
        if (reqVO.getStartOrderDate() != null && reqVO.getEndOrderDate() != null) {
            boolQuery.must(QueryBuilders.rangeQuery("orderTime").gte(DateUtil.getDateFormate(reqVO.getStartOrderDate(),"yyyy-MM-dd")));
            boolQuery.must(QueryBuilders.rangeQuery("orderTime").lte(DateUtil.getDateFormate(reqVO.getEndOrderDate(),"yyyy-MM-dd")));
        }
        if (reqVO.getStoreTypeId() != null){
            boolQuery.must(QueryBuilders.termQuery("storeTypeId",reqVO.getStoreTypeId()));
        }
        if (StringUtils.isNotBlank(reqVO.getOrderCode())){
            reqVO.setOrderCode(reqVO.getOrderCode().toLowerCase());
            boolQuery.must(QueryBuilders.boolQuery().should(QueryBuilders.wildcardQuery("orderCode","*"+reqVO.getOrderCode()+"*"))
                                                    .should(QueryBuilders.matchPhraseQuery("orderCode",reqVO.getOrderCode()))
            );
        }
        if (StringUtils.isNotBlank(reqVO.getStoreCode())){
            //字母转小写，不支持大写字母查询
            reqVO.setStoreCode(reqVO.getStoreCode().toLowerCase());
            //字段值都是中文使用matchPhraseQuery ，其他数字、英文、混合使用wildcardQuery
            QueryBuilder builderStoreCode = QueryBuilders.boolQuery()
                    .should(QueryBuilders.matchPhraseQuery("storeCode",reqVO.getStoreCode()))
                    .should(QueryBuilders.wildcardQuery("storeCode","*"+reqVO.getStoreCode()+"*"))
                    .should(QueryBuilders.wildcardQuery("storeName","*"+reqVO.getStoreCode()+"*"))
                    .should(QueryBuilders.matchPhraseQuery("storeName",reqVO.getStoreCode()))
                    ;

            boolQuery.must(builderStoreCode);
        }
        if (reqVO.getSettlementCustomerId() != null){
            boolQuery.must(QueryBuilders.termQuery("storeSettId",reqVO.getSettlementCustomerId()));
        }
        if (StringUtils.isNotBlank(reqVO.getSettlementCustomerCode())){
            reqVO.setSettlementCustomerCode(reqVO.getSettlementCustomerCode().toLowerCase());
            QueryBuilder builderSettlement = QueryBuilders.boolQuery()
                    .should(QueryBuilders.matchPhraseQuery("storeSettCode",reqVO.getSettlementCustomerCode()))
                    .should(QueryBuilders.wildcardQuery("storeSettCode","*"+reqVO.getSettlementCustomerCode()+"*"))
                    .should(QueryBuilders.wildcardQuery("storeSettName","*"+reqVO.getSettlementCustomerCode()+"*"))
                    .should(QueryBuilders.matchPhraseQuery("storeSettName",reqVO.getSettlementCustomerCode()));
            boolQuery.must(builderSettlement);
        }

        if (reqVO.getDeliveryManId() != null){
            boolQuery.must(QueryBuilders.termQuery("deliverymanId", reqVO.getDeliveryManId()));
        }
        if (reqVO.getLineGroupId() != null){
            boolQuery.must(QueryBuilders.termQuery("storeLineGroupId",reqVO.getLineGroupId()));
        }
        if (reqVO.getStoreChannelId() != null){
            boolQuery.must(QueryBuilders.termQuery("storeChannelId",reqVO.getStoreChannelId()));
        }
        if (reqVO.getSupervisorId() != null){
            boolQuery.must(QueryBuilders.termQuery("supervisorId", reqVO.getSupervisorId()));
        }
        if (reqVO.getOperatorId() != null){
            boolQuery.must(QueryBuilders.termQuery("createId", reqVO.getOperatorId()));
        }
        if (reqVO.getDeliveryBatch() != null){
            boolQuery.must(QueryBuilders.termQuery("deliveryBatch", reqVO.getDeliveryBatch()));
        }

    }


    /**
     * 送货单详情
     * @param orderId
     * @return
     */
    public DeliveryOrderDetailRespDTO queryDeliveryOrderDetail(Long orderId){
        DeliveryOrderDetailRespDTO detailRespDTO = new DeliveryOrderDetailRespDTO();
        detailRespDTO.setDetail(queryOrderDetail(orderId));
        detailRespDTO.setOrderList(queryOrderGiftList(orderId,1));
        detailRespDTO.setGiftList(queryOrderGiftList(orderId,2));
        detailRespDTO.setRationList(queryOrderGiftList(orderId,3));

        return detailRespDTO;
    }

    /**
     * 订单明细
     * @param orderId
     * @return
     */
    private DeliveryOrderDetailEntry queryOrderDetail(Long orderId){
        EsOrder esOrder = esOrderRepository.findById(orderId).get();
        if (esOrder == null){
            return new DeliveryOrderDetailEntry();
        }
        DeliveryOrderDetailEntry detailEntry = DeliveryOrderDetailEntry.covert(esOrder);
        EsStore esStore = esStoreRepository.findById(esOrder.getStoreId()).get();
        if (esStore != null) {
            detailEntry.setAreaName(esStore.getStoreDistrictName());
            detailEntry.setDeliveryAddress(esStore.getStoreDeliveryAddress());
            detailEntry.setLinkmanMobile(esStore.getLinkmanMobile());
        }

        return detailEntry;
    }

    /**
     * 订单商品明细
     * @param orderId
     * @param type
     * @return
     */
    private List<DeliveryOrderGiftEntry> queryOrderGiftList(Long orderId, Integer type){
        List<DeliveryOrderGiftEntry> detailEntries = new ArrayList<>();

        //查询订单商品明细
        BoolQueryBuilder boolQuery = QueryBuilders.boolQuery().must(QueryBuilders.termQuery("orderId",orderId))
                .must(QueryBuilders.termQuery("type",type));
        Iterator<EsOrderItem> esOrderItems = esOrderItemRepository.search(boolQuery).iterator();
        while (esOrderItems.hasNext()){
            EsOrderItem esOrderItem = esOrderItems.next();
            DeliveryOrderGiftEntry detailEntry = DeliveryOrderGiftEntry.covert(esOrderItem);
            //商品信息
            EsTjCommodity esTjCommodity = esTjCommodityRepository.findById(esOrderItem.getCommodityId()).get();
            if (esTjCommodity != null){
                detailEntry.setCommodityCode(esTjCommodity.getCommodityCode());
                detailEntry.setCommodityName(esTjCommodity.getCommodityName());
                detailEntry.setCommoditySpec(esTjCommodity.getCommoditySpec());
            }
            detailEntries.add(detailEntry);
        }

        return detailEntries;
    }
}

package com.pinshang.qingyun.order.search.dto;

import com.pinshang.qingyun.order.search.document.EsOrder;
import lombok.Data;
import org.springframework.beans.BeanUtils;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 订单客户信息
 */
@Data
public class DeliveryOrderDetailEntry {
    private Long id;
    /**
     * 客户编码
     */
    private String storeCode;
    /**
     * 送货员
     */
    private String deliveryManName;
    /**
     * 督导
     */
    private String supervisorName;
    /**
     * 客户名称
     */
    private String storeName;
    /**
     * 地区
     */
    private String areaName;
    /**
     * 结账客户
     */
    private String settlementCustomer;
    /**
     * 销售员
     */
    private String salesmanName;
    /**
     * 客户备注
     */
    private String storeDescribe;
    /**
     * 地址
     */
    private String deliveryAddress;
    /**
     * 类型
     */
    private String storeTypeName;
    /**
     * 联系电话
     */
    private String linkmanMobile;
    /**
     * 备注
     */
    private String orderRemark;
    /**
     * 余额
     */
    private BigDecimal collectPrice;
    /**
     * 送货日期
     */
    private Date deliveryDate;
    /**
     * 更新日期
     */
    private Date updateTime;
    /**
     * 制单日期
     */
    private Date createTime;
    /**
     * 操作人员
     */
    private String operatorName;
    /**
     * 订单金额
     */
    private BigDecimal finalAmount;

    public static DeliveryOrderDetailEntry covert(EsOrder esOrder){
        DeliveryOrderDetailEntry detailEntry = new DeliveryOrderDetailEntry();
        BeanUtils.copyProperties(esOrder, detailEntry);

        detailEntry.setDeliveryManName(esOrder.getDeliverymanName());
        detailEntry.setSettlementCustomer(esOrder.getStoreSettName());
        detailEntry.setDeliveryDate(esOrder.getOrderTime());
        detailEntry.setOperatorName(esOrder.getCreateName());
        detailEntry.setFinalAmount(new BigDecimal(esOrder.getFinalAmount()));

        return detailEntry;
    }
}

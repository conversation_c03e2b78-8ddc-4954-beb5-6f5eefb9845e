package com.pinshang.qingyun.order.search.service;

import com.pinshang.qingyun.box.utils.DateUtil;
import com.pinshang.qingyun.box.utils.SpringUtil;
import com.pinshang.qingyun.order.search.document.EsOrderItem;
import com.pinshang.qingyun.order.search.dto.ProductShipmentsEntry;
import com.pinshang.qingyun.order.search.repository.EsTjCommodityRepository;
import com.pinshang.qingyun.order.search.vo.ProductShipmentsVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.search.aggregations.AggregationBuilders;
import org.elasticsearch.search.aggregations.bucket.terms.Terms;
import org.elasticsearch.search.aggregations.metrics.sum.InternalSum;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.elasticsearch.core.ElasticsearchTemplate;
import org.springframework.data.elasticsearch.core.aggregation.AggregatedPage;
import org.springframework.data.elasticsearch.core.query.NativeSearchQueryBuilder;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.*;

/**
 * 产品发货总量
 */
@Service
@Slf4j
public class EsProductShipmentsService {

    @Autowired
    private ElasticsearchTemplate elasticsearchTemplate;
    @Autowired
    private EsTjCommodityRepository esTjCommodityRepository;
    @Autowired
    private EsStatisticsCommonService commonService;


    /**
     * 产品发货总量查询列表
     * @param vo
     * @return
     */
    public List<ProductShipmentsEntry> queryList(@RequestBody ProductShipmentsVo vo){
        List<ProductShipmentsEntry> orderItemList = this.queryEsOrderItemByParam(vo);
        if(SpringUtil.isEmpty(orderItemList)){
            log.debug("产品发货总量，搜索中没有满足条件的订单明细数据。");
            return Collections.EMPTY_LIST;
        }
        Collections.sort(orderItemList);
        return orderItemList;
    }


    /**
     * 查询订单明细
     * @param vo
     * @return
     */
    private List<ProductShipmentsEntry> queryEsOrderItemByParam(ProductShipmentsVo vo){
        BoolQueryBuilder orderItemQuery = this.processQueryBuilder(vo);
        Long commodityCount = esTjCommodityRepository.count();
        NativeSearchQueryBuilder nativeSearchQueryBuilder = new NativeSearchQueryBuilder()
                .withQuery(orderItemQuery).withFields("commodityId","storeTypeId","commodityNum")
                .addAggregation(
                        AggregationBuilders.terms("term_commodityId").field("commodityId").size(commodityCount.intValue())
                        .subAggregation(AggregationBuilders.terms("term_storeTypeId").field("storeTypeId").size(Integer.MAX_VALUE)
                                .subAggregation(AggregationBuilders.sum("sum_commodityNum").field("commodityNum"))
                        )
                )
                ;
        AggregatedPage<EsOrderItem> orderItemPage = elasticsearchTemplate.queryForPage(nativeSearchQueryBuilder.build(),EsOrderItem.class);
        List<ProductShipmentsEntry> entryList = new ArrayList<>();
        Set<Long> commodityIds = new HashSet<>();
        if(orderItemPage.hasAggregations()){
            Terms commodityIdTerms = (Terms) orderItemPage.getAggregation("term_commodityId");
            Iterator<Terms.Bucket> iterator = (Iterator<Terms.Bucket>) commodityIdTerms.getBuckets().iterator();
            while (iterator.hasNext()){
                Terms.Bucket bucket = iterator.next();
                Terms storeIdTerms = (Terms) bucket.getAggregations().asMap().get("term_storeTypeId");
                commodityIds.add(Long.valueOf(bucket.getKeyAsString()));

                Iterator<Terms.Bucket> subIterator = (Iterator<Terms.Bucket>) storeIdTerms.getBuckets().iterator();
                while (subIterator.hasNext()){
                    Terms.Bucket subBucket = subIterator.next();
                    InternalSum internalSum = subBucket.getAggregations().get("sum_commodityNum");

                    ProductShipmentsEntry entry = new ProductShipmentsEntry();
                    entry.setCommodityId(Long.valueOf(bucket.getKeyAsString()));
                    entry.setStoreTypeId(Long.valueOf(subBucket.getKeyAsString()));
                    entry.setStoreTypeTotal(internalSum.getValue());
                    entryList.add(entry);
                }
            }
        }
        commonService.processCommodityInfo(entryList,commodityIds);
        return entryList;
    }

    private BoolQueryBuilder processQueryBuilder(ProductShipmentsVo vo){
        BoolQueryBuilder boolQuery = QueryBuilders.boolQuery();
        if(vo.getStartOrderDate()!=null && vo.getEndOrderDate()!=null){
            QueryBuilder query1 = QueryBuilders.rangeQuery("orderTime").gte(DateUtil.getDateFormate(vo.getStartOrderDate(),"yyyy-MM-dd"));
            QueryBuilder query2 = QueryBuilders.rangeQuery("orderTime").lte(DateUtil.getDateFormate(vo.getEndOrderDate(),"yyyy-MM-dd"));
            boolQuery.must(query1).must(query2);
        }
        if(vo.getWarehouseId()!=null){
            boolQuery.must(QueryBuilders.termQuery("deliveryWarehouseId",vo.getWarehouseId()));
        }
        if(vo.getLineGroupId()!=null){
            boolQuery.must(QueryBuilders.termQuery("storeLineGroupId",vo.getLineGroupId()));
        }
        if(StringUtils.isNotEmpty(vo.getDeliveryTime())){
            boolQuery.must(QueryBuilders.termQuery("deliveryTime", vo.getDeliveryTime()));
        }
        if(vo.getDeliveryBatch()!=null){
            boolQuery.must(QueryBuilders.termQuery("deliveryBatch",vo.getDeliveryBatch()));
        }
        boolQuery.must(QueryBuilders.termQuery("orderStatus",0));
        if(vo.getFactoryId()!=null){
            boolQuery.must(QueryBuilders.termQuery("commodityFactoryId",vo.getFactoryId()));
        }
        return boolQuery;
    }


}

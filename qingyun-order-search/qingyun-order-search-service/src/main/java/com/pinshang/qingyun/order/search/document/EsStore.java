package com.pinshang.qingyun.order.search.document;

import com.pinshang.qingyun.box.utils.SpringUtil;
import com.pinshang.qingyun.order.dto.orderStatistics.OrderSyncODTO;
import com.pinshang.qingyun.product.dto.sync.StoreODTO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.data.elasticsearch.annotations.Document;
import org.springframework.data.elasticsearch.annotations.Field;
import org.springframework.data.elasticsearch.annotations.FieldType;

import javax.persistence.Id;
import javax.persistence.Table;
import java.util.List;
import java.util.stream.Collectors;

@Data
@Document(indexName = "tj-store", type = "tj-store",replicas = 2)
@NoArgsConstructor
@AllArgsConstructor
public class EsStore {

    @Id
    private Long id;
    /**
     * 客户编码
     */
    @Field(type = FieldType.text)
    private String storeCode;
    /**
     * 客户名称
     */
    @Field(type = FieldType.text)
    private String storeName;
    /**
     * 客户简称
     */
    @Field(type = FieldType.text,index = false)
    private String storeShortName;
    /**
     * 客户类型
     */
    @Field(type = FieldType.Long)
    private Long storeTypeId;
    @Field(type = FieldType.text,index = false)
    private String storeTypeName;
    /**
     * 线路
     */
    @Field(type = FieldType.Long)
    private Long storeLineId;
    @Field(type = FieldType.text)
    private String storeLineCode;
    @Field(type = FieldType.text,index = false)
    private String storeLineName;
    /**
     * 线路组
     */
    @Field(type = FieldType.Long)
    private Long storeLineGroupId;
    @Field(type = FieldType.text)
    private String storeLineGroupName;
    /**
     * 送货地址
     */
    @Field(type = FieldType.text)
    private String storeDeliveryAddress;
    /**
     * 发货时间
     */
    @Field(type = FieldType.Long)
    private Long deliveryTimeId;
    @Field(type = FieldType.keyword)
    private String deliveryTime;
    /**
     * 发货仓库
     */
    @Field(type = FieldType.Long)
    private Long deliveryWarehouseId;
    @Field(type = FieldType.text,index = false)
    private String deliveryWarehouseName;
    /**
     * 车位
     */
    @Field(type = FieldType.text,index = false)
    private String carportName;
    /**
     * 送货员
     */
    @Field(type = FieldType.Long)
    private Long deliverymanId;
    @Field(type = FieldType.text,index = false)
    private String deliverymanName;
    /**
     * 班组长
     */
    @Field(type = FieldType.Long)
    private Long teamLeaderId;
    @Field(type = FieldType.text,index = false)
    private String teamLeaderName;
    /**
     * 销售员
     */
    @Field(type = FieldType.Long)
    private Long salesmanId;
    @Field(type = FieldType.text,index = false)
    private String salesmanName;
    /**
     * 督导
     */
    @Field(type = FieldType.Long)
    private Long supervisorId;
    @Field(type = FieldType.text,index = false)
    private String supervisorName;
    /**
     * 办事处主任
     */
    @Field(type = FieldType.Long,index = false)
    private Long officeDirectorId;
    @Field(type = FieldType.text,index = false)
    private String officeDirectorName;
    /**
     * 大区经理
     */
    @Field(type = FieldType.Long)
    private Long regionManagerId;
    @Field(type = FieldType.text,index = false)
    private String regionManagerName;
    /**
     * 结账客户
     */
    @Field(type = FieldType.Long)
    private Long storeSettId;
    @Field(type = FieldType.text,index = false)
    private String storeSettCode;
    @Field(type = FieldType.text,index = false)
    private String storeSettName;
    /**
     * 客户所属公司
     */
    @Field(type = FieldType.Long,index = false)
    private Long storeCompanyId;
    @Field(type = FieldType.text,index = false)
    private String storeCompanyName;
    /**
     * 打印批次
     */
    @Field(type = FieldType.Long,index = false)
    private Long printDeliveryBatch;
    @Field(type = FieldType.text,index = false)
    private String printDeliveryBatchName;
    /**
     * 打印顺序
     */
    @Field(type = FieldType.Integer,index = false)
    private Integer printDeliveryQueue;
    /**
     * 渠道
     */
    @Field(type = FieldType.Long)
    private Long storeChannelId;
    @Field(type = FieldType.text,index = false)
    private String storeChannelName;
    /**
     * 地区
     */
    @Field(type = FieldType.Long)
    private Long storeDistrictId;
    @Field(type = FieldType.text,index = false)
    private String storeDistrictName;
    /**
     * 联系手机
     */
    @Field(type = FieldType.text,index = false)
    private String linkmanMobile;
    /**
     * 是否显示单价
     */
    @Field(type = FieldType.Integer,index = false)
    private Integer showPriceRetail;
    /**
     * 是否要求检验报告
     */
    @Field(type = FieldType.Integer,index = false)
    private Integer isTestReport;

    public static EsStore convert(StoreODTO dto) {
        EsStore esStore = new EsStore();
        SpringUtil.copyProperties(dto,esStore);
        esStore.setDeliveryWarehouseId(dto.getDeliveryStorageId());
        esStore.setDeliveryWarehouseName(dto.getDeliveryStorageName());
        return esStore;
    }

}

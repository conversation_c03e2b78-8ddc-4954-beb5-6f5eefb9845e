package com.pinshang.qingyun.order.search.service;

import com.pinshang.qingyun.box.utils.DateUtil;
import com.pinshang.qingyun.box.utils.SpringUtil;
import com.pinshang.qingyun.order.search.document.EsOrderItem;
import com.pinshang.qingyun.order.search.document.EsTjCommodity;
import com.pinshang.qingyun.order.search.dto.FreshProductShipmentsEntry;
import com.pinshang.qingyun.order.search.dto.ProductSaleStatisticsTempEntry;
import com.pinshang.qingyun.order.search.repository.EsTjCommodityRepository;
import com.pinshang.qingyun.order.search.vo.FreshProductShipmentsVo;
import lombok.extern.slf4j.Slf4j;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.search.aggregations.AggregationBuilders;
import org.elasticsearch.search.aggregations.bucket.terms.Terms;
import org.elasticsearch.search.aggregations.metrics.sum.InternalSum;
import org.elasticsearch.search.aggregations.metrics.tophits.TopHits;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.elasticsearch.core.ElasticsearchTemplate;
import org.springframework.data.elasticsearch.core.aggregation.AggregatedPage;
import org.springframework.data.elasticsearch.core.query.NativeSearchQueryBuilder;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.*;


/**
 * 产品发货总量(鲜食)
 */
@Service
@Slf4j
public class EsFreshProductShipmentsService {

    @Autowired
    private ElasticsearchTemplate elasticsearchTemplate;
    @Autowired
    private EsTjCommodityRepository esTjCommodityRepository;
    @Autowired
    private EsStatisticsCommonService commonService;


    /**
     * 产品发货总量(鲜食)列表
     * @param vo
     * @return
     */
    public List<FreshProductShipmentsEntry> queryList(@RequestBody FreshProductShipmentsVo vo){
        List<FreshProductShipmentsEntry> orderItemList = this.queryEsOrderItemByParam(vo);
        if(SpringUtil.isEmpty(orderItemList)){
            log.debug("产品发货总量(鲜食)，搜索中没有满足条件的订单明细数据。");
            return Collections.EMPTY_LIST;
        }
        Collections.sort(orderItemList);
        return orderItemList;
    }


    /**
     * 查询订单明细
     * @param vo
     * @return
     */
    private List<FreshProductShipmentsEntry> queryEsOrderItemByParam(FreshProductShipmentsVo vo){
        BoolQueryBuilder orderItemQuery = this.processQueryBuilder(vo);
        Long commodityCount = esTjCommodityRepository.count();
        NativeSearchQueryBuilder nativeSearchQueryBuilder = new NativeSearchQueryBuilder()
                .withQuery(orderItemQuery).withFields("commodityId","storeId","commodityNum")
                .addAggregation(
                         AggregationBuilders.terms("term_commodityId").field("commodityId").size(commodityCount.intValue())
                         .subAggregation(AggregationBuilders.terms("term_storeId").field("storeId").size(Integer.MAX_VALUE)
                                 .subAggregation(AggregationBuilders.sum("sum_commodityNum").field("commodityNum"))
                         )
                )
        ;
        AggregatedPage<EsOrderItem> orderItemPage = elasticsearchTemplate.queryForPage(nativeSearchQueryBuilder.build(),EsOrderItem.class);
        List<FreshProductShipmentsEntry> entryList = new ArrayList<>();
        Set<Long> commodityIdList = new HashSet<>();
        if(orderItemPage.hasAggregations()){
            Terms commodityIdTerms = (Terms) orderItemPage.getAggregation("term_commodityId");
            Iterator<Terms.Bucket> iterator = (Iterator<Terms.Bucket>) commodityIdTerms.getBuckets().iterator();
            while (iterator.hasNext()) {
                Terms.Bucket bucket = iterator.next();
                commodityIdList.add(Long.valueOf(bucket.getKeyAsString()));
                Terms storeIdTerms = (Terms) bucket.getAggregations().asMap().get("term_storeId");
                Iterator<Terms.Bucket> subIterator = (Iterator<Terms.Bucket>) storeIdTerms.getBuckets().iterator();
                while (subIterator.hasNext()) {
                    Terms.Bucket subBucket = subIterator.next();
                    InternalSum internalSum = subBucket.getAggregations().get("sum_commodityNum");

                    FreshProductShipmentsEntry entry = new FreshProductShipmentsEntry();
                    entry.setCommodityId(Long.valueOf(bucket.getKeyAsString()));
                    entry.setStoreId(Long.valueOf(subBucket.getKeyAsString()));
                    entry.setCommodityTotal(internalSum.getValue());
                    entryList.add(entry);
                }
            }
        }
        commonService.processCommodityInfo(entryList,commodityIdList);
        return entryList;
    }

    private BoolQueryBuilder processQueryBuilder(FreshProductShipmentsVo vo){
        BoolQueryBuilder boolQuery = QueryBuilders.boolQuery();
        if(vo.getOrderDate()!=null){
            boolQuery.must(QueryBuilders.termQuery("orderTime",DateUtil.getDateFormate(vo.getOrderDate(),"yyyy-MM-dd")));
        }
        if(vo.getLineGroupId()!=null){
            boolQuery.must(QueryBuilders.termQuery("storeLineGroupId",vo.getLineGroupId()));
        }
        if(vo.getDeliveryBatch()!=null){
            boolQuery.must(QueryBuilders.termQuery("deliveryBatch",vo.getDeliveryBatch()));
        }
        if(vo.getFactoryId()!=null){
            boolQuery.must(QueryBuilders.termQuery("commodityFactoryId",vo.getFactoryId()));
        }
        boolQuery.must(QueryBuilders.termQuery("orderStatus",0));
        return boolQuery;
    }
}

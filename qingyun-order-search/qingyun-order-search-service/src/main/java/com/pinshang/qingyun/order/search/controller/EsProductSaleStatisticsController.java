package com.pinshang.qingyun.order.search.controller;

import com.pinshang.qingyun.order.search.dto.ProductSaleStatisticsSumEntry;
import com.pinshang.qingyun.order.search.dto.ProductSaleStatisticsTempEntry;
import com.pinshang.qingyun.order.search.service.EsProductSaleStatisticsService;
import com.pinshang.qingyun.order.search.vo.ProductSaleStatisticsVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 产品销售汇总
 */
@RestController
@RequestMapping(value = "/esStatistical/productSaleStatistics")
public class EsProductSaleStatisticsController {

    @Autowired
    private EsProductSaleStatisticsService esProductSaleStatisticsService;


    /**
     * 产品销售汇总
     * @param vo
     * @return
     */
    @PostMapping("/list")
    public List<ProductSaleStatisticsTempEntry> queryList(@RequestBody ProductSaleStatisticsVo vo) {
        return esProductSaleStatisticsService.queryList(vo);
    }

    /**
     * 产品销售汇总金额合计
     * @param vo
     * @return
     */
    @PostMapping("/querySum")
    public ProductSaleStatisticsSumEntry querySum(@RequestBody ProductSaleStatisticsVo vo) {
        return esProductSaleStatisticsService.querySum(vo);
    }

}
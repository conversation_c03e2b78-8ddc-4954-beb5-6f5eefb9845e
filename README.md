#门店订单服务

####服务简介
门店订单服务是支撑清美集团门店订单相关的核心模块。

#### 主要研发人员
ALL

####git 地址
1. http://*************/pinshang/qingyun-order.git
2. git@*************:pinshang/qingyun-order.git

####jenkins 部署

一。服务部署
1. qingyun-order-service build 配置
   > Root POM = pom.xml
   > Goals and options = clean  package -Dmaven.test.skip=true -pl qingyun-order-service -am
2. qingyun-order-report-service build 配置
   > Root POM = pom.xml
   > Goals and options = clean  package -Dmaven.test.skip=true -pl qingyun-order-report-service -am   

二。Client部署
1. qingyun-order-client build 配置
   > Root POM = pom.xml
   > Goals and options = clean  deploy -Dmaven.test.skip=true -pl qingyun-order-client -am
2. qingyun-order-report-client build 配置
   > Root POM = pom.xml
   > Goals and options = clean  deploy -Dmaven.test.skip=true -pl qingyun-order-report-client -am
   
####服务架构
1. springboot + springcloud + redis + kafka + db + eureka + apollo

#### 安装要求
1. pinshang库
2. jar包运行
3. 2核，4G，20G最低要求
4. 注册中心eureka
5. 配置中心apollo
6. 鲜食redis
7. kafka集群 